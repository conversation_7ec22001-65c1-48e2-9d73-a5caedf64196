package ru.naumen.dynaform.client.content.complexrelation.form;

import static ru.naumen.commons.shared.utils.CollectionUtils.isEmpty;
import static ru.naumen.objectlist.shared.RelationFormListUtils.getObjectRelationListPermissionKey;
import static ru.naumen.objectlist.shared.RelationFormListUtils.getRelationHierarchyGridPermissionKey;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.TakesValue;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.HasValue;
import com.google.gwt.user.client.ui.Widget;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.SingleValueWidget;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.events.FireFormCancelEvent;
import ru.naumen.core.client.forms.ComplexFormResources;
import ru.naumen.core.client.forms.ComplexFormResources.ComplexFormCss;
import ru.naumen.core.client.forms.HasTabOrder;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dispatch.tree.ResetFilteredTreeAction;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.content.complexrelation.ComplexRelationFormUtils;
import ru.naumen.dynaform.client.content.complexrelation.form.hierarchygrid.ComplexRelationFormHierarchyGridPresenter;
import ru.naumen.dynaform.client.content.complexrelation.form.list.ComplexRelationFormListPresenter;
import ru.naumen.dynaform.client.content.complexrelation.form.list.ComplexRelationFormWithFullTextSearchListPresenter;
import ru.naumen.dynaform.client.content.embeddedapplications.EmbeddedApplicationContext;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.objectlist.shared.RelationFormList;
import ru.naumen.objectlist.shared.RelationFormListType;

/**
 * Презентер для сложной формы добавления связи
 * <AUTHOR>
 * @since 13.10.2015
 */
public class ComplexRelationForm extends AbstractComplexRelationForm<ObjectRelationFormContext>
{
    public static final String RELATION_FORM_LIST_PROPERTY = "relationFormList";

    @Inject
    protected Provider<ComplexRelationFormListPresenter> complexRelationFormListPresenter;
    @Inject
    private Provider<ComplexRelationFormHierarchyGridPresenter> complexRelationFormHierarchyGridPresenter;
    @Inject
    private Provider<ComplexRelationFormWithFullTextSearchListPresenter> complexRelationFormWithFullTextSearchListPresenter;
    @Inject
    protected ComplexRelationMessages messages;
    @Inject
    protected CommonMessages commonMessages;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    private Provider<SelectedObjectValuePresenter> selectedValuePresenterProvider;
    @Inject
    private SharedSettingsClientService sharedSettingsService;

    protected final ComplexFormCss styles;
    protected ComplexRelationFormListPresenter listPresenter;
    private TakesValue<Object> widget;
    private ReadyState openComplexFormReadyState;

    @Inject
    public ComplexRelationForm(ComplexRelationFormDisplay display, EventBus eventBus, ComplexFormResources res)
    {
        super(display, eventBus);
        styles = res.styles();
        styles.ensureInjected();
    }

    public void init(PresentationContext prsContext, TakesValue<Object> widget, @Nullable Set<DtObject> originalValue,
            List<DtObject> unsavedObjects, String searchQuery)
    {
        int nextBaseTabIndex = 1;

        if (widget instanceof Widget)
        {
            HasTabOrder parentElement = TabOrderHelper
                    .findFirstParentElementWithDeterminedTabOrder(((Widget)widget).asWidget());
            if (parentElement != null)
            {
                nextBaseTabIndex = parentElement.getNextBaseTabIndex();
            }
        }

        initContext(new ObjectRelationFormContext(prsContext, nextBaseTabIndex,
                ComplexRelationFormUtils.getInitialValue(widget.getValue(), prsContext), searchQuery,
                widget instanceof SingleValueWidget, originalValue));

        FlowContent relationList = getRelationList();
        if (null != relationList)
        {
            context.getListContents().put(RelationFormListType.AvailableRelations, relationList);
        }

        Context parentContext = prsContext.getParentContext();
        if (parentContext instanceof EmbeddedApplicationContext)
        {
            registerHandler(parentContext.getEventBus().addHandler(FireFormCancelEvent.getType(),
                    (event) -> onCancel()));
        }

        updateUnsavedObjects(unsavedObjects);
        this.widget = widget;
    }

    public void init(PresentationContext prsContext, TakesValue<Object> widget, Set<DtObject> originalValue,
            List<DtObject> unsavedObjects, String searchQuery, ReadyState openComplexFormReadyState)
    {
        this.init(prsContext, widget, originalValue, unsavedObjects, searchQuery);
        this.openComplexFormReadyState = openComplexFormReadyState;
    }

    @Override
    public void onApply()
    {
        lockForm();
        Set<DtObject> value = context.getValue();
        if (context.isSingleSelection() && context.getInitialValue() == null)
        {
            DtObject selectedValue = !isEmpty(value) ? value.iterator().next() : null;
            if (widget instanceof HasValue)
            {
                if (widget instanceof SingleSelectCellList && selectedValue instanceof AbstractDtObject)
                {
                    ((SingleSelectCellList)widget).setValue(new SelectItem((SimpleDtObject)selectedValue),
                            true);
                }
                else
                {
                    ((HasValue<Object>)widget).setValue(selectedValue, true);
                }
            }
            else
            {
                widget.setValue(selectedValue);
            }
        }
        else if (!context.isSingleSelection() && !isEmpty(value))
        {
            @SuppressWarnings("unchecked")
            HashSet<DtObject> oldValue = (HashSet<DtObject>)widget.getValue();
            for (DtObject valueItem : value)
            {
                oldValue.add(new SelectItem((AbstractDtObject)valueItem));
            }

            if (widget instanceof HasValue)
            {
                ((HasValue<Object>)widget).setValue(oldValue, true);
            }
            else
            {
                widget.setValue(oldValue);
            }
        }
        unbind();
    }

    @Override
    public void onCancel()
    {
        unbind();
    }

    @Override
    public void revealDisplay()
    {
        super.revealDisplay();
        listPresenter.revealDisplay();
        getDisplay().display();
    }

    protected void bindForm()
    {
        if (!context.getListContents().containsKey(RelationFormListType.AvailableRelations))
        {
            dialogs.warning(messages.needRefreshPage());
            return;
        }
        getDisplay().setCaptionText(messages.addComplexRelation());
        setApplyButtonCaption(commonMessages.save());
        DebugIdBuilder.ensureDebugId(getDisplay(), "ComplexRelationForm");

        if (widget instanceof HasEnabled)
        {
            ((HasEnabled)widget).setEnabled(false);
        }

        if (context.isSingleSelection())
        {
            SelectedObjectValuePresenter selectedValuePresenter = selectedValuePresenterProvider.get();
            getDisplay().addContent(selectedValuePresenter.getDisplay());
            selectedValuePresenter.init(context);
            selectedValuePresenter.bind();
        }

        createListPresenter();

        listPresenter.init(context);
        listPresenter.bind();
        getDisplay().addContent(listPresenter.getDisplay());
        getDisplay().display();
        setFocusOnSearchField();
    }

    private FlowContent getRelationList()
    {
        PresentationContext prsContext = context.getPrsContext();
        String complexRelationType = prsContext.getAttributeType().cast().getComplexRelationType();
        if (ComplexRelationType.HIERARCHY.getCode().equals(complexRelationType))
        {
            final String attributeKey;
            if (prsContext.getParentContext() instanceof MassEditActionContext)
            {
                attributeKey = ((MassEditActionContext)prsContext.getParentContext()).getAttributeCode(
                        Objects.requireNonNull(prsContext.getAttribute()));
            }
            else
            {
                attributeKey = Objects.requireNonNull(prsContext.getAttribute()).getCode();
            }
            return ObjectUtils.clone(
                    Objects.requireNonNull(prsContext.getObject())
                            .getPermissionMetaData(getRelationHierarchyGridPermissionKey(attributeKey)));
        }

        RelationFormList relationFormList = prsContext.getProperty(RELATION_FORM_LIST_PROPERTY);
        if (null == relationFormList && null != prsContext.getObject() && null != prsContext.getAttribute())
        {
            final String attributeKey;
            if (prsContext.getParentContext() instanceof MassEditActionContext)
            {
                attributeKey = ((MassEditActionContext)prsContext.getParentContext())
                        .getAttributeCode(prsContext.getAttribute());
            }
            else
            {
                attributeKey = prsContext.getAttribute().getCode();
            }
            relationFormList = prsContext.getObject().getPermissionMetaData(
                    getObjectRelationListPermissionKey(attributeKey));
        }
        return ObjectUtils.clone(relationFormList);
    }

    private void createListPresenter()
    {
        String complexRelationType = context.getPrsContext().getAttributeType().cast().getComplexRelationType();
        if (ComplexRelationType.HIERARCHY.getCode().equals(complexRelationType))
        {
            listPresenter = complexRelationFormHierarchyGridPresenter.get();
        }
        else if (ComplexRelationType.FLAT_WITH_FULL_TEXT_SEARCH.getCode().equals(complexRelationType)
                 && sharedSettingsService.isFullTextSearchFormEnabled())
        {
            listPresenter = complexRelationFormWithFullTextSearchListPresenter.get();
        }
        else
        {
            listPresenter = complexRelationFormListPresenter.get();
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        ReadyState currentFormReadyState = openComplexFormReadyState != null ? openComplexFormReadyState :
                context.getPrsContext().getFormReadyState();
        if (context.getPrsContext().isFilteredByScript())
        {
            // Обновляем время открытия сложной формы на сервере для того, чтобы при запросе данных
            // для сложной формы взять актуальные значения из FilteredTreeCache
            dispatch.execute(
                    new ResetFilteredTreeAction(
                            AttributeFqn.toString(
                                    Objects.requireNonNull(context.getPrsContext().getAttribute())
                                            .getMetaClassLite().getFqn(),
                                    context.getPrsContext().getAttribute().getCode())),
                    new BasicCallback<SimpleResult<Void>>(currentFormReadyState)
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<Void> response)
                        {
                            bindForm();
                        }
                    });
        }
        else
        {
            bindForm();
        }
    }

    @Override
    protected void onUnbind()
    {
        if (widget instanceof HasEnabled)
        {
            ((HasEnabled)widget).setEnabled(true);
        }
        super.onUnbind();
        if (null != listPresenter)
        {
            listPresenter.unbind();
        }
        getDisplay().destroy();
    }

    protected void setFocusOnSearchField()
    {
        listPresenter.setFocusOnSearchField();
    }

    private boolean isObjectCasePermitted(DtObject dto, @Nullable ObjectAttributeType objectType)
    {
        ClassFqn fqn = dto.getMetaClass();
        if (null == objectType || null == fqn || !fqn.fqnOfClass().equals(objectType.getRelatedMetaClass()))
        {
            return false;
        }

        Set<ClassFqn> permittedCases = objectType.getPermittedTypes();
        return CollectionUtils.isEmpty(permittedCases) || permittedCases.contains(fqn);
    }

    private void updateUnsavedObjects(List<DtObject> unsavedObjects)
    {
        Map<String, DtObject> unsavedObjectsMap = this.context.getUnsavedObjects();
        unsavedObjectsMap.clear();

        AttributeType attrType = context.getPrsContext().getAttributeType();
        ObjectAttributeType objectType = ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES
                .contains(attrType.getCode()) ? attrType.cast() : null;

        for (DtObject obj : unsavedObjects)
        {
            if (!UuidHelper.isTempUuid(obj.getUUID()) || isObjectCasePermitted(obj, objectType))
            {
                unsavedObjectsMap.put(obj.getUUID(), obj);
            }
        }
    }
}