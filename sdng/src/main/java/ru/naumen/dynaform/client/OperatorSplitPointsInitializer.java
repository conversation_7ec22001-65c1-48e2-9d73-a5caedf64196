package ru.naumen.dynaform.client;

import jakarta.inject.Inject;

import com.google.gwt.inject.client.AsyncProvider;

import ru.naumen.core.client.attr.presentation.factories.AttributeEditWidgetFactoriesSplitPoint;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.tree.dto.DtoTreeFactorySplitPoint;
import ru.naumen.core.client.widgets.RichTextWidgetSplitPoint;
import ru.naumen.core.client.widgets.SourceCodeWidgetSplitPoint;
import ru.naumen.core.client.widgets.datepicker.DateTimeWidgetsSplitPoint;
import ru.naumen.dynaform.client.content.objectlist.ObjectListDynaSplitPoint;
import ru.naumen.dynaform.client.toolbar.single.ChangeCaseFormSplitPoint;
import ru.naumen.dynaform.client.widgets.RichTextWidgetOperatorSplitPoint;
import ru.naumen.fts.client.FtsSplitPoint;
import ru.naumen.reports.client.dynaform.ReportsSplitPoint;

/**
 * Выполняет инициализацию точек разделения кода оператора
 *
 * <AUTHOR>
 * @since Dec 29, 2015
 */
public class OperatorSplitPointsInitializer
{
    /**
     * Сплит поинты, необходимые на формах
     */
    //@formatter:off
    public static final Class<?>[] FORM_SPLIT_POINTS = new Class<?>[] {
            AttributeEditWidgetFactoriesSplitPoint.class, 
            DateTimeWidgetsSplitPoint.class, 
            DtoTreeFactorySplitPoint.class,
            RichTextWidgetSplitPoint.class,
            SourceCodeWidgetSplitPoint.class};
    //@formatter:on

    @Inject
    //@formatter:off
    public void initSplitPoints(
            SplitPointService service,
            
            AsyncProvider<FormPresentersSplitPoint> formPresenters,
            AsyncProvider<ChangeCaseFormSplitPoint> changeCaseForms,
            AsyncProvider<FtsSplitPoint> fts,
            AsyncProvider<ObjectListDynaSplitPoint> objectList,
            AsyncProvider<ReportsSplitPoint> reports,
            
            AsyncProvider<AttributeEditWidgetFactoriesSplitPoint> attributeEditWidgets,
            AsyncProvider<DateTimeWidgetsSplitPoint> dateTimeWidgets,
            AsyncProvider<DtoTreeFactorySplitPoint> dtoTreeFactories,
            AsyncProvider<RichTextWidgetOperatorSplitPoint> rtf,
            AsyncProvider<SourceCodeWidgetSplitPoint> sourceCode)
    //@formatter:on
    {
        service.register(FormPresentersSplitPoint.class, formPresenters);
        service.register(ChangeCaseFormSplitPoint.class, changeCaseForms);
        service.register(FtsSplitPoint.class, fts);
        service.register(ObjectListDynaSplitPoint.class, objectList);
        service.register(ReportsSplitPoint.class, reports);

        //edit widgets
        service.register(AttributeEditWidgetFactoriesSplitPoint.class, attributeEditWidgets);
        service.register(DateTimeWidgetsSplitPoint.class, dateTimeWidgets);
        service.register(DtoTreeFactorySplitPoint.class, dtoTreeFactories);
        service.register(RichTextWidgetSplitPoint.class, rtf);
        service.register(SourceCodeWidgetSplitPoint.class, sourceCode);
    }
}
