package ru.naumen.dynaform.client.toolbar;

import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;
import static ru.naumen.metainfo.shared.ui.Constants.CHANGE_PASSWORD;
import static ru.naumen.metainfo.shared.ui.Constants.CHANGE_STATE;
import static ru.naumen.metainfo.shared.ui.Constants.FIRE_USER_EVENT;
import static ru.naumen.metainfo.shared.ui.Constants.TRANSITION_CHANGE_STATE;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.google.common.base.Predicates;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.actions.ActionHandler;
import ru.naumen.core.client.content.toolbar.actions.StartActionEvent;
import ru.naumen.core.client.events.EventFireFrom;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.QuickActions;
import ru.naumen.core.shared.RemovedMode;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.licensing.quota.QuotingBalanceCheckMode;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.DefaultDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaContextDecorator;
import ru.naumen.dynaform.client.DynaformObjectService;
import ru.naumen.dynaform.client.FormPresentersSplitPoint;
import ru.naumen.dynaform.client.actioncommand.FormContextDecorator;
import ru.naumen.dynaform.client.content.objectlist.listpresentation.ObjectListDynaContext;
import ru.naumen.dynaform.client.content.objectlist.listpresentation.extended.advlist.AdvListToolBarDynaContext;
import ru.naumen.dynaform.client.toolbar.executors.ActionExecutor;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.MetaClassAttributeType;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.HasAttributesFilledByCurrent;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * Базовая реализация ActionHandler - фасад, который сочетает в себе проверку на видимость и выполнение действия
 * <AUTHOR>
 * @since 12.01.2012
 */
public class DynaformActionHandler<T extends ToolBarAction> implements ActionHandler
{
    private static final Set<String> REMOVED_LIST_ACTIONS = Sets.newHashSet(
            Constants.DELETE,
            Constants.RESTORE_OBJECT);

    @Inject
    protected EventBus eventBus;
    @Inject
    protected DynaformObjectService objectService;
    @Inject
    protected SplitPointService splitPointService;
    protected final ActionToolContext context;
    @Inject
    private ActionExecutorRegistry actionExecutors;
    @Inject
    private T toolBarAction;
    @Inject
    private MetainfoServiceSync metainfoService;
    @Inject
    private SharedSettingsClientService settings;

    @Inject
    public DynaformActionHandler(@Assisted ActionToolContext context)
    {
        this.context = context;
    }

    @Override
    public final void execute()
    {
        eventBus.fireEvent(new StartActionEvent());

        final DynaContext ctx = context.getParentContext();
        if (toolBarAction.needReload() && ctx instanceof AdvListToolBarDynaContext) //NOSONAR
        {
            executeFromAdvList((AdvListToolBarDynaContext)ctx);
        }
        else if (toolBarAction.needReload()
                 && (ctx instanceof DynaContextDecorator
                     || context.getParentContent() instanceof RelObjPropertyList
                     || context.getParentContent() instanceof CommentList
                     || context.getParentContent() instanceof FileList
                     || context.isObjectActionMenuPressed()
                     || context.isActionFromEditableCell()
                     || context.fireFrom() == EventFireFrom.CUSTOM_BUTTON
                     || (context.getActionTool() instanceof UserEventTool //NOSONAR
                         && ((UserEventTool)context.getActionTool()).getEventUuid() == null)
                     || context.getActionTool().getAction().equals(TRANSITION_CHANGE_STATE))
                 && !ctx.getClass().equals(FormContextDecorator.class))
        {
            executeFromCard(ctx);
        }
        else if (toolBarAction.needReload() && (context.getParentContent() == null
                                                || context.getParentContent() instanceof EmbeddedApplicationContent))
        {
            executeFromLink(ctx.getReadyState());
        }
        else
        {
            executeAfterReload(ctx.getReadyState());
        }
    }

    public void executeAfterReload(ReadyState readyState)
    {
        splitPointService.inject(FormPresentersSplitPoint.class,
                new BasicCallback<FormPresentersSplitPoint>(readyState)
                {
                    @Override
                    protected void handleSuccess(FormPresentersSplitPoint formPresentersSP)
                    {
                        ActionExecutor executor = actionExecutors.getActionExecutor(context.getAction(), toolBarAction);
                        assertNotNull(executor, "Executor");
                        executor.init(context);
                        executor.execute(context.<DynaContext> getParentContext().getObjects());
                    }
                });
    }

    @Override
    public boolean isEnabled()
    {
        ActionTool tool = context.getActionTool();
        String action = tool.getAction();
        if (tool.isMassOperation() && context.getParentContext() instanceof ObjectListDynaContext //NOSONAR
            && RemovedMode.isRemovedObjectsOnly(
                ((ObjectListDynaContext)context.getParentContext()).getMode().getRemovedMode())
            && !REMOVED_LIST_ACTIONS.contains(action))
        {
            return false;
        }
        if (context.getParentContext() instanceof AdvListToolBarDynaContext)
        {
            //проверяем, что действие доступно хотя бы для одного выбранного объекта
            AdvListToolBarDynaContext advlistContext = context.getParentContext();
            if (Constants.SINGLE_OBJECT_ACTIONS.contains(action)
                && advlistContext.getMode().getSelectedObjects().size() > 1)
            {
                return false;
            }
            if (tool instanceof HasAttributesFilledByCurrent) //NOSONAR
            {
                return checkEnableToolIfHasAttrsFilledByCurrent((HasAttributesFilledByCurrent)tool, advlistContext);
            }
            return advlistContext.hasContentPermission(tool, SecConstants.CONTENT_VISIBLE);
        }
        return tool.isVisible();
    }

    private boolean checkEnableToolIfHasAttrsFilledByCurrent(HasAttributesFilledByCurrent tool,
            AdvListToolBarDynaContext advlistContext)
    {
        List<String> strAttributeFqnFromCommandSettings = tool.getAttributesFqnFilledByCurrent();
        if (CollectionUtils.isEmpty(strAttributeFqnFromCommandSettings) || !((ActionTool)tool).isVisible())
        {
            return advlistContext.hasContentPermission((ActionTool)tool, SecConstants.CONTENT_VISIBLE);
        }

        AttributeFqn attributeFqnFromCommandSettings = AttributeFqn.parse(
                strAttributeFqnFromCommandSettings.get(0));
        ClassFqn classFqnFromAttr = attributeFqnFromCommandSettings.getClassFqn();
        MetaClass metaClass = metainfoService.getMetaClass(classFqnFromAttr);
        Attribute attributeFromCommandSettings = metaClass.getAttribute(attributeFqnFromCommandSettings.getCode());
        boolean isAttrSingleObjectLink = Objects.requireNonNull(attributeFromCommandSettings)
                .getType()
                .getCode()
                .equals(ru.naumen.metainfo.shared.Constants.ObjectAttributeType.CODE);
        if (isAttrSingleObjectLink
            && advlistContext.getMode().getSelectedObjects().size() > 1)
        {
            return false;
        }
        for (DtObject dtObject : advlistContext.getMode().getSelectedObjects())
        {
            ClassFqn classFqnSelectedObject = dtObject.getProperty(MetaClassAttributeType.CODE);
            if (!LinkAttributeUtils.canAttributeRefToMetaClasses(attributeFromCommandSettings,
                    Collections.singletonList(classFqnSelectedObject)))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * Для оптимизации может понадобиться загружать не все свойства объектов для массовых действий
     * @return необходимые свойства для действия
     */
    protected Collection<String> getActionProperties()
    {
        return Collections.emptyList();
    }

    protected QuotingBalanceCheckMode getQuotingBalanceCheckMode()
    {
        return QuotingBalanceCheckMode.NONE;
    }

    protected DtObject getObject()
    {
        DynaContext ctx = context.getParentContext();
        return ctx.getObject();
    }

    protected HashMap<String, PermissionHolder> getObjectsPermissions()
    {
        HashMap<String, PermissionHolder> result = new HashMap<>();
        if (context.getParentContext() instanceof AdvListToolBarDynaContext)
        {
            HashSet<String> uuids = new HashSet<>(getObjectUUIDs());
            AdvListToolBarDynaContext advlistContext = context.getParentContext();
            result.putAll(Maps.filterKeys(advlistContext.getMode().getObjectPermissions(), Predicates.in(uuids)));
        }
        else
        {
            DynaContext dynaContext = context.getParentContext();
            // Для списка для системных действий главной панели инструментов (не меню действий с объектом, не инлайн
            // редактирования, не массовой панели), родительский контекст будет не контекст списка,
            // а контекст карточки объекта
            if (dynaContext instanceof ObjectListDynaContext && !context.isObjectActionMenuPressed() //NOSONAR
                && !context.isActionFromEditableCell() && context.getMassOperationProperties().isEmpty())
            {
                dynaContext = ((ObjectListDynaContext)dynaContext).getParentContext();
                if (dynaContext != null && (context.getParentContent() instanceof CommentList
                                            || context.getParentContent() instanceof FileList))
                {
                    dynaContext = ((DynaContextDecorator)dynaContext).getParentContext();
                }
            }
            // Для параметров связанного объекта если тип действия к объекту связи, то контекст будет контекст этого
            // связанного объекта
            if (context.getParentContent() instanceof RelObjPropertyList && dynaContext != null
                && context.getActionTool().getAppliedToType().equals(AppliedToType.RELATED_OBJECT)
                && context.getActionTool().getAction().equals(CHANGE_STATE))
            {
                //убрал пока чтобы не подменялся контекст на карточке (нужна пока только для смены статуса)
                dynaContext = ((DefaultDynaContext)dynaContext).getParentContext();
            }
            result.put(getObject().getUUID(), null != dynaContext ? dynaContext.getPermissions() : null);
        }
        return result;
    }

    /**
     * Для некоторых действий мы понимаем необходимые свойства на этапе формирования презентера,
     * загрузим для них все свойства
     */
    protected boolean isActionUseAllProperties()
    {
        return false;
    }

    protected boolean isAdvlist()
    {
        return context.getParentContext() instanceof AdvListToolBarDynaContext;
    }

    protected void listObjects(DtoCriteria dtoCriteria, AsyncCallback<List<DtObject>> callback)
    {
        objectService.getObjects(dtoCriteria, getQuotingBalanceCheckMode(), UI.WINDOW_KEY, callback);
    }

    private void executeFromAdvList(final AdvListToolBarDynaContext ctx)
    {
        loadObjectsForList(ctx, new BasicCallback<List<DtObject>>(ctx.getReadyState())
        {
            @Override
            protected void handleSuccess(List<DtObject> value)
            {
                ((ObjectListActive)ctx.getMode()).setSelectedObjects(Sets.newHashSet(value));
                executeAfterReload(ctx.getReadyState());
            }
        });
    }

    private void executeFromCard(final DynaContext ctx)
    {
        // в случае ПДПС берем объекты из родителя (карточки), т.к действительно нужен только uuid
        if (FIRE_USER_EVENT.equals(context.getAction()) && !settings.isReloadSubjectsOnFireForUserEvent())
        {
            executeFireUserEventFromCard(ctx);
        }
        // при вызове экшена для изменения пароля берем уже загруженные данные из контекста карточки, вместо того,
        // чтобы загружать их заново
        else if (CHANGE_PASSWORD.equals(context.getAction()))
        {
            executeWithObjectsFromParentContext(ctx);
        }
        else
        {
            DtoCriteria criteria = new DtoCriteria(getObjectUUIDs());
            listObjects(criteria, new BasicCallback<List<DtObject>>(ctx.getReadyState())
            {
                @Override
                protected void handleSuccess(List<DtObject> value)
                {
                    ctx.setObjects(value);
                    executeAfterReload(ctx.getReadyState());
                }
            });
        }
    }

    /**
     * Выполнение пользовательского ДПС с карточки объекта
     */
    private void executeFireUserEventFromCard(final DynaContext ctx)
    {
        executeWithObjectsFromParentContext(ctx);
    }

    private void executeWithObjectsFromParentContext(final DynaContext ctx)
    {
        ctx.setObjects(context.<DynaContext> getParentContext().getObjects());
        executeAfterReload(ctx.getReadyState());
    }

    private void executeFromLink(final ReadyState readyState)
    {
        DtoCriteria criteria = new DtoCriteria(getObjectUUIDs());
        // не пойму пока откуда вызов
        listObjects(criteria, new BasicCallback<List<DtObject>>(readyState)
        {
            @Override
            protected void handleSuccess(List<DtObject> value)
            {
                executeAfterReload(readyState);
            }
        });
    }

    private ArrayList<String> getObjectUUIDs()
    {
        DynaContext ctx = context.getParentContext();
        Collection<DtObject> objects = ctx.getObjects();
        return new ArrayList<>(CommonUtils.extractUuids(objects));
    }

    private void loadObjectsForList(final AdvListToolBarDynaContext ctx, AsyncCallback<List<DtObject>> callback)
    {
        List<DtObject> objects = new ArrayList<>();
        Map<String, DtObject> editedObjects = new HashMap<>();
        List<String> uuidsToLoad = new ArrayList<>();
        ctx.getObjects().forEach(dto ->
        {
            if (UuidHelper.isTempUuid(dto.getUUID()))
            {
                objects.add(dto);
            }
            else
            {
                if (Boolean.TRUE.equals(dto.getProperty(QuickActions.OBJECT_IS_EDITED)))
                {
                    editedObjects.put(dto.getUUID(), dto);
                }
                uuidsToLoad.add(dto.getUUID());
            }
        });

        if (uuidsToLoad.isEmpty())
        {
            callback.onSuccess(objects);
        }
        else
        {
            DtoCriteria criteria = new DtoCriteria(uuidsToLoad);
            if (!isActionUseAllProperties())
            {
                criteria.setProperties(ctx.getMode().getAttributeCodes())
                        .setProperties(getActionProperties());
            }
            listObjects(criteria, new CallbackDecorator<List<DtObject>, List<DtObject>>(callback)
            {
                @Override
                protected List<DtObject> apply(List<DtObject> from)
                {
                    objects.addAll(from);
                    objects.stream().filter(dto -> editedObjects.containsKey(dto.getUUID()))
                            .forEach(dto -> dto.setAll(editedObjects.get(dto.getUUID())));
                    return objects;
                }
            });
        }
    }
}