package ru.naumen.dynaform.client.preview.canvas;

import jakarta.inject.Inject;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.preview.FilePreviewResources;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.media.client.Audio;
import com.google.gwt.media.client.MediaBase;
import com.google.inject.assistedinject.Assisted;

/**
 * {@link PreviewCanvas} для аудио-файлов
 *
 * <AUTHOR>
 * @since 14 окт. 2015 г.
 */
public class AudioPreviewCanvas extends AbstractMediaPreviewCanvas
{
    @Inject
    public AudioPreviewCanvas(@Assisted DtObject file, @Assisted EventBus eventBus, FilePreviewResources resources)
    {
        super(file, eventBus, resources);
    }

    @Override
    protected MediaBase createMedia()
    {
        return Audio.createIfSupported();
    }
}