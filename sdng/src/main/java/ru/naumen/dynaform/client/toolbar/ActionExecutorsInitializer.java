package ru.naumen.dynaform.client.toolbar;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.dynaform.client.toolbar.multi.add.bo.AddBusinessObjectActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.add.bo.sc.AddScActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.add.system.comment.AddCommentActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.add.system.file.AddFileActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.deletelink.DeleteLinkActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.downloadfile.DownloadFileActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.event.FireUserEventActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.linkcheck.delete.DelActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.linkcheck.remove.RemoveActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.relationedit.AddDeleteObjsFormActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.move.MoveActionExecutor;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.restore.RestoreActionExecutor;
import ru.naumen.dynaform.client.toolbar.single.addlink.AddLinkActionExecutor;
import ru.naumen.dynaform.client.toolbar.single.changepass.ChangePasswordActionExecutor;
import ru.naumen.dynaform.client.toolbar.single.openmass.OpenMassServiceCallFormActionExecutor;
import ru.naumen.dynaform.client.toolbar.single.showremoved.ShowRemovedActionExecutor;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.reports.client.dynaform.report.add.AddReportActionExecutor;
import ru.naumen.reports.client.dynaform.report.content.toolbar.SaveReportActionExecutor;

/**
 * Набор ActionExecutorов, которые можно проинициализировать отложенно через AsyncProvider 
 *
 * <AUTHOR>
 * @since Nov 30, 2015
 */
@Singleton
public class ActionExecutorsInitializer
{
    @Inject
    //@formatter:off
    public ActionExecutorsInitializer(
            ActionExecutorRegistry registry,
            
            Provider<ChangePasswordActionExecutor> changePassword,
            Provider<AddScActionExecutor> addSc,
            Provider<DeleteLinkActionExecutor> deleteLink,
            Provider<DownloadFileActionExecutor> downloadFile,
            Provider<DelActionExecutor> del,
            Provider<RemoveActionExecutor> remove,
            Provider<MoveActionExecutor> move,
            Provider<RestoreActionExecutor> restore,
            Provider<FireUserEventActionExecutor> userEvent,
            Provider<AddLinkActionExecutor> addLink,
            Provider<ShowRemovedActionExecutor> showRemoved,
            Provider<SaveReportActionExecutor> saveReport,
            Provider<AddDeleteObjsFormActionExecutor> addDeletObjsForm,
            Provider<OpenMassServiceCallFormActionExecutor> openMassCallForm,
            Provider<AddBusinessObjectActionExecutor> addBo,
            Provider<AddCommentActionExecutor> addComment,
            Provider<AddFileActionExecutor> addFile, 
            Provider<AddReportActionExecutor> addReport)
    //@formatter:on
    {
        registry.register(Constants.CHANGE_PASSWORD, changePassword);
        registry.register(Constants.ADD_SC, addSc);
        registry.register(Constants.DELETE_LINK, deleteLink);
        registry.register(Constants.DOWNLOAD_FILE, downloadFile);
        registry.register(Constants.DELETE, del);
        registry.register(Constants.DELETE_ACTION_TOOL, del);
        registry.register(Constants.REMOVE_OBJECT, remove);
        registry.register(Constants.MOVE_OBJECT, move);
        registry.register(Constants.RESTORE_OBJECT, restore);
        registry.register(Constants.FIRE_USER_EVENT, userEvent);
        registry.register(Constants.LINK, addLink);
        registry.register(Constants.SHOW_REMOVED, showRemoved);
        registry.register(Constants.SAVE_REPORT, saveReport);
        registry.register(Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_MASS_CALL, openMassCallForm);
        registry.register(Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_REGULAR_CALL, openMassCallForm);
        registry.register(Constants.ADD, addBo);
        registry.register(Constants.ADD_FILE, addFile);
        registry.register(Constants.ADD_COMMENT, addComment);
        registry.register(Constants.CREATE_NEW_REPORT, addReport);
        registry.register(Constants.ADD_DELETE_OBJS, addDeletObjsForm);
    }

}
