package ru.naumen.dynaform.client;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CrumbsOrBackChangedVisibilityEvent;
import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.menu.NavigationHelper;
import ru.naumen.core.client.widgets.NavigationLineWidget;
import ru.naumen.core.shared.breadcrumb.BreadCrumbDtoService;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Презентер отображения хлебных крошек в интерфейсе оператора
 *
 * <AUTHOR>
 * @since 10 июля 2014 г.
 */
public class BreadCrumbPresenter
{
    @Inject
    private EventBus eventBus;
    @Inject
    private DynaformUtils utils;
    @Inject
    private PlaceHistoryMapper placeHistoryMapper;
    @Inject
    private BadgeUtils badgeUtils;
    @Inject
    private BreadCrumbDtoService breadCrumbDtoService;
    private final NavigationLineWidget navigationLine;
    private DtObject lastCrumbObject;

    @Inject
    public BreadCrumbPresenter(@Assisted NavigationLineWidget navigationLine)
    {
        this.navigationLine = navigationLine;
    }

    /**
     * Задаем текущий объект
     */
    public void init(DtObject object)
    {
        lastCrumbObject = object;
    }

    /**
     * Обновление крошек
     */
    public void refresh(String title)
    {
        SafeHtml lastCrumbObjectBadge = badgeUtils.getInlineBadge(lastCrumbObject);
        navigationLine.setLastCrumb(title, lastCrumbObjectBadge);
        navigationLine.removeCrumbs();

        if (NavigationHelper.isShowBreadCrumb())
        {
            List<DtObject> breadCrumbs = breadCrumbDtoService.getBreadCrumb(lastCrumbObject);
            if (breadCrumbs != null)
            {
                for (DtObject object : breadCrumbs)
                {
                    SafeHtml badge = badgeUtils.getInlineBadge(object);
                    String breadCrumb = utils.getObjectCardCaption(object);
                    breadCrumb = utils.prefixIfRemoved(breadCrumb, object);
                    navigationLine.addCrumb(getHref(object), breadCrumb, badge);
                    navigationLine.setCrumbsVisible(true);
                }
            }
        }

        eventBus.fireEvent(new CrumbsOrBackChangedVisibilityEvent());
    }

    private String getHref(DtObject object)
    {
        return StringUtilities.getHrefByToken(placeHistoryMapper.getToken(new UserPlace(object)));
    }
}