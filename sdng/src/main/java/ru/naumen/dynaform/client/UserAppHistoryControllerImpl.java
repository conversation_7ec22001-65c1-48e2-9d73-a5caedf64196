package ru.naumen.dynaform.client;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import ru.naumen.core.client.AppHistoryControllerImpl;
import ru.naumen.core.client.AppHistoryItem;
import ru.naumen.core.client.events.AddObjectEvent;
import ru.naumen.core.client.events.AddObjectEventHandler;
import ru.naumen.core.client.events.DeleteObjectEvent;
import ru.naumen.core.client.events.DeleteObjectEventHandler;
import ru.naumen.core.client.events.EditObjectEvent;
import ru.naumen.core.client.events.EditObjectEventHandler;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 */
@Singleton
public class UserAppHistoryControllerImpl extends AppHistoryControllerImpl implements UserAppHistoryController,
        AddObject<PERSON><PERSON><PERSON><PERSON><PERSON>, EditObjectEventHandler, DeleteObject<PERSON>vent<PERSON>and<PERSON>
{
    @Inject
    DynaformUtils dynaformUtils;

    @edu.umd.cs.findbugs.annotations.SuppressWarnings(value = "RV_RETURN_VALUE_IGNORED", justification = "Controller "
                                                                                                         + "exists "
                                                                                                         + "forever")
    @Inject
    public UserAppHistoryControllerImpl(EventBus eventBus)
    {
        super(eventBus);
        eventBus.addHandler(EditObjectEvent.getType(), this);
        eventBus.addHandler(AddObjectEvent.getType(), this);
        eventBus.addHandler(DeleteObjectEvent.getType(), this);
    }

    @Override
    public void clearUuid(String uuid)
    {
        AppHistoryItem item = getItemByObjectUUID(uuid);
        if (item != null)
        {
            history.remove(item);
            fire();
        }
    }

    @Override
    public void onObjectAdded(AddObjectEvent e)
    {
        onObjectAddedEdited(e.getDtObject());
    }

    public void onObjectAddedEdited(DtObject obj)
    {
        AppHistoryItem item = getItemByObjectUUID(obj.getUUID());
        if (item != null)
        {
            item.setTitle(dynaformUtils.getObjectCardCaption(obj));
            fire();
        }
    }

    @Override
    public void onObjectDeleted(DeleteObjectEvent e)
    {
        clearUuid(e.getUUID());
    }

    @Override
    public void onObjectEdited(EditObjectEvent e)
    {
        for (DtObject object : e.getDtObjects())
        {
            onObjectAddedEdited(object);
        }
    }

    @Override
    protected boolean isSamePlaces(Place p1, Place p2)
    {
        if (p1 instanceof UserPlace && p2 instanceof UserPlace)
        {
            return ((UserPlace)p1).getUUID().equals(((UserPlace)p2).getUUID());
        }
        return super.isSamePlaces(p1, p2);
    }

    private AppHistoryItem getItemByObjectUUID(String uuid)
    {
        for (AppHistoryItem item : history)
        {
            Place histPlace = item.getPlace();
            if (histPlace instanceof UserPlace)
            {
                UserPlace up = (UserPlace)histPlace;
                if (uuid.equals(up.getUUID()))
                {
                    return item;
                }
            }
        }
        return null;
    }
}
