package ru.naumen.dynaform.client;

import java.util.List;

import com.google.common.collect.Lists;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.place.shared.PlaceHistoryHandler;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.TypeLiteral;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.AppHistoryController;
import ru.naumen.core.client.CoreGinjector;
import ru.naumen.core.client.CurrentUser;
import ru.naumen.core.client.IPageNameProvider;
import ru.naumen.core.client.ModuleInitializer;
import ru.naumen.core.client.PageNameProviderImpl;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.ToolInfoInitializer;
import ru.naumen.core.client.activity.ActivityFactory;
import ru.naumen.core.client.activity.ActivityFactoryImpl;
import ru.naumen.core.client.activity.ActivityGinjector;
import ru.naumen.core.client.activity.PrevLinkContainer;
import ru.naumen.core.client.browsertabs.BrowserTabsLimitService;
import ru.naumen.core.client.browsertabs.BrowserTabsLimitServiceImpl;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.ContextDecoratorCreator;
import ru.naumen.core.client.content.factory.ContentFactory;
import ru.naumen.core.client.content.toolbar.ToolCreationContextProvider;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.dispatchasync.Constants;
import ru.naumen.core.client.menu.LeftNavPanelDisplay;
import ru.naumen.core.client.menu.NavigationCommonGinModule;
import ru.naumen.core.client.theme.ThemeLogos;
import ru.naumen.core.client.utils.PageState;
import ru.naumen.core.client.widgets.ISchemeInjector;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.richtext.FroalaInitializer;
import ru.naumen.dynaform.client.DynaformGinjector.AddCommentInlineFormPresenterFactory;
import ru.naumen.dynaform.client.DynaformGinjector.BreadCrumbPresenterFactory;
import ru.naumen.dynaform.client.DynaformGinjector.CompactFormContentPresenterFactory;
import ru.naumen.dynaform.client.DynaformGinjector.DelFavoritesCommandFactory;
import ru.naumen.dynaform.client.DynaformGinjector.FormContentPresenterFactory;
import ru.naumen.dynaform.client.DynaformGinjector.QuickAccessAreaFactory;
import ru.naumen.dynaform.client.DynaformGinjector.QuickAccessPinCommandFactory;
import ru.naumen.dynaform.client.DynaformGinjector.QuickAccessTileFactory;
import ru.naumen.dynaform.client.DynaformGinjector.SelectContactsFormPartExtendedFactory;
import ru.naumen.dynaform.client.DynaformGinjector.SelectContactsFormPartSimpleFactory;
import ru.naumen.dynaform.client.DynaformGinjector.TreeNodeCommandFactory;
import ru.naumen.dynaform.client.activity.OperatorPlaceController;
import ru.naumen.dynaform.client.activity.OperatorPlaceHistoryHandler;
import ru.naumen.dynaform.client.attributes.AttributesGinModule;
import ru.naumen.dynaform.client.changetracking.ChangeTrackingGinModule;
import ru.naumen.dynaform.client.comments.CommentsGinModule;
import ru.naumen.dynaform.client.content.CompactFormContentPresenter;
import ru.naumen.dynaform.client.content.DynaformContentGinModule;
import ru.naumen.dynaform.client.content.FormContentPresenter;
import ru.naumen.dynaform.client.content.ISelectContactsPartPresenter;
import ru.naumen.dynaform.client.content.ISelectContactsPartPresenterExtended;
import ru.naumen.dynaform.client.content.ISelectContactsSimple;
import ru.naumen.dynaform.client.content.SelectContactsPartExtendedPresenter;
import ru.naumen.dynaform.client.content.SelectContactsPartPresenter;
import ru.naumen.dynaform.client.content.factory.ContentFactoriesGinModule;
import ru.naumen.dynaform.client.content.factory.ContentFactoryImpl;
import ru.naumen.dynaform.client.content.massproblems.MassProblemsContentGinModule;
import ru.naumen.dynaform.client.content.objectlist.ObjectListDynaGinModule;
import ru.naumen.dynaform.client.contextvariables.OperatorOriginProvider;
import ru.naumen.dynaform.client.customforms.CustomFormPresenterFactory;
import ru.naumen.dynaform.client.favorites.AddFavoriteToolPresenter;
import ru.naumen.dynaform.client.favorites.FavoritesGinModule;
import ru.naumen.dynaform.client.favorites.editform.favoritestab.command.DelFavoritesCommandImpl;
import ru.naumen.dynaform.client.header.HeaderTabIconController;
import ru.naumen.dynaform.client.header.OperatorMainContentHeaderPresenter;
import ru.naumen.dynaform.client.homepage.SetHomePageToolPresenter;
import ru.naumen.dynaform.client.leftmenu.LeftNavPanelDisplayImpl;
import ru.naumen.dynaform.client.leftmenu.commands.ExpandTreeNodeCommand;
import ru.naumen.dynaform.client.leftmenu.commands.QuickAccessPinCommand;
import ru.naumen.dynaform.client.leftmenu.quickaccess.QuickAccessAreaPresenter;
import ru.naumen.dynaform.client.leftmenu.quickaccess.QuickAccessTilePresenter;
import ru.naumen.dynaform.client.navigation.menu.NavigationMenuGinModule;
import ru.naumen.dynaform.client.navtree.DynaformNavigationTreeGinModule;
import ru.naumen.dynaform.client.permissions.PermissionsCheckGinModule;
import ru.naumen.dynaform.client.preview.FilePreviewGinModule;
import ru.naumen.dynaform.client.push.PushGinjector;
import ru.naumen.dynaform.client.quickforms.QuickActionsGinModule;
import ru.naumen.dynaform.client.toolbar.ToolBarGinModule;
import ru.naumen.dynaform.client.toolbar.multi.add.LoadParameterObjectStrategy;
import ru.naumen.dynaform.client.toolbar.multi.add.LoadParameterObjectStrategyDefault;
import ru.naumen.dynaform.client.toolbar.multi.add.LoadParameterObjectStrategyFactory;
import ru.naumen.dynaform.client.toolbar.multi.add.LoadParameterObjectStrategyFactoryImpl;
import ru.naumen.dynaform.client.toolbar.multi.add.system.comment.AddCommentInlineFormPresenter;
import ru.naumen.dynaform.client.tour.SelectTourIconPresenter;
import ru.naumen.dynaform.client.widgets.DynaformSchemeInjector;
import ru.naumen.dynaform.client.widgets.DynaformWidgetResources;
import ru.naumen.dynaform.client.widgets.FroalaWithMentionsInitializerImpl;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ProvidesMetaClasses;
import ru.naumen.reports.client.dynaform.ReportsDynaGinModule;
import ru.naumen.tour.client.TourSettingsService;

/**
 * Модуль настраиваемого пользовательского интерфейса при отображении в интерфейсе оператора.
 *
 * <AUTHOR>
 */
public class DynaformGinModule extends AbstractGinModule
{
    @Provides
    @Singleton
    @Named(Constants.CONTEXT_STATE)
    public PageState contextState()
    {
        return new PageState(Constants.CONTEXT_STATE);
    }

    @Inject
    @Provides
    @Named(ActivityGinjector.DEFAULT_PLACE)
    public Provider<Place> defaultPlace(final DynaformObjectService objectService, final SecurityHelper securityHelper,
            final PlaceHistoryMapper placeHistoryMapper)
    {
        return () ->
        {
            CurrentUser user = securityHelper.getCurrentUser();
            if (StringUtilities.isNotEmpty(user.getHomePage()))
            {
                Place place = placeHistoryMapper.getPlace(user.getHomePage());
                if (place != null)
                {
                    return place;
                }
            }
            if (StringUtilities.isNotEmpty(user.getDefaultHomePage()))
            {
                Place place = placeHistoryMapper.getPlace(user.getDefaultHomePage());
                if (place != null)
                {
                    return place;
                }
            }
            // пользователя перенаправляем на его карточку, а суперпользователя на карточку компании
            return UserPlace.create(user.getUUID(), objectService);
        };
    }

    @Provides
    public List<HeaderTabIconController> getHeaderTabIconControllers(
            Provider<AddFavoriteToolPresenter> addFavoriteToolController,
            Provider<SetHomePageToolPresenter> setHomePageToolController,
            Provider<SelectTourIconPresenter> selectTourIconPresenter,
            TourSettingsService tourSettingsService)
    {
        List<HeaderTabIconController> controllers =
                Lists.newArrayList(addFavoriteToolController.get(), setHomePageToolController.get());
        if (tourSettingsService.isEnabled())
        {
            controllers.add(selectTourIconPresenter.get());
        }
        return controllers;
    }

    /**
     * Упорядочиваем строки по алфавиту
     */
    @Override
    protected void configure()
    {
        install(new ReportsDynaGinModule());
        install(new ContentFactoriesGinModule());
        install(new DynaformContentGinModule());
        install(new MassProblemsContentGinModule());
        install(new DynaformNavigationTreeGinModule());
        install(new ToolBarGinModule());
        install(new NavigationMenuGinModule());
        install(new ObjectListDynaGinModule());
        install(new FilePreviewGinModule());
        install(new PermissionsCheckGinModule());
        install(new CommentsGinModule());
        install(new QuickActionsGinModule());
        install(new FavoritesGinModule());
        install(new NavigationCommonGinModule());
        install(new ChangeTrackingGinModule());
        install(new AttributesGinModule());

        bind(CoreGinjector.class).to(DynaformGinjector.class).in(Singleton.class);
        bind(PushGinjector.class).to(DynaformGinjector.class).in(Singleton.class);
        bind(PrevLinkContainer.class).to(DynaformPrevLinkContainerImpl.class).in(Singleton.class);

        bind(WidgetResources.class).to(DynaformWidgetResources.class).in(Singleton.class);
        bind(new TypeLiteral<ISchemeInjector<DynaformWidgetResources>>()
        {
        }).to(new TypeLiteral<DynaformSchemeInjector>()
        {
        }).in(Singleton.class);

        bind(new TypeLiteral<ru.naumen.core.client.header.MainContentHeaderPresenter>()
        {
        }).to(new TypeLiteral<OperatorMainContentHeaderPresenter>()
        {
        });

        bind(ModuleInitializer.class).to(UserInitializer.class);

        bind(MetainfoServiceAsync.class).to(CachedMetainfoServiceAsync.class);
        bind(MetainfoServiceSync.class).to(CachedMetainfoServiceAsync.class);
        bind(ProvidesMetaClasses.class).to(CachedMetainfoServiceAsync.class);
        bind(DynaformObjectService.class).to(DynaformObjectServiceImpl.class).in(Singleton.class);

        bind(ToolInfoInitializer.class).in(Singleton.class);
        bind(ToolCreationContextProvider.class).to(UserToolCreationContextProvider.class).in(Singleton.class);

        //@formatter:off
        bind(new TypeLiteral<ContentFactory<DynaContext>>(){}).to(DynaformContentFactory.class).in(Singleton.class);
        //@formatter:on
        bind(DynaformContentFactory.class).to(ContentFactoryImpl.class).asEagerSingleton();
        bind(OperatorSplitPointsInitializer.class).asEagerSingleton();

        bind(AppHistoryController.class).to(UserAppHistoryControllerImpl.class).in(Singleton.class);

        bind(LeftNavPanelDisplay.class).to(LeftNavPanelDisplayImpl.class).in(Singleton.class);

        bind(LoadParameterObjectStrategyFactory.class).to(LoadParameterObjectStrategyFactoryImpl.class)
                .in(Singleton.class);
        bind(BrowserTabsLimitService.class).to(BrowserTabsLimitServiceImpl.class).in(Singleton.class);
        bind(ThemeLogos.class).in(Singleton.class);

        bind(LoadParameterObjectStrategy.class).to(LoadParameterObjectStrategyDefault.class);

        installFactories();
        installActivities();

        bind(ContextDecoratorCreator.class).to(DynaformContextDecoratorCreator.class);
        bind(OriginProvider.class).to(OperatorOriginProvider.class);
        bind(PlaceController.class).to(OperatorPlaceController.class).in(Singleton.class);
        bind(PlaceHistoryHandler.class).to(OperatorPlaceHistoryHandler.class).in(Singleton.class);

        bind(FroalaInitializer.class).to(FroalaWithMentionsInitializerImpl.class).in(Singleton.class);
    }

    private void installFactories()
    {
        //@formatter:off

        install(new GinFactoryModuleBuilder()
             .implement(FormContentPresenter.class, FormContentPresenter.class)
             .build(FormContentPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(CompactFormContentPresenter.class, CompactFormContentPresenter.class)
                .build(CompactFormContentPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(AddCommentInlineFormPresenter.class, AddCommentInlineFormPresenter.class)
                .build(AddCommentInlineFormPresenterFactory.class));
        install(new GinFactoryModuleBuilder()
              .implement(new TypeLiteral<ISelectContactsPartPresenter<ISelectContactsSimple>>(){},
                          new TypeLiteral<SelectContactsPartPresenter<ISelectContactsSimple>>(){})
              .build(SelectContactsFormPartSimpleFactory.class));
        install(new GinFactoryModuleBuilder()
             .implement(ISelectContactsPartPresenterExtended.class,
                           SelectContactsPartExtendedPresenter.class)
              .build(SelectContactsFormPartExtendedFactory.class));
        install(new GinFactoryModuleBuilder().implement(IPageNameProvider.class, PageNameProviderImpl.class).build(
                CoreGinjector.PageNameProviderFactory.class));
        install(new GinFactoryModuleBuilder().implement(BreadCrumbPresenter.class, BreadCrumbPresenter.class).build(
                BreadCrumbPresenterFactory.class));
        install(new GinFactoryModuleBuilder().build(CustomFormPresenterFactory.class));

        install(new GinFactoryModuleBuilder()
                .implement(QuickAccessAreaPresenter.class, QuickAccessAreaPresenter.class)
                .build(QuickAccessAreaFactory.class));
        install(new GinFactoryModuleBuilder()
                .implement(QuickAccessTilePresenter.class, QuickAccessTilePresenter.class)
                .build(QuickAccessTileFactory.class));
        install(new GinFactoryModuleBuilder().implement(BaseCommand.class, QuickAccessPinCommand.class).build(
                QuickAccessPinCommandFactory.class));
        install(new GinFactoryModuleBuilder().implement(BaseCommand.class, ExpandTreeNodeCommand.class).build(
                TreeNodeCommandFactory.class));

        install(new GinFactoryModuleBuilder().implement(BaseCommand.class, DelFavoritesCommandImpl.class).build(
                DelFavoritesCommandFactory.class));
        //@formatter:on
    }

    private void installActivities()
    {
        bind(ActivityFactory.class).to(ActivityFactoryImpl.class).in(Singleton.class);
    }
}
