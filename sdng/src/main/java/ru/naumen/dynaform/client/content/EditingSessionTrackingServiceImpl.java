package ru.naumen.dynaform.client.content;

import static ru.naumen.core.shared.changetracking.MessageConstants.SPECIAL_EDITABLE_ATTRIBUTES;
import static ru.naumen.core.shared.changetracking.MessageConstants.SPECIAL_EDITABLE_SC_ATTRIBUTES;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.Window;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.jsinterop.JSON;
import ru.naumen.core.client.jsinterop.JsObject;
import ru.naumen.core.client.jsinterop.Navigator;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.changetracking.ChangeTrackingMessage;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.changetracking.ChangeTrackingMessageDeserializer;
import ru.naumen.dynaform.client.changetracking.content.ContentChangeCheckingService;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.dynaform.shared.dispatch.CloseEditSessionsAction;
import ru.naumen.dynaform.shared.dispatch.GetPersistentTrackingMessagesAction;
import ru.naumen.dynaform.shared.dispatch.GetTrackingMessagesResponse;
import ru.naumen.dynaform.shared.dispatch.OpenEditSessionsAction;
import ru.naumen.dynaform.shared.dispatch.OpenEditSessionsResponse;
import ru.naumen.dynaform.shared.masseditform.MassEditBlockInfo;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.Form;

/**
 * Реализация сервиса для управления сессиями редактирования на стороне клиента.
 * <AUTHOR>
 * @since May 23, 2022
 */
@Singleton
public class EditingSessionTrackingServiceImpl implements EditingSessionTrackingService
{
    private final DispatchAsync dispatch;
    private final ChangeTrackingMessageDeserializer messageDeserializer;
    private final ContentChangeCheckingService contentChangeCheckingService;
    private final OriginProvider originProvider;

    private class CloseWindowHandler implements CloseHandler<Window>
    {
        private final DynaContext context;

        public CloseWindowHandler(DynaContext context)
        {
            this.context = context;
        }

        @Override
        public void onClose(CloseEvent<Window> event)
        {
            if (context.getEditingSessionIds().isEmpty())
            {
                return;
            }

            if (Navigator.supportsBeacon())
            {
                long[] ids = context.getEditingSessionIds().stream().mapToLong(Long::longValue).toArray();
                context.getEditingSessionIds().clear();
                Navigator.get().sendBeacon("./closeEditSession", JSON.stringify(ids));
            }
            else
            {
                closeSessions(context);
            }
        }
    }

    @Inject
    public EditingSessionTrackingServiceImpl(
            DispatchAsync dispatch,
            ChangeTrackingMessageDeserializer messageDeserializer,
            ContentChangeCheckingService contentChangeCheckingService,
            OriginProvider originProvider)
    {
        this.dispatch = dispatch;
        this.messageDeserializer = messageDeserializer;
        this.contentChangeCheckingService = contentChangeCheckingService;
        this.originProvider = originProvider;
    }

    @Override
    public HandlerRegistration addWindowCloseHandler(DynaContext context)
    {
        return Window.addCloseHandler(new CloseWindowHandler(context));
    }

    @Override
    public void openSessions(DynaContext context, EditSessionFormType formType, @Nullable String attributeGroup,
            @Nullable Set<String> additionalAttributeCodes, @Nullable String formCode)
    {
        openSessions(context, formType, attributeGroup, additionalAttributeCodes, null, formCode);
    }

    @Override
    public void openSessions(DynaContext context, EditSessionFormType formType, @Nullable String attributeGroup,
            @Nullable Set<String> additionalAttributeCodes, @Nullable ClassFqn newCase, @Nullable String formCode)
    {
        Map<String, Set<String>> editableAttributes = new HashMap<>();
        for (DtObject dto : context.getObjects())
        {
            if (UuidHelper.isTempUuid(dto.getUUID()))
            {
                continue;
            }
            Set<String> attrCodes = new HashSet<>();
            if (context.getMetainfo() != null && attributeGroup != null)
            {
                context.getMetainfo().getGroupAttributes(attributeGroup).stream()
                        .filter(attribute -> Boolean.TRUE.equals(attribute.isEditable()))
                        .map(Attribute::getCode)
                        .filter(getAttributePermissionFilter(dto))
                        .forEach(attrCodes::add);
            }
            if (additionalAttributeCodes != null)
            {
                additionalAttributeCodes.stream()
                        .filter(getAttributePermissionFilter(dto))
                        .forEach(attrCodes::add);
            }
            editableAttributes.put(dto.getUUID(), attrCodes);
        }

        callOpenSessions(context, formType, editableAttributes, newCase, formCode);
    }

    @Override
    public void openSessions(DynaContext context, Form form)
    {
        String objectUuid = Objects.requireNonNull(context.getObject()).getUUID();
        if (UuidHelper.isTempUuid(objectUuid))
        {
            return;
        }
        Map<String, Set<String>> editableAttributes = new HashMap<>();
        Set<String> attrCodes = contentChangeCheckingService.getEditableAttributes(context, form).stream()
                .map(Attribute::getCode)
                .filter(getAttributePermissionFilter(Objects.requireNonNull(context.getObject())))
                .collect(Collectors.toSet());
        editableAttributes.put(objectUuid, attrCodes);

        callOpenSessions(context, EditSessionFormType.edit, editableAttributes, null,
                originProvider.getFormCode(context));
    }

    @Override
    public void openSessions(MassEditActionContext context, EditSessionFormType formType,
            @Nullable Collection<String> additionalAttributeCodes, @Nullable String formCode)
    {
        Map<String, Set<String>> editableAttributes = new HashMap<>();
        Set<String> commonBlockAttributes = new HashSet<>();
        context.getFormBlocks().values().stream()
                .filter(block -> Boolean.TRUE.equals(block.getIsCommonBlock()))
                .findFirst().ifPresent(block -> commonBlockAttributes.addAll(getBlockAttributes(block)));
        for (MassEditBlockInfo blockInfo : context.getFormBlocks().values())
        {
            Set<String> attributeCodes = getBlockAttributes(blockInfo);
            if (null != additionalAttributeCodes)
            {
                attributeCodes.addAll(additionalAttributeCodes);
            }
            attributeCodes.addAll(commonBlockAttributes);
            blockInfo.getObjectUuids().forEach(uuid -> editableAttributes.put(uuid, attributeCodes));
        }

        callOpenSessions(context, formType, editableAttributes, null, formCode);
    }

    @Override
    public void closeSessions(DynaContext context)
    {
        if (context.getEditingSessionIds().isEmpty())
        {
            return;
        }
        List<Long> sessionIds = new ArrayList<>(context.getEditingSessionIds());
        context.getEditingSessionIds().clear();
        dispatch.execute(new CloseEditSessionsAction(sessionIds), new BasicCallback<>());
    }

    @Override
    public void loadPersistentSessionMessages(String subjectUuid, BasicCallback<List<ChangeTrackingMessage>> callback)
    {
        dispatch.execute(new GetPersistentTrackingMessagesAction(subjectUuid),
                new CallbackDecorator<GetTrackingMessagesResponse, List<ChangeTrackingMessage>>(callback)
                {
                    @Override
                    protected List<ChangeTrackingMessage> apply(GetTrackingMessagesResponse from)
                    {
                        return from.getMessages().stream()
                                .map(JSON::<JsObject>parse)
                                .map(messageDeserializer::deserialize)
                                .collect(Collectors.toList()); //NOSONAR
                    }
                });
    }

    private void callOpenSessions(DynaContext context, EditSessionFormType formType,
            Map<String, Set<String>> editableAttributes, @Nullable ClassFqn newCase, @Nullable String formCode)
    {
        if (editableAttributes.values().stream().noneMatch(CollectionUtils::isNotEmpty))
        {
            return;
        }
        List<Long> editingSessionIds = new ArrayList<>(context.getEditingSessionIds());
        context.getEditingSessionIds().clear();
        OpenEditSessionsAction action = new OpenEditSessionsAction(formType.name(), editableAttributes,
                editingSessionIds, newCase);
        action.setFormCode(formCode);
        dispatch.execute(action,
                new BasicCallback<OpenEditSessionsResponse>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(OpenEditSessionsResponse value)
                    {
                        context.getEditingSessionIds().addAll(value.getSessionIds());
                    }
                });
    }

    private static Predicate<String> getAttributePermissionFilter(DtObject object)
    {
        boolean isServiceCall = ServiceCall.FQN.isSameClass(object.getMetaClass());
        return attr -> isServiceCall && SPECIAL_EDITABLE_SC_ATTRIBUTES.contains(attr)
                       || SPECIAL_EDITABLE_ATTRIBUTES.contains(attr) || Boolean.TRUE.equals(
                object.hasWritePermission(attr));
    }

    private static Set<String> getBlockAttributes(MassEditBlockInfo blockInfo)
    {
        return blockInfo.getAttributes().stream()
                .map(attribute -> attribute.getAttribute().getCode())
                .collect(Collectors.toSet());
    }
}
