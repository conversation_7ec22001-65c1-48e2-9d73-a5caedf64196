package ru.naumen.dynaform.client.preview.canvas.scroll;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.Element;
import com.google.inject.assistedinject.Assisted;

/**
 * Содержит логику управления скролами виджета, предоставленными библиотекой "Perfect Scrollbar"
 *
 * <AUTHOR>
 * @since 14 сент. 2015 г.
 */
public class ScrollbarController implements IScrollbarController
{
    private final Element element;
    private final ScrollbarResourcesInitializer initializer;
    private boolean scrollEnabled;

    @Inject
    public ScrollbarController(@Assisted Element element, ScrollbarResourcesInitializer initializer)
    {
        this.element = element;
        this.initializer = initializer;
        scrollEnabled = false;
    }

    /* (non-Javadoc)
     * @see ru.naumen.dynaform.client.preview.canvas.scroll.IScrollbarController#destroy()
     */
    @Override
    public void destroy()
    {
        if (!scrollEnabled)
        {
            return;
        }
        destroy(element);
        scrollEnabled = false;
    }

    /* (non-Javadoc)
     * @see ru.naumen.dynaform.client.preview.canvas.scroll.IScrollbarController#scrollTo(int, int)
     */
    @Override
    public void scrollTo(int left, int top)
    {
        scrollTo(element, left, top);
    }

    /* (non-Javadoc)
     * @see ru.naumen.dynaform.client.preview.canvas.scroll.IScrollbarController#update()
     */
    @Override
    public void update()
    {
        if (scrollEnabled)
        {
            update(element);
        }
        else
        {
            initializer.onComplete(() ->
            {
                enable(element);
                scrollEnabled = true;
            });
        }
    }

    private native void destroy(Element element)
    /*-{
    	$wnd.Ps.destroy(element);
    }-*/;

    private native void enable(Element element)
    /*-{
    	$wnd.Ps.initialize(element);
    }-*/;

    private native void scrollTo(Element elem, int left, int top)
    /*-{
    	elem.scrollLeft = left;
    	elem.scrollTop = top;
    }-*/;

    private native void update(Element element)
    /*-{
    	$wnd.Ps.update(element);
    }-*/;
}
