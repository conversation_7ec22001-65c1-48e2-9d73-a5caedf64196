package ru.naumen.dynaform.client.preview.infopanel;

import java.util.Map;
import java.util.Map.Entry;

import jakarta.inject.Inject;

import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.inject.name.Named;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.preview.InitializationContext;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.preview.FilePreviewMessages;
import ru.naumen.dynaform.client.preview.PreviewContext;
import ru.naumen.dynaform.client.preview.events.FileChangedEvent;
import ru.naumen.dynaform.client.preview.events.FileChangedEventHandler;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 *
 * <AUTHOR>
 * @since 05 окт. 2015 г.
 */
public class FileInfoPanelPresenterImpl<T extends InitializationContext> extends BasicPresenter<FileInfoPanelDisplay>
        implements FileChangedEventHandler, FileInfoPanelPresenter<T>
{
    @Inject
    protected FilePreviewMessages messages;
    @Inject
    protected Formatters formatters;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    private PresentationFactories prsFactories;

    private Map<String, Property<?>> properties;

    protected PreviewContext context;

    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> description;

    @Inject
    public FileInfoPanelPresenterImpl(FileInfoPanelDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        this.properties = new HashMap<>();
    }

    @Override
    public void init(PreviewContext context)
    {
        this.context = context;
    }

    @Override
    public void onFileChanged(FileChangedEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        for (Entry<String, Property<?>> e : properties.entrySet())
        {
            setValue(e.getKey(), e.getValue());
        }
    }

    protected String getFileSize(DtObject file)
    {
        Long fileSize = file.<Long> getProperty(File.FILE_SIZE);
        return formatters.formatFileSize(fileSize, false);
    }

    @Override
    protected void onBind()
    {
        context.getEventBus().addHandler(FileChangedEvent.getType(), this);
        getDisplay().setTitle(messages.fileInfo());

        for (final String attrCode : context.getAttrCodes())
        {
            if (File.DESCRIPTION.equals(attrCode))
            {
                //Не отображаем описание файла в общей таблице. Если описание есть в атрибутах,
                //то оно будет выведено после таблицы с остальными атрибутами
                continue;
            }
            addProperty(attrCode, false);
        }

        if (context.getAttrCodes().contains(File.DESCRIPTION))
        {
            //Если описание есть среди атрибутов выводим его отдельно
            //addProperty(File.DESCRIPTION, true);
            properties.put(File.DESCRIPTION, description);
            ((PropertyBase<?, ?>)description).setWide(true);

            DebugIdBuilder.ensureDebugId(description, "description");

            getDisplay().addProperty(description);
            setValue(File.DESCRIPTION, description);
        }
    }

    private void addProperty(final String attrCode, final boolean wide)
    {
        final PropertyRegistration<Object> propertyRegistration = getDisplay().addProperty(null);

        final Attribute attribute = context.getMetaClass().getAttribute(attrCode);
        String viewPresentationCode = attribute.getViewPresentation().getCode();
        PresentationFactoryView<?> prsFactory = prsFactories.getViewPresentationFactory(viewPresentationCode);

        PresentationContext prsContext = new PresentationContext(attribute).setObject(context.getCurrentFile());

        prsFactory.createWidget(prsContext, new BasicCallback<IsWidget>()
        {
            @Override
            protected void handleSuccess(IsWidget widget)
            {
                Property<Object> property = propertyCreator.create(attribute.getTitle() + ":", widget, wide);

                propertyRegistration.setProperty(property);
                properties.put(attrCode, property);

                setValue(attrCode, property);

                DebugIdBuilder.ensureDebugId(property, attribute.getCode());
            }
        });
    }

    @SuppressWarnings("unchecked")
    private void setValue(String attrCode, Property<?> property)
    {
        DtObject file = context.getCurrentFile();
        Object value = file.getProperty(attrCode);
        if (File.FILE_SIZE.equals(attrCode))
        {
            value = getFileSize(file);
        }
        if (value != null && File.DESCRIPTION.equals(attrCode))
        {
            value = SafeHtmlUtils.fromString((String)value).asString();
        }
        ((Property<Object>)property).setValue(value);
    }
}
