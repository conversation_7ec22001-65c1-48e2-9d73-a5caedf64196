package ru.naumen.dynaform.client.content;

import static ru.naumen.core.shared.Constants.AbstractBO.METACLASS;
import static ru.naumen.core.shared.Constants.Association.AGREEMENT;
import static ru.naumen.core.shared.Constants.Association.CLIENT;
import static ru.naumen.core.shared.Constants.Association.SERVICE;
import static ru.naumen.core.shared.Constants.HAS_NEW_FORMAT_PARAMETERS;
import static ru.naumen.core.shared.Constants.ServiceCall.MASSPROBLEM_SLAVES;
import static ru.naumen.core.shared.Constants.ServiceCall.MASTER_MASS_PROBLEM;
import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.content.sccase.SelectScCaseFormPart;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.GetClientDefaultScParamsAction;
import ru.naumen.core.shared.dispatch.GetClientDefaultScParamsResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.sccase.agrserv.item.AgreementServiceItem;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.DynaContextDecorator;
import ru.naumen.dynaform.client.events.CaseChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedHandler;
import ru.naumen.dynaform.client.events.RefreshContentsOnAddFormEvent;
import ru.naumen.dynaform.client.toolbar.multi.add.bo.sc.ServiceCallHelper;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.SelectScCase;

/**
 * Presenter для {@link SelectScCase выбора типа запроса, соглашения и услуги}
 *
 * <AUTHOR>
 *
 */
public class SelectScCaseContentPresenter
        extends AbstractValidationContentPresenter<SelectScCaseContentDisplay, SelectScCase>
        implements SelectScCaseFormPart.ValuesChangeHandler
{
    /**
     * Срез хранящий старые и изменяемые значения полей.
     */
    private class ChangedValues
    {
        private ClassFqn newCase;
        private DtObject newAgreement;
        private DtObject newService;

        private ClassFqn oldCase;
        private DtObject oldAgreement;
        private DtObject oldService;

        /**
         *  Был ли изменен контрагент, если true - это означает что новые значения являются значениями по умолчанию.
         */
        private boolean changeClient;

        public ChangedValues(boolean changeClient)
        {
            this.changeClient = changeClient;
        }

        public ChangedValues setNewValues(DtObject newAgreement, DtObject newService, ClassFqn newCase)
        {
            this.newAgreement = newAgreement;
            this.newService = newService;
            this.newCase = newCase;
            return this;
        }

        public ChangedValues setOldValues(DtObject oldAgreement, DtObject oldService, ClassFqn oldCase)
        {
            this.oldAgreement = oldAgreement;
            this.oldService = oldService;
            this.oldCase = oldCase;
            return this;
        }
    }

    @Inject
    private Provider<SelectScCaseDynaFormPart> scCaseDynaFormPartProvider;
    @Inject
    private MetainfoServiceSync metainfoServiceSync;
    @Inject
    private ServiceCallHelper serviceCallHelper;
    @Inject
    private DispatchAsync dispatch;

    private SelectScCaseDynaFormPart formPart;
    protected SCParameters scParameters;

    @Inject
    public SelectScCaseContentPresenter(SelectScCaseContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectScCase");
    }

    @Override
    public void onRefresh(final RefreshContentsOnAddFormEvent e)
    {
        formPart.setCaseValueAfterRefresh(e.getNewMetainfo());
    }

    @Override
    public void onValidate(ValidateEvent e)
    {
        validation.setPositionForScrolling(e.getPositionForScrolling());
        if (getDisplay().asWidget().isVisible() && DynaContextDecorator.compareDecorated(context, e.getContext())
            && !validation.validate())
        {
            e.setPositionForScrolling(validation.getPositionForScrolling());
            e.cancel();
        }
        validation.validateAsync(e);
    }

    @Override
    public void onValuesChanged(@Nullable DtObject agreement, @Nullable DtObject service, ClassFqn newCase)
    {
        DtObject object = getContext().getObject();
        DtObject oldAgreement = null == object ? null : object.<DtObject> getProperty(AGREEMENT);
        DtObject oldService = null == object ? null : object.<DtObject> getProperty(SERVICE);
        ClassFqn oldCase = object.getMetainfo();

        setProperty(AGREEMENT, agreement);
        setProperty(SERVICE, service);
        setProperty(METACLASS, newCase);

        ChangedValues changedValues = new ChangedValues(false);
        changedValues.setNewValues(agreement, service, newCase);
        changedValues.setOldValues(oldAgreement, oldService, oldCase);
        valuesChanged(changedValues);
    }

    @Override
    public void refreshDisplay()
    {
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        registerAttributes();
        final DtObject object = getContext().getObject();
        assertObjectNotNull(object);

        formPart = scCaseDynaFormPartProvider.get();
        Collection<MetaClassLite> possibleCases = getContext().getPossibleCases().stream()
                .map(metainfoServiceSync::getMetaClassLite)
                .collect(Collectors.toList());
        formPart.init(getDisplay(), getContext(), validation, possibleCases, object,
                SelectScCase.hasSelectScCaseContent(getContext().getForm(), true));
        scParameters = metainfoServiceSync.getSettings().getScParameters();
        formPart.initScParameters(scParameters);
        formPart.setValuesChangeHandler(this);
        formPart.setShowAgreementServiceDescription(content.isShowAttrDescription());
        formPart.bindProperties();

        MetaClass classMetaClass = metainfoServiceSync.getMetaClass(getContext().getMetainfo().getFqn().fqnOfClass());
        final Attribute clientAttribute = classMetaClass.getAttribute(CLIENT);
        addContextHandler(FieldChangedEvent.getType(), new FieldChangedHandler()
        {
            @Override
            public void onFieldChanged(FieldChangedEvent e)
            {
                if (!e.isApplicable(context, content, null, clientAttribute))
                {
                    return;
                }
                onClientChanged(e, new BasicCallback<ChangedValues>()
                {
                    @Override
                    protected void handleSuccess(ChangedValues changedValues)
                    {
                        valuesChanged(changedValues);
                    }
                });
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != formPart)
        {
            formPart.unbind();
        }
    }

    protected void registerAttributes()
    {
        getContext().registerAttribute(getAttribute(AGREEMENT), getAttribute(SERVICE), getAttribute(METACLASS));
    }

    private void fireChangeEvent(String name)
    {
        FieldChangedEvent changeEvent = new FieldChangedEvent(getAttribute(name), getContext(), getContent(),
                getProperty(name));
        eventBus.fireEvent(changeEvent);
        getContext().getEventBus().fireEvent(changeEvent);
    }

    private Attribute getAttribute(String name)
    {
        return getContext().getMetainfo().getAttribute(name);
    }

    private <T> T getProperty(String name)
    {
        DtObject object = getContext().getObject();
        assertObjectNotNull(object);
        return object.<T> getProperty(name);
    }

    private boolean isChangeCase(ChangedValues changedValues)
    {
        ScCaseFieldsOrderSettings orderSettings = formPart.getOrderSettings();
        boolean isCaseChanged = !Objects.equals(changedValues.oldCase, changedValues.newCase);
        //При открытии формы в случае если установлен тип запроса по-умолчанию обязательно нужно
        //послать событие смены типа
        isCaseChanged |= !Objects.equals(changedValues.newCase, getContext().getMetainfo().getFqn())
                         && changedValues.newCase != null && !changedValues.newCase.isClass();

        boolean isAgreementFilled = changedValues.newAgreement != null;
        boolean isOldAgreementFilled = changedValues.oldAgreement != null;
        if (!isAgreementFilled && isOldAgreementFilled)
        {
            return isCaseChanged
                   && (orderSettings.isCaseMain() || changedValues.newCase == null || changedValues.newCase.isClass());
        }
        return isCaseChanged && (orderSettings.isCaseMain() || isAgreementFilled || changedValues.newCase == null);
    }

    /**
     * Возвращает true, если форма была открыта по ссылке, сгенерированной с помощью web.api,
     * содержащей параметры, передаваемые в "новом" формате, с конструкцией {fast:true}
     */
    private boolean isWebApiAddLinkWithFastTrue()
    {
        return getContext().getParentContext().getContextProperty(HAS_NEW_FORMAT_PARAMETERS);
    }

    /**
     * Обрабатывает событие изменения контрагента, возвращает набор новых данных (зн. по умолчанию)
     */
    private void onClientChanged(final FieldChangedEvent e, final AsyncCallback<ChangedValues> callback)
    {
        final DtObject newClient = (DtObject)((HasValueOrThrow<?>)e.getProperty()).getValue();
        MetaClass newClientMetaClass = newClient != null ? metainfoServiceSync.getMetaClass(newClient.getMetaClass())
                : null;
        formPart.setClientUUID(null == newClient ? null : newClient.getUUID());
        boolean isEmptyDefaultScTypeSettings = serviceCallHelper.isEmptyDefaultScTypeSettings(newClientMetaClass);
        /* Если форма добавления была открыта по ссылке, сгенерированной с помощью web.api,
         * содержащей параметры, передаваемые в "новом" формате, с конструкцией {fast:true}
         * и нет дефолтных параметров добавления запроса,
         * то значение oldObject нужно установить в null, для того, чтобы при смене контрагента
         * произошла перерисовка формы и применились выданные на атрибуты права и роли
         */
        DtObject oldObject = isWebApiAddLinkWithFastTrue() && null != newClient && isEmptyDefaultScTypeSettings
                ? null : getContext().getObject();
        DtObject oldAgreement = null == oldObject ? null : oldObject.getProperty(AGREEMENT);
        DtObject oldService = null == oldObject ? null : oldObject.getProperty(SERVICE);
        ClassFqn oldCase = null == oldObject ? null : oldObject.getMetainfo();

        final ChangedValues cv = new ChangedValues(true);
        cv.setOldValues(oldAgreement, oldService, oldCase);

        // если контрагент может быть необязательным, то сбрасывать не нужно
        if (null == newClient && !scParameters.isClientRequiredEditable())
        {
            ClassFqn newDefaultFqn = oldCase != null ? oldCase.fqnOfClass() : null;
            oldObject.setProperty(AGREEMENT, null);
            oldObject.setProperty(SERVICE, null);
            oldObject.setProperty(METACLASS, null);
            cv.setNewValues(null, null, newDefaultFqn);
            callback.onSuccess(cv);
        }
        else if (newClient != null && !isEmptyDefaultScTypeSettings)
        {
            GetClientDefaultScParamsAction action = new GetClientDefaultScParamsAction(
                    newClient.getUUID(), new ArrayList<>(this.formPart.getPossibleCasesFqns()));
            if (context.getDefaultMetaClass() != null)
            {
                action.setDefaultFqn(context.getDefaultMetaClass().getFqn());
            }
            //параметры объекта нужны в скриптах фильтрации
            if (scParameters.isFilterAgreements() || scParameters.isFilterServices())
            {
                action.setObject(context.getObject());
            }
            dispatch.execute(action, new BasicCallback<GetClientDefaultScParamsResponse>()
            {
                @Override
                protected void handleSuccess(GetClientDefaultScParamsResponse response)
                {
                    setClientInfo(newClient);

                    SimpleDtObject agreement = response.getAgreement();
                    SimpleDtObject service = response.getService();
                    TitledClassFqn scType = response.getScType();

                    context.getObject().setProperty(AGREEMENT, agreement);
                    context.getObject().setProperty(SERVICE, service);
                    context.getObject().setProperty(METACLASS, scType);

                    updateValues(cv);
                    callback.onSuccess(cv);
                }
            });
        }
        else
        {
            updateValues(cv);
            callback.onSuccess(cv);
        }
    }

    private void setClientInfo(DtObject client)
    {
        ClassFqn clientFqn = client.getMetaClass().fqnOfClass();
        if (Constants.Team.FQN.equals(clientFqn))
        {
            updateClient(null, client, null);
        }
        else if (Constants.OU.FQN.equals(clientFqn))
        {
            updateClient(null, null, client);
        }
        else if (client instanceof TreeDtObject)
        {
            DtObject parent = ((TreeDtObject)client).getParent();
            ClassFqn parentFqn = parent.getMetaClass();
            if (Constants.Team.FQN.equals(parentFqn))
            {
                updateClient(client, parent, null);
            }
            else
            {
                updateClient(client, null, parent);
            }
        }
        else
        {
            updateClient(client, null, null);
        }
    }

    private void setClientLinkName()
    {
        DtObject agreement = getProperty(AGREEMENT);
        DtObject service = getProperty(SERVICE);
        DtObject client = getProperty(CLIENT);
        if (null == client)
        {
            setProperty(Constants.Association.CLIENT_LINK_NAME, "");
        }
        else
        {
            StringBuilder sb = new StringBuilder();
            sb.append(client.getTitle());
            if (agreement != null)
            {
                sb.append(" / ").append(agreement.getTitle());
            }
            if (service != null)
            {
                sb.append(" / ").append(service.getTitle());
            }
            setProperty(Constants.Association.CLIENT_LINK_NAME, sb.toString());
        }
        fireChangeEvent(Constants.Association.CLIENT_LINK_NAME);
    }

    private void setProperty(String name, @Nullable Object value)
    {
        DtObject object = getContext().getObject();
        assertObjectNotNull(object);
        object.setProperty(name, value);
    }

    private void updateClient(IUUIDIdentifiable employee, IUUIDIdentifiable team, IUUIDIdentifiable ou)
    {
        updateClientAggregate(Association.CLIENT_EMPLOYEE, employee);
        updateClientAggregate(Association.CLIENT_TEAM, team);
        updateClientAggregate(Association.CLIENT_OU, ou);
    }

    private void updateClientAggregate(String code, IUUIDIdentifiable object)
    {
        setProperty(code, object);
        fireChangeEvent(code);
        formPart.getAgreementServiceContext().getFiltrationProperties().setProperty(code, getProperty(code));
    }

    /**
     * Вычисление новых значений полей при смене контрагента
     * @param cv
     */
    private void updateValues(ChangedValues cv)
    {
        DtObject newAgreement = null;
        DtObject newService = null;
        ClassFqn newCase = null;
        DtObject newObject = getContext().getObject();
        newService = getProperty(SERVICE);
        newAgreement = getProperty(AGREEMENT);
        newCase = this.getProperty(METACLASS);
        Set<ClassFqn> possibleCases = new HashSet<>(context.getPossibleCases());
        if (null != cv.oldService || null != cv.oldAgreement)
        {
            // Сохраняем значения по умолчанию
            formPart.setDefaultItem(new AgreementServiceItem(newAgreement, newService));
            newAgreement = cv.oldAgreement;
            newService = cv.oldService;
        }

        if (!(Constants.ServiceCall.FQN == cv.oldCase || cv.oldCase == null) && possibleCases.contains(cv.oldCase))
        {
            // Сохраняем тип запроса по умолчанию
            formPart.setDefaultFqn(newCase);
            newCase = cv.oldCase;
        }
        newObject.setProperty(AGREEMENT, newAgreement);
        newObject.setProperty(SERVICE, newService);
        newObject.setProperty(METACLASS, newCase);
        cv.setNewValues(newAgreement, newService, newCase);
    }

    /**
     * Обрабатывает изменения типа запроса, соглашения, услуги. Посылает события в шину и контролирует обновление
     * свойств (виджетов).
     * @param changedValues
     */
    private void valuesChanged(ChangedValues changedValues)
    {
        ScCaseFieldsOrderSettings orderSettings = formPart.getOrderSettings();

        String typeRefreshFromPart = changedValues.changeClient ? SelectScCaseFormPart.REFRESH_PROPERTIES : null;
        setClientLinkName();
        if (null != getProperty(MASTER_MASS_PROBLEM))
        {
            setProperty(MASTER_MASS_PROBLEM, null);
            fireChangeEvent(MASTER_MASS_PROBLEM);
        }
        Object prop = getProperty(MASSPROBLEM_SLAVES);
        if (!ObjectUtils.isEmpty(prop))
        {
            setProperty(MASSPROBLEM_SLAVES, new HashSet<>());
            fireChangeEvent(MASSPROBLEM_SLAVES);
        }

        if (!Objects.equals(changedValues.oldAgreement, changedValues.newAgreement)
            || !Objects.equals(changedValues.oldService, changedValues.newService))
        {
            if (orderSettings.isAgsMain())
            {
                typeRefreshFromPart = changedValues.changeClient ? SelectScCaseFormPart.REFRESH_PROPERTIES
                        : SelectScCaseFormPart.REFRESH_CASE;
            }
            else
            {
                if (null == typeRefreshFromPart)
                {
                    typeRefreshFromPart = SelectScCaseFormPart.REFRESH_AGREEMENT_SERVICE;
                }
            }
            fireChangeEvent(AGREEMENT);
            fireChangeEvent(SERVICE);
        }
        else
        {
            //Все равно помечаем атрибуты как измененные, иначе может не работать логика значений по-умолчанию
            context.setAttributeChanged(getAttribute(AGREEMENT));
            context.setAttributeChanged(getAttribute(SERVICE));
        }

        if (isChangeCase(changedValues))
        {
            if (orderSettings.isCaseMain() && null == typeRefreshFromPart)
            {
                typeRefreshFromPart = SelectScCaseFormPart.REFRESH_AGREEMENT_SERVICE;
            }
            ClassFqn newCase = changedValues.newCase;

            CaseChangedEvent event = new CaseChangedEvent(newCase);
            eventBus.fireEvent(event);
            getContext().getEventBus().fireEvent(event);
            fireChangeEvent(METACLASS);

            formPart.setCurrentFqn(newCase);
        }
        IProperties filtrationProperties = formPart.getAgreementServiceContext().getFiltrationProperties();
        for (String name : getContext().getObject().propertyNames())
        {
            filtrationProperties.setProperty(name, getContext().getObject().getProperty(name));
        }
        if (null == filtrationProperties.getProperty(METACLASS))
        {
            filtrationProperties.setProperty(METACLASS, getContext().getMetainfo().getFqn());
        }
        formPart.onRefresh(typeRefreshFromPart, changedValues.newAgreement, changedValues.newService,
                changedValues.newCase);
    }
}
