package ru.naumen.dynaform.client.toolbar.multi.add.bo.sc;

import static ru.naumen.core.shared.Constants.AbstractBO.METACLASS;
import static ru.naumen.core.shared.Constants.Association.AGREEMENT;
import static ru.naumen.core.shared.Constants.Association.SERVICE;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Вспомогательные методы для формы регистрации запроса
 *
 * <AUTHOR>
 */
public class ServiceCallHelper
{
    @Inject
    private MetainfoServiceSync metainfoService;

    public AgreementServiceSetting getAgreementServiceSettings()
    {
        Settings settings = metainfoService.getSettings();
        return settings.getScParameters().getAgreementServiceSetting();
    }

    /**
     * @return true, если в метаклассе не заполнены настройки по-умолчанию типа запроса
     */
    public boolean isEmptyDefaultScTypeSettings(@Nullable MetaClass metaClass)
    {
        if (metaClass == null)
        {
            return true;
        }
        IProperties properties = metaClass.getProperties();
        Object defaultAgreement = properties.getProperty(MetaClassProperties.DEFAULT_CLIENT_AGREEMENT);
        Object defaultService = properties.getProperty(MetaClassProperties.DEFAULT_CLIENT_SERVICE);
        Object defaultScType = properties.getProperty(MetaClassProperties.DEFAULT_SC_TYPE);
        return defaultAgreement == null && defaultService == null && defaultScType == null;
    }

    /**
     * Устанавливает соглашение, услугу и тип запроса согласно настройкам метакласса клиента
     *
     * @param context
     * @param clientMetainfo
     * @param updateAgreementAndCase false - выставлять только если объект не содержит информации об соглашении и
     *                               услуге,
     *        true перезатирать значение
     */
    public void updateDefaultAgreement(FormContext context, MetaClass clientMetainfo, boolean updateAgreementAndCase)
    {
        if (null != clientMetainfo)
        {
            IProperties cmcProps = clientMetainfo.getProperties();
            if (updateAgreementAndCase || ObjectUtils.isEmpty(context.getObject().<Object> getProperty(AGREEMENT)))
            {
                final Object defaultAgreement = cmcProps.getProperty(MetaClassProperties.DEFAULT_CLIENT_AGREEMENT);
                final Object defaultService = cmcProps.getProperty(MetaClassProperties.DEFAULT_CLIENT_SERVICE);

                AgreementServiceSetting setting = getAgreementServiceSettings();
                if (null == defaultService && !setting.isWithAgreements())
                {
                    context.getObject().setProperty(AGREEMENT, null);
                }
                else
                {
                    context.getObject().setProperty(AGREEMENT, defaultAgreement);
                }
                if (setting.isWithServices())
                {
                    context.getObject().setProperty(SERVICE, defaultService);
                }
            }
            if (updateAgreementAndCase || ObjectUtils.isEmpty(context.getObject().<Object> getProperty(METACLASS)))
            {
                context.getObject().setProperty(METACLASS, cmcProps.getProperty(MetaClassProperties.DEFAULT_SC_TYPE));
            }
        }
    }
}
