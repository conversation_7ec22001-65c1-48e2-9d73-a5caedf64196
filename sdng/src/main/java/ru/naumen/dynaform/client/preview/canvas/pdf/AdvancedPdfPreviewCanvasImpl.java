package ru.naumen.dynaform.client.preview.canvas.pdf;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Node;
import com.google.gwt.dom.client.Style;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.Widget;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.preview.FilePreviewCss;
import ru.naumen.dynaform.client.preview.FilePreviewMessages;
import ru.naumen.dynaform.client.preview.FilePreviewResources;
import ru.naumen.dynaform.client.preview.canvas.DocumentContentService.DocumentRequest;
import ru.naumen.dynaform.client.preview.canvas.NotAvailibleMessageHtmlFactory;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvas;
import ru.naumen.dynaform.client.preview.events.CanvasIsReadyEvent;

/**
 * Виджет, отображающий содержимое .pdf файлов в интерфейсе предварительного просмотра.
 * Использует библиотеку pdfjs и ее доработанный для NSD viewer
 *
 * <AUTHOR>
 * @since 18 сент. 2015 г.
 */
public class AdvancedPdfPreviewCanvasImpl implements PreviewCanvas
{
    @Inject
    private NotAvailibleMessageHtmlFactory notAvailableMessageFactory;
    @Inject
    private FilePreviewMessages messages;

    protected Panel panel;
    protected DocumentRequest request;
    protected EventBus eventBus;
    protected FilePreviewResources resources = GWT.create(FilePreviewResources.class);

    @Inject
    public AdvancedPdfPreviewCanvasImpl(@Assisted DtObject file, @Assisted EventBus eventBus,
            PdfResourcesInitializer initializer)
    {
        this.eventBus = eventBus;
        this.request = new DocumentRequest(false, file);
        panel = new HTMLPanel("");
        resources.style().ensureInjected();
        panel.setStyleName(resources.style().viewer());

        panel.addAttachHandler(event ->
        {
            if (!event.isAttached())
            {
                return;
            }

            initializer.onComplete(() ->
            {
                Element elem = panel.getElement();
                elem.setClassName("pdfViewer");
                if (elem.getParentNode() == null)
                {
                    //К моменту когда будет готова библиотека пользователь уже
                    //может переключиться на другой файл
                    return;
                }
                Node parentNode = elem.getParentNode().getParentNode();
                Element container = Element.as(parentNode);
                container.getStyle().setOverflow(Style.Overflow.AUTO);
                init(request.toUrl(), elem, container);
            });
        });
    }

    @Override
    public Widget asWidget()
    {
        return panel;
    }

    @Override
    public void destroy()
    {
    }

    @Override
    public void refresh()
    {
    }

    //Используется в js
    private void throwException(int status) //NOPMD
    {
        String msg = notAvailableMessageFactory.getHtml(request.getFile()).asString();
        if (status == 409)
        {
            msg = notAvailableMessageFactory.getHtml(request.getFile(), true).asString();
        }
        if (status == 204)
        {
            msg = messages.fileNotAvailible();
        }

        panel.clear();

        HTMLPanel htmlPanel = new HTMLPanel("<span>" + msg + "</span>");
        FilePreviewCss style = resources.style();
        style.ensureInjected();
        htmlPanel.setStyleName(style.captionCanvas());

        panel.setStyleName(style.canvasLocker());
        panel.add(htmlPanel);
        panel.setVisible(true);

        fireReady();
    }

    private native void init(String url, Element viewer, Node container)
    /*-{
       var that = this;
      $wnd.initPdfViewer(
        url,
        container,
        viewer,
        function() { // onReady
          <EMAIL>::fireReady(*)();
        },
        function(status) { // onError
          <EMAIL>::throwException(*)(status);
        }
      );
    }-*/;

    private void fireReady()
    {
        Scheduler.get().scheduleDeferred(new ScheduledCommand()
        {
            @Override
            public void execute()
            {
                eventBus.fireEvent(new CanvasIsReadyEvent());
            }
        });
    }
}
