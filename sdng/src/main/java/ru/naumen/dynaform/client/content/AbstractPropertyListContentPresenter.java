package ru.naumen.dynaform.client.content;

import static ru.naumen.core.client.content.ContextUtils.getDecoratedContext;
import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.base.Predicates;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Widget;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.ContextChangedHandler;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenter;
import ru.naumen.core.client.events.DeleteObjectEvent;
import ru.naumen.core.client.events.DeleteObjectEventHandler;
import ru.naumen.core.client.events.ObjectLoadedEvent;
import ru.naumen.core.client.events.ObjectLoadedHandler;
import ru.naumen.core.client.events.RefreshContentWithCompAttrsEvent;
import ru.naumen.core.client.events.RefreshContentWithCompAttrsHandler;
import ru.naumen.core.client.events.UpdatePermissionsEvent;
import ru.naumen.core.client.events.UpdatePermissionsHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.AbstractSelectCellList;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.ToolPanelLocation;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.PropertyListEmptyValueChecker;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.dynaform.client.ChildDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformContentFactory;
import ru.naumen.dynaform.client.actioncommand.AddActionContext;
import ru.naumen.dynaform.client.actioncommand.DefaultAddActionContext;
import ru.naumen.dynaform.client.css.DynaformContentLayoutCss;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedHandler;
import ru.naumen.dynaform.client.widgets.DynaformObjectPropertyList;
import ru.naumen.dynaform.client.widgets.DynaformWidgetResources;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.AttributeToolPanel;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;

/**
 * Базовый функционал контента со списком свойств
 * <AUTHOR>
 *
 * @param <C>
 * @param <R>
 */
public class AbstractPropertyListContentPresenter<C extends PropertyList, R extends DynaContext>
        extends AbstractInfoContentPresenter<HasPropertiesContentDisplay, C, R>
        implements UpdatePermissionsHandler
{
    @Override
    protected void initStyles()
    {
        super.initStyles();
        getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.contentLayout().propertyListContent());
    }

    protected class MyContextChangedHandler implements ContextChangedHandler<Context>
    {
        @Override
        public void onContextChanged(ContextChangedEvent<Context> e)
        {
            refreshDisplay();
        }
    }

    protected class PropertyListFieldChangedHandler implements FieldChangedHandler
    {
        @Override
        public void onFieldChanged(FieldChangedEvent e)
        {
            if (null == getContext())
            {
                return;
            }
            DtObject objectFromEvent = e.getContext().getObject();
            if (objectFromEvent == null)
            {
                return;
            }
            // Изменение атрибута типа Файл через форму добавления тоже порождает FieldChanged
            if (File.FQN.equals(objectFromEvent.getMetaClass()) && e.getContext() instanceof DefaultAddActionContext)
            {
                objectFromEvent = e.getContext().getParentContext().getObject();
            }

            DtObject object = getContext().getObject();
            if (ObjectUtils.equals(object, objectFromEvent) && changedAttributeIsInMyGroup(e))
            {
                changeMyAttributeValueToo(e, objectFromEvent);
            }
        }

        protected boolean changedAttributeIsInMyGroup(FieldChangedEvent e)
        {
            String attributeGroup = getContent().getAttributeGroup();
            if (!getContext().getMetainfo().getAttributeGroupCodes().contains(attributeGroup))
            {
                return false;
            }
            List<Attribute> attributes = getContext().getMetainfo().getGroupAttributes(attributeGroup);
            return attributes.contains(e.getAttribute());
        }

        protected void changeMyAttributeValueToo(FieldChangedEvent e, @Nullable DtObject object)
        {
            String attrCode = e.getAttribute().getCode();
            assertObjectNotNull(object);
            Object value = object.getProperty(attrCode);
            changePropertyValue(attrCode, value);
            refreshDisplay();
        }
    }

    protected class PropertyListObjectLoadedHandler implements ObjectLoadedHandler
    {
        @Override
        public void onObjectLoaded(ObjectLoadedEvent e)
        {
            if (getContext() == null)
            {
                return;
            }
            DtObject loadedObject = Iterables.find(e.getObjects(), Predicates.equalTo(getContext().getObject()), null);
            if (loadedObject == null)
            {
                return;
            }
            updatePropertyValues(loadedObject);
        }

        protected void updatePropertyValues(DtObject loadedObject)
        {
            DtObject object = getContext().getObject();
            if (object == null)
            {
                return;
            }

            MetaClass oldMetaClass = getContext().getMetainfo();
            Map<String, Object> properties = propertyRegistrations.keySet().stream()
                    .filter(loadedObject::hasProperty)
                    .collect(Collectors.toMap(Function.identity(), loadedObject::getProperty));
            object.putAll(properties);
            if (oldMetaClass != null && oldMetaClass.getFqn().equals(loadedObject.getMetaClass()))
            {
                properties.forEach(AbstractPropertyListContentPresenter.this::changePropertyValue);
            }
        }
    }

    @Inject
    private PresentationFactories prsFactories;
    @Inject
    private DynaformContentFactory contentFactory;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    protected MetainfoServiceSync metainfoService;
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected AttributePropertiesExtensionService propertiesExtensionService;
    @Inject
    protected ContentHighlighter contentHighlighter;

    protected ToolPanelContentPresenter<DynaContext> toolPanelPresenter;

    private final Map<String, ToolPanelContentPresenter<DynaContext>> attributeToolPanels;

    protected Map<String, PropertyRegistration<Object>> propertyRegistrations = new HashMap<>();

    private final ReadyState attributesToolPanelLoading;

    @Inject
    public AbstractPropertyListContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus,
            String debugPrefix)
    {
        super(display, eventBus, debugPrefix);
        attributeToolPanels = Maps.newLinkedHashMap();
        attributesToolPanelLoading = new ReadyState(this);
    }

    private void changePropertyValue(@Nullable String attrCode, @Nullable Object value)
    {
        BiConsumer<Property<Object>, Object> updater = (p, v) ->
        {
            p.setValue(v);
            DynaformObjectPropertyList.customizeToolPanelDependOnAttrValue(p.getToolPanelWidget(), p);
        };
        updateProperty(attrCode, value, updater);
    }

    public void highlightProperty(@Nullable String attrCode)
    {
        BiConsumer<Property<Object>, Object> updater = (p, v) ->
        {
            Widget captionWidget = p.getCaptionWidget().asWidget();
            Element tableRowElement = p.isWide()
                    ? captionWidget.getParent().getElement().getParentElement()
                    : captionWidget.getElement().getParentElement().getParentElement();
            this.contentHighlighter.highlight(tableRowElement, 5);
        };
        updateProperty(attrCode, null, updater);
    }

    private void updateProperty(@Nullable String attrCode, @Nullable Object value,
            BiConsumer<Property<Object>, Object> updater)
    {
        if (StringUtilities.isEmpty(attrCode))
        {
            return;
        }
        PropertyRegistration<Object> propertyRegistration = propertyRegistrations.get(attrCode);
        if (propertyRegistration == null)
        {
            return;
        }
        updater.accept(propertyRegistration.getProperty(), value);
    }

    @Override
    public void onReveal()
    {
        super.onReveal();
        refreshDisplay();
    }

    @Override
    public void onUpdatePermissions(UpdatePermissionsEvent event)
    {
        if (null != toolPanelPresenter)
        {
            toolPanelPresenter.onUpdatePermissions(event);
        }

        if (null != attributeToolPanels)
        {
            for (ToolPanelContentPresenter<DynaContext> attrToolPanel : attributeToolPanels.values())
            {
                attrToolPanel.onUpdatePermissions(event);
            }
        }
    }

    @Override
    public void refreshDisplay()
    {
        if (toolPanelPresenter != null)
        {
            toolPanelPresenterRefreshDisplay();
        }
        refreshProperties();
        getDisplay().setCaptionVisible(getContent().isShowCaption());
        refreshContentVisibility();
    }

    protected void createPropertyRegistration(DtObject dto, Attribute attr)
    {
        PropertyRegistration<Object> registration = createProperty(attr, dto.getProperty(attr.getCode()));
        if (registration != null)
        {
            propertyRegistrations.put(attr.getCode(), registration);
            DebugIdBuilder.ensureDebugId(registration.getProperty(), attr.getCode());
        }
    }

    protected List<Attribute> getAttributes()
    {
        return getContext().getMetainfo().getGroupAttributes(getContent().getAttributeGroup());
    }

    protected DeleteObjectEventHandler getObjectDeleteHandler()
    {
        return new DeleteObjectEventHandler()
        {
            @Override
            public void onObjectDeleted(DeleteObjectEvent e)
            {
                if (null == getContext())
                {
                    return;
                }
                DtObject dto = context.getObject();
                if (null == dto || null == e.getRelAttrCode())
                {
                    return;
                }
                GetDtObjectAction dataAction = new GetDtObjectAction(dto.getUUID());
                dispatch.execute(dataAction, new BasicCallback<GetDtObjectResponse>(getReadyState(), getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetDtObjectResponse value)
                    {
                        context.setObjects(Collections.singleton(value.getObj()));
                        refreshProperties();
                        refreshDisplay();
                    }
                });

                if (dto.hasProperty(e.getRelAttrCode()))
                {
                    Object value = dto.getProperty(e.getRelAttrCode());
                    if (value instanceof Collection)
                    {
                        List<?> objs = Lists.newArrayList((Collection<?>)value);
                        DtObject dtObject = (DtObject)objs.stream()
                                .filter(obj -> ((DtObject)obj).getUUID().equals(e.getUUID()))
                                .findFirst().orElse(null);
                        if (dtObject != null)
                        {
                            objs.remove(dtObject);
                            dto.setProperty(e.getRelAttrCode(), objs);
                            changePropertyValue(e.getRelAttrCode(), objs);
                        }
                    }
                    else if (value != null && value.equals(e.getUUID()))
                    {
                        DtObject dtObject = (DtObject)value;
                        if (dtObject.getUUID().equals(e.getUUID()))
                        {
                            dto.setProperty(e.getRelAttrCode(), null);
                            changePropertyValue(e.getRelAttrCode(), null);
                        }
                    }
                    refreshProperties();
                    refreshDisplay();
                }
            }
        };
    }

    protected ObjectLoadedHandler getObjectLoadedHandler()
    {
        return new PropertyListObjectLoadedHandler();
    }

    protected ReadyState getReadyState()
    {
        return getContext().getReadyState();
    }

    protected final List<Attribute> getVisibleAttributes()
    {
        if (context.getObject() == null)
        {
            return getAttributes();
        }
        return propertiesExtensionService.getVisibleAttributes(getAttributes(), context, false);
    }

    /**
     * Метод проверяет наличие права на чтение для конкретного атрибута.
     * Действует разрешительный тип правового регулирования: "Все, что не разрешено - запрещено".
     */
    protected boolean hasReadPermission(DtObject dto, Attribute attr)
    {
        if (!metainfoService.isElementEnabled(attr))
        {
            return false;
        }
        return Boolean.TRUE.equals(dto.hasReadPermission(attr.getCode()));
    }

    protected boolean isAnyAttributeVisible()
    {
        return !propertyRegistrations.isEmpty();
    }

    @Override
    protected boolean isContentVisible()
    {
        return super.isContentVisible() &&
               (isAnyAttributeVisible() || !getContent().getToolPanel().getTools().isEmpty());
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        addContextHandler(ContextChangedEvent.getType(), new MyContextChangedHandler());
        //Этот Handler обрабатывает изменение значений атрибутов в другом EditablePropertyList на форме, чтобы
        //изменения отражались и в PropertyList
        addContextHandler(FieldChangedEvent.getType(), new PropertyListFieldChangedHandler());
        registerHandler(eventBus.addHandler(ObjectLoadedEvent.getType(), getObjectLoadedHandler()));
        registerHandler(eventBus.addHandler(DeleteObjectEvent.getType(), getObjectDeleteHandler()));
        registerHandler(
                eventBus.addHandler(RefreshContentWithCompAttrsEvent.getType(), new RefreshContentWithCompAttrsHandler()
                {
                    @Override
                    public void refreshContent(RefreshContentWithCompAttrsEvent e)
                    {
                        if (content.getUuid().equalsIgnoreCase(e.getContentUUID()))
                        {
                            if (content instanceof RelObjPropertyList)
                            {
                                eventBus.fireEvent(new ObjectLoadedEvent(e.getDtObject()));
                            }
                            refreshDisplay();
                        }
                    }
                }));

        WidgetResources.INSTANCE.propertyDescription().ensureInjected();
        bindAttributesToolPanels();
        contentFactory.build(getContent().getToolPanel(), getContext(),
                new BasicCallback<ToolPanelContentPresenter<DynaContext>>(getReadyState(), getDisplay())
                {
                    @Override
                    protected void handleSuccess(ToolPanelContentPresenter<DynaContext> value)
                    {
                        toolPanelPresenter = value;
                        toolPanelPresenter.getDisplay()
                                .addStyleName(WidgetResources.INSTANCE.buttons().contentToolPanel());
                        getDisplay().bindActionBar(toolPanelPresenter.getDisplay());
                        toolPanelPresenterRefreshDisplay();
                    }
                });
    }

    protected void bindAttributesToolPanels()
    {
        for (final Attribute attribute : getContext().getMetainfo()
                .getGroupAttributes(getContent().getAttributeGroup()))
        {
            if (getContent().hasAttributeToolPanel(attribute.getCode()))
            {
                attributesToolPanelLoading.notReady();
                AttributeToolPanel attributeToolPanel = getContent().getAttributeToolPanel(attribute.getCode());
                contentFactory.build(attributeToolPanel.getToolPanel(), getContext(),
                        new BasicCallback<ToolPanelContentPresenter<DynaContext>>(getReadyState(), getDisplay())
                        {
                            @Override
                            protected void handleFailure(String msg)
                            {
                                super.handleFailure(msg);
                                attributesToolPanelLoading.ready();
                            }

                            @Override
                            protected void handleSuccess(ToolPanelContentPresenter<DynaContext> value)
                            {
                                ToolPanelContentPresenter<DynaContext> attrToolPanelPresenter = value;
                                attributeToolPanels.put(attribute.getCode(), attrToolPanelPresenter);
                                customizeAttributeToolPanel(attrToolPanelPresenter, attributeToolPanel);
                                attributesToolPanelLoading.ready();
                            }

                            /**
                             * Выставление стилей в соответствии с настройками панели инструментов для атрибута
                             */
                            private void customizeAttributeToolPanel(
                                    ToolPanelContentPresenter<DynaContext> attrToolPanelPresenter,
                                    AttributeToolPanel content)
                            {
                                Widget display = attrToolPanelPresenter.getDisplay().asWidget();
                                DynaformContentLayoutCss css = DynaformWidgetResources.INSTANCE.contentLayout();
                                if (content.getLocation() == ToolPanelLocation.TO_RIGHT_OF_ATTR)
                                {
                                    display.addStyleName(css.attributeToolBarRight());
                                }
                                if (content.isShowOnlyOnContentHover())
                                {
                                    display.addStyleName(css.showOnlyOnHover());
                                }
                            }
                        });
            }
        }

        attributesToolPanelLoading.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                refreshProperties();
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != toolPanelPresenter)
        {
            toolPanelPresenter.unbind();
        }

        if (null != attributeToolPanels)
        {
            for (ToolPanelContentPresenter<DynaContext> attrToolPanel : attributeToolPanels.values())
            {
                if (attrToolPanel != null)
                {
                    attrToolPanel.unbind();
                }
            }
        }
    }

    protected void refreshProperties()
    {
        getDisplay().clearProperties();
        propertyRegistrations.clear();
        if (null == context)
        {
            return;
        }
        final DtObject dto = context.getObject();
        if (null == dto)
        {
            return;
        }

        for (final Attribute attr : getVisibleAttributes())
        {
            if (hasReadPermission(dto, attr))
            {
                createPropertyRegistration(dto, attr);
            }
        }
    }

    /**
     * Создание свойства соотв. атрибуту
     * @param attr метаинформация атрибута
     * @param attrValue значение атрибута
     * @return
     */
    PropertyRegistration<Object> createProperty(final Attribute attr, @Nullable final Object attrValue)
    {
        if (PropertyListEmptyValueChecker.isEmpty(attr, attrValue) && (attr.isHiddenWhenEmpty()
                                                                       || attr.isHiddenAttrCaption()
                                                                          && !attributeToolPanels.containsKey(
                attr.getCode())))
        {
            return null;
        }
        final PropertyRegistration<Object> propertyRegistration = getDisplay().add(null);
        String viewPresentationCode = attr.getViewPresentation().getCode();
        PresentationFactoryView<?> prsFactory = prsFactories.getViewPresentationFactory(viewPresentationCode);
        DtObject dto = getContext().getObject();
        PresentationContext context = new PresentationContext(attr).setObject(dto);
        final boolean isWide = ru.naumen.metainfo.shared.Constants.Presentations.WIDE_ATTR_CODES
                .contains(viewPresentationCode);
        prsFactory.createWidget(context, new ContextualCallback<IsWidget>(getContext())
        {
            @Override
            protected void handleSuccess(IsWidget widget)
            {
                String title = Boolean.TRUE.equals(attr.isHiddenAttrCaption())
                        ? StringUtilities.EMPTY
                        : attr.getTitle() + ":";
                Property<Object> property = propertyCreator.create(title, widget, isWide);

                if (attributeToolPanels.containsKey(attr.getCode()))
                {
                    property.setToolPanelWidget(attributeToolPanels.get(attr.getCode()).getDisplay());
                }

                String description = attr.getDescription();
                if (description != null)
                {
                    if (getContent().isShowAttrDescription())
                    {
                        property.setDescription(description);
                    }
                    else
                    {
                        if (!StringUtilities.isEmpty(description) && property.getCaptionWidget() instanceof HTML)
                        {
                            HTML htmlCaptionWidget = (HTML)property.getCaptionWidget();
                            if (PropertyUtils.isShowAttributeDescriptionIcon())
                            {
                                PropertyUtils.addDescriptionIconWithHint(description, htmlCaptionWidget,
                                        attr.isHiddenAttrCaption());
                            }
                            else
                            {
                                addMouseHandlers(htmlCaptionWidget, description);
                            }
                        }
                    }
                }

                Object value = widget instanceof AbstractSelectCellList
                        ? ValueToSelectItemConverter.convert(attrValue)
                        : attrValue;
                property.setValue(value);

                propertyRegistration.setProperty(property);
            }
        });
        return propertyRegistration;
    }

    private void addMouseHandlers(final HTML htmlCaptionWidget, final String description)
    {
        htmlCaptionWidget.addMouseOverHandler(new MouseOverAttrTitleHandler(htmlCaptionWidget, description));
        htmlCaptionWidget.addMouseOutHandler(new MouseOutAttrTitleHandler(htmlCaptionWidget));
    }

    private void toolPanelPresenterRefreshDisplay()
    {
        toolPanelPresenter.getDisplay().asWidget().setVisible(!toolPanelPresenter.getContent().getTools().isEmpty());
        toolPanelPresenter.refreshDisplay();
    }

    protected boolean isItAddFormContentPresenter(Context context)
    {
        return getAddActionContext(context) != null;
    }

    protected AddActionContext getAddActionContext(Context context)
    {
        if (getDecoratedContext(context) instanceof AddActionContext)
        {
            return (AddActionContext)getDecoratedContext(context);
        }
        if (context instanceof ChildDynaContext)
        {
            Context parentCtx = getDecoratedContext(((ChildDynaContext)context).getParentContext());
            if (parentCtx instanceof AddActionContext)
            {
                return (AddActionContext)parentCtx;
            }
        }
        return null;
    }
}
