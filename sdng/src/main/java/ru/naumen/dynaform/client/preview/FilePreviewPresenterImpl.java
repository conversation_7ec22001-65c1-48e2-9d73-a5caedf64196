package ru.naumen.dynaform.client.preview;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyDownEvent;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceChangeEvent;
import com.google.gwt.user.client.Window;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.activity.ParameterizedPlaceController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.preview.FilePreviewPresenter;
import ru.naumen.core.client.preview.InitializationContext;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvasFactory;
import ru.naumen.dynaform.client.preview.events.ClosePreviewEvent;
import ru.naumen.dynaform.client.preview.events.ClosePreviewEventHandler;
import ru.naumen.dynaform.client.preview.events.FileChangedEvent;
import ru.naumen.dynaform.client.preview.events.FileChangedEventHandler;
import ru.naumen.dynaform.client.preview.events.ScaleEvent;
import ru.naumen.dynaform.client.preview.events.ScaleEventHandler;
import ru.naumen.dynaform.client.preview.events.ShowInfoEvent;
import ru.naumen.dynaform.client.preview.events.ShowInfoEventHandler;
import ru.naumen.dynaform.client.preview.infopanel.FileInfoPanelPresenter;
import ru.naumen.dynaform.client.preview.toolbar.Button;
import ru.naumen.dynaform.client.preview.toolbar.PreviewToolbarPresenter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.PreventOnClickAllTransitionEvent;

/**
 *
 * <AUTHOR>
 * @since 27 авг. 2015 г.
 */
public class FilePreviewPresenterImpl<T extends InitializationContext> extends BasicPresenter<FilePreviewDisplay>
        implements ClosePreviewEventHandler, ScaleEventHandler, ShowInfoEventHandler, FileChangedEventHandler,
        FilePreviewPresenter<T>
{
    @Inject
    private PreviewContextFactory<T> contextFactory;
    @Inject
    private ParameterizedPlaceController placeController;
    @Inject
    private FilePreviewMessages messages;
    @Inject
    private FilePreviewResources resources;
    @Inject
    private PreviewCanvasFactory canvasFactory;

    @Inject
    private PreviewToolbarPresenter<T> toolbarPresenter;
    @Inject
    private FileInfoPanelPresenter<T> infoPanelPresenter;

    private PreviewContext context;
    private final T initializationContext;
    private PreviewCanvas canvas;

    @Inject
    public FilePreviewPresenterImpl(@Assisted T initializationContext, FilePreviewDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        this.initializationContext = initializationContext;
        hookWindowClosing();
    }

    /**
     * Слушатели закрытия окна браузера и перезагрузки страницы, для возможности сброса счетчиков активных preview на
     * стороне сервера
     */
    private void hookWindowClosing()
    {
        Window.addCloseHandler(new CloseHandler<Window>()
        {
            @Override
            public void onClose(CloseEvent<Window> event)
            {
                clearContextFilesForPreview();
            }
        });

        Window.addWindowClosingHandler(new Window.ClosingHandler()
        {
            public void onWindowClosing(Window.ClosingEvent closingEvent)
            {
                clearContextFilesForPreview();
            }
        });
    }

    @Override
    public void onClose()
    {
        unbind();
    }

    @Override
    public void onFileChanged(FileChangedEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void onScale(ScaleEvent event)
    {
        getDisplay().maximize(event.isMaximized());
        canvas.refresh();
    }

    @Override
    public void onShowInfo(ShowInfoEvent event)
    {
        getDisplay().showInfo(event.isShowing());
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (canvas != null)
        {
            canvas.destroy();
        }
        getDisplay().setCanvasDisplay(canvas = canvasFactory.create(context));
    }

    @Override
    protected void onBind()
    {
        contextFactory.create(initializationContext, new BasicCallback<PreviewContext>()
        {
            @Override
            public void handleSuccess(PreviewContext context)
            {
                if (CollectionUtils.isEmpty(context.getFiles()))
                {
                    return;
                }

                FilePreviewPresenterImpl.this.context = context;

                getDisplay().setContext(context);

                toolbarPresenter.init(context);
                getDisplay().setToolbarDisplay(toolbarPresenter.getDisplay());
                toolbarPresenter.bind();

                infoPanelPresenter.init(context);
                getDisplay().setInfoDisplay(infoPanelPresenter.getDisplay());
                infoPanelPresenter.bind();

                bindSwitchButtons();

                ensureDebugIds();

                refreshDisplay();
                getDisplay().show();

                setUpHandlers();
            }
        });
    }

    @Override
    protected void onUnbind()
    {
        if (null != canvas)
        {
            canvas.destroy();
        }
        clearContextFilesForPreview();
        eventBus.fireEvent(new PreventOnClickAllTransitionEvent(false));
    }

    /**
     * Очистка подготовленного списка файлов для показа в preview, с набором всех необходимых параметров
     */
    private void clearContextFilesForPreview()
    {
        /*
         так как данный метод будет запускаться при уничтожении каждого виджета, созданного фабрикой, в котором
         запущен preview файла, и в event'е сопровождающем закрытие - нет привязки к fileUUID
         очищаем список файлов в context. Повторное открытие
         preview сопровождается повторным перезапросом актуального состояния файлов в PreviewContextFactory
         */
        if (context != null && !context.getFiles().isEmpty())
        {
            context.getFiles().clear();
        }
    }

    private void bindSwitchButtons()
    {
        if (context.getFiles().size() < 2)
        {
            return;
        }
        resources.style().ensureInjected();
        Button prevButton = new Button(messages.previousFile(), resources.style().prevButton());
        prevButton.replaceStyle(resources.style().button(), resources.style().switchButton());
        getDisplay().setPrevButton(prevButton);
        prevButton.addClickHandler(new ClickHandler()
        {

            @Override
            public void onClick(ClickEvent event)
            {
                context.goToPrevFile();
            }
        });

        Button nextButton = new Button(messages.nextFile(), resources.style().nextButton());
        nextButton.replaceStyle(resources.style().button(), resources.style().switchButton());
        getDisplay().setNextButton(nextButton);
        nextButton.addClickHandler(new ClickHandler()
        {

            @Override
            public void onClick(ClickEvent event)
            {
                context.goToNextFile();
            }
        });

        DebugIdBuilder.ensureDebugId(nextButton, "nextButton");
        DebugIdBuilder.ensureDebugId(prevButton, "prevButton");

    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(getDisplay(), "filePreview");
        DebugIdBuilder.ensureDebugId(toolbarPresenter.getDisplay(), "toolbar");
        DebugIdBuilder.ensureDebugId(infoPanelPresenter.getDisplay(), "infoPanel");
    }

    private void setUpHandlers()
    {
        context.getEventBus().addHandler(ClosePreviewEvent.getType(), this);
        context.getEventBus().addHandler(ScaleEvent.getType(), this);
        context.getEventBus().addHandler(ShowInfoEvent.getType(), this);
        context.getEventBus().addHandler(FileChangedEvent.getType(), this);

        registerHandler(placeController.addPlaceChangeHandler(new PlaceChangeEvent.Handler()
        {
            @Override
            public void onPlaceChange(PlaceChangeEvent event)
            {
                context.getEventBus().fireEvent(new ClosePreviewEvent());
            }
        }));

        registerHandler(getDisplay().addKeyDownHandler(new KeyDownHandler()
        {

            @Override
            public void onKeyDown(KeyDownEvent event)
            {
                switch (event.getNativeKeyCode())
                {
                    case KeyCodes.KEY_ESCAPE:
                        context.getEventBus().fireEvent(new ClosePreviewEvent());
                        break;
                    case KeyCodes.KEY_RIGHT:
                        event.preventDefault();
                        context.goToNextFile();
                        break;
                    case KeyCodes.KEY_LEFT:
                        event.preventDefault();
                        context.goToPrevFile();
                        break;
                    case KeyCodes.KEY_UP:
                    case KeyCodes.KEY_DOWN:
                        event.preventDefault();
                        event.stopPropagation();
                        break;
                    case KeyCodes.KEY_F11:
                        event.preventDefault();
                        context.setInfoIsShowing(!context.isInfoShowing());
                        break;
                    default:
                        break;
                }
            }
        }));
    }
}
