package ru.naumen.dynaform.client.preview;

import java.util.logging.Logger;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FocusPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Panel;

import ru.naumen.dynaform.client.preview.canvas.InfoPanelAnimation;
import ru.naumen.dynaform.client.preview.canvas.scroll.IScrollbarController;
import ru.naumen.dynaform.client.preview.canvas.scroll.ScrollbarControllerFactory;
import ru.naumen.dynaform.client.preview.events.CanvasIsReadyEvent;

/**
 * Виджет предварительного просмотра файлов. Предоставляет места для размещения
 * основных элементов окна предварительного просмотра
 *
 * <AUTHOR>
 * @since 10 сент. 2015 г.
 */
public class PreviewWidget extends Composite
{
    interface PreviewWidgetUiBinder extends UiBinder<FocusPanel, PreviewWidget>
    {
    }

    private static PreviewWidgetUiBinder uiBinder = GWT.create(PreviewWidgetUiBinder.class);

    private static final Logger LOG = Logger.getLogger("PreviewWidget");

    @UiField
    protected HTMLPanel panel;
    @UiField
    protected Panel toolbarHolder;
    @UiField
    protected Panel infoPanelHolder;
    @UiField
    protected Panel canvasHolder;
    @UiField
    protected Panel contentHolder;
    @UiField
    protected Panel prevButtonHolder;
    @UiField
    protected Panel nextButtonHolder;
    @UiField
    protected FocusPanel focusPanel;
    @UiField
    protected Image loader;

    private final FilePreviewResources resourses;
    private final IScrollbarController scrollController;
    private final HandlerRegistration windowResizeHR;

    @Inject
    public PreviewWidget(ScrollbarControllerFactory factory)
    {
        resourses = GWT.create(FilePreviewResources.class);
        resourses.style().ensureInjected();
        initWidget(uiBinder.createAndBindUi(this));

        scrollController = factory.create(canvasHolder.getElement().getParentElement());

        windowResizeHR = Window.addResizeHandler(event -> scrollController.update());
    }

    public void addHandlers(EventBus eventBus)
    {
        eventBus.addHandler(CanvasIsReadyEvent.getType(), () ->
        {
            scrollController.update();
            scrollController.scrollTo(0, 0);
            loader.setVisible(false);
        });
    }

    /**
     * Добавить обработчик нажатия клавиш
     * @param handler
     * @return
     */
    public HandlerRegistration addKeyDownHandler(KeyDownHandler handler)
    {
        return focusPanel.addKeyDownHandler(handler);
    }

    /**
     * Уничтожить виджет
     */
    public void destroy()
    {
        windowResizeHR.removeHandler();
    }

    /**
     * Метод переключает виджет между режимами полноразмерного отображения файла и
     * вписывания в свободное пространство холста
     *
     * @param maximize true если нужно отображать файл в полном размере
     */
    public void maximize(boolean maximize)
    {
        if (maximize)
        {
            contentHolder.addStyleName(resourses.style().fullSize());
            scrollController.update();
            scrollCanvasToMiddle();
        }
        else
        {
            scrollController.destroy();
            clearCanvasPosition();
            contentHolder.removeStyleName(resourses.style().fullSize());
        }
    }

    /**
     * Установить холст
     *
     * @param widget виджет холста
     */
    public void setCanvas(IsWidget widget)
    {
        loader.setVisible(true);
        canvasHolder.clear();
        canvasHolder.add(widget);
    }

    /**
     * Устанавливает фокус на панель на которой происходит обработка 
     * нажатия клавиш
     */
    public void setFocus()
    {
        focusPanel.setFocus(true);
    }

    /**
     * Установить информационную панель
     *
     * @param widget виджет информационной панели
     */
    public void setInfoPanel(IsWidget widget)
    {
        infoPanelHolder.add(widget);
    }

    /**
     * Установить кнопку "Следующий файл"
     * @param button
     */
    public void setNextButton(IsWidget button)
    {
        nextButtonHolder.add(button);
    }

    /**
     * Установить кнопку "Предыдущий файл"
     * @param button
     */
    public void setPrevButton(IsWidget button)
    {
        prevButtonHolder.add(button);
    }

    /**
     * Установить панель инструментов
     * @param widget
     */
    public void setToolbar(IsWidget widget)
    {
        toolbarHolder.add(widget);
    }

    /**
     * Показывает/скрывает информационную панель
     * @param show true-показать панель
     */
    public void showInfo(boolean show)
    {
        if (show)
        {
            contentHolder.addStyleName(resourses.style().showInfo());
        }
        else
        {
            contentHolder.removeStyleName(resourses.style().showInfo());
        }
        new InfoPanelAnimation(scrollController).run(500);
    }

    private void clearCanvasPosition()
    {
        Element child = (Element)canvasHolder.getElement().getChild(0);
        child.getStyle().clearPosition();
    }

    private void scrollCanvasToMiddle()
    {
        Scheduler.get().scheduleFixedDelay(() ->
        {
            Element child = (Element)canvasHolder.getElement().getChild(0);
            int width = canvasHolder.getElement().getParentElement().getOffsetWidth();
            int height = canvasHolder.getElement().getParentElement().getOffsetHeight();

            int canvasWidth = child.getOffsetWidth();
            int canvasHeight = child.getOffsetHeight();

            if (canvasWidth == 0)
            {
                //Содержимое не успело отрисоваться
                LOG.info("Too soon...");
                return true;
            }

            scrollController.update();

            int left = (canvasWidth - width) / 2;
            int top = (canvasHeight - height) / 2;

            LOG.info("Scrolling to... left=" + left + " top=" + top);

            scrollController.scrollTo(left, top);
            return false;
        }, 10);
    }
}
