package ru.naumen.dynaform.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.OPERATOR_INTERFACE;
import static ru.naumen.core.shared.permission.PermissionType.ALL;

import java.util.Arrays;
import java.util.Collection;
import java.util.function.Predicate;

import com.google.gwt.activity.shared.ActivityManager;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.place.shared.PlaceHistoryHandler;
import com.google.gwt.user.client.ui.InsertPanel;
import com.google.inject.Provider;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.client.ActionToolPredicate;
import ru.naumen.core.client.CoreGinjector;
import ru.naumen.core.client.ModuleInitializer;
import ru.naumen.core.client.activity.ActivityFactory;
import ru.naumen.core.client.activity.ActivityGinjector;
import ru.naumen.core.client.activity.AsyncActivity;
import ru.naumen.core.client.activity.DefaultActivityCheck;
import ru.naumen.core.client.activity.PlaceHistoryMapperRegistry;
import ru.naumen.core.client.activity.SyncActivity;
import ru.naumen.core.client.browsertabs.BrowserTabsLimitService;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.ActionHandlerProvider;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.ObjectListActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ObjectListActionHandlerRegistry.Criteria;
import ru.naumen.core.client.content.toolbar.actions.EventActionHandler;
import ru.naumen.core.client.homepage.HomePagePlace;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.permission.AdminPermissionCheckServiceAsync;
import ru.naumen.core.client.permission.PermissionHelper;
import ru.naumen.core.client.personalsettings.PersonalSettingsPlace;
import ru.naumen.core.client.personalsettings.PersonalSettingsPresenter;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.dispatch.CheckAdminPermissionActionResponse;
import ru.naumen.dynaform.client.activity.add.AddPlace;
import ru.naumen.dynaform.client.activity.add.AddPlaceActivity;
import ru.naumen.dynaform.client.browsertabslimit.TabsLimitExceededPresenter;
import ru.naumen.dynaform.client.content.objectlist.standalone.ObjectListPlace;
import ru.naumen.dynaform.client.content.objectlist.standalone.ObjectListPlaceActivity;
import ru.naumen.dynaform.client.content.standalone.ContentPlace;
import ru.naumen.dynaform.client.content.standalone.ContentPlaceActivity;
import ru.naumen.dynaform.client.favorites.FavoritesPlace;
import ru.naumen.dynaform.client.homepage.OperatorHomePageActivity;
import ru.naumen.dynaform.client.toolbar.DynaformActionHandler;
import ru.naumen.dynaform.client.toolbar.NeverActionHandler;
import ru.naumen.dynaform.client.toolbar.multi.add.bo.def.ToolBarActionAddDefaultBO;
import ru.naumen.dynaform.client.toolbar.multi.add.bo.sc.ToolBarActionAddSc;
import ru.naumen.dynaform.client.toolbar.multi.add.system.comment.ToolBarActionAddComment;
import ru.naumen.dynaform.client.toolbar.multi.add.system.file.ToolBarActionAddFile;
import ru.naumen.dynaform.client.toolbar.multi.deletelink.ToolBarActionDeleteLink;
import ru.naumen.dynaform.client.toolbar.multi.downloadfile.ToolBarActionDownloadFile;
import ru.naumen.dynaform.client.toolbar.multi.editresponsible.ToolBarActionEditResponsible;
import ru.naumen.dynaform.client.toolbar.multi.editstate.ToolBarActionEditState;
import ru.naumen.dynaform.client.toolbar.multi.event.FireEventToolbarAction;
import ru.naumen.dynaform.client.toolbar.multi.linkcheck.delete.ToolBarActionDelete;
import ru.naumen.dynaform.client.toolbar.multi.linkcheck.remove.ToolBarActionRemove;
import ru.naumen.dynaform.client.toolbar.multi.massedit.ToolBarActionMassEdit;
import ru.naumen.dynaform.client.toolbar.multi.relationedit.AddDeleteObjsFormAction;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.move.ToolBarActionMove;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.restore.ToolBarActionRestore;
import ru.naumen.dynaform.client.toolbar.single.addlink.ToolBarActionAddLink;
import ru.naumen.dynaform.client.toolbar.single.changeassoc.ToolBarActionChangeAssociation;
import ru.naumen.dynaform.client.toolbar.single.changecase.ToolBarActionChangeCase;
import ru.naumen.dynaform.client.toolbar.single.changepass.ToolBarActionChangePassword;
import ru.naumen.dynaform.client.toolbar.single.commentfiles.ToolBarActionShowAllCommentFiles;
import ru.naumen.dynaform.client.toolbar.single.copy.ToolBarActionCopy;
import ru.naumen.dynaform.client.toolbar.single.copylinktolist.ToolBarActionCopyLinkToList;
import ru.naumen.dynaform.client.toolbar.single.download.DownloadSingleFileActionHandler;
import ru.naumen.dynaform.client.toolbar.single.edit.editcomment.ToolBarActionEditComment;
import ru.naumen.dynaform.client.toolbar.single.edit.editproperties.ToolBarActionEditProperties;
import ru.naumen.dynaform.client.toolbar.single.edit.editproperties.advlist.ToolBarActionEditPropertiesAdvlist;
import ru.naumen.dynaform.client.toolbar.single.edit.editproperty.EditPropertyAction;
import ru.naumen.dynaform.client.toolbar.single.editobject.ToolBarActionEditObject;
import ru.naumen.dynaform.client.toolbar.single.openmass.ToolBarActionOpenMassSCFormForMassCall;
import ru.naumen.dynaform.client.toolbar.single.openmass.ToolBarActionOpenMassSCFormForRegularCall;
import ru.naumen.dynaform.client.toolbar.single.refresh.ToolBarActionRefresh;
import ru.naumen.dynaform.client.toolbar.single.showmore.ToolBarActionShowMoreCommentAttrs;
import ru.naumen.dynaform.client.toolbar.single.showremoved.ToolBarActionShowRemoved;
import ru.naumen.fts.client.SearchHistoryMapper;
import ru.naumen.fts.client.extended.result.ExtendedSearchResultsActivity;
import ru.naumen.fts.client.extended.result.ExtendedSearchResultsPlace;
import ru.naumen.fts.client.simple.SimpleSearchResultsActivity;
import ru.naumen.fts.client.simple.SimpleSearchResultsPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.SearchList;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.export.ExportListEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.AdvListFilterApplyAndHideEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.AdvListFilterApplyEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.AdvListFilterCancelEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.SwitchVisibleListFilterEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.search.SearchParamsEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.ResetGlobalDefaultSettingsEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.SaveAdvlistSettingsEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.sort.SwitchVisibleListSortEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.AdvListFilterResetEvent;
import ru.naumen.reports.client.common.ReportTemplatePlace;
import ru.naumen.reports.client.dynaform.ReportsPlaceHistoryMapper;
import ru.naumen.reports.client.dynaform.report.instance.ReportInstancePlace;
import ru.naumen.reports.client.dynaform.report.instance.ReportInstancePlaceActivity;
import ru.naumen.reports.client.dynaform.template.ReportTemplatePlaceActivity;

/**
 * <AUTHOR>
 * @since 29.10.2010
 */
@Singleton
public class UserInitializer implements ModuleInitializer
{
    private static class NotUserEventToolPredicate extends ActionToolPredicate
    {
        public NotUserEventToolPredicate(Class<?>... classes)
        {
            super(classes);
        }

        @Override
        protected boolean doApply(ActionToolContext input)
        {
            return !(input.getActionTool() instanceof UserEventTool);
        }
    }

    @Inject
    CoreGinjector injector;
    @Inject
    EventBus eventBus;
    @Inject
    PlaceHistoryHandler placeHistoryHandler;
    @Inject
    PlaceController placeController;
    @Inject
    @Named(ActivityGinjector.DEFAULT_PLACE)
    Provider<Place> defaultPlaceProvider;

    @Inject
    CommonMessages messages;
    @Inject
    FormatterService formatterService;

    @Inject
    ActionHandlerRegistry registry;
    @Inject
    ObjectListActionHandlerRegistry listRegistry;
    @Inject
    private BrowserTabsLimitService browserTabsLimitService;

    //ActionHandlers:
    @Inject
    ActionHandlerProvider<NeverActionHandler> neverActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditProperties>> editPropertiesActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<EditPropertyAction>> editPropertyActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditPropertiesAdvlist>> editAdvlistElementFormActionHandler;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditObject>> editActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditComment>> editCommentActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionShowMoreCommentAttrs>> showMoreCommentAttrsActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionMove>> moveActionProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionDelete>> delActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionChangePassword>> userChangePasswordActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionRemove>> removeActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionRestore>> restoreActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionAddDefaultBO>> defaultAddActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionAddSc>> addScActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionAddComment>> addCommentActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionAddFile>> addFileActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionAddLink>> addLinkActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionDeleteLink>> deleteLinkActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionDownloadFile>> downloadActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditState>> editStateActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionMassEdit>> massEditActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionChangeAssociation>> changeAssociationActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionCopy>> copyObjectActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionChangeCase>> changeCaseActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<SwitchVisibleListFilterEvent>> switchVisibleListFilterActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<SwitchVisibleListSortEvent>> switchVisibleListSortActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionRefresh>> refreshActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionShowRemoved>> showRemovedActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<SaveAdvlistSettingsEvent>> saveAdvlistPresentationActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<ResetGlobalDefaultSettingsEvent>> resetGlobalDefaultSettingsActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionEditResponsible>> editResponsibleActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<ExportListEvent>> exportAdvlistActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<SearchParamsEvent>> searchParamsActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<AdvListFilterApplyAndHideEvent>> applyAndHideAdvListFilterActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<AdvListFilterApplyEvent>> applyAdvListFilterActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<AdvListFilterCancelEvent>> cancelAdvListFilterActionHandlerProvider;
    @Inject
    ActionHandlerProvider<EventActionHandler<AdvListFilterResetEvent>> resetAdvListFilterActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<AddDeleteObjsFormAction>> addDeleteObjsActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionOpenMassSCFormForMassCall>> openMassServiceCallFormForMassCallActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionOpenMassSCFormForRegularCall>> openMassServiceCallFormForRegularCallActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<FireEventToolbarAction>> fireEventToolbarActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DownloadSingleFileActionHandler> downloadSingleFileActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionCopyLinkToList>> copyLinkToListActionHandlerProvider;
    @Inject
    ActionHandlerProvider<DynaformActionHandler<ToolBarActionShowAllCommentFiles>> showAllCommentFilesActionHandlerProvider;

    @Inject
    OperatorPresenter operatorPresenter;

    @Inject
    private TabsLimitExceededPresenter tabsLimitExceededPresenter;
    @Inject
    private PermissionHelper permissionHelper;
    @Inject
    private AdminPermissionCheckServiceAsync adminPermissionCheckServiceAsync;

    // @formatter:off
    @Inject
    public UserInitializer(ActivityFactory factory,
            Provider<OperatorHomePageActivity>                                       homePageActivityProvider,
            Provider<UserPlaceActivity>                                              userActivityProvider,
            Provider<AddPlaceActivity>                                               addActivityProvider,
            Provider<EditPlaceActivity>                                              editActivityProvider,
            Provider<SimpleSearchResultsActivity>                                    simpleSearchActivityProvider,
            Provider<ExtendedSearchResultsActivity>                                  extendedSearchActivityProvider,
            Provider<SyncActivity<ObjectCardPresenter, DefaultActivityCheck>>        favoritesActivityProvider,
            Provider<SyncActivity<ObjectCardPresenter, DefaultActivityCheck>>        historyActivityProvider,
            Provider<AsyncActivity<PersonalSettingsPresenter, DefaultActivityCheck>> personalSettingsActivity,
            Provider<ReportTemplatePlaceActivity>                                    reportTemplateActivityProvider,
            Provider<ReportInstancePlaceActivity>                                    reportInstanceActivityProvider,
            Provider<ObjectListPlaceActivity>                                        objectListActivityProvider,
            Provider<ContentPlaceActivity>                                           contentPlaceActivity,
            
            PlaceHistoryMapperRegistry placeHistoryMapperRegistry,
            UserPlaceHistoryMapper userPlaceHistoryMapper,
            SearchHistoryMapper searchHistoryMapper,
            ReportsPlaceHistoryMapper reportsPlaceHistoryMapper)
    {
        factory.register(HomePagePlace.class, homePageActivityProvider);
        factory.register(UserPlace.class, userActivityProvider);
        factory.register(AddPlace.class,  addActivityProvider);
        factory.register(EditPlace.class, editActivityProvider);
        factory.register(SimpleSearchResultsPlace.class, simpleSearchActivityProvider);
        factory.register(ExtendedSearchResultsPlace.class, extendedSearchActivityProvider);
        factory.register(FavoritesPlace.class, favoritesActivityProvider);
        factory.register(HistoryPlace.class, historyActivityProvider);
        factory.register(PersonalSettingsPlace.class, personalSettingsActivity);
        factory.register(ReportTemplatePlace.class, reportTemplateActivityProvider);
        factory.register(ReportInstancePlace.class, reportInstanceActivityProvider);
        factory.register(ObjectListPlace.class, objectListActivityProvider);
        factory.register(ContentPlace.class, contentPlaceActivity);
         
        placeHistoryMapperRegistry.register(userPlaceHistoryMapper);
        placeHistoryMapperRegistry.register(searchHistoryMapper);
        placeHistoryMapperRegistry.register(reportsPlaceHistoryMapper);
    }
    // @formatter:on

    @Override
    public void go(InsertPanel.ForIsWidget slot)
    {
        boolean tabsLimitExceeded = browserTabsLimitService.isTabsLimitExceeded();

        slot.add(tabsLimitExceeded
                ? tabsLimitExceededPresenter.getDisplay()
                : operatorPresenter.getDisplay());

        adminPermissionCheckServiceAsync.hasPermission(OPERATOR_INTERFACE, ALL,
                new BasicCallback<CheckAdminPermissionActionResponse>()
                {
                    @Override
                    protected void handleSuccess(CheckAdminPermissionActionResponse response)
                    {
                        if (response.hasPermission())
                        {
                            initializeOperatorModule(tabsLimitExceeded);
                        }
                        else
                        {
                            permissionHelper.showAccessMarkerDeniedError();
                        }
                    }
                });
    }

    private void initializeOperatorModule(boolean isTabsLimitExceeded)
    {
        if (isTabsLimitExceeded)
        {
            tabsLimitExceededPresenter.bind();
        }
        else
        {
            operatorPresenter.bind();
            placeHistoryHandler.register(placeController, eventBus, defaultPlaceProvider.get());

            // Start ActivityManager for the main widget with our ActivityMapper
            ActivityManager activityManager = injector.getActivityManager();
            activityManager.setDisplay(operatorPresenter.getDisplay());

            // Start PlaceHistoryHandler with our PlaceHistoryMapper
            PlaceHistoryHandler historyHandler = injector.getPlaceHistoryHandler();

            // Goes to the place represented on URL else default place
            historyHandler.handleCurrentHistory();
        }
    }

    @Override
    public void init()
    {
        injector.toolInfoInitializer().init();
        initToolBar();
        initListActions();
        initPropertyDialogBox();
        try
        {
            formatterService.init();
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public void initListActions()
    {
        listRegistry.register(messages.edit(), Constants.EDIT_PROPERTIES,
                (obj, ctx) -> !obj.getProperty(AbstractBO.REMOVED, false) && File.FQN.equals(obj.getMetainfo()));
        listRegistry.register(messages.edit(), Constants.EDIT,
                (obj, ctx) -> !obj.getProperty(AbstractBO.REMOVED, false) && !Event.FQN.equals(obj.getMetainfo())
                              && !File.FQN.equals(obj.getMetainfo()));
        listRegistry.register(messages.delete(), Constants.DELETE, (obj, ctx) -> !Event.FQN.equals(obj.getMetainfo()));
        listRegistry.register(messages.delete(), Constants.DELETE_ACTION_TOOL,
                (obj, ctx) -> !Event.FQN.equals(obj.getMetainfo()));
        listRegistry.register(messages.addFile(), Constants.DOWNLOAD_FILE,
                (obj, ctx) -> File.FQN.equals(obj.getMetainfo()));
        listRegistry.register(messages.addComment(), Constants.ADD_COMMENT, new Criteria()
        {
            final Collection<ClassFqn> disallowed = Arrays.asList(Event.FQN, Comment.FQN, File.FQN);

            @Override
            public boolean apply(DtObject obj, Context ctx)
            {
                return !disallowed.contains(obj.getMetainfo());
            }
        });
        listRegistry.register(messages.massEdit(), Constants.MASS_EDIT,
                (obj, ctx) -> !obj.getProperty(AbstractBO.REMOVED, false) && !Event.FQN.equals(obj.getMetainfo())
                              && !File.FQN.equals(obj.getMetainfo()));
    }

    /**
     * Метод реализует workaround для Issue432 (При первом открытии диалогового окна,
     * оно появляется не по центру, а в верхнем правом углу (пример: добавление атрибута))
     */
    private static void initPropertyDialogBox()
    {
        new DefaultPropertyFormDisplayImpl().hide();
    }

    private void initToolBar()
    {
        registry.register(Constants.ADD_SC, addScActionHandlerProvider);
        registry.register(Constants.ADD, defaultAddActionHandlerProvider);
        registry.register(Constants.ADD, addCommentActionHandlerProvider, 100,
                new ActionToolPredicate(CommentList.class));
        registry.register(Constants.LINK, addLinkActionHandlerProvider);
        registry.register(Constants.ADD_FILE, addFileActionHandlerProvider);
        registry.register(Constants.ADD_COMMENT, addCommentActionHandlerProvider);
        registry.register(Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_MASS_CALL,
                openMassServiceCallFormForMassCallActionHandlerProvider);
        registry.register(Constants.OPEN_MASS_SERVICE_CALL_FORM_FOR_REGULAR_CALL,
                openMassServiceCallFormForRegularCallActionHandlerProvider);
        registry.register(Constants.ADD_DELETE_OBJS, addDeleteObjsActionHandlerProvider);
        registry.register(Constants.EDIT, editActionHandlerProvider);
        registry.register(Constants.EDIT, editAdvlistElementFormActionHandler, 100,
                new NotUserEventToolPredicate(FileList.class));
        registry.register(Constants.EDIT, editCommentActionHandlerProvider, 100,
                new NotUserEventToolPredicate(CommentList.class));
        registry.register(Constants.EDIT_COMMENT, editCommentActionHandlerProvider);
        registry.register(Constants.SHOW_MORE_COMMENT_ATTRS, showMoreCommentAttrsActionHandlerProvider, 100,
                new ActionToolPredicate(CommentList.class));
        registry.register(Constants.SHOW_ALL_COMMENT_FILES, showAllCommentFilesActionHandlerProvider, 100,
                new ActionToolPredicate(CommentList.class));
        registry.register(Constants.MOVE_OBJECT, moveActionProvider);
        registry.register(Constants.EDIT_PROPERTIES, editPropertiesActionHandlerProvider);
        registry.register(Constants.EDIT_PROPERTY, editPropertyActionHandlerProvider);
        registry.register(Constants.DELETE, delActionHandlerProvider);
        registry.register(Constants.DELETE_LINK, deleteLinkActionHandlerProvider);
        registry.register(Constants.DOWNLOAD_FILE, downloadActionHandlerProvider);
        registry.register(Constants.DELETE_ACTION_TOOL, delActionHandlerProvider, 0,
                new ActionToolPredicate(RelObjectList.class));
        registry.register(Constants.CHANGE_PASSWORD, userChangePasswordActionHandlerProvider);
        Predicate<ActionToolContext> hasWorkflow = input ->
        {
            MetaClass metaClass = input.getParentContext().getMetainfo();
            return null != metaClass && metaClass.isHasWorkflow();
        };
        registry.register(Constants.CHANGE_STATE, editStateActionHandlerProvider, 100, hasWorkflow);
        registry.register(Constants.CHANGE_STATE, neverActionHandlerProvider);
        registry.register(Constants.TRANSITION_CHANGE_STATE, editStateActionHandlerProvider, 100, hasWorkflow);
        registry.register(Constants.TRANSITION_CHANGE_STATE, neverActionHandlerProvider);
        registry.register(Constants.CHANGE_CASE, changeCaseActionHandlerProvider);
        registry.register(Constants.CHANGE_ASSOCIATION, changeAssociationActionHandlerProvider);
        registry.register(Constants.COPY_OBJECT, copyObjectActionHandlerProvider, 100,
                input -> !ru.naumen.core.shared.Constants.ServiceCall.CLASS_ID
                        .equals(input.getParentContext().getMetainfo().getFqn().getId()));
        registry.register(Constants.REMOVE_OBJECT, removeActionHandlerProvider);
        registry.register(Constants.RESTORE_OBJECT, restoreActionHandlerProvider);
        registry.register(Constants.SHOW_ADVLIST_FILTER, switchVisibleListFilterActionHandlerProvider);
        registry.register(Constants.SHOW_ADVLIST_SORT, switchVisibleListSortActionHandlerProvider);
        registry.register(Constants.EXPORT_ADVLIST, exportAdvlistActionHandlerProvider);
        registry.register(Constants.SEARCH_PARAMS, searchParamsActionHandlerProvider, 100,
                new ActionToolPredicate(SearchList.class)
                {
                    @Override
                    protected boolean doApply(ActionToolContext input)
                    {
                        return !((SearchList)input.getParentContent()).isFull();
                    }
                });
        registry.register(Constants.REFRESH, refreshActionHandlerProvider);
        registry.register(Constants.SHOW_REMOVED, showRemovedActionHandlerProvider);
        registry.register(Constants.SAVE_ADVLIST_PRS, saveAdvlistPresentationActionHandlerProvider);
        registry.register(Constants.RESET_GLOBAL_DEFAULT_SETTINGS, resetGlobalDefaultSettingsActionHandlerProvider);
        registry.register(Constants.EDIT_RESPONSIBLE, editResponsibleActionHandlerProvider, 100,
                input -> input.getParentContext().getMetainfo().isHasResponsible());
        registry.register(Constants.EDIT_RESPONSIBLE, neverActionHandlerProvider);
        registry.register(Constants.APPLY_AND_HIDE_ADVLIST_FILTER, applyAndHideAdvListFilterActionHandlerProvider);
        registry.register(Constants.APPLY_ADVLIST_FILTER, applyAdvListFilterActionHandlerProvider);
        registry.register(Constants.CANCEL_ADVLIST_FILTER, cancelAdvListFilterActionHandlerProvider);
        registry.register(Constants.RESET_ADVLIST_FILTER, resetAdvListFilterActionHandlerProvider);
        registry.register(Constants.FIRE_USER_EVENT, fireEventToolbarActionHandlerProvider);
        registry.register(Constants.DOWNLOAD_SINGLE_FILE, downloadSingleFileActionHandlerProvider);
        registry.register(Constants.MASS_EDIT, massEditActionHandlerProvider);
        registry.register(Constants.COPY_LINK_TO_LIST, copyLinkToListActionHandlerProvider);
    }
}