package ru.naumen.dynaform.client.content;

import java.util.Objects;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextChangedEvent;
import ru.naumen.core.client.content.property.PropertyGridDisplay;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaContextDecorator;
import ru.naumen.dynaform.client.activity.add.AddPlace;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.RefreshContentsOnAddFormEvent;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.SelectClient;

/**
 * Презентер контента {@link SelectClient}
 *
 * @see <a href="https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530">...</a>
 * @see <a href="https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529">...</a>
 * <AUTHOR>
 */
public class SelectClientContentPresenter
        extends AbstractValidationContentPresenter<PropertyGridDisplay, SelectClient>
{
    public static boolean isVisible(PlaceController placeController, SecurityHelper securityHelper, Context context)
    {
        Place place = placeController.getWhere();

        // нелицензированного пользователя - контрагентом считается данный нелицензированный пользователь
        // контент не показывается
        // контент доступен только при добавлении через кнопку добавить ("быструю")
        return securityHelper.getCurrentUser().isLicensed()
               && place instanceof AddPlace //NOSONAR
               && ((AddPlace)place).isFastCreate()
               && securityHelper.isCurrentUserLicensed(context.getMetainfo().getFqn());
    }

    private PropertyRegistration<DtObject> propertyReg;
    @Inject
    private EditAttrPropertyCreator editAttrPropertyCreator;
    @Inject
    private MetainfoServiceSync metainfoServiceSync;
    @Inject
    private PlaceController placeController;
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    private NotNullValidator<DtObject> notNullValidator;
    private ValidationUnit<DtObject> notNullValidatorReg;

    @Inject
    public SelectClientContentPresenter(PropertyGridDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectClient");
    }

    @Override
    public void onRefresh(RefreshContentsOnAddFormEvent e)
    {
        if (null == propertyReg)
        {
            return;
        }
        if (caseNotChanged(e))
        {
            return;
        }
        init(getContent(), e.getContext());
        removeClientProperty();
        editAttrPropertyCreator.unbind();
        editAttrPropertyCreator.bind();
        updateCaption();
        editAttrPropertyCreator.init(getContent(), e.getContext(), validation, getDisplay());

        final Attribute attribute = getContext().getMetainfo().getAttribute(Constants.Association.CLIENT);
        getContext().registerAttribute(attribute);
        addClientProperty(Objects.requireNonNull(attribute));
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        if (!isVisible(placeController, securityHelper, context))
        {
            getDisplay().setVisible(false);
            return;
        }

        editAttrPropertyCreator.init(getContent(), getContext(), validation, getDisplay());

        final Attribute attribute = getContext().getMetainfo().getAttribute(Constants.Association.CLIENT);
        getContext().registerAttribute(attribute);
        addClientProperty(Objects.requireNonNull(attribute));

        MetaClass classMetaClass = metainfoServiceSync.getMetaClass(getContext().getMetainfo().getFqn().fqnOfClass());
        final Attribute classAttribute = classMetaClass.getAttribute(Constants.Association.CLIENT);
        addContextHandler(FieldChangedEvent.getType(), e ->
        {
            if (e.isApplicable(getContext(), null, null, Objects.requireNonNull(classAttribute)))
            {
                onClientChanged();
            }
        });
        registerHandler(context.getEventBus().addHandler(ContextChangedEvent.getType(), this::onContextChanged));
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        Attribute attribute = getContext().getMetainfo().getAttribute(Constants.Association.CLIENT);
        getContext().unregisterAttribute(Objects.requireNonNull(attribute));
        editAttrPropertyCreator.unbind();
    }

    private void onContextChanged(final ContextChangedEvent<Context> e)
    {
        Context eventContext = e.getContext();
        if (DynaContextDecorator.compareDecorated(context, eventContext) && eventContext instanceof DynaContext)
        {
            context.getReadyState().ready(new HasReadyState.ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    updateContext(e);
                }
            });
        }
    }

    private void updateContext(ContextChangedEvent<Context> e)
    {
        DtObject object = ((DynaContext)e.getContext()).getObject();
        PresentationContext prsContext = editAttrPropertyCreator
                .getPresentationContext(Constants.Association.CLIENT);
        if (prsContext != null)
        {
            prsContext.setObject(object);
        }
    }

    private void addClientProperty(final Attribute attribute)
    {
        editAttrPropertyCreator.createProperty(attribute, getContext().getObject(), getContext(), true, null,
                new BasicCallback<Property<Object>>(getContext().getReadyState(), getDisplay())
                {
                    @Override
                    @SuppressWarnings({ "rawtypes", "unchecked" })
                    protected void handleSuccess(Property<Object> value)
                    {
                        Property<DtObject> client = (Property)value;
                        PropertyUtils.setCaptionDescription(value, content, attribute.getDescription());
                        if (Boolean.TRUE.equals(attribute.isHiddenAttrCaption()))
                        {
                            client.setCaption(StringUtilities.EMPTY);
                        }

                        propertyReg = getDisplay().add(client, attribute.getCode());
                        eventBus.fireEvent(new UpdateTabOrderEvent(true));

                        if (attribute.isRequired() || attribute.isRequiredInInterface())
                        {
                            client.setValidationMarker(true);
                            notNullValidatorReg = validation.validate(client, notNullValidator);
                        }
                    }
                });
    }

    private static boolean caseNotChanged(RefreshContentsOnAddFormEvent e)
    {
        return e.getOldMetainfo().getFqn().equals(e.getNewMetainfo().getFqn());
    }

    private void onClientChanged()
    {
        DtObject object = getContext().getObject();
        DtObject client = Objects.requireNonNull(object).getProperty(Constants.Association.CLIENT);
        if (null == client)
        {
            updateClientAggregates(null, null, null);
        }
        else
        {
            ClassFqn clientFqn = client.getMetaClass().fqnOfClass();
            if (Constants.Team.FQN.equals(clientFqn))
            {
                updateClientAggregates(null, client, null);
            }
            else if (Constants.OU.FQN.equals(clientFqn))
            {
                updateClientAggregates(null, null, client);
            }
            else if (client instanceof TreeDtObject) //NOSONAR
            {
                DtObject parent = ((TreeDtObject)client).getParent();
                ClassFqn parentFqn = parent.getMetaClass();
                if (Constants.Team.FQN.equals(parentFqn))
                {
                    updateClientAggregates(client, parent, null);
                }
                else
                {
                    updateClientAggregates(client, null, parent);
                }
            }
            else
            {
                updateClientAggregates(client, null, null);
            }
        }
    }

    private void removeClientProperty()
    {
        getDisplay().unregister(propertyReg);
        propertyReg = null;
        if (notNullValidatorReg != null)
        {
            notNullValidatorReg.unregister();
            notNullValidatorReg = null;
        }
    }

    private void updateClientAggregates(@Nullable IUUIDIdentifiable employee, @Nullable IUUIDIdentifiable team,
            @Nullable IUUIDIdentifiable ou)
    {
        updateProperty(Constants.Association.CLIENT_EMPLOYEE, employee);
        updateProperty(Constants.Association.CLIENT_TEAM, team);
        updateProperty(Constants.Association.CLIENT_OU, ou);
    }

    private void updateProperty(String attributeCode, @Nullable Object value)
    {
        getContext().setAttributeAndNotify(attributeCode, value);
    }
}
