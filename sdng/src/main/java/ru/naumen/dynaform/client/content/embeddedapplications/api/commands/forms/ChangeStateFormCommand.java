package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.forms;

import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.REQUIRED_CONFIRM;
import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.STATUSES;
import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.UUID;
import static ru.naumen.metainfo.shared.elements.ContentPermissionHolderUtils.hasPermission;
import static ru.naumen.metainfo.shared.ui.Constants.CHANGE_STATE;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONObject;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.HandlerRegistrationHolder;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.ActionToolContextFactory;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEvent;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEventHandler;
import ru.naumen.core.client.events.FormCanceledEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.SecConstants.ServiceCall;
import ru.naumen.core.shared.dispatch.InitContentAndObjectResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.content.embeddedapplications.api.JsApiUtils;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.WorkflowLite;

public class ChangeStateFormCommand extends FormCommandBase implements ActionExecutedEventHandler
{
    private final MetainfoServiceSync metainfoService;
    private final CommonMessages messages;
    private final ActionToolContextFactory factory;
    private final ActionHandlerRegistry actionHandlerRegistry;
    private final JsApiUtils utils;

    @Inject
    public ChangeStateFormCommand(
            final HandlerRegistrationHolder handlerRegistrationHolder,
            final MetainfoServiceSync metainfoService,
            final CommonMessages messages,
            final ActionToolContextFactory factory,
            final ActionHandlerRegistry actionHandlerRegistry,
            final JsApiUtils utils)
    {
        super(handlerRegistrationHolder);
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.factory = factory;
        this.actionHandlerRegistry = actionHandlerRegistry;
        this.utils = utils;
    }

    @Override
    public JsApiCommandType getType()
    {
        return JsApiCommandType.CHANGE_STATE_FORM;
    }

    @Override
    public void perform(final JSONObject parameters)
    {
        final String objectUuid = Objects.requireNonNull(parameters.get(UUID).isString()).stringValue();
        // в части приложений используется команд вызов в обход jsApi, для обратной совместимости
        final boolean requiredConfirm = parameters.containsKey(REQUIRED_CONFIRM) &&
                                        Objects.requireNonNull(parameters.get(REQUIRED_CONFIRM).isBoolean())
                                                .booleanValue();

        final JSONArray statusesAsJson = parameters.get(STATUSES).isArray();
        final List<String> statusCandidates = new ArrayList<>(statusesAsJson.size());
        for (int i = 0; i < statusesAsJson.size(); i++)
        {
            statusCandidates.add(statusesAsJson.get(i).isString().stringValue());
        }
        MapProperties params = MapProperties.builder().put(STATUSES, statusCandidates).build();
        utils.getFormContext(context.getParentContent(), CHANGE_STATE, objectUuid, params,
                new BasicCallback<InitContentAndObjectResponse>()
                {
                    @Override
                    public void handleSuccess(InitContentAndObjectResponse response)
                    {
                        final DtObject dto = response.getDto();
                        final ClassFqn fqn = dto.getMetaClass();
                        final MetaClass metaClass = metainfoService.getMetaClass(dto.getMetaClass());

                        final ErrorAndAttentionMessageHandler errorHandler =
                                Objects.requireNonNull(context.getErrorAndAttentionMsgHandler());
                        if (!metaClass.hasAttribute(HasState.STATE))
                        {
                            final MetaClass parentMetaClass = metainfoService.getMetaClass(
                                    metaClass.getFqn().fqnOfClass());
                            // у класса объекта нет жизненного цикла
                            errorHandler.addErrorMessage(
                                    messages.hasNoWorkflow(dto.getTitle(), parentMetaClass.getTitle()));
                            return;
                        }
                        final PermissionHolder permissions = response.getPermissions();
                        if (!hasPermission(permissions, CHANGE_STATE, response.getContent()))
                        {
                            errorHandler.addErrorMessage(
                                    messages.cannotChangeState(dto.getTitle(), metaClass.getTitle()));
                            return;
                        }

                        final WorkflowLite workflow = metainfoService.getMetaClassLite(fqn).getWorkflowLite();
                        final String currentState = dto.getProperty(HasState.STATE);
                        if (statusCandidates.size() == 1 && ObjectUtils.equals(currentState, statusCandidates.get(0)))
                        {
                            sendFormCommandResponse(HasState.STATE, currentState);
                            return;
                        }

                        final List<String> statuses = new ArrayList<>();
                        for (final String statusCandidate : statusCandidates)
                        {
                            if (workflow.getState(statusCandidate) == null)
                            {
                                continue;
                            }

                            if (ObjectUtils.equals(currentState, statusCandidate))
                            {
                                continue;
                            }

                            String permissionKey = ServiceCall.CHANGE_STATE + currentState
                                                   + ServiceCall.CHANGE_STATE_CODE_DELIMITER + statusCandidate;
                            if (hasPermission(permissions, permissionKey, response.getContent()))
                            {
                                statuses.add(statusCandidate);
                            }
                        }

                        if (statuses.isEmpty())
                        {
                            errorHandler.addErrorMessage(
                                    messages.cannotChangeState(dto.getTitle(), metaClass.getTitle()));
                            return;
                        }

                        context.setRequiredConfirm(requiredConfirm);
                        context.setPossibleStates(statuses);
                        context.setMetainfo(metaClass, false);
                        context.setObject(dto, false);
                        context.setPermissions(permissions);

                        final EventBus eventBus = context.getEventBus();
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(ActionExecutedEvent.getType(), ChangeStateFormCommand.this));
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(FormCanceledEvent.getType(), ChangeStateFormCommand.this));

                        final ActionToolContext actionToolContext =
                                factory.create(response.getContent(), context, null, CHANGE_STATE);
                        actionHandlerRegistry.getHandler(CHANGE_STATE, actionToolContext).execute();
                    }
                });
    }

    /**
     * Срабатывает при сохранении формы
     *
     * @param e событие
     */
    @Override
    public void onActionExecutedEvent(final ActionExecutedEvent e)
    {
        handlerRegistrationHolder.removeHandlers();
        sendFormCommandResponse(HasState.STATE, e.getResultValue());
    }
}
