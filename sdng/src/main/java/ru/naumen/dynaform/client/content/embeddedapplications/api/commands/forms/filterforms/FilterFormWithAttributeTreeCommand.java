package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.forms.filterforms;

import static java.util.stream.Collectors.toMap;
import static ru.naumen.core.shared.Constants.AttributeLink.ATTRIBUTE_RESTRICTIONS;
import static ru.naumen.core.shared.Constants.AttributeLink.TOP_LEVEL_RESTRICTIONS;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.condition.DefaultConditionFormProviderFactory;
import ru.naumen.core.client.widgets.properties.condition.FilterFormContext;
import ru.naumen.core.shared.autobean.wrappers.AdvlistSettingsAutoBeanFactory;
import ru.naumen.core.shared.dtotree.AttributeTreeLevel;
import ru.naumen.core.shared.dtotree.LevelRestriction;
import ru.naumen.core.shared.dtotree.attrribute.AttributeRestriction;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterMessages;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Команда вызова формы фильтрации из встроенного приложения.
 * Выводит атрибуты в виде дерева выбора для поддержки цепочек атрибутов.
 *
 * <AUTHOR>
 * @since 28.10.2021
 */
public class FilterFormWithAttributeTreeCommand extends FilterFormCommand
{
    @Inject
    public FilterFormWithAttributeTreeCommand(
            final AdvlistSettingsAutoBeanFactory autoBeanFactory,
            final FilterFormDataServiceAsync listDataService,
            final DefaultConditionFormProviderFactory formProviderFactory,
            final ListFilterMessages listFilterMessages,
            final SplitPointService splitPointService,
            final MetainfoServiceSync metainfoService)
    {
        super(autoBeanFactory, listDataService, formProviderFactory, listFilterMessages, splitPointService,
                metainfoService);
    }

    @Override
    public JsApiCommandType getType()
    {
        return JsApiCommandType.FILTER_FORM_WITH_ATTRIBUTE_TREE;
    }

    @Override
    protected void unwrapDataContext(final JSONObject parameters, final AsyncCallback<ObjectListDataContext> callback)
    {
        super.unwrapDataContext(parameters, new BasicCallback<ObjectListDataContext>()
        {
            @Override
            public void onSuccess(final ObjectListDataContext dataContext)
            {
                prepareFiltrationForTree(dataContext, callback);
            }
        });
    }

    /**
     * Выполняет подготовку фильтров для отображения атрибутов деревом на форме настроек фильтрации
     *
     * @param dataContext контекст с данными списка
     * @param callback обработчик результата, выполняемый после получения подготовленной фильтрации
     */
    private void prepareFiltrationForTree(final ObjectListDataContext dataContext,
            final AsyncCallback<ObjectListDataContext> callback)
    {
        listDataService.prepareFiltrationForTree(dataContext.getListFilter(),
                new BasicCallback<ListFilter>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(ListFilter listFilter)
                    {
                        dataContext.setListFilter(listFilter);
                        callback.onSuccess(dataContext);
                    }
                });
    }

    @Override
    protected void buildFormContext(final ObjectListDataContext dataContext,
            final Map<AttributeFqn, Attribute> attributes, final JSONObject parameters,
            final AsyncCallback<FilterFormContext> callback)
    {
        super.buildFormContext(dataContext, attributes, parameters,
                new BasicCallback<FilterFormContext>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(FilterFormContext formContext)
                    {
                        formContext.setUsedAttributeTree(true);

                        final Map<ClassFqn, Set<AttributeRestriction>> restrictions = getAttributeRestrictions(
                                parameters);
                        formContext.setContextProperty(ATTRIBUTE_RESTRICTIONS, restrictions);
                        formContext.setContextProperty(TOP_LEVEL_RESTRICTIONS, new HashSet<>(attributes.keySet()));

                        callback.onSuccess(formContext);
                    }
                });
    }

    /**
     * Получает ограничения на набор доступных атрибутов из параметров команды и выполняет их конвертацию.
     *
     * @param parameters параметры команды
     * @return ограничения на набор доступных атрибутов
     */
    @Nullable
    private static Map<ClassFqn, Set<AttributeRestriction>> getAttributeRestrictions(final JSONObject parameters)
    {
        if (!parameters.containsKey(ATTRIBUTE_RESTRICTIONS))
        {
            return null;
        }
        final JSONObject restrictionsJson = parameters.get(ATTRIBUTE_RESTRICTIONS).isObject();
        return restrictionsJson.keySet().stream()
                .map(fqn -> new Pair<>(fqn, RestrictionResolver.resolve(restrictionsJson.get(fqn))))
                .filter(pair -> !pair.getRight().isEmpty())
                .collect(toMap(pair -> ClassFqn.parse(pair.getLeft()), Pair::getRight, (value1, value2) -> value1,
                        HashMap::new));
    }

    /**
     * Позволяет преобразовать {@link AttributeRestriction ограничения} из JSON представления в Java-объект
     */
    private static class RestrictionResolver
    {
        /**
         * Преобразует {@link AttributeRestriction ограничение} из JSON представления в java объект
         *
         * @param restriction JSON представление ограничения
         * @return набор ограничений
         */
        public static Set<AttributeRestriction> resolve(JSONValue restriction)
        {
            if (restriction.isObject() != null)
            {
                return createLevelRestriction(restriction.isObject());
            }
            Set<AttributeRestriction> attributeRestrictions = new HashSet<>(1);
            AttributeRestriction attributeRestriction = createRestriction(restriction, null);
            if (attributeRestriction != null)
            {
                attributeRestrictions.add(attributeRestriction);
            }
            return attributeRestrictions;
        }

        /**
         * Получает набор {@link AttributeRestriction ограничений}, заданных для разных уровней дерева
         *
         * @param restrictionsJson json-объект представляющий собой мапу, где ключ - {@link AttributeTreeLevel уровень
         * дерева}, значение - либо массив кодов атрибутов, либо строка (группа атрибутов)
         * @return набор ограничений
         */
        private static Set<AttributeRestriction> createLevelRestriction(JSONObject restrictionsJson)
        {
            Set<AttributeRestriction> attributeRestrictions = new HashSet<>();
            for (final String key : restrictionsJson.keySet())
            {
                AttributeTreeLevel attributeTreeLevel = AttributeTreeLevel.fromString(key);
                if (attributeTreeLevel != null)
                {
                    JSONValue restriction = restrictionsJson.get(key);
                    if (restriction != null)
                    {
                        attributeRestrictions.add(createRestriction(restriction, attributeTreeLevel));
                    }
                }
            }
            return attributeRestrictions;
        }

        /**
         * Создаёт {@link AttributeRestriction ограничение} на основе его представления в JSON
         *
         * @param restriction ограничение представленное в виде JSON
         * @param attributeTreeLevel уровень для которого задано ограничение
         * @return ограничение набора доступных атрибутов
         */
        @Nullable
        private static AttributeRestriction createRestriction(JSONValue restriction,
                @Nullable AttributeTreeLevel attributeTreeLevel)
        {
            String groupAttrCode = getGroupAttrCode(restriction);
            if (groupAttrCode != null)
            {
                return (attributeTreeLevel == null)
                        ? new AttributeRestriction(groupAttrCode)
                        : new LevelRestriction(groupAttrCode, attributeTreeLevel);
            }

            Set<String> attributeCodes = getAttributeCodes(restriction);
            if (attributeCodes != null)
            {
                return attributeTreeLevel == null
                        ? new AttributeRestriction(attributeCodes)
                        : new LevelRestriction(attributeCodes, attributeTreeLevel);
            }
            return null;
        }

        /**
         * Получает коды атрибутов из JSON массива
         *
         * @param attrCodes коды атрибутов в виде JSON
         * @return набор кодов атрибутов
         */
        @Nullable
        private static Set<String> getAttributeCodes(JSONValue attrCodes)
        {
            final JSONArray attributeCodesJson = attrCodes.isArray();
            return attributeCodesJson == null
                    ? null
                    : IntStream.range(0, attributeCodesJson.size())
                            .mapToObj(attributeCodesJson::get)
                            .map(JSONValue::isString)
                            .filter(Objects::nonNull)
                            .map(JSONString::stringValue)
                            .collect(Collectors.toSet());
        }

        /**
         * Получает код группы атрибутов из JSON объекта
         *
         * @param groupAttrCode значение JSON из которого пытаемся получить строку
         * @return код группы атрибутов
         */
        @Nullable
        private static String getGroupAttrCode(JSONValue groupAttrCode)
        {
            JSONString value = groupAttrCode.isString();
            return value == null
                    ? null
                    : value.stringValue();
        }
    }
}
