package ru.naumen.dynaform.client.content;

import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.EventHandler;
import com.google.gwt.event.shared.GwtEvent.Type;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.RequestObjectDefinition;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.dynaform.client.DefaultDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformObjectService;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedHandler;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ClientInfo;

/**
 * <AUTHOR>
 * @since 29.03.2012
 *
 */
public class ClientInfoContentPresenter extends PropertyListContentPresenter<ClientInfo>
{
    private static Logger LOG = Logger.getLogger(ClientInfoContentPresenter.class.getName());
    @Inject
    private MetainfoServiceSync metainfoService;
    @Inject
    private DynaformObjectService objectService;
    @Inject
    private DispatchAsync dispatch;

    private DynaContext sourceContext;

    @Inject
    public ClientInfoContentPresenter(HasPropertiesContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "ClientInfo");
    }

    @Override
    public DefaultDynaContext getContext()
    {
        return (DefaultDynaContext)super.getContext();
    }

    @Override
    public void init(ClientInfo content, DynaContext context)
    {
        this.sourceContext = context;
        super.init(content, createThisContext(sourceContext));
    }

    protected <H extends EventHandler> void addSourceContextHandler(Type<H> type, H handler)
    {
        registerHandler(sourceContext.getEventBus().addHandler(type, handler));
    }

    @Override
    protected List<Attribute> getAttributes()
    {
        DtObject object = sourceContext.getObject();
        if (null == object)
        {
            return Collections.emptyList();
        }
        DtObject client = object.getProperty(Constants.Association.CLIENT);
        if (null == client)
        {
            return Collections.emptyList();
        }
        ClassFqn clientFqn = client.getMetainfo();
        ClassFqn hostFqn = clientFqn.fqnOfClass();
        // @formatter:off
        String attrGroup = OU.FQN.equals(hostFqn)       ? getContent().getOuAttributeGroup()   :
                           Team.FQN.equals(hostFqn)     ? getContent().getTeamAttributeGroup() :
                           Employee.FQN.equals(hostFqn) ? getContent().getEmplAttributeGroup() :
                           null;
        // @formatter:on

        if (null == attrGroup)
        {
            return Collections.emptyList();
        }
        return metainfoService.getMetaClass(clientFqn).getGroupAttributes(attrGroup);
    }

    @Override
    protected Context getGlobalContext()
    {
        return sourceContext;
    }

    @Override
    protected ReadyState getReadyState()
    {
        return sourceContext.getReadyState();
    }

    @Override
    protected boolean needToChangeDefaultCaption()
    {
        return false;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        final Attribute clientAttribute = sourceContext.getMetainfo().getAttribute(Constants.Association.CLIENT);
        addSourceContextHandler(FieldChangedEvent.getType(), new FieldChangedHandler()
        {
            @Override
            public void onFieldChanged(FieldChangedEvent e)
            {
                if (e.isApplicable((FormContext)sourceContext, null, null, clientAttribute))
                {
                    updateThisContext();
                }
            }
        });
    }

    @Override
    protected void refreshProperties()
    {
        getDisplay().clearProperties();
        propertyRegistrations.clear();
        if (null == context)
        {
            return;
        }
        final DtObject dto = getContext().getObject();
        if (null == dto)
        {
            return;
        }

        List<Attribute> attributesForProps = getVisibleAttributes();
        boolean callbackUsed = false;
        for (Attribute attr : attributesForProps)
        {
            if (ru.naumen.core.shared.utils.ObjectUtils.isEmpty((Object)dto.getProperty(attr.getCode())))//NOPMD
            {
                loadFullObjectAndCreateProps(attributesForProps, dto.getUUID());
                callbackUsed = true;
                break;
            }
        }
        if (!callbackUsed)
        {
            for (Attribute attr : attributesForProps)
            {
                checkPermissionAndCreatePropertyReg(attr, dto);
            }
        }
    }

    @Override
    protected boolean isAnyAttributeVisible()
    {
        return !propertyRegistrations.isEmpty();
    }

    protected void updateThisContext()
    {
        DtObject object = sourceContext.getObject();
        DtObject client = null == object ? null : object.<DtObject> getProperty(Constants.Association.CLIENT);
        if (null == client)
        {
            getContext().setObject(null);
            refreshContentVisibility();
            return;
        }
        RequestObjectDefinition requestObjectDefinition = new RequestObjectDefinition(client.getUUID())
                .setContext(sourceContext)
                .setCallback(new ContextualCallback<DtObject>(sourceContext)
                {
                    @Override
                    protected void handleSuccess(DtObject value)
                    {
                        MetaClass clientMetaClass = metainfoService.getMetaClass(value.getMetaClass());
                        DefaultDynaContext ctx = ClientInfoContentPresenter.this.getContext();
                        ctx.setMetainfo(clientMetaClass, false);
                        ctx.setObject(value);
                        refreshProperties();
                    }
                });
        objectService.getObject(requestObjectDefinition);
    }

    private void checkPermissionAndCreatePropertyReg(Attribute attr, DtObject dto)
    {
        if (hasReadPermission(dto, attr) && !propertyRegistrations.containsKey(attr.getCode()))
        {
            createPropertyRegistration(dto, attr);
        }
    }

    private DefaultDynaContext createThisContext(DynaContext sourceContext)
    {
        DtObject object = sourceContext.getObject();
        DtObject client = null == object ? null : object.<DtObject> getProperty(Constants.Association.CLIENT);
        ClassFqn clientClassFqn = client != null ? client.getMetaClass() : Constants.AbstractBO.FQN;
        MetaClass clientMetaClass = metainfoService.getMetaClass(clientClassFqn);
        return new DefaultDynaContext(sourceContext, clientMetaClass, client);
    }

    private void loadFullObjectAndCreateProps(List<Attribute> attributes, String dtoUuid)
    {
        List<String> props = new java.util.ArrayList<>();
        for (Attribute attr : attributes)
        {
            if (attr != null && attr.getCode() != null)
            {
                props.add(attr.getCode());
            }
        }
        dispatch.execute(new GetDtObjectAction(dtoUuid).setProperties(new DtoProperties(null, props)),
                new BasicCallback<GetDtObjectResponse>()
                {
                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        if (LOG.isLoggable(Level.FINE))
                        {
                            LOG.log(Level.FINE, t.getMessage(), t);
                        }
                    }

                    @Override
                    protected void handleSuccess(GetDtObjectResponse result)
                    {
                        DtObject dtoRes = result.getObj();
                        for (Attribute attr : attributes)
                        {
                            checkPermissionAndCreatePropertyReg(attr, dtoRes);
                        }
                        refreshContentVisibility();
                    }
                });
    }
}
