package ru.naumen.dynaform.client.push.log;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;

import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.dynaform.client.push.log.table.NotificationLogCellTable;

/**
 * Лог уведомлений.
 * <AUTHOR>
 * @since Sep 23, 2019
 */
public class NotificationLogDisplayImpl extends Composite implements NotificationLogDisplay
{
    private static final NotificationLogDisplayImplUiBinder uiBinder = GWT.create(
            NotificationLogDisplayImplUiBinder.class);
    private static final int HIDE_TIMEOUT = 500;

    interface NotificationLogDisplayImplUiBinder extends UiBinder<FlowPanel, NotificationLogDisplayImpl>
    {
    }

    private final NotificationLogResources resources;
    private final Timer hideTimer = new Timer()
    {
        @Override
        public void run()
        {
            setVisible(false);
        }
    };

    @UiField(provided = true)
    NotificationLogCellTable dataTable;
    @UiField(provided = true)
    NotificationLogToolPanel toolPanel;
    @UiField
    HTMLPanel messageContainer;
    @UiField
    NotificationLogScrollableWidget scrollablePanel;

    @Inject
    public NotificationLogDisplayImpl(NotificationLogResources resources, NotificationLogCellTable dataTable,
            NotificationLogToolPanel toolPanel)
    {
        this.resources = resources;
        this.dataTable = dataTable;
        this.toolPanel = toolPanel;
        resources.css().ensureInjected();
        initWidget(uiBinder.createAndBindUi(this));
        scrollablePanel.enableControls();
        scrollablePanel.setScrollPadding(2, 2, 2, 2);
        messageContainer.setVisible(false);

        DebugIdBuilder.ensureDebugId(toolPanel, "notificationLogToolPanel");
        DebugIdBuilder.ensureDebugId(dataTable, "notificationLogData");
    }

    @Override
    public void destroy()
    {
    }

    @Override
    public HTMLPanel getMessageContainer()
    {
        return messageContainer;
    }

    @Override
    public NotificationLogCellTable getNotificationTable()
    {
        return dataTable;
    }

    @Override
    public int getScrollPosition()
    {
        return scrollablePanel.getScrollTop();
    }

    @Override
    public NotificationLogToolPanel getToolPanel()
    {
        return toolPanel;
    }

    @Override
    public void hide()
    {
        removeStyleName(resources.css().notificationLogOpened());
        addStyleName(resources.css().notificationLogClosed());
        hideTimer.schedule(HIDE_TIMEOUT);
    }

    @Override
    public void setScrollPosition(int scrollPosition)
    {
        scrollablePanel.setScrollTop(scrollPosition);
    }

    @Override
    public void setSummary(String summary)
    {
        scrollablePanel.setSummaryText(summary);
    }

    @Override
    public void show()
    {
        hideTimer.cancel();
        setVisible(true);
        Scheduler.get().scheduleDeferred(() ->
        {
            removeStyleName(resources.css().notificationLogClosed());
            addStyleName(resources.css().notificationLogOpened());
        });
    }
}
