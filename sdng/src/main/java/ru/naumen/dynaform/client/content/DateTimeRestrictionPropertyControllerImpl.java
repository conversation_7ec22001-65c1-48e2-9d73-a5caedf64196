package ru.naumen.dynaform.client.content;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.inject.Singleton;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;
import ru.naumen.dynaform.client.customforms.CustomFormContext;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.objectlist.client.ListComponentsHolder;
import ru.naumen.objectlist.client.mode.active.extended.advlist.AdvlistEditableCellContext;
import ru.naumen.reports.client.common.ReportContext;
import ru.naumen.reports.client.common.ReportParametersHelper;

/**
 * Контроллер, отвечающий за вычисление ограничений для атрибутов Дата и Дата/Время
 * <AUTHOR>
 * @since 8 окт. 2018 г.
 */
@Singleton
public class DateTimeRestrictionPropertyControllerImpl implements DateTimeRestrictionPropertyController
{
    private final DispatchAsync dispatch;
    private final FormUtils formUtils;
    private final ValidationMessages validationMessages;
    private final Map<Context, DateTimeRestrictionFormScope> scopes = new HashMap<>();
    private final Map<Context, List<Attribute>> contextAttributes = new HashMap<>();
    private final MetainfoServiceSync metainfoService;
    private final ListComponentsHolder listComponentsHolder;
    private final OriginProvider originProvider;

    @Inject
    public DateTimeRestrictionPropertyControllerImpl(
            DispatchAsync dispatch,
            FormUtils formUtils,
            ValidationMessages validationMessages,
            MetainfoServiceSync metainfoService,
            ListComponentsHolder listComponentsHolder,
            OriginProvider originProvider)
    {
        this.dispatch = dispatch;
        this.formUtils = formUtils;
        this.validationMessages = validationMessages;
        this.metainfoService = metainfoService;
        this.listComponentsHolder = listComponentsHolder;
        this.originProvider = originProvider;
    }

    @Override
    public void init(Context formContext)
    {
        unregisterContext(formContext);
        Collection<Attribute> formAttributes = getFormAttributes(formContext);
        Collection<Attribute> allAttributes = getAllAttributes(formContext);
        DateTimeRestrictionFormScope scope = new DateTimeRestrictionFormScope(dispatch, formUtils, validationMessages,
                formContext, metainfoService, listComponentsHolder, originProvider);
        scopes.put(formContext, scope);
        scope.initAndRecalcRestrictions(formAttributes, allAttributes);
    }

    @Override
    public void initContextScope(PropertyListContext formContext)
    {
        Collection<Attribute> formAttributes = contextAttributes.get(formContext);
        Collection<Attribute> allAttributes = getAllAttributes(formContext);
        DateTimeRestrictionFormScope scope = new DateTimeRestrictionFormScope(dispatch, formUtils, validationMessages,
                formContext, metainfoService, listComponentsHolder, originProvider);
        scopes.put(formContext, scope);
        scope.initAndRecalcRestrictions(formAttributes, allAttributes);
    }

    @Override
    public void reRegisterContext(PropertyListContext context)
    {
        unregisterContext(context);
        registerContext(context);
    }

    @Override
    public void recalcRestrictions(PropertyListContext formContext)
    {
        if (scopes.containsKey(formContext))
        {
            scopes.get(formContext).recalcRestrictions();
        }
    }

    @Override
    public void registerAttribute(PropertyListContext context, Attribute attribute)
    {
        contextAttributes.get(context).add(attribute);
    }

    @Override
    public void registerContext(Context context)
    {
        contextAttributes.putIfAbsent(context, new ArrayList<>());
    }

    @Override
    public void unregisterAttribute(PropertyListContext context, Attribute attribute)
    {
        if (contextAttributes.containsKey(context))
        {
            contextAttributes.get(context).remove(attribute);
        }
        if (scopes.containsKey(context))
        {
            scopes.get(context).unregisterAttribute(attribute);
        }
    }

    @Override
    public void unregisterContext(Context formContext)
    {
        if (scopes.containsKey(formContext))
        {
            scopes.get(formContext).unregisterAll();
            scopes.remove(formContext);
        }
    }

    private static Collection<Attribute> getAllAttributes(Context formContext)
    {
        Collection<Attribute> attributes = getCustomContextAttributes(formContext);
        if (formContext instanceof PropertyListContext && attributes == null)
        {
            attributes = getAttributesFromMetainfo((PropertyListContext)formContext);
        }
        return attributes;
    }

    private static Set<String> getAttributesFromContentLayout(FormContext formContext)
    {
        MetaClass metaClass = formContext.getMetainfo();
        Collection<String> attributeGroupCodes = metaClass.getAttributeGroupCodes();
        Queue<Layout> layouts = new ArrayDeque<>();
        layouts.add(formContext.getForm().getLayout());
        Set<String> formAttributes = new HashSet<>();
        while (!layouts.isEmpty())
        {
            Layout layout = layouts.poll();
            for (Content content : layout.getContent())
            {
                if (content instanceof TabBar)
                {
                    ((TabBar)content).getTab().stream().map(Tab::getLayout).forEach(layouts::add);
                }
                else if (content instanceof PropertyListBase && !(content instanceof RelObjPropertyList))
                {
                    String attributeGroupCode = ((PropertyListBase)content).getAttributeGroup();
                    if (!attributeGroupCodes.contains(attributeGroupCode))
                    {
                        continue;
                    }
                    formAttributes.addAll(metaClass.getAttributeGroup(attributeGroupCode).getAttributeCodes());
                }
            }
        }
        return formAttributes;
    }

    private static Collection<Attribute> getAttributesFromMetainfo(PropertyListContext formContext)
    {
        Collection<Attribute> attributes;
        MetaClass metainfo = formContext.getMetainfo();
        attributes = metainfo.getAttributes();
        return attributes;
    }

    @SuppressWarnings("unchecked")
    @CheckForNull
    private static Collection<Attribute> getCustomContextAttributes(Context formContext)
    {
        if (formContext instanceof CustomFormContext)
        {
            return (Collection<Attribute>)((CustomFormContext)formContext).getForm().getAttributes();
        }
        if (formContext instanceof MassEditActionContext)
        {
            return ((MassEditActionContext)formContext).getAllAttributesOnForm();
        }
        if (formContext instanceof PropertyListContext &&
            ((PropertyListContext)formContext).getParentContext() instanceof AdvlistEditableCellContext)
        {
            Attribute attr = ((AdvlistEditableCellContext)((PropertyListContext)formContext).getParentContext())
                    .getCellAttribute();
            return Lists.newArrayList(attr);
        }
        else if (formContext instanceof ReportContext)
        {
            return ((ReportContext)formContext).getParameters()
                    .stream()
                    .map(p -> ReportParametersHelper.getAttribute(p, (ReportContext)formContext))
                    .collect(Collectors.toList());
        }
        return null;
    }

    private static Collection<Attribute> getFormAttributes(Context context)
    {
        Collection<Attribute> attributes = getCustomContextAttributes(context);
        if (attributes == null && context instanceof FormContext)
        {
            MetaClass metaClass = context.getMetainfo();
            FormContext formContext = (FormContext)context;
            if (formContext.getForm() != null && formContext.getForm().getLayout() != null)
            {
                Set<String> formAttributes = getAttributesFromContentLayout(formContext);
                attributes = metaClass.getAttributes()
                        .stream()
                        .filter(a -> formAttributes.contains(a.getCode()))
                        .collect(Collectors.toList());
                attributes = attributes.isEmpty() ? formContext.getAttributes() : attributes;
            }
        }
        if (context instanceof PropertyListContext && attributes == null)
        {
            attributes = getAttributesFromMetainfo((PropertyListContext)context);
        }
        return attributes;
    }
}
