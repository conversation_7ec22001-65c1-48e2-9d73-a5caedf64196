package ru.naumen.dynaform.client.preview;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.preview.AbstractPreviewInitContext;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.shared.preview.GetFileListDtosAction;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;

import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.List;

/**
 * Реализация {@link PreviewContextFactory} по-умолчанию
 *
 * <AUTHOR>
 * @since 08 окт. 2015 г.
 */
public class PreviewContextFactoryImpl<T extends AbstractPreviewInitContext> implements PreviewContextFactory<T>
{
    private final DispatchAsync dispatch;
    private final MetainfoServiceSync metainfoServiceSync;

    @Inject
    public PreviewContextFactoryImpl(DispatchAsync dispatch, MetainfoServiceSync metainfoServiceSync)
    {
        this.dispatch = dispatch;
        this.metainfoServiceSync = metainfoServiceSync;
    }

    @Override
    public void create(final T context, final AsyncCallback<PreviewContext> callback)
    {
       /*
       Должен вернуть коллекцию всех файлов контента, в каждом файле должны быть параметры, характеризующие
       возможность открытия файла в preview.
       Вызывается каждый раз при открытии preview, после закрытия preview, коллекция файлов удаляется из контекста.
        */
        dispatch.execute(new GetFileListDtosAction(context.getFileUuid()),
                new BasicCallback<SimpleResult<ArrayList<DtObject>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<ArrayList<DtObject>> result)
                    {
                        MetaClass metaClass = metainfoServiceSync.getMetaClass(File.FQN);
                        List<String> attrCodes = metaClass.getAttributeGroup(AttrGroup.SYSTEM).getAttributeCodes();
                        //определяем index файла, для которого изначально произошел вызов открытия в preview
                        int index = Iterables.indexOf(result.get(), new Predicate<DtObject>()
                        {
                            @Override
                            public boolean apply(DtObject file)
                            {
                                return ObjectUtils.equals(file.getUUID(), context.getFileUuid());
                            }
                        });
                        callback.onSuccess(new PreviewContext(result.get(), metaClass, attrCodes, index));
                    }
                });
    }
}

