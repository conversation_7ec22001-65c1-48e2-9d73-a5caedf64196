package ru.naumen.dynaform.client.content;

import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.hasScript;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByAttributeCondition;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByCommonCondition;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByScript;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.events.RestrictionDateTimeFieldEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dispatch.TransactionalBatchAction;
import ru.naumen.core.shared.dispatch.datetime.DateTimeRestrictionByScriptResponse;
import ru.naumen.core.shared.dispatch.datetime.Restriction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.client.ListComponentsHolder;

/**
 * Класс, отвечающий за хранение ограничений для конкретной формы или ее части
 * @since 13 дек. 2018 г.
 */
public class DateTimeRestrictionFormScope
{
    private final DispatchAsync dispatch;
    private final FormUtils formUtils;
    private final Context formContext;
    private final ValidationMessages validationMessages;
    private final MetainfoServiceSync metainfoService;
    private final Map<Attribute, IDateTimeRestrictedAttribute> restrictions = new HashMap<>();
    private Map<String, Attribute> attributeByFqn;
    private final ListComponentsHolder listComponentsHolder;
    private final OriginProvider originProvider;

    DateTimeRestrictionFormScope( //NOSONAR более 8 аргументов конструктора
            DispatchAsync dispatch,
            FormUtils formUtils,
            ValidationMessages validationMessages,
            Context formContext,
            MetainfoServiceSync metainfoService,
            ListComponentsHolder listComponentsHolder,
            OriginProvider originProvider)
    {
        this.dispatch = dispatch;
        this.formUtils = formUtils;
        this.formContext = formContext;
        this.validationMessages = validationMessages;
        this.metainfoService = metainfoService;
        this.listComponentsHolder = listComponentsHolder;
        this.originProvider = originProvider;
    }

    public void initAndRecalcRestrictions(Collection<Attribute> formAttributes, Collection<Attribute> allAttributes)
    {
        if (CollectionUtils.isEmpty(formAttributes))
        {
            return;
        }
        attributeByFqn = allAttributes.stream()
                .filter(attr -> Objects.nonNull(attr.getHierarchicalFqn()))
                .collect(Collectors.toMap(attr -> attr.getHierarchicalFqn().toString(), Function.identity()));
        formAttributes.stream()
                .filter(DateTimeRestrictionAttributeTool::isRestricted)
                .forEach(this::addRestrictedAttribute);
        recalcRestrictions();
    }

    public void unregisterAll()
    {
        restrictions.values().forEach(IDateTimeRestrictedAttribute::removeHandlers);
        restrictions.values().forEach(IDateTimeRestrictedAttribute::unregisterRestrictions);
        restrictions.clear();
    }

    public void unregisterAttribute(Attribute attribute)
    {
        restrictions.values()
                .stream()
                .filter(restriction -> restriction.getOwner().equals(attribute))
                .forEach(restriction ->
                {
                    restriction.removeHandlers();
                    restriction.unregisterRestrictions();
                });
    }

    void recalcRestrictions()
    {
        formContext.getReadyState().ready(new ReadyCallback(formContext)
        {
            @Override
            public void onReady()
            {
                recalcClientRestrictions();
                recalcRemoteRestrictions();
            }
        });
    }

    private void addRestrictedAttribute(Attribute attribute)
    {
        if (formContext instanceof PropertyListContext && isRestrictedByAttributeCondition(attribute))
        {
            DateTimeRestrictedByAttributeScope restItem = new DateTimeRestrictedByAttributeScope(attributeByFqn,
                    validationMessages, attribute, (PropertyListContext)formContext, formUtils, metainfoService);
            DtObject subject = restItem.getObject();
            if (subject.getUUID() == null)
            {
                return;
            }
            if (subject.hasProperty(attribute.getDateTimeRestrictionAttribute())
                || UuidHelper.isTempUuid(subject.getUUID()))
            {
                restItem.init();
                restrictions.put(attribute, restItem);
                return;
            }
            ReadyState readyState = formContext.getReadyState();
            readyState.notReady();
            GetDtObjectAction action = new GetDtObjectAction(subject.getUUID());
            action.setCheckAttrPermissions(false);
            dispatch.execute(action, new BasicCallback<GetDtObjectResponse>()
            {
                @Override
                protected void handleSuccess(GetDtObjectResponse result)
                {
                    restItem.setFullDtObject(result.getObj());
                    restItem.init();
                    restrictions.put(attribute, restItem);
                    readyState.ready();
                }
            });
            return;
        }

        IDateTimeRestrictedAttribute restrictionItem = null;
        if (hasRestrictionByScript(attribute))
        {
            restrictionItem = new DateTimeRestrictedByScriptAttributeScope(dispatch, attribute, formContext,
                    metainfoService, listComponentsHolder, originProvider);
        }
        else if (formContext instanceof PropertyListContext && isRestrictedByCommonCondition(attribute))
        {
            restrictionItem = new DateTimeCommonRestrictedAttributeScope(validationMessages, attribute,
                    (PropertyListContext)formContext, formUtils);
        }
        if (restrictionItem != null)
        {
            restrictionItem.init();
            restrictions.put(attribute, restrictionItem);
        }
    }

    private void executeRecalcActions(List<Action<?>> actions)
    {
        dispatch.execute(new TransactionalBatchAction(actions),
                new BasicCallback<BatchResult>(formContext.getReadyState())
                {
                    @Override
                    protected void handleSuccess(BatchResult results)
                    {
                        results.forEach(result ->
                        {
                            DateTimeRestrictionByScriptResponse restrictionResponse =
                                    (DateTimeRestrictionByScriptResponse)result;
                            processResponse(restrictionResponse, restrictionResponse.getScriptOwner());
                        });
                    }
                });
    }

    private void fireRestrictionDateTimeFieldEvent(Attribute restrictionOwner, Attribute changedAttr,
            Map<Restriction<?>, String> restrictions)
    {
        formContext.getEventBus()
                .fireEvent(new RestrictionDateTimeFieldEvent(restrictionOwner, changedAttr, restrictions));
    }

    private static boolean hasRestrictionByScript(Attribute attribute)
    {
        return isRestrictedByScript(attribute) && hasScript(attribute);
    }

    private void processResponse(DateTimeRestrictionByScriptResponse response, Attribute changedAttr)
    {
        fireRestrictionDateTimeFieldEvent(response.getScriptOwner(), changedAttr, response.getRestrictions());
    }

    private void recalcClientRestrictions()
    {
        restrictions.values()
                .stream()
                .filter(item -> !(item instanceof IDateTimeRemoteRestrictedAttribute))
                .forEach(IDateTimeRestrictedAttribute::recalcRestriction);
    }

    private void recalcRemoteRestrictions()
    {
        List<Action<?>> actions = restrictions.values()
                .stream()
                .filter(iDateTimeRestrictedAttribute -> iDateTimeRestrictedAttribute instanceof IDateTimeRemoteRestrictedAttribute)
                .map(iDateTimeRestrictedAttribute -> (IDateTimeRemoteRestrictedAttribute)iDateTimeRestrictedAttribute)
                .map(IDateTimeRemoteRestrictedAttribute::getRecalcAction)
                .collect(Collectors.toList());
        executeRecalcActions(actions);
    }
}