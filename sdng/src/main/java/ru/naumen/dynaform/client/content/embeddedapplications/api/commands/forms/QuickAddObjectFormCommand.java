package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.forms;

import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.CLASS_FQN;
import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.FORM_CODE;
import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.PROPERTIES;

import java.util.Objects;
import java.util.Optional;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.json.client.JSONObject;

import ru.naumen.common.client.utils.HandlerRegistrationHolder;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.events.FormCanceledEvent;
import ru.naumen.core.client.events.PendingActionsChangedEvent;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.FormPresentersSplitPoint;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;
import ru.naumen.dynaform.client.quickforms.QuickFormFactory;
import ru.naumen.dynaform.client.quickforms.QuickFormHelper;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;

/**
 * Команда вызова формы быстрого добавления из встроенного приложения.
 *
 * <AUTHOR>
 * @since Feb 11, 2019
 */
public class QuickAddObjectFormCommand extends FormCommandBase
{
    private final QuickFormFactory quickFormFactory;
    private final SplitPointService splitPointService;
    private final QuickFormHelper quickFormHelper;

    @Inject
    public QuickAddObjectFormCommand(
            final HandlerRegistrationHolder handlerRegistrationHolder,
            final QuickFormFactory quickFormFactory,
            final QuickFormHelper quickFormHelper,
            final SplitPointService splitPointService)
    {
        super(handlerRegistrationHolder);
        this.quickFormFactory = quickFormFactory;
        this.quickFormHelper = quickFormHelper;
        this.splitPointService = splitPointService;
    }

    @Override
    public JsApiCommandType getType()
    {
        return JsApiCommandType.QUICK_ADD_OBJECT_FORM;
    }

    @Override
    protected void perform(final JSONObject parameters)
    {
        final ClassFqn objectFqn = ClassFqn.parse(
                Objects.requireNonNull(parameters.get(CLASS_FQN).isString()).stringValue());
        final String formCode = Objects.requireNonNull(parameters.get(FORM_CODE).isString()).stringValue();
        final MapProperties properties = Optional.ofNullable(parameters.get(PROPERTIES).isObject())
                .map(quickFormHelper::resolveJsonProperties)
                .orElse(null);

        final DtObject sourceObject = Objects.requireNonNull(context.getParentContext()).getObject();
        splitPointService.inject(FormPresentersSplitPoint.class,
                new ContextualCallback<FormPresentersSplitPoint>(context)
                {
                    @Override
                    protected void handleSuccess(final FormPresentersSplitPoint sp)
                    {
                        final EventBus eventBus = context.getEventBus();
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(PendingActionsChangedEvent.TYPE, QuickAddObjectFormCommand.this));
                        handlerRegistrationHolder.register(
                                eventBus.addHandler(FormCanceledEvent.getType(), QuickAddObjectFormCommand.this));

                        quickFormFactory.showQuickAddForm(sourceObject, objectFqn,
                                QuickForm.UUID_PREFIX + formCode, properties, context,
                                new ContextualCallback<>(context));
                    }
                });
    }
}
