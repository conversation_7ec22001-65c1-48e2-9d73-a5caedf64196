package ru.naumen.dynaform.client.form;

import java.util.ArrayList;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.DialogBox.Caption;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.events.ClearBrowserValueStashEvent;
import ru.naumen.core.client.events.ClearBrowserValueStashEventHandler;
import ru.naumen.core.client.events.HasAnyValueInBrowserStashRequestEvent;
import ru.naumen.core.client.events.HasAnyValueInBrowserStashRequestEventHandler;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormDisplayImpl;
import ru.naumen.core.client.forms.FormPresenterBase;
import ru.naumen.core.client.licensing.quota.QuotingBalanceChecker;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.shared.HasBadge;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.attention.AttentionUtils;
import ru.naumen.dynaform.client.content.embeddedapplications.ModalFormInfo;
import ru.naumen.dynaform.client.content.embeddedapplications.api.JsApiFormHelper;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.objectlist.client.ObjectListContext;

/**
 * Базовый функционал формы в ИО, имеющей контекст DynaContext
 *
 * <AUTHOR>
 * @since 13.09.22
 */
public abstract class OperatorFormPresenterBase<C extends DynaContext, D extends FormDisplay>
        extends FormPresenterBase<D> implements HasAnyValueInBrowserStashRequestEventHandler,
        ClearBrowserValueStashEventHandler
{
    @Inject
    protected AttentionUtils attentionUtils;
    @Inject
    protected Processor validator;
    @Inject
    private QuotingBalanceChecker quotingBalanceChecker;
    @Inject
    private BadgeUtils badgeUtils;
    @Inject
    private JsApiFormHelper jsApiFormHelper;
    @Inject
    private ClientValidationServiceAsync clientValidationServiceAsync;
    @Inject
    private SharedSettingsClientService sharedSettingsClientService;

    protected C context;

    public OperatorFormPresenterBase(D display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        initializeBadge();
        initializeDefaultAttention();
        addHandler(HasAnyValueInBrowserStashRequestEvent.getType(), this);
        addHandler(ClearBrowserValueStashEvent.getType(), this);
    }

    @Override
    public void onFormHasAnyAttribute(HasAnyValueInBrowserStashRequestEvent e)
    {
        if (browserValueStash.hasAnyAttributeValue())
        {
            e.setHasAnyValue(true);
        }
    }

    @Override
    public void onClearAttributeValues(ClearBrowserValueStashEvent e)
    {
        browserValueStash.clearFormData();
    }

    protected void initContext(C context)
    {
        this.context = context;
    }

    public C getContext()
    {
        return context;
    }

    /**
     * Инициализирует предупреждение на форме, которые должны отобразиться сразу после открытия
     */
    private void initializeDefaultAttention()
    {
        DynaContext context = getContext();
        if (context == null)
        {
            return;
        }
        clearAttentionMessage();
        attentionUtils.initializeDefaultAttention(this, context);
    }

    /**
     * Обновляет предупреждение на форме
     */
    public void refreshAttention()
    {
        DynaContext context = getContext();
        if (context == null)
        {
            return;
        }
        clearAttentionMessage();
        attentionUtils.initializeDefaultAttention(this, context);
        quotingBalanceChecker.checkBalance(context.getObjects(), this);
    }

    private void initializeBadge()
    {
        DynaContext context = getContext();
        if (context == null)
        {
            return;
        }
        if (getRealDisplay() instanceof FormDisplayImpl)
        {
            Caption caption = ((FormDisplayImpl)getRealDisplay()).getCaption();
            if (caption instanceof HasBadge)
            {
                if (!(Form.NEW.equals(context.getFormCode()) || Form.QUICK_ADD_FORM.equals(context.getFormCode()))
                    && context instanceof FormContext
                    && ((FormContext)context).getParentContext() instanceof ObjectListContext)
                {
                    badgeUtils.initializeBadge(context.getMetainfo(),
                            ((FormContext)context).getParentContext().getObjects(), (HasBadge)caption);
                }
                else
                {
                    badgeUtils.initializeBadge(context.getMetainfo(), context.getObject(), (HasBadge)caption);
                }
            }
        }
    }

    /**
     * Выполняет валидацию значений внутри встроенного приложения через jsApi
     * @return true - если на форме нет ВП или валидация прошла успешна, false - если прошла не успешно или была
     * выброшена ошибка
     */
    protected boolean validateForApp()
    {
        try
        {
            return isFormWithoutEmbeddedApplications() || JsApiFormHelper.validateProperties();
        }
        catch (Exception ex)
        {
            processAppException(ex);
            return false;
        }
    }

    /**
     * Выполняет дополнительную асинхронную валидацию полей формы, отправляет результаты на сервер.
     * Нужно для работы логирования клиентских валидаций.
     * Завязан на значении параметра dbaccess ru.naumen.log.clientValidation.enabled. Если выключен -
     * дополнительной валидации не будет.
     */
    protected void validateAsyncIfNeeded()
    {
        if (sharedSettingsClientService.isLogClientValidationEnabled())
        {
            ValidateEvent validateEvent = new ValidateEvent(context);
            validator.validateAsync(validateEvent);
            context.getReadyState().ready(new ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    clientValidationServiceAsync.logOnServer(context.getFormCode(),
                            context.getObjects() == null
                                    ? new ArrayList<>()
                                    : context.getObjects().stream().map(DtObject::getUUID).collect(Collectors.toList()),
                            context.getMetainfo() == null ? StringUtilities.EMPTY : context.getMetainfo().getCode(),
                            validateEvent.getValidationMessages());
                }
            });
        }
    }

    /**
     * Извлекает значения атрибутов из встроенного приложения через jsApi
     */
    protected MapProperties getPropertiesForApp()
    {
        if (formExtensionModule.isEmpty())
        {
            return new MapProperties();
        }
        try
        {
            return jsApiFormHelper.extractProperties();
        }
        catch (Exception ex)
        {
            processAppException(ex);
            getDisplay().stopProcessing();
            return new MapProperties();
        }
    }

    private void processAppException(Exception ex)
    {
        ErrorAndAttentionMessageHandler msgHandler = getContext().getErrorAndAttentionMsgHandler();
        if (null != msgHandler)
        {
            msgHandler.addErrorMessage(SafeHtmlUtils.fromString(JsApiFormHelper.prepareException(ex)));
        }
    }

    /**
     * Возвращает информацию о модальной форме для встроенного приложения
     * @return информация о модальной форме или null
     */
    @Nullable
    public ModalFormInfo getFormInfo()
    {
        return null;
    }
}
