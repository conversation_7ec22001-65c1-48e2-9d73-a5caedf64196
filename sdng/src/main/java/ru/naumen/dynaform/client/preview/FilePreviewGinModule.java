package ru.naumen.dynaform.client.preview;

import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import java.util.HashMap;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.preview.FileAdvlistInitContext;
import ru.naumen.core.client.preview.FileCellListInitContext;
import ru.naumen.core.client.preview.ImageFileInitContext;
import ru.naumen.core.client.preview.OpenPreviewFromImagesHandler;
import ru.naumen.core.client.preview.RtfImageInitContext;
import ru.naumen.dynaform.client.preview.canvas.AudioPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.DocPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.DocxPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.ImagePreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.MsExcelPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.PowerPointDocPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvasFactory;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvasFactoryImpl;
import ru.naumen.dynaform.client.preview.canvas.PreviewCanvasWidgetFactory;
import ru.naumen.dynaform.client.preview.canvas.TextPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.VideoPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.XmlPreviewCanvas;
import ru.naumen.dynaform.client.preview.canvas.pdf.AdvancedPdfPreviewCanvasImpl;
import ru.naumen.dynaform.client.preview.canvas.scroll.IScrollbarController;
import ru.naumen.dynaform.client.preview.canvas.scroll.ScrollbarController;
import ru.naumen.dynaform.client.preview.canvas.scroll.ScrollbarControllerFactory;
import ru.naumen.dynaform.client.preview.toolbar.TextRTFImagePreviewButtonBinder;

/**
 *
 * <AUTHOR>
 * @since 27 авг. 2015 г.
 */
public class FilePreviewGinModule extends AbstractGinModule
{
    /**
     *
     * <AUTHOR>
     * @since 08 окт. 2015 г.
     */
    public static class CanvasFactoriesProvider implements
            Provider<Map<String, PreviewCanvasWidgetFactory<? extends PreviewCanvas>>>
    {
        @Inject
        private PreviewCanvasWidgetFactory<ImagePreviewCanvas> imagePCF;
        @Inject
        private PreviewCanvasWidgetFactory<AdvancedPdfPreviewCanvasImpl> pdfPCF;
        @Inject
        private PreviewCanvasWidgetFactory<DocPreviewCanvas> docPCF;
        @Inject
        private PreviewCanvasWidgetFactory<DocxPreviewCanvas> docxPCF;
        @Inject
        private PreviewCanvasWidgetFactory<MsExcelPreviewCanvas> msExcelPCF;
        @Inject
        private PreviewCanvasWidgetFactory<PowerPointDocPreviewCanvas> powerPointPCF;
        @Inject
        private PreviewCanvasWidgetFactory<TextPreviewCanvas> textPCF;
        @Inject
        private PreviewCanvasWidgetFactory<XmlPreviewCanvas> xmlPCF;
        @Inject
        private PreviewCanvasWidgetFactory<VideoPreviewCanvas> videoPCF;
        @Inject
        private PreviewCanvasWidgetFactory<AudioPreviewCanvas> audioPCF;

        @Override
        public Map<String, PreviewCanvasWidgetFactory<? extends PreviewCanvas>> get()
        {
            Map<String, PreviewCanvasWidgetFactory<? extends PreviewCanvas>> registry = new HashMap<>();

            //@formatter:off
            registry.put("image/jpeg",                                                                imagePCF);
            registry.put("image/png",                                                                 imagePCF);
            registry.put("image/gif",                                                                 imagePCF);
            registry.put("image/bmp",                                                                 imagePCF);
            registry.put("image/nbmp",                                                                imagePCF);
            registry.put("image/x-ms-bmp",                                                            imagePCF);
            registry.put("image/svg+xml",                                                             imagePCF);
            registry.put("application/pdf",                                                           pdfPCF);
            registry.put("application/msword",                                                        docPCF);
            registry.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document",   docxPCF);
            registry.put("application/vnd.ms-excel",                                                  msExcelPCF);
            registry.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",         msExcelPCF);
            registry.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", powerPointPCF);
            registry.put("application/vnd.ms-powerpoint",                                             powerPointPCF);
            registry.put("text/csv",                                                                  textPCF);
            registry.put("text/plain",                                                                textPCF);
            registry.put("text/xml",                                                                  xmlPCF);
            registry.put("application/xml",                                                           xmlPCF);
            registry.put("video/mp4",                                                                 videoPCF);
            registry.put("video/webm",                                                                videoPCF);
            registry.put("video/ogg",                                                                 videoPCF);
            registry.put("audio/mpeg",                                                                audioPCF);
            registry.put("audio/ogg",                                                                 audioPCF);
            registry.put("audio/wav",                                                                 audioPCF);
            registry.put("audio/x-wav",                                                               audioPCF);
            registry.put("audio/vnd.wave",                                                            audioPCF);
            registry.put("audio/mp3",                                                                 audioPCF);
            //@formatter:on

            return registry;
        }
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(PreviewCanvasFactory.class).to(PreviewCanvasFactoryImpl.class);
        
        install(PreviewPresenterGinModule.create(RtfImageInitContext.class, TextRTFPreviewContextFactory.class)
                                         .setButtonBinder(TextRTFImagePreviewButtonBinder.class));
        
        install(PreviewPresenterGinModule.create(FileCellListInitContext.class, 
                new TypeLiteral<PreviewContextFactoryImpl<FileCellListInitContext>>(){}));
        
        install(PreviewPresenterGinModule.create(FileAdvlistInitContext.class, 
                FileAdvlistPreviewContextFactoryImpl.class));
        
        install(PreviewPresenterGinModule.create(ImageFileInitContext.class, 
                new TypeLiteral<PreviewContextFactoryImpl<ImageFileInitContext>>(){}));

        // кастомный скролл
        install(new GinFactoryModuleBuilder()
            .implement(IScrollbarController.class, ScrollbarController.class)
            .build(ScrollbarControllerFactory.class));
        
        bind(ShowPreviewHandlerInitializer.class).asEagerSingleton();
        
        bind(OpenPreviewFromListHandler.class)
            .to(OpenPreviewFromListHandlerImpl.class)
            .in(Singleton.class);
        
        bind(OpenPreviewFromImagesHandler.class)
            .to(OpenPreviewFromImagesHandlerImpl.class)
            .in(Singleton.class);
        
        bindCanvasFactories();
        //@formatter:on
    }

    private void bindCanvasFactories()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(ImagePreviewCanvas.class, ImagePreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<ImagePreviewCanvas>>(){}));

        install(new GinFactoryModuleBuilder()
                .implement(AdvancedPdfPreviewCanvasImpl.class, AdvancedPdfPreviewCanvasImpl.class)
                .build(new TypeLiteral<PreviewCanvasWidgetFactory<AdvancedPdfPreviewCanvasImpl>>(){}));

        install(new GinFactoryModuleBuilder()
            .implement(DocPreviewCanvas.class, DocPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<DocPreviewCanvas>>(){}));

        install(new GinFactoryModuleBuilder()
            .implement(DocxPreviewCanvas.class, DocxPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<DocxPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(MsExcelPreviewCanvas.class, MsExcelPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<MsExcelPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(PowerPointDocPreviewCanvas.class, PowerPointDocPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<PowerPointDocPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(TextPreviewCanvas.class, TextPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<TextPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(XmlPreviewCanvas.class, XmlPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<XmlPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(VideoPreviewCanvas.class, VideoPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<VideoPreviewCanvas>>(){}));
        
        install(new GinFactoryModuleBuilder()
            .implement(AudioPreviewCanvas.class, AudioPreviewCanvas.class)
            .build(new TypeLiteral<PreviewCanvasWidgetFactory<AudioPreviewCanvas>>(){}));

        bind(new TypeLiteral<Map<String, PreviewCanvasWidgetFactory<? extends PreviewCanvas>>>(){})
            .toProvider(CanvasFactoriesProvider.class)
            .in(Singleton.class);
        //@formatter:on
    }
}
