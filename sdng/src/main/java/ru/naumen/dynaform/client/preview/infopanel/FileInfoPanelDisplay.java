package ru.naumen.dynaform.client.preview.infopanel;

import java.util.List;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.dynaform.client.preview.FilePreviewCss;
import ru.naumen.dynaform.client.preview.FilePreviewHtmlTemplates;
import ru.naumen.dynaform.client.preview.FilePreviewResources;

import java.util.ArrayList;

import com.google.gwt.core.client.GWT;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

/**
 * Реализация {@link Display} для информационной панели
 *
 * <AUTHOR>
 * @since 03 сент. 2015 г.
 */
public class FileInfoPanelDisplay implements Display
{
    public class PropertyRegistrationImpl<T> implements PropertyRegistration<T>
    {
        private Property<T> property;

        public PropertyRegistrationImpl(Property<T> property)
        {
            this.property = property;
        }

        @Override
        public Property<T> getProperty()
        {
            return property;
        }

        @Override
        public boolean isEnabled()
        {
            return property.isEnabled();
        }

        @Override
        public void setEnabled(boolean enabled)
        {
            property.setEnabled(enabled);
        }

        @Override
        public PropertyRegistration<T> setProperty(Property<T> property)
        {
            this.property = property;
            addRow(this);
            return this;
        }

        @Override
        public void unregister()
        {
        }
    }

    public static final int INFO_PANEL_WIDTH = 400;
    private FilePreviewResources resources = GWT.create(FilePreviewResources.class);

    private FilePreviewHtmlTemplates templates = GWT.create(FilePreviewHtmlTemplates.class);

    private VerticalPanel panel;

    private HTML title;

    private FlexTable table;

    List<PropertyRegistration<?>> properties;

    public FileInfoPanelDisplay()
    {
        panel = new VerticalPanel();

        title = new HTML(); // NOPMD NSDPRD-28509 unsafe html
        panel.add(title);
        panel.setCellHeight(title, "60px");

        table = new FlexTable();
        panel.add(table);
        table.addStyleName(resources.style().propertyList());

        FilePreviewCss style = resources.style();
        style.ensureInjected();
        panel.addStyleName(style.panel());
        panel.addStyleName(style.infoPanel());

        properties = new ArrayList<>();
    }

    /**
     * Добавить свойство
     *
     * @param property
     */
    public <T> PropertyRegistration<T> addProperty(Property<T> property)
    {
        PropertyRegistration<T> registration = new PropertyRegistrationImpl<>(property);
        properties.add(registration);
        addRow(registration);
        return registration;
    }

    @Override
    public Widget asWidget()
    {
        return panel;
    }

    @Override
    public void destroy()
    {
        asWidget().removeFromParent();
    }

    /**
     * Установить заголовок панели 
     *
     * @param title заголовок
     */
    public void setTitle(String title)
    {
        this.title.setHTML(templates.infoPanelTitle(title));
    }

    @Override
    public void startProcessing()
    {
    }

    @Override
    public void stopProcessing()
    {
    }

    private void addRow(PropertyRegistration<?> registration)
    {
        Property<?> property = registration.getProperty();

        int index = properties.indexOf(registration);

        if (property != null)
        {
            if (property.isWide())
            {
                table.setWidget(index, 0, property.getValueWidget());
                table.getFlexCellFormatter().setColSpan(index, 0, 2);

                table.getCellFormatter().setStyleName(index, 0, resources.style().widePropertyValue());
            }
            else
            {
                table.setWidget(index, 0, property.getCaptionWidget());
                table.setWidget(index, 1, property.getValueWidget());

                table.getCellFormatter().setStyleName(index, 0, resources.style().propertyTitle());
                table.getCellFormatter().setStyleName(index, 1, resources.style().propertyValue());
            }
        }
        else
        {
            table.insertRow(index);
        }
    }

}
