package ru.naumen.dynaform.client;

import static ru.naumen.core.shared.Constants.ENCODED_PARAMETERS;
import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.common.collect.Sets;
import com.google.gwt.core.client.JsonUtils;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.http.client.URL;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.UrlUtils;
import ru.naumen.core.client.AppHistoryItem;
import ru.naumen.core.client.CompanyInfoService;
import ru.naumen.core.client.FastAction;
import ru.naumen.core.client.activity.AbstractPlace;
import ru.naumen.core.client.activity.IDtObjectPlace;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.toolbar.ActionHandlerRegistry;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.ActionToolContextFactory;
import ru.naumen.core.client.content.toolbar.actions.ActionHandler;
import ru.naumen.core.client.events.ObjectLoadedEvent;
import ru.naumen.core.client.events.RefreshUserInterfaceEvent;
import ru.naumen.core.client.events.UpdatePermissionsEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tabbar.TabBarPresenterLogic;
import ru.naumen.core.client.utils.IWindowWrapper;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.activity.HasExtraOptions;
import ru.naumen.core.shared.dispatch.InitWindowAndObjectAction;
import ru.naumen.core.shared.dispatch.InitWindowAndObjectResponse;
import ru.naumen.core.shared.dispatch.ObjectNotExistException;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.FormPlaceParameter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.activity.OperatorAbstractPlaceWithEncodedParameters;
import ru.naumen.dynaform.client.changetracking.ChangeTrackingUpdateService;
import ru.naumen.dynaform.client.changetracking.events.ChangedContentsRequestEvent;
import ru.naumen.dynaform.client.changetracking.events.MessageEditableAttributesFilterHandlerFactory;
import ru.naumen.dynaform.client.changetracking.events.OpenCurrentObjectEditSessionEvent;
import ru.naumen.dynaform.client.changetracking.events.StartPageUpdateEvent;
import ru.naumen.dynaform.client.content.WindowContentPresenter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.WorkflowLite;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Window;

/**
 * Activity для карточки объекта
 * <AUTHOR>
 *
 */
public class UserPlaceActivity extends DynaformPlaceActivity<ObjectCardPresenter>
{
    protected class UserPlacePresenterAsyncProvider implements AsyncProvider<ObjectCardPresenter>
    {
        private class InitWindowAndObjectCallback extends BasicCallback<InitWindowAndObjectResponse>
        {
            private final AsyncCallback<? super ObjectCardPresenter> callback;

            private InitWindowAndObjectCallback(AsyncCallback<? super ObjectCardPresenter> callback)
            {
                this.callback = callback;
            }

            @Override
            protected void handleFailure(Throwable t)
            {
                Place back = null;
                if (t instanceof ObjectNotExistException)
                {
                    String uuid = ((ObjectNotExistException)t).getUuid();
                    Iterator<AppHistoryItem> it = historyController.getHistoryItems().iterator();
                    if (it.hasNext())
                    {
                        Place place = it.next().getPlace();
                        if (place instanceof UserPlace && uuid.equals(((UserPlace)place).getUUID()) && it.hasNext())
                        {
                            back = it.next().getPlace();
                        }
                    }
                    historyController.clearUuid(uuid);
                    // переопределяем сообщение пользователя
                    t = new ObjectNotExistException(messages.objectNotFoundUserMessage(uuid), uuid);
                }
                super.handleFailure(t);
                if (back != null)
                {
                    placeController.goTo(back);
                }
            }

            @Override
            protected void handleSuccess(InitWindowAndObjectResponse value)
            {
                String jsDownloadHref = DownloadHelper.getJSDownloadHref();
                // если установлена js переменная "downloadHref", то переходим по данной ссылке
                if (jsDownloadHref != null && jsDownloadHref.contains("download"))
                {
                    DownloadHelper.clearJSDownloadHref();
                    com.google.gwt.user.client.Window.Location.replace(jsDownloadHref);
                }
                //если установлена cookie, значит мы после редиректа от Download-сервлета 
                //(была попытка скачать несуществующий файл), необходимо показать сообщение о ошибке.
                else
                {
                    try
                    {
                        utils.checkCookie();
                    }
                    catch (ObjectNotExistException e)
                    {
                        callback.onFailure(e);
                    }
                }

                createPresenter(value, callback);
            }
        }

        protected IDtObjectPlace place;
        protected Window content;
        protected PermissionHolder permissions;
        protected DtObject obj;
        private DefaultDynaContext context;

        @Override
        public void get(final AsyncCallback<? super ObjectCardPresenter> callback)
        {
            try
            {
                place = (UserPlace)placeController.getWhere();
                initWindowAndObject(new InitWindowAndObjectCallback(new BasicCallback<ObjectCardPresenter>()
                {
                    @Override
                    protected void handleSuccess(ObjectCardPresenter presenter)
                    {
                        callback.onSuccess(presenter);
                        executeFastAction();
                    }
                }));
            }
            catch (Exception ex)
            {
                callback.onFailure(ex);
            }
        }

        protected void createPresenter(final InitWindowAndObjectResponse initWindowAndObjectResponse,
                final AsyncCallback<? super ObjectCardPresenter> callback)
        {
            MetaClass metaClass = metainfoServiceSync.getMetaClass(obj.getMetainfo());
            context = new DefaultDynaContext(metaClass, obj, permissions);
            context.getReadyState().registerSynchronization(new ContextStateSynchronization(this, state));
            context.setContextProperty(TabBarPresenterLogic.OBJECTS_COUNT_MAP,
                    initWindowAndObjectResponse.getObjectsCountMap());

            refreshDisplay(context);
            context.registerHandler(eventBus.addHandler(ObjectLoadedEvent.getType(), e ->
            {
                DtObject object = context.getObject();
                if (null != object && e.getObjects().contains(object))
                {
                    DtObject newObject = e.getObjectsAsMap().get(object.getUUID());
                    if (newObject.getMetainfo().equals(object.getMetainfo()))
                    {
                        context.setObject(newObject);
                        refreshDisplay(context);
                    }
                    else
                    {
                        placeController.goTo(new UserPlace(newObject));
                    }
                }
            }));

            dynaformFactory.build(content, context, new BasicCallback<WindowContentPresenter>()
            {
                @Override
                protected void handleSuccess(WindowContentPresenter presenter)
                {
                    objectCardPresenter.setContent(presenter);
                    context.registerHandler(eventBus.addHandler(UpdatePermissionsEvent.getType(),
                            event ->
                            {
                                if (!ObjectUtils.equals(context.getPermissions(), event.getPermissions()))
                                {
                                    context.setPermissions(event.getPermissions());
                                    presenter.onUpdatePermissions(event);
                                }
                            }));

                    context.registerHandler(eventBus.addHandler(RefreshUserInterfaceEvent.getType(),
                            event -> restart()));

                    presenter.registerHandler(eventBus.addHandler(StartPageUpdateEvent.TYPE,
                            event -> changeTrackingUpdateService.update(content, context, event.getMessage())));
                    presenter.registerHandler(eventBus.addHandler(ChangedContentsRequestEvent.TYPE,
                            ChangedContentsRequestEvent::setNeedRefresh));
                    presenter.registerHandler(eventBus.addHandler(OpenCurrentObjectEditSessionEvent.TYPE,
                            messageEditableAttributesFilterHandlerFactory.create(context, content)));

                    callback.onSuccess(objectCardPresenter);
                }
            });
        }

        protected void initWindowAndObject(final AsyncCallback<InitWindowAndObjectResponse> callback)
        {
            String encodedParametersUuid = null;
            if (place instanceof HasExtraOptions placeWithExtraOptions)
            {
                FormPlaceParameter parameter = placeWithExtraOptions.getExtraOption(ENCODED_PARAMETERS);
                encodedParametersUuid = null == parameter ? null : parameter.getValue();
            }
            if (encodedParametersUuid == null
                && place instanceof OperatorAbstractPlaceWithEncodedParameters operatorAbstractPlaceWithEncodedParameters)
            {
                encodedParametersUuid = operatorAbstractPlaceWithEncodedParameters.getEncodedParametersUuid();
            }
            InitWindowAndObjectAction action = new InitWindowAndObjectAction(place.getUUID(), null,
                    getAdditionalContents(), encodedParametersUuid);

            dispatch.execute(action, new BasicCallback<InitWindowAndObjectResponse>()
            {
                @Override
                public void onFailure(Throwable t)
                {
                    callback.onFailure(t);
                }

                @Override
                protected void handleSuccess(InitWindowAndObjectResponse response)
                {
                    content = response.getContent();
                    permissions = response.getPermissions();
                    obj = response.getDto();
                    Content additionalContent = response.getAdditionalContent();
                    if (additionalContent instanceof ActionTool)
                    {
                        fastTool = (ActionTool)additionalContent;
                        ((UserPlace)place).setParameters(
                                new JSONObject(JsonUtils.safeEval(response.getParameters())));
                    }
                    callback.onSuccess(response);
                }
            });
        }

        protected void refreshDisplay(DynaContext ctx)
        {
            DtObject object = ctx.getObject();
            assertObjectNotNull(object);
            String objectTitle = object.getProperty(AbstractBO.BROWSER_TAB_TITLE, object.getTitle());
            String title = companyInfoService.getWindowTitle(objectTitle);
            UserPlaceActivity.this.window.setTitle(title);
        }

        /**
         * Метод предназначен для приведения переданных значений атрибутов к 
         * типам соответствующим типам атрибутов а также для отсева некорректных значений
         * @param value - значение атрибута
         * @param attr - сам атрибут
         * @return - значения типа подходящего для переданного атрибута либо null
         */
        private Object convert(Object value, @Nullable Attribute attr) //NOPMD
        {
            if (attr == null)
            {
                return null;
            }
            String typeCode = attr.getType().getCode();
            String decodeValue = UrlUtils.decodeSpecialCharsInParameter(URL.decode(value.toString()));
            if (ru.naumen.metainfo.shared.Constants.STRING_ATTRIBUTE_TYPES.contains(typeCode))
            {
                return decodeValue;
            }
            return getNumberValue(decodeValue, typeCode);
        }

        private Object getNumberValue(String decodeValue, String typeCode)
        {
            try
            {
                switch (typeCode)
                {
                    case (IntegerAttributeType.CODE):
                        return Long.parseLong(decodeValue);
                    case (DoubleAttributeType.CODE):
                        return Double.parseDouble(decodeValue);
                    default:
                        return null;
                }
            }
            catch (NumberFormatException e)
            {
                LOG.log(Level.FINE, e.getMessage(), e);
                return null;
            }
        }

        /**
         * Выполнить действие заданное в place'е. На данный момент для выполнения 
         * доступно только смена статуса. С появлением новых действий предполагается
         * усложнение метода.
         *
         */
        private void executeFastAction()
        {
            if (fastTool == null)
            {
                return;
            }

            //Выполняем отложенно для того чтобы дать возможность презентеру карточки
            //немного времени на отрисовку
            Scheduler.get().scheduleFixedDelay(() ->
            {
                // Для того чтобы предотвратить выполнение действия при обновлении страницы
                // заменяем текущий place на place без действия
                placeController.goTo(((AbstractPlace)place).cloneIt());

                DtObject object = new SimpleDtObject(Objects.requireNonNull(context.getObject()));
                ClassFqn fqn = object.getMetaClass();
                MetaClass metaClass = metainfoServiceSync.getMetaClass(fqn);

                if (!metaClass.hasAttribute(HasState.STATE))
                {
                    MetaClass parentMetaClass = metainfoServiceSync.getMetaClass(metaClass.getFqn().fqnOfClass());
                    //У класса объекта нет жизненного цикла
                    dialogs.error(cmessages.hasNoWorkflow(object.getTitle(), parentMetaClass.getTitle()));
                    return false;
                }

                FastAction fastAction = ((UserPlace)place).getFastAction();
                for (String key : fastAction.getKeys())
                {
                    object.setProperty(key, convert(fastAction.getValue(key),
                            metaClass.getAttribute(HasState.TARGET_STATE.equals(key) ? HasState.STATE : key)));
                }

                WorkflowLite wf = metainfoServiceSync.getMetaClassLite(fqn).getWorkflowLite();
                String currentState = object.getProperty(HasState.STATE);
                String targetState = object.getProperty(HasState.TARGET_STATE);

                if (targetState != null && wf.getState(targetState) == null)
                {
                    dialogs.error(cmessages.noStateWithCode(object.getTitle(), targetState));
                    return false;
                }

                if (ObjectUtils.equals(currentState, targetState))
                {
                    String currentStateTitle = wf.getState(currentState).getTitle();
                    dialogs.error(cmessages.objectAlreadyIsInNewState(currentStateTitle, object.getTitle()));
                    return false;
                }

                if (!context.hasContentPermission(fastTool, Constants.CHANGE_STATE))
                {
                    MetaClass parentMetaClass = metainfoServiceSync.getMetaClass(metaClass.getFqn().fqnOfClass());
                    dialogs.error(cmessages.cannotChangeState(object.getTitle(), parentMetaClass.getTitle()));
                    return false;
                }

                if (targetState != null && !context.hasContentPermission(fastTool, SecConstants.ServiceCall.CHANGE_STATE
                                                                                   + currentState
                                                                                   + SecConstants.ServiceCall.CHANGE_STATE_CODE_DELIMITER
                                                                                   + targetState))
                {
                    String currentStateTitle = wf.getState(currentState).getTitle();
                    String targetStateTitle = wf.getState(targetState).getTitle();
                    String metaClassTitle = metainfoServiceSync.getMetaClass(metaClass.getFqn().fqnOfClass()).getTitle()
                                            + ": " + metaClass.getTitle();

                    dialogs.error(cmessages.haveNoChangeStatePermission(currentStateTitle, targetStateTitle,
                            metaClassTitle));
                    return false;
                }

                DefaultDynaContext newContext = new DefaultDynaContext(context.getMetainfo(), object,
                        context.getPermissions());
                ActionToolContext actionToolContext = factory.create(fastTool, newContext, null,
                        Constants.CHANGE_STATE);
                ActionHandler actionHandler = actionHandlerRegistry.getHandler(Constants.CHANGE_STATE,
                        actionToolContext);

                actionHandler.execute();
                return false;
            }, 1500);
        }

        private HashSet<Content> getAdditionalContents()
        {
            initFastTool();
            if (fastTool == null)
            {
                return null;
            }
            return Sets.newHashSet(fastTool);
        }

        private void initFastTool()
        {
            if (place instanceof UserPlace)
            {
                FastAction fastAction = ((UserPlace)place).getFastAction();
                if (fastAction == null || !Constants.CHANGE_STATE.equals(((UserPlace)place).getFastAction().getCode()))
                {
                    return;
                }
                fastTool = new ActionTool();
                fastTool.setAction(fastAction.getCode());
            }
        }
    }

    private static final Logger LOG = Logger.getLogger(UserPlaceActivity.class.getName());

    @Inject
    private CompanyInfoService companyInfoService;
    @Inject
    private DynaformContentFactory dynaformFactory;
    @Inject
    private IWindowWrapper window;
    @Inject
    private ActionHandlerRegistry actionHandlerRegistry;
    @Inject
    private ActionToolContextFactory factory;
    @Inject
    private Dialogs dialogs;
    @Inject
    private DynaformUtils utils;
    @Inject
    private ObjectCardPresenter objectCardPresenter;
    @Inject
    private ChangeTrackingUpdateService changeTrackingUpdateService;
    @Inject
    private MessageEditableAttributesFilterHandlerFactory messageEditableAttributesFilterHandlerFactory;

    //Тул, имитирующий реальный контролл для выполнения действия сразу после перехода
    //на карточку объекта. Используется для проверки прав. Настоящий контролл не используется 
    //т.к. необходимо отвязать логику выполнения быстрых действий от настроек карточки конкретного
    //метакласса
    private ActionTool fastTool;

    @Override
    protected AsyncProvider<ObjectCardPresenter> getProvider()
    {
        return new UserPlacePresenterAsyncProvider();
    }
}
