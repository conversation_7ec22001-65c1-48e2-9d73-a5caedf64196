package ru.naumen.dynaform.client.content;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.google.common.collect.ImmutableSet;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.user.client.rpc.AsyncCallback;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.forms.HasTabOrder;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.stash.BrowserValueStash;
import ru.naumen.core.client.stash.FormKey;
import ru.naumen.core.client.utils.RegistrationContainer;
import ru.naumen.core.client.utils.UnsavedObjectsHelper;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.Constants;
import ru.naumen.core.client.widgets.HasMassEditState;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueAsync;
import ru.naumen.core.client.widgets.MassEditState;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.datepicker.DateTextBoxWithPickerWidgetBase;
import ru.naumen.core.client.widgets.sourcecode.edit.SourceCodeEditWidget;
import ru.naumen.core.shared.Constants.AdvList;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.core.shared.userevents.UserEventParametersProperties;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.dynaform.client.ChildDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.AbstractFormContext;
import ru.naumen.dynaform.client.actioncommand.DefaultEditActionContext;
import ru.naumen.dynaform.client.actioncommand.FormContextDecorator;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;
import ru.naumen.dynaform.client.content.objectlist.listpresentation.extended.advlist.AdvListToolBarDynaContext;
import ru.naumen.dynaform.client.contextvariables.OriginUtils;
import ru.naumen.dynaform.client.customforms.CustomFormContext;
import ru.naumen.dynaform.client.customforms.propertycreator.AttrPropertyCreatorSimple;
import ru.naumen.dynaform.client.customforms.propertycreator.ObjectPropertyCustomizer;
import ru.naumen.dynaform.client.quickforms.PropertyActionsToolBarPresenter;
import ru.naumen.dynaform.client.quickforms.PropertyActionsToolBarPresenterFactory;
import ru.naumen.dynaform.client.quickforms.QuickFormPresenterBase;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditFormMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.CustomForm;
import ru.naumen.metainfo.shared.Constants.DateTimeAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;

/**
 * Объект класса предназначен для создания {@link Property свойств},
 * предназначененых для редактирования значений атрибутов объекта(ов).
 * Такие свойства обычно размещаются на разного рода формах редактирования.
 * Класс инкапсулирует особенности таких свойств:
 * <ul>
 * <li>Используется текущее {@link Attribute#getEditPresentation()} представление атрибута для редактирования</li>
 * <li>При создании свойства, на него вешаются обработчики событий изменения значения</li>
 * <li>При создании свойства, к нему привязываются валидаторы, определённые в представлении, кроме валидаторов
 * для обязательного заполнения значения атрибута</li>
 * <li>Для привязки валидаторов связанных с обязательностью заполнения
 * используется метод {@link #bindRequiredValidation(Attribute, Property)}</li>
 * </ul>
 * <AUTHOR>
 */
public class EditAttrPropertyCreator
{
    /**
     * Метаклассы, для которых берется родительский контекст для упоминаний.
     */
    private static final ImmutableSet<ClassFqn> MENTION_PARENT_CONTEXT_FQNS = ImmutableSet.of(Comment.FQN,
            File.FQN); //NOSONAR

    private final MassEditFormMessages massFormMessages;
    private final RegistrationContainer handlerRegistrationContainer;
    private final BrowserValueStash browserValueStash;
    private final PropertyActionsToolBarPresenterFactory quickToolBarFactory;
    private final UnsavedObjectsHelper unsavedObjectsHelper;
    private final AttrPropertyCreatorSimple attrPropertyCreatorSimple;
    private PropertyListContext context;
    private HasTabOrder tabOrderHolder;
    private PropertyListContext parentFormContext;
    private final List<PropertyActionsToolBarPresenter> quickToolBars = new ArrayList<>();
    private final ObjectPropertyCustomizer objectPropertyCustomizer;

    @Inject
    public EditAttrPropertyCreator(MassEditFormMessages massFormMessages,
            RegistrationContainer handlerRegistrationContainer, BrowserValueStash browserValueStash,
            PropertyActionsToolBarPresenterFactory quickToolBarFactory, UnsavedObjectsHelper unsavedObjectsHelper,
            AttrPropertyCreatorSimple attrPropertyCreatorSimple, ObjectPropertyCustomizer objectPropertyCustomizer)
    {
        this.massFormMessages = massFormMessages;
        this.handlerRegistrationContainer = handlerRegistrationContainer;
        this.browserValueStash = browserValueStash;
        this.quickToolBarFactory = quickToolBarFactory;
        this.unsavedObjectsHelper = unsavedObjectsHelper;
        this.attrPropertyCreatorSimple = attrPropertyCreatorSimple;
        this.objectPropertyCustomizer = objectPropertyCustomizer;
    }

    /**
     * Аналогично {@link Presenter#bind()}
     */
    public void bind()
    {
        attrPropertyCreatorSimple.clearPresentationContexts();
        handlerRegistrationContainer.removeAll();
        attrPropertyCreatorSimple.clearPropertyHandlers();
    }

    /**
     * Привязка валидатора, предназначенного для валидации
     * обязательности значения указанного свойства.
     * Объект валидатора извлекается из представления для редактирования указанного атрибута.
     *
     * @param attr метаинформация атрибута
     * @param property свойство формы
     */
    public ValidationUnit<Object> bindRequiredValidation(Attribute attr, Property<Object> property)
    {
        return bindRequiredValidation(attr, property, true);
    }

    public ValidationUnit<Object> bindRequiredValidation(Attribute attr, Property<Object> property,
            boolean showValidationMarker)
    {
        return attrPropertyCreatorSimple.bindRequiredValidation(attr, property, showValidationMarker);
    }

    public void clearDateTimeValidators()
    {
        attrPropertyCreatorSimple.clearDateTimeValidators();
    }

    /**
     * Основной метод создания свойства, размещаемого на форме редактирования.
     * Свойство предназначено для редактирования значения указанного атрибута.
     * @param attr метаинформация атрибута
     * @param callback объект обратного вызова
     */
    public void createProperty(Attribute attr, DtObject obj, boolean isContentVisible,
            AsyncCallback<Property<Object>> callback)
    {
        createProperty(new PresentationContext(attr, attr.getType(), obj)
                .setPresentation(attr.getEditPresentation())
                .setSourceFormObject(getSourceFormObject())
                .setContentCode(getContentCode())
                .setContentVisible(isContentVisible), callback);
    }

    public void createProperty(Attribute attr, @Nullable DtObject obj, Context context, boolean isContentVisible,
            @Nullable String originFormCode, AsyncCallback<Property<Object>> callback)
    {
        // @formatter:off
        PresentationContext ctx = new PresentationContext(attr, attr.getType(), obj)
                .setParentContext(context)
                .setSourceFormObject(getSourceFormObject())
                .setContentCode(getContentCode())
                .setPresentation(attr.getEditPresentation())
                .setContentVisible(isContentVisible)
                .setOrigin(OriginUtils.getOrigin(context));
        // @formatter:on

        ctx.setOriginFormCode(originFormCode);

        if (context instanceof MassEditActionContext) //NOSONAR
        {
            ctx.setMassEditParams(((MassEditActionContext)context).getAttributeCode(attr));
            Objects.requireNonNull(ctx.getObject()).setMetainfo(attr.getMetaClassLite().getFqn());
        }

        if (context instanceof CustomFormContext)
        {
            UserEventContext uec = (UserEventContext)((CustomFormContext)context).getEnvironmentContext();
            ctx.setUserEventParams(new UserEventParametersProperties(uec.getObjectUuids(),
                    uec.getObjectsOnAdvlistUuids(), uec.getObjectOnCardUuid(), uec.getEventSource().toString()));
        }

        ctx.setFilteredByScript(LinkAttributeUtils.isFiltered(attr));
        createProperty(ctx, callback);
    }

    public void createProperty(PresentationContext context, AsyncCallback<Property<Object>> callback)
    {
        Attribute attr = context.getAttribute();
        CommonUtils.assertAttrNotNull(attr);
        setOperationMentionContext(context);
        if (null != attrPropertyCreatorSimple.getContext())
        {
            context.getUnsavedObjects().clear();
            context.getUnsavedObjects().addAll(
                    unsavedObjectsHelper.extractUnsavedObjects(
                            attrPropertyCreatorSimple.getContext().getVisibleSourceObject(),
                            attrPropertyCreatorSimple.getContext().getVisibleQuickActions()));
        }
        attrPropertyCreatorSimple.createProperty(context, new BasicCallback<Property<Object>>()
        {
            @Override
            protected void handleSuccess(Property<Object> value)
            {
                customizeProperty(context, value);
                callback.onSuccess(value);
            }
        });
    }

    public void createPropertySimple(final PresentationContext context,
            AsyncCallback<HasProperties.Property<Object>> callback)
    {
        attrPropertyCreatorSimple.createPropertySimple(context, new BasicCallback<Property<Object>>()
        {
            @Override
            protected void handleSuccess(Property<Object> value)
            {
                customizeProperty(context, value);
                callback.onSuccess(value);
            }
        });
    }

    public PropertyListContext getParentFormContext()
    {
        return parentFormContext;
    }

    @CheckForNull
    public PresentationContext getPresentationContext(String attributeCode)
    {
        return attrPropertyCreatorSimple.getPresentationContext(attributeCode);
    }

    /**
     * Инициализация контекстными значениями
     * @param content контент формы
     * @param context контекст формы
     * @param validation процессор валидации
     */
    public void init(@Nullable Content content, PropertyListContext context, Processor validation,
            HasTabOrder tabOrderHolder)
    {
        this.context = context;
        this.parentFormContext = findPropertyListContext(context);
        this.tabOrderHolder = tabOrderHolder;
        browserValueStash.setFormKey(context.getContextProperty(Constants.FORM_KEY));
        attrPropertyCreatorSimple.init(context, content, validation);
    }

    public void clearBrowserValueStashKey()
    {
        browserValueStash.setFormKey(null);
        attrPropertyCreatorSimple.clearBrowserValueStashKey();
    }

    public void refreshContentVisibility(boolean isContentVisible)
    {
        attrPropertyCreatorSimple.refreshContentVisibility(isContentVisible);
    }

    public void registerProperties(PropertyRegistration<?> property)
    {
        handlerRegistrationContainer.register(property);
    }

    public void setRequiredValidation(Attribute attribute, Property<?> property, boolean enabled)
    {
        attrPropertyCreatorSimple.setRequiredValidation(attribute, property, enabled);
    }

    /**
     * Аналогично {@link Presenter#unbind()}
     */
    public void unbind()
    {
        quickToolBars.forEach(Presenter::unbind);
        quickToolBars.clear();
        attrPropertyCreatorSimple.clearPresentationContexts();
        handlerRegistrationContainer.unbindProperties();
        handlerRegistrationContainer.removeAll();
        attrPropertyCreatorSimple.clearDateTimeValidators();
        attrPropertyCreatorSimple.clearPropertyHandlers();
        browserValueStash.setFormKey(null);
    }

    public void unbindPropertyHandlers(HasProperties.Property<?> property)
    {
        attrPropertyCreatorSimple.unbindPropertyHandlers(property);
    }

    private void addQuickActions(Attribute attribute, Property<Object> property, PresentationContext prsContext)
    {
        if (!property.isEnabled())
        {
            return;
        }
        PropertyActionsToolBarPresenter toolBar = quickToolBarFactory.create(attribute, property, context,
                tabOrderHolder, prsContext, new BasicCallback<PropertyActionsToolBarPresenter>()
                {
                    @Override
                    protected void handleSuccess(PropertyActionsToolBarPresenter presenter)
                    {
                        if (property.getValueWidget() instanceof SourceCodeEditWidget)
                        {
                            presenter.getDisplay().asWidget()
                                    .addStyleName(WidgetResources.INSTANCE.form().sourceCodeToolBar());
                        }
                        if (context instanceof MassEditActionContext && context.getObjects().size() > 1)
                        {
                            initMassEditActions(property, attribute, presenter);
                        }
                    }
                });

        toolBar.bind();
        quickToolBars.add(toolBar);
        property.setToolPanelWidget(toolBar.getDisplay());
    }

    @Nullable
    private String getContentCode()
    {
        Context contentContext = context instanceof FormContextDecorator //NOSONAR
                ? ((FormContextDecorator)context).getAdaptee()
                : context;
        if (contentContext instanceof AbstractFormContext) //NOSONAR
        {
            return ((AbstractFormContext)contentContext).getContentCode();
        }
        return null;
    }

    private void customizeProperty(PresentationContext prsContext, Property<Object> property)
    {
        final Attribute attribute = prsContext.getAttribute();
        String caption = attribute == null || attribute.isHiddenAttrCaption() ? StringUtilities.EMPTY :
                attribute.getTitle();
        attrPropertyCreatorSimple.customizeProperty(property, caption,
                null == attribute ? StringUtilities.EMPTY : attribute.getCode());
        customizeForPlannedVersionMode(attribute, property);
        if (null == context)
        {
            return;
        }
        if (attribute != null && attribute.getType().getCode().equals(DateTimeAttributeType.CODE)
            && property.getValueWidget() instanceof DateTextBoxWithPickerWidgetBase<?>)
        {
            ((DateTextBoxWithPickerWidgetBase<?>)property.getValueWidget()).setAttrCode(attribute.getCode());
        }
        addQuickActions(attribute, property, prsContext);
    }

    private void customizeForPlannedVersionMode(@Nullable Attribute attribute, Property<Object> property)
    {
        if (objectPropertyCustomizer != null && attribute != null)
        {
            Content content = attrPropertyCreatorSimple.getContent();
            objectPropertyCustomizer.customizeLinkProperty(attribute, property, content);
        }
    }

    private static PropertyListContext findPropertyListContext(Context context)
    {
        if (context instanceof ChildDynaContext) //NOSONAR
        {
            Context parent = ContextUtils.getDecoratedContext(((ChildDynaContext)context).getParentContext());
            if (parent instanceof PropertyListContext) //NOSONAR
            {
                return (PropertyListContext)parent;
            }
            return findPropertyListContext(parent);
        }
        return null;
    }

    private static ClassFqn getClassFqnSafe(DtObject obj)
    {
        Object metaClassValue = obj.getProperty(ru.naumen.core.shared.Constants.AbstractBO.METACLASS);
        if (metaClassValue instanceof ClassFqn) //NOSONAR
        {
            return (ClassFqn)metaClassValue;
        }
        return null;
    }

    @Nullable
    private DtObject getSourceFormObject()
    {
        DynaContext parentContext = QuickFormPresenterBase.prepareParentContext(context.getParentContext());

        return null != parentContext && null != getParentFormContext() ? parentContext.getObject() : null;
    }

    /**
     * Добавляет инструменты для управления значениями атрибутов при массовом редактировании
     * @param property свойство, к которому добавляем инструменты
     * @param attribute соответствующий атрибут
     * @param presenter панель инструментов атрибута
     */
    private void initMassEditActions(Property<Object> property, Attribute attribute,
            PropertyActionsToolBarPresenter presenter)
    {
        MassEditActionContext massContext = (MassEditActionContext)context;
        String code = attrPropertyCreatorSimple.getAttrCode(attribute);

        presenter.addIconTool(massFormMessages.resetValue(), IconCodes.REFRESH, event ->
                        resetAttributeValue(property, attribute, presenter, massContext, code, false),
                prop -> massContext.getChangedValues().hasProperty(code));

        presenter.addIconTool(massFormMessages.clearValue(), IconCodes.CLEAR, event ->
        {
            browserValueStash.saveAttributeValue(attrPropertyCreatorSimple.getAttrCodeForBrowserStash(attribute), null);
            setValueAsync(property, null, new BasicCallback<Void>()
            {
                @Override
                protected void handleSuccess(Void value)
                {
                    massContext.getChangedValues().setProperty(code, property.getValue());
                    setRequiredValidation(attribute, property, true);
                    presenter.getDisplay().setToolVisible(IconCodes.REFRESH, true);
                    if (property.getValueWidget() instanceof HasMassEditState) //NOSONAR
                    {
                        ((HasMassEditState)property.getValueWidget()).setMassEditState(MassEditState.SINGLE_VALUE);
                    }
                    if (massContext.getObjects().size() > 1)
                    {
                        property.setAttentionMessage(massFormMessages.willBeSetToAllObjects());
                    }
                }
            });
        }, prop -> true);

        if (property.getValueWidget() instanceof HasValueAsync<?>)
        {
            Scheduler.get().scheduleDeferred(
                    () -> resetAttributeValue(property, attribute, presenter, massContext, code, true));
        }
    }

    private void resetAttributeValue(Property<Object> property, Attribute attribute,
            PropertyActionsToolBarPresenter presenter, MassEditActionContext massContext, String code,
            boolean isInitialReset)
    {
        Object initialValue = massContext.getInitialObject().getProperty(code);
        boolean isDifferent = CommonUtils.isDifferentValuesObject(initialValue);
        final FormKey savedKey = browserValueStash.getFormKey();

        if (isInitialReset)
        {
            browserValueStash.setFormKey(null);
        }

        setValueAsync(property, isDifferent ? null : initialValue, new BasicCallback<Void>()
        {
            @Override
            protected void handleSuccess(Void value)
            {
                Objects.requireNonNull(context.getObject()).setProperty(code, property.getValue());
                if (property.getValueWidget() instanceof HasMassEditState)
                {
                    HasMassEditState widget = property.getValueWidget();
                    if (isDifferent)
                    {
                        widget.setMassEditState(MassEditState.DIFFERENT_VALUES);
                    }
                    else if (property.isValueEmpty())
                    {
                        widget.setMassEditState(MassEditState.NOT_SET);
                    }
                    else
                    {
                        widget.setMassEditState(MassEditState.SINGLE_VALUE);
                    }
                }
                massContext.getChangedValues().removeProperty(code);
                setRequiredValidation(attribute, property, false);
                property.setAttentionMessage(StringUtilities.EMPTY);
                presenter.getDisplay().setToolVisible(IconCodes.REFRESH, false);

                if (isInitialReset)
                {
                    browserValueStash.setFormKey(savedKey);
                }
                else
                {
                    browserValueStash.clearAttributeValue(attrPropertyCreatorSimple.getAttrCodeForBrowserStash(
                            attribute));
                }
            }
        });
    }

    private void setOperationMentionContext(PresentationContext ctx)
    {
        DynaContext dynaContext = attrPropertyCreatorSimple.getContext();
        if (dynaContext.getObject() != null
            && MENTION_PARENT_CONTEXT_FQNS.contains(getClassFqnSafe(dynaContext.getObject()))
            && dynaContext instanceof ChildDynaContext) //NOSONAR
        {
            dynaContext = ((ChildDynaContext)dynaContext).getParentContext();
            if (dynaContext.getObject() != null
                && MENTION_PARENT_CONTEXT_FQNS.contains(getClassFqnSafe(dynaContext.getObject()))
                && dynaContext instanceof ChildDynaContext) //NOSONAR
            {
                // При редактировании комментария нужно подняться еще на уровень выше
                dynaContext = ((ChildDynaContext)dynaContext).getParentContext();
            }
        }

        ClassFqn mentionContextFqn = dynaContext.getObject() == null || getClassFqnSafe(dynaContext.getObject()) == null
                ? dynaContext.getMetainfo().getFqn()
                : getClassFqnSafe(dynaContext.getObject());

        if ((dynaContext instanceof ChildDynaContext //NOSONAR
             && ((ChildDynaContext)dynaContext).getParentContext() instanceof AdvListToolBarDynaContext
             || dynaContext.getObjects().size() > 1) && mentionContextFqn != null
            && !(dynaContext instanceof DefaultEditActionContext //NOSONAR
                 && ((DefaultEditActionContext)dynaContext).getForm() instanceof QuickForm))
        {
            // Для массовых операций всегда используем класс
            mentionContextFqn = mentionContextFqn.fqnOfClass();
        }

        // Для пользовательских форм ДПС
        if (mentionContextFqn != null && CustomForm.FQN.isSameClass(mentionContextFqn)
            && dynaContext instanceof ChildDynaContext) //NOSONAR
        {
            mentionContextFqn = ((ChildDynaContext)dynaContext).getParentContext().getMetainfo().getFqn();
        }

        if (ctx.getObject() != null)
        {
            ctx.getObject().setProperty(AdvList.MASS_OPERATION_MENTION_CONTEXT_FQN, mentionContextFqn); //NOSONAR
        }
    }

    private static void setValueAsync(Property<Object> property, @Nullable Object value, AsyncCallback<Void> callback)
    {
        if (property.getValueWidget() instanceof HasValueAsync<?>)
        {
            HasValueAsync<Object> widget = property.getValueWidget();
            widget.setValueAsync(value, callback);
        }
        else
        {
            if (null == value)
            {
                property.clearValue();
            }
            else
            {
                SelectListPropertyValueExtractor.setValue(property, value, true);
            }
            callback.onSuccess(null);
        }
    }
}