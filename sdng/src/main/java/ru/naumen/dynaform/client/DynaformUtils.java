package ru.naumen.dynaform.client;

import static ru.naumen.commons.shared.utils.StringUtilities.capitalizeFirstLetter;
import static ru.naumen.core.shared.Constants.*;
import static ru.naumen.core.shared.Constants.AbstractBO.REMOVED;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.http.client.URL;
import com.google.gwt.user.client.Cookies;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.dispatch.ObjectNotExistException;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Общие утилитарные методы для ИО
 * <AUTHOR>
 */
@Singleton
public class DynaformUtils
{
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private CommonMessages messages;
    @Inject
    private UserMessages umessages;

    public void checkCookie() throws ObjectNotExistException
    {
        //Если установлено cookie, сигнализирующая о том, что была попытка скачать несуществующий файл, то показываем
        // соответствующий диалог.
        if (Cookies.getCookie(FILE_NOT_EXIST_COOKIE) != null)
        {
            String fileUUID = Cookies.getCookie(FILE_NOT_EXIST_COOKIE);
            Cookies.removeCookieNative(FILE_NOT_EXIST_COOKIE, "/");
            throw new ObjectNotExistException(umessages.fileNotFoundUserMessage(fileUUID), fileUUID);
        }
        else if (Cookies.getCookie(FILE_CONTENT_NOT_FOUND) != null)
        {
            String errorMessage = URL.decodeQueryString(Cookies.getCookie(FILE_CONTENT_NOT_FOUND));
            Cookies.removeCookieNative(FILE_CONTENT_NOT_FOUND, "/");
            throw new ObjectNotExistException(errorMessage, StringUtilities.EMPTY);
        }
        else if (Cookies.getCookie(FILE_STORAGE_NOT_AVAILABLE) != null)
        {
            Cookies.removeCookieNative(FILE_STORAGE_NOT_AVAILABLE, "/");
            throw new ObjectNotExistException(umessages.fileStorageNotAvailable(), StringUtilities.EMPTY);
        }
        else if (Cookies.getCookie(FILE_BLOCK_DOWNLOADING) != null)
        {
            String errorMessage = URL.decodeQueryString(Cookies.getCookie(FILE_BLOCK_DOWNLOADING));
            Cookies.removeCookieNative(FILE_BLOCK_DOWNLOADING, "/");
            throw new ObjectNotExistException(errorMessage, StringUtilities.EMPTY);
        }
    }

    /**
     * Метод предназначен для получения заголовка карточки объекта
     * с учетом его возможного переопределения
     *
     * @param dto объект
     * @return заголовок карточки объекта
     */
    public String getObjectCardCaption(DtObject dto)
    {
        String cardCaption = dto.getProperty(AbstractBO.CARD_CAPTION);
        return StringUtilities.isEmpty(cardCaption) ? getObjectTypeTitle(dto) : cardCaption;
    }

    /**
     * Метод предназначен для получения заголовка карточки объекта
     * с учетом его возможного переопределения
     *
     * @param dto объект
     * @param fqn идентификатор метакласса
     * @return заголовок карточки объекта
     */
    public String getObjectCardCaption(DtObject dto, @Nullable ClassFqn fqn)
    {
        String cardCaption = dto.getProperty(AbstractBO.CARD_CAPTION);
        return StringUtilities.isEmpty(cardCaption) ? getObjectTypeTitle(dto, fqn) : cardCaption;
    }

    private String getObjectTypeTitle(DtObject dto)
    {
        return getObjectTypeTitle(dto, dto.getMetaClass());
    }

    private String getObjectTypeTitle(DtObject dto, @Nullable ClassFqn fqn)
    {
        String objTitle = dto.getTitle();
        fqn = dto.getMetaClass() != null ? dto.getMetaClass() : fqn;
        return fqn == null || Root.FQN.equals(fqn) ? objTitle : metainfoService.getMetaClassTitle(fqn) + " \""
                                                                + objTitle + "\"";
    }

    /**
     * Приписывает к заголовку объекта признак архивности в случае если объект
     * находится в архиве
     *
     * @param cardCaption - заголовок
     * @param dto - объект
     */
    public String prefixIfRemoved(String cardCaption, DtObject dto)
    {
        if (Boolean.TRUE.equals(dto.getProperty(REMOVED)))
        {
            return "(" + capitalizeFirstLetter(messages.arch()) + ") " + cardCaption;
        }
        return cardCaption;
    }
}
