package ru.naumen.dynaform.client.content;

import java.util.Collection;
import java.util.Objects;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryEdit;
import ru.naumen.core.client.content.property.PropertyGridDisplay;
import ru.naumen.core.client.events.PossibleCasesChangedEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.properties.PropertyUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.dynaform.client.DynaformSelectCaseUtils;
import ru.naumen.dynaform.client.events.CaseChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.RefreshContentsOnAddFormEvent;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.SelectCase;

/**
 * Презентер контента "Выбор типа объекта"
 * <AUTHOR>
 * @since 26 сент. 2013 г.
 */
public class SelectCaseContentPresenter extends AbstractValidationContentPresenter<PropertyGridDisplay, SelectCase>
{
    @Inject
    private WidgetResources resources;
    @Inject
    private PresentationFactories prsFactories;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    private MetainfoServiceSync metainfoServiceSync;
    @Inject
    private NotEmptyObjectValidator<DtObject> notEmptyCaseValidator;

    private Property<DtObject> property;

    @Inject
    public SelectCaseContentPresenter(PropertyGridDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "SelectCase");
    }

    @Override
    public void onRefresh(RefreshContentsOnAddFormEvent e)
    {
        if (property != null)
        {
            Attribute caseAttr = getCaseAttr();
            property.setCaption(getCaptionAttr(caseAttr));
        }
    }

    /**
     * Презентер не реализует CaseChangedHandler,
     * т.к. в случае изменения типа объекта вся форма будет перестраиваться,
     * и будет создаваться новая копия этого презентера
     */
    @Override
    protected void onBind()
    {
        super.onBind();

        updateDisplayVisibility();

        Attribute caseAttr = getCaseAttr();
        DtObject object = Objects.requireNonNull(context.getObject());
        String editPrsCode = (String)object.get(Constants.CASE_PRESENT_CODE);
        editPrsCode = editPrsCode != null ? editPrsCode : caseAttr.getEditPresentation().getCode();
        PresentationFactoryEdit<DtObject> editPrsFactory = prsFactories.getEditPresentationFactory(editPrsCode);
        PresentationContext presentationContext = new PresentationContext(caseAttr, caseAttr.getType(),
                context.getObject());
        presentationContext.setPermittedTypeFqns(context.getPossibleCases());
        presentationContext.setParentContext(context);
        // Переложить тип объекта в свойство контекста, чтоб можно было достать из presentationContext
        context.setContextProperty(Constants.CASE_PRESENT_VALUE, object.getMetainfo());

        editPrsFactory.createWidget(presentationContext, new BasicCallback<HasValueOrThrow<DtObject>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<DtObject> widget)
            {
                property = propertyCreator.create(messages.metaCase(), widget);
                property.setCode(caseAttr.getCode());
                property.setValidationMarker(true);
                validation.validate(property, notEmptyCaseValidator);
                if(property.getValueWidget() instanceof SingleSelectCellList<?>)
                {
                    property.<SingleSelectCellList<?>> getValueWidget().setHasSearchLite(true);
                }
                DebugIdBuilder.ensureDebugId(property, "caseProperty");

                MetaClass value = object.getMetaClass() == null ? null
                        : metainfoServiceSync.getMetaClass(object.getMetaClass());
                if (value != null && value.getFqn().isCase())
                {
                    SimpleDtObject item = new SimpleDtObject(value.getFqn().asString(), value.getTitle(), value
                            .getFqn());
                    property.setValue(new SelectItem(item));
                }
                property.setCaption(getCaptionAttr(caseAttr));
                PropertyUtils.setCaptionDescription(property, content, caseAttr.getDescription());

                registerHandler(property.addValueChangeHandler(event ->
                {
                    ClassFqn newValue = event.getValue() == null
                            ? context.getDefaultMetaClass().getFqn()
                            : event.getValue().getMetaClass();

                    eventBus.fireEvent(new CaseChangedEvent(newValue));
                    DtObject object = getContext().getObject();
                    CommonUtils.assertObjectNotNull(object);
                    object.setProperty(Constants.AbstractBO.METACLASS, newValue);
                    FieldChangedEvent changeEvent = new FieldChangedEvent(
                            getContext().getMetainfo().getAttribute(Constants.AbstractBO.METACLASS),
                            getContext(), getContent());
                    eventBus.fireEvent(changeEvent);
                    getContext().getEventBus().fireEvent(changeEvent);
                }));
                getDisplay().add(property);
            }
        });
        registerHandler(eventBus.addHandler(RefreshContentsOnAddFormEvent.getType(), this));

        registerHandler(getContext().getEventBus().addHandler(PossibleCasesChangedEvent.getType(), e ->
                updateDisplayVisibility(e.getPossibleCases())));

        registerHandler(getContext().getEventBus().addHandler(FieldChangedEvent.getType(), e ->
                DynaformSelectCaseUtils.handleFieldChangedEvent(presentationContext, caseAttr, property, e)));
    }

    private void updateDisplayVisibility()
    {
        updateDisplayVisibility(context.getPossibleCases());
    }

    private void updateDisplayVisibility(Collection<? extends ClassFqn> possibleCases)
    {
        // На данный момент решено, что контент нужно скрыть, только если для выбора доступен 1 тип.
        // Если больше 1 или 0 - контент нужно отобразить
        getDisplay().asWidget().setStyleName(resources.additional().displayNone(), possibleCases.size() == 1);
    }

    private Attribute getCaseAttr()
    {
        MetaClass presentMetaclass = metainfoServiceSync.getMetaClass(context.getMetainfo().getFqn());
        return presentMetaclass.getAttribute(Constants.AbstractBO.METACLASS);
    }

    private static String getCaptionAttr(Attribute caseAttr)
    {
        return Boolean.TRUE.equals(caseAttr.isHiddenAttrCaption()) ? StringUtilities.EMPTY : caseAttr.getTitle();
    }
}