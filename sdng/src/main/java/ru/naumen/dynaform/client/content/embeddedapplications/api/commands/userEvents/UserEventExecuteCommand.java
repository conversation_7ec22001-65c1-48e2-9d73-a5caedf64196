package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.userEvents;

import static ru.naumen.core.shared.Constants.EmbeddedApplicationCommand.UUID;

import java.util.ArrayList;
import java.util.Objects;

import jakarta.inject.Inject;

import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONString;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.ActionToolContextFactory;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.userevents.EventSyncType;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.dynaform.client.FormPresentersSplitPoint;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandBase;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;
import ru.naumen.core.client.events.EventFireFrom;
import ru.naumen.dynaform.client.toolbar.multi.event.UserEventActionProcessor;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.metainfo.shared.ui.UserEventTool;

/**
 * Команда для выполнения пользовательского действия по событию, из встроенного приложения.
 * <AUTHOR>
 * @since 24.11.2022
 */
public class UserEventExecuteCommand extends JsApiCommandBase
{
    /**
     * Объекты контекста выполнения ПДПС
     */
    private static final String SUBJECTS_UUIDS = "subjectsUuids";
    private static final String EVENT_ACTION_TYPE = "eventActionType";

    private final UserEventActionProcessor userEventActionProcessor;
    private final ActionToolContextFactory actionToolContextFactory;
    private final SplitPointService splitPointService;
    private final ILocaleInfo localeInfo;

    @Inject
    public UserEventExecuteCommand(
            UserEventActionProcessor userEventActionProcessor,
            ActionToolContextFactory actionToolContextFactory,
            SplitPointService splitPointService,
            ILocaleInfo localeInfo)
    {
        this.userEventActionProcessor = userEventActionProcessor;
        this.actionToolContextFactory = actionToolContextFactory;
        this.splitPointService = splitPointService;
        this.localeInfo = localeInfo;
    }

    @Override
    public JsApiCommandType getType()
    {
        return JsApiCommandType.USER_EVENT_EXECUTE;
    }

    @Override
    protected void perform(JSONObject parameters)
    {
        final String eventUuid = Objects.requireNonNull(parameters.get(UUID).isString()).stringValue();

        final JSONArray subjects = Objects.requireNonNull(parameters.get(SUBJECTS_UUIDS).isArray());
        final ArrayList<String> subjectsUuids = new ArrayList<>();
        for (int i = 0; i < subjects.size(); i++)
        {
            subjectsUuids.add(subjects.get(i).isString().stringValue());
        }

        final UserEventTool userEventTool = new UserEventTool();
        userEventTool.setUseQuickForm(false);
        userEventTool.setEventUuid(eventUuid);
        userEventTool.setAppliedToType(AppliedToType.CURRENT_OBJECT);

        final String applicationTitle = ((EmbeddedApplicationContent)context.getParentContent()).getApplication();
        userEventTool.getCaption().add(new LocalizedString(localeInfo.getCurrentLang(), applicationTitle));

        final ActionToolContext actionToolContext = actionToolContextFactory.create(userEventTool,
                Objects.requireNonNull(context.getParentContext()),
                null, Constants.FIRE_USER_EVENT, EventFireFrom.EMBEDDED_APPLICATION);

        final ErrorAndAttentionMessageHandler errorHandler =
                Objects.requireNonNull(context.getErrorAndAttentionMsgHandler());

        BasicCallback<EventSyncType> eventFireCallback = new BasicCallback<EventSyncType>()
        {
            @Override
            protected void handleFailure(Throwable t)
            {
                if (t instanceof InterruptedException)
                {
                    cancelCommand();
                    return;
                }
                super.handleFailure(t);
            }

            @Override
            protected void handleSuccess(EventSyncType eventSyncType)
            {
                JSONObject response = new JSONObject();
                response.put(EVENT_ACTION_TYPE, new JSONString(eventSyncType.getName()));
                sendCommandResponse(response);
            }
        }.setErrorMessageHandler(errorHandler);

        splitPointService.inject(FormPresentersSplitPoint.class, new BasicCallback<FormPresentersSplitPoint>()
        {
            @Override
            public void onSuccess(FormPresentersSplitPoint result)
            {
                userEventActionProcessor.fireUserEvent(actionToolContext, subjectsUuids, eventUuid, null,
                        new MapProperties(), EventFireFrom.EMBEDDED_APPLICATION, eventFireCallback);
            }
        }.setErrorMessageHandler(errorHandler));
    }
}