package ru.naumen.dynaform.client.content.factory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import com.google.common.base.Preconditions;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.content.factory.ContentFactoryInner;
import ru.naumen.core.client.content.factory.DefaultContentFactory;
import ru.naumen.core.client.content.toolbar.ActionToolFactory;
import ru.naumen.core.client.content.toolbar.ChangeStateToolFactory;
import ru.naumen.core.client.inject.splitpoint.SplitPoint;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformContentFactory;
import ru.naumen.dynaform.client.OperatorSplitPointsInitializer;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.content.objectlist.ObjectListDynaSplitPoint;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddDeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.ChangeStateTool;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.ClientInfo;
import ru.naumen.metainfo.shared.ui.CommentEditablePropertyList;
import ru.naumen.metainfo.shared.ui.CommentInlineForm;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.CompactForm;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.DeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.EditTool;
import ru.naumen.metainfo.shared.ui.EditablePropertyContent;
import ru.naumen.metainfo.shared.ui.EditablePropertyList;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.EventList;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.MassProblems;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfo.shared.ui.SelectClient;
import ru.naumen.metainfo.shared.ui.SelectContacts;
import ru.naumen.metainfo.shared.ui.SelectParent;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfo.shared.ui.UserHistoryList;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfo.shared.ui.WorkflowContent;

/**
 * <AUTHOR>
 * @since Dec 7, 2015
 */
@Singleton
public class ContentFactoryInitializer
{
    @Singleton
    public static class CardContentPresenterFactories implements ContentPresenterFactories, SplitPoint
    {
        public HashMap<Class<? extends Content>, Provider<? extends ContentFactoryInner<?, ?>>> providers =
                new HashMap<>();

        @Inject
        //@formatter:off
        public CardContentPresenterFactories(
                Provider<ActionToolFactory<ActionTool, DynaContext>> actionTool, 
                Provider<ChangeStateToolFactory<DynaContext>> changeState,
                Provider<RelObjPropertyListFactory> relObjectPropertyList,
                Provider<TabBarFactory> tabBar,
                Provider<DefaultContentFactory<ClientInfo, DynaContext>> clientInfo,
                Provider<DefaultContentFactory<ObjectList, DynaContext>> objectList,
                Provider<DefaultContentFactory<ChildObjectList, DynaContext>> childObjectList,
                Provider<CommentListFactory> commentList,
                Provider<DefaultContentFactory<EventList, DynaContext>> eventList,
                Provider<FileListFactory> fileList,
                Provider<DefaultContentFactory<Layout, DynaContext>> layout, 
                Provider<DefaultContentFactory<PropertyList, DynaContext>> propertyList,
                Provider<DefaultContentFactory<RelObjectList, DynaContext>> relObjectList,
                Provider<DefaultContentFactory<ToolBar, DynaContext>> toolBar,
                Provider<DefaultContentFactory<ToolPanel, DynaContext>> toolPanel,
                Provider<DefaultContentFactory<Window, DynaContext>> window,
                Provider<DefaultContentFactory<WorkflowContent, DynaContext>> workflowContent,
                Provider<DefaultContentFactory<UserHistoryList, DynaContext>> userHistoryList,
                Provider<DefaultContentFactory<EmbeddedApplicationContent, DynaContext>> embeddedApplicationContent,
                Provider<DefaultContentFactory<HierarchyGrid, DynaContext>> hierarchyGridContent)
        //@formatter:on
        {
            providers.put(ActionTool.class, actionTool);
            providers.put(UserEventTool.class, actionTool);
            providers.put(EditTool.class, actionTool);
            providers.put(AddFileTool.class, actionTool);
            providers.put(AddDeleteFromObjectListTool.class, actionTool);
            providers.put(ChangeStateTool.class, changeState);
            providers.put(RelObjPropertyList.class, relObjectPropertyList);
            providers.put(TabBar.class, tabBar);
            providers.put(ClientInfo.class, clientInfo);
            providers.put(ChildObjectList.class, childObjectList);
            providers.put(ObjectList.class, objectList);
            providers.put(CommentList.class, commentList);
            providers.put(EventList.class, eventList);
            providers.put(FileList.class, fileList);
            providers.put(Layout.class, layout);
            providers.put(PropertyList.class, propertyList);
            providers.put(RelObjectList.class, relObjectList);
            providers.put(ToolBar.class, toolBar);
            providers.put(ToolPanel.class, toolPanel);
            providers.put(Window.class, window);
            providers.put(WorkflowContent.class, workflowContent);
            providers.put(EmbeddedApplicationContent.class, embeddedApplicationContent);
            providers.put(UserHistoryList.class, userHistoryList);
            providers.put(HierarchyGrid.class, hierarchyGridContent);
        }

        @Override
        public Collection<Class<?>> getDependencies()
        {
            return Collections.emptyList();
        }

        @SuppressWarnings("unchecked")
        @Override
        public <T extends ContentFactoryInner<?, ?>> Provider<T> getFactory(Class<? extends Content> contentClass)
        {
            return (Provider<T>)providers.get(contentClass);
        }
    }

    @Singleton
    public static class FormContentPresenterFactories implements ContentPresenterFactories, SplitPoint
    {
        public HashMap<Class<? extends Content>, Provider<? extends ContentFactoryInner<?, ?>>> providers =
                new HashMap<>();

        @Inject
        //@formatter:off
        public FormContentPresenterFactories(
                Provider<FormFactory> form,
                Provider<DefaultContentFactory<MassProblems, FormContext>> massProblems,
                Provider<DefaultContentFactory<EditablePropertyList, FormContext>> editablePropertyList,
                // ТОDO dzevako Убрать эту зависимость отсюда, т.к. это не контент в NSDPRD-12486 Рефакторинг форм
                Provider<DefaultContentFactory<EditablePropertyContent, FormContext>> editablePropertyContent,
                Provider<DefaultContentFactory<SelectCase, FormContext>> selectCase,
                Provider<DefaultContentFactory<SelectClient, FormContext>> selectClient,
                Provider<DefaultContentFactory<SelectContacts, FormContext>> selectContacts,
                Provider<DefaultContentFactory<SelectParent, FormContext>> selectParent,
                Provider<DefaultContentFactory<CommentEditablePropertyList, FormContext>> commentPropertyList)
        //@formatter:on
        {
            providers.put(Form.class, form);
            providers.put(MassProblems.class, massProblems);
            providers.put(EditablePropertyList.class, editablePropertyList);
            providers.put(EditablePropertyContent.class, editablePropertyContent);
            providers.put(CommentEditablePropertyList.class, commentPropertyList);
            providers.put(SelectCase.class, selectCase);
            providers.put(SelectClient.class, selectClient);
            providers.put(SelectContacts.class, selectContacts);
            providers.put(SelectParent.class, selectParent);
        }

        @Override
        public Collection<Class<?>> getDependencies()
        {
            return Collections.emptyList();
        }

        @SuppressWarnings("unchecked")
        @Override
        public <T extends ContentFactoryInner<?, ?>> Provider<T> getFactory(Class<? extends Content> contentClass)
        {
            return (Provider<T>)providers.get(contentClass);
        }
    }

    private interface ContentPresenterFactories
    {
        <T extends ContentFactoryInner<?, ?>> Provider<T> getFactory(Class<? extends Content> contentClass);
    }

    private class ContentProvider<T extends ContentFactoryInner<?, ?>> implements AsyncProvider<T>
    {

        private Class<? extends Content> contentClass;
        private AsyncProvider<? extends ContentPresenterFactories> factories;
        private Class<?>[] splitPoints = new Class<?>[] {};

        @Override
        public void get(final AsyncCallback<? super T> callback)
        {
            factories.get(new BasicCallback<ContentPresenterFactories>()
            {
                @Override
                protected void handleSuccess(ContentPresenterFactories factories)
                {
                    Provider<T> provider = factories.getFactory(contentClass);
                    Preconditions.checkNotNull(provider, "Factory not registered for %s", contentClass);

                    final T contentFactory = provider.get();

                    if (splitPoints.length == 0)
                    {
                        callback.onSuccess(contentFactory);
                    }
                    else
                    {
                        splitPointService.inject(splitPoints, new BasicCallback<ArrayList<SplitPoint>>()
                        {
                            @Override
                            protected void handleSuccess(ArrayList<SplitPoint> value)
                            {
                                callback.onSuccess(contentFactory);
                            }
                        });
                    }
                }
            });
        }
    }

    @Inject
    private AsyncProvider<CardContentPresenterFactories> contentPresenters;
    @Inject
    private AsyncProvider<FormContentPresenterFactories> formPresenters;
    @Inject
    private SplitPointService splitPointService;

    @Inject
    public void initContentFactory(DynaformContentFactory factory)
    {
        //@formatter:off
        factory.register(ActionTool.class, this.<ActionToolFactory<ActionTool, DynaContext>> card(ActionTool.class));
        factory.register(UserEventTool.class,this.<ActionToolFactory<ActionTool, DynaContext>> card(UserEventTool.class));
        factory.register(EditTool.class,this.<ActionToolFactory<ActionTool, DynaContext>> card(EditTool.class));
        factory.register(AddFileTool.class,this.<ActionToolFactory<ActionTool, DynaContext>> card(AddFileTool.class));
        factory.register(AddDeleteFromObjectListTool.class,this.<ActionToolFactory<ActionTool, DynaContext>> card(AddDeleteFromObjectListTool.class));
        factory.register(DeleteFromObjectListTool.class,this.<ActionToolFactory<ActionTool, DynaContext>> card(DeleteFromObjectListTool.class));
        factory.register(ChangeStateTool.class, this.<ChangeStateToolFactory<DynaContext>> card(ChangeStateTool.class));
        factory.register(RelObjPropertyList.class, this.<RelObjPropertyListFactory> card(RelObjPropertyList.class));
        factory.register(FileList.class, this.<FileListFactory> card(FileList.class));
        factory.register(TabBar.class, this.<TabBarFactory> card(TabBar.class));

        factory.register(ObjectList.class, this.<DefaultContentFactory<ObjectList, DynaContext>> card(ObjectList.class, ObjectListDynaSplitPoint.class));
        factory.register(RelObjectList.class, this.<DefaultContentFactory<RelObjectList, DynaContext>> card(RelObjectList.class, ObjectListDynaSplitPoint.class));
        factory.register(ChildObjectList.class, this.<DefaultContentFactory<ChildObjectList, DynaContext>> card(ChildObjectList.class, ObjectListDynaSplitPoint.class));
        factory.register(CommentList.class, this.<DefaultContentFactory<CommentList, DynaContext>> card(CommentList.class, ObjectListDynaSplitPoint.class));
        factory.register(EventList.class, this.<DefaultContentFactory<EventList, DynaContext>> card(EventList.class, ObjectListDynaSplitPoint.class));
        factory.register(UserHistoryList.class, this.<DefaultContentFactory<UserHistoryList, DynaContext>> card(UserHistoryList.class, ObjectListDynaSplitPoint.class));
        factory.register(FileList.class, this.<DefaultContentFactory<FileList, DynaContext>> card(FileList.class, ObjectListDynaSplitPoint.class));
        factory.register(ClientInfo.class, this.<DefaultContentFactory<ClientInfo, DynaContext>> card(ClientInfo.class));
        factory.register(Layout.class, this.<DefaultContentFactory<Layout, DynaContext>> card(Layout.class));
        factory.register(PropertyList.class, this.<DefaultContentFactory<PropertyList, DynaContext>> card(PropertyList.class));
        factory.register(ToolBar.class, this.<DefaultContentFactory<ToolBar, DynaContext>> card(ToolBar.class));
        factory.register(ToolPanel.class, this.<DefaultContentFactory<ToolPanel, DynaContext>> card(ToolPanel.class));
        factory.register(Window.class, this.<DefaultContentFactory<Window, DynaContext>> card(Window.class));
        factory.register(WorkflowContent.class, this.<DefaultContentFactory<WorkflowContent, DynaContext>> card(WorkflowContent.class));
        factory.register(EmbeddedApplicationContent.class, this.<DefaultContentFactory<EmbeddedApplicationContent, DynaContext>> card(EmbeddedApplicationContent.class));
        factory.register(HierarchyGrid.class, this.<DefaultContentFactory<HierarchyGrid, DynaContext>> card(HierarchyGrid.class));

        factory.register(Form.class, form(Form.class, OperatorSplitPointsInitializer.FORM_SPLIT_POINTS));
        factory.register(MassProblems.class, this.<DefaultContentFactory<MassProblems, FormContext>> form(MassProblems.class, ObjectListDynaSplitPoint.class));
        factory.register(EditablePropertyList.class, this.<DefaultContentFactory<EditablePropertyList, FormContext>> form(EditablePropertyList.class));
        factory.register(CompactForm.class, this.<FormFactory> form(Form.class, OperatorSplitPointsInitializer.FORM_SPLIT_POINTS));
        factory.register(CommentInlineForm.class, this.<FormFactory> form(Form.class, OperatorSplitPointsInitializer.FORM_SPLIT_POINTS));
        factory.register(EditablePropertyContent.class, this.<DefaultContentFactory<EditablePropertyContent, FormContext>> form(EditablePropertyContent.class));
        factory.register(CommentEditablePropertyList.class, this.<DefaultContentFactory<CommentEditablePropertyList, FormContext>> form(CommentEditablePropertyList.class));
        factory.register(SelectCase.class, this.<DefaultContentFactory<SelectCase, FormContext>> form(SelectCase.class));
        factory.register(SelectClient.class, this.<DefaultContentFactory<SelectClient, FormContext>> form(SelectClient.class));
        factory.register(SelectContacts.class, this.<DefaultContentFactory<SelectContacts, FormContext>> form(SelectContacts.class));
        factory.register(SelectParent.class, this.<DefaultContentFactory<SelectParent, FormContext>> form(SelectParent.class));        
        //@formatter:on
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> card(Class<? extends Content> contentClass)
    {
        return card(contentClass, new Class<?>[] {});
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> card(Class<? extends Content> contentClass,
            Class<?> splitPoint)
    {
        return card(contentClass, new Class<?>[] { splitPoint });
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> card(Class<? extends Content> contentClass,
            Class<?>[] splitPoints)
    {
        ContentProvider<T> result = new ContentProvider<>();
        result.contentClass = contentClass;
        result.factories = contentPresenters;
        result.splitPoints = splitPoints;
        return result;
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> form(Class<? extends Content> contentClass)
    {
        return form(contentClass, new Class<?>[] {});
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> form(Class<? extends Content> contentClass,
            Class<?> splitPoint)
    {
        return form(contentClass, new Class<?>[] { splitPoint });
    }

    private <T extends ContentFactoryInner<?, ?>> ContentProvider<T> form(Class<? extends Content> contentClass,
            Class<?>[] splitPoints)
    {
        ContentProvider<T> result = new ContentProvider<>();
        result.contentClass = contentClass;
        result.factories = formPresenters;
        result.splitPoints = splitPoints;
        return result;
    }
}
