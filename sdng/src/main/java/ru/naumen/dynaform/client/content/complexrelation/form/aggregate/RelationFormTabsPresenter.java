package ru.naumen.dynaform.client.content.complexrelation.form.aggregate;

import static ru.naumen.metainfo.shared.elements.AttributeDescription.REF_METACLASS_EXTRACTOR;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.AbstractTabPresenter;
import ru.naumen.core.client.forms.FormTabDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;

/**
 * Реализация {@link AbstractTabPresenter} для панели вкладок на сложной форме добавления
 * связи для атгрегирующих атрибутов
 *
 * <AUTHOR>
 * @since 05 февр. 2016 г.
 */
public class RelationFormTabsPresenter extends AbstractTabPresenter<FormTabDisplay>
{
    @Inject
    private MetainfoServiceSync metainfoService;
    @Inject
    private Provider<OuEmployeeTabPresenter> ousTabPresenterProvider;
    @Inject
    private Provider<TeamEmployeeTabPresenter> teamsTabPresenterProvider;

    private AggrRelationFormContext context;

    @Inject
    public RelationFormTabsPresenter(FormTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    /**
     * Инициализировать презентер
     *
     * @param context - контекст формы
     */
    public void init(AggrRelationFormContext context)
    {
        this.context = context;
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        AggregateAttributeType attrType = context.getPrsContext().getAttributeType().cast();
        Set<ClassFqn> classes = attrType.getAttributes().stream()
                .map(REF_METACLASS_EXTRACTOR).collect(Collectors.toSet());
        Map<ClassFqn, String> attrGroups = attrType.getComplexRelationAttrGroups();

        boolean isOuTabExists = classes.contains(OU.FQN)
                                && (attrGroups.get(OU.FQN) != null || attrGroups.get(Employee.FQN) != null);
        boolean isTeamTabExists = classes.contains(Team.FQN)
                                  && (attrGroups.get(Team.FQN) != null
                                      || !classes.contains(OU.FQN) && attrGroups.get(Employee.FQN) != null);

        getDisplay().setTabBarVisible(isOuTabExists && isTeamTabExists);

        if (isOuTabExists)
        {
            OuEmployeeTabPresenter ouTab = ousTabPresenterProvider.get();
            ouTab.init(context);
            String tabTitle = null == attrType.getComplexRelationAttrGroups().get(OU.FQN)
                    ? metainfoService.getMetaClass(Employee.FQN).getTitle()
                    : metainfoService.getMetaClass(OU.FQN).getTitle();
            addTab(tabTitle, ouTab, "ous");
        }
        if (isTeamTabExists)
        {
            TeamEmployeeTabPresenter teamEmployeeTab = teamsTabPresenterProvider.get();
            teamEmployeeTab.init(context);
            addTab(metainfoService.getMetaClass(Team.FQN).getTitle(), teamEmployeeTab, "teams");
        }
        callback.onSuccess(null);
    }

    @Override
    protected void afterInitTabs()
    {
        super.afterInitTabs();
        setFocusOnUpperSearchField();
    }

    @Override
    protected void handleTabSelection(SelectionEvent<Integer> event)
    {
        super.handleTabSelection(event);
        setFocusOnUpperSearchField();
    }

    private void setFocusOnUpperSearchField()
    {
        Presenter p = getSelectedPresenter();
        if (p instanceof AbstractAggrTabPresenter)
        {
            ((AbstractAggrTabPresenter)p).setFocusOnUpperSearchField();
        }
    }
}
