package ru.naumen.dynaform.client.preview;

import static ru.naumen.core.client.inject.Gin.type;
import static ru.naumen.core.client.inject.Gin.typeLiteral;

import jakarta.inject.Singleton;

import ru.naumen.core.client.preview.FilePreviewPresenter;
import ru.naumen.core.client.preview.FilePreviewPresenterFactory;
import ru.naumen.core.client.preview.InitializationContext;
import ru.naumen.dynaform.client.preview.infopanel.FileInfoPanelPresenter;
import ru.naumen.dynaform.client.preview.infopanel.FileInfoPanelPresenterImpl;
import ru.naumen.dynaform.client.preview.toolbar.ButtonBinder;
import ru.naumen.dynaform.client.preview.toolbar.DefaultButtonBinder;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

/**
 *
 * <AUTHOR>
 * @since 17 сент. 2015 г.
 */
public class PreviewPresenterGinModule<T extends InitializationContext> extends AbstractGinModule
{
    public static <T extends InitializationContext> PreviewPresenterGinModule<T> create(Class<T> contextType,
            Class<? extends PreviewContextFactory<T>> contextFactoryType)
    {
        return new PreviewPresenterGinModule<>(contextType, contextFactoryType);
    }

    public static <T extends InitializationContext> PreviewPresenterGinModule<T> create(Class<T> contextType,
            TypeLiteral<? extends PreviewContextFactory<T>> contextFactoryType)
    {
        return new PreviewPresenterGinModule<>(contextType, contextFactoryType);
    }

    private Class<T> contextType;

    private TypeLiteral<? extends FilePreviewPresenter<T>> filePreviewPresenterType;
    private TypeLiteral<? extends PreviewContextFactory<T>> contextFactoryType;
    private TypeLiteral<? extends ButtonBinder<T>> buttonBinderType;
    private TypeLiteral<? extends FileInfoPanelPresenter<T>> infoPanelPresenterType;

    public PreviewPresenterGinModule(Class<T> contextType, Class<? extends PreviewContextFactory<T>> contextFactoryType)
    {
        this.contextType = contextType;
        this.contextFactoryType = typeLiteral(contextFactoryType);
    }

    public PreviewPresenterGinModule(Class<T> contextType,
            TypeLiteral<? extends PreviewContextFactory<T>> contextFactoryType)
    {
        this.contextType = contextType;
        this.contextFactoryType = contextFactoryType;
    }

    public PreviewPresenterGinModule<T> setButtonBinder(Class<? extends ButtonBinder<T>> buttonBinder)
    {
        this.buttonBinderType = typeLiteral(buttonBinder);
        return this;
    }

    public PreviewPresenterGinModule<T> setButtonBinder(TypeLiteral<? extends ButtonBinder<T>> buttonBinder)
    {
        this.buttonBinderType = buttonBinder;
        return this;
    }

    public PreviewPresenterGinModule<T> setInfoPanelPresenter(
            Class<? extends FileInfoPanelPresenter<T>> infoPanelPresenterType)
    {
        this.infoPanelPresenterType = typeLiteral(infoPanelPresenterType);
        return this;
    }

    public PreviewPresenterGinModule<T> setInfoPanelPresenter(
            TypeLiteral<? extends FileInfoPanelPresenter<T>> infoPanelPresenterType)
    {
        this.infoPanelPresenterType = infoPanelPresenterType;
        return this;
    }

    public PreviewPresenterGinModule<T> setPreviewPresenter(
            TypeLiteral<? extends FilePreviewPresenter<T>> presenterType)
    {
        this.filePreviewPresenterType = presenterType;
        return this;
    }

    @Override
    protected void configure()
    {
        if (filePreviewPresenterType == null)
        {
            filePreviewPresenterType = typeLiteral(FilePreviewPresenterImpl.class, type(contextType));
        }
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(typeLiteral(FilePreviewPresenter.class, contextType), 
                filePreviewPresenterType)
            .build(typeLiteral(FilePreviewPresenterFactory.class, type(contextType))));
        //@formatter:on

        if (buttonBinderType == null)
        {
            buttonBinderType = typeLiteral(DefaultButtonBinder.class, type(contextType));
        }
        //@formatter:off
        bind(typeLiteral(ButtonBinder.class, type(contextType)))
            .to(buttonBinderType)
            .in(Singleton.class);
        
        bind(typeLiteral(PreviewContextFactory.class, type(contextType)))
            .to(contextFactoryType)
            .in(Singleton.class);
        //@formatter:on

        if (infoPanelPresenterType == null)
        {
            infoPanelPresenterType = typeLiteral(FileInfoPanelPresenterImpl.class, type(contextType));
        }
        //@formatter:off
        bind(typeLiteral(FileInfoPanelPresenter.class, type(contextType)))
            .to(infoPanelPresenterType);
        //@formatter:on
    }
}
