package ru.naumen.core.server.flex.spi;

import java.sql.Connection;
import java.util.Map;

import org.hibernate.boot.Metadata;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.dialect.Dialect;
import org.hibernate.tool.schema.extract.spi.DatabaseInformation;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Реалзиция {@link SchemaUpdaterStrategy} по умолчанию
 *
 * <AUTHOR>
 * @since 11.03.2020
 */
public class SchemaUpdaterStrategyImpl implements SchemaUpdaterStrategy
{
    @Override
    public SessionFactorySchemaUpdater getSchemaUpdater(DataBaseInfo dataBaseInfo, Connection connection,
            Dialect dialect,
            DatabaseInformation databaseMetadata, Metadata metadata, SqlStringGenerationContext context,
            Map<?, ?> configuration)
    {
        return new SessionFactorySchemaUpdater(new DDLTool(connection), dataBaseInfo, dialect, databaseMetadata,
                metadata, context, configuration);
    }
}
