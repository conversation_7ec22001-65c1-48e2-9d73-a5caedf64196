package ru.naumen.core.server.flex.spi;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import javax.naming.NamingException;
import javax.naming.Reference;

import org.apache.commons.dbcp2.DelegatingConnection;
import org.hibernate.CacheMode;
import org.hibernate.CustomEntityDirtinessStrategy;
import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.SessionFactoryObserver;
import org.hibernate.StatelessSession;
import org.hibernate.StatelessSessionBuilder;
import org.hibernate.boot.Metadata;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.boot.spi.SessionFactoryOptions;
import org.hibernate.cache.spi.CacheImplementor;
import org.hibernate.cfg.CacheSettings;
import org.hibernate.cfg.Configuration;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.hibernate.engine.jdbc.spi.JdbcServices;
import org.hibernate.engine.profile.FetchProfile;
import org.hibernate.engine.spi.FilterDefinition;
import org.hibernate.engine.spi.SessionBuilderImplementor;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.event.spi.EventEngine;
import org.hibernate.generator.Generator;
import org.hibernate.graph.spi.RootGraphImplementor;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.internal.FastSessionServices;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.metamodel.model.domain.spi.JpaMetamodelImplementor;
import org.hibernate.metamodel.spi.MetamodelImplementor;
import org.hibernate.metamodel.spi.RuntimeMetamodelsImplementor;
import org.hibernate.proxy.EntityNotFoundDelegate;
import org.hibernate.query.BindableType;
import org.hibernate.query.criteria.HibernateCriteriaBuilder;
import org.hibernate.query.spi.QueryEngine;
import org.hibernate.relational.SchemaManager;
import org.hibernate.service.spi.ServiceRegistryImplementor;
import org.hibernate.stat.internal.StatisticsImpl;
import org.hibernate.stat.spi.StatisticsImplementor;
import org.hibernate.type.Type;
import org.hibernate.type.descriptor.WrapperOptions;
import org.hibernate.type.descriptor.java.JavaType;
import org.hibernate.type.spi.TypeConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.InfrastructureProxy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.persistence.EntityGraph;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceUnitUtil;
import jakarta.persistence.Query;
import jakarta.persistence.SynchronizationType;
import jakarta.transaction.SystemException;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.AfterCompletionSync;
import ru.naumen.core.server.BeforeCompletionSync;
import ru.naumen.core.server.bo.impl.DaoFactoryImpl;
import ru.naumen.core.server.common.ReflectionValueAttributeProvider;
import ru.naumen.core.server.flex.spi.classloader.ClassLoaderDetails;
import ru.naumen.core.server.flex.spi.classloader.SessionFactoryCreator;
import ru.naumen.core.server.hibernate.ComplexInterceptor;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.dialect.NauHibernateDialectHelper;
import ru.naumen.core.server.hibernate.hbm2ddl.CachedMetaDataConnection;
import ru.naumen.core.server.hibernate.readonly.ReadOnlyCacheModeConfiguration;
import ru.naumen.core.server.jms.JMSManager;
import ru.naumen.core.server.jta.ds.DataSourceType;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.server.license.LicensingPolicyHierarchyCache;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.fts.server.dbsearch.DatabaseFTSConfiguration;
import ru.naumen.metainfo.server.pageinfo.PageMetaInfoContentResetEvent;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;

/**
 * {@link SessionFactory} поддерживающая перезагрузку. Реально поднимается {@link FlexSessionFactoryBean}.
 * <p>
 * Для потоков получаемых из пула требуется сообщать об освобождение потока (возвращение потока в пул) методом
 * {@link #release()}.
 *
 * <AUTHOR>
 * @see ReloadableSessionFactoryWrapper
 * @see #reload()
 * @see #release()
 */
public class ReloadableSessionFactoryBean implements InitializingBean, DisposableBean
{
    /**
     * Враппер для текущей {@link SessionFactory}.
     * <p>
     * Гарантируется, что каждый поток будет обращаться к одной и той же {@link SessionFactory}, за исключением
     * принудительного "сброса" {@link SessionFactory} для текущего потока.
     */
    public class ReloadableSessionFactoryWrapper implements SessionFactoryImplementor, InfrastructureProxy
    {
        private static final long serialVersionUID = -193774856888620171L;

        private transient CurrentSessionFactoryStrategy currentSessionFactoryStrategy =
                ReloadableSessionFactoryBean.this::getCurrentSessionFactory;

        @Override
        public void addObserver(SessionFactoryObserver observer)
        {
            getThreadSessionFactory().addObserver(observer);
        }

        @Override
        public void close() throws HibernateException
        {
            getThreadSessionFactory().close();
        }

        @Override
        public Map<String, Object> getProperties()
        {
            return getThreadSessionFactory().getProperties();
        }

        @Override
        public boolean containsFetchProfileDefinition(String name)
        {
            return getThreadSessionFactory().containsFetchProfileDefinition(name);
        }

        @Override
        public CacheImplementor getCache()
        {
            return getThreadSessionFactory().getCache();
        }

        @Override
        public PersistenceUnitUtil getPersistenceUnitUtil()
        {
            return getThreadSessionFactory().getPersistenceUnitUtil();
        }

        @Override
        public void addNamedQuery(String s, Query query)
        {
            getThreadSessionFactory().addNamedQuery(s, query);
        }

        @Override
        public <T> T unwrap(Class<T> aClass)
        {
            return getThreadSessionFactory().unwrap(aClass);
        }

        @Override
        public <T> void addNamedEntityGraph(String s, EntityGraph<T> entityGraph)
        {
            getThreadSessionFactory().addNamedEntityGraph(s, entityGraph);
        }

        @Override
        public Session getCurrentSession() throws HibernateException
        {
            Session currentSession = getThreadSessionFactory().getCurrentSession();
            CacheMode sessionCacheMode = getSessionCacheModeToFreeze();
            if (sessionCacheMode == null)
            {
                //Если не нужен явно заданный cacheMode то просто возращаем сессию без оберток
                return currentSession;
            }
            return FrozenCacheModeSession.wrap(currentSession, sessionCacheMode);
        }

        @Override
        public CurrentTenantIdentifierResolver getCurrentTenantIdentifierResolver()
        {
            return getThreadSessionFactory().getCurrentTenantIdentifierResolver();
        }

        @Override
        public CustomEntityDirtinessStrategy getCustomEntityDirtinessStrategy()
        {
            return getThreadSessionFactory().getCustomEntityDirtinessStrategy();
        }

        @Override
        public Set getDefinedFilterNames()
        {
            return getThreadSessionFactory().getDefinedFilterNames();
        }

        @Override
        public SqlStringGenerationContext getSqlStringGenerationContext()
        {
            return getThreadSessionFactory().getSqlStringGenerationContext();
        }

        @Override
        public EntityNotFoundDelegate getEntityNotFoundDelegate()
        {
            return getThreadSessionFactory().getEntityNotFoundDelegate();
        }

        @Override
        public FetchProfile getFetchProfile(String name)
        {
            return getThreadSessionFactory().getFetchProfile(name);
        }

        @Override
        public FilterDefinition getFilterDefinition(String filterName) throws HibernateException
        {
            return getThreadSessionFactory().getFilterDefinition(filterName);
        }

        @Override
        public IdentifierGenerator getIdentifierGenerator(String rootEntityName)
        {
            return getThreadSessionFactory().getIdentifierGenerator(rootEntityName);
        }

        @Override
        public String getIdentifierPropertyName(String className) throws MappingException
        {
            return getThreadSessionFactory().getIdentifierPropertyName(className);
        }

        @Override
        public Type getIdentifierType(String className) throws MappingException
        {
            return getThreadSessionFactory().getIdentifierType(className);
        }

        @Override
        public JdbcServices getJdbcServices()
        {
            return getThreadSessionFactory().getJdbcServices();
        }

        @Override
        public Reference getReference() throws NamingException
        {
            return getThreadSessionFactory().getReference();
        }

        @Override
        public Type getReferencedPropertyType(String className, String propertyName) throws MappingException
        {
            return getThreadSessionFactory().getReferencedPropertyType(className, propertyName);
        }

        @Override
        public ServiceRegistryImplementor getServiceRegistry()
        {
            return getThreadSessionFactory().getServiceRegistry();
        }

        @Override
        public EventEngine getEventEngine()
        {
            return getThreadSessionFactory().getEventEngine();
        }

        @Override
        public SessionFactoryOptions getSessionFactoryOptions()
        {
            return getThreadSessionFactory().getSessionFactoryOptions();
        }

        @Override
        public EntityManager createEntityManager()
        {
            return getThreadSessionFactory().createEntityManager();
        }

        @Override
        public EntityManager createEntityManager(Map map)
        {
            return getThreadSessionFactory().createEntityManager(map);
        }

        @Override
        public EntityManager createEntityManager(SynchronizationType synchronizationType)
        {
            return getThreadSessionFactory().createEntityManager(synchronizationType);
        }

        @Override
        public EntityManager createEntityManager(SynchronizationType synchronizationType, Map map)
        {
            return getThreadSessionFactory().createEntityManager(synchronizationType, map);
        }

        @Override
        public HibernateCriteriaBuilder getCriteriaBuilder()
        {
            return getThreadSessionFactory().getCriteriaBuilder();
        }

        @Override
        public MetamodelImplementor getMetamodel()
        {
            return getThreadSessionFactory().getMetamodel();
        }

        @Override
        public boolean isOpen()
        {
            return getThreadSessionFactory().isOpen();
        }

        @Override
        public RootGraphImplementor<?> findEntityGraphByName(String name)
        {
            return getThreadSessionFactory().findEntityGraphByName(name);
        }

        @Override
        public StatisticsImplementor getStatistics()
        {
            if (hibernateStatistics)
            {
                return getThreadSessionFactory().getStatistics();
            }
            else
            {
                return null;
            }
        }

        @Override
        public Object getWrappedObject()
        {
            return getThreadSessionFactory();
        }

        @Override
        public boolean isClosed()
        {
            return getThreadSessionFactory().isClosed();
        }

        @Override
        public FastSessionServices getFastSessionServices()
        {
            return getThreadSessionFactory().getFastSessionServices();
        }

        @Override
        public SessionImplementor openSession() throws HibernateException
        {
            SessionImplementor session = getThreadSessionFactory().openSession();
            final CacheMode cacheMode = getSessionCacheModeToFreeze();
            if (cacheMode == null)
            {
                //Если не нужен явно заданный cacheMode то просто возращаем сессию без оберток
                return session;
            }
            return (SessionImplementor)FrozenCacheModeSession.wrap(session, cacheMode);
        }

        @Override
        public StatelessSession openStatelessSession()
        {
            return getThreadSessionFactory().openStatelessSession();
        }

        @Override
        public StatelessSession openStatelessSession(Connection connection)
        {
            return getThreadSessionFactory().openStatelessSession(connection);
        }

        @Override
        public SessionImplementor openTemporarySession() throws HibernateException
        {
            return getThreadSessionFactory().openTemporarySession();
        }

        @Override
        public String getUuid()
        {
            return getThreadSessionFactory().getUuid();
        }

        @Override
        public String getName()
        {
            return getThreadSessionFactory().getName();
        }

        @Override
        public SessionBuilderImplementor withOptions()
        {
            return getThreadSessionFactory().withOptions();
        }

        @Override
        public StatelessSessionBuilder withStatelessOptions()
        {
            return getThreadSessionFactory().withStatelessOptions();
        }

        private SessionFactoryImplementor getThreadSessionFactory()
        {
            return currentSessionFactoryStrategy.getCurrentSessionFactory();
        }

        @Nullable
        private CacheMode getSessionCacheModeToFreeze()
        {
            if (!secondLevelCacheEnabled)
            {
                return CacheMode.IGNORE;
            }
            if (DataSourceType.READ_ONLY == dataSourceInvocationContext.getCurrentInvocation().getBoundDataSourceType())
            {
                return readOnlyCacheModeConfiguration.getReadOnlyCacheMode();
            }
            return null;
        }

        public void setCurrentSessionFactoryStrategy(CurrentSessionFactoryStrategy currentSessionFactoryStrategy)
        {
            this.currentSessionFactoryStrategy = currentSessionFactoryStrategy;
        }

        @SuppressWarnings("java:S5738")
        @Override
        public <T> BindableType<? super T> resolveParameterBindType(T bindValue)
        {
            return getThreadSessionFactory().resolveParameterBindType(bindValue);
        }

        @SuppressWarnings("java:S5738")
        @Override
        public <T> BindableType<T> resolveParameterBindType(Class<T> clazz)
        {
            return getThreadSessionFactory().resolveParameterBindType(clazz);
        }

        @Override
        public TypeConfiguration getTypeConfiguration()
        {
            return getThreadSessionFactory().getTypeConfiguration();
        }

        @Override
        public QueryEngine getQueryEngine()
        {
            return getThreadSessionFactory().getQueryEngine();
        }

        @Override
        public RuntimeMetamodelsImplementor getRuntimeMetamodels()
        {
            return getThreadSessionFactory().getRuntimeMetamodels();
        }

        @Override
        public Generator getGenerator(String rootEntityName)
        {
            return getThreadSessionFactory().getGenerator(rootEntityName);
        }

        @Override
        public JavaType<Object> getTenantIdentifierJavaType()
        {
            return getThreadSessionFactory().getTenantIdentifierJavaType();
        }

        @Override
        public WrapperOptions getWrapperOptions()
        {
            return getThreadSessionFactory().getWrapperOptions();
        }

        @Override
        public Collection<FilterDefinition> getAutoEnabledFilters()
        {
            return getThreadSessionFactory().getAutoEnabledFilters();
        }

        @Override
        public String bestGuessEntityName(Object object)
        {
            return getThreadSessionFactory().bestGuessEntityName(object);
        }

        @Override
        public SchemaManager getSchemaManager()
        {
            return getThreadSessionFactory().getSchemaManager();
        }

        @Override
        public <T> List<EntityGraph<? super T>> findEntityGraphsByType(Class<T> entityClass)
        {
            return getThreadSessionFactory().findEntityGraphsByType(entityClass);
        }

        @Override
        public Set<String> getDefinedFetchProfileNames()
        {
            return getThreadSessionFactory().getDefinedFetchProfileNames();
        }

        @Override
        public JpaMetamodelImplementor getJpaMetamodel()
        {
            return getThreadSessionFactory().getJpaMetamodel();
        }

        @Override
        public Integer getMaximumFetchDepth()
        {
            return getThreadSessionFactory().getMaximumFetchDepth();
        }

        @SuppressWarnings("java:S5738")
        @Override
        public DeserializationResolver<?> getDeserializationResolver()
        {
            return getThreadSessionFactory().getDeserializationResolver();
        }
    }

    private class TxSynchronization implements AfterCompletionSync
    {
        private final Transaction tx;

        public TxSynchronization(Transaction tx)
        {
            this.tx = tx;
        }

        @Override
        public void afterCompletion(int status)
        {
            onTxEnd(tx);
        }
    }

    private static final Set<String> HIBERNATE_PROPERTIES_TO_TRIM = Set.of(
            CacheSettings.USE_SECOND_LEVEL_CACHE,
            CacheSettings.USE_QUERY_CACHE);

    public static final String HIBERNATE_DIALECT_PROPERTY_NAME = "hibernate.dialect";

    private static final String SESSION_FACTORY = "SessionFactory";
    private static final String CLASSLOADER_KEY = "MAIN";
    private static final String METAMODEL_FIELD = "metamodel";
    private static final String CACHE_FIELD = "cache";

    private static final Logger LOG = LoggerFactory.getLogger(ReloadableSessionFactoryBean.class);
    /**
     * Объект для блокировки получения текущей {@link SessionFactory}
     *
     * @see #sessionFactoryBean
     */
    private final ReadWriteLock accessLock = new ReentrantReadWriteLock();
    /**
     * Суммарное время перезагрузки SessionFactory
     */
    private final AtomicLong reloadTime = new AtomicLong(0);
    @Value("${old-session-factories-check.enabled}")
    private boolean checkOldSessionFactories;
    private TransactionalDataSource dataSource;
    protected List<String> packagesToScan;
    protected List<String> annotatedPackages;
    private Class<?>[] annotatedClasses;
    private Properties hibernateProperties;
    @Inject
    private TransactionManager jtaTxManager;
    private List<SchemaUpdater> updaters;
    // текущий bean возвращающий актуальную sessionFactory
    private SessionFactoryImpl sessionFactoryBean;
    // bean-ы замененные новой версией sessionFactory.
    // когда они закончат использоваться их следует удалить
    private volatile Set<SessionFactory> oldSessionFactories;
    // количество старых sessionFactory
    private final AtomicInteger countOldSessionFactories = new AtomicInteger(0);
    // sessionFactory относящаяся к текущей транзакции
    private ConcurrentMap<Transaction, SessionFactoryImpl> txMap;
    // запоминает кол-во использований sessionFactory
    private ConcurrentMap<SessionFactory, AtomicInteger> usedSessionFactories;
    /**
     * Экземпляр {@link ReloadableSessionFactoryWrapper}
     * <p>
     * Для каждой {@link ReloadableSessionFactoryBean} не имеет смысла создавать более одного экземпляря
     * {@link ReloadableSessionFactoryWrapper} т.к. не содержит внутренних полей.
     */
    private ReloadableSessionFactoryWrapper INSTANCE;
    @Inject
    private LicensingPolicyHierarchyCache hierarchyCache;
    @Inject
    @Lazy
    private MetainfoServiceBean metainfoService;
    @Inject
    private ReflectionValueAttributeProvider reflectionValueAttributeProvider;
    @Inject
    private DaoFactoryImpl daoFactory;
    @Inject
    protected ComplexInterceptor interceptor;
    @Inject
    private JMSManager jmsManager;
    @Value("${db.events.datasource.enabled}")
    protected boolean isDbEventsDataSourceEnable;
    @Inject
    private NauHibernateDialectHelper hibernateDialectHelper;
    @Inject
    private SessionFactoryCreator currentOrNewCLSessionFactoryCreator;
    @Inject
    private SessionFactoryRegistry sessionFactoryRegistry;

    private volatile FlexSessionFactoryBuilder sfBuilder; // NOSONAR
    private final Set<Class<?>> annotatedClassesCache = new HashSet<>();

    private final Set<String> packageNamesCache = new HashSet<>();

    @Value("${ru.naumen.persistence.second_level_cache.use}")
    private volatile boolean secondLevelCacheEnabled;

    @Inject
    private DataSourceBoundInvocationManager dataSourceInvocationContext;

    @Inject
    private ReadOnlyCacheModeConfiguration readOnlyCacheModeConfiguration;

    @Inject
    private ModulesService modulesService;

    @Inject
    private ApplicationEventPublisher eventPublisher;

    @Value("${hibernate.generate_statistics}")
    private boolean hibernateStatistics;

    @Inject
    protected DataBaseInfo dataBaseInfo;

    @Override
    public void afterPropertiesSet()
    {
        oldSessionFactories = new HashSet<>();
        usedSessionFactories = new ConcurrentHashMap<>();
        txMap = new ConcurrentHashMap<>();

        hibernateDialectHelper.handleHibernateDialect(this.hibernateProperties);
        sessionFactoryBean = createSessionFactory(true);
        INSTANCE = new ReloadableSessionFactoryWrapper();
    }

    protected String getSessionFactoryName()
    {
        return SESSION_FACTORY;
    }

    protected FlexSessionFactoryBuilder createSessionFactoryBuilder(ClassLoaderDetails details)
    {
        return new FlexSessionFactoryBuilder(getDataSource(),
                new PathMatchingResourcePatternResolver(), interceptor, dataBaseInfo, details);
    }

    public void setHibernateDialectAndReload(String dialect)
    {
        hibernateProperties.setProperty(HIBERNATE_DIALECT_PROPERTY_NAME, dialect);
        hibernateDialectHelper.handleHibernateDialect(hibernateProperties);
        MetainfoModification.setReloadClassFqn(MetainfoModification.ALL_CLASS_RELOAD);
        reload();
    }

    @Override
    @PreDestroy
    public void destroy()
    {
        LOG.info("Destroy reloadable session factory");

        for (SessionFactory sessionFactory : oldSessionFactories)
        {
            sessionFactory.close();
        }
        oldSessionFactories.clear();
        countOldSessionFactories.set(0);
        usedSessionFactories.clear();
        txMap.clear();

        if (sessionFactoryBean != null)
        {
            sessionFactoryBean.close();
            sessionFactoryBean = null;
        }
    }

    public Configuration getConfiguration()
    {
        if (this.sfBuilder == null)
        {
            throw new IllegalStateException("Configuration not initialized yet");
        }
        return this.sfBuilder;
    }

    public Metadata getMetadata()
    {
        if (this.sfBuilder != null)
        {
            return this.sfBuilder.getMetadata();
        }
        throw new IllegalStateException("Metadata not initialized yet");
    }

    public SessionFactoryImpl getCurrentSessionFactory()
    {
        Transaction tx = getCurrentTransaction();
        SessionFactoryImpl current = sessionFactoryBean;
        if (tx != null)
        {
            current = txMap.get(tx);
            if (current != null)
            {
                return current;
            }

            //Во время операций не должна измениться session factory(!)
            //Иначе другой поток в методе txRelease
            //может сделать destroy для текущей session factory
            accessLock.readLock().lock();
            try
            {

                current = sessionFactoryBean;
                AtomicInteger count = usedSessionFactories.get(current);
                if (count == null)
                {
                    usedSessionFactories.putIfAbsent(current, new AtomicInteger());
                }
                count = usedSessionFactories.get(current);
                count.incrementAndGet();
                txMap.put(tx, current);
                sessionFactoryRegistry.putTx(tx, current);

                try
                {
                    tx.registerSynchronization(new TxSynchronization(tx));
                }
                catch (Exception e)
                {
                    // Если не удалось зарегистрировать событие = транзакция плохая,
                    // нужно удалить транзакцию из txMap, а фабрику из usedSessionFactories
                    onTxEnd(tx);
                    throw new FxException(e);
                }
            }
            finally
            {
                accessLock.readLock().unlock();
            }
        }

        return current;
    }

    public TransactionalDataSource getDataSource()
    {
        return dataSource;
    }

    public long getReloadTime()
    {
        return reloadTime.get();
    }

    public boolean hasOldSessionFactories()
    {
        return countOldSessionFactories.get() > 0;
    }

    public boolean isCheckOldSessionFactories()
    {
        return checkOldSessionFactories;
    }

    /**
     * Метод предназначен для регистрации синхронизации, которая по окончании текущей транзакции
     * производит обновление схемы для указанного метакласса. Предназначен для обновления constraint-ов на таблице
     * класса
     */
    public void registerSchemaUpdateSyncronization(final Object param)
    {
        Transaction tx = getCurrentTransaction();
        try
        {
            tx.registerSynchronization((BeforeCompletionSync)() -> updateDatabaseSchema(param));
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public void release()
    {
        processRelease(getCurrentSessionFactory(), false);
    }

    /**
     * Перезагружает sessionFactory
     */
    public void reload()
    {
        reload(true);
    }

    public void reload(boolean local)
    {
        jmsManager.disconnectIndexers();
        try
        {
            LOG.info("Reloading session factory");
            SessionFactoryImpl newSessionFactory = createSessionFactory(local);
            accessLock.writeLock().lock();
            try
            {
                countOldSessionFactories.incrementAndGet();
                oldSessionFactories.add(sessionFactoryBean);
                if (hibernateStatistics)
                {
                    StatisticsImpl statistics =
                            (StatisticsImpl)sessionFactoryBean.getStatistics();
                    if (statistics != null)
                    {
                        final Field metamodel = statistics.getClass().getDeclaredField(METAMODEL_FIELD);
                        metamodel.setAccessible(true); // NOSONAR
                        metamodel.set(statistics, newSessionFactory.getMetamodel()); // NOSONAR
                        final Field cache = statistics.getClass().getDeclaredField(CACHE_FIELD);
                        cache.setAccessible(true); // NOSONAR
                        cache.set(statistics, newSessionFactory.getCache()); // NOSONAR
                    }
                }
                // присвоение текущей фабрики может случится до того, как в getCurrentSessionFactory в локальную
                // переменную присвоится уже устаревшее значение
                accessLock.readLock().lock();
                try
                {
                    processRelease(sessionFactoryBean, false);
                    sessionFactoryBean = newSessionFactory;
                }
                finally
                {
                    accessLock.readLock().unlock();
                }
            }
            catch (NoSuchFieldException | IllegalAccessException e)
            {
                throw new FxException(e);
            }
            finally
            {
                try
                {
                    reflectionValueAttributeProvider.cleanCache();
                    hierarchyCache.cleanCache();
                }
                finally
                {
                    accessLock.writeLock().unlock();
                }
            }
        }
        finally
        {
            jmsManager.connectIndexers();
        }
    }

    public void reloadCluster()
    {
        long start = System.currentTimeMillis();
        try
        {
            LOG.info("Reloading session factory: Cluster mode");
            daoFactory.reinit();
            metainfoService.init();
            modulesService.init();
            // Инициализация daoFactory нужна после инициализации metainfoService, так как одно от другого зависит.
            // Это гарантирует, что после синхронизации в daoFactory всегда будут актуальные данные,
            // даже если во время reload другой поток обратится к dao
            daoFactory.reinit();
            daoFactory.ensureInitialised();
            eventPublisher.publishEvent(new PageMetaInfoContentResetEvent());
            MetainfoModification.setReloadClassFqn(MetainfoModification.ALL_CLASS_RELOAD);
            reload(false);
        }
        finally
        {
            LOG.info("Done({}): Reloading session factory: Cluster mode", System.currentTimeMillis() - start);
        }
    }

    public void setAnnotatedClasses(Class<?>... annotatedClasses)
    {
        this.annotatedClasses = annotatedClasses;
    }

    public void setAnnotatedPackages(List<String> annotatedPackages)
    {
        if (isDbEventsDataSourceEnable)
        {
            this.annotatedPackages = annotatedPackages.stream()
                    .filter(item -> !"ru.naumen.core.server.events.AbstractStateResponsibleEvent".equals(item))
                    .toList();
        }
        else
        {
            this.annotatedPackages = annotatedPackages;
        }
    }

    public void setDataSource(TransactionalDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    public void setHibernateProperties(Properties hibernateProperties)
    {
        this.hibernateProperties = hibernateProperties;

        //удалим лишние пробелы, чтобы hibernate смог распарсить корректно значения. см ConfigurationHelper
        // .getBoolean(String, Map, boolean)
        for (String property : HIBERNATE_PROPERTIES_TO_TRIM)
        {
            if (hibernateProperties.containsKey(property))
            {
                String value = hibernateProperties.getProperty(property);
                value = StringUtilities.trimToEmpty(value);
                hibernateProperties.put(property, value);
            }
        }
    }

    public void setJtaTxManager(TransactionManager jtaTxManager)
    {
        this.jtaTxManager = jtaTxManager;
    }

    @GroovyUsage
    public void setL2CacheEnabled(boolean enabled)
    {
        secondLevelCacheEnabled = enabled;
    }

    public void setPackagesToScan(List<String> packagesToScan)
    {
        this.packagesToScan = packagesToScan;
    }

    public void setUpdaters(List<SchemaUpdater> updaters)
    {
        this.updaters = updaters;
    }

    public void updateDatabaseSchema(final Object param)
    {
        LOG.info("Updating database schema for {}", param);
        try (Connection connection = dataSource.getConnectionForSchemeUpdate(dataBaseInfo);
             Connection conn = new CachedMetaDataConnection(new DelegatingConnection<>(connection)))
        {
            for (SchemaUpdater updater : updaters)
            {
                if (!updater.canProcess(param))
                {
                    continue;
                }
                LOG.info("Process {}", updater);
                updater.update(param, conn, getMetadata(), sfBuilder.getProperties());
            }
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    private SessionFactoryImpl createSessionFactory(final boolean local)
    {
        return currentOrNewCLSessionFactoryCreator.create(local, getSessionFactoryClassLoaderKey(), reloadTime,
                this::setupSessionFactoryBuilder);
    }

    private FlexSessionFactoryBuilder setupSessionFactoryBuilder(ClassLoaderDetails details)
    {
        FlexSessionFactoryBuilder builder = createSessionFactoryBuilder(details);

        builder.setAnnotatedClassesCache(annotatedClassesCache);
        builder.setPackageNamesCache(packageNamesCache);
        builder.scanPackages(packagesToScan.toArray(new String[0]));
        for (String pkg : annotatedPackages)
        {
            builder.addPackage(pkg);
        }
        if (this.annotatedClasses != null)
        {
            builder.addAnnotatedClasses(this.annotatedClasses);
        }
        builder.addProperties(hibernateProperties);
        DatabaseFTSConfiguration.init(hibernateProperties);
        builder.setProperty("sfType", "flexSessionFactory");

        builder.setUpdaters(updaters);

        sfBuilder = builder;

        return builder;
    }

    protected Transaction getCurrentTransaction()
    {
        try
        {
            return jtaTxManager.getTransaction();
        }
        catch (SystemException e)
        {
            throw new FxException(e);
        }
    }

    protected String getSessionFactoryClassLoaderKey()
    {
        return CLASSLOADER_KEY;
    }

    private void onTxEnd(Transaction tx)
    {
        sessionFactoryRegistry.removeTx(tx);
        SessionFactory sessionFactory = txMap.remove(tx);
        if (sessionFactory != null)
        {
            processRelease(sessionFactory, true);
        }
    }

    private void processRelease(SessionFactory sessionFactory, boolean free)
    {
        accessLock.readLock().lock();
        try
        {
            AtomicInteger count = usedSessionFactories.get(sessionFactory);

            if (count == null)
            {
                count = new AtomicInteger();
            }

            int value = count.get();

            if (free)
            {
                value = count.decrementAndGet();
            }

            if (value == 0 && oldSessionFactories.contains(sessionFactory))
            {
                // удаляем если session factory потока помечена на удаление и больше не используется
                sessionFactory.close();
                countOldSessionFactories.decrementAndGet();
                oldSessionFactories.remove(sessionFactory);
                usedSessionFactories.remove(sessionFactory);
                sessionFactoryRegistry.getAggregatedClassLoader().clearClassLoadersForRemove();
                LOG.info("Destroyed {}", sessionFactory);
                if (sessionFactoryBean != null && sessionFactoryBean != sessionFactory)
                {
                    // Т.к. все SF ссылаются на одни и те же данные кэшей, закрытие старой SF повлечет частичное
                    // удаление данных, приводя кэш в неконсистентное состояние (например, останутся данные в кэше
                    // запросов, но очистятся все метки времени, определяющие его актуальность).
                    // Поэтому кэш запросов в текущей SF нужно сбросить полностью.
                    sessionFactoryBean.getCache().evictQueryRegions();
                }
            }
        }
        finally
        {
            accessLock.readLock().unlock();
        }
    }

    public ReloadableSessionFactoryWrapper getSessionFactoryInstance()
    {
        return INSTANCE;
    }
}
