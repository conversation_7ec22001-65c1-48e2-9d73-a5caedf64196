package ru.naumen.core.server.flex.spi;

import java.io.File;
import java.io.IOException;
import java.util.Properties;

import javax.sql.DataSource;

import org.hibernate.Interceptor;
import org.hibernate.SessionFactory;
import org.hibernate.boot.model.naming.ImplicitNamingStrategy;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.cfg.Configuration;
import org.hibernate.resource.jdbc.spi.StatementInspector;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.orm.hibernate5.HibernateExceptionTranslator;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.orm.hibernate5.LocalSessionFactoryBuilder;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.dialect.NauHibernateDialectHelper;

/**
 * Сделан на основе {@link LocalSessionFactoryBean}. Переопределяет {@link SessionFactoryBuilder}
 *
 * <AUTHOR>
 *
 */
public class SessionFactoryBean extends HibernateExceptionTranslator implements FactoryBean<SessionFactory>,
        ResourceLoaderAware, InitializingBean, DisposableBean
{
    private DataSource dataSource;

    private Resource[] configLocations;

    private String[] mappingResources;

    private Resource[] mappingLocations;

    private Resource[] cacheableMappingLocations;

    private Resource[] mappingJarLocations;

    private Resource[] mappingDirectoryLocations;

    private Interceptor entityInterceptor;
    private StatementInspector statementInspector;

    private ImplicitNamingStrategy implicitNamingStrategy;

    private PhysicalNamingStrategy physicalNamingStrategy;

    private Properties hibernateProperties;

    private Class<?>[] annotatedClasses;

    private String[] annotatedPackages;

    private String[] packagesToScan;

    private ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();

    private Configuration configuration;

    private DataBaseInfo dataBaseInfo;

    private SessionFactory sessionFactory;

    @Override
    public void afterPropertiesSet() throws IOException
    {
        SpringContext.getInstance().getBean(NauHibernateDialectHelper.class)
                .handleHibernateDialect(this.hibernateProperties);

        LocalSessionFactoryBuilder sfb = new SessionFactoryBuilder(
                this.dataSource,
                this.resourcePatternResolver,
                SessionFactoryType.META,
                dataBaseInfo,
                this.getClass().getClassLoader());

        if (this.configLocations != null)
        {
            for (Resource resource : this.configLocations)
            {
                // Load Hibernate configuration from given location.
                sfb.configure(resource.getURL());
            }
        }

        if (this.mappingResources != null)
        {
            // Register given Hibernate mapping definitions, contained in resource files.
            for (String mapping : this.mappingResources)
            {
                Resource mr = new ClassPathResource(mapping.trim(), this.resourcePatternResolver.getClassLoader());
                sfb.addInputStream(mr.getInputStream());
            }
        }

        if (this.mappingLocations != null)
        {
            // Register given Hibernate mapping definitions, contained in resource files.
            for (Resource resource : this.mappingLocations)
            {
                sfb.addInputStream(resource.getInputStream());
            }
        }

        if (this.cacheableMappingLocations != null)
        {
            // Register given cacheable Hibernate mapping definitions, read from the file system.
            for (Resource resource : this.cacheableMappingLocations)
            {
                sfb.addCacheableFile(resource.getFile());
            }
        }

        if (this.mappingJarLocations != null)
        {
            // Register given Hibernate mapping definitions, contained in jar files.
            for (Resource resource : this.mappingJarLocations)
            {
                sfb.addJar(resource.getFile());
            }
        }

        if (this.mappingDirectoryLocations != null)
        {
            // Register all Hibernate mapping definitions in the given directories.
            for (Resource resource : this.mappingDirectoryLocations)
            {
                File file = resource.getFile();
                if (!file.isDirectory())
                {
                    throw new IllegalArgumentException("Mapping directory location [" + resource
                                                       + "] does not denote a directory");
                }
                sfb.addDirectory(file);
            }
        }

        if (this.entityInterceptor != null)
        {
            sfb.setInterceptor(this.entityInterceptor);
        }

        if (this.statementInspector != null)
        {
            sfb.setStatementInspector(this.statementInspector);
        }

        if (this.implicitNamingStrategy != null)
        {
            sfb.setImplicitNamingStrategy(this.implicitNamingStrategy);
        }

        if (this.physicalNamingStrategy != null)
        {
            sfb.setPhysicalNamingStrategy(this.physicalNamingStrategy);
        }

        if (this.hibernateProperties != null)
        {
            sfb.addProperties(this.hibernateProperties);
        }

        if (this.annotatedClasses != null)
        {
            sfb.addAnnotatedClasses(this.annotatedClasses);
        }

        if (this.annotatedPackages != null)
        {
            sfb.addPackages(this.annotatedPackages);
        }

        if (this.packagesToScan != null)
        {
            sfb.scanPackages(this.packagesToScan);
        }
        // Build SessionFactory instance.
        this.configuration = sfb;
        this.sessionFactory = buildSessionFactory(sfb);
    }

    @Override
    public void destroy()
    {
        this.sessionFactory.close();
    }

    /**
     * Return the Hibernate Configuration object used to build the SessionFactory.
     * Allows for access to configuration metadata stored there (rarely needed).
     * @throws IllegalStateException if the Configuration object has not been initialized yet
     */
    public final Configuration getConfiguration()
    {
        if (this.configuration == null)
        {
            throw new IllegalStateException("Configuration not initialized yet");
        }
        return this.configuration;
    }

    /**
     * Return the Hibernate properties, if any. Mainly available for
     * configuration through property paths that specify individual keys.
     */
    public Properties getHibernateProperties()
    {
        if (this.hibernateProperties == null)
        {
            this.hibernateProperties = new Properties();
        }
        return this.hibernateProperties;
    }

    @Override
    public SessionFactory getObject()
    {
        return this.sessionFactory;
    }

    @Override
    public Class<?> getObjectType()
    {
        return this.sessionFactory != null ? this.sessionFactory.getClass() : SessionFactory.class;
    }

    @Override
    public boolean isSingleton()
    {
        return true;
    }

    /**
     * Specify annotated entity classes to register with this Hibernate SessionFactory.
     * @see org.hibernate.cfg.Configuration#addAnnotatedClass(String)
     */
    public void setAnnotatedClasses(Class<?>[] annotatedClasses)
    {
        this.annotatedClasses = annotatedClasses;
    }

    /**
     * Specify the names of annotated packages, for which package-level
     * annotation metadata will be read.
     * @see org.hibernate.cfg.Configuration#addPackage(String)
     */
    public void setAnnotatedPackages(String[] annotatedPackages)
    {
        this.annotatedPackages = annotatedPackages;
    }

    /**
     * Set locations of cacheable Hibernate mapping files, for example as web app
     * resource "/WEB-INF/mapping/example.hbm.xml". Supports any resource location
     * via Spring's resource abstraction, as long as the resource can be resolved
     * in the file system.
     * <p>Can be used to add to mappings from a Hibernate XML config file,
     * or to specify all mappings locally.
     * @see org.hibernate.cfg.Configuration#addCacheableFile(java.io.File)
     */
    public void setCacheableMappingLocations(Resource[] cacheableMappingLocations)
    {
        this.cacheableMappingLocations = cacheableMappingLocations;
    }

    /**
     * Set the location of a single Hibernate XML config file, for example as
     * classpath resource "classpath:hibernate.cfg.xml".
     * <p>Note: Can be omitted when all necessary properties and mapping
     * resources are specified locally via this bean.
     * @see org.hibernate.cfg.Configuration#configure(java.net.URL)
     */
    public void setConfigLocation(Resource configLocation)
    {
        this.configLocations = new Resource[] { configLocation };
    }

    /**
     * Set the locations of multiple Hibernate XML config files, for example as
     * classpath resources "classpath:hibernate.cfg.xml,classpath:extension.cfg.xml".
     * <p>Note: Can be omitted when all necessary properties and mapping
     * resources are specified locally via this bean.
     * @see org.hibernate.cfg.Configuration#configure(java.net.URL)
     */
    public void setConfigLocations(Resource[] configLocations)
    {
        this.configLocations = configLocations;
    }

    /**
     * Set the DataSource to be used by the SessionFactory.
     * If set, this will override corresponding settings in Hibernate properties.
     * <p>If this is set, the Hibernate settings should not define
     * a connection provider to avoid meaningless double configuration.
     */
    public void setDataSource(DataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    /**
     * Set a Hibernate entity interceptor that allows to inspect and change
     * property values before writing to and reading from the database.
     * Will get applied to any new Session created by this factory.
     * @see org.hibernate.cfg.Configuration#setInterceptor
     */
    public void setEntityInterceptor(Interceptor entityInterceptor)
    {
        this.entityInterceptor = entityInterceptor;
    }

    /**
     * Set Hibernate properties, such as "hibernate.dialect".
     * <p>Note: Do not specify a transaction provider here when using
     * Spring-driven transactions. It is also advisable to omit connection
     * provider settings and use a Spring-set DataSource instead.
     * @see #setDataSource
     */
    public void setHibernateProperties(Properties hibernateProperties)
    {
        this.hibernateProperties = hibernateProperties;
    }

    /**
     * Set locations of directories that contain Hibernate mapping resources,
     * like "WEB-INF/mappings".
     * <p>Can be used to add to mappings from a Hibernate XML config file,
     * or to specify all mappings locally.
     * @see org.hibernate.cfg.Configuration#addDirectory(java.io.File)
     */
    public void setMappingDirectoryLocations(Resource[] mappingDirectoryLocations)
    {
        this.mappingDirectoryLocations = mappingDirectoryLocations;
    }

    /**
     * Set locations of jar files that contain Hibernate mapping resources,
     * like "WEB-INF/lib/example.hbm.jar".
     * <p>Can be used to add to mappings from a Hibernate XML config file,
     * or to specify all mappings locally.
     * @see org.hibernate.cfg.Configuration#addJar(java.io.File)
     */
    public void setMappingJarLocations(Resource[] mappingJarLocations)
    {
        this.mappingJarLocations = mappingJarLocations;
    }

    /**
     * Set locations of Hibernate mapping files, for example as classpath
     * resource "classpath:example.hbm.xml". Supports any resource location
     * via Spring's resource abstraction, for example relative paths like
     * "WEB-INF/mappings/example.hbm.xml" when running in an application context.
     * <p>Can be used to add to mappings from a Hibernate XML config file,
     * or to specify all mappings locally.
     * @see org.hibernate.cfg.Configuration#addInputStream
     */
    public void setMappingLocations(Resource[] mappingLocations)
    {
        this.mappingLocations = mappingLocations;
    }

    /**
     * Set Hibernate mapping resources to be found in the class path,
     * like "example.hbm.xml" or "mypackage/example.hbm.xml".
     * Analogous to mapping entries in a Hibernate XML config file.
     * Alternative to the more generic setMappingLocations method.
     * <p>Can be used to add to mappings from a Hibernate XML config file,
     * or to specify all mappings locally.
     * @see #setMappingLocations
     * @see org.hibernate.cfg.Configuration#addResource
     */
    public void setMappingResources(String[] mappingResources)
    {
        this.mappingResources = mappingResources;
    }

    /**
     * Set pluggable strategy for applying implicit naming rules when an explicit name is not given.
     */
    public void setImplicitNamingStrategy(ImplicitNamingStrategy namingStrategy)
    {
        this.implicitNamingStrategy = namingStrategy;
    }

    /**
     * Set pluggable strategy contract for applying physical naming rules for database object names.
     */
    public void setPhysicalNamingStrategy(PhysicalNamingStrategy namingStrategy)
    {
        this.physicalNamingStrategy = namingStrategy;
    }

    /**
     * Specify packages to search for autodetection of your entity classes in the
     * classpath. This is analogous to Spring's component-scan feature
     * ({@link org.springframework.context.annotation.ClassPathBeanDefinitionScanner}).
     */
    public void setPackagesToScan(String... packagesToScan)
    {
        this.packagesToScan = packagesToScan;
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader)
    {
        this.resourcePatternResolver = ResourcePatternUtils.getResourcePatternResolver(resourceLoader);
    }

    public void setStatementInspector(StatementInspector statementInspector)
    {
        this.statementInspector = statementInspector;
    }

    /**
     * Subclasses can override this method to perform custom initialization
     * of the SessionFactory instance, creating it via the given Configuration
     * object that got prepared by this LocalSessionFactoryBean.
     * <p>The default implementation invokes LocalSessionFactoryBuilder's buildSessionFactory.
     * A custom implementation could prepare the instance in a specific way (e.g. applying
     * a custom ServiceRegistry) or use a custom SessionFactoryImpl subclass.
     * @param sfb LocalSessionFactoryBuilder prepared by this LocalSessionFactoryBean
     * @return the SessionFactory instance
     * @see LocalSessionFactoryBuilder#buildSessionFactory
     */
    protected SessionFactory buildSessionFactory(LocalSessionFactoryBuilder sfb)
    {
        return sfb.buildSessionFactory();
    }

    public void setDataBaseInfo(DataBaseInfo dataBaseInfo)
    {
        this.dataBaseInfo = dataBaseInfo;
    }
}
