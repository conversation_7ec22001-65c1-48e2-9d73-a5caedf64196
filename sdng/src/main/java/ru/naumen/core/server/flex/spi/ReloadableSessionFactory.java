package ru.naumen.core.server.flex.spi;

import org.hibernate.SessionFactory;
import org.hibernate.boot.Metadata;
import org.hibernate.cfg.Configuration;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.beans.factory.FactoryBean;

import ru.naumen.core.server.script.GroovyUsage;

/**
 * Интерфейс {@link SessionFactory}, поддерживающей перезагрузку
 *
 * <AUTHOR>
 * @since 06.02.2020
 */
public interface ReloadableSessionFactory extends FactoryBean<SessionFactory>
{
    /**
     * Установить диалект БД для Hibernate и перезагрузить SessionFactory
     * @param dialect устанавливаемый диалект
     */
    void setHibernateDialectAndReload(String dialect);

    /**
     * Получить конфигурацию SessionFactory
     */
    Configuration getConfiguration();

    /**
     * Получить из Hibernate мапинги фабрики сессий
     */
    Metadata getMetadata();

    /**
     * Перезагрузить SessionFactory
     */
    void reload();

    /**
     * Метод предназначен для регистрации синхронизации, которая по окончании текущей транзакции
     * производит обновление схемы для указанного метакласса.
     * @param param - fqn метакласса
     */
    void registerSchemaUpdateSyncronization(Object param);

    /**
     * Получить текущую SessionFactory
     */
    SessionFactoryImpl getCurrentSessionFactory();

    /**
     * Включить/отключить использование L2-кэша Hibernate
     */
    @GroovyUsage
    void setL2CacheEnabled(boolean enabled);

    /**
     * Возвращает true, если  включена проверка наличия в системе старых фабрик сессий: тех, которые после перезагрузки
     * метаинформации, продолжают работать по старой модели данных
     */
    boolean isCheckOldSessionFactories();

    /**
     * Возвращает true, если  существуют старые фабрики сессий, которые после перезагрузки
     * метаинформации, продолжают работать по старой модели данных
     */
    boolean hasOldSessionFactories();

    /**
     * Возвращает суммарное время перезагрузки SessionFactory
     */
    long getReloadTime();

    /**
     * Перезагружает SessionFactory в кластере
     */
    void reloadCluster();
}
