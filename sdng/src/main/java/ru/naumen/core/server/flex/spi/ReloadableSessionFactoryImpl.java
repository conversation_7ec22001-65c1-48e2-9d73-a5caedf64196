package ru.naumen.core.server.flex.spi;

import jakarta.inject.Inject;

import org.hibernate.SessionFactory;
import org.hibernate.boot.Metadata;
import org.hibernate.cfg.Configuration;
import org.hibernate.internal.SessionFactoryImpl;

import ru.naumen.core.server.script.GroovyUsage;

/**
 * {@link SessionFactory} поддерживающая перезагрузку. Является фасадом для бина
 * {@link ReloadableSessionFactoryBean} и бина SessionFactory для работы с плановыми
 * версиями из модуля версионирвоания. Переключает свою работу в соответствии со
 * стратегией {@link ReloadableSessionFactoryStrategy}
 *
 * <AUTHOR>
 * @since 12.02.2020
 */
public class ReloadableSessionFactoryImpl implements ReloadableSessionFactory
{
    @Inject
    private ReloadableSessionFactoryStrategy reloadableSessionFactoryStrategy;

    private ReloadableSessionFactoryBean getReloadableSessionFactoryBean()
    {
        return reloadableSessionFactoryStrategy.getReloadableSessionFactoryBean();
    }

    @Override
    public void setHibernateDialectAndReload(String dialect)
    {
        getReloadableSessionFactoryBean().setHibernateDialectAndReload(dialect);
    }

    @Override
    public Configuration getConfiguration()
    {
        return getReloadableSessionFactoryBean().getConfiguration();
    }

    @Override
    public Metadata getMetadata()
    {
        return getReloadableSessionFactoryBean().getMetadata();
    }

    @Override
    public void reload()
    {
        reloadableSessionFactoryStrategy.reload();
    }

    @Override
    public void registerSchemaUpdateSyncronization(Object param)
    {
        reloadableSessionFactoryStrategy.registerSchemaUpdateSyncronization(param);
    }

    @Override
    public SessionFactoryImpl getCurrentSessionFactory()
    {
        return getReloadableSessionFactoryBean().getCurrentSessionFactory();
    }

    @GroovyUsage
    @Override
    public void setL2CacheEnabled(boolean enabled)
    {
        reloadableSessionFactoryStrategy.setL2CacheEnabled(enabled);
    }

    @Override
    public boolean isCheckOldSessionFactories()
    {
        return getReloadableSessionFactoryBean().isCheckOldSessionFactories();
    }

    @Override
    public boolean hasOldSessionFactories()
    {
        return getReloadableSessionFactoryBean().hasOldSessionFactories();
    }

    @Override
    public long getReloadTime()
    {
        return getReloadableSessionFactoryBean().getReloadTime();
    }

    @Override
    public void reloadCluster()
    {
        reloadableSessionFactoryStrategy.reloadCluster();
    }

    @Override
    public SessionFactory getObject() throws Exception
    {
        return reloadableSessionFactoryStrategy.getSessionFactory();
    }

    @Override
    public Class<?> getObjectType()
    {
        return SessionFactory.class;
    }

    public ReloadableSessionFactoryBean getSessionFactoryBean()
    {
        return getReloadableSessionFactoryBean();
    }

    @Override
    public boolean isSingleton()
    {
        return true;
    }
}
