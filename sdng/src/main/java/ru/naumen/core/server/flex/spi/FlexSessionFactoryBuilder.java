package ru.naumen.core.server.flex.spi;

import static com.google.common.collect.Sets.difference;
import static ru.naumen.core.server.flex.FlexHelper.getUniqueNameForRemove;
import static ru.naumen.core.server.flex.FlexHelper.renameColumns;
import static ru.naumen.core.server.partition.PartitionUtils.OLD_SUFFIX;
import static ru.naumen.core.server.partition.PartitionUtils.PART_SUFFIX;
import static ru.naumen.core.server.partition.PartitionUtils.getIgnoredTables;
import static ru.naumen.core.server.schemaoptimization.SchemaOptimizationFeatureConfiguration.DELETE_SIGN;
import static ru.naumen.core.shared.Constants.PLANNED_VERSION_POSTFIX;
import static ru.naumen.core.shared.Constants.SYS_MIGRATED_COLUMN;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import org.hibernate.cfg.MappingSettings;
import org.hibernate.cfg.SchemaToolingSettings;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.mapping.Column;
import org.hibernate.mapping.Table;
import org.hibernate.service.ServiceRegistry;
import org.jgroups.View;
import org.postgresql.util.PSQLException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ResourceLoader;
import org.springframework.dao.DataAccessException;
import org.springframework.orm.hibernate5.LocalSessionFactoryBuilder;

import java.util.HashSet;

import com.google.common.collect.Sets.SetView;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import javassist.ClassPool;
import javassist.CtClass;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.AbstractConnectionWrapper;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.attr.AttrStrategyFactory;
import ru.naumen.core.server.flex.codegen.CasesEntitiesBuilder;
import ru.naumen.core.server.flex.codegen.SourceGenHelper;
import ru.naumen.core.server.flex.codegen.attributes.IGeneratedEmbedded;
import ru.naumen.core.server.flex.spi.classloader.ClassLoaderDetails;
import ru.naumen.core.server.hibernate.ComplexInterceptor;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DDLTool.Function;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.DataBaseInfo.DbType;
import ru.naumen.core.server.hibernate.hbm2ddl.TableMetaData;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderRegistryImpl;
import ru.naumen.core.server.rest.SystemChecker;
import ru.naumen.core.server.schemaoptimization.SchemaOptimizationFeatureConfiguration;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ExtMetainfoDataProvider;
import ru.naumen.metainfo.server.spi.MetaInfoHelper;
import ru.naumen.metainfo.server.spi.MetainfoImportHelper;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.migration.server.jdbclogger.DataSourceWrapper.WrappedDataSource;

/**
 * Конструирует {@link SessionFactory} с поддержкой флекс атрибутов
 * Используется в системе для обращения к данным
 *
 * <AUTHOR>
 */
public class FlexSessionFactoryBuilder extends SessionFactoryBuilder
{
    public static class FlexLoggerImpl
            extends ru.naumen.migration.server.jdbclogger.DataSourceWrapper.MigrationLoggerImpl
    {
        @Override
        public void migrationLogging(String sql)
        {
            LOG.debug(sql);
        }
    }

    public static class FlexSqlLogger extends ru.naumen.migration.server.jdbclogger.DataSourceWrapper.SqlLogger
    {
        public FlexSqlLogger(
                ru.naumen.migration.server.jdbclogger.DataSourceWrapper.MigrationLoggerImpl migrationLogger)
        {
            super(migrationLogger);
        }

        @Override
        public boolean isQueryIgnored(String sql)
        {
            return false;
        }
    }

    private static final long serialVersionUID = -1668059042938961244L;

    private static final Logger LOG = LoggerFactory.getLogger(FlexSessionFactoryBuilder.class);
    private static final String UPDATE = "update";

    @Inject
    protected MetainfoService metainfoService;
    @Inject
    protected MetainfoUtils metainfoUtils;
    @Inject
    protected MetaInfoHelper metainfoHelper;
    @Inject
    protected AttrStrategyFactory attrStrategyFactory;
    @Inject
    protected PrefixObjectLoaderRegistryImpl prefixObjectLoaderRegistry;
    @Inject
    protected FlexHelper flexHelper;
    @Inject
    protected ClusterInfoService clusterInfoService;
    @Value("${db.startup.drop_unnecessary_tables}")
    protected Boolean isDropUnnecessaryTables;

    private List<SchemaUpdater> updaters;
    private Set<Class<?>> scanAnnotatedClassesCache = new HashSet<>();
    private Set<String> scanPackageNamesCache = new HashSet<>();
    private boolean scanPackagesActive = false;
    @Inject
    private SchemaOptimizationFeatureConfiguration schemaOptimizationFeatureConfiguration;
    @Inject
    @Lazy
    private MetainfoImportHelper metainfoImportHelper;
    @Inject
    private CasesEntitiesBuilder casesEntitiesBuilder;
    @Inject
    private MessageFacade messages;
    @Value("${ru.naumen.schema.ddl.timeout}")
    protected int ddlOperationsTimeoutInMs;
    @Value("${ru.naumen.cluster.skipSchemaUpdate.enabled}")
    private boolean skipSchemaUpdateEnabled;

    @Inject
    private ClusterServiceManager clusterServiceManager;

    protected ClassPool classPool;

    private boolean isDropUnnecessaryTables()
    {
        return Objects.requireNonNullElseGet(isDropUnnecessaryTables, () -> !clusterInfoService.isAnyClusterMode());
    }

    public FlexSessionFactoryBuilder(TransactionalDataSource dataSource, ResourceLoader resourceLoader,
            ComplexInterceptor interceptor, DataBaseInfo dataBaseInfo, ClassLoaderDetails details)
    {
        this(dataSource, resourceLoader, interceptor, SessionFactoryType.FLEX, dataBaseInfo, details);
    }

    public FlexSessionFactoryBuilder(TransactionalDataSource dataSource, ResourceLoader resourceLoader,
            ComplexInterceptor interceptor, SessionFactoryType sessionFactoryType, DataBaseInfo dataBaseInfo,
            ClassLoaderDetails details)
    {
        super(dataSource, resourceLoader, sessionFactoryType, dataBaseInfo, details.getClassLoader());
        setInterceptor(interceptor);
        setStatementInspector(interceptor);
        this.classPool = details.getClassPool();
    }

    @Override
    public Configuration addAnnotatedClass(Class annotatedClass)
    {
        if (scanPackagesActive)
        {
            scanAnnotatedClassesCache.add(annotatedClass);
        }
        return super.addAnnotatedClass(annotatedClass);
    }

    @Override
    public LocalSessionFactoryBuilder addAnnotatedClasses(Class<?>... annotatedClasses)
    {
        scanAnnotatedClassesCache.addAll(Arrays.asList(annotatedClasses));
        return super.addAnnotatedClasses(annotatedClasses);
    }

    @Override
    public Configuration addPackage(String packageName) throws MappingException
    {
        if (scanPackagesActive)
        {
            scanPackageNamesCache.add(packageName);
        }
        return super.addPackage(packageName);
    }

    @Override
    public SessionFactoryImpl buildSessionFactory() throws HibernateException
    {
        //Исключение выбрасывается в том случае, если SessionFactory (FlexSessionFactoryBean) пытается подняться до
        // того,
        //как проинициализирован сервис метаинформации.
        //Это возникает, когда во время инициализации сервиса метаинформации используются классы, в которых
        // инжектится SessionFactory - это ошибка.
        // необходимо в данном случае использовать конструкцию вида:
        //(SessionFactory)beanFactory.getBean("sessionFactory")
        if (!metainfoService.isInitialized())
        {
            throw new IllegalStateException("Metainfoservice is not initialized");
        }
        return super.buildSessionFactory();
    }

    @SuppressWarnings("MethodMayBeStatic") // Для Mock в тестах
    public String getSchema(SessionFactory sessionFactory)
    {
        SessionFactoryImplementor sfi = (SessionFactoryImplementor)sessionFactory;
        return sfi.getSessionFactoryOptions().getDefaultSchema();
    }

    @Override
    public SessionFactory buildSessionFactory(ServiceRegistry serviceRegistry) throws HibernateException
    {
        initSidObjectClasses();

        final Map<String, List<CtClass>> classMappings = initCases();
        classMappings.values().stream()
                .flatMap(Collection::stream)
                .filter(CollectionUtils.distinct(CtClass::getName))
                .map((CtClass cc) -> SourceGenHelper.toClass(cc, classLoader))
                .filter(annotatedClass -> !IGeneratedEmbedded.class.isAssignableFrom(annotatedClass))
                .forEach(this::addAnnotatedClass);

        initStateResponsibleEvents();

        SessionFactory sessionFactory = buildSessionFactorySuper(serviceRegistry);
        if (!AppContext.isReadOnly() && !AppContext.denyDbSchemaModification() && UPDATE.equals(getProperty(
                SchemaToolingSettings.JAKARTA_HBM2DDL_DATABASE_ACTION + "_int")))
        {
            updateDatabaseSchema(getSchema(sessionFactory));
        }
        return sessionFactory;
    }

    @Override
    public LocalSessionFactoryBuilder scanPackages(String... packagesToScan) throws HibernateException
    {
        if (scanAnnotatedClassesCache.isEmpty() || scanPackageNamesCache.isEmpty())
        {
            try
            {
                scanPackagesActive = true;
                return super.scanPackages(packagesToScan);
            }
            finally
            {
                scanPackagesActive = false;
            }
        }

        for (Class<?> clazz : scanAnnotatedClassesCache)
        {
            addAnnotatedClass(clazz);
        }
        for (String packageName : scanPackageNamesCache)
        {
            addPackage(packageName);
        }
        return this;
    }

    public void setAnnotatedClassesCache(Set<Class<?>> annotatedClassesCache)
    {
        this.scanAnnotatedClassesCache = annotatedClassesCache;
    }

    public void setPackageNamesCache(Set<String> packageNamesCache)
    {
        this.scanPackageNamesCache = packageNamesCache;
    }

    public void setUpdaters(List<SchemaUpdater> updaters)
    {
        this.updaters = updaters;
    }

    @SuppressWarnings("PMD.CloseResource")
    public void updateDatabaseSchema(String currentSchema) throws DataAccessException
    {
        LOG.info("Updating database schema for Hibernate SessionFactory");

        try (Connection connection = getConnectionForSchemaUpdate())
        {
            // Только хозяин схемы будет запускать DDL-команды
            if (!connection.getSchema().equalsIgnoreCase(currentSchema))
            {
                return;
            }

            Connection currentConnection = connection;
            // Выставление коннекту схемы возможно только в Postgres - в Oracle и Mssql нет возможности
            if (dataBaseInfo.isPostgres())
            {
                currentConnection.setSchema(currentSchema);
            }
            if (connection instanceof AbstractConnectionWrapper)
            {
                currentConnection = ((AbstractConnectionWrapper)currentConnection).getWrappedConnection();
            }

            try (SessionFactorySchemaUpdaterResource updaterResource = getSessionFactorySchemaUpdater(
                    currentConnection))
            {
                DDLTool ddlTool = new DDLTool(updaterResource.getCachedConnection());
                Optional<Integer> previousDdlTimeout = setDefaultDDLLockTimeout(ddlTool);
                if (isSchemaUpdateNeeded())
                {
                    LOG.info("Generating schema update script");

                    updaterResource.getSchemaUpdater().updateSchema(updaterResource.getCachedConnection(), updaters);
                }
                else
                {
                    LOG.info(
                            "Skipping generating schema update script because there are already working nodes in "
                            + "cluster");
                }
                dropUnnecessary(ddlTool);
                if (previousDdlTimeout.isPresent())
                {
                    ddlTool.setDdlLockTimeout(previousDdlTimeout.get());
                }
            }
        }
        catch (SQLException e)
        {
            if (isTimeoutLockException(e, dataBaseInfo.getDbType()))
            {
                throw new TimeoutLockSQLRuntimeException(messages.getMessage("databaseAcquireLockTimeoutError"), e);
            }
            throw new FxException(e);
        }
    }

    /**
     * Необходимо ли обновление схемы
     * Проверяет значение параметра в конфигурации и условие, что уже есть запущенная нода в кластере
     * @return true, если есть необходимость обновления схемы
     */
    private boolean isSchemaUpdateNeeded()
    {
        if (!skipSchemaUpdateEnabled)
        {
            return true;
        }
        View view = clusterServiceManager.getCurrentView();
        return view.getViewId() == null || view.size() < 2;

    }

    /**
     * Установить таймаут на получение блокировки для DDL изменений в значение по умолчанию,
     * которое задано в dbaccess
     *
     * @return старое значение таймаута, если оно есть.
     */
    private Optional<Integer> setDefaultDDLLockTimeout(DDLTool ddlTool) throws SQLException
    {
        if (ddlOperationsTimeoutInMs > 0)
        {
            Integer previousValue = ddlTool.getDdlLockTimeoutInMs();
            // если нам не удалось узнать предыдущее значение, то никаких изменений не производим
            if (previousValue != null)
            {
                ddlTool.setDdlLockTimeout(ddlOperationsTimeoutInMs);
                return Optional.of(previousValue);
            }
            LOG.warn("Failed to find the previous timeout value.");
        }
        return Optional.empty();
    }

    /**
     * Отвечает на вопрос: возникла ли переданная ошибка из-за таймаута на взятие блокировки.
     *
     * @param sqlException ошибка при работе с базой данных;
     * @param dbType       тип СУБД;
     * @return возникла ли переданная ошибка из-за таймаута на взятие блокировки
     */
    private static boolean isTimeoutLockException(SQLException sqlException, DbType dbType)
    {
        return // https://www.postgresql.org/docs/13/errcodes-appendix.html
                (dbType == DbType.POSTGRES && sqlException instanceof PSQLException && "55P03".equals(
                        sqlException.getSQLState())) ||
                // https://docs.microsoft.com/en-us/sql/relational-databases/errors-events/mssqlserver-1222-database-engine-error?view=sql-server-ver15
                (dbType == DbType.MSSQL && sqlException.getErrorCode() == 1222) ||
                // http://www.dba-oracle.com/t_ora_00054_locks.htm
                (dbType == DbType.ORACLE && sqlException.getErrorCode() == 54 && "61000".equals(
                        sqlException.getSQLState()));
    }

    /**
     * Записать в табличку tbl_sys_schema_optimization информацию о количестве колонок/таблиц, которые были
     * переименованы для последующего удаления
     */
    private static void updateCountObjectsForDelete(DDLTool ddlTool, int count)
    {
        try
        {
            final Integer oldCount = ddlTool.executeStatement(
                    "select count from tbl_sys_schema_optimization", List.of(),
                    new Function<>()
                    {
                        @Nullable
                        @Override
                        public Integer apply(@Nullable ResultSet input) throws SQLException
                        {
                            if (input == null || !input.next()) //NOSONAR
                            {
                                return null;
                            }
                            return input.getInt(1);
                        }
                    });

            if (oldCount == null)
            {
                LOG.error("Table tbl_sys_schema_optimization not initialized!");
                return;
            }
            ddlTool.executeUpdate(
                    String.format("update tbl_sys_schema_optimization set count=%d",
                            count + oldCount));
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    protected void initSidObjectClasses()
    {
        for (MetaClass cls : metainfoService.getMetaClasses())
        {
            ClassFqn fqn = cls.getFqn();
            if (Boolean.TRUE.equals(cls.isHardcoded()) || !StringUtilities.isEmptyTrim(fqn.getCase()))
            {
                continue;
            }

            Class<? extends IUUIDIdentifiable> userEntityClass = (Class)metainfoService.getJavaClass(fqn);
            if (null == userEntityClass)
            {
                userEntityClass = metainfoHelper.initJavaClass(cls);
            }
            LOG.debug("Register Entity {}", userEntityClass);
            addAnnotatedClass(userEntityClass);
            prefixObjectLoaderRegistry.addDefaultLoader(userEntityClass, fqn);
        }

        //remove unnecessary loaders
        for (Map.Entry<String, ClassFqn> entry : prefixObjectLoaderRegistry.getDefaultLoaderPrefixes().entrySet())
        {
            String prefix = entry.getKey();
            ClassFqn fqn = entry.getValue();
            if (fqn != null && !metainfoService.isMetaclassExists(fqn))
            {
                prefixObjectLoaderRegistry.remove(prefix);
            }
        }
    }

    protected void initStateResponsibleEvents()
    {
        MetaClass abstractEventMetaClass = metainfoService.getMetaClass(Constants.AbstractStateResponsibleEvent.FQN);
        for (ClassFqn fqn : abstractEventMetaClass.getChildren())
        {
            addAnnotatedClass(metainfoService.getJavaClass(fqn));
        }
    }

    @Override
    protected void setDataSource(DataSource dataSource)
    {
        this.dataSource = new WrappedDataSource(dataSource, new FlexSqlLogger(new FlexLoggerImpl()));
    }

    SessionFactory buildSessionFactorySuper(ServiceRegistry serviceRegistry)
    {
        return super.buildSessionFactory(serviceRegistry);
    }

    /**
     * Удалить неиспользуемые объекты (колонки и таблицы)
     */
    protected void dropUnnecessary(DDLTool ddlTool)
    {
        final boolean deferredDeletionEnabled = isDeferredDeletionEnabled();
        int countDelete = dropUnnecessaryColumns(ddlTool, deferredDeletionEnabled);
        if (isDropUnnecessaryTables())
        {
            countDelete += dropUnnecessaryTables(ddlTool, deferredDeletionEnabled);
        }

        if (deferredDeletionEnabled && countDelete > 0)
        {
            updateCountObjectsForDelete(ddlTool, countDelete);
        }
    }

    /**
     * Удаляет колонки в таблицах которые не зарегистрированы в hibernate-маппинге. Должна вызываться после
     * регистрации информации о пользовательских атрибутах.
     *
     * @param deferredDeletionEnabled разрешено ли использовать отложенное удаление 1. Должно быть включено в
     *                                настройках 2. Выполняться процесс может только в рамках импорта метаинформации;
     * @return количество колонок, которые нужно удалить (число отличается от нуля, если включено отложенное удаление);
     */
    private int dropUnnecessaryColumns(DDLTool tool, boolean deferredDeletionEnabled)
    {
        LOG.info("Dropping unnecessary columns");
        String defaultSchema = getProperty(MappingSettings.DEFAULT_SCHEMA);

        try
        {
            Set<String> ignoredTableNames = getIgnoredTables(tool, dataBaseInfo.getSchema());
            Iterator<Table> it = getMetadata().collectTableMappings().iterator();
            int countForDelete = 0;
            while (it.hasNext())
            {
                Table table = it.next();
                String catalog = null == table.getCatalog() ? null : table.getCatalog().toLowerCase();
                String schema = null == table.getSchema() ? defaultSchema : table.getSchema().toLowerCase();
                String tableName = table.getName().toLowerCase();
                if (ignoredTableNames.contains(tableName))
                {
                    continue;
                }
                LOG.debug("Process table {}", tableName);

                // получаем список колонок которые должны существовать после построения sessionFactory
                SortedSet<String> registeredColumns = getRegisteredColumns(table);
                boolean isDebugEnabled = LOG.isDebugEnabled();
                if (isDebugEnabled)
                {
                    LOG.debug("Discovery registered columns: {}", registeredColumns);
                }

                // обрабатываем колонки существующие в БД и удаляем ненужные
                Set<String> existsColumns = tool.getColumns(catalog, schema, tableName);
                existsColumns.removeIf(column -> column.equalsIgnoreCase(SYS_MIGRATED_COLUMN));

                if (isDebugEnabled)
                {
                    LOG.debug("Discovery existed columns: {}", existsColumns);
                }

                final SetView<String> columnsForDelete = difference(existsColumns, registeredColumns);
                // Если включено отложенное удаление, то просто переименовываем колонки, иначе удаляем пачкой
                if (deferredDeletionEnabled)
                {
                    countForDelete += renameColumns(tool, tableName, schema, columnsForDelete);
                }
                else
                {
                    tool.dropColumns(schema, tableName, columnsForDelete, true);
                }
            }
            return countForDelete;
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    private static SortedSet<String> getRegisteredColumns(Table table)
    {
        SortedSet<String> registeredColumns = new TreeSet<>();
        @SuppressWarnings("unchecked")
        Iterator<Column> columnIterator = table.getColumns().iterator();
        while (columnIterator.hasNext())
        {
            String columnName = columnIterator.next().getName().toLowerCase();
            registeredColumns.add(columnName);
        }
        return registeredColumns;
    }

    /**
     * Удаляет таблицы более не описанные в sessionFactory
     *
     * @param ddlTool                 соединение в рамках которого нужно проводить изменения;
     * @param deferredDeletionEnabled разрешено ли использовать отложенное удаление;
     * @return количество таблиц, которые нужно удалить (отлично от 0 только при включенном отложенном удалении)
     * @see #isDeferredDeletionEnabled()
     */
    private int dropUnnecessaryTables(DDLTool ddlTool, boolean deferredDeletionEnabled)
    {
        boolean isDebugEnabled = LOG.isDebugEnabled();
        LOG.info("Dropping unnecessary tables");
        String defaultSchema = getProperty(MappingSettings.DEFAULT_SCHEMA);
        try
        {
            SortedSet<String> hbmTableNames = new TreeSet<>();
            hbmTableNames.add("tbl_sys_metastorage"); // таблица относится к другой sessionFactory
            hbmTableNames.add("tbl_sys_task_date"); // таблица относится к другой sessionFactory
            hbmTableNames.add("tbl_sys_reportstorage"); // таблица относится к другой sessionFactory
            hbmTableNames.add("tbl_sys_cluster_lock"); // таблица относится к другой sessionFactory
            hbmTableNames.add("tbl_sys_returned_values"); //Таблица не управляется через Hibernate.
            hbmTableNames.add("tbl_event"); // таблица может относиться к другой sessionFactory
            hbmTableNames.add("tbl_event_category"); // таблица может относиться к другой sessionFactory
            hbmTableNames.add("tbl_sys_schema_optimization"); // таблица относится к другой sessionFactory
            hbmTableNames.add("tbl_sys_reindex_state_data"); // таблица относится к другой sessionFactory
            // добавить таблицы с данными метаинфы
            hbmTableNames.addAll(ExtMetainfoDataProvider.getAllTablesNames());

            for (Table table : getMetadata().collectTableMappings())
            {
                hbmTableNames.add(table.getName().toLowerCase());
            }
            if (isDebugEnabled)
            {
                LOG.debug("Discovery registered tables: {}", hbmTableNames);
            }

            Map<String, TableMetaData> partitionTables = new HashMap<>();
            ddlTool.getExistingPartitionTablesMetaData(partitionTables);
            hbmTableNames.addAll(partitionTables.keySet());

            Set<String> systemCheckTables = ddlTool.getTables(
                    defaultSchema, SystemChecker.CHECK_DB_TABLE_NAME + '%');
            // обрабатываем колонки существующие в БД и удаляем ненужные
            Set<String> existsTables = ddlTool.getTables(defaultSchema, "tbl_%")
                    .stream()
                    .filter(e -> !systemCheckTables.contains(e))
                    .filter(e -> !e.endsWith(OLD_SUFFIX) && !e.endsWith(PART_SUFFIX))
                    .collect(Collectors.toSet());
            if (isDebugEnabled)
            {
                LOG.debug("Discovery existed tables: {}", existsTables);
            }

            Set<String> tablesToDelete = difference(existsTables, hbmTableNames);
            tablesToDelete = tablesToDelete.stream()
                    .filter(getTablesToDeleteFilter())
                    .collect(Collectors.toSet());
            int countForDelete = 0;
            for (String tableName : tablesToDelete)
            {
                if (deferredDeletionEnabled)
                {
                    countForDelete = renameTableForDeferredDeletion(
                            tableName, defaultSchema, ddlTool, countForDelete);
                }
                else
                {
                    ddlTool.dropTable(defaultSchema, tableName);
                }
            }
            return countForDelete;
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Определим, если таблица для атрибута - то переименуем ее и все ее колонки/индексы/ограничения, иначе удалим
     */
    private static int renameTableForDeferredDeletion(String tableName, String defaultSchema,
            DDLTool tools, int countForDelete) throws SQLException
    {
        if (tableName.endsWith(DELETE_SIGN))
        {
            return countForDelete;
        }
        final SortedSet<String> columns = tools.getColumns(null, defaultSchema, tableName); //NOSONAR
        // ищем НБО, где всегда 2 колонки и название хоть одной заканчивается на _id (у НБО и списка справочников
        // оба _id, а вот у набора типов классов только одна.
        if (columns.size() == 2 && columns.stream().anyMatch(column -> column.endsWith("_id")))
        {
            // сначала переименуем колонки/индексы/ограничения, а потом саму табличку
            renameColumns(tools, tableName, defaultSchema, columns);
            tools.renameTable(tableName, getUniqueNameForRemove(tools::tableExists, tableName, DELETE_SIGN));
            countForDelete++;
        }
        else
        {
            tools.dropTable(defaultSchema, tableName);
        }
        return countForDelete;
    }

    /**
     * Разрешено ли использовать отложенное удаление
     * <ol>
     * <li>Должно быть включено в настройках</li>
     * <li>Выполняться процесс может только в рамках импорта метаинформации</li>
     * </ol>
     */
    private boolean isDeferredDeletionEnabled()
    {
        return schemaOptimizationFeatureConfiguration.isDbDeferredDeletionEnabled()
               && metainfoImportHelper.getIsMetainfoImportLocked().get();
    }

    protected Predicate<String> getTablesToDeleteFilter()
    {
        return tableName -> !tableName.endsWith(PLANNED_VERSION_POSTFIX);
    }

    /**
     * Инициализирует sessionFactory Типами Классов.
     * <p>
     * На каждый Тип регистрируется свой entity. Это позволяет иметь в каждом типе свои пользовательские атрибуты.
     * в разных Типах одного Класса могут быть пользовательские атрибуты с одинаковыми кодами
     * не определяем javaClass для типов т.к. объект может изменить свой тип
     */
    protected Map<String, List<CtClass>> initCases()
    {
        return casesEntitiesBuilder.build(classPool, sessionFactoryType);
    }
}