package ru.naumen.core.server.jta.readonly;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.server.AppContext;
import ru.naumen.sec.server.session.CurrentSessionProvider;

/**
 * Конфигурация @{@link ReadOnlyInvocationPermissionManager}
 *
 * <AUTHOR>
 * @since 26.03.19
 */
@Configuration
public class ReadOnlyPermissionManagersConfiguration
{
    public static final String REST_CHECKER_ID = "restChecker";
    public static final String DISPATCH_CHECKER_ID = "dispatchChecker";
    private static final Logger LOG = LoggerFactory.getLogger(ReadOnlyPermissionManagersConfiguration.class);

    @Bean(DISPATCH_CHECKER_ID)
    ReadOnlyInvocationPermissionManager dispatchPermissionManager(
            @Value("${ru.naumen.read_only.dispatch.percent}") Integer dispatchReadOnlyPercentage,
            ReplicationConfiguration replicationConfiguration, CurrentSessionProvider sessionProvider)
    {
        int percentage = dispatchReadOnlyPercentage;
        if (percentage == 0 && AppContext.isTestingEnvironment())
        {
            percentage = 50;
        }
        LOG.info("Dispatch read only percentage {}", percentage);
        return new DispatchReadOnlyPermissionManager(dispatchReadOnlyPercentage, replicationConfiguration,
                sessionProvider);
    }

    @Bean(REST_CHECKER_ID)
    ReadOnlyInvocationPermissionManager restPermissionManager(
            @Value("${ru.naumen.read_only.rest.percent}") Integer restPermittedPercentage)
    {
        int percentage = restPermittedPercentage;
        if (percentage == 0 && AppContext.isTestingEnvironment())
        {
            percentage = 50;
        }
        LOG.info("Rest read only percentage {}", percentage);
        return new RestReadOnlyPermissionManager(restPermittedPercentage);
    }

}
