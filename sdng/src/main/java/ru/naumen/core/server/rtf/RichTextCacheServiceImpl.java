package ru.naumen.core.server.rtf;

import static ru.naumen.core.server.dispatch.AbstractGetDtObjectTemplateActionHandler.TEMPLATE_UUID;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;

import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.InfinispanConfiguration;

/**
 * Реализация {@link RichTextCacheService}
 *
 * <AUTHOR>
 * @since 8/4/22
 */
@Component
public class RichTextCacheServiceImpl implements RichTextCacheService
{
    private static final Logger LOG = LoggerFactory.getLogger(RichTextCacheServiceImpl.class);
    @Resource(name = InfinispanConfiguration.RTF_SERVLET_CACHE)
    private Cache<String, Map<String, Object>> cache;
    @Resource(name = InfinispanConfiguration.RTF_SERVLET_CACHE_FOR_INNER_SOURCE)
    private Cache<String, List<String>> cacheSource;
    @Value("${ru.naumen.cache.rtf.enable}")
    private boolean rtfCacheEnable;
    @Value("${ru.naumen.cache.rtf.workWithCache.enable}")
    private boolean rtfWorkWithCacheEnable;
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    @Override
    public void deleteFromCache(String objUuid)
    {
        if (!rtfCacheEnable || !rtfWorkWithCacheEnable)
        {
            return;
        }
        try
        {
            lock.writeLock().lock();
            LOG.debug("Remove RTF from cache for {}", objUuid);
            cache.remove(objUuid);
        }
        finally
        {
            lock.writeLock().unlock();
        }
    }

    @Override
    public void deleteFromCache(String objUuid, String attrCode)
    {
        if (!rtfCacheEnable || !rtfWorkWithCacheEnable)
        {
            return;
        }
        try
        {
            lock.writeLock().lock();
            Map<String, Object> valueMap = cache.get(objUuid);
            if (valueMap != null)
            {
                LOG.debug("Remove RTF from cache for {}#{}", objUuid, attrCode);
                valueMap.remove(attrCode);
            }
        }
        finally
        {
            lock.writeLock().unlock();
        }
    }

    @Override
    public void deleteFromCache(String uuid, Collection<String> attributes)
    {
        if (!attributes.isEmpty())
        {
            attributes.forEach(attribute -> deleteFromCache(uuid, attribute));
        }
        else
        {
            deleteFromCache(uuid);
        }
    }

    @Override
    public void deleteFromCacheByAttributeCode(String fqn, String attrCode)
    {
        if (!rtfCacheEnable || !rtfWorkWithCacheEnable)
        {
            return;
        }
        try
        {
            lock.writeLock().lock();
            cache.forEach((key, value) ->
            {
                if (key.startsWith(fqn))
                {
                    LOG.debug("Remove RTF from cache for {}#{}", key, attrCode);
                    value.remove(attrCode);
                }
            });
        }
        finally
        {
            lock.writeLock().unlock();
        }
    }

    @Nullable
    public Object getValueFromCache(String objUuid, String attrCode)
    {
        if (!rtfCacheEnable || !rtfWorkWithCacheEnable)
        {
            return null;
        }
        try
        {
            lock.readLock().lock();
            Map<String, Object> valueMap = cache.get(objUuid);
            if (valueMap != null)
            {
                LOG.debug("Get RTF from cache for {}#{}", objUuid, attrCode);
                return valueMap.get(attrCode);
            }

            return null;
        }
        finally
        {
            lock.readLock().unlock();
        }
    }

    @Override
    public void putValueToCache(String objUuid, String attrCode, @Nullable Object value, List<String> objectsLink)
    {
        if (!rtfCacheEnable || !rtfWorkWithCacheEnable || objUuid.equals(TEMPLATE_UUID) || StringUtilities.isEmpty(
                (String)value))
        {
            return;
        }
        try
        {
            lock.writeLock().lock();
            LOG.debug("Put RTF to cache for {}#{}", objUuid, attrCode);
            Map<String, Object> valueMap = cache.get(objUuid);
            if (valueMap != null)
            {
                valueMap.put(attrCode, value);
                putToCacheSource(objectsLink, objUuid);
            }
            else
            {
                cache.put(objUuid, Maps.newHashMap(Map.of(attrCode, value)));
                putToCacheSource(objectsLink, objUuid);
            }
        }
        finally
        {
            lock.writeLock().unlock();
        }
    }

    private void putToCacheSource(List<String> objectsList, String uuidObject)
    {
        objectsList.forEach(e ->
        {
            cacheSource.putIfAbsent(e, new ArrayList<>());
            cacheSource.get(e).add(uuidObject);
        });
    }

    @Override
    public boolean isRtfWorkWithCacheEnable()
    {
        return rtfWorkWithCacheEnable;
    }

    @Override
    public void setRtfWorkWithCacheEnable(boolean rtfWorkWithCacheEnable)
    {
        this.rtfWorkWithCacheEnable = rtfWorkWithCacheEnable;
    }
}