package ru.naumen.core.server.flex.spi;

import static ru.naumen.core.server.flex.FlexHelper.INDEX_PREFIX;
import static ru.naumen.core.server.flex.spi.SessionFactorySchemaUpdater.isNotDbEntity;
import static ru.naumen.core.server.partition.impl.PartitionTableServiceImpl.VIEW;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import jakarta.transaction.RollbackException;
import jakarta.transaction.SystemException;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.dbcp2.DelegatingConnection;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.cfg.Configuration;
import org.hibernate.dialect.Dialect;
import org.hibernate.mapping.Column;
import org.hibernate.mapping.ForeignKey;
import org.hibernate.mapping.Index;
import org.hibernate.mapping.Selectable;
import org.hibernate.mapping.Table;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.hibernate.CreateIndexQueryProperties;
import ru.naumen.core.server.hibernate.DDLDialect;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.PlannedVersionDDLTool;
import ru.naumen.core.server.hibernate.constraint.Constraint;
import ru.naumen.core.server.hibernate.hbm2ddl.CachedMetaDataConnection;
import ru.naumen.core.server.hibernate.hbm2ddl.IndexMetaData;
import ru.naumen.core.server.hibernate.hbm2ddl.TableMetaData;
import ru.naumen.core.server.jta.ds.CommitSynchronization;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.server.util.StopWatchFactory;

/**
 * Сервис по обновлению схемы БД.
 * Умеет генерировать запросы и выполнять их.
 *
 * <AUTHOR>
 * @since 17.08.2022
 */
@Component
public class SchemaUpdateScriptService
{
    private static final Logger LOG = LoggerFactory.getLogger(SchemaUpdateScriptService.class);
    /**
     * ORA-01408: этот список столбцов уже индексирован
     */
    private static final int ALREADY_INDEXED_ERROR_CODE = 1408;
    /**
     * ORA-02275: такое ссылочное ограничение уже есть в таблице
     */
    private static final int CONSTRAINT_ALREADY_EXISTS_ERROR_CODE = 2275;

    private final ExecutorService indexExecutorService = Executors.newSingleThreadExecutor(r ->
    {
        final Thread thread = new Thread(r);
        thread.setName("index-thread #" + thread.threadId());
        return thread;
    });
    private final ExecutorService constraintExecutorService = Executors.newSingleThreadExecutor(r ->
    {
        final Thread thread = new Thread(r);
        thread.setName("constraint-thread #" + thread.threadId());
        return thread;
    });
    private final ConfigurationProperties configurationProperties;
    private final TransactionalDataSource dataSource;
    private final DataBaseInfo dataBaseInfo;
    private final ForbiddenIndexRegistry forbiddenIndexRegistry;
    private final DDLDialect ddlDialect;
    private final TransactionManager transactionManager;

    private final Queue<SchemaUpdateScript> deferredScripts = new ConcurrentLinkedQueue<>();

    @Inject
    public SchemaUpdateScriptService(
            ConfigurationProperties configurationProperties,
            TransactionalDataSource dataSource,
            DataBaseInfo dataBaseInfo,
            ForbiddenIndexRegistry forbiddenIndexRegistry,
            DDLDialect ddlDialect,
            TransactionManager transactionManager)
    {
        this.configurationProperties = configurationProperties;
        this.dataSource = dataSource;
        this.dataBaseInfo = dataBaseInfo;
        this.forbiddenIndexRegistry = forbiddenIndexRegistry;
        this.ddlDialect = ddlDialect;
        this.transactionManager = transactionManager;
    }

    /**
     * Создать индексы в БД.
     * Если конкурентное построение индексов включено, то выполнит скрипт в отдельном потоке,
     * если выключено, выполнит в текущем потоке.
     */
    public void createIndexes(CachedMetaDataConnection connection,
            List<Table> tablesFromMapping) throws SQLException
    {
        if (configurationProperties.isCreateIndexesConcurrently())
        {
            createIndexesConcurrently(tablesFromMapping);
        }
        else
        {
            generateAndExecuteIndexes(tablesFromMapping, connection);
        }
    }

    /**
     * Сгенерировать скрипт создания констрейнта
     */
    @SuppressWarnings("MethodMayBeStatic")
    public SchemaUpdateScript generateCreateConstraintScript(String tableName, Constraint constraint)
    {
        String createConstraintQuery = DDLTool.getCreateConstraintQuery(
                tableName, constraint);
        LOG.debug("Add column constraint SQL: {}", createConstraintQuery);
        return new SchemaUpdateScript(createConstraintQuery, false);
    }

    /**
     * Сгенерировать запрос создания внешнего ключа
     */
    public SchemaUpdateScript generateCreateForeignKeyScript(ForeignKey foreignKey,
            SqlStringGenerationContext context, String tableCatalog, String tableSchema)
    {
        boolean validateForeignKeyConcurrently = configurationProperties.isValidateForeignKeyConcurrently();
        String sqlCreate = ddlDialect.getCreateForeignKeyQuery(
                foreignKey, context, tableCatalog, tableSchema,
                !validateForeignKeyConcurrently);
        LOG.debug("Add create foreign key SQL: {}", sqlCreate);
        if (validateForeignKeyConcurrently)
        {
            String validateQuery = ddlDialect.getValidateForeignKeyQuery(
                    foreignKey, context, tableCatalog, tableSchema);
            if (StringUtils.isNotEmpty(validateQuery))
            {
                addDeferredScript(new SchemaUpdateScript(validateQuery, true));
            }
        }
        return new SchemaUpdateScript(sqlCreate, false);
    }

    /**
     * Сгенерировать скрипт создания индекса.<br>
     * В случае если включено свойство параллельности (для Oracle),
     * то сгенерирует второй запрос на отключение параллельности.
     *
     * @param indexProperties параметры для создания индекса
     * @param isQuiet тихий режим для скрипта
     */
    public List<SchemaUpdateScript> generateCreateIndexScript(CreateIndexQueryProperties indexProperties,
            boolean isQuiet)
    {
        /*
         Для конкурентного построения индекса делаем "тихий" режим выполнения скриптов,
         чтобы ошибка в скрипте не останавливала создание других индексов
        */
        String creationQuery = ddlDialect.getCreateIndexQuery(indexProperties);
        if (LOG.isDebugEnabled())
        {
            LOG.debug("Add {} index SQL: {}",
                    (indexProperties.isUnique() ? "unique " : ""), creationQuery);
        }
        SchemaUpdateScript createIndexScript = new SchemaUpdateScript(creationQuery, isQuiet);

        if (!indexProperties.isParallel())
        {
            return List.of(createIndexScript);
        }
        String disableParallelismQuery = ddlDialect.getAlterIndexDisableParallelismQuery(
                indexProperties.getIndexName());
        if (StringUtils.isNotEmpty(disableParallelismQuery))
        {
            LOG.debug("Alter index SQL: {}", disableParallelismQuery);
            /* Индекс по таким же колонкам может уже существовать, но называться по другому,
               поэтому делаем тихий режим, т.е. приложение не должно из-за этого падать */
            SchemaUpdateScript disableParallelismScript =
                    new SchemaUpdateScript(disableParallelismQuery, true);
            return List.of(createIndexScript, disableParallelismScript);
        }
        return List.of(createIndexScript);
    }

    /**
     * Сгенерировать скрипт переименования индекса
     */
    public SchemaUpdateScript generateRenameIndexScript(String tableName, String oldIndexName, String newIndexName,
            boolean isQuiet)
    {
        String renameQuery = ddlDialect.getRenameIndexQuery(
                tableName, oldIndexName, newIndexName);
        LOG.debug("Rename index SQL: {}", renameQuery);
        return new SchemaUpdateScript(renameQuery, isQuiet);
    }

    /**
     * Скрипт переименования констрейнта
     */
    public SchemaUpdateScript getRenameConstraintScript(
            String tableName, @Nullable String schema, String oldName, String newName)
    {
        String constraintString = ddlDialect.getRenameConstraintQuery(
                tableName, schema, oldName, newName);
        LOG.debug("Rename column constraint SQL: {}", constraintString);
        return new SchemaUpdateScript(constraintString, false);
    }

    /**
     * Запустить отложенные скрипты в отдельном потоке
     */
    public void runDeferredScriptsConcurrently()
    {
        if (deferredScripts.isEmpty())
        {
            return;
        }
        runOnCommitTransactionOrNow(() -> constraintExecutorService.submit(() ->
        {
            LOG.debug("Executing deferred scripts concurrently");
            try (Connection connection = dataSource.getConnectionForSchemeUpdate(dataBaseInfo))
            {
                CachedMetaDataConnection cachedConnection =
                        new CachedMetaDataConnection(new DelegatingConnection<>(connection));
                executeScripts(cachedConnection, pollDeferredScripts());
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
            LOG.debug("Finished execution deferred scripts concurrently");
        }));
    }

    /**
     * Запустить задачу после коммита транзакции или, если активной транзакции нет,
     * то прямо сейчас.
     */
    private void runOnCommitTransactionOrNow(Runnable task)
    {
        try
        {
            Transaction transaction = transactionManager.getTransaction();
            if (transaction != null)
            {
                transaction.registerSynchronization(new CommitSynchronization()
                {
                    @Override
                    protected void onCommit()
                    {
                        task.run();
                    }
                });
            }
            else
            {
                task.run();
            }
        }
        catch (SystemException | RollbackException | IllegalStateException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Выполнить скрипты
     */
    @SuppressWarnings("MethodMayBeStatic")
    void executeScripts(CachedMetaDataConnection connection,
            @Nullable List<SchemaUpdateScript> scripts)
            throws SQLException
    {
        DDLTool ddlTool = new DDLTool(connection);
        if (CollectionUtils.isNotEmpty(scripts))
        {
            StopWatch sw = StopWatchFactory.create("SchemaUpdateScriptService.executeScripts", LOG.isDebugEnabled());
            try
            {
                for (SchemaUpdateScript script : scripts)
                {
                    String sqlScript = script.script();
                    LOG.debug("Executing schema statement: {}", sqlScript);
                    try
                    {
                        sw.start("Script: " + sqlScript);
                        ddlTool.executeUpdate(sqlScript);
                    }
                    catch (SQLException e)
                    {
                        if (ALREADY_INDEXED_ERROR_CODE == e.getErrorCode()
                            || CONSTRAINT_ALREADY_EXISTS_ERROR_CODE == e.getErrorCode()
                            || script.quiet())
                        {
                            LOG.warn("Error executing '{}': {}", sqlScript, e.getMessage());
                            continue;
                        }
                        throw e;
                    }
                    finally
                    {
                        sw.stop();
                    }
                }
            }
            catch (SQLException ex)
            {
                LOG.warn("Unsuccessful schema batch update: ", ex);

                for (Throwable nextException : ex)
                {
                    LOG.debug(nextException.getMessage(), nextException);
                }
                throw ex;
            }
            finally
            {
                LOG.atDebug().log(sw::prettyPrint);
                connection.clearCache();
            }
        }
    }

    boolean isIndexForbidden(String indexName, String tableName, String[] columnNames)
    {
        if (forbiddenIndexRegistry.hasIndex(tableName, columnNames))
        {
            LOG.warn("Index {} will not be created: index for table {} and columns ({}) is forbidden",
                    indexName, tableName, String.join(",", columnNames));
            return true;
        }
        return false;
    }

    /**
     * Добавить скрипт для отложенного выполнения
     */
    private void addDeferredScript(SchemaUpdateScript schemaUpdateScript)
    {
        deferredScripts.add(schemaUpdateScript);
    }

    public void createSemanticFilterIndexesConcurrently(CreateIndexQueryProperties createIndexQueryProperties)
    {
        runOnCommitTransactionOrNow(() -> indexExecutorService.submit(() ->
        {
            LOG.debug("Executing creation indexes concurrently");
            try (Connection newConnection = dataSource.getConnectionForSchemeUpdate(dataBaseInfo))
            {
                CachedMetaDataConnection cachedConnection =
                        new CachedMetaDataConnection(new DelegatingConnection<>(newConnection));
                new DDLTool(cachedConnection).createIndex(createIndexQueryProperties);
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
            LOG.debug("Finished creation indexes concurrently");
        }));
    }

    private void createIndexesConcurrently(List<Table> tablesFromMapping)
    {
        runOnCommitTransactionOrNow(() -> indexExecutorService.submit(() ->
        {
            LOG.debug("Executing creation indexes concurrently");
            try (Connection newConnection = dataSource.getConnectionForSchemeUpdate(dataBaseInfo))
            {
                CachedMetaDataConnection cachedConnection =
                        new CachedMetaDataConnection(new DelegatingConnection<>(newConnection));
                generateAndExecuteIndexes(tablesFromMapping, cachedConnection);
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
            LOG.debug("Finished creation indexes concurrently");
        }));
    }

    private void generateAndExecuteIndexes(List<Table> tablesFromMapping,
            CachedMetaDataConnection connection) throws SQLException
    {
        List<SchemaUpdateScript> indexesScripts =
                generateIndexesForColumns(new DDLTool(connection), tablesFromMapping);
        executeScripts(connection, indexesScripts);
    }

    /**
     * Генерирует запросы для обновления неуникальных индексов в соответствии с маппингами Hibernate.
     * Расширяет логику {@link Configuration#generateSchemaUpdateScriptList(Dialect, DatabaseMetadata)},
     * при наличия индекса с другим именем, но тем же списком столбцов, создает запрос на переименование,
     * а не на создание нового индекса
     *
     * @return список сгенерированных запросов
     */
    private List<SchemaUpdateScript> generateIndexesForColumns(DDLTool ddlTool,
            List<Table> tablesFromMapping) throws SQLException
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();

        Map<String, TableMetaData> tablesMetaData = new HashMap<>();
        ddlTool.getExistingIndexesMetaData(tablesMetaData);
        for (Table table : tablesFromMapping)
        {
            if (isNotDbEntity(ddlTool, table, VIEW))
            {
                scripts.addAll(generateTableIndexes(table, tablesMetaData));
            }
        }
        return scripts;
    }

    private List<SchemaUpdateScript> generateTableIndexes(Table table,
            Map<String, TableMetaData> tablesMetaData) throws SQLException
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();
        Iterator<Index> indexesIter = table.getIndexes().values().iterator();
        String tableName = DDLTool.getCanonicalIdentifier(table.getName());
        TableMetaData tableInfo = tablesMetaData.get(tableName);

        while (indexesIter.hasNext())
        {
            Index index = indexesIter.next();
            final String indexName;
            if (PlannedVersionDDLTool.isVersionedIdentifier(tableName))
            {
                final String versionedID = PlannedVersionDDLTool.getPlannedVersionIdentifierName(
                        index.getName(), INDEX_PREFIX);
                indexName = DDLTool.getCanonicalIdentifier(versionedID);
            }
            else
            {
                indexName = DDLTool.getCanonicalIdentifier(index.getName());
            }
            if (null != tableInfo
                && null != tableInfo.getIndex(indexName)
                || 0 == index.getColumnSpan())
            {
                continue;
            }

            String[] columnNames = new String[index.getColumnSpan()];
            Iterator<Selectable> columnIter = index.getSelectables().iterator();
            int i = 0;
            while (columnIter.hasNext())
            {
                columnNames[i++] = ((Column)columnIter.next()).getName();
            }

            if (isIndexForbidden(indexName, table.getName(), columnNames))
            {
                continue;
            }

            boolean createIndexesConcurrently = configurationProperties.isCreateIndexesConcurrently();
            /*
             Для конкурентного построения индекса делаем "тихий" режим выполнения скриптов,
             чтобы ошибка в скрипте не останавливала создание других индексов
            */
            @SuppressWarnings("UnnecessaryLocalVariable")
            boolean isQuiet = createIndexesConcurrently;
            IndexMetaData existingIndex = null != tableInfo
                    ? tableInfo.getExistingSameIndex(index) : null;
            if (null != existingIndex)
            {
                // Уникальный индекс имеет приоритет, его не нужно переименовывать.
                // Также не требуется и создавать неуникальный индекс с теми же колонками
                if (existingIndex.isUnique())
                {
                    LOG.warn(
                            "Non-unique index {} will not be created: unique index {} with same columns already exists",
                            indexName, existingIndex.getName());
                    continue;
                }
                scripts.add(
                        generateRenameIndexScript(tableInfo.getName(), existingIndex.getName(), indexName, isQuiet));

                continue;
            }

            scripts.addAll(generateCreateIndexScript(
                    new CreateIndexQueryProperties.Builder(indexName, tableName, List.of(columnNames))
                            .setConcurrently(createIndexesConcurrently)
                            .setParallel(true)
                            .setNoLogging(true)
                            .build(),
                    isQuiet));
        }
        return scripts;
    }

    /**
     * Получить список отложенных скриптов и удалить их из очереди
     */
    private List<SchemaUpdateScript> pollDeferredScripts()
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();

        SchemaUpdateScript script = deferredScripts.poll();
        while (script != null)
        {
            scripts.add(script);
            script = deferredScripts.poll();
        }
        return scripts;
    }
}
