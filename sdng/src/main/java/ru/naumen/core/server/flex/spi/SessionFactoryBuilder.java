package ru.naumen.core.server.flex.spi;

import static ru.naumen.core.server.flex.spi.metadatabuilder.NauSessionFactoryBuilderFactory.ejectMetadata;

import java.io.Serial;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.apache.commons.dbcp2.DelegatingConnection;
import org.hibernate.HibernateException;
import org.hibernate.Interceptor;
import org.hibernate.SessionFactory;
import org.hibernate.SessionFactoryObserver;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.boot.model.relational.internal.SqlStringGenerationContextImpl;
import org.hibernate.boot.registry.StandardServiceRegistry;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl;
import org.hibernate.boot.registry.classloading.spi.ClassLoaderService;
import org.hibernate.boot.registry.internal.BootstrapServiceRegistryImpl;
import org.hibernate.boot.registry.internal.StandardServiceRegistryImpl;
import org.hibernate.boot.spi.MetadataImplementor;
import org.hibernate.bytecode.spi.BytecodeProvider;
import org.hibernate.cfg.SessionEventSettings;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.hibernate.engine.jdbc.spi.JdbcServices;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerGroup;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.AutoFlushEventListener;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.internal.util.config.ConfigurationHelper;
import org.hibernate.resource.transaction.spi.DdlTransactionIsolator;
import org.hibernate.service.ServiceRegistry;
import org.hibernate.tool.schema.extract.spi.DatabaseInformation;
import org.hibernate.tool.schema.internal.Helper;
import org.hibernate.tool.schema.internal.exec.JdbcContext;
import org.hibernate.tool.schema.spi.SchemaManagementTool;
import org.springframework.core.io.ResourceLoader;
import org.springframework.orm.hibernate5.LocalSessionFactoryBuilder;

import jakarta.inject.Inject;
import ru.naumen.core.server.flex.spi.FlexSessionFactoryBuilder.FlexSqlLogger;
import ru.naumen.core.server.flex.spi.classloader.AggregatedClassLoader;
import ru.naumen.core.server.flex.spi.jdbc.CustomJdbcEnvironmentInitiator;
import ru.naumen.core.server.flex.spi.migrationtool.EmptyDdlTransactionIsolatorImpl;
import ru.naumen.core.server.flex.spi.migrationtool.NauJdbcContext;
import ru.naumen.core.server.flex.spi.migrationtool.NauSchemaManagementToolInitiator;
import ru.naumen.core.server.flex.spi.migrationtool.ProvidedConnectionAccess;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.SessionFactoryAware;
import ru.naumen.core.server.hibernate.hbm2ddl.CachedMetaDataConnection;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableBytecodeProviderImpl;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableBytecodeProviderInitiator;
import ru.naumen.core.server.hquery.escape.EscapeFunctionsProvider;
import ru.naumen.core.server.jta.NauBatchBuilderInitiator;
import ru.naumen.migration.server.jdbclogger.DataSourceWrapper.MigrationLoggerImpl;
import ru.naumen.migration.server.jdbclogger.DataSourceWrapper.WrappedDataSource;

/**
 * Переопределяет {@link ClassLoaderService} для {@link SessionFactory}
 * <p>
 * Используется в системе для обращения к метаинформации
 *
 * <AUTHOR>
 */
public class SessionFactoryBuilder extends LocalSessionFactoryBuilder
{
    private static Map<String, Object> propertiesToMap(Properties prop)
    {
        return prop.entrySet().stream().collect(
                Collectors.toMap(
                        e -> String.valueOf(e.getKey()),
                        Entry::getValue,
                        (prev, next) -> next, HashMap::new
                ));
    }

    private static void setupListeners(SessionFactoryImplementor sessionFactory)
    {
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);
        EventListenerGroup<AutoFlushEventListener> listenerGroup = registry.getEventListenerGroup(EventType.AUTO_FLUSH);
        listenerGroup.clearListeners();
        listenerGroup.appendListener(new SkipAutoFlushEventListener());
    }

    @Serial
    private static final long serialVersionUID = 1L;
    private static final AtomicInteger counter = new AtomicInteger();

    public static long getSessionFactoryCount()
    {
        return counter.get();
    }

    protected transient WrappedDataSource dataSource;
    @Inject
    protected transient EscapeFunctionsProvider escapeProvider;

    protected transient DataBaseInfo dataBaseInfo;
    protected SessionFactoryType sessionFactoryType;
    private transient SchemaUpdaterStrategy schemaUpdaterStrategy;
    protected transient ClassLoader classLoader;
    protected transient MetadataImplementor metadata;

    public SessionFactoryBuilder(
            DataSource dataSource,
            ResourceLoader resourceLoader,
            SessionFactoryType sessionFactoryType,
            DataBaseInfo dataBaseInfo,
            ClassLoader classLoader)
    {
        super(dataSource, resourceLoader);
        setDataSource(dataSource);
        setImplicitNamingStrategy(new NauImplicitNamingStrategy());
        this.sessionFactoryType = sessionFactoryType;
        this.dataBaseInfo = dataBaseInfo;
        this.classLoader = classLoader;
        this.schemaUpdaterStrategy = new SchemaUpdaterStrategyImpl();
    }

    @Override
    public SessionFactory buildSessionFactory(ServiceRegistry serviceRegistry) throws HibernateException
    {
        SessionFactory sessionFactory = super.buildSessionFactory(serviceRegistry);
        metadata = ejectMetadata();
        return sessionFactory;
    }

    @Override
    public SessionFactoryImpl buildSessionFactory() throws HibernateException
    {
        ConfigurationHelper.resolvePlaceHolders(getProperties());
        setProperty(SessionEventSettings.AUTO_SESSION_EVENTS_LISTENER, ComplexSessionEventListener.class.getName());

        if (escapeProvider != null)
        {
            addSqlFunction(escapeProvider.getLikeEscapeSqlFunctionName(), escapeProvider.getLikeEscapeSqlFunction());
        }

        final ServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder(
                new BootstrapServiceRegistryImpl(
                        new ClassLoaderServiceImpl(classLoader),
                        new LinkedHashSet<>())
        )
                .applySettings(getProperties())
                .addInitiator(new NauSchemaManagementToolInitiator(
                        dataSource, dataBaseInfo, schemaUpdaterStrategy))
                .addInitiator(CustomJdbcEnvironmentInitiator.INSTANCE)
                .addInitiator(UUIDIdentifiableBytecodeProviderInitiator.INSTANCE)
                .addInitiator(NauBatchBuilderInitiator.INSTANCE)
                .build();

        setSessionFactoryObserver(new SessionFactoryObserver()
        {
            @Serial
            private static final long serialVersionUID = 1L;

            @Override
            public void sessionFactoryCreated(SessionFactory factory)
            {
                //Does nothing
            }

            @Override
            public void sessionFactoryClosed(SessionFactory factory)
            {
                BytecodeProvider bytecodeProvider = serviceRegistry.getService(BytecodeProvider.class);
                if (bytecodeProvider instanceof UUIDIdentifiableBytecodeProviderImpl uuidBytecodeProvider)
                {
                    if (classLoader instanceof AggregatedClassLoader aggregatedClassLoader)
                    {
                        aggregatedClassLoader.resetClassLoaderCache(uuidBytecodeProvider);
                    }
                    else
                    {
                        uuidBytecodeProvider.resetClassloaderCache(classLoader);
                    }
                }

                ((StandardServiceRegistryImpl)serviceRegistry).destroy();
                classLoader = null;
            }

        });

        SessionFactoryImpl sessionFactory = (SessionFactoryImpl)buildSessionFactory(serviceRegistry);
        setupListeners(sessionFactory);

        Interceptor interceptor = getInterceptor();
        if (interceptor instanceof SessionFactoryAware sessionFactoryAware)
        {
            sessionFactoryAware.setSessionFactory(sessionFactory, sessionFactoryType);
        }
        return sessionFactory;
    }

    /**
     * Создает мигратор схемы {@link SessionFactorySchemaUpdater}
     *
     * @param connection jdbc соединение
     * @return мигратор схемы
     */
    public SessionFactorySchemaUpdaterResource getSessionFactorySchemaUpdater(Connection connection) throws SQLException
    {
        CachedMetaDataConnection cachedConnection = new CachedMetaDataConnection(
                new DelegatingConnection<>(connection));
        final MetadataImplementor meta = getMetadata();
        final StandardServiceRegistry serviceRegistry = meta.getMetadataBuildingOptions().getServiceRegistry();
        final SchemaManagementTool tool = serviceRegistry.getService(SchemaManagementTool.class);
        final Map<String, Object> config = propertiesToMap(getProperties());
        final SqlStringGenerationContext sqlStringGenerationContext =
                SqlStringGenerationContextImpl.fromConfigurationMapForMigration(
                        serviceRegistry.getService(JdbcEnvironment.class),
                        meta.getDatabase(),
                        config
                );
        final JdbcServices jdbcServices = serviceRegistry.getService(JdbcServices.class);
        final JdbcContext jdbcContext = new NauJdbcContext(
                new ProvidedConnectionAccess(cachedConnection),
                sqlStringGenerationContext.getDialect(),
                jdbcServices.getSqlStatementLogger(),
                jdbcServices.getSqlExceptionHelper(),
                serviceRegistry);
        final DdlTransactionIsolator ddlTransactionIsolator = new EmptyDdlTransactionIsolatorImpl(jdbcContext);
        try
        {
            final DatabaseInformation databaseInformation = Helper.buildDatabaseInformation(
                    serviceRegistry,
                    ddlTransactionIsolator,
                    sqlStringGenerationContext,
                    tool
            );

            final SessionFactorySchemaUpdater updater = schemaUpdaterStrategy.getSchemaUpdater(
                    dataBaseInfo,
                    cachedConnection,
                    sqlStringGenerationContext.getDialect(),
                    databaseInformation,
                    meta,
                    sqlStringGenerationContext,
                    getProperties());
            return new SessionFactorySchemaUpdaterResource(
                    updater,
                    ddlTransactionIsolator,
                    cachedConnection);
        }
        catch (Exception ex)
        {
            ddlTransactionIsolator.release();
            throw ex;
        }
    }

    protected Connection getConnectionForSchemaUpdate() throws SQLException
    {
        return dataSource.getConnectionForSchemeUpdate(dataBaseInfo);
    }

    protected void setDataSource(DataSource dataSource)
    {
        this.dataSource = new WrappedDataSource(dataSource, new FlexSqlLogger(new MigrationLoggerImpl()));
    }

    protected void setSchemaUpdaterStrategy(SchemaUpdaterStrategy schemaUpdaterStrategy)
    {
        this.schemaUpdaterStrategy = schemaUpdaterStrategy;
    }

    /**
     * Возвращает метаданные hibernate мапинга
     */
    public MetadataImplementor getMetadata()
    {
        if (metadata == null)
        {
            throw new IllegalStateException("Metadata not initialized yet");
        }
        return metadata;
    }
}
