package ru.naumen.core.server.flex.spi;

import static ru.naumen.core.server.partition.PartitionUtils.getIgnoredTables;
import static ru.naumen.core.server.partition.impl.PartitionTableServiceImpl.VIEW;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.function.Predicate;

import org.hibernate.boot.Metadata;
import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.relational.Namespace;
import org.hibernate.boot.model.relational.QualifiedSequenceName;
import org.hibernate.boot.model.relational.Sequence;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.cfg.AvailableSettings;
import org.hibernate.dialect.Dialect;
import org.hibernate.dialect.MySQLDialect;
import org.hibernate.internal.util.StringHelper;
import org.hibernate.mapping.Column;
import org.hibernate.mapping.ForeignKey;
import org.hibernate.mapping.Table;
import org.hibernate.mapping.UniqueKey;
import org.hibernate.tool.schema.extract.spi.DatabaseInformation;
import org.hibernate.tool.schema.extract.spi.IndexInformation;
import org.hibernate.tool.schema.extract.spi.TableInformation;
import org.hibernate.tool.schema.spi.ExecutionOptions;
import org.hibernate.tool.schema.spi.TargetDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.hibernate.CreateIndexQueryProperties;
import ru.naumen.core.server.hibernate.CreateIndexQueryProperties.Builder;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.MSDDLDialect;
import ru.naumen.core.server.hibernate.constraint.UniqueConstraint;
import ru.naumen.core.server.hibernate.hbm2ddl.CachedMetaDataConnection;
import ru.naumen.core.server.hibernate.hbm2ddl.ConstraintMetaData;
import ru.naumen.core.server.hibernate.hbm2ddl.TableMetaData;
import ru.naumen.core.server.hibernate.index.IndexCondition;

/**
 * Генерирует запросы для обновления схемы БД.
 * Реализация замещает скрипты в
 * {@link org.hibernate.tool.schema.internal.AbstractSchemaMigrator#doMigration(Metadata, ExecutionOptions, TargetDescriptor)},
 * т.к. возникла необходимость расширить логику Hibernate введением дополнительных проверок
 * индексов и ограничений уникальности (unique constraint), чтобы не допустить создания их дубликатов
 *
 * <AUTHOR>
 * @since Sep 16, 2015
 */
public class SessionFactorySchemaUpdater
{
    private static final Logger LOG = LoggerFactory.getLogger(SessionFactorySchemaUpdater.class);

    private final DDLTool ddlTool;
    private final Map<?, ?> config;
    private final Metadata metadata;
    private final SqlStringGenerationContext generationContext;
    private final Dialect dialect;
    private final DatabaseInformation databaseInformation;
    private final SchemaUpdateScriptService schemaUpdateScriptService;
    @Nullable
    private List<Table> tablesFromMapping;
    private Predicate<String> tablesFilteringPredicate = s -> true;
    private BiPredicate<TableInformation, ForeignKey> dropExistConstraintPredicate = (table, key) -> false;
    private final DataBaseInfo dataBaseInfo;

    public SessionFactorySchemaUpdater(DDLTool ddlTool, DataBaseInfo dataBaseInfo, Dialect dialect,
            DatabaseInformation databaseInformation, Metadata metadata, SqlStringGenerationContext generationContext,
            Map<?, ?> config)
    {
        this.ddlTool = ddlTool;
        this.dataBaseInfo = dataBaseInfo;
        this.config = config;
        this.dialect = dialect;
        this.databaseInformation = databaseInformation;
        this.metadata = metadata;
        this.generationContext = generationContext;
        this.schemaUpdateScriptService = SpringContext.getInstance().getBean(SchemaUpdateScriptService.class);
    }

    /**
     * Обновить схему БД.
     * Генерирует необходимые скрипты и выполняет их.
     */
    public void updateSchema(CachedMetaDataConnection connection,
            List<SchemaUpdater> updaters) throws SQLException
    {
        List<SchemaUpdateScript> scripts = generateSchemaUpdateScripts();
        LOG.debug("Executing schema update scripts");
        schemaUpdateScriptService.executeScripts(connection, scripts);
        LOG.debug("Finished executing schema update scripts");
        LOG.debug("Executing creation indexes");
        schemaUpdateScriptService.createIndexes(connection, getTablesFromMapping());
        LOG.debug("Finished creation indexes");

        for (SchemaUpdater updater : updaters)
        {
            LOG.info("Process {}", updater.getClass().getSimpleName());
            updater.update(connection, metadata, config);
        }
    }

    /**
     * Генерирует запросы для обновления схемы БД
     *
     * @return список сгенерированных запросов
     */
    public List<SchemaUpdateScript> generateSchemaUpdateScripts() throws SQLException
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();
        scripts.addAll(generateTables());
        scripts.addAll(generateUniqueConstraints());
        scripts.addAll(generateForeignKeys());
        Namespace namespace = metadata.getDatabase().getDefaultNamespace();
        scripts.addAll(generateSequences(namespace));

        schemaUpdateScriptService.runDeferredScriptsConcurrently();

        return scripts;
    }

    /**
     * Генерирует запросы для обновления внешних ключей в соответствии с маппингами Hibernate
     *
     * @return список сгенерированных запросов
     */
    private List<SchemaUpdateScript> generateForeignKeys() throws SQLException
    {
        ArrayList<SchemaUpdateScript> scripts = new ArrayList<>();

        if (!dialect.hasAlterTable())
        {
            return scripts;
        }

        String defaultCatalog = (String)config.get(AvailableSettings.DEFAULT_CATALOG);
        String defaultSchema = (String)config.get(AvailableSettings.DEFAULT_SCHEMA);

        for (Table table : getTablesFromMapping())
        {
            scripts.addAll(generateForeignKeys(defaultCatalog, defaultSchema, table));
        }
        return scripts;
    }

    private List<SchemaUpdateScript> generateForeignKeys(String defaultCatalog, String defaultSchema, Table table)
            throws SQLException
    {
        ArrayList<SchemaUpdateScript> scripts = new ArrayList<>();
        Set<String> usedScripts = new HashSet<>();
        String tableSchema = (null == table.getSchema()) ? defaultSchema : table.getSchema();
        String tableCatalog = (null == table.getCatalog()) ? defaultCatalog : table.getCatalog();

        Identifier tableSchemaIdentifier = Identifier.toIdentifier(tableSchema);
        Identifier tableCatalogIdentifier = Identifier.toIdentifier(tableCatalog);
        TableInformation tableInfo = databaseInformation.getTableInformation(tableCatalogIdentifier,
                tableSchemaIdentifier, table.getNameIdentifier());
        Iterator<ForeignKey> foreignKeys = table.getForeignKeys().values().iterator();
        while (foreignKeys.hasNext())
        {
            ForeignKey foreignKey = foreignKeys.next();
            if (!foreignKey.isPhysicalConstraint())
            {
                continue;
            }

            // Icky workaround for MySQL bug
            // TODO: После обновления до Hibernate >5.2.8 исправить в соответствии с комментарием к задаче
            //  NSDPRD-5683
            Identifier foreignKeyIdentifier = Identifier.toIdentifier(foreignKey.getName());
            boolean shouldCreateForeignKey = !foreignKey.getTable().getName().matches("tbl_.*_folders")
                                             && (tableInfo == null || (
                    tableInfo.getForeignKey(foreignKeyIdentifier) == null
                    && (!(dialect instanceof MySQLDialect)
                        || tableInfo.getIndex(foreignKeyIdentifier) == null)))
                                             && isNotDbEntity(ddlTool, foreignKey.getReferencedTable(), VIEW)
                                             && isNotDbEntity(ddlTool, foreignKey.getReferencedTable(),
                    "PARTITIONED TABLE")
                                             && isNotDbEntity(ddlTool, foreignKey.getTable(), VIEW);
            if (shouldCreateForeignKey)
            {
                dropExistConstraint(tableInfo, foreignKey, tableSchema);
                SchemaUpdateScript script = schemaUpdateScriptService.generateCreateForeignKeyScript(
                        foreignKey, generationContext, tableCatalog, tableSchema);

                if (!usedScripts.contains(script.script()))
                {
                    usedScripts.add(script.script());
                    scripts.add(script);
                }
            }
        }
        return scripts;
    }

    private void dropExistConstraint(@Nullable TableInformation tableInfo, ForeignKey foreignKey, String tableSchema)
            throws SQLException
    {
        if (dropExistConstraintPredicate.test(tableInfo, foreignKey))
        {
            final String tableName = Objects.requireNonNull(tableInfo).getName().getTableName().getText();
            ddlTool.dropConstraint(tableSchema, tableName, foreignKey.getName());
        }
    }

    /**
     * Генерирует запросы для обновления генераторов ID в соответствии с маппингами Hibernate
     *
     * @return список сгенерированных запросов
     */
    private List<SchemaUpdateScript> generateSequences(Namespace namespace)
    {
        ArrayList<SchemaUpdateScript> scripts = new ArrayList<>();
        for (Sequence sequence : namespace.getSequences())
        {
            QualifiedSequenceName qSeqName = sequence.getName();
            String seqName = qSeqName.getSequenceName().getText();

            if (ddlTool.isSequence(seqName) || ddlTool.isMsSqlSequenceTableExist(seqName))
            {
                continue;
            }
            String[] lines = dialect.getSequenceExporter().getSqlCreateStrings(sequence, metadata, generationContext);
            scripts.addAll(SchemaUpdateScript.fromStringArray(lines, false));
        }
        return scripts;
    }

    /**
     * Генерация запросов создания unique constraint. Автоматическое их создание в hibernate отключено
     * (hibernate.schema_update.unique_constraint_strategy=SKIP) - это обусловлено особенностью реализации unique
     * constraint
     * на разных БД(см. generateUniqueColumnConstraintsForTable) поэтому контролируем их генерацию сами.
     * Плюс реализованы дополнительные проверки при создании на наличие дубликатов.
     */
    private List<SchemaUpdateScript> generateUniqueConstraints() throws SQLException
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();
        String defaultCatalog = (String)config.get(AvailableSettings.DEFAULT_CATALOG);
        String defaultSchema = (String)config.get(AvailableSettings.DEFAULT_SCHEMA);

        Map<String, TableMetaData> tablesMetaData = new HashMap<>();
        ddlTool.getExistingUniqueConstraintsMetaData(tablesMetaData);
        ddlTool.getExistingPartitionTablesMetaData(tablesMetaData);

        for (Table table : getTablesFromMapping())
        {
            scripts.addAll(generateUniqueTableConstraints(table, tablesMetaData));
            scripts.addAll(generateUniqueColumnConstraintsForTable(databaseInformation, table, defaultSchema,
                    defaultCatalog, tablesMetaData));
        }
        return scripts;
    }

    /**
     * Генерирует запросы для обновления определений таблиц в соответствии с маппингами Hibernate
     *
     * @return список сгенерированных запросов
     */
    @SuppressWarnings("unchecked")
    private List<SchemaUpdateScript> generateTables() throws SQLException
    {
        String defaultCatalog = (String)config.get(AvailableSettings.DEFAULT_CATALOG);
        String defaultSchema = (String)config.get(AvailableSettings.DEFAULT_SCHEMA);
        List<SchemaUpdateScript> scripts = new ArrayList<>();
        for (Table table : getTablesFromMapping())
        {
            Identifier tableSchemaIdentifier = Identifier.toIdentifier(
                    (null == table.getSchema()) ? defaultSchema : table.getSchema());
            Identifier tableCatalogIdentifier = Identifier.toIdentifier(
                    (null == table.getCatalog()) ? defaultCatalog : table.getCatalog());
            TableInformation tableInfo = databaseInformation.getTableInformation(tableCatalogIdentifier,
                    tableSchemaIdentifier,
                    table.getNameIdentifier());

            scripts.addAll(getScriptsForGenerateTable(tableInfo, table));
        }
        return scripts;
    }

    private List<SchemaUpdateScript> getScriptsForGenerateTable(@Nullable TableInformation tableInfo, Table table)
    {
        List<SchemaUpdateScript> scripts = new ArrayList<>();
        if (tableInfo == null)
        {
            for (String sql : dialect.getTableExporter().getSqlCreateStrings(table, metadata, generationContext))
            {
                scripts.add(new SchemaUpdateScript(sql, false));
            }
        }
        else
        {
            Iterator<String> subiter = table.sqlAlterStrings(dialect, metadata, tableInfo, generationContext);
            while (subiter.hasNext())
            {
                scripts.add(new SchemaUpdateScript(subiter.next(), false));
            }
        }
        return scripts;
    }

    /**
     * Создаются запросы по изменению таблиц - добавление уникальных ограничений на колонки.
     * Если уже существовали подобные - переименовываем. Т.о. обеспечиваем защиту от дубликатов.
     * Для MS SQL: В случае, если колонка может содержать NULL (Nullable), то индекс позволяет несколько NULL значений.
     * Реализовано за счет индексов с условием is not null.
     * </p><ul>Необходимость этого действия связана со следующим:
     * <li> Для MSSQL Server 2008 ограничения создаются в виде индексов. При этом NULL значение является обычным
     * значением и
     * участвует в индексе. Таким образом, среди данных не может быть два NULL значения. Мы же хотим, чтобы все БД
     * вели себя одинаково в данном случае. <a href="http://msdn.microsoft.com/en-us/library/ms191166.aspx">...</a>
     * <li> Начиная с версии hibernate 4.1.10 в режиме UPDATE не создаются уникальные ограничения на колонки.
     * Создаём их вручную.
     * </ul>
     */
    private List<SchemaUpdateScript> generateUniqueColumnConstraintsForTable(
            DatabaseInformation databaseInformation,
            Table table, String defaultSchema, String defaultCatalog,
            Map<String, TableMetaData> tablesMetaData) throws SQLException
    {
        String tableName = DDLTool.getCanonicalIdentifier(table.getName());
        String tableSchema = (table.getSchema() == null) ? defaultSchema : table.getSchema();
        String tableCatalog = (table.getCatalog() == null) ? defaultCatalog : table.getCatalog();

        Identifier tableSchemaIdentifier = Identifier.toIdentifier(tableSchema);
        Identifier tableCatalogIdentifier = Identifier.toIdentifier(tableCatalog);
        Identifier tableIdentifier = Identifier.toIdentifier(tableName);

        TableInformation tableInfo = databaseInformation.getTableInformation(tableCatalogIdentifier,
                tableSchemaIdentifier, tableIdentifier);
        tableSchema = (tableSchema == null) ? "" : tableSchema + ".";
        TableMetaData tableMetaData = tablesMetaData.get(tableName);

        // Все колонки с уникальными полями
        List<String> listExistingUnique = tableMetaData != null
                ? tableMetaData.getConstraints()
                .stream()
                .map(ConstraintMetaData::getColumns)
                .flatMap(List::stream)
                .map(String::toLowerCase)
                .toList()
                : List.of();

        Column partitionColumn = null;
        boolean isPartitionedTable = tableMetaData != null
                                     && tableMetaData.getPartitionColumnName() != null;
        for (Column column : table.getColumns())
        {
            if (isPartitionedTable && column.getName().equals(tableMetaData.getPartitionColumnName()))
            {
                partitionColumn = column;
                break;
            }
        }
        final List<SchemaUpdateScript> scripts = new ArrayList<>();

        @SuppressWarnings("rawtypes")
        Iterator iter = table.getColumns().iterator();
        while (iter.hasNext()) //NOSONAR
        {
            Column col = (Column)iter.next();
            String constraintName = DDLTool.getCanonicalIdentifier(ddlTool.createUniqueIndexName(table, col));
            // если колонка входит в уникальность, то пропускаем. Логику еще надо проверять.
            if (!col.isUnique() || listExistingUnique.contains(col.getName().toLowerCase())
                || schemaUpdateScriptService.isIndexForbidden(
                    constraintName, table.getName(), new String[] { col.getName() }))
            {
                continue;
            }

            if (dataBaseInfo.isMssql())
            {
                if (tableInfo != null && StringHelper.isNotEmpty(constraintName))
                {
                    Identifier constraintIdentifier = Identifier.toIdentifier(constraintName);
                    final IndexInformation meta = tableInfo.getIndex(constraintIdentifier);
                    if (meta != null)
                    {
                        continue;
                    }
                }

                if (ddlTool.indexExists(constraintName))
                {
                    continue;
                }

                IndexCondition indexCondition = null;
                if (col.isNullable())
                {
                    indexCondition = MSDDLDialect.notNull(col.getName());
                }
                CreateIndexQueryProperties indexQueryProperties = new Builder(
                        constraintName, tableSchema + table.getName(),
                        List.of(col.getName()))
                        .setConcurrently(false)
                        .setUnique(true)
                        .setParallel(true)
                        .setNoLogging(true)
                        .setIndexCondition(indexCondition)
                        .build();
                scripts.addAll(schemaUpdateScriptService.generateCreateIndexScript(
                        indexQueryProperties, false));
            }
            else
            {
                if (tableMetaData != null && tableMetaData.getConstraint(constraintName) != null)
                {
                    continue;
                }

                UniqueKey uniqueKey = new UniqueKey();
                uniqueKey.setName(constraintName);
                uniqueKey.addColumn(col);
                if (partitionColumn != null)
                {
                    // необходимо добавлять колонку, по которой происходит секционирование во все уникальные ограничения
                    uniqueKey.addColumn(partitionColumn);
                }
                ConstraintMetaData existingConstraint = tableMetaData != null
                        ? tableMetaData.getExistingSameConstraint(uniqueKey)
                        : null;
                if (existingConstraint != null)
                {
                    scripts.add(schemaUpdateScriptService.getRenameConstraintScript(tableName, tableSchema,
                            existingConstraint.getName(), constraintName));
                    continue;
                }
                scripts.add(schemaUpdateScriptService.generateCreateConstraintScript(tableName,
                        new UniqueConstraint(constraintName,
                                uniqueKey.getColumns().stream().map(Column::getName).toList())));
            }
        }
        return scripts;
    }

    /**
     * Генерация запросов создания unique constraint для аннотации @UniqueConstraint используемой в @Table - все эти
     * аннотации обрабатывает до нас hibernate и чтобы не выполнять двойную работу используем результаты обработки.
     * Если уже существовали подобные - переименовываем. Т.о. обеспечиваем защиту от дубликатов.
     * Используем @UniqueConstraint только на системных таблицах, где гарантируется что колонки NOT_NULL
     * (см. generateUniqueColumnConstraintsForTable) для MSSQL не стали вводить индексы вместо constraint
     * т.к. нет необходимости - используется только в системных таблицах.
     * Внимание ХАК!! не называем constraint начиная с "UK_" - hibernate  создает имена начиная с них -
     * используется для отличия системных (нами заданных) от генерируемых(ненужных) hibernate -
     * hibernate скидывает в uniqueKeys информацию о @Column(unique=true) - мы ее обрабатываем отдельно.
     */
    private List<SchemaUpdateScript> generateUniqueTableConstraints(Table table,
            Map<String, TableMetaData> tablesMetaData)
    {
        Iterator<UniqueKey> uniqueIter = table.getUniqueKeys().values().iterator();
        String tableName = DDLTool.getCanonicalIdentifier(table.getName());
        String schema = null == table.getSchema() ? null : table.getSchema().toLowerCase();
        TableMetaData tableInfo = tablesMetaData.get(tableName);

        final List<SchemaUpdateScript> scripts = new ArrayList<>();
        while (uniqueIter.hasNext())
        {
            UniqueKey uniqueKey = uniqueIter.next();
            String uniqueKeyName = DDLTool.getCanonicalIdentifier(uniqueKey.getName());
            if (uniqueKey.getName().startsWith("UK")
                || (tableInfo != null && tableInfo.getConstraint(uniqueKeyName) != null)
                || uniqueKey.getColumns().isEmpty())
            {
                continue;
            }

            String[] columnNames = new String[uniqueKey.getColumns().size()];
            for (int i = 0; i < uniqueKey.getColumns().size(); i++)
            {
                columnNames[i] = uniqueKey.getColumn(i).getName();
            }

            if (schemaUpdateScriptService.isIndexForbidden(uniqueKeyName, table.getName(), columnNames))
            {
                continue;
            }

            ConstraintMetaData existngConstraint = tableInfo != null ? tableInfo.getExistingSameConstraint(uniqueKey)
                    : null;
            if (existngConstraint != null)
            {
                scripts.add(schemaUpdateScriptService.getRenameConstraintScript(
                        tableInfo.getName(), schema, existngConstraint.getName(), uniqueKeyName));
                continue;
            }

            scripts.add(schemaUpdateScriptService.generateCreateConstraintScript(
                    tableName, new UniqueConstraint(uniqueKeyName, columnNames)));
        }
        return scripts;
    }

    public List<Table> getTablesFromMapping() throws SQLException
    {
        return tablesFromMapping == null ? getTablesFromMappingInit() : tablesFromMapping;
    }

    private List<Table> getTablesFromMappingInit() throws SQLException
    {
        Set<String> ignoreTables = dataBaseInfo.isPostgres()
                ? getIgnoredTables(ddlTool, dataBaseInfo.getSchema())
                : HashSet.newHashSet(0);
        tablesFromMapping = new ArrayList<>();
        for (Table table : metadata.collectTableMappings())
        {
            if (tablesFilteringPredicate.test(table.getName())
                && table.isPhysicalTable()
                && !"##temp##".equals(table.getSchema())
                && !ignoreTables.contains(table.getName()))
            {
                tablesFromMapping.add(table);
            }
        }
        return tablesFromMapping;
    }

    public void setTablesFilteringPredicate(Predicate<String> predicate)
    {
        this.tablesFilteringPredicate = predicate;
    }

    public void setDropExistConstraintPredicate(BiPredicate<TableInformation, ForeignKey> dropExistConstraintPredicate)
    {
        this.dropExistConstraintPredicate = dropExistConstraintPredicate;
    }

    public static boolean isNotDbEntity(DDLTool ddlTool, Table table, String entityDb) throws SQLException
    {
        String tableType = ddlTool.getTableType(table.getName());
        return !entityDb.equalsIgnoreCase(tableType);
    }
}
