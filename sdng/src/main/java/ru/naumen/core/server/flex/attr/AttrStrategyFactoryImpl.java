package ru.naumen.core.server.flex.attr;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.flex.codegen.attributes.AttributeAppender;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Реализация {@link AttrStrategyFactory}
 *
 * <AUTHOR>
 *
 */
@Component("attrStrategyFactory")
public class AttrStrategyFactoryImpl implements AttrStrategyFactory
{
    private static final Logger LOG = LoggerFactory.getLogger(AttrStrategyFactoryImpl.class);

    private final Map<String, AttributeAppender> strategies = new HashMap<>();

    /**
     * Реализация {@link AttrStrategyFactory}.
     * @param appenders стратегии генерации метаданных Hibernate для всех типов атрибутов системы.
     */
    @Inject
    public AttrStrategyFactoryImpl(List<AttributeAppender> appenders)
    {
        for (AttributeAppender appender : appenders)
        {
            AttrStrategyComponent cmp = appender.getClass().getAnnotation(AttrStrategyComponent.class);
            if (cmp == null)
            {
                throw new FxException("Annotation AttrStrategyComponent not found for " + appender);
            }
            for (String attrType : cmp.types())
            {
                if (strategies.put(attrType, appender) != null)
                {
                    LOG.warn("Attribute strategy for attribute type '{}' is overridden to '{}'", attrType, appender);
                }
            }
        }
    }

    @Override
    public AttributeAppender getStrategy(Attribute attr)
    {
        String code = attr.getType().getCode();
        if (LOG.isDebugEnabled())
        {
            LOG.debug("Getting strategy for attribute '{}' with type: '{}' and type's code '{}'", attr,
                    attr.getType(), code);
        }
        AttributeAppender strategy = getStrategy(code);
        if (strategy == null)
        {
            throw new FxException("Strategy for type '" + code + "' not registered. Attribute: "
                                  + attr.getMetaClass().getFqn() + ":" + attr.getCode());
        }
        return strategy;
    }

    @Override
    public AttributeAppender getStrategy(String typeCode)
    {
        return strategies.get(typeCode);
    }

}
