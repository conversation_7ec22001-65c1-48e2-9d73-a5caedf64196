package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.dom.client.Element;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.jsinterop.inputmask.Alias;
import ru.naumen.core.client.jsinterop.inputmask.DigitsRestrictionAlias;
import ru.naumen.core.client.jsinterop.inputmask.SeparatedGroupsAlias;
import ru.naumen.core.client.jsinterop.inputmask.SeparatedGroupsWithDigitRestrictions;
import ru.naumen.core.client.widgets.mask.InputMaskOptions;

/**
 * Вспомогательные методы для работы с библиотекой InputMask масок ввода
 * TODO: возможно стоит заменить JSNI на JsInterop
 * <AUTHOR>
 * @since 09.07.2019
 */
public class InputMaskUtils
{
    /**
     * Проверить, имеется ли уже алиас с данным именем в системе.
     * Для удобства будем считать, что алиасы, создаваемые в фабриках для полей ввода чисел, с одинаковым именем
     * имеют одинаковые настройки
     * @param alias название алиаса
     * @return имеется ли такой алиас в системе или нет
     */
    public static native boolean checkAlias(String alias)
    /*-{
        return alias in $wnd.Inputmask.prototype.defaults.aliases;
    }-*/;

    /**
     * Создает алиас с заданными параметрами и если его еще нет в системе, добавляет
     * @param hasGroupSeparators есть ли разделители для групп чисел
     * @param hasDigitsRestriction есть ли ограничение на количество чисел в дробной части
     * @param digits количество чисел в дробной части
     * @return созданный алиас для маски ввода в поле
     */
    @Nullable
    public static Alias createAlias(boolean hasGroupSeparators, boolean hasDigitsRestriction, int digits)
    {
        Alias alias = null;
        if (hasGroupSeparators && !hasDigitsRestriction)
        {
            alias = new SeparatedGroupsAlias();
        }
        else if (!hasGroupSeparators && hasDigitsRestriction)
        {
            alias = new DigitsRestrictionAlias(digits);
        }
        else if (hasGroupSeparators && hasDigitsRestriction)
        {
            alias = new SeparatedGroupsWithDigitRestrictions(digits);
        }
        return alias;
    }

    /**
     * Добавляет новый алиас с определенными параметрами
     * @param aliasName название алиаса
     * @param alias параметры маски ввода, определенные в алиасе
     */
    public static native void extendAliases(String aliasName, Alias alias)
    /*-{
        var myObj = new Object();
        myObj[aliasName.toString()]=alias;
        $wnd.Inputmask.extendAliases(myObj);
    }-*/;

    /**
     * Проверить, удовлетворяет ли введенное значение заданным параметрам маски ввода
     * @param value введенное значение
     * @param maskOptions параметры маски ввода
     * @return значение удовлетворяет маске ввода или нет
     */
    public static native boolean fitsMask(String value, JavaScriptObject maskOptions)
    /*-{
        if (value == null || value === "")
        {
            return true;
        }
        try
        {
            var maskedValue = $wnd.Inputmask.format(value, maskOptions);
            if (!$wnd.Inputmask.isValid(maskedValue, maskOptions))
            {
                return false;
            }
            var unmaskedValue = $wnd.Inputmask.unmask(maskedValue, maskOptions);
            return unmaskedValue === value || maskedValue === value;
        }
        catch(e)
        {
            return true;
        }
    }-*/;

    public static native String format(Double value, Long decimalsCountRestriction)
    /*-{
        var fractionResult = parseFloat(value).toFixed(decimalsCountRestriction);
        return fractionResult.toString();
    }-*/;

    public static native String getEmptyMask(Element el)
    /*-{
         return $wnd.$(el).inputmask("getemptymask");
    }-*/;

    public static native String getUnmaskedValue(Element el)
    /*-{
         return $wnd.$(el).inputmask('unmaskedvalue');
    }-*/;

    public static native String getValue(Element el)
    /*-{
         return $wnd.$(el).val();
    }-*/;

    /**
     * Инициализирует маску для элемента el по его атрибуту data-inputmask
     * @param el элемент на который необходимо добавить и проинициализировать маску ввода
     */
    public static native void initMasking(Element el)
    /*-{
        $wnd.$(el).inputmask();
    }-*/;

    public static native boolean isComplete(Element el)
    /*-{
        return $wnd.$(el).inputmask("isComplete");
    }-*/;

    /**
     * Проверить строковое представление целого числа - не превышает ли оно максимально допустимое значение
     * @param number строковое представление целого числа
     * @return находится ли заданное число в допустимом диапазоне
     */
    public static boolean isInRange(String number)
    {
        number = replaceDigitSeparators(number);
        String maxValue = Long.toString(Long.MAX_VALUE);
        number = number.replaceFirst("^0+", ""); // remove leading zeroes
        return number.length() < maxValue.length() ||
               (number.length() == maxValue.length() &&
                number.compareTo(maxValue) <= 0);
    }

    /**
     * Добавить маску ввода с новыми параметрами для заданного элемента
     * @param maskOptions параметры маски вввода
     * @param el элемент для которого добавляем новую маску ввода
     */
    public static void maskWithOptions(InputMaskOptions maskOptions, Element el)
    {
        if (isMaskValid(maskOptions))
        {
            maskWithOptions(maskOptions.getObject().getJavaScriptObject(), el);
        }
    }

    private static native void maskWithOptions(JavaScriptObject maskOptions, Element el)
    /*-{
        $wnd.$(el).inputmask(maskOptions);
    }-*/;

    /**
     * Удаление маски ввода с заданного элемента
     * @param el елемент, с которого необходимо удалить маску ввода
     */
    public static native void removeMask(Element el) //NOPMD
    /*-{
        $wnd.$(el).inputmask('remove');
    }-*/;

    /**
     * Убирает разделители десятичных разрядов из строкового представления числа
     * @param original исходное представление числа
     * @return строка с замененными разделителями
     */
    public static String replaceDigitSeparators(String original)
    {
        return original.trim().replace(" ", StringUtilities.EMPTY);
    }

    /**
     * Округлить заданное вещественное число до нужного числа знаков после запятой
     * @param value вещественное число
     * @param restriction ограничение на количество знаков
     * @return строковое представление числа с ограниченным колиечеством знаков
     */
    public static String roundDoubleValue(Double value, Integer restriction)
    {
        String[] splitter = String.valueOf(value).split("\\.");
        int digitsCountInFractionPart = splitter.length <= 1 ? 0 : splitter[1].length();
        if (digitsCountInFractionPart < restriction)
        {
            return format(value, restriction.longValue()); //NOPMD
        }
        return String.valueOf(value);
    }

    public static native void triggerMouseOutEvent(Element el)
    /*-{
        $wnd.$(el).trigger('mouseenter.inputmask');
        $wnd.$(el).trigger('mouseleave.inputmask');
    }-*/;

    public static native boolean validate(String value, JavaScriptObject maskOptions)
    /*-{
        try
        {
            return $wnd.Inputmask.isValid(value, maskOptions);
        }
        catch(e)
        {
            return true;
        }
    }-*/;

    /**
     * Проверить корректность маски ввода
     *
     * @param maskOptions параметры маски вввода
     * @return возвращает true, если маска ввода корректна, иначе false
     */
    public static boolean isMaskValid(InputMaskOptions maskOptions)
    {
        return maskOptions.isMaskEmpty() || isMaskValid(maskOptions.getObject().getJavaScriptObject());
    }

    private static native boolean isMaskValid(JavaScriptObject maskOptions)
    /*-{
        try
        {
            $wnd.Inputmask.format("value", maskOptions);
            return true;
        }
        catch(e)
        {
            return false;
        }
    }-*/;
}
