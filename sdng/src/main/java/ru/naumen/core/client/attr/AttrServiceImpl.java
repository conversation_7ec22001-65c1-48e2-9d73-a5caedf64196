package ru.naumen.core.client.attr;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.attr.IPresentationRegistry;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;

/**
 * Реализация {@link AttrService}
 *
 * <AUTHOR>
 *
 */
@Singleton
public class AttrServiceImpl implements AttrService
{
    class Factory
    {
        void initType(IProperties prop)
        {
        }
    }

    static final Logger LOG = Logger.getLogger(AttrServiceImpl.class.getName());

    @Inject
    private IPresentationRegistry presentations;

    // <класс типа атрибута, фабрика манипуляции этим типом>
    private final Map<String, Factory> factories;

    @Inject
    public AttrServiceImpl()
    {
        factories = new HashMap<String, Factory>();

        factories.put(StringAttributeType.CODE, new Factory()
        {
            @Override
            public void initType(IProperties prop)
            {
                prop.setProperty(StringAttributeType.CODE, StringAttributeType.MAX_LENGTH_DEFAULT);
            }
        });
    }

    @Override
    public IProperties createType(String typeName)
    {
        IProperties prop = new MapProperties();
        initType(typeName, prop);
        return prop;
    }

    @Override
    public String[] getEdit(String type)
    {
        return presentations.getEdit(type);
    }

    @Override
    public String[] getEdit(String type, Map<String, Object> settings)
    {
        return presentations.getEdit(type, settings);
    }

    @Override
    public String getEditDef(String type)
    {
        return presentations.getEditDef(type);
    }

    @Override
    public String getEditDef(String type, Map<String, Object> settings)
    {
        return presentations.getEditDef(type, settings);
    }

    @Override
    public String[] getShow(String type)
    {
        return presentations.getShow(type);
    }

    @Override
    public String[] getShow(String type, Map<String, Object> settings)
    {
        return presentations.getShow(type, settings);
    }

    @Override
    public String getShowDef(String type)
    {
        return presentations.getShowDef(type);
    }

    @Override
    public String getShowDef(String type, Map<String, Object> settings)
    {
        return presentations.getShowDef(type, settings);
    }

    @Override
    public void initType(String typeName, IProperties typeProperties)
    {
        if (getFactory(typeName) != null)
        {
            getFactory(typeName).initType(typeProperties);
        }
    }

    Factory getFactory(String typeName)
    {
        return factories.get(typeName);
    }
}
