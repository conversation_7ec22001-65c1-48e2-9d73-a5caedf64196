package ru.naumen.core.client.attr.presentation;

import com.google.gwt.inject.client.AbstractGinModule;

import jakarta.inject.Singleton;
import ru.naumen.core.client.attr.presentation.factories.AttributePrsFactoryGinModule;
import ru.naumen.core.shared.attr.presentation.AttributeOfRelatedObjectPresentationExtension;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtensionService;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtensionServiceImpl;

/**
 * Класс для разрешения зависимостей фабрик представления атрибута. Использует
 * {@link PresentationFactoryEditGinModule} и {@link PresentationFactoryViewGinModule} для задания реализаций
 * компонентов фабрики представления для конкретного кода атрибута
 * Также см. {@link PresentationTitlesRegistryImpl} - там задается наименование типа представления атрибута.
 * Чтобы добавить новый тип представления, его надо будет зарегистрировать здесь и в
 * {@link PresentationTitlesRegistryImpl}
 * <AUTHOR>
 * @since 26.12.2011
 */
public class AttributePrsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new AttributePrsFactoryGinModule());

        bind(AttributePresentationExtensionService.class).to(AttributePresentationExtensionServiceImpl.class)
                .in(Singleton.class);
        bind(AttributeOfRelatedObjectPresentationExtension.class).asEagerSingleton();
    }
}
