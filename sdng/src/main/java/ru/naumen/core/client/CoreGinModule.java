package ru.naumen.core.client;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;

import com.google.gwt.core.client.GWT.UncaughtExceptionHandler;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.gwt.storage.client.Storage;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import jakarta.inject.Singleton;
import ru.naumen.core.client.CoreGinjector.AttributeViewColumnFactory;
import ru.naumen.core.client.CoreGinjector.CellTableWithRowsIdFactory;
import ru.naumen.core.client.CoreGinjector.FormDisplayFactory;
import ru.naumen.core.client.CoreGinjector.PrevPageLinkPresenterFactory;
import ru.naumen.core.client.CoreGinjector.StringRowIdExtractorProvider;
import ru.naumen.core.client.CoreGinjector.TabProxyPresenterFactory;
import ru.naumen.core.client.CoreGinjector.UncaughtExceptionHandlerFactory;
import ru.naumen.core.client.activity.ActivityGinjector;
import ru.naumen.core.client.attr.AttrGinjector;
import ru.naumen.core.client.attr.FormatterServiceImpl;
import ru.naumen.core.client.badge.BadgeInitializer;
import ru.naumen.core.client.badge.BadgeInitializerStub;
import ru.naumen.core.client.comet.CometGinModule;
import ru.naumen.core.client.common.CommonGinjector;
import ru.naumen.core.client.content.ContentGinjector;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplayImpl;
import ru.naumen.core.client.content.toolbar.display.buttons.PushableButtonToolDisplayImpl;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory.ButtonTypes;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactoryInitializer;
import ru.naumen.core.client.dispatchasync.AsyncDispatchModule;
import ru.naumen.core.client.files.FileUploadDnDController;
import ru.naumen.core.client.files.FileUploadDnDControllerImpl;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormDisplayImpl;
import ru.naumen.core.client.jscomm.JsCommunicator;
import ru.naumen.core.client.listeditor.ListEditorGinjector;
import ru.naumen.core.client.listeditor.ListEditorResources;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.TabProxyPresenter;
import ru.naumen.core.client.permission.AdminPermissionCheckServiceAsync;
import ru.naumen.core.client.permission.AdminPermissionCheckServiceAsyncImpl;
import ru.naumen.core.client.personalsettings.PersonalSettingsGinModule;
import ru.naumen.core.client.styles.HoverCellTableResources;
import ru.naumen.core.client.styles.TasksCellTableResources;
import ru.naumen.core.client.tree.TreeGinjector;
import ru.naumen.core.client.utils.LocaleInfoImpl;
import ru.naumen.core.client.validation.ValidationGinModule;
import ru.naumen.core.client.widgets.WidgetGinjector;
import ru.naumen.core.client.widgets.buttons.AbstractButton;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector;
import ru.naumen.core.shared.DtoCriteriaHelper;
import ru.naumen.core.shared.DtoCriteriaHelperBase;
import ru.naumen.core.shared.HasCode.HasCodeExtractor;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.core.shared.timer.definition.TimerDefinitionWithScript;
import ru.naumen.core.shared.utils.CommonUtils.HasCodeDtoContainerExtractor;
import ru.naumen.core.shared.utils.CommonUtils.MapRowIdExtractor;
import ru.naumen.core.shared.utils.CommonUtils.UUIDExtractor;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.client.MetainfoGinModule;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.password.form.client.PasswordFormGinModule;

/**
 * Gin Модуль для Core
 *
 * <AUTHOR>
 */
public class CoreGinModule extends AbstractGinModule
{
    public static final String LOCALE_PARAM = "locale";
    public static final String THEME_PARAM = "theme";
    public static final String ROW_ID_EXTRACTOR = "rowIdExtractor";

    @Override
    protected void configure()
    {
        install(new CometGinModule());
        install(new AsyncDispatchModule());
        install(new PersonalSettingsGinModule());
        install(new MetainfoGinModule());
        install(new ValidationGinModule());
        install(new PasswordFormGinModule());

        bind(AttrGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(CommonGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(PropertiesGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(FormattersInjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(ContentGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(ListEditorGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(WidgetGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(TreeGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(ActivityGinjector.class).to(CoreGinjector.class).in(Singleton.class);
        bind(BadgeInitializer.class).to(BadgeInitializerStub.class).in(Singleton.class);

        bind(Storage.class).annotatedWith(Names.named(StorageType.LocalStorage)).toProvider(LocalStorageProvider.class);
        bind(Storage.class).annotatedWith(Names.named(StorageType.SessionStorage)).toProvider(
                SessionStorageProvider.class);
        bind(FormatterService.class).to(FormatterServiceImpl.class).in(Singleton.class);

        // @formatter:off
        bind(DtObject.class).to(SelectItem.class);
        bind(new TypeLiteral<Collection<DtObject>>() {}).to(new TypeLiteral<ArrayList<DtObject>>() {});
        bind(new TypeLiteral<Collection<SelectItem>>() {}).to(new TypeLiteral<ArrayList<SelectItem>>() {});
        // @formatter:on

        bind(ButtonFactoryInitializer.class).asEagerSingleton();

        // @formatter:off
        bind(new TypeLiteral<AbstractButton>() {}).annotatedWith(Names.named(ButtonTypes.BUTTON)).to(ButtonToolDisplayImpl.class);
        bind(new TypeLiteral<AbstractButton>() {}).annotatedWith(Names.named(ButtonTypes.PUSH)).to(PushableButtonToolDisplayImpl.class);
        // @formatter:on

        bind(ILocaleInfo.class).to(LocaleInfoImpl.class).in(Singleton.class);

        bind(AdminPermissionCheckServiceAsync.class).to(AdminPermissionCheckServiceAsyncImpl.class).in(Singleton.class);

        install(new GinFactoryModuleBuilder().implement(UncaughtExceptionHandler.class,
                JSUncaughtExceptionHandler.class).build(UncaughtExceptionHandlerFactory.class));

        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(new TypeLiteral<Column<DtObject, Object>>(){}, AttributeViewColumn.class)
                .build(AttributeViewColumnFactory.class));

        install(new GinFactoryModuleBuilder()
                .implement(Presenter.class, TabProxyPresenter.class)
                .build(TabProxyPresenterFactory.class));

        install(new GinFactoryModuleBuilder()
             .implement(new TypeLiteral<CellTableWithRowsId<Role, TasksCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<Role, TasksCellTableResources>>(){})
             .build(new TypeLiteral<CellTableWithRowsIdFactory<Role, TasksCellTableResources>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<Group, TasksCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<Group, TasksCellTableResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<Group, TasksCellTableResources>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<String, ListEditorResources>>(){}, new TypeLiteral<CellTableWithRowsId<String, ListEditorResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<String, ListEditorResources>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<TimerDefinitionWithScript, HoverCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<TimerDefinitionWithScript, HoverCellTableResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<TimerDefinitionWithScript, HoverCellTableResources>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<DtoContainer<EscalationScheme>, HoverCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<DtoContainer<EscalationScheme>, HoverCellTableResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<DtoContainer<EscalationScheme>, HoverCellTableResources>>(){}));
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<CellTableWithRowsId<EscalationSchemeLevel, HoverCellTableResources>>(){}, new TypeLiteral<CellTableWithRowsId<EscalationSchemeLevel, HoverCellTableResources>>(){})
            .build(new TypeLiteral<CellTableWithRowsIdFactory<EscalationSchemeLevel, HoverCellTableResources>>(){}));

        bind(new TypeLiteral<Function<? super Role, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(HasCodeExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super Group, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(HasCodeExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super TimerDefinitionWithScript, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(HasCodeExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super String, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .toProvider(StringRowIdExtractorProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super Map<String, Object>, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(MapRowIdExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super DtoContainer<EscalationScheme>, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(HasCodeDtoContainerExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super EscalationSchemeLevel, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(EscalationSchemeLevel.IdExtractor.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Function<? super DtObject, String>>(){})
            .annotatedWith(Names.named(ROW_ID_EXTRACTOR))
            .to(UUIDExtractor.class)
            .in(Singleton.class);

        install(new GinFactoryModuleBuilder()
                .implement(FormDisplay.class, FormDisplayImpl.class)
                .build(FormDisplayFactory.class));

        install(new GinFactoryModuleBuilder()
            .implement(PrevPageLinkPresenter.class, PrevPageLinkPresenter.class)
            .build(PrevPageLinkPresenterFactory.class));
        //@formatter:on

        bind(JsCommunicator.class).asEagerSingleton();

        bind(FileUploadDnDController.class).to(FileUploadDnDControllerImpl.class).asEagerSingleton();
        bind(DtoCriteriaHelper.class).to(DtoCriteriaHelperBase.class).in(Singleton.class);
    }
}
