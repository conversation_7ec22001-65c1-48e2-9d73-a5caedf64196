package ru.naumen.core.client.mvp;

import ru.naumen.core.shared.utils.ReadyState;

/**
 * Реализация {@link BasicCallback} для предотвращения ухода со страницы при потери сессии
 * с выводом диалога остаться на объекте или перейти на форму логина.
 * Использовать по согласованию с аналитиками!
 *
 * <AUTHOR>
 * @since 18.06.2014
 */
public class SafeBasicCallback<T> extends BasicCallback<T>
{
    public SafeBasicCallback()
    {
        super();
    }

    public SafeBasicCallback(Display display)
    {
        super(display);
    }

    public SafeBasicCallback(ReadyState rs)
    {
        super(rs);
    }

    @Override
    public boolean useSafetySTOutHandler()
    {
        return true;
    }
}
