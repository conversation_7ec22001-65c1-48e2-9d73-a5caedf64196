package ru.naumen.core.client.attr.presentation.factories;

import java.util.Collection;
import java.util.Collections;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttrStringWithCatalogSuggestionWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeBackTimerDeadLineFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeBackTimerExceedWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeCaseTreeWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeDateTimeTwoBoxesWidgetAsyncFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeDateTimeWidgetFactoryAsyncProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeDateTimeWithMillisWidgetFactoryAsyncProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeDateWidgetFactoryAsyncProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeDoubleWithMaskWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeEmptyWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeFileUploadWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeIntegerWithMaskWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeLicenseAllMultiSelectWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeLicenseAllWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeLicenseWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeLinkedClassesTreeWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeMetaClassCollectionWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeMetaClassPlaneWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeMetaClassTreeWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeRadioButtonWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeRichTextWidgetFactoryAsyncProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeSelectCaseWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeSourceCodeEditWidgetFactoryAsyncProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeStateMultiSelectWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeStateWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeStringWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeStringWithMaskWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeTextWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeThreeStateRadioButtonWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeTimerStateWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.AttributeWidgetFactoryProviderImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.QuickSelectionFieldTreeFactory;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.aggregate.AggregateSelectWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.list.ResponsibleListWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.responsible.tree.ResponsibleTreeWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.AttributeSelectWidgetFactoryImpl;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithPermittedTypes;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithPermittedTypesUnion;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.PrsFactoryEditSelectGinModule.WithoutPermittedTypes;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.list.AttributeSelectCellListFactoryImpl;
import ru.naumen.core.client.inject.splitpoint.SplitPoint;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.Aggregate;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.BoTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.CatalogItems;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.ResponsibleTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.widgets.ColorTextBox;
import ru.naumen.core.client.widgets.DateTimeIntervalWidget;
import ru.naumen.core.client.widgets.HyperlinkWidget;
import ru.naumen.core.client.widgets.NauCheckBox;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Виджеты редактирования инициализируются отложено для оптимизации первого входа в приложение.
 * На карточки заходят в 10 раз чаще, чем на формы.
 *
 * <AUTHOR>
 * @since Nov 26, 2015
 */
@Singleton
public class AttributeEditWidgetFactoriesSplitPoint implements SplitPoint
{
    @Inject
    //@formatter:off
    protected AttributeEditWidgetFactoriesSplitPoint(
            AttributeWidgetFactories factories,
            
            //edit
            Provider<AggregateSelectWidgetFactoryImpl<DtObject, SelectItem, WithoutPermittedTypes, WithoutFolders, Aggregate>> aggregateEditAllTypes,
            Provider<AggregateSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypesUnion, WithoutFolders, Aggregate>> aggregateEditTypesUnion,
            Provider<AggregateSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithFolders, Aggregate>> aggregateSingleWithFoldersEdit,
            Provider<AggregateSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithoutFolders, Aggregate>> aggregateSingleEdit,
            Provider<AggregateSelectWidgetFactoryImpl<DtObject, SelectItem, WithoutPermittedTypes, WithoutFolders, ResponsibleTree>> responsibleTreeAllTypesEditSingle,
            Provider<ResponsibleTreeWidgetFactoryImpl<DtObject, WithFolders, ResponsibleTree>> responsibleTreeWithFoldersEditSingle,
            Provider<ResponsibleTreeWidgetFactoryImpl<DtObject, WithoutFolders, ResponsibleTree>> responsibleTreeEditSingle,
            Provider<AggregateSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithFolders, Aggregate>> aggregateMultiEditWithFolders,
            Provider<AggregateSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithoutFolders, Aggregate>> aggregateMultiEdit,
            Provider<AggregateSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithFolders, Aggregate>> responsibleTreeMultiEditWithFolders,
            Provider<AggregateSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithoutFolders, Aggregate>> responsibleMultiTreeEdit,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithoutPermittedTypes, WithFolders, CatalogItems>> catalogItemTreeEdit,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithoutPermittedTypes, WithFolders, CatalogItems>> catalogItemsTreeEdit,
            Provider<AttributeSelectCellListFactoryImpl<DtObject>> catalogItemEdit,
            Provider<AttributeSelectCellListFactoryImpl<DtObject>> serviceTimeEdit,
            Provider<AttributeSelectCellListFactoryImpl<Collection<DtObject>>> catalogItemsEdit,
            Provider<AttributeSelectCellListFactoryImpl<Collection<DtObject>>> catalogItemsEditAny,
            Provider<AttributeTimerStateWidgetFactoryImpl> backTimerStatusEdit,
            Provider<AttributeWidgetFactoryProviderImpl<Boolean, NauCheckBox>> checkBox,
            Provider<AttributeWidgetFactoryProviderImpl<Color, ColorTextBox>> colorTextEdit,
            Provider<AttributeDateWidgetFactoryAsyncProviderImpl> dateEdit,
            Provider<AttributeWidgetFactoryProviderImpl<DateTimeInterval, DateTimeIntervalWidget>> dateTimeIntervalEdit,
            Provider<AttributeDateTimeWidgetFactoryAsyncProviderImpl> dateTimeEdit,
            Provider<AttributeDateTimeWithMillisWidgetFactoryAsyncProviderImpl> dateTimeWithMillisEdit,
            Provider<AttributeDateTimeTwoBoxesWidgetAsyncFactoryImpl> dateTimeSeparateEdit,
            Provider<AttributeDoubleWithMaskWidgetFactoryImpl> doubleEdit,
            Provider<AttributeWidgetFactoryProviderImpl<Hyperlink, HyperlinkWidget>> hyperlinkEdit,
            Provider<AttributeIntegerWithMaskWidgetFactoryImpl> integerEdit,
            Provider<AttributeRichTextWidgetFactoryAsyncProviderImpl> richTextEdit,
            Provider<AttributeSourceCodeEditWidgetFactoryAsyncProviderImpl> sourceCodeEdit,
            Provider<AttributeLicenseWidgetFactoryImpl> licenseEdit,
            
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithoutPermittedTypes, WithoutFolders,BoTree>> boLinksEditAllTypes,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypesUnion, WithoutFolders,BoTree>> boLinksEditTypesUnion,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithFolders,BoTree>> boLinksEditFolders,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithoutFolders,BoTree>> boLinksEdit,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithoutFolders,BoTree>> boLinksListEdit,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithFolders,BoTree>> boLinksListEditFolders,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithoutFolders, BoTree>> structureBasedSelectionTreeLinksEdit,
            Provider<AttributeSelectWidgetFactoryImpl<Collection<DtObject>, Collection<SelectItem>, WithPermittedTypes, WithFolders, BoTree>> structureBasedSelectionTreeWithFoldersLinksEdit,
            
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithoutPermittedTypes, WithoutFolders,BoTree>> boSelectAllTypes,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypesUnion, WithoutFolders,BoTree>> boSelectTypesUnion,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithFolders,BoTree>> boSelectWithFolders,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithoutFolders,BoTree>> boSelect,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithoutFolders,BoTree>> boSelectList,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithFolders,BoTree>> boSelectListFolders,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithoutFolders, BoTree>> structureBasedSelectionTreeEdit,
            Provider<AttributeSelectWidgetFactoryImpl<DtObject, SelectItem, WithPermittedTypes, WithFolders, BoTree>> structureBasedSelectionTreeWithFoldersEdit,

            Provider<AttributeTimerStateWidgetFactoryImpl> timerStatusEdit,
            Provider<AttributeBackTimerDeadLineFactoryImpl> backTimerDeadline,
            Provider<AttributeBackTimerExceedWidgetFactoryImpl> backTimerExceed,
            Provider<AttributeFileUploadWidgetFactoryImpl> fileUpload,
            Provider<AttributeStringWidgetFactoryImpl> string, 
            Provider<AttributeStringWithMaskWidgetFactoryImpl> stringWithMask,
            Provider<AttrStringWithCatalogSuggestionWidgetFactoryImpl> stringWithCatalogSuggestion,
            Provider<AttributeTextWidgetFactoryImpl> text,
            Provider<AttributeEmptyWidgetFactoryImpl> empty,
            Provider<AttributeLicenseAllWidgetFactoryImpl> licenseAll,
            Provider<AttributeLicenseAllMultiSelectWidgetFactoryImpl> licenseAllMultiSelect,
            Provider<AttributeMetaClassCollectionWidgetFactoryImpl> metaClassCollection,
            Provider<AttributeMetaClassPlaneWidgetFactoryImpl> metaClassPlane,
            Provider<AttributeMetaClassTreeWidgetFactoryImpl> metaClassTree,
            Provider<AttributeRadioButtonWidgetFactoryImpl> radioButton,
            Provider<AttributeThreeStateRadioButtonWidgetFactoryImpl> threeStateRadioButton,
            Provider<ResponsibleListWidgetFactoryImpl> responsibleList,
            Provider<AttributeSelectCaseWidgetFactoryImpl> selectCase,
            Provider<AttributeCaseTreeWidgetFactoryImpl> caseTree,            
            Provider<QuickSelectionFieldTreeFactory> quickSelectionTree,
            Provider<AttributeStateWidgetFactoryImpl> state,
            Provider<AttributeStateMultiSelectWidgetFactoryImpl> stateMultiSelect,
            Provider<AttributeLinkedClassesTreeWidgetFactoryImpl> linkedClassesTree,
            Provider<AttributeSecGroupsWidgetFactory> secGroups)
    //@formatter:on
    {
        //edit
        factories.register(Presentations.AGGREGATE_EDIT_ALL_TYPES, aggregateEditAllTypes);
        factories.register(Presentations.AGGREGATE_EDIT_TYPES_UNION, aggregateEditTypesUnion);
        factories.register(Presentations.AGGREGATE_EDIT_WITH_FOLDERS, aggregateSingleWithFoldersEdit);
        factories.register(Presentations.AGGREGATE_EDIT, aggregateSingleEdit);
        factories.register(Presentations.RESPONSIBLE_TREE_EDIT_ALL_TYPES, responsibleTreeAllTypesEditSingle);
        factories.register(Presentations.RESPONSIBLE_TREE_EDIT_WITH_FOLDERS, responsibleTreeWithFoldersEditSingle);
        factories.register(Presentations.RESPONSIBLE_TREE_EDIT, responsibleTreeEditSingle);
        factories.register(Presentations.AGGREGATES_EDIT_WITH_FOLDERS, aggregateMultiEditWithFolders);
        factories.register(Presentations.AGGREGATES_EDIT, aggregateMultiEdit);
        factories.register(Presentations.RESPONSIBLES_TREE_EDIT_WITH_FOLDERS, responsibleTreeMultiEditWithFolders);
        factories.register(Presentations.RESPONSIBLES_TREE_EDIT, responsibleMultiTreeEdit);
        factories.register(Presentations.CATALOG_ITEM_TREE_EDIT, catalogItemTreeEdit);
        factories.register(Presentations.CATALOG_ITEMS_TREE_EDIT, catalogItemsTreeEdit);
        factories.register(Presentations.CATALOG_ITEM_EDIT, catalogItemEdit);
        factories.register(Presentations.SERVICE_TIME_EDIT, serviceTimeEdit);
        factories.register(Presentations.CATALOG_ITEMS_EDIT, catalogItemsEdit);
        factories.register(Presentations.CATALOG_ITEMS_EDIT_ANY, catalogItemsEditAny);
        factories.register(Presentations.BACKTIMER_STATUS_EDIT, backTimerStatusEdit);
        factories.register(Presentations.BOOL_CHECKBOX, checkBox);
        factories.register(Presentations.COLOR_EDIT, colorTextEdit);
        factories.register(Presentations.DATE_EDIT, dateEdit);
        factories.register(Presentations.DATETIME_INTERVAL_EDIT, dateTimeIntervalEdit);
        factories.register(Presentations.DATETIME_EDIT, dateTimeEdit);
        factories.register(Presentations.DATETIME_WITH_MILLIS_EDIT, dateTimeWithMillisEdit);
        factories.register(Presentations.DATETIME_SEPARATE_EDIT, dateTimeSeparateEdit);
        factories.register(Presentations.DOUBLE_EDIT, doubleEdit);
        factories.register(Presentations.HYPERLINK_EDIT, hyperlinkEdit);
        factories.register(Presentations.INTEGER_EDIT, integerEdit);
        factories.register(Presentations.RICH_TEXT_EDIT, richTextEdit);
        factories.register(Presentations.SOURCE_CODE_EDIT, sourceCodeEdit);
        factories.register(Presentations.JSON_EDIT, empty);
        factories.register(Presentations.LOCALIZED_TEXT_EDIT, string);
        factories.register(Presentations.LICENSE_EDIT, licenseEdit);
        factories.register(Presentations.BO_LINKS_EDIT_ALL_TYPES, boLinksEditAllTypes);
        factories.register(Presentations.BO_LINKS_EDIT_TYPES_UNION, boLinksEditTypesUnion);
        factories.register(Presentations.BO_LINKS_EDIT_FOLDERS, boLinksEditFolders);
        factories.register(Presentations.BO_LINKS_EDIT, boLinksEdit);
        factories.register(Presentations.BO_LINKS_LIST_EDIT, boLinksListEdit);
        factories.register(Presentations.BO_LINKS_LIST_EDIT_FOLDERS, boLinksListEditFolders);
        factories.register(Presentations.BO_SELECT_ALL_TYPES, boSelectAllTypes);
        factories.register(Presentations.BO_SELECT_TYPES_UNION, boSelectTypesUnion);
        factories.register(Presentations.BO_SELECT_FOLDERS, boSelectWithFolders);
        factories.register(Presentations.BO_SELECT, boSelect);
        factories.register(Presentations.BO_SELECT_LIST, boSelectList);
        factories.register(Presentations.BO_SELECT_LIST_FOLDERS, boSelectListFolders);
        factories.register(Presentations.STRUCTURE_BASED_SELECTION_TREE_EDIT, structureBasedSelectionTreeEdit);
        factories.register(Presentations.STRUCTURE_BASED_SELECTION_TREE_LINKS_EDIT,
                structureBasedSelectionTreeLinksEdit);
        factories.register(Presentations.STRUCTURE_BASED_SELECTION_TREE_WITH_FOLDERS_EDIT,
                structureBasedSelectionTreeWithFoldersEdit);
        factories.register(Presentations.STRUCTURE_BASED_SELECTION_TREE_WITH_FOLDERS_LINKS_EDIT,
                structureBasedSelectionTreeWithFoldersLinksEdit);
        factories.register(Presentations.TIMER_STATUS_EDIT, timerStatusEdit);
        factories.register(Presentations.BACKTIMER_DEADLINE_EDIT, backTimerDeadline);
        factories.register(Presentations.BACKTIMER_YES_NO_EDIT, backTimerExceed);
        factories.register(Presentations.FILE_UPLOAD, fileUpload);
        factories.register(Presentations.STRING_EDIT, string);
        factories.register(Presentations.STRING_EDIT_WITH_MASK, stringWithMask);
        factories.register(Presentations.STRING_EDIT_WITH_CATALOG_SUGGESTION, stringWithCatalogSuggestion);
        factories.register(Presentations.TEXT_EDIT, text);
        factories.register(Presentations.LICENSE_SELECT, licenseAll);
        factories.register(Presentations.LICENSE_MULTI_SELECT, licenseAllMultiSelect);
        factories.register(Presentations.METACLASS_COLLECTION_EDIT, metaClassCollection);
        factories.register(Presentations.METACLASS_EDIT_PLANE, metaClassPlane);
        factories.register(Presentations.METACLASS_EDIT_TREE, metaClassTree);
        factories.register(Presentations.BOOL_RADIOBUTTON, radioButton);
        factories.register(Presentations.BOOL_THREE_STATE_RADIOBUTTON, threeStateRadioButton);
        factories.register(Presentations.RESPONSIBLE_LIST_EDIT, responsibleList);
        factories.register(Presentations.CASE_LIST_EDIT, selectCase);
        factories.register(Presentations.CASE_TREE_EDIT, caseTree);
        factories.register(Presentations.QUICK_SELECTION_FIELD, quickSelectionTree);
        factories.register(Presentations.STATE_EDIT, state);
        factories.register(Presentations.STATE_MULTI_SELECT, stateMultiSelect);
        factories.register(Presentations.LINKED_CLASSES_EDIT, linkedClassesTree);
        factories.register(Presentations.SEC_GROUPS_EDIT, secGroups);
    }

    @Override
    public Collection<Class<?>> getDependencies()
    {
        return Collections.emptyList();
    }
}
