package ru.naumen.core.client;

import jakarta.inject.Singleton;

import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Сервис для получения информации о компании.
 *
 * <AUTHOR>
 * @since Dec 30, 2014
 */
@Singleton
public class CompanyInfoService
{
    public native String getCopyright() /*-{
                                        return $wnd.copyright;
                                        }-*/;

    public native String getProductName() /*-{
                                          return $wnd.productName;
                                          }-*/;

    public String getWindowTitle(String addTitle)
    {
        String title = getProductName() + (getProductName().isEmpty() ? StringUtilities.EMPTY : StringUtilities.SPACE)
                       + addTitle;
        return title;
    }
}
