package ru.naumen.core.client;

import java.util.Set;
import java.util.function.Function;

import com.google.common.base.Functions;
import com.google.gwt.cell.client.Cell;
import com.google.gwt.core.client.GWT.UncaughtExceptionHandler;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.inject.client.GinModules;
import com.google.gwt.inject.client.Ginjector;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.cellview.client.CellTable.Resources;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.view.client.AsyncDataProvider;
import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.activity.ActivityGinjector;
import ru.naumen.core.client.attr.AttrGinjector;
import ru.naumen.core.client.common.CommonGinjector;
import ru.naumen.core.client.content.ContentGinjector;
import ru.naumen.core.client.editgrid.EditGridGinjector;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.licensing.LicensingService;
import ru.naumen.core.client.listeditor.ListEditorGinjector;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.TabProxyPresenter;
import ru.naumen.core.client.tree.TreeGinjector;
import ru.naumen.core.client.widgets.PrevPageLink;
import ru.naumen.core.client.widgets.WidgetGinjector;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector;
import ru.naumen.core.shared.IHasTitle;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * {@link Ginjector} модуля ru.naumen.core.Core
 * <AUTHOR>
 * @since 02.12.2010
 */
@GinModules(CoreGinModule.class)
public interface CoreGinjector extends AttrGinjector, CommonGinjector, PropertiesGinjector, FormattersInjector,
        EditGridGinjector, ListEditorGinjector, WidgetGinjector, TreeGinjector, ContentGinjector, ActivityGinjector
{
    interface AttributeViewColumnFactory
    {
        Column<DtObject, Object> create(Cell<Object> cell, String attrCode);
    }

    interface CellTableWithRowsIdFactory<T, R extends Resources>
    {
        CellTableWithRowsId<T, R> create(AsyncDataProvider<T> provider);
    }

    interface FormDisplayFactory
    {
        FormDisplay create(@Assisted("formCaption") String formCaption);

        FormDisplay create(@Assisted("widthStyleName") String widthStyleName,
                @Assisted("formCaption") String formCaption);
    }

    @Deprecated
    interface PageNameProviderFactory
    {
        IPageNameProvider create(IHasTitle display);
    }

    interface PrevPageLinkPresenterFactory
    {
        PrevPageLinkPresenter create(PrevPageLink display);
    }

    class StringRowIdExtractorProvider implements Provider<Function<String, String>>
    {
        @Override
        public Function<String, String> get()
        {
            return Functions.<String> identity();
        }
    }

    interface TabProxyPresenterFactory
    {
        TabProxyPresenter create(AsyncProvider<? extends Presenter> provider,
                Function<Presenter, Presenter> presenterInitializer);

        TabProxyPresenter create(AsyncProvider<? extends Presenter> provider,
                Function<Presenter, Presenter> presenterInitializer, ReadyState readyState);
    }

    /**
     * установка обработчика js ошибок и невидимый дисплей для отражения количества активных асинхронных запросов.  
     */
    interface UncaughtExceptionHandlerFactory
    {
        UncaughtExceptionHandler jsUncaughtExceptionHandler(UncaughtExceptionHandler prevExceptionHandler);
    }

    CompanyInfoService companyInfoService();

    FormatterService formatterService();

    DispatchAsync getDispatch();

    LicensingService getLicensingService();

    ModuleInitializer moduleInitializer();

    PlaceController placeController();

    ToolInfoInitializer toolInfoInitializer();

    Set<Module> getExtensionModules();
}