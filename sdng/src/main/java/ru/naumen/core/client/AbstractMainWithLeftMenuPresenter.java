package ru.naumen.core.client;

import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeUri;
import com.google.gwt.user.client.ui.Hyperlink;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.impl.DialogWidget;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.client.events.BeforeClientLogoutEvent;
import ru.naumen.core.client.events.UserChangedEvent;
import ru.naumen.core.client.homepage.HomePagePlace;
import ru.naumen.core.client.menu.LeftMenuPresenterBase;
import ru.naumen.core.client.menu.LeftNavPanelPresenter;
import ru.naumen.core.client.menu.ShowNavTreeEvent;
import ru.naumen.core.client.menu.subtree.SubNavigationTreePresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.personalsettings.PersonalSettingsPlace;
import ru.naumen.core.client.widgets.AbstractMainPresenter;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.buttons.Button;

/**
 * Абстрактный {@link Presenter} для интерфейсов оператора и администратора
 *
 * <AUTHOR>
 * @since 12.02.2013
 *
 * @see AbstractMainWithLeftMenuPresenter
 * @see LeftNavPanelPresenter
 * @see ru.naumen.core.client.menu.NavigationTreePresenter
 * @see SubNavigationTreePresenter
 */
public abstract class AbstractMainWithLeftMenuPresenter<T extends MainDisplay, L extends LeftMenuPresenterBase>
        extends AbstractMainPresenter<T>
{
    @Inject
    private Provider<SilentModeInfoPresenter> silentModeInfoProvider;
    @Inject
    protected L leftMenuPresenter;
    @Inject
    protected CommonMessages messages;
    @Inject
    private PlaceHistoryMapper placeHistoryMapper;

    public AbstractMainWithLeftMenuPresenter(T display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public abstract void focus();

    protected void bindLeftMenu()
    {
        if (leftMenuPresenter.isBound())
        {
            return;
        }
        registerChildPresenter(leftMenuPresenter);
        getDisplay().setLeftMenu(leftMenuPresenter.getDisplay());
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        securityHelper.fireUserChangedEvent();
        addHandler(UserChangedEvent.TYPE, this::onUserChanged);

        bindLeftMenu();

        repositionMainContent();

        addHandler(ShowNavTreeEvent.getType(), event ->
        {
            leftMenuPresenter.setNavTreeVisibility(false);
            leftMenuPresenter.showNavTree(event.isShow());
            repositionMainContent(); // выполняется при невидимой nav панели, чтоб не было наложений
            leftMenuPresenter.setNavTreeVisibility(true);
        });

        bindSilentModeInfoPresenter();

        registerHandler(getDisplay().getHeaderPanel().getHeaderTools().getLogoutLink().addMouseDownHandler((event) ->
        {
            if (event.getNativeButton() == NativeEvent.BUTTON_LEFT)
            {
                eventBus.fireEvent(new BeforeClientLogoutEvent());
            }
            event.preventDefault();
        }));

        setHomePage();
        setEditProfileLink();
    }

    /**
     * Заново спозиционировать главный контент
     * (т.к. ширина левого меню может меняться)
     */
    private void repositionMainContent()
    {
        getDisplay().repositionMainContent();
    }

    /**
     * Используется для того, чтобы оповестить соседние вкладки смене пользователя (входе в систему)
     */
    private void onUserChanged(UserChangedEvent event) // NOPMD
    {
        DialogWidget w = (DialogWidget)new DialogsImpl()
        {
            @Override
            protected Button addBtn(Dialogs.Buttons btn, DialogWidget w, DialogCallback callback)
            {
                Button button = super.addBtn(btn, w, callback);
                button.setText(messages.sessionChangedButton());
                return button;
            }
        }.info(messages.sessionChangedHeader(), messages.sessionChangedMessage());
        w.setGlassStyleName(WidgetResources.INSTANCE.form().bLightboxFormDarkeningWithHideForm());
        w.setGlassEnabled(true);
    }

    @Override
    protected void updateLogo()
    {
        CurrentUser info = securityHelper.getCurrentUser();
        SafeUri logoUri = getLogoUri(getLogoUuid(info));
        getDisplay().getHeaderPanel().updateLogo(logoUri);
    }

    protected abstract String getLogoUuid(CurrentUser info);

    private void bindSilentModeInfoPresenter()
    {
        if (!securityHelper.isAdminInterfaceAllowed())
        {
            return;
        }
        SilentModeInfoPresenter presenter = silentModeInfoProvider.get();
        registerChildPresenter(presenter, false);
        display.getHeaderPanel().getSilentModeContainer().setVisible(true);
        display.getHeaderPanel().getSilentModeContainer().add(presenter.getDisplay().asWidget());
    }

    /**
     * Установить ссылку на домашнюю страницу
     */
    private void setHomePage()
    {
        String homeToken = placeHistoryMapper.getToken(HomePagePlace.INSTANCE);
        Hyperlink homeLink = getDisplay().getHomeLink();
        homeLink.setTargetHistoryToken(homeToken);
    }

    /**
     * Установить ссылку на страницу редактирования профиля
     */
    private void setEditProfileLink()
    {
        String personalSettingsToken = placeHistoryMapper
                .getToken(new PersonalSettingsPlace(securityHelper.getCurrentUser().getUUID()));
        getDisplay().getHeaderPanel().getHeaderTools().setEditProfileLink(personalSettingsToken);
    }
}
