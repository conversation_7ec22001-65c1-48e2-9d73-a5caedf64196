package ru.naumen.core.client.mvp;

import static com.google.gwt.core.client.impl.FragmentLoaderHttpDownloadExceptionHelper.getExceptionCode;
import static com.google.gwt.core.client.impl.FragmentLoaderHttpDownloadExceptionHelper.isItHttpDownloadFailure;
import static com.google.gwt.http.client.Response.SC_CONFLICT;
import static com.google.gwt.http.client.Response.SC_INTERNAL_SERVER_ERROR;
import static com.google.gwt.http.client.Response.SC_UNAUTHORIZED;
import static ru.naumen.core.shared.Constants.*;

import com.google.common.base.Preconditions;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.rpc.InvocationException;
import com.google.gwt.user.client.rpc.StatusCodeException;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.shared.ServiceException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.RedirectUtils;
import ru.naumen.core.client.common.impl.DialogWidget;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.client.utils.ReadyStateReportingDecorator;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.buttons.Button;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallback;
import ru.naumen.core.shared.utils.ReadyState;

public abstract class AbstractAsyncCallback<T> implements AsyncCallback<T>, AbortableCallback
{
    public static class ActionAbortedException extends Exception
    {
    }

    public static class DialogLifecycle implements Lifecycle
    {
        private final Dialog dialog;

        public DialogLifecycle(Dialog dialog)
        {
            Preconditions.checkNotNull(dialog);
            this.dialog = dialog;
        }

        @Override
        public void failure()
        {
            dialog.hide();
        }

        @Override
        public void start()
        {
            // do nothing
        }

        @Override
        public void success()
        {
            dialog.hide();
        }
    }

    public static class DisplayLifecycle implements Lifecycle
    {
        private final Display display;

        public DisplayLifecycle(Display display)
        {
            Preconditions.checkNotNull(display);
            this.display = display;
        }

        @Override
        public void failure()
        {
            display.stopProcessing();
        }

        @Override
        public void start()
        {
            display.startProcessing();
        }

        @Override
        public void success()
        {
            display.stopProcessing();
        }
    }

    public interface Lifecycle
    {
        void failure();

        void start();

        void success();
    }

    public static class ReadyStateLifecycle implements Lifecycle
    {
        private final ReadyState readyState;

        public ReadyStateLifecycle(ReadyState readyState)
        {
            Preconditions.checkNotNull(readyState);
            this.readyState = readyState;
        }

        @Override
        public void failure()
        {
            readyState.error();
        }

        @Override
        public void start()
        {
            readyState.notReady();
        }

        @Override
        public void success()
        {
            readyState.ready();
        }
    }

    private class DialogCallbackInt extends DialogCallback
    {
        private final String error;
        private final Throwable t;

        private DialogCallbackInt(@Nullable final String error, @Nullable final Throwable t)
        {
            this.error = error;
            this.t = t;
        }

        @Override
        protected void onNo(Dialog widget)
        {
            super.onNo(widget);
            reloading = true;

            if (error == null)
            {
                Location.reload();
            }
            else
            {
                RedirectUtils.logoutWithError(error);
            }
            setIsDialogPresent(false);
            handleFailureInt(t);
        }

        @Override
        protected void onYes(Dialog widget)
        {
            super.onYes(widget);
            setIsDialogPresent(false);
            handleFailureInt(t);
        }
    }

    private static CommonMessages messages;
    private static boolean reloading = false;

    private final Iterable<Lifecycle> lifecycle;
    private final ReadyStateReportingDecorator reporter;
    private boolean sessionTimeOutError = false;
    private boolean abortedAction = false;
    private boolean connectionIssue = false;

    protected AbstractAsyncCallback(Iterable<Lifecycle> lifecycle)
    {
        this.lifecycle = lifecycle;
        reporter = findReporter();
        startLifecycle();
    }

    @Override
    public void setAborted(boolean abortedAction)
    {
        this.abortedAction = abortedAction;
    }

    @Override
    public void onFailure(final Throwable t)
    {
        if (reloading)
        {
            return;
        }

        if (t instanceof ActionAbortedException)
        {
            abortedAction = true;
            handleFailureInt(t);
            return;
        }

        if (t instanceof StatusCodeException)
        {
            Scheduler.get().scheduleDeferred(() -> processStatusCodeException(t));
            return;
        }

        if (isItHttpDownloadFailure(t) && SESSION_TIMEOUT_STATUS == getExceptionCode(t))
        {
            showDialog(SESSION_TIMEDOUT, null);
            return;
        }

        //В случае если пришел неожиданный ответ
        if (t instanceof InvocationException)
        {
            RedirectUtils.logoutWithError(COMMUNICATION_ERROR);
            return;
        }

        handleFailureInt(t);
    }

    private void processStatusCodeException(Throwable t)
    {
        int statusCode = ((StatusCodeException)t).getStatusCode();
        boolean isSessionTimeoutOrScConflict = statusCode == SESSION_TIMEOUT_STATUS || statusCode == SC_CONFLICT;
        if (statusCode == SC_UNAUTHORIZED || isSpnegoAuthenticator() && isSessionTimeoutOrScConflict)
        {
            SecurityHelper.addCurrentSessionToExpiredSessionList();
            reloading = true;//NOSONAR
            Location.reload();
            return;
        }

        // 12015 код ошибки IE, когда не удалась Kerberos авторизация
        if (statusCode == 12015)
        {
            // требуется перезагрузить страницу, чтобы избежать разных ошибок
            reloading = true;//NOSONAR
            Location.reload();
            return;
        }

        if (statusCode == SC_INTERNAL_SERVER_ERROR)
        {
            RedirectUtils.logoutWithError(INTERNAL_SERVER_ERROR);
            return;
        }

        //409 Conflict выдается сервером в том случае, если произошла ошибка аутентификации CSRF
        //проверка происходит в org.springframework.security.web.csrf.CsrfFilter
        if (isSessionTimeoutOrScConflict)
        {
            SecurityHelper.addCurrentSessionToExpiredSessionList();
            if (useSafetySTOutHandler())
            {
                sessionTimeOutError = true;

                if (isShowSessionTimeOutDialog())
                {
                    showDialog(SESSION_TIMEDOUT, t);
                    return;
                }
            }
            else
            {
                showDialog(SESSION_TIMEDOUT, null);
                return;
            }
        }
        if (statusCode == CHANGE_SUPERUSER_ERROR_STATUS)
        {
            showDialog(SESSION_CHANGED_SUPERUSER, null);
            return;
        }

        if (statusCode == DATABASE_CONNECTION_ERROR_STATUS)
        {
            RedirectUtils.logoutWithError(DATABASE_CONNECTION_ERROR);
            return;
        }
        handleFailureInt(t);
    }

    private native boolean isSpnegoAuthenticator()
    /*-{
        return $wnd.isSpnegoAuthenticator;
    }-*/;

    @Override
    public void onSuccess(T result)
    {
        try
        {
            handleSuccess(result);
        }
        finally
        {
            successLifecycle();
        }
    }

    protected String extractFailureMessage(Throwable t)
    {
        if (t instanceof StatusCodeException)
        {
            if (((StatusCodeException)t).getStatusCode() == 0)
            {
                return getCommonMessages().serverNotAvailable();
            }
            if (((StatusCodeException)t).getStatusCode() == 403)
            {
                return getCommonMessages().operationForbidden();
            }
            if (((StatusCodeException)t).getStatusCode() == 401)
            {
                return getCommonMessages().operationUnauthorized();
            }
            if (((StatusCodeException)t).getStatusCode() == 504)
            {
                return getCommonMessages().serverTimedOut();
            }
            return getCommonMessages().winInetErrors(((StatusCodeException)t).getStatusCode());
        }
        boolean hasMessage = !StringUtilities.isEmpty(t.getMessage());
        return hasMessage ? t.getMessage() : t.getClass().getName();
    }

    protected void failureLifecycle()
    {
        for (Lifecycle l : lifecycle)
        {
            l.failure();
        }
    }

    protected void handleFailure(String msg)
    {
        handleFailure(msg, null);
    }

    protected abstract void handleFailure(String msg, @Nullable String details);

    protected void handleFailure(Throwable t)
    {
        final String msg = extractFailureMessage(t);
        final String details = t instanceof ServiceException ? ((ServiceException)t).getDetails() : null;
        if (isConnectionIssue(t))
        {
            if (null == reporter)
            {
                return;
            }
            connectionIssue = true;
            reporter.scheduleReporting(msg);
        }
        handleFailure(msg, details);
    }

    protected abstract void handleSuccess(T value);

    /**
     * Скрывать отображение ошибки если произошел SessionTimeOut
     */
    protected boolean hideErrorMsgIfSessionTimeOut()
    {
        return sessionTimeOutError && useSafetySTOutHandler();
    }

    /**
     * Скрывать отображение ошибки если произошел разрыв связи
     */
    protected boolean isConnectionIssue()
    {
        return connectionIssue;
    }

    @Override
    public boolean isAborted()
    {
        return abortedAction;
    }

    protected boolean isConnectionIssue(Throwable t)
    {
        return t instanceof StatusCodeException && 0 == ((StatusCodeException)t).getStatusCode();
    }

    /**
     * Стоит ли отображать диалог о том что истекла сессия.
     * При отрицательном ответе ваш callback не будет
     * информировать пользователя о истечении сессии.
     * Использовать следует в случае если один callback вызывает другой (выскакивает два диалога)
     * либо если callback висит на кнопке отмена и логика позволяем проигнорировать его.
     */
    public boolean isShowSessionTimeOutDialog()
    {
        return true;
    }

    protected void reset(Throwable e)
    {
    }

    protected void startLifecycle()
    {
        for (Lifecycle l : lifecycle)
        {
            l.start();
        }
    }

    protected void successLifecycle()
    {
        for (Lifecycle l : lifecycle)
        {
            l.success();
        }
    }

    /**
     * Использовать или нет безопасный для пользовательских данных обработчик SessionTimeOut
     * Если нет то будет выкинута ошибка с последующим редиректом на форму логина
     * В случае положительного результа ошибка будет игнорироваться либо будет
     * предложено выбрать пользователя дальнейшие действия (isShowSessionTimeOutDialog())
     */
    public boolean useSafetySTOutHandler()
    {
        return false;
    }

    private ReadyStateReportingDecorator findReporter()
    {
        for (Lifecycle lc : lifecycle)
        {
            if (lc instanceof ReadyStateLifecycle)
            {
                ReadyState readyState = ((ReadyStateLifecycle)lc).readyState;
                ReadyStateReportingDecorator reporter = findReporterInt(readyState);
                if (null != reporter)
                {
                    return reporter;
                }
            }
        }
        return null;
    }

    private ReadyStateReportingDecorator findReporterInt(ReadyState readyState)
    {
        ReadyStateReportingDecorator decorator = ReadyStateReportingDecorator.get(readyState);
        if (null != decorator)
        {
            return decorator;
        }
        for (SynchronizationCallback sync : readyState.getSyncCallbacks())
        {
            if (sync instanceof ReadyState)
            {
                ReadyStateReportingDecorator reporter = findReporterInt((ReadyState)sync);
                if (null != reporter)
                {
                    return reporter;
                }
            }
        }
        return null;
    }

    private CommonMessages getCommonMessages()
    {
        if (null == messages)
        {
            messages = GWT.create(CommonMessages.class);
        }
        return messages;
    }

    private void handleFailureInt(@Nullable Throwable t)
    {
        if (t == null)
        {
            return;
        }
        try
        {
            handleFailure(t);
        }
        finally
        {
            reset(t);
            failureLifecycle();
        }
    }

    /**
     * Выводит диалог с выбором:
     * <ol>
     * <li>Остаться на странице</li>
     * <li>Обновить страницу (если error == null) или перейти на страницу авторизации</li>
     * </ol>
     * @param error код полученной ошибки
     * @param t throwable ошибки для проброса в родителя, если t != null
     */
    private void showDialog(@Nullable final String error, @Nullable final Throwable t)
    {
        if (isDialogPresent())
        {
            return;
        }
        setIsDialogPresent(true);
        boolean isReload = error == null;
        final CommonMessages msg = getCommonMessages();
        DialogWidget w = (DialogWidget)new DialogsImpl()
        {
            @Override
            protected Button addBtn(Buttons btn, DialogWidget w, DialogCallback callback)
            {
                Button button = super.addBtn(btn, w, callback);

                String yesButtonMessage = isReload
                        ? msg.refresh()
                        : msg.sessionTimeOutYesButton();
                button.setText(Buttons.NO.equals(btn)
                        ? yesButtonMessage
                        : msg.sessionTimeOutNoButton());
                return button;
            }
        }.question(
                msg.sessionTimeOutMessage(),
                isReload
                        ? msg.needReloadAfterSessionTimeOutDescription()
                        : msg.sessionTimeOutDescription(),
                msg.sessionTimeOutAdditionalInfo(),
                new DialogCallbackInt(error, t));
        w.setGlassStyleName(WidgetResources.INSTANCE.form().bLightboxFormDarkeningWithHideForm());
        w.setGlassEnabled(true);
    }

    private native static boolean isDialogPresent()
    /*-{
        return $wnd.isDialogPresent;
    }-*/;

    private native static void setIsDialogPresent(boolean isDialogPresent)
    /*-{
        $wnd.isDialogPresent = isDialogPresent;
    }-*/;
}
