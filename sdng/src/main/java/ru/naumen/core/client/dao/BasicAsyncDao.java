package ru.naumen.core.client.dao;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.dao.events.DataUpdateEvent;
import ru.naumen.core.client.dao.registrations.DataRegistration;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.utils.SuccessReadyState;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.EventHandler;
import com.google.gwt.event.shared.GwtEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Базовая реализация dao
 * Подсчитывает количество использований данных, если оно обнуляется, то очищает данные - 
 * это позволяет работать независимо с данными из разных презентеров
 *
 * <AUTHOR>
 * @since Feb 27, 2014
 */
public abstract class BasicAsyncDao<T> implements AsyncDao<T>
{
    private class UnloadDataRegistration implements DataRegistration
    {
        @Override
        public void unloadData()
        {
            unload();
        }
    }

    @Inject
    @Named("new")
    protected EventBus eventBus;

    private UnloadDataRegistration unloadDR = new UnloadDataRegistration();
    protected int uses = 0;
    protected T data;

    @Override
    public <H extends EventHandler> HandlerRegistration addHandler(GwtEvent.Type<H> type, H handler)
    {
        return eventBus.addHandler(type, handler);
    }

    @Override
    public T get()
    {
        return data;
    }

    @Override
    public DataRegistration load(SuccessReadyState readyState)
    {
        ++uses;

        if (data == null)
        {
            load(new BasicCallback<T>(readyState)
            {
                @Override
                protected void handleSuccess(T value)
                {
                    data = value;
                }
            });
        }

        return unloadDR;
    }

    @Override
    public void save(T data, AsyncCallback<T> callback)
    {
        onSave(data, new CallbackDecorator<T, T>(callback)
        {
            @Override
            public void onSuccess(T data)
            {
                super.onSuccess(data);

                BasicAsyncDao.this.data = data;

                eventBus.fireEvent(new DataUpdateEvent(data));
            }
        });
    }

    @Override
    public void unload()
    {
        --uses;
        if (uses <= 0)
        {
            uses = 0;
            data = null;
        }
    }

    protected abstract void load(AsyncCallback<T> callback);

    protected abstract void onSave(T data, AsyncCallback<T> callback);
}
