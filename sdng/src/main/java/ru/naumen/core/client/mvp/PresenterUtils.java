package ru.naumen.core.client.mvp;

import java.util.List;

import jakarta.inject.Singleton;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 28.11.2012
 */
@Singleton
public class PresenterUtils
{
    public List<Presenter> getDescendantPresenters(Presenter presenter)
    {
        List<Presenter> list = new ArrayList<>();
        collectDescendantPresenters(presenter, list);
        return list;
    }

    @SuppressWarnings("rawtypes")
    private void collectDescendantPresenters(Presenter presenter, List<Presenter> list)
    {
        list.add(presenter);
        if (presenter instanceof BasicPresenter)
        {
            for (Object child : ((BasicPresenter)presenter).getChildPresenters())
            {
                collectDescendantPresenters((Presenter)child, list);
            }
        }
    }
}
