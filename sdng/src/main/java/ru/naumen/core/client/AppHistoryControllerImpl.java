package ru.naumen.core.client;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.GwtEvent;
import com.google.gwt.event.shared.HandlerManager;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceChangeEvent;

import ru.naumen.core.client.activity.AbstractPlace;
import ru.naumen.core.client.activity.PlaceParametersChangeEvent;
import ru.naumen.core.client.activity.PlaceParametersChangeHandler;
import ru.naumen.core.client.events.HistoryChangeEvent;

import jakarta.inject.Inject;

import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AppHistoryControllerImpl implements AppHistoryController, PlaceChangeEvent.Handler,
        PlaceParametersChangeHandler
{
    /**
     * Глубина стека
     */
    public static final int DEEP = 5;

    protected final EventBus eventBus;

    private HandlerManager handlerManager;

    /**
     * Стек историй
     */
    protected LinkedList<AppHistoryItem> history = Lists.newLinkedList();

    @edu.umd.cs.findbugs.annotations.SuppressWarnings(value = "RV_RETURN_VALUE_IGNORED", justification = "Controller "
                                                                                                         + "exists "
                                                                                                         + "forever")
    @Inject
    public AppHistoryControllerImpl(EventBus eventBus)
    {
        this.eventBus = eventBus;
        eventBus.addHandler(PlaceChangeEvent.TYPE, this);
        eventBus.addHandler(PlaceParametersChangeEvent.getType(), this);
        eventBus.addHandler(HistoryChangeEvent.getType(), this);
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<List<AppHistoryItem>> handler)
    {
        return ensureHandlers().addHandler(ValueChangeEvent.getType(), handler);
    }

    @Override
    public void fireEvent(GwtEvent<?> event)
    {
        if (handlerManager != null)
        {
            handlerManager.fireEvent(event);
        }
    }

    @Override
    public List<AppHistoryItem> getHistoryItems()
    {
        return Collections.unmodifiableList(history);
    }

    @Override
    public void onHistoryChange(HistoryChangeEvent event)
    {
        addHistory(event.getPlace(), event.getTitle());
    }

    @Override
    public void onPlaceChange(PlaceChangeEvent event)
    {
        Place newPlace = event.getNewPlace();
        if (isSamePlaces(newPlace, getCurrentHistPlace()))
        {
            updateCurrentHistPlace(newPlace);
        }
    }

    @Override
    public void onPlaceParametersChanged(PlaceParametersChangeEvent event)
    {
        updateCurrentHistPlace(event.getPlace());
    }

    protected void fire()
    {
        ValueChangeEvent.fire(this, getHistoryItems());
    }

    protected Place getCurrentHistPlace()
    {
        return history.peek() != null ? history.peek().getPlace() : null;
    }

    protected boolean isSamePlaces(Place p1, Place p2)
    {
        if (p1 == null || p2 == null)
        {
            return false;
        }
        return p1 instanceof AbstractPlace ? ((AbstractPlace)p1).isSomePlace(p2) : p1.equals(p2);
    }

    protected void updateCurrentHistPlace(Place newPlace)
    {
        if (history.peek() != null)
        {
            history.peek().setPlace(newPlace);
        }
    }

    private void addHistory(Place place, String title)
    {
        AppHistoryItem item = new AppHistoryItem(place, title);
        int count = 0;
        for (Iterator<AppHistoryItem> it = history.iterator(); it.hasNext(); )
        {
            AppHistoryItem item2 = it.next();
            if (count >= DEEP || isSamePlaces(item, item2))
            {
                it.remove();
            }
            else
            {
                count += 1;
            }
        }
        history.addFirst(item);
        fire();
    }

    private HandlerManager ensureHandlers()
    {
        return handlerManager == null ? handlerManager = new HandlerManager(this) : handlerManager;
    }

    private boolean isSamePlaces(AppHistoryItem i1, AppHistoryItem i2)
    {
        if (i1 == null || i2 == null)
        {
            return false;
        }
        return isSamePlaces(i1.getPlace(), i2.getPlace());
    }
}
