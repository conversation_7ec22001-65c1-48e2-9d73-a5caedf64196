package ru.naumen.core.client.attr.presentation;

import static ru.naumen.core.shared.Constants.CONTENT_CODE;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;

import com.google.common.collect.Sets;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.utils.FormLockSettingsHelper;
import ru.naumen.core.client.utils.ReadyStateReportingDecorator;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.QuickActions;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.OriginService.Origin;
import ru.naumen.core.shared.userevents.UserEventParametersProperties;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.ReadyStateExtendedLoggingDecorator;
import ru.naumen.core.shared.utils.ReadyStateTimeoutDecorator;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.Constants.StringAttributeType.InputMaskMode;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Контекст для передачи параметров в {@link PresentationFactoryBase}
 * В основе лежит {@link MapProperties}
 *
 * <AUTHOR>
 * @since 01.08.2012
 */
public class PresentationContext extends FormatterContext implements WidgetContext<Attribute>
{
    @Serial
    private static final long serialVersionUID = -531899325465869894L;

    /**
     * Тип атрибута, значение которого форматируется
     */
    private transient AttributeType attributeType;
    private transient Context parentContext;
    //Тип расположения, где отображается атрибут
    private Origin origin = Origin.READ;
    // Признак того, что находимся в списке (неважно: в простом или сложном)
    private boolean isInList;
    private Collection<ClassFqn> permittedTypeFqns;
    private boolean filterEnabled = true;
    private boolean filteredByScript;
    private boolean selectAnyOption = false;
    private boolean selectEmptyOption = false;
    private String editPresentationCode;
    private boolean useThumbnail;
    private String originFormCode;
    private UserEventParametersProperties userEventParams; // Контекстные переменные для скрипта параметра
    // пользовательского события
    private DtObject sourceFormObject;
    private List<DtObject> unsavedObjects;
    //Признак адвлиста
    private boolean isAdvlist = false;
    //Признак отображения значения по умолчанию
    private boolean isDefaultValue = false;
    //Признак, надо ли скрывать уже выбранные значения
    private boolean attributeValuesExcluded = false;
    private Set<String> attributeValues = null;
    private transient Object initialValue;
    private String inputMask;
    private InputMaskMode inputMaskMode;
    private String massEditAttributeCode;
    private boolean isContentVisible = true;
    @Nullable
    private String contentCode;

    public PresentationContext()
    {
        setObject(new SimpleDtObject());
    }

    //Конструктор с дефолтными значениями, когда есть атрибут
    public PresentationContext(Attribute attribute)
    {
        this(attribute, attribute.getType(), new SimpleDtObject());
    }

    /**
     * Стандартный конструктор<br>
     * Атрибут может быть null на форме добавления атрибута в интерфейсе администратора.
     * См. ru.naumen.metainfoadmin.client.attributes.forms.props.DefaultValuePropertyControllerImpl
     * .createPresentationContext(AttributeType)
     */
    public PresentationContext(@Nullable Attribute attr, @Nullable AttributeType attributeType,
            @Nullable DtObject dtObject)
    {
        super(attr, dtObject);
        this.attributeType = attributeType;
        filteredByScript = attr != null && LinkAttributeUtils.isFiltered(attr);
    }

    //Конструктор с дефолтными значениями, когда неизвестно, есть ли атрибут
    public PresentationContext(AttributeType attributeType)
    {
        this(attributeType.getAttribute(), attributeType, new SimpleDtObject());
    }

    public boolean areAttributeValuesExcluded()
    {
        return attributeValuesExcluded;
    }

    public void copyOptionalFeatureProperties(BiConsumer<String, Object> destination)
    {
        entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(ru.naumen.metainfo.shared.Constants.OPTIONAL_FEATURE_PREFIX))
                .forEach(entry -> destination.accept(entry.getKey(), entry.getValue()));
    }

    /**
     * Формирует объект (свойства) для фильтрации значений атрибута.
     */
    public IProperties createFiltrationProperties()
    {
        Attribute attribute = getAttribute();
        ClassFqn metaclass = attribute != null ? attribute.getMetaClassLite().getFqn() : null;
        return createFiltrationProperties(metaclass);
    }

    /**
     * Формирует объект (свойства) для фильтрации значений атрибута.
     *
     * @param metaclass тип объекта, в контексте которого фильтруются значения
     *                  (используется для формы массового редактирования)
     */
    public IProperties createFiltrationProperties(@Nullable ClassFqn metaclass)
    {
        Attribute attribute = getAttribute();
        DtObject object = getObject();
        MapProperties filtrationProperties = new MapProperties();
        if (object != null)
        {
            object.forEach((propertyName, value) -> filtrationProperties.setProperty(propertyName,
                    CommonUtils.isDifferentValuesObject(value) ? null : value));
        }
        filtrationProperties.setProperty(AbstractBO.METACLASS, metaclass);
        filtrationProperties.setProperty(CONTENT_CODE, contentCode);
        if (sourceFormObject != null)
        {
            filtrationProperties.setProperty(QuickActions.SOURCE_FORM, sourceFormObject);
        }
        if (attribute == null)
        {
            return filtrationProperties;
        }

        AttributeType type = attribute.getType();
        if (type != null && type.isAttributeOfRelatedObject())
        {
            filtrationProperties.setProperty(AttributeOfRelatedObjectSettings.RELATED_OBJECT_ATTRIBUTE,
                    new AttributeFqn(type.getRelatedObjectMetaClass(), type.getRelatedObjectAttribute()));
        }
        if (metaclass != null && getParentContext() instanceof MassEditActionContext)
        {
            for (String propertyName : Sets.newHashSet(filtrationProperties.propertyNames()))
            {
                String prefix = metaclass + AttributeFqn.DELIMITER;
                if (propertyName.startsWith(prefix))
                {
                    filtrationProperties.put(propertyName.replace(prefix, ""),
                            filtrationProperties.get(propertyName));
                }
            }
        }
        return filtrationProperties;
    }

    /**
     * Получение кода атрибута из контекста, также возможно получения кода на формах массового редактирования.
     * @return - код атрибута
     */
    @Nullable
    public String getAttributeCode()
    {
        if (getMassEditAttributeCode() == null)
        {
            Attribute attribute = getAttribute();
            return attribute == null ? null : attribute.getCode();
        }
        else
        {
            return getMassEditAttributeCode();
        }
    }

    public AttributeType getAttributeType()
    {
        return attributeType;
    }

    public Set<String> getAttributeValues()
    {
        return attributeValues;
    }

    @Override
    @Nullable
    public Attribute getContextObject()
    {
        return getAttribute();
    }

    public String getEditPresentationCode()
    {
        return editPresentationCode;
    }

    public ReadyState getFormReadyState()
    {
        Context context = getParentContext();
        if (null != context)
        {
            ReadyState readyState = context.getContextProperty(Constants.FORM_APPLY_READY_STATE);
            if (null == readyState)
            {
                readyState = new ReadyState(context);
                if (FormLockSettingsHelper.isExtendedLoggingEnabled())
                {
                    readyState = new ReadyStateExtendedLoggingDecorator(readyState);
                }
                if (FormLockSettingsHelper.getUnlockTimeout() > 0)
                {
                    readyState = new ReadyStateTimeoutDecorator(readyState, FormLockSettingsHelper.getUnlockTimeout());
                }
                readyState = new ReadyStateReportingDecorator(readyState);
                context.setContextProperty(Constants.FORM_APPLY_READY_STATE, readyState);
                context.getReadyState().registerSynchronization(readyState);
            }
            return readyState;
        }
        return new ReadyState(this);
    }

    @SuppressWarnings("unchecked")
    public <T> T getInitialValue()
    {
        return (T)initialValue;
    }

    public String getInputMask()
    {
        return inputMask;
    }

    public InputMaskMode getInputMaskMode()
    {
        return inputMaskMode;
    }

    /**
     * @return - код атрибута для формы массового редактирования
     * Не всегда совпадает с типом атрибута, так как может содержать признак типа (аналогично AttributeFqn)
     */
    private String getMassEditAttributeCode()
    {
        return massEditAttributeCode;
    }

    @SuppressWarnings("unchecked")
    @Override
    @CheckForNull
    public DtObject getObject()
    {
        return super.getObject();
    }

    @Nullable
    public Context getParentContext()
    {
        return parentContext;
    }

    public Collection<ClassFqn> getPermittedTypeFqns()
    {
        return permittedTypeFqns;
    }

    public DtObject getSourceFormObject()
    {
        return sourceFormObject;
    }

    public List<DtObject> getUnsavedObjects()
    {
        if (null == unsavedObjects)
        {
            unsavedObjects = new ArrayList<>();
        }
        return unsavedObjects;
    }

    public UserEventParametersProperties getUserEventParams()
    {
        return userEventParams;
    }

    public Object getValue()
    {
        //@formatter:off
        Attribute attribute = getAttribute();
        DtObject object = getObject();
        return  attribute != null && object != null
                ? object.get(attribute.getCode())
                        : null;
        //@formatter:on
    }

    public boolean isAdvlist()
    {
        return isAdvlist;
    }

    public boolean isContentVisible()
    {
        return isContentVisible;
    }

    public boolean isDefaultValue()
    {
        return isDefaultValue;
    }

    public boolean isFilteredByScript()
    {
        return filteredByScript;
    }

    /**
     * @return разрешена ли фильтрация значений атрибутов при выборе из списка/дерева
     */
    public boolean isFilterEnabled()
    {
        return filterEnabled;
    }

    /**
     * Признак того, что находимся в списке (неважно: в простом или сложном)
     *
     * @return находимся в списке или нет
     */
    public boolean isInList()
    {
        return isInList;
    }

    /**
     * Признак представления для выпадающих списков множественного выбора
     * - показывать или нет опцию [Любой]
     */
    public boolean isSelectAnyOption()
    {
        return selectAnyOption;
    }

    public boolean isSelectEmptyOption()
    {
        return selectEmptyOption;
    }

    /**
     * Признак отображения миниатюрной картинки вместо оригинальной,
     * используется при отображении элементов справочника и файла
     */
    public boolean isUseThumbnail()
    {
        return useThumbnail;
    }

    public void setAttributeType(AttributeType attributeType)
    {
        this.attributeType = attributeType;
    }

    public void setAttributeValues(Set<String> attributeValues)
    {
        this.attributeValues = attributeValues;
    }

    public void setAttributeValuesExcluded(boolean attributeValuesExcluded)
    {
        this.attributeValuesExcluded = attributeValuesExcluded;
    }

    public PresentationContext setContentVisible(boolean contentVisible)
    {
        isContentVisible = contentVisible;
        return this;
    }

    public PresentationContext setEditPresentationCode(String editPresentationCode)
    {
        this.editPresentationCode = editPresentationCode;
        return this;
    }

    public void setFilteredByScript(boolean value)
    {
        filteredByScript = value;
    }

    /**
     * @param filterEnabled the filterEnabled to set
     */
    public void setFilterEnabled(boolean filterEnabled)
    {
        this.filterEnabled = filterEnabled;
    }

    public void setInitialValue(Object initialValue)
    {
        this.initialValue = initialValue;
    }

    public void setInList(boolean isInList)
    {
        this.isInList = isInList;
    }

    public void setInputMask(String inputMask)
    {
        this.inputMask = inputMask;
    }

    public void setInputMaskMode(InputMaskMode inputMaskMode)
    {
        this.inputMaskMode = inputMaskMode;
    }

    public void setIsAdvlist(boolean isAdvlist)
    {
        this.isAdvlist = isAdvlist;
    }

    public void setIsDefaultValue(boolean isDefaultValue)
    {
        this.isDefaultValue = isDefaultValue;
    }

    public void setMassEditParams(String massEditAttributeCode)
    {
        this.massEditAttributeCode = massEditAttributeCode;
    }

    public PresentationContext setParentContext(@Nullable Context parentContext)
    {
        this.parentContext = parentContext;
        return this;
    }

    public PresentationContext setPermittedTypeFqns(Collection<ClassFqn> permittedTypeFqns)
    {
        this.permittedTypeFqns = permittedTypeFqns;
        return this;
    }

    @Override
    public PresentationContext setPresentation(IProperties presentation)
    {
        return (PresentationContext)super.setPresentation(presentation);
    }

    @Nullable
    public String getOriginFormCode()
    {
        return originFormCode;
    }

    public void setOriginFormCode(@Nullable String originFormCode)
    {
        this.originFormCode = originFormCode;
    }

    public void setSelectAnyOption(boolean selectAnyOption)
    {
        this.selectAnyOption = selectAnyOption;
    }

    public void setSelectEmptyOption(boolean selectEmptyOption)
    {
        this.selectEmptyOption = selectEmptyOption;
    }

    public PresentationContext setSourceFormObject(@Nullable DtObject sourceFormObject)
    {
        this.sourceFormObject = sourceFormObject;
        return this;
    }

    /**
     * Установить код контента из которого вызвана форма, действия
     */
    public PresentationContext setContentCode(@Nullable String contentCode)
    {
        this.contentCode = contentCode;
        return this;
    }

    public void setUserEventParams(UserEventParametersProperties userEventParams)
    {
        this.userEventParams = userEventParams;
    }

    public void setUseThumbnail(boolean useThumbnail)
    {
        this.useThumbnail = useThumbnail;
    }

    public Origin getOrigin()
    {
        return origin;
    }

    public PresentationContext setOrigin(Origin origin)
    {
        this.origin = origin;
        return this;
    }
}