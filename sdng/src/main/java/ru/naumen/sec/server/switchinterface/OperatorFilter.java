package ru.naumen.sec.server.switchinterface;

import static ru.naumen.core.shared.Constants.ASSOCIATED_SUPERUSER;
import static ru.naumen.core.shared.Constants.ERROR_MESSAGE;
import static ru.naumen.core.shared.Constants.INTERFACE_NAME;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;

import jakarta.inject.Inject;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.SuperUserDetailsService;

/**
 * Фильтр переключения в режим оператора
 * <AUTHOR>
 * @since May 10, 2018
 */
public class OperatorFilter implements Filter
{
    private static final Logger LOG = LoggerFactory.getLogger(OperatorFilter.class);

    @Inject
    private SuperUserDetailsService superUserDetailsService;

    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    @Override
    public void destroy() //NOSONAR
    {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        HttpServletRequest req = (HttpServletRequest)request;

        HttpServletResponse res = (HttpServletResponse)response;

        HttpSession session = req.getSession(false);
        // Проверяем, что пользователь не является Суперпользователем, у которого есть
        // ассоциированные пользователи
        String login = CurrentEmployeeContext.getCurrentUserLogin();

        if (login != null && superUserDetailsService.hasAssociatedEmployee(login)
            && !superUserDetailsService.isSuperUserVendorOrAdmin(login))
        {
            LOG.atDebug().log(
                    "Session of user {} was invalidated due to switching interface while has associated employee",
                    login);
            session.invalidate();
            String params = ERROR_MESSAGE
                            + '='
                            + ASSOCIATED_SUPERUSER;
            redirectStrategy.sendRedirect(req, res, "/?" + params);
            return;
        }
        req.getSession().setAttribute(INTERFACE_NAME, Constants.OPERATOR_ALIAS);

        chain.doFilter(request, response);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {
        SpringContext.getInstance().autowireBean(this);
    }
}
