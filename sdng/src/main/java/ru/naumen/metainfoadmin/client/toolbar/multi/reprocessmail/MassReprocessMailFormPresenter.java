package ru.naumen.metainfoadmin.client.toolbar.multi.reprocessmail;

import java.util.ArrayList;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEvent;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.mailreader.shared.dispatch.ReprocessMailsAction;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.mail.AbstractSelectProcessingRuleFormPresenter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.UpdateAdvlistSelectionEvent;

/**
 * Презентер формы повторной обработки письма для массовых операций
 *
 * <AUTHOR>
 *
 */
public class MassReprocessMailFormPresenter extends AbstractSelectProcessingRuleFormPresenter
{
    @Inject
    private DispatchAsync dispatch;

    private ActionToolContext context;

    @Inject
    public MassReprocessMailFormPresenter(@Assisted ActionToolContext context, PropertyDialogDisplay display,
            EventBus eventBus)
    {
        super(display, eventBus);
        this.context = context;
    }

    @Override
    public void onApply()
    {
        super.onApply();

        final ObjectListUIContext parentContext = context.getParentContext();
        ArrayList<String> uuids = new ArrayList<>(parentContext.getObjects().size());
        for (DtObject object : parentContext.getObjects())
        {
            uuids.add(UuidHelper.SAFE_UUID_EXTRACTOR.apply(object));
        }
        String ruleCode = SelectListPropertyValueExtractor.getValue(processingRule);

        dispatch.execute(new ReprocessMailsAction(uuids, ruleCode), new BasicCallback<EmptyResult>(getDisplay())
        {
            @Override
            public void handleSuccess(EmptyResult result)
            {
                unbind();
                EventBus contextEventBus = parentContext.getParentContext().getEventBus();
                contextEventBus.fireEvent(new ActionExecutedEvent(context.getParentContent()));
                contextEventBus.fireEvent(new RefreshContentEvent(context.getParentContent()));
                contextEventBus.fireEvent(new UpdateAdvlistSelectionEvent());
            }
        });
    }

}
