package ru.naumen.metainfoadmin.client.catalog.command;

import jakarta.inject.Inject;
import ru.naumen.core.client.adapter.IValueAdapter;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * Пока нет мультибиндинга - приходится делать так
 * <AUTHOR>
 * @since 12.08.2011
 */
public class CatalogCommandFactoryInitializer
{
    @Inject
    public CatalogCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<AddCatalogCommand, CommandParam<DtoContainer<Catalog>, Catalog>> addCatalogProvider,
            CommandProvider<AddCatalogItemCommand, CatalogItemCommandParam<DtObject>> addItemProvider,
            CommandProvider<CopyCatalogItemCommand, CatalogItemCommandParam<DtObject>> copyCatalogProvider,
            CommandProvider<ExportValueMapCatalogItemCommand, CatalogItemCommandParam<DtObject>> exportValueMapProvider,
            CommandProvider<ImportValueMapCatalogItemCommand, CatalogItemCommandParam<DtObject>> importValueMapProvider,
            CommandProvider<ImportValueMapsCommand, CatalogItemCommandParam<DtObject>> importValueMapsProvider,
            CommandProvider<DeleteCatalogCommand, CommandParam<Catalog, Void>> deleteCatalogProvider,
            CommandProvider<DeleteCatalogItemCommand, CatalogItemCommandParam<Void>> deleteItemProvider,
            CommandProvider<EditCatalogCommand, CommandParam<IValueAdapter<DtoContainer<Catalog>>, Catalog>> editCatalogProvider,
            CommandProvider<EditCatalogItemCommand, CatalogItemCommandParam<DtObject>> editItemProvider,
            CommandProvider<RemoveCatalogCommand, CommandParam<Catalog, Void>> removeCatalogProvider,
            CommandProvider<RemoveCatalogItemCommand, CatalogItemCommandParam<DtObject>> removeItemProvider,
            CommandProvider<RestoreCatalogCommand, CommandParam<Catalog, Void>> restoreCatalogProvider,
            CommandProvider<RestoreCatalogItemCommand, CatalogItemCommandParam<DtObject>> restoreItemProvider)

    {
        // @formatter:off
        factory.register(CatalogCommandCode.ADD_CATALOG,              addCatalogProvider);
        factory.register(CatalogCommandCode.ADD_CATALOG_ITEM,         addItemProvider);
        factory.register(CatalogCommandCode.COPY_CATALOG_ITEM,        copyCatalogProvider);
        factory.register(CatalogCommandCode.EXPORT_VMAP_CATALOG_ITEM, exportValueMapProvider);
        factory.register(CatalogCommandCode.IMPORT_VMAP_CATALOG_ITEM, importValueMapProvider);
        factory.register(CatalogCommandCode.IMPORT_VALUE_MAPS,        importValueMapsProvider);
        factory.register(CatalogCommandCode.DELETE_CATALOG,           deleteCatalogProvider);
        factory.register(CatalogCommandCode.DELETE_CATALOG_ITEM,      deleteItemProvider);
        factory.register(CatalogCommandCode.EDIT_CATALOG,             editCatalogProvider);
        factory.register(CatalogCommandCode.EDIT_CATALOG_ITEM,        editItemProvider);
        factory.register(CatalogCommandCode.REMOVE_CATALOG,           removeCatalogProvider);
        factory.register(CatalogCommandCode.REMOVE_CATALOG_ITEM,      removeItemProvider);
        factory.register(CatalogCommandCode.RESTORE_CATALOG,          restoreCatalogProvider);
        factory.register(CatalogCommandCode.RESTORE_CATALOG_ITEM,     restoreItemProvider);
        // @formatter:on
    }
}
