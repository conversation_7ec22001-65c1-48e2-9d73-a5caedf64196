package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.extended.columns.ExtendedListPresentationHelper;

/**
 * Форма добавления/редактирования атрибута на форме настройки системного вида по умолчанию
 *
 * <AUTHOR>
 * @since 08 мая 2015 г.
 */
public abstract class AdvlistAttributeFormPresenter extends OkCancelPresenter<PropertyFormDisplay>
{
    @Inject
    private AdvlistDefaultPrsMessages messages;
    @Inject
    protected ListBoxProperty prs;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private ExtendedListPresentationHelper listPresentationHelper;

    protected MetaClass relatedMetaClass;
    protected AdvlistSettingsDefault settings;
    protected ObjectListUIContext objectListContext;

    @Inject
    public AdvlistAttributeFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    protected abstract void addAttributeProperty();

    protected void initPrs(String attrCode)
    {
        final Attribute attr = objectListContext.getMode().getAttributes().get(attrCode);
        relatedMetaClass = null;
        if (Constants.LINK_ATTRIBUTE_TYPES.contains(attr.getType().getCode()))
        {
            metainfoService.getMetaClass(attr.getType().<ObjectAttributeType> cast().getRelatedMetaClass(),
                    new BasicCallback<MetaClass>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(MetaClass metaClass)
                        {
                            relatedMetaClass = metaClass;
                            fillPrs(attr);
                        }
                    });
        }
        else
        {
            fillPrs(attr);
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().asWidget().ensureDebugId("addAdvlistAttribute");
        addAttributeProperty();
        addPrsProperty();
    }

    private void addPrsProperty()
    {
        prs.setCaption(messages.attrPrsColumn());
        getDisplay().add(prs);
        DebugIdBuilder.ensureDebugId(prs, "prs");
    }

    private void fillPrs(Attribute attr)
    {
        prs.getValueWidget().clear();
        String[] prsCodes = listPresentationHelper.getAvailableListPresentations(attr,
                objectListContext.getMode().getOverriddenPrsCodes(),
                getPrsSettings(attr.getType().getCode()));
        Arrays.stream(prsCodes)
                .map(prs -> new SelectItem(prs, listPresentationHelper.getPresentationTitleForList(attr, prs)))
                .sorted(ITitled.IGNORE_CASE_COMPARATOR)
                .forEach(item ->
                {
                    if (item.getCode().equals(attr.getViewPresentation().getCode()))
                    {
                        prs.setValue(item);
                    }
                    prs.getValueWidget().addItem(item.getTitle(), item.getUUID());
                });
    }

    private Map<String, Object> getPrsSettings(String type)
    {
        if (Constants.LINK_ATTRIBUTE_TYPES.contains(type))
        {
            if ((BOLinksAttributeType.CODE.equals(type) || BackLinkAttributeType.CODE.equals(type)) && isSelfNested())
            {
                return Presentations.SELF_NESTED_LINK;
            }
            return isWithParent() ? Presentations.NESTED_LINK : Presentations.FLAT_LINK;
        }
        if (Constants.StringAttributeType.CODE.equals(type))
        {
            return CollectionUtils.map(Presentations.SYSTEM_METACLASS,
                    Constants.SYSTEM_METACLASSES.contains(objectListContext.getObjectList().getFqnOfClass()));
        }
        return null;
    }

    private boolean isSelfNested()
    {
        if (relatedMetaClass == null)
        {
            return false;
        }

        if (!relatedMetaClass.hasAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR))
        {
            return false;
        }

        ClassFqn fqn = relatedMetaClass.getAttribute(ru.naumen.core.shared.Constants.PARENT_ATTR).getType()
                .<ObjectAttributeType> cast().getRelatedMetaClass();

        if (relatedMetaClass.getFqn().isSameClass(fqn))
        {
            return true;
        }

        return false;
    }

    private boolean isWithParent()
    {
        //@formatter:off
        Collection<String> codes = relatedMetaClass != null 
                ? CollectionUtils.transform(relatedMetaClass.getAttributeFqns(), HasCode.CODE_EXTRACTOR) 
                : Collections.<String> emptyList();
        //@formatter:on

        //для невложенных удалим список выбора
        return codes.contains(ru.naumen.core.shared.Constants.PARENT_ATTR);
    }
}
