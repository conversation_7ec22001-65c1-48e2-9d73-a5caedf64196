package ru.naumen.metainfoadmin.client.toolbar.multi.reprocessmail;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;

/**
 *
 * <AUTHOR>
 *
 */
public class ReprocessMailGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
                   .implement(MassReprocessMailFormPresenter.class, MassReprocessMailFormPresenter.class)
                   .build(MassReprocessMailPresenterFactory.class));
        //@formatter:on

    }

}
