package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import java.util.HashMap;
import java.util.Map;

import com.google.inject.Provider;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.advimport.client.admin.config.AdvImportConfigMetainfoAdvlistProvider;
import ru.naumen.advimport.client.admin.connections.AdvImportConnectionMetainfoAdvlistProvider;
import ru.naumen.core.shared.Constants.AdminProfileMetainfo;
import ru.naumen.core.shared.Constants.CustomJSElementMetainfo;
import ru.naumen.core.shared.Constants.SettingsSetMetainfo;
import ru.naumen.core.shared.Constants.TagMetainfo;
import ru.naumen.mailreader.client.processor.MailProcessorRuleVersionMetainfoAdvlistProvider;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.AdvImportConfig;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.AdvImportConnection;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.FilterSettings;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Group;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.JMSQueue;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.MailProcessorRuleVersion;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ReportTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Role;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.SchedulerTask;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Script;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ScriptModule;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StyleTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageAttr;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageAttrGroup;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsageListTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Version.AdvImportConfigVersion;
import ru.naumen.metainfo.shared.ui.ApplicationList;
import ru.naumen.metainfo.shared.ui.CustomFormList;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.attributes.forms.usage.AttributeUsageMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.adminprofiles.AdminProfilesAdvlistMetainfoProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms.CustomFormMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customjs.CustomJSAdvlistMetainfoProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.ApplicationMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.EventActionMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings.FilterSettingsMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.sets.SettingsSetAdvlistMetainfoProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.tags.TagAdvlistMetainfoProvider;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.version.importconfig.AdvImportConfigVersionAdvlistMetainfoProvider;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.UsagePointsListApplicationMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.EventStorageRuleMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.group.usagelist.UsageAttrGroupMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.jmsqueue.JMSQueueAdvlistProvider;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerTasksMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.script.modules.ScriptModulesMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.script.scripts.ScriptsMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.groups.GroupsMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.roles.RolesMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsAdvlistProvider;
import ru.naumen.metainfoadmin.client.style.templates.StyleTemplatesMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.templates.list.ListTemplatesMetainfoAdvlistProvider;
import ru.naumen.metainfoadmin.client.templates.list.card.usagelist.UsageListTemplateMetainfoAdvlistProvider;
import ru.naumen.objectlist.client.gin.runtime.ObjectListMetainfoProviderRegistry;
import ru.naumen.objectlist.client.metainfo.ObjectListMetainfoProvider;
import ru.naumen.reports.client.metainfoadmin.tmpls.ReportTemplatesMetainfoAdvlistProvider;

/**
 * Реестр метаинфы для списочных контентов в ИА
 *
 * <AUTHOR>
 * @since 20 янв. 2017 г.
 */
@Singleton
public class AdminObjectListMetainfoProviderRegistry extends ObjectListMetainfoProviderRegistry
{
    private final Map<ClassFqn, Provider<? extends ObjectListMetainfoProvider>> customListProviders =
            new HashMap<>();

    @Inject
    public AdminObjectListMetainfoProviderRegistry(
            Provider<EventActionMetainfoAdvlistProvider> eventActionMetainfo,
            Provider<CustomFormMetainfoAdvlistProvider> customFormMetainfo,
            Provider<ApplicationMetainfoAdvlistProvider> applicationMetainfo,
            Provider<FilterSettingsMetainfoAdvlistProvider> settingFilteringMetainfo,
            Provider<TagAdvlistMetainfoProvider> tagMetainfo,
            Provider<SettingsSetAdvlistMetainfoProvider> setMetainfo,
            Provider<AdminProfilesAdvlistMetainfoProvider> adminProfileMetainfo,
            Provider<AdvImportConfigMetainfoAdvlistProvider> advImportConfigMetainfo,
            Provider<AdvImportConnectionMetainfoAdvlistProvider> advImportConnectionMetainfo,
            Provider<SchedulerTasksMetainfoAdvlistProvider> schedulerTasksMetainfo,
            Provider<GroupsMetainfoAdvlistProvider> groupsMetainfo,
            Provider<RolesMetainfoAdvlistProvider> rolesMetainfo,
            Provider<ReportTemplatesMetainfoAdvlistProvider> reportTemplatesMetainfo,
            Provider<ScriptModulesMetainfoAdvlistProvider> scriptModulesMetainfo,
            Provider<ScriptsMetainfoAdvlistProvider> scriptsMetainfo,
            Provider<StyleTemplatesMetainfoAdvlistProvider> styleTemplatesMetainfo,
            Provider<CustomJSAdvlistMetainfoProvider> customJSMetainfo,
            Provider<MailProcessorRuleVersionMetainfoAdvlistProvider> mailProcessorRuleVersionMetainfo,
            Provider<UsageAttrGroupMetainfoAdvlistProvider> usageAttrGroupMetainfoAdvlistProvider,
            Provider<AttributeUsageMetainfoAdvlistProvider> usageAttrMetainfoAdvlistProvider,
            Provider<ListTemplatesMetainfoAdvlistProvider> listTemplatesMetainfo,
            Provider<UsageListTemplateMetainfoAdvlistProvider> usageListTemplateMetainfoAdvlistProvider,
            Provider<AdvImportConfigVersionAdvlistMetainfoProvider> advImportConfigVersionMetainfo,
            Provider<StructuredObjectsViewsAdvlistProvider> structuredObjectsViewsMetainfo,
            Provider<JMSQueueAdvlistProvider> jmsQueueAdvlistProviderProvider,
            Provider<ContentTemplateMetainfoAdvlistProvider> contentTemplateMetainfoAdvlistProvider,
            Provider<UsagePointsListApplicationMetainfoAdvlistProvider> usagePointsListApplicationMetainfoAdvlistProvider,
            Provider<EventStorageRuleMetainfoAdvlistProvider> eventStorageRuleMetainfoAdvlistProviderProvider)
    {
        register(EventActionList.class, eventActionMetainfo);
        register(CustomFormList.class, customFormMetainfo);
        register(ApplicationList.class, applicationMetainfo);

        registerCustom(FilterSettings.FQN, settingFilteringMetainfo);
        registerCustom(AdvImportConfig.FQN, advImportConfigMetainfo);
        registerCustom(AdvImportConnection.FQN, advImportConnectionMetainfo);
        registerCustom(SchedulerTask.FQN, schedulerTasksMetainfo);
        registerCustom(Group.FQN, groupsMetainfo);
        registerCustom(Role.FQN, rolesMetainfo);
        registerCustom(ReportTemplate.FQN, reportTemplatesMetainfo);
        registerCustom(TagMetainfo.FQN, tagMetainfo);
        registerCustom(SettingsSetMetainfo.FQN, setMetainfo);
        registerCustom(AdminProfileMetainfo.FQN, adminProfileMetainfo);
        registerCustom(ScriptModule.FQN, scriptModulesMetainfo);
        registerCustom(Script.FQN, scriptsMetainfo);
        registerCustom(StyleTemplate.FQN, styleTemplatesMetainfo);
        registerCustom(CustomJSElementMetainfo.FQN, customJSMetainfo);
        registerCustom(MailProcessorRuleVersion.FQN, mailProcessorRuleVersionMetainfo);
        registerCustom(UsageAttrGroup.FQN, usageAttrGroupMetainfoAdvlistProvider);
        registerCustom(UsageAttr.FQN, usageAttrMetainfoAdvlistProvider);
        registerCustom(ListTemplate.FQN, listTemplatesMetainfo);
        registerCustom(UsageListTemplate.FQN, usageListTemplateMetainfoAdvlistProvider);
        registerCustom(AdvImportConfigVersion.FQN, advImportConfigVersionMetainfo);
        registerCustom(StructuredObjectsView.FQN, structuredObjectsViewsMetainfo);
        registerCustom(JMSQueue.FQN, jmsQueueAdvlistProviderProvider);
        registerCustom(ContentTemplate.FQN, contentTemplateMetainfoAdvlistProvider);
        registerCustom(UsagePointApplication.FQN, usagePointsListApplicationMetainfoAdvlistProvider);
        registerCustom(EventStorageRule.FQN, eventStorageRuleMetainfoAdvlistProviderProvider);
    }

    /**
     * Зарегистрировать ObjectListMetainfoProvider для наследников CustomList
     */
    public void registerCustom(ClassFqn contentType,
            Provider<? extends ObjectListMetainfoProvider> provider)
    {
        customListProviders.put(contentType, provider);
    }

    @Override
    public ObjectListMetainfoProvider get(ObjectListBase content)
    {
        Provider<? extends ObjectListMetainfoProvider> provider = null;
        if (content.getClazz() != null)
        {
            provider = customListProviders.get(content.getClazz());
        }
        return provider == null
                ? super.get(content)
                : provider.get();
    }
}
