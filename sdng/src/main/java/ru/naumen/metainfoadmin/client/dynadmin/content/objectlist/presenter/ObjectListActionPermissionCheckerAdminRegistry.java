package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.inject.Provider;

import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.script.modules.ScriptModulesActionPermissionCheckerImpl;
import ru.naumen.metainfoadmin.client.script.scripts.ScriptsActionPermissionCheckerImpl;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.groups.GroupsActionPermissionCheckerImpl;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.roles.RolesActionPermissionCheckerImpl;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionPermissionChecker;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionPermissionCheckerRegistry;
import ru.naumen.objectlist.shared.CustomList;

/**
 * <AUTHOR>
 * @since 18 янв. 2017 г.
 */
@Singleton
public class ObjectListActionPermissionCheckerAdminRegistry implements ObjectListActionPermissionCheckerRegistry
{
    @Inject
    private ObjectListActionPermissionCheckerAdminImpl checker;
    @Inject
    private Provider<RolesActionPermissionCheckerImpl> rolesChecker;
    @Inject
    private Provider<GroupsActionPermissionCheckerImpl> groupsChecker;
    @Inject
    private Provider<ScriptModulesActionPermissionCheckerImpl> scriptModulesChecker;
    @Inject
    private Provider<ScriptsActionPermissionCheckerImpl> scriptsChecker;

    @Override
    public ObjectListActionPermissionChecker get(ListComponents components)
    {
        ObjectListActionPermissionChecker result;
        ObjectListBase content = components.getContent();
        if (content instanceof CustomList && content.getClazz() != null
            && FakeMetaClassesConstants.FQNS_METAINFO_DATA.contains(content.getClazz()))
        {
            if (content.getClazz().equals(FakeMetaClassesConstants.Role.FQN))
            {
                result = rolesChecker.get();
            }
            else if (content.getClazz().equals(FakeMetaClassesConstants.Group.FQN))
            {
                result = groupsChecker.get();
            }
            else if (content.getClazz().equals(FakeMetaClassesConstants.ScriptModule.FQN))
            {
                result = scriptModulesChecker.get();
            }
            else if (content.getClazz().equals(FakeMetaClassesConstants.Script.FQN))
            {
                result = scriptsChecker.get();
            }
            else
            {
                result = checker;
            }
        }
        else
        {
            result = checker;
        }
        result.init(components);
        return result;
    }

}
