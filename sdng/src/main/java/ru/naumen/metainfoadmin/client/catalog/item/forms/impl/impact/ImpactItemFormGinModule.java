/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.impact;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyAfterBindHandlerEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProviderWithCatalogItemImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemParentPropertyBindDelegateEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormContextGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemEditFormPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormPropertyControllerFactoryImpl;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public class ImpactItemFormGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off       
        install(CatalogItemFormContextGinModule.create(ImpactItemFormContext.class));
        
        install(CatalogItemFormGinModule.create(ImpactItemFormContext.class, ObjectFormAdd.class)
                .setPropertControllerFactory(       new TypeLiteral<DefaultItemFormPropertyControllerFactoryImpl<ImpactItemFormContext, ObjectFormAdd>>(){}));
        
        install(CatalogItemFormGinModule.create(ImpactItemFormContext.class, ObjectFormEdit.class)
                .setPropertControllerFactory(       new TypeLiteral<DefaultItemEditFormPropertyControllerFactoryImpl<ImpactItemFormContext, ObjectFormEdit>>(){})
                .setPropertyValuesProvider(         new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<ImpactItemFormContext, ObjectFormEdit>>(){})
                .setAfterBindHandler(               new TypeLiteral<CatalogItemFormPropertyAfterBindHandlerEditImpl<ImpactItemFormContext>>(){})
                .setParentBindDelegate(             new TypeLiteral<CatalogItemParentPropertyBindDelegateEditImpl<ImpactItemFormContext>>(){}));
        
        install(CatalogItemFormGinModule.create(ImpactItemFormContext.class, ObjectFormCopy.class)
                .setPropertControllerFactory(       new TypeLiteral<DefaultItemFormPropertyControllerFactoryImpl<ImpactItemFormContext, ObjectFormCopy>>(){})
                .setPropertyValuesProvider(         new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<ImpactItemFormContext, ObjectFormCopy>>(){}));
        //@formatter:on
    }
}