package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.HasObjectsRelation;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.IObjectListFeature;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Вывод прямых и обратных ссылок (на один и тот же тип) в одном списке.
 * Логика для чекбокса "Показывать объекты с двух сторон связи"
 * на форме добавления и радактирования контента "Список связанных объектов"
 * NSDPRD-1984
 *
 * <AUTHOR>
 *
 */
public class ShowLinkedItemsFeature implements IObjectListFeature
{
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    Property<Boolean> showLinkedObjects;

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    AttrReference linkedAttrReference;

    HasProperties.PropertyRegistration<Boolean> propertyRegistration = null;

    @Inject
    CommonMessages messages;

    String attrCode = null;

    boolean initialized = false;

    @Override
    public void addControlsToAddForm(MetaClass objectClass, FormPropertiesCreator container)
    {
        initLabel();
        // TODO нужно избавиться от захардкоженных индексов
        container.add(showLinkedObjects, 5);
    }

    @Override
    public void addControlsToEditForm(MetaClass objectClass, PropertyDialogDisplay container)
    {
        initLabel();
        // TODO нужно избавиться от захардкоженных индексов
        propertyRegistration = container.addProperty(showLinkedObjects, 7);
    }

    public Property<Boolean> getShowLinkedObjectsProperty()
    {
        return showLinkedObjects;
    }

    @Override
    public HasObjectsRelation updateContent(HasObjectsRelation content)
    {
        content.setShowLinkedObjects(showLinkedObjects.getValue());
        content.setAttrLinkCode(attrCode);
        return content;
    }

    @Override
    public void updateControls(MetaClass objectClass, @Nullable RelationsAttrTreeObject attrObject,
            @Nullable HasObjectsRelation content)
    {
        if (null != attrObject)
        {
            updateShowLinkedLabel(objectClass, attrObject);
        }

        // Если это открытие формы редактирования контента берем текущее значение из контента
        if (null != content && !initialized)
        {
            showLinkedObjects.setValue(content.isShowLinkedObjects(), true);
            initialized = true;
        }
        if (attrObject.getAttribute() != null)
        {
            attrCode = attrObject.getAttribute().getCode();
        }
    }

    public void updateShowLinkedLabel(final MetaClass objectClass, @Nullable RelationsAttrTreeObject attrObject)
    {
        disableAnHideLabel();

        if (attrObject == null || attrObject.getParent() != null)
        {
            return;
        }

        Attribute attribute = attrObject.getAttribute();

        // пропускаем
        if (attribute == null || isMassProblemAttribute(attribute))
        {
            return;
        }

        ClassFqn cls = attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        if (cls != null && (!cls.getId().equals(objectClass.getFqn().getId())))
        {
            return;
        }

        if (!BOLinksAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            return;
        }
        enableAndShowLabel(false);
    }

    private void disableAnHideLabel()
    {
        showLinkedObjects.setValue(false);
        showLinkedObjects.asWidget().setVisible(false);
        showLinkedObjects.setEnabled(false);

        if (propertyRegistration != null)
        {
            propertyRegistration.setEnabled(false);
        }
    }

    private void enableAndShowLabel(boolean oldValue)
    {
        showLinkedObjects.setValue(oldValue);
        showLinkedObjects.asWidget().setVisible(true);
        showLinkedObjects.setEnabled(true);

        if (propertyRegistration != null)
        {
            propertyRegistration.setEnabled(true);
        }
    }

    private void initLabel()
    {
        DebugIdBuilder.ensureDebugId(showLinkedObjects, "show-linked-objects");
        showLinkedObjects.setCaption(messages.showLinkedObjects());
        showLinkedObjects.asWidget().setVisible(false);
        showLinkedObjects.setEnabled(true);
    }

    private boolean isMassProblemAttribute(Attribute attribute)
    {
        return attribute.getCode().equalsIgnoreCase("masterMassProblem")
               || attribute.getCode().equalsIgnoreCase("massProblem")
               || attribute.getCode().equalsIgnoreCase("massProblemSlaves");
    }
}
