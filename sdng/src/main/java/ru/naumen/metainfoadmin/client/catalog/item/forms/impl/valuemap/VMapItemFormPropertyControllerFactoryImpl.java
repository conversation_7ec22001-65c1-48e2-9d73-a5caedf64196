/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import jakarta.inject.Provider;

import java.util.HashMap;

import com.google.inject.Inject;

import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.validation.CatalogItemCodeValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.MultiSelectProperty;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.TextAreaProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormIsNotFolderRefreshDelegate;

/**
 * <AUTHOR>
 * @since 19.10.2012
 *
 */
public class VMapItemFormPropertyControllerFactoryImpl<C extends ValueMapItemFormContext, F extends ObjectForm> extends
        CatalogItemFormPropertyControllerFactoryImpl<C, F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextAreaProperty> textAreaPropertyFactory;
    @Inject
    PropertyControllerSyncFactoryInj<List<String>, MultiSelectProperty> multiSelectPropertyFactory;
    @Inject
    LinkedMetaClassesPropertyControllerFactory<F> linkedMetaClassesPropertyFactory;

    @Inject
    DefaultItemFormIsNotFolderRefreshDelegate<Collection<DtObject>, PropertyBase<Collection<DtObject>,
            PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly>>> linkedClassesRefreshDelegate;
    @Inject
    DefaultItemFormIsNotFolderRefreshDelegate<String, TextAreaProperty> descriptionRefreshDelegate;

    @Inject
    LinkedMetaClassesVCHDelegateImpl<C> linkedClassesVCHDelegate;

    @Inject
    VMapItemFormAttrsRefreshDelegateFactory attrsRefreshDelegateFactory;

    @Inject
    private VMapItemFormMultiSelectDnDBind multiSelectDnDBind;

    private final Map<Validator<List<String>>, String> sourceAttrValidators = new HashMap<>();
    private final Map<Validator<List<String>>, String> targetAttrValidators = new HashMap<>();

    @Inject
    public void setUpValidators(Provider<NotEmptyCollectionValidator<List<String>>> notEmptyCollectionValidator,
            CatalogItemCodeValidator catalogItemCodeValidator)
    {
        codeValidators.put(catalogItemCodeValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        sourceAttrValidators.put(notEmptyCollectionValidator.get(),
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        targetAttrValidators.put(notEmptyCollectionValidator.get(),
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        super.build();
        //@formatter:off
        register(ValueMapCatalogItem.LINKED_CLASSES,linkedMetaClassesPropertyFactory)
            .setVchDelegate(linkedClassesVCHDelegate)
            .setRefreshDelegate(linkedClassesRefreshDelegate);
        register(ValueMapCatalogItem.TARGET_ATTRS,multiSelectPropertyFactory)
            .setBindDelegate(multiSelectDnDBind)
            .setVchDelegate(new VMapItemAttrsVCHDelegateImpl(true))
            .setRefreshDelegate(attrsRefreshDelegateFactory.create(ValueMapCatalogItem.TARGET_ATTRS, "target"))
            .setValidators(targetAttrValidators);
        register(ValueMapCatalogItem.SOURCE_ATTRS,multiSelectPropertyFactory)
            .setBindDelegate(multiSelectDnDBind)
            .setVchDelegate(new VMapItemAttrsVCHDelegateImpl(false))
            .setRefreshDelegate(attrsRefreshDelegateFactory.create(ValueMapCatalogItem.SOURCE_ATTRS, "source"))
            .setValidators(sourceAttrValidators);
        register(ValueMapCatalogItem.DESCRIPTION,textAreaPropertyFactory)
            .setRefreshDelegate(descriptionRefreshDelegate);
        //@formatter:on
    }
}