package ru.naumen.metainfoadmin.client.widgets.properties;

import static ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerGinModuleFactory.createSyncFactory;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.RolesAndGroupProperty;
import ru.naumen.core.client.widgets.properties.condition.ConditionFormProvider;
import ru.naumen.core.client.widgets.properties.condition.DefaultConditionFormProvider;
import ru.naumen.core.client.widgets.properties.condition.DefaultConditionFormProviderFactory;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.sec.matrix.checkboxmatrix.accessmatrix.profile.form.RolesAndGroups;
import ru.naumen.metainfoadmin.client.widgets.properties.container.factory.AdminPropertyControllerFactoriesGinModule;

/**
 * <AUTHOR>
 * @since Dec 18, 2015
 */
public class AdminPropertiesGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new AdminPropertyControllerFactoriesGinModule());

        install(new GinFactoryModuleBuilder()
                .implement(ConditionFormProvider.class, DefaultConditionFormProvider.class)
                .build(DefaultConditionFormProviderFactory.class));
        install(createSyncFactory(String.class, ScriptEditProperty.class));
        install(createSyncFactory(ScriptDto.class, ScriptComponentEditProperty.class));
        bind(new TypeLiteral<Property<ScriptDto>>()
        {
        }).annotatedWith(Names.named(PropertiesGinModule.SCRIPT_COMPONENT_EDIT)).to(ScriptComponentEditProperty.class);
        bind(new TypeLiteral<Property<ScriptDto>>()
        {
        }).annotatedWith(Names.named(PropertiesGinModule.SCRIPT_COMPONENT_VIEW)).to(ScriptComponentViewProperty.class);
        bind(new TypeLiteral<Property<String>>()
        {
        }).annotatedWith(Names.named(PropertiesGinModule.SCRIPT_VIEW)).to(ScriptViewProperty.class);
        bind(new TypeLiteral<Property<String>>()
        {
        }).annotatedWith(Names.named(PropertiesGinModule.SCRIPT_EDIT)).to(ScriptEditProperty.class);
        bind(new TypeLiteral<Property<RolesAndGroups>>()
        {
        }).annotatedWith(Names.named(PropertiesGinModule.ROLES_AND_GROUPS)).to(RolesAndGroupProperty.class);
    }
}
