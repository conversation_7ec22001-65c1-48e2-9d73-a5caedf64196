/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.components;

import java.util.ArrayList;
import java.util.function.Function;

import com.google.inject.Inject;
import com.google.inject.Provider;

import jakarta.inject.Named;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.container.AttributePropertyDescription;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.CachedMetainfoServiceAsync;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.catalog.CatalogContext;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;
import ru.naumen.metainfoadmin.client.sets.formatters.SettingsSetPropertyFormatter;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Provider списка объектов, описывающих атрибуты, которые будут отображаться на карточке справочника. 
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public class CatalogInfoPropertyDescProvider<C extends CatalogContext> implements
        Provider<ArrayList<AttributePropertyDescription<?, C>>>
{
    @Inject
    protected CatalogMessages messages;
    @Inject
    private AdminDialogMessages adminDialogMessages;
    @Inject
    CommonMessages cmessages;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Provider<Property<String>> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Provider<Property<String>> code;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    Provider<Property<Boolean>> flat;
    @Inject
    @Named(PropertiesGinModule.BOOLEAN_IMAGE)
    Provider<Property<Boolean>> withFolders;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Provider<Property<String>> description;
    @Inject
    SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    CachedMetainfoServiceAsync cachedMetainfoServiceAsync;
    @Inject
    SettingsSetPropertyFormatter settingsSetPropertyFormatter;

    @Override
    public ArrayList<AttributePropertyDescription<?, C>> get()
    {
        ArrayList<AttributePropertyDescription<?, C>> result = new ArrayList<>();
        result.add(new AttributePropertyDescription<>(cmessages.title(), title.get(), "title",
                input -> input == null ? "" : input.getCatalog().get().getTitle()));
        result.add(new AttributePropertyDescription<>(cmessages.catalogCode(), code.get(), "code",
                input -> input == null ? "" : input.getCatalog().get().getCode()));
        result.add(new AttributePropertyDescription<>(messages.flat(), flat.get(), "flat",
                input -> input == null || input.getCatalog().get().isFlat()));
        result.add(new AttributePropertyDescription<>(messages.withFolders(), withFolders.get(),
                "withFolders", input -> input != null && input.getCatalog().get().isWithFolders()));
        result.add(new AttributePropertyDescription<>(cmessages.description(), description.get(),
                "description", input -> input == null ? "" : input.getCatalog().get().getDescription()));
        if (settingsSetOnFormCreator.isDisplayedOnCards())
        {
            result.add(new AttributePropertyDescription<>(adminDialogMessages.settingsSet(),
                    settingsSetOnFormCreator.createFieldOnCard(), "Info.settingsSet",
                    getSettingsSetFunction()));
        }
        return result;
    }

    private Function<C, String> getSettingsSetFunction()
    {
        return input ->
        {
            if (input == null)
            {
                return "";
            }
            DtObject settingsSet =
                    cachedMetainfoServiceAsync.getSettingsSet(input.getCatalog().get().<String> getSettingsSet());
            if (settingsSet == null)
            {
                return "";
            }
            return settingsSetPropertyFormatter.format(settingsSet.getUUID(), settingsSet.getTitle()).asString();
        };
    }
}