/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.priority;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.PriorityCatalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 19.10.2012
 *
 */
public class PriorityItemFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm> extends
        DefaultItemFormPropertyParametersDescriptorFactoryImpl<PriorityItemFormContext, F>
{
    @Override
    protected void build()
    {
        registerOrModifyProperty(Constants.CatalogItem.ITEM_TITLE, cmessages.title(), true,
                Constants.CatalogItem.ITEM_TITLE, 0, true, true);
        registerOrModifyProperty(Constants.CatalogItem.ITEM_CODE, cmessages.code(), true,
                Constants.CatalogItem.ITEM_CODE, 1, true, true);
        registerOrModifyProperty(Constants.CatalogItem.ITEM_PARENT, cmessages.parent(), false,
                Constants.CatalogItem.ITEM_PARENT, 2, true, true);
        registerOrModifyProperty(CatalogItem.ITEM_COLOR, cmessages.color(), false, CatalogItem.ITEM_COLOR, 3, true,
                true);
        registerOrModifyProperty(CatalogItem.ITEM_ICON, cmessages.icon(), false, CatalogItem.ITEM_ICON, 4, true,
                true);

        //@formatter:off
        registerOrModifyProperty(PriorityCatalog.LEVEL, cmessages.level(), true, PriorityCatalog.LEVEL, 5, true, true);
        registerOrModifyProperty(Constants.CatalogItem.SETTINGS_SET,   adminDialogMessages.settingsSet(),         false,
                "settingsSet", 6, true, false);
        //@formatter:on
    }
}