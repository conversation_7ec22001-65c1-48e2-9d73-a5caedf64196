/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.columns;

import java.util.HashMap;
import java.util.Map;

import com.google.inject.Provider;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.core.shared.Constants.PriorityCatalog;
import ru.naumen.core.shared.Constants.TimezoneCatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.TableDisplay.ColumnInfo;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;

/**
 *
 * <AUTHOR>
 *
 */
@Singleton
public class CatalogColumnInfoFactoryImpl implements CatalogColumnInfoFactory
{
    @Inject
    CatalogCommandColumnFactory commandColumnFactory;
    @Inject
    CatalogColumnGinModule.CatalogColumnFactory columnFactory;
    @Inject
    Provider<TitleAttributeColumn> titleColumnProvider;

    private Map<String, FactoryColumnInfo> factoryColumnInfos;
    private Map<String, String> columnTitles;
    private Map<String, FactoryColumnInfo> attributeColumnInfos;

    @Inject
    CommandFactory commandFactory;

    @Override
    public ColumnInfo createAttributeColumn(Attribute attr)
    {
        IsCatalogColumn<?> column = createColumn(attr);
        String title = createTitle(attr);
        String style = createStyle(attr);
        column.asColumn().setCellStyleNames(style);
        ColumnInfo result = new ColumnInfo(column, title, attr.getCode());
        if (attr.getCode().equals(CatalogItem.ITEM_CODE) || attr.getCode().equals(CatalogItem.ITEM_TITLE))
        {
            result.column.asColumn().setSortable(true);
        }
        return result;
    }

    @Override
    public ColumnInfo createCommandColumn(String code, CommandParam<?, ?> param, String... commands)
    {
        return createCommandColumn(code, (Map)commandFactory.create(param, commands));
    }

    public ColumnInfo createCommandColumn(String code, Map<String, BaseCommand<DtObject, ?>> commands)
    {
        IsCatalogColumn<?> column = commandColumnFactory.create(code);
        ((AbstractCatalogImageColumn)column.asColumn()).init(commands);
        column.asColumn().setCellStyleNames(factoryColumnInfos.get(code).style);
        return new ColumnInfo(column, DEFAULT_COLUMN_TITLE);
    }

    @Inject
    public void initAttributeColumnInfos(AdminWidgetResources resources)
    {
        //@formatter:off
        attributeColumnInfos = new HashMap<>();
        attributeColumnInfos.put(Constants.CatalogItem.ITEM_COLOR, new FactoryColumnInfo(resources.tables().tableImageCell()));
        //@formatter:on
    }

    @Inject
    public void initColumnTitles(CommonMessages messages, CatalogMessages catalogMessages)
    {
        columnTitles = new HashMap<>();
        columnTitles.put(CatalogItem.ITEM_COLOR, "");
        columnTitles.put(CatalogItem.ITEM_CODE, messages.code());
        columnTitles.put(CatalogItem.ITEM_TITLE, messages.title());
        columnTitles.put(FolderCatalog.DESCRIPTION, messages.description());
        columnTitles.put(PriorityCatalog.LEVEL, messages.level());
        columnTitles.put(FolderCatalog.OBJECTS, messages.objects());
        columnTitles.put(ValueMapCatalogItem.TARGET_ATTRS, messages.targetAttrCodes());
        columnTitles.put(ValueMapCatalogItem.SOURCE_ATTRS, messages.sourceAttrCodes());
        columnTitles.put(TimezoneCatalogItem.TRANSITIONAL, catalogMessages.transitional());
        columnTitles.put(TimezoneCatalogItem.CURRENT_TIME_TZ, catalogMessages.currentServerTime());
    }

    @Inject
    public void initFactoryColumnInfos(AdminWidgetResources resources)
    {
        //@formatter:off
        factoryColumnInfos = new HashMap<>();
        factoryColumnInfos.put(ColumnCode.DEFAULT_ATTRIBUTE,    new FactoryColumnInfo(""));
        factoryColumnInfos.put(ColumnCode.COPY,                 new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.DELETE,               new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.EDIT,                 new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.RESTORE,              new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.REMOVE,               new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.REMOVE_RESTORE,       new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.EXPORT,               new FactoryColumnInfo(resources.tables().tableRowIcons()));
        factoryColumnInfos.put(ColumnCode.TITLE_ATTRIBUTE,      new FactoryColumnInfo(resources.tables().tableElemsTDLeft()));
        //@formatter:on
    }

    private IsCatalogColumn<?> createColumn(Attribute attr)
    {
        if (attr.getCode().equals(CatalogItem.ITEM_TITLE))
        {
            return titleColumnProvider.get();
        }
        else if (attr.getCode().equals(TimezoneCatalogItem.CURRENT_TIME_TZ))
        {
            return columnFactory.currentTimeAttribute(attr);
        }
        else
        {
            return columnFactory.defaultAttribute(attr);
        }
    }

    private String createStyle(Attribute attr)
    {
        String result = "";
        if (attr.getCode().equals(CatalogItem.ITEM_TITLE))
        {
            result = factoryColumnInfos.get(ColumnCode.TITLE_ATTRIBUTE).style;
        }
        else
        {
            result = factoryColumnInfos.get(ColumnCode.DEFAULT_ATTRIBUTE).style;
        }
        if (attributeColumnInfos.containsKey(attr.getCode()))
        {
            result += " " + attributeColumnInfos.get(attr.getCode()).style;
        }
        return result;
    }

    private String createTitle(Attribute attr)
    {
        if (columnTitles.containsKey(attr.getCode()))
        {
            return columnTitles.get(attr.getCode());
        }
        else
        {
            return attr.getTitle();
        }
    }
}
