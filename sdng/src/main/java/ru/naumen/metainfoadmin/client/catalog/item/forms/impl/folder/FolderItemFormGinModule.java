package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyAfterBindHandlerEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProviderWithCatalogItemImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormContextGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.converters.FolderItemFormPropertyMapConverterImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.messages.FolderItemFormMessagesAdd;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.messages.FolderItemFormMessagesCopy;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.messages.FolderItemFormMessagesEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.parent.FolderItemParentPropertyBindDelegateAddImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.parent.FolderItemParentPropertyBindDelegateEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.parent.FolderItemParentPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.validation.FolderItemFormCodePropertyValidatorDelegate;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 22.12.2012
 */
public class FolderItemFormGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off       
        install(CatalogItemFormContextGinModule.create(FolderItemFormContext.class)
                .setConstants(                              new TypeLiteral<FolderItemFormConstants>(){})
                .setCodePropertyValidatorDelegate(          new TypeLiteral<FolderItemFormCodePropertyValidatorDelegate>(){}));
        //@formatter:on

        bindFormAdd();
        bindFormEdit();
        bindFormCopy();
    }

    private void bindFormAdd()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(FolderItemFormContext.class, ObjectFormAdd.class)
                .setPropertControllerFactory(   new TypeLiteral<FolderItemFormPropertyControllerFactoryImpl<ObjectFormAdd>>(){})
                .setMapConverter(               new TypeLiteral<FolderItemFormPropertyMapConverterImpl<ObjectFormAdd>>(){})
                .setMessages(                   new TypeLiteral<FolderItemFormMessagesAdd>(){})
                .setParentBindDelegate(         new TypeLiteral<FolderItemParentPropertyBindDelegateAddImpl>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<FolderItemFormPropertyParametersDescriptorFactoryImpl<ObjectFormAdd>>(){}));
        //@formatter:on
    }

    private void bindFormCopy()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(FolderItemFormContext.class, ObjectFormCopy.class)
                .setPropertControllerFactory(   new TypeLiteral<FolderItemFormPropertyControllerFactoryImpl<ObjectFormCopy>>(){})
                .setPropertyValuesProvider(     new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<FolderItemFormContext, ObjectFormCopy>>(){})
                .setMapConverter(               new TypeLiteral<FolderItemFormPropertyMapConverterImpl<ObjectFormCopy>>(){})
                .setMessages(                   new TypeLiteral<FolderItemFormMessagesCopy>(){})
                .setParentBindDelegate(         new TypeLiteral<FolderItemParentPropertyBindDelegateImpl<ObjectFormCopy>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<FolderItemFormPropertyParametersDescriptorFactoryImpl<ObjectFormCopy>>(){}));
        //@formatter:on
    }

    private void bindFormEdit()
    {
        //@formatter:off
        install(CatalogItemFormGinModule.create(FolderItemFormContext.class, ObjectFormEdit.class)
                .setPropertControllerFactory(   new TypeLiteral<FolderItemEditFormPropertyControllerFactoryImpl<ObjectFormEdit>>(){})
                .setPropertyValuesProvider(     new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<FolderItemFormContext, ObjectFormEdit>>(){})
                .setAfterBindHandler(           new TypeLiteral<CatalogItemFormPropertyAfterBindHandlerEditImpl<FolderItemFormContext>>(){})
                .setMapConverter(               new TypeLiteral<FolderItemFormPropertyMapConverterImpl<ObjectFormEdit>>(){})
                .setMessages(                   new TypeLiteral<FolderItemFormMessagesEdit>(){})
                .setParentBindDelegate(         new TypeLiteral<FolderItemParentPropertyBindDelegateEditImpl>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<FolderItemFormPropertyParametersDescriptorFactoryImpl<ObjectFormEdit>>(){}));
        //@formatter:on
    }
}