package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.ColorTextBoxProperty;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.ActionAreaBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AppliedToTypeBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AttributeForFillByCurrentObjectBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AttributeGroupForAddFileBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.AttributeToSaveFileBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.EditFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.IconBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.InvocationMethodBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.MassEditFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.PresentationTypeBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.QuickFormBindDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate.TitleBindDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ActionAreaRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ActionBackgroundColorRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ActionRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AddFileRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AllowInMassOperationsRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AppliedToTypeRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.AttributeForFillByCurrentObjectRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.CustomFormRefreshDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.EditFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.GeoRequiredRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.GoToCardAfterCreationRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.IconRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.InvocationMethodRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ObjectActionRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.PresentationTypeRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.ReturnToListAfterCreationRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.RowIconRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.SystemActionRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.UseMassEditFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.UseQuickAddFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate.UseQuickEditFormRefreshDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.AppliedToTypeVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.AttributeGroupForAddFileVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.EditFormVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.InvocationMethodVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.ObjectActionVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.PresentationTypeVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.QuickFormVCHDelegete;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate.UseCustomFormVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;

/**
 * Фабрика контроллеров редактируемых свойств настраиваемой кнопки.
 * <AUTHOR>
 * @since Feb 28, 2019
 */
public class ToolFormPropertyControllerFactory extends PropertyControllerFactorySyncImpl<Tool, ObjectForm>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Boolean, BooleanCheckBoxProperty> checkBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, DtObjectSelectProperty> dtoSelectPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Color, ColorTextBoxProperty> backgroundColorTextBoxProperty;

    @Inject
    private PresentationTypeBindDelegate presentationTypeBindDelegate;
    @Inject
    private AppliedToTypeBindDelegate appliedToTypeBindDelegate;
    @Inject
    private SettingsSetBindDelegate settingsSetOnActionToolBindDelegate;
    @Inject
    private IconBindDelegate iconBindDelegate;
    @Inject
    private AddFileRefreshDelegate addFileRefreshDelegate;
    @Inject
    private MassEditFormBindDelegate massEditFormBindDelegate;
    @Inject
    private AttributeToSaveFileBindDelegate attributeToSaveFileBindDelegate;
    @Inject
    private AttributeGroupForAddFileBindDelegate attributeGroupForAddFileBindDelegate;
    @Inject
    private AttributeGroupForAddFileVCHDelegate attributeGroupForAddFileVCHDelegate;
    @Inject
    private AttributeForFillByCurrentObjectBindDelegate attributeForFillByCurrentObjectBindDelegate;
    @Inject
    private AttributeForFillByCurrentObjectRefreshDelegate attributeForFillByCurrentObjectRefreshDelegate;
    @Inject
    private TitleBindDelegate titleBindDelegate;
    @Inject
    private InvocationMethodBindDelegate invocationMethodBindDelegate;
    @Inject
    private AppliedToTypeRefreshDelegate appliedToTypeRefreshDelegate;
    @Inject
    private SettingsSetRefreshDelegate settingsSetOnActionToolRefreshDelegate;
    @Inject
    private IconRefreshDelegate iconRefreshDelegate;
    @Inject
    private RowIconRefreshDelegate rowIconRefreshDelegate;
    @Inject
    private UseMassEditFormRefreshDelegate useMassEditFormRefreshDelegate;
    @Inject
    private UseQuickAddFormRefreshDelegate useQuickAddFormRefreshDelegate;
    @Inject
    private UseQuickEditFormRefreshDelegate useQuickEditFormRefreshDelegate;
    @Inject
    private ActionRefreshDelegate actionRefreshDelegate;
    @Inject
    private SystemActionRefreshDelegate systemActionRefreshDelegate;
    @Inject
    private ReturnToListAfterCreationRefreshDelegate returnToListAfterCreationRefreshDelegate;
    @Inject
    private InvocationMethodRefreshDelegate invocationMethodRefreshDelegate;
    @Inject
    private ObjectActionRefreshDelegate objectActionRefreshDelegate;
    @Inject
    private PresentationTypeRefreshDelegate presentationTypeRefreshDelegate;
    @Inject
    private GeoRequiredRefreshDelegate geoRequiredRefreshDelegate;
    @Inject
    private GoToCardAfterCreationRefreshDelegate goToCardAfterCreationRefreshDelegate;
    @Inject
    private AllowInMassOperationsRefreshDelegate allowInMassOperationsRefreshDelegate;
    @Inject
    private PresentationTypeVCHDelegate presentationTypeVCHDelegate;
    @Inject
    private AppliedToTypeVCHDelegate appliedToTypeVCHDelegate;
    @Inject
    private InvocationMethodVCHDelegate invocationMethodVCHDelegate;
    @Inject
    private ObjectActionVCHDelegate objectActionVCHDelegate;
    @Inject
    private ActionAreaBindDelegate actionAreaBindDelegate;
    @Inject
    private ActionAreaRefreshDelegate actionAreaRefreshDelegate;
    @Inject
    private ActionBackgroundColorRefreshDelegate actionBackgroundColorRefreshDelegate;

    @Inject
    private UseCustomFormVCHDelegateFactory useCustomFormVCHDelegateFactory;
    @Inject
    private CustomFormRefreshDelegateFactory customFormRefreshDelegateFactory;
    @Inject
    private QuickFormBindDelegateFactory quickFormBindDelegateFactory;
    @Inject
    private QuickFormVCHDelegateFactory quickFormVCHDelegateFactory;
    @Inject
    private EditFormBindDelegate editFormBindDelegate;
    @Inject
    private EditFormRefreshDelegate editFormRefreshDelegate;
    @Inject
    private EditFormVCHDelegate editFormVCHDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> iconValidators = new HashMap<>();

    @Override
    protected void build()
    {
        UseCustomFormVCHDelegate useMassEditFormVCHDelegate = useCustomFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.USE_MASS_EDIT_FORM, ToolFormPropertyCodes.MASS_EDIT_FORM);
        UseCustomFormVCHDelegate useQuickAddFormVCHDelegate = useCustomFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM, ToolFormPropertyCodes.QUICK_ADD_FORM);
        UseCustomFormVCHDelegate useQuickEditFormVCHDelegate = useCustomFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, ToolFormPropertyCodes.QUICK_EDIT_FORM);
        CustomFormRefreshDelegate massEditFormRefreshDelegate = customFormRefreshDelegateFactory.create(
                ToolFormPropertyCodes.USE_MASS_EDIT_FORM);
        CustomFormRefreshDelegate quickAddFormRefreshDelegate = customFormRefreshDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM);
        CustomFormRefreshDelegate quickEditFormRefreshDelegate = customFormRefreshDelegateFactory.create(
                ToolFormPropertyCodes.USE_QUICK_EDIT_FORM);
        QuickFormBindDelegate quickAddFormBindDelegate = quickFormBindDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);
        QuickFormBindDelegate quickEditFormBindDelegate = quickFormBindDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_EDIT_FORM);
        QuickFormVCHDelegete quickAddFormVCHDelegete = quickFormVCHDelegateFactory.create(
                ToolFormPropertyCodes.QUICK_ADD_FORM);

        register(ToolFormPropertyCodes.TITLE, textBoxPropertyFactory)
                .setBindDelegate(titleBindDelegate)
                .setValidators(titleValidators);
        register(ToolFormPropertyCodes.PRESENTATION_TYPE, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(presentationTypeBindDelegate)
                .setRefreshDelegate(presentationTypeRefreshDelegate)
                .setVchDelegate(presentationTypeVCHDelegate);
        register(ToolFormPropertyCodes.APPLIED_TO_TYPE, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(appliedToTypeBindDelegate)
                .setRefreshDelegate(appliedToTypeRefreshDelegate)
                .setVchDelegate(appliedToTypeVCHDelegate);
        register(ToolFormPropertyCodes.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetOnActionToolBindDelegate)
                .setRefreshDelegate(settingsSetOnActionToolRefreshDelegate);
        register(ToolFormPropertyCodes.ICON, dtoSelectPropertyFactory)
                .setBindDelegate(iconBindDelegate)
                .setRefreshDelegate(iconRefreshDelegate)
                .setValidators(iconValidators);
        register(ToolFormPropertyCodes.SYSTEM_ACTION, textBoxPropertyFactory)
                .setRefreshDelegate(systemActionRefreshDelegate);
        register(ToolFormPropertyCodes.ACTION, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(actionRefreshDelegate);

        register(ToolFormPropertyCodes.OBJECT_ACTION, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(objectActionRefreshDelegate)
                .setVchDelegate(objectActionVCHDelegate);
        register(ToolFormPropertyCodes.EDIT_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(editFormBindDelegate)
                .setRefreshDelegate(editFormRefreshDelegate)
                .setVchDelegate(editFormVCHDelegate);
        register(ToolFormPropertyCodes.INVOCATION_METHOD, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(invocationMethodBindDelegate)
                .setRefreshDelegate(invocationMethodRefreshDelegate)
                .setVchDelegate(invocationMethodVCHDelegate);
        register(ToolFormPropertyCodes.ROW_ICON, dtoSelectPropertyFactory)
                .setBindDelegate(iconBindDelegate)
                .setRefreshDelegate(rowIconRefreshDelegate);
        register(ToolFormPropertyCodes.IS_GEO_REQUIRED, checkBoxPropertyFactory)
                .setRefreshDelegate(geoRequiredRefreshDelegate);

        register(ToolFormPropertyCodes.RETURN_TO_LIST_AFTER_CREATION, checkBoxPropertyFactory)
                .setRefreshDelegate(returnToListAfterCreationRefreshDelegate);

        register(ToolFormPropertyCodes.USE_MASS_EDIT_FORM, checkBoxPropertyFactory)
                .setVchDelegate(useMassEditFormVCHDelegate)
                .setRefreshDelegate(useMassEditFormRefreshDelegate);
        register(ToolFormPropertyCodes.MASS_EDIT_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(massEditFormBindDelegate)
                .setRefreshDelegate(massEditFormRefreshDelegate);

        register(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, checkBoxPropertyFactory)
                .setVchDelegate(useQuickAddFormVCHDelegate)
                .setRefreshDelegate(useQuickAddFormRefreshDelegate);
        register(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, checkBoxPropertyFactory)
                .setRefreshDelegate(goToCardAfterCreationRefreshDelegate);
        register(ToolFormPropertyCodes.QUICK_ADD_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(quickAddFormBindDelegate)
                .setRefreshDelegate(quickAddFormRefreshDelegate)
                .setVchDelegate(quickAddFormVCHDelegete);
        register(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, checkBoxPropertyFactory)
                .setVchDelegate(useQuickEditFormVCHDelegate)
                .setRefreshDelegate(useQuickEditFormRefreshDelegate);
        register(ToolFormPropertyCodes.QUICK_EDIT_FORM, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(quickEditFormBindDelegate)
                .setRefreshDelegate(quickEditFormRefreshDelegate);

        register(ToolFormPropertyCodes.ATTRIBUTE_TO_SAVE_FILE, listBoxPropertyFactory)
                .setBindDelegate(attributeToSaveFileBindDelegate)
                .setRefreshDelegate(addFileRefreshDelegate);
        register(ToolFormPropertyCodes.ATTRIBUTE_GROUP_FOR_ADD_FILE, listBoxPropertyFactory)
                .setBindDelegate(attributeGroupForAddFileBindDelegate)
                .setVchDelegate(attributeGroupForAddFileVCHDelegate)
                .setRefreshDelegate(addFileRefreshDelegate);

        register(ToolFormPropertyCodes.ALLOW_IN_MASS_OPERATIONS, checkBoxPropertyFactory)
                .setRefreshDelegate(allowInMassOperationsRefreshDelegate);
        register(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(attributeForFillByCurrentObjectBindDelegate)
                .setRefreshDelegate(attributeForFillByCurrentObjectRefreshDelegate);
        register(ToolFormPropertyCodes.ACTION_AREA, listBoxPropertyFactory)
                .setBindDelegate(actionAreaBindDelegate)
                .setRefreshDelegate(actionAreaRefreshDelegate);
        register(ToolFormPropertyCodes.BACKGROUND_ACTION_COLOR, backgroundColorTextBoxProperty)
                .setRefreshDelegate(actionBackgroundColorRefreshDelegate);
    }

    @Inject
    protected void setUpValidators(NotEmptyValidator notEmptyValidator,
            NotEmptyObjectValidator<SelectItem> notEmptyObjectValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        iconValidators.put(notEmptyObjectValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }
}
