/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.timezone;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProviderWithCatalogItemImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemParentPropertyBindDelegateEditImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormContextGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.CatalogItemFormGinModule;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public class TimeZoneItemFormGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<TimeZoneItemFormTransitionalPropertyRefreshDelegate<ObjectFormAdd>>(){})
            .to(new TypeLiteral<TimeZoneItemFormTransitionalPropertyRefreshDelegateImpl<ObjectFormAdd>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<TimeZoneItemFormTransitionalPropertyRefreshDelegate<ObjectFormCopy>>(){})
            .to(new TypeLiteral<TimeZoneItemFormTransitionalPropertyRefreshDelegateImpl<ObjectFormCopy>>(){})
            .in(Singleton.class);
        bind(new TypeLiteral<TimeZoneItemFormTransitionalPropertyRefreshDelegate<ObjectFormEdit>>(){})
            .to(TimeZoneItemFormTransitionalPropertyRefreshDelegateEditImpl.class)
            .in(Singleton.class);
                
        install(CatalogItemFormContextGinModule.create(TimeZoneItemFormContext.class)
                .setConstants(                              new TypeLiteral<TimeZoneItemFormConstants>(){})
                .setContextMessages(                        new TypeLiteral<TimeZoneItemFormContextMessages>(){}));
        
        install(CatalogItemFormGinModule.create(TimeZoneItemFormContext.class, ObjectFormAdd.class)
                .setPropertControllerFactory(               new TypeLiteral<TimeZoneItemFormPropertyControllerFactoryImpl<ObjectFormAdd>>(){})
                .setAfterBindHandler(                       new TypeLiteral<TimeZoneItemFormPropertyAfterBindHandlerImpl<ObjectFormAdd>>(){})
                .setMapConverter(                           new TypeLiteral<TimeZoneItemFormPropertyMapConverterImpl<ObjectFormAdd>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<TimeZoneItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormAdd>>(){}));
        
        install(CatalogItemFormGinModule.create(TimeZoneItemFormContext.class, ObjectFormEdit.class)
                .setPropertControllerFactory(               new TypeLiteral<TimeZoneItemFormPropertyControllerFactoryImpl<ObjectFormEdit>>(){})
                .setPropertyValuesProvider(                 new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<TimeZoneItemFormContext, ObjectFormEdit>>(){})
                .setAfterBindHandler(                       new TypeLiteral<TimeZoneItemFormPropertyAfterBindHandlerEditImpl>(){})
                .setMapConverter(                           new TypeLiteral<TimeZoneItemFormPropertyMapConverterEditImpl>(){})
                .setParentBindDelegate(                     new TypeLiteral<CatalogItemParentPropertyBindDelegateEditImpl<TimeZoneItemFormContext>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<TimeZoneItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormEdit>>(){}));
        
        install(CatalogItemFormGinModule.create(TimeZoneItemFormContext.class, ObjectFormCopy.class)
                .setPropertControllerFactory(               new TypeLiteral<TimeZoneItemFormPropertyControllerFactoryImpl<ObjectFormCopy>>(){})
                .setPropertyValuesProvider(                 new TypeLiteral<CatalogItemFormPropertyValuesProviderWithCatalogItemImpl<TimeZoneItemFormContext, ObjectFormCopy>>(){})
                .setAfterBindHandler(                       new TypeLiteral<TimeZoneItemFormPropertyAfterBindHandlerImpl<ObjectFormCopy>>(){})
                .setMapConverter(                           new TypeLiteral<TimeZoneItemFormPropertyMapConverterImpl<ObjectFormCopy>>(){})
                .setPropertyParametersDescriptorFactory(    new TypeLiteral<TimeZoneItemFormPropertyParametersDescriptionFactoryImpl<ObjectFormCopy>>(){}));
        //@formatter:on
    }
}