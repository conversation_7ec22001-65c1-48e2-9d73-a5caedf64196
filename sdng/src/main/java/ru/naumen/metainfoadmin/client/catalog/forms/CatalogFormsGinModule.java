/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.forms;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * Модуль формы добавления и редактирования справочника
 * Формы одного вида для всех типов справочников 
 * Использует {@link PropertyContainerPresenter}
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public class CatalogFormsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off   
        install(PropertyControllerGinModule.create(Catalog.class, ObjectFormAdd.class)
                .setPropertyControllerFactory(new TypeLiteral<CatalogFormPropertyControllerFactoryAddImpl>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<CatalogAddFormPropertyParametersDescriptorFactoryImpl>(){}));
        install(PropertyControllerGinModule.create(Catalog.class, ObjectFormEdit.class)
                .setPropertyControllerFactory(new TypeLiteral<CatalogFormPropertyControllerFactoryImpl<ObjectFormEdit>>(){})
                .setPropertyParametersDescriptorFactory(new TypeLiteral<CatalogEditFormPropertyParametersDescriptorFactoryImpl>(){}));
        
        bind(new TypeLiteral<CatalogFormPropertyAfterBindHandler<ObjectFormEdit>>(){})
            .to(CatalogFormPropertyAfterBindHandlerEditImpl.class)
            .in(Singleton.class);
        
       bind(new TypeLiteral<CatalogFormPropertyAfterBindHandler<ObjectFormAdd>>(){})
        .to(CatalogFormPropertyAfterBindHandlerAddImpl.class)
        .in(Singleton.class);
        

        bind(new TypeLiteral<CatalogFormPresenter<ObjectFormAdd>>(){})
            .to(AddCatalogFormPresenter.class);
        bind(new TypeLiteral<CatalogFormPresenter<ObjectFormEdit>>(){})
            .to(EditCatalogFormPresenter.class);
        //@formatter:on
    }
}