/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl;

import java.lang.reflect.Type;
import java.util.Map;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerGinModule;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormContext;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormMessages;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormMessagesAdd;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormMessagesCopy;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormMessagesEdit;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyAfterBindHandler;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyAfterBindHandlerImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyMapConverter;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyMapConverterImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProvider;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyValuesProviderImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemParentPropertyBindDelegate;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemParentPropertyBindDelegateImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormPropertyParametersDescriptorFactoryImpl;

import com.google.common.collect.ImmutableMap;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

/**
 * Модуль, задающий биндинги для конкретной формы типа F extends ObjectForm справочника C extends CatalogItemFormContext
 * <AUTHOR>
 * @since 19.10.2012
 *
 */
public class CatalogItemFormGinModule<C extends CatalogItemFormContext, F extends ObjectForm> extends AbstractGinModule
{
    //@formatter:off
    @SuppressWarnings("rawtypes")
    private static final Map<Class<? extends ObjectForm>, Class<? extends CatalogItemFormMessages>> DEFAULT_MESSAGES = ImmutableMap.of(
            ObjectFormAdd.class, CatalogItemFormMessagesAdd.class, 
            ObjectFormEdit.class, CatalogItemFormMessagesEdit.class, 
            ObjectFormCopy.class, CatalogItemFormMessagesCopy.class);
    //@formatter:on

    public static <C extends CatalogItemFormContext, F extends ObjectForm> CatalogItemFormGinModule<C, F> create(
            Class<C> contextClass, Class<F> formClass)
    {
        return new CatalogItemFormGinModule<C, F>(contextClass, formClass);
    }

    private final Type contextType;
    private final Type formType;
    private final PropertyControllerGinModule<C, F> pcGinModule;

    private TypeLiteral<? extends CatalogItemFormPropertyAfterBindHandler<C, F>> afterBindHandler;
    private TypeLiteral<? extends CatalogItemFormPropertyMapConverter<C, F>> mapConverter;
    private TypeLiteral<? extends CatalogItemFormMessages<C, F>> messages;
    private TypeLiteral<? extends CatalogItemParentPropertyBindDelegate<C, F>> parentBindDelegate;
    private TypeLiteral<? extends PropertyControllerFactory<C, F>> propertyControllerFactory;
    private TypeLiteral<? extends CatalogItemFormPropertyValuesProvider<C, F>> propertyValuesProvider;
    private TypeLiteral<? extends PropertyParametersDescriptorFactory<C, F>> propertyParametersDescriptorFactory;

    private CatalogItemFormGinModule(Class<C> contextClass, Class<F> formClass)
    {
        contextType = TypeLiteral.get(contextClass).getType();
        formType = TypeLiteral.get(formClass).getType();
        pcGinModule = PropertyControllerGinModule.create(contextClass, formClass);

        //@formatter:off
        afterBindHandler = Gin.typeLiteral(CatalogItemFormPropertyAfterBindHandlerImpl.class, contextType, formType);
        mapConverter = Gin.typeLiteral(CatalogItemFormPropertyMapConverterImpl.class, contextType, formType);
        messages = Gin.typeLiteral(DEFAULT_MESSAGES.get(formClass), contextType);
        parentBindDelegate = Gin.typeLiteral(CatalogItemParentPropertyBindDelegateImpl.class, contextType, formType);
        propertyControllerFactory = Gin.typeLiteral(CatalogItemFormPropertyControllerFactoryImpl.class, contextType, formType);
        propertyValuesProvider = Gin.typeLiteral(CatalogItemFormPropertyValuesProviderImpl.class, contextType, formType);
        propertyParametersDescriptorFactory = Gin.typeLiteral(DefaultItemFormPropertyParametersDescriptorFactoryImpl.class, contextType, formType);
        //@formatter:on
    }

    public CatalogItemFormGinModule<C, F> setAfterBindHandler(
            TypeLiteral<? extends CatalogItemFormPropertyAfterBindHandler<C, F>> afterBindHandler)
    {
        this.afterBindHandler = afterBindHandler;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setMapConverter(
            TypeLiteral<? extends CatalogItemFormPropertyMapConverter<C, F>> mapConverter)
    {
        this.mapConverter = mapConverter;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setMessages(TypeLiteral<? extends CatalogItemFormMessages<C, F>> messages)
    {
        this.messages = messages;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setParentBindDelegate(
            TypeLiteral<? extends CatalogItemParentPropertyBindDelegateImpl<C, F>> parentBindDelegate)
    {
        this.parentBindDelegate = parentBindDelegate;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setPropertControllerFactory(
            TypeLiteral<? extends PropertyControllerFactory<C, F>> propertyControllerFactory)
    {
        this.propertyControllerFactory = propertyControllerFactory;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setPropertyParametersDescriptorFactory(
            TypeLiteral<? extends PropertyParametersDescriptorFactory<C, F>> propertyParametersDescriptorFactory)
    {
        this.propertyParametersDescriptorFactory = propertyParametersDescriptorFactory;
        return this;
    }

    public CatalogItemFormGinModule<C, F> setPropertyValuesProvider(
            TypeLiteral<? extends CatalogItemFormPropertyValuesProvider<C, F>> propertyValuesProvider)
    {
        this.propertyValuesProvider = propertyValuesProvider;
        return this;
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(Gin.bindSingleton(
                Gin.<CatalogItemFormPropertyAfterBindHandler<C, F>> typeLiteral(CatalogItemFormPropertyAfterBindHandler.class, contextType, formType), 
                afterBindHandler));
        
        install(Gin.bindSingleton(
                Gin.<CatalogItemFormPropertyMapConverter<C, F>> typeLiteral(CatalogItemFormPropertyMapConverter.class, contextType, formType), 
                mapConverter));
        
        install(Gin.bindSingleton(
                Gin.<CatalogItemFormMessages<C, F>> typeLiteral(CatalogItemFormMessages.class, contextType, formType), 
                messages));
        
        install(Gin.bindSingleton(
                Gin.<CatalogItemParentPropertyBindDelegate<C, F>> typeLiteral(CatalogItemParentPropertyBindDelegate.class, contextType, formType), 
                parentBindDelegate));
        
        install(Gin.bindSingleton(
                Gin.<CatalogItemFormPropertyValuesProvider<C, F>> typeLiteral(CatalogItemFormPropertyValuesProvider.class, contextType, formType), 
                propertyValuesProvider));
        
        install(pcGinModule
                .setPropertyControllerFactory(propertyControllerFactory)
                .setPropertyParametersDescriptorFactory(propertyParametersDescriptorFactory));
        //@formatter:on
    }
}