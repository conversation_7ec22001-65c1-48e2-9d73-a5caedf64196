package ru.naumen.reports.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.fileupload2.core.FileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.upload.UploadServiceException;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.file.XmlFile;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.reports.server.spi.ReportTemplateFileService;
import ru.naumen.reports.shared.Constants;
import ru.naumen.reports.shared.MissingObjectsContainer;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.reports.shared.dispatch.GetReportParametersAction;
import ru.naumen.reports.shared.dispatch.GetReportTemplatesAction;
import ru.naumen.reports.shared.dispatch.SaveReportTemplateAction;
import ru.naumen.sec.server.AntivirusValidationService;
import ru.naumen.sec.server.admin.log.ReportTemplateLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;
import ru.naumen.sec.server.admin.log.impl.AdminLogRecordDetailsService;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Обработчик {@link SaveReportTemplateAction}.
 * <AUTHOR>
 */
@Component
public class SaveReportTemplateActionHandler
        extends UpdateReportTemplateActionHandlerBase<SaveReportTemplateAction, SimpleScriptedResult<ReportTemplate>>
{
    public static final Logger LOG = LoggerFactory.getLogger(SaveReportTemplateActionHandler.class);

    private final CommonUtils commonUtils;
    private final Dispatch dispatch;
    private final MessageFacade messages;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final ReportTemplateFileService reportTemplateService;
    private final MetainfoModification metainfoModification;
    private final ScriptStorageService scriptStorageService;
    private final ReportTemplateLogService reportTemplateLogService;
    private final ScriptLogService scriptLogService;
    private final UploadService uploadService;
    private final AntivirusValidationService antivirusValidationService;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;

    public SaveReportTemplateActionHandler(CommonUtils commonUtils,
            Dispatch dispatch,
            MessageFacade messages,
            ScriptModifyRegistry scriptModifyRegistry,
            ReportTemplateFileService reportTemplateService,
            MetainfoModification metainfoModification,
            ScriptStorageService scriptStorageService,
            ReportTemplateLogService reportTemplateLogService,
            ScriptLogService scriptLogService,
            UploadService uploadService,
            AntivirusValidationService antivirusValidationService,
            ApplicationEventPublisher eventPublisher,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.messages = messages;
        this.commonUtils = commonUtils;
        this.dispatch = dispatch;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.reportTemplateService = reportTemplateService;
        this.metainfoModification = metainfoModification;
        this.scriptStorageService = scriptStorageService;
        this.reportTemplateLogService = reportTemplateLogService;
        this.scriptLogService = scriptLogService;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.uploadService = uploadService;
        this.antivirusValidationService = antivirusValidationService;
    }

    @Override
    public SimpleScriptedResult<ReportTemplate> executeInTransaction(SaveReportTemplateAction action,
            ExecutionContext context) throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.REPORT_TEMPLATES);
        ReportTemplate template = action.getReportTemplate();
        boolean isNew = action.isNew();
        performAntivirusValidation(template);
        ReportTemplate old = reportsStorageService.getTemplate(template.getCode());
        if (old != null)
        {
            old = old.cloneWithoutParameters();
        }

        if (isNew)
        {
            adminPermissionCheckService.checkPermission(template, TEMPLATES, CREATE);
        }
        else
        {
            adminPermissionCheckService.checkPermission(old, TEMPLATES, EDIT);
        }

        MapProperties oldProperties = null;
        if (old != null)
        {
            oldProperties = reportTemplateLogService.getReportTemplateInfo(old);
        }

        String fileUuid = (old != null) ? old.getFileUuid() : null;
        if (isNew && old != null)
        {

            throw new FxException(
                    messages.getMessage("SaveReportTemplateActionHandler.templateWithCodeAlreadyExist", old.getCode()),
                    true);
        }

        DtObject clientTemplateFile = template.getClientTemplateFile();
        XmlFile xmlTemplateFile = template.getXmlTemplateFile();
        template.setClientTemplateFile(null);
        template.setXmlTemplateFile(null);

        if (clientTemplateFile != null && xmlTemplateFile != null)
        {
            throw new FxException("Only one template file source should be specified");
        }

        if ((null == clientTemplateFile || null == clientTemplateFile.getUUID()) && null == xmlTemplateFile)
        {
            throw new FxException(messages.getMessage("SaveReportTemplateActionHandler.templateFileNotSpecified",
                    template.getCode()));
        }

        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();
        if (!action.isUploadMetainfo())
        {
            scriptsLog.addAll(processSaveScripts(action, old, template));
        }

        processTemplateParameters(template, action.isUploadMetainfo());
        boolean isTemplateSave = false;
        isTemplateSave |= reportsStorageService.saveTemplate(template);

        if (xmlTemplateFile != null)
        {
            if (action.isSaveFile())
            {
                reportTemplateService.saveXmlTemplateFile(template, xmlTemplateFile);
            }
            else
            {
                template.setFileUuid(null);
            }
            isTemplateSave |= reportsStorageService.saveTemplate(template);
        }
        else if (!clientTemplateFile.getUUID().startsWith("file$"))
        {
            if (action.isSaveFile())
            {
                reportTemplateService.saveClientTemplateFile(template, clientTemplateFile);
            }
            else
            {
                template.setFileUuid(null);
            }
            isTemplateSave |= reportsStorageService.saveTemplate(template);
        }
        if (old != null && fileUuid != null
            && (xmlTemplateFile != null || !clientTemplateFile.getUUID().equals(fileUuid)) && action.isSaveFile())
        {
            try
            {
                IUUIDIdentifiable oldTemplateFile = commonUtils.getByUUID(fileUuid);
                commonUtils.delete(oldTemplateFile);
            }
            catch (ObjectNotFoundException e)
            {
                LOG.info("Old report template file {} does not exist, so it cannot be deleted.", fileUuid);
            }
        }
        if (isTemplateSave)
        {
            doAdminLog(template, oldProperties, old == null, action.isUploadMetainfo(), action.isUploadReports());
            eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(template,
                    old == null ? null : old.getSettingsSet(), template.getSettingsSet())); //NOSONAR
        }
        scriptLogService.makeLogs(scriptsLog);
        GetReportTemplatesAction getAction = new GetReportTemplatesAction(List.of(template.getCode()));
        getAction.setWithScripts(action.isWithScripts());
        SimpleScriptedResult<Collection<ReportTemplate>> res = dispatch.execute(getAction);
        return new SimpleScriptedResult<>(res.get().iterator().next());
    }

    private void doAdminLog(ReportTemplate newRT, MapProperties oldProperties, boolean isAdd,
            boolean isUploadMetainfo, boolean isUploadReports)
    {
        String details = AdminLogRecordDetailsService.getKeyTypeDetails(newRT.getCode(),
                Constants.REPORT_TEMPLATES_STORAGE_TYPE);
        if (isAdd)
        {
            reportTemplateLogService.reportTemplateAdd(newRT, details, isUploadMetainfo, isUploadReports);
        }
        else
        {
            reportTemplateLogService.reportTemplateEdit(newRT, oldProperties, details, isUploadMetainfo,
                    isUploadReports);
        }
    }

    /**
     * Метод выполняет проверку на наличие вредоносного контента в файле шаблона отчета.
     *
     * @param template шаблон отчета
     *
     * @throws FxException если проверяемый файл содержит вредоносный контент.
     */
    private void performAntivirusValidation(ReportTemplate template) throws FxException
    {
        XmlFile xmlFile = template.getXmlTemplateFile();
        DtObject clientTemplateFile = template.getClientTemplateFile();
        byte[] content = null;
        String fileName = StringUtilities.EMPTY;
        String contentType = StringUtilities.EMPTY;
        if (xmlFile != null)
        {
            content = xmlFile.getContent();
            fileName = xmlFile.getFileName();
            contentType = xmlFile.getMimeType();
        }
        else if (null != clientTemplateFile && null != clientTemplateFile.getUUID()
                 && !clientTemplateFile.getUUID().startsWith("file$"))
        {
            try
            {
                FileItem<?> file = uploadService.get(clientTemplateFile.getUUID());
                if (file != null)
                {
                    content = file.get();
                    fileName = file.getName();
                    contentType = file.getContentType();
                }
            }
            catch (UploadServiceException | IOException e)
            {
                LOG.trace(e.getMessage());
            }
        }
        if (content != null)
        {
            antivirusValidationService.verifyFile(fileName, contentType, content);
        }
    }

    private List<ScriptAdminLogInfo> processSaveScripts(SaveReportTemplateAction action, ReportTemplate oldTemplate,
            ReportTemplate newTemplate)
    {
        if (!action.isWithScripts() || action.isUploadMetainfo())
        {
            return new ArrayList<>();
        }
        ScriptModifyProcess<ReportTemplate> process = scriptModifyRegistry.getProcess(newTemplate);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.REPORT_TEMPLATE,
                ScriptHolders.REPORT_TEMPLATE);
        process.save(oldTemplate, newTemplate, action.getScript(), context);
        return context.getScriptsLogInfo();
    }

    // при импорте метаинформации шаблон отчета сохраняем без параметров, т.к в скрипте может происходить обращение к
    // классам и объектам, которых в системе еще нет
    // параметры шаблона отчета будут заполнены позже из скрипта
    private void processTemplateParameters(ReportTemplate template, boolean isImportMetainfo) throws DispatchException
    {
        if (isImportMetainfo)
        {
            return;
        }
        Script script = scriptStorageService.getScript(template.getScript());

        List<Parameter> parameters = dispatch.execute(new GetReportParametersAction(script, true))
                .getParametersOrdered();

        StringBuilder sb = new StringBuilder();
        parameters.stream()
                .filter(SaveReportTemplateActionHandler::hasMissingObjects)
                .forEach(param -> sb.append(messages.getMessage("reportParameters.nonExistentObjects",
                        param.getTitle(), param.getCode(), ((MissingObjectsContainer)param).nonExistentObjects())));
        parameters.stream()
                .filter(SaveReportTemplateActionHandler::hasMissingStates)
                .forEach(param -> sb.append(messages.getMessage("reportParameters.nonExistentStates",
                        param.getTitle(), param.getCode(), param.getMetaClassFqn(),
                        ((MissingObjectsContainer)param).nonExistentStates())));
        if (!sb.isEmpty())
        {
            throw new FxException(sb.toString());
        }

        template.setParameters(parameters);
    }

    private static boolean hasMissingStates(Parameter parameter)
    {
        return parameter instanceof MissingObjectsContainer missingObjectsContainer
               && !missingObjectsContainer.nonExistentStates().isEmpty();
    }

    private static boolean hasMissingObjects(Parameter parameter)
    {
        return parameter instanceof MissingObjectsContainer missingObjectsContainer
               && !missingObjectsContainer.nonExistentObjects().isEmpty();
    }
}
