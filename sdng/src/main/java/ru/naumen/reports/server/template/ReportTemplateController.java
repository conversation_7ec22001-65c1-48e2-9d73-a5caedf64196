package ru.naumen.reports.server.template;

import static ru.naumen.core.shared.Constants.SettingsSet.ADMIN_PERMISSIONS;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;
import static ru.naumen.core.shared.permission.PermissionType.VIEW;

import org.springframework.stereotype.Component;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.common.FormattersSrv;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.sets.SettingsSetInterfaceUtils;
import ru.naumen.core.server.sets.UserSettingsSetService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.guic.server.controller.CardControllerUtils;
import ru.naumen.guic.server.controller.ICardController;
import ru.naumen.guic.server.service.UIActionParametersHolder;
import ru.naumen.guic.shared.components.Button;
import ru.naumen.guic.shared.components.ComponentBase;
import ru.naumen.guic.shared.components.Publisher;
import ru.naumen.guic.shared.environment.IUIEnvironment;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.server.spi.ReportsDtObjectUtils;
import ru.naumen.reports.shared.Constants.ReportTemplateCard;
import ru.naumen.reports.shared.ReportTemplate;

/**
 * Контроллер для карточки шаблона отчета
 * <AUTHOR>
 * @since July, 8, 2015
 */
@Component("ru.naumen.reports.server.template.ReportTemplateController")
public class ReportTemplateController implements ICardController
{
    private final MessageFacade messages;
    private final I18nUtil i18nUtil;
    private final ReportsStorageService reportStorage;
    private final UIActionParametersHolder params;
    private final FormattersSrv formSrv;
    private final ReportsDtObjectUtils utils;
    private final ScriptStorageService scriptStorageService;
    private final ScriptDtoFactory scriptDtoFactory;
    private final UserSettingsSetService userSettingsSetService;
    private final SettingsSetInterfaceUtils settingsSetUtils;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ReportTemplateController(MessageFacade messages, I18nUtil i18nUtil, ReportsStorageService reportStorage,
            UIActionParametersHolder params, FormattersSrv formSrv, ReportsDtObjectUtils utils,
            ScriptStorageService scriptStorageService, ScriptDtoFactory scriptDtoFactory,
            UserSettingsSetService userSettingsSetService,
            SettingsSetInterfaceUtils settingsSetUtils,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.messages = messages;
        this.i18nUtil = i18nUtil;
        this.reportStorage = reportStorage;
        this.params = params;
        this.formSrv = formSrv;
        this.utils = utils;
        this.scriptStorageService = scriptStorageService;
        this.scriptDtoFactory = scriptDtoFactory;
        this.userSettingsSetService = userSettingsSetService;
        this.settingsSetUtils = settingsSetUtils;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public void onLoad(Publisher publisher, IUIEnvironment env)
    {
        adminPermissionCheckService.checkPermission(TEMPLATES, VIEW);
        ReportTemplate reportTemplate = initReportService();
        initCard(publisher, env, reportTemplate);
    }

    private void initCard(Publisher publisher, IUIEnvironment env, ReportTemplate template)
    {
        String title = i18nUtil.getLocalizedTitle(template);
        publisher.setTitle(title);

        ComponentBase propertyList = publisher.getChild("info").getChild("info");
        Button button = propertyList.getChild("delete");

        String localizedTemplate = messages.getMessage("ReportTemplateMessages.template");
        String dialogMessages = messages.getMessage("deleteMessage", localizedTemplate, title);
        button.setDialogMessage(dialogMessages);

        env.setProperty(ReportTemplateCard.TITLE, title);
        env.setProperty(ReportTemplateCard.DESCRIPTION, i18nUtil.getLocalizedDescription(template));
        env.setProperty(ReportTemplateCard.CODE, template.getCode());

        Script script = scriptStorageService.getScript(template.getScript());
        ScriptDto scriptDto = scriptDtoFactory.create(script);

        env.setProperty(ReportTemplateCard.SCRIPT, scriptDto);
        env.setProperty(publisher.getPrevTokenProperty(), "report-templates:");
        env.setProperty(publisher.getPrevTitleProperty(), messages.getMessage(ReportTemplateCard.BACK_TITLE));
        env.setProperty(ADMIN_PERMISSIONS, adminPermissionCheckService.getPermissions(template));

        if (template.getSettingsSet() != null)
        {
            env.setProperty(ReportTemplateCard.SETTINGS_SET,
                    settingsSetUtils.getSettingsSetPlaceLink(template.getSettingsSet()));
        }

        if (!userSettingsSetService.isSettingsSetsDisplayedOnCards())
        {
            CardControllerUtils.disableField(publisher, ReportTemplateCard.SETTINGS_SET);
        }

        DtObject tFile = template.getClientTemplateFile();
        SafeHtml html = SafeHtmlUtils.fromString(StringUtilities.EMPTY);
        String templateFileUuid = template.getFileUuid();

        if (tFile != null && StringUtilities.isNotEmpty(templateFileUuid))
        {
            String fileName = tFile.getTitle();
            String htmlURL = "<a href='" + formSrv.downloadUrl(templateFileUuid) + "' class='link'>" + fileName
                             + "</a>";
            html = SafeHtmlUtils.fromTrustedString(htmlURL);
        }
        env.setProperty(ReportTemplateCard.FILE, html.asString());
    }

    private ReportTemplate initReportService()
    {
        String code = params.getPlaceToken(0);
        ReportTemplate template = reportStorage.getTemplate(code);
        if (template == null)
        {
            throw new FxException(messages.getMessage("ReportTemplateMessages.notFound"), true);
        }
        ReportTemplate clone = template.cloneWithoutParameters();
        utils.beforeSendClient(clone);
        return clone;
    }
}
