package ru.naumen.reports.shared;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Настройки хранятся только для ReportContent, так как они изменяются в интерфейсе технолога
 * Для ReportInstance настройки строятся на клиенте на основании параметров отчета, которые хранятся в самом отчете 
 *
 * <AUTHOR>
 * @since 14.11.2012
 */
@XmlRootElement
@XmlType(name = "report-settings", propOrder = { "reportUuid", "subjectUuid", "userUuid", "parameters" })
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ReportSettings implements HasParameters, IsSerializable, Serializable
{
    private List<Parameter> parameters = new ArrayList<>();

    private String reportUuid;

    private String subjectUuid;

    private String userUuid;

    @Override
    @XmlElementWrapper(name = "parameters")
    @XmlElement(name = "parameter", type = ReportParameter.class)
    public List<Parameter> getParameters()
    {
        return parameters;
    }

    @XmlElement(name = "reportUuid")
    public String getReportUuid()
    {
        return reportUuid;
    }

    @XmlElement(name = "subjectUuid")
    public String getSubjectUuid()
    {
        return subjectUuid;
    }

    @XmlElement(name = "userUuid")
    public String getUserUuid()
    {
        return userUuid;
    }

    @Override
    public void setParameters(List<Parameter> parameters)
    {
        this.parameters = parameters;
    }

    public ReportSettings setReportUuid(String reportUuid)
    {
        this.reportUuid = reportUuid;
        return this;
    }

    public ReportSettings setSubjectUuid(String subjectUuid)
    {
        this.subjectUuid = subjectUuid;
        return this;
    }

    public ReportSettings setUserUuid(String userUuid)
    {
        this.userUuid = userUuid;
        return this;
    }

}
