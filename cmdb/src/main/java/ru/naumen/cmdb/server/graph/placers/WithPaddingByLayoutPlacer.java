package ru.naumen.cmdb.server.graph.placers;

import java.awt.geom.Point2D;
import java.util.List;
import java.util.Map;

import ru.naumen.cmdb.server.graph.GraphBOWrapper;
import ru.naumen.cmdb.server.graph.GraphRelation;
import ru.naumen.cmdb.server.graph.utils.PaddingsWrapper.Paddings;
import ru.naumen.graph.algorithms.layouts.IGraphLayout;
import ru.naumen.graph.components.modifyers.Move;
import ru.naumen.graph.components.primitives.Primitive;

/**
 *
 * <AUTHOR>
 * @since 17.02.2012
 */
public abstract class WithPaddingByLayoutPlacer extends WithPaddingPlacer<Object, Object>
{
    protected abstract IGraphLayout<GraphBOWrapper, GraphRelation> getLayout();

    @Override
    protected void place(List<Primitive> prims)
    {
        IGraphLayout<GraphBOWrapper, GraphRelation> layout = getLayout();

        layout.setPaddings(getPadding(Paddings.T), getPadding(Paddings.B), getPadding(Paddings.L),
                getPadding(Paddings.R));
        Map<GraphBOWrapper, Point2D> coords = layout.proceed();

        double maxX = 0.0;
        double maxY = 0.0;
        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        Move shift = new Move(true, false, 0.0, 0.0);
        for (Primitive prim : prims)
        {
            if (coords.containsKey(prim.getObject()))
            {
                Point2D point2d = coords.get(prim.getObject());
                prim.modify(new Move(true, false, point2d.getX(), point2d.getY()));
                prim.modify(shift);
                maxX = Math.max(point2d.getX(), maxX);
                minX = Math.min(point2d.getX(), minX);
                maxY = Math.max(point2d.getY(), maxY);
                minY = Math.min(point2d.getY(), minY);
            }
        }

        double dx = (minX > 0.0 ? -Math.abs(minX) : Math.abs(minX)) + getPadding(Paddings.L);
        double dy = (minY > 0.0 ? -Math.abs(minY) : Math.abs(minY)) + getPadding(Paddings.T);
        shift.setDx(dx);
        shift.setDy(dy);
        graph.setWidth(maxX + dx + getPadding(Paddings.R));
        graph.setHeight(maxY + dy + getPadding(Paddings.B));
    }
}
