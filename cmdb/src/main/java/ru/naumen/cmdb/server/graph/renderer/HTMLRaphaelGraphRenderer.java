package ru.naumen.cmdb.server.graph.renderer;

import java.io.IOException;
import java.util.Comparator;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang.StringEscapeUtils;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.util.JsonUtils;
import ru.naumen.graph.components.BOGraph;
import ru.naumen.graph.components.modifyers.ModifyerBase;
import ru.naumen.graph.components.modifyers.Move;
import ru.naumen.graph.components.modifyers.Rotate;
import ru.naumen.graph.components.modifyers.Scale;
import ru.naumen.graph.components.primitives.Circle;
import ru.naumen.graph.components.primitives.Group;
import ru.naumen.graph.components.primitives.Image;
import ru.naumen.graph.components.primitives.Path;
import ru.naumen.graph.components.primitives.Primitive;
import ru.naumen.graph.components.primitives.Rectangle;
import ru.naumen.graph.components.primitives.Text;

/**
 * Реализация рендерера для генерирования html + raphael js
 *
 * @author: dkiselev
 * @since: 23.11.2009
 */
public class HTMLRaphaelGraphRenderer extends GraphRenderBase<RaphaelObjectDescription>
{
    int eventsCount = 0;
    private String raphaelId;

    @Override
    protected List<RaphaelObjectDescription> drawPrimitivesRecursevly(Primitive p, Deque<Primitive> parents,
            String graphDivId)
    {
        raphaelId = "r" + trimId(graphDivId);
        return super.drawPrimitivesRecursevly(p, parents, graphDivId);
    }

    @Override
    protected String fillGraphContainer(BOGraph graph, String id, List<RaphaelObjectDescription> rds) throws IOException
    {
        double width = graph.getWidth();
        double height = graph.getHeight();

        StringBuilder sb = new StringBuilder();

        if (isUseAjaxObserver())
        {
            sb.append("if (window.MessageFactory && window.AjaxObserver)\n");
            sb.append("\tMessageFactory.dispatchMSG({key: AjaxObserver.MESSAGES.beforeRequest, rootMessages:null});\n");
        }

        sb.append("var ")
                .append(raphaelId)
                .append(" = Raphael('")
                .append(id)
                .append("', ")
                .append(width)
                .append(", ")
                .append(height)
                .append(");\n"); //NOPMD

        rds.sort(Comparator.comparingInt(RaphaelObjectDescription::getZIndex));

        String w = "schemeWrapper" + trimId(id);
        //@formatter:off
        sb.append(String.format("function drawScheme%s()\n", raphaelId))
            .append("{\n")
            .append(String.format("var zoomGroup = %s.set();\n", raphaelId))
            .append(String.format("var zoomTextGroup = %s.set();\n", raphaelId))
            .append(String.format("var zoomImageGroup = %s.set();\n", raphaelId))
            .append("var allObjects = [];\n")
            .append(String.format("window.%s = {", w))
            .append(String.format("    'raphael':%s,", raphaelId))
            .append(String.format("    'div':document.getElementById('%s'),", id))
            .append("    'zg':zoomGroup,") //NOPMD
            .append("    'tg':zoomTextGroup,") //NOPMD
            .append("    'ig':zoomImageGroup,") //NOPMD
            .append("    'allObjects': allObjects,") //NOPMD
            .append("    'acc2obj': {},")
            .append(String.format("    'id':'%s',", id))
            .append("    'done':false\n") //NOPMD
            .append("};\n") //NOPMD
            .append("var drawGraph = new Array();\n");
        //@formatter:on

        int i;
        int drawThread = 0;
        for (i = 0; i < rds.size(); i++)
        {
            RaphaelObjectDescription rd = rds.get(i);

            if (i % 100 == 0)
            {
                drawThread = i / 100;
                sb.append(String.format("drawGraph[%d] = function(){\n", drawThread));
            }

            sb.append(String.format("%s%s.acc2obj['%s'] = %s;\n", rd.getCreator(), w, rd.getAccessor(),
                    rd.getAccessor()));

            if (Text.class.isAssignableFrom(rd.getPrimitiveClass()))
            {
                sb.append(String.format("zoomTextGroup.push(%s);\n", rd.getAccessor()));
            }
            else if (Image.class.isAssignableFrom(rd.getPrimitiveClass()))
            {
                sb.append(String.format("zoomImageGroup.push(%s);\n", rd.getAccessor()));
            }
            else
            {
                sb.append(String.format("zoomGroup.push(%s);\n", rd.getAccessor()));
            }

            sb.append(String.format("allObjects.push(%s);\n", rd.getAccessor()));

            if ((i + 1) % 100 == 0 || i == rds.size() - 1)
            {
                sb.append(String.format("\nsetTimeout(drawGraph[%d], 50);\n};\n\n", drawThread + 1));
            }
        }

        if (rds.isEmpty())
        {
            drawThread = -1; //
        }

        //@formatter:off
        sb.append(String.format("drawGraph[%d] = function(){\n", drawThread + 1))
            .append("for(var i=0; i<allObjects.length; i++)\n")
            .append(String.format("allObjects[i].scheme = %s;\n", w))
            .append("graphDone();\n")
            .append("};\n");
        //@formatter:on

        if (isUseAjaxObserver())
        {
            //@formatter:off
            sb.append("if (window.MessageFactory && window.AjaxObserver)\n")
                .append("   MessageFactory.dispatchMSG({key: AjaxObserver.MESSAGES.afterRequest, rootMessages:null});\n");
            //formatter:on
        }

        sb.append("setTimeout(drawGraph[0], 50);\n")
            .append(String.format("function graphDone() {\n%s.done=true;\n", w));
        int zoom = 100;
        if (zoom != 100)
        {
            double z = (double)zoom / 100;
            sb.append(String.format("    setNearestZoom(%s, %f);\n", w, z));
        }
        sb.append(String.format("}\n }\n drawScheme%s()\n", raphaelId));

        return sb.toString();
    }

    @Override
    protected RaphaelObjectDescription getNewRenderMetaObject(String id, int zIndex)
    {
        //id = "rd";
        return new RaphaelObjectDescription(id, zIndex);
    }

    @Override
    protected void handleCircle(Circle circle, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        rd.appendCreator("var " + rd.getAccessor() + " = r.circle(" + circle.getCx() + ", " + circle.getCy() + ", "
                + circle.getR() + ");\n");
    }

    @Override
    protected void handleEvents(RaphaelObjectDescription rd, Entry<String, String> event, String graphDivId)
    {
        eventsCount++;
        if (eventsCount > 200 && ("onmouseout".equals(event.getKey()) || "onmouseover".equals(event.getKey())))
        {
            return;
        }
        rd.appendCreator(rd.getAccessor() + ".node." + event.getKey() + " = function(event){"
                + event.getValue().replace(Primitive.RAPHAEL_DIV_EVNT_ALIAS, graphDivId)
                        .replace(Primitive.THIS_ELEMENT_EVNT_ALIAS, rd.getAccessor())
                + "};\n");
    }

    @Override
    protected void handleGroup(Group g, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        rd.appendCreator("var " + rd.getAccessor() + " = r.set();\n");

        //Добавляем в группу детей дочерних групп но не сами групы
        //чтобы при применении модификаций они не производились дважды
        //(1 раз для группы 1 раз для объекта)
        for (RaphaelObjectDescription crd : rds)
        {
            if (!Group.class.isAssignableFrom(crd.getPrimitiveClass()))
            {
                rd.appendCreator(rd.getAccessor() + ".push(" + crd.getAccessor() + ");\n");
            }
        }

        if (g.getX() != 0.0 && g.getY() != 0.0)
        {
            rd.appendCreator(rd.getAccessor() + ".translate(" + g.getX() + ", " + g.getY() + ");\n");
        }
    }

    @Override
    protected void handleImage(Image img, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        rd.appendCreator("var " + rd.getAccessor() + " = " + raphaelId + ".image('" + img.getUrl() + "', " + img.getX()
                + ", " + img.getY() + ", " + img.getWidth() + ", " + img.getHeight() + ");\n");
        rd.appendCreator(rd.getAccessor() + ".node.id = '" + img.getDebugId() + "';\n");
    }

    @Override
    protected void handleModify(List<ModifyerBase> modifyers, RaphaelObjectDescription rd,
            List<RaphaelObjectDescription> rds, Primitive p)
    {
        //Добавить Rotate в конце, так как на них могут повлиять предшествующие трансформации
        for (ModifyerBase m : modifyers)
        {
            String modObjId = rd.getAccessor();
            if (m instanceof Rotate rotate)
            {
                String cx = (rotate.getCx() == null || rotate.getCy() == null) ? "" : ", " + rotate.getCx();
                String cy = (rotate.getCx() == null || rotate.getCy() == null) ? "" : ", " + rotate.getCy();
                if ((rotate.getCx() != null && rotate.getCy() != null) || !m.isAsObject())
                {
                    rd.appendCreator("\n" + modObjId + ".rotate(" + rotate.getDegree() + cx + cy + ");");
                }
                else
                {
                    //баг рафаэля
                    rd.appendCreator("\n" + "var " + modObjId + "BBox = " + modObjId + ".getBBox();\n");
                    rd.appendCreator(modObjId + ".rotate(" + rotate.getDegree() + ", " + modObjId + "BBox.x + "
                            + modObjId + "BBox.width/2, " + modObjId + "B1Box.y + " + modObjId + "BBox.height/2);\n");
                }
            }
        }

        for (ModifyerBase m : modifyers)
        {
            String modObjId = rd.getAccessor();
            if (m.isAsObject() && !Group.class.isAssignableFrom(rd.getPrimitiveClass()))
            {
                modObjId = generateId();
                rd.appendCreator("var " + modObjId + " = r.set();\n");

                for (RaphaelObjectDescription crd : rds)
                {
                    if (!Group.class.isAssignableFrom(crd.getPrimitiveClass()))
                    {
                        rd.appendCreator(modObjId + ".push(" + crd.getAccessor() + ");\n");
                    }
                }

                rd.appendCreator(modObjId + ".push(" + rd.getAccessor() + ");\n");
            }

            if (m instanceof Move move)
            {
                rd.appendCreator("\n" + modObjId + ".translate(" + move.getDx() + ", " + move.getDy() + ");");
            }

            if (m instanceof Scale scale)
            {
                String cx = (scale.getCx() == null || scale.getCy() == null) ? "" : ", " + scale.getCx();
                String cy = (scale.getCx() == null || scale.getCy() == null) ? "" : ", " + scale.getCy();

                if ((scale.getCx() != null && scale.getCy() != null) || !m.isAsObject())
                {
                    rd.appendCreator(
                            "\n" + modObjId + ".scale(" + scale.getNx() + ", " + scale.getNy() + cx + cy + ");");
                }
                else
                {
                    //баг рафаэля
                    rd.appendCreator("\n" + "var " + modObjId + "BBox = " + modObjId + ".getBBox();\n");
                    rd.appendCreator(modObjId + ".scale(" + scale.getNx() + ", " + scale.getNy() + ", " + modObjId
                            + "BBox.x + " + modObjId + "BBox.width/2, " + modObjId + "BBox.y + " + modObjId
                            + "BBox.height/2);");
                }
            }
        }
    }

    @Override
    protected void handlePath(Path path, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        rd.appendCreator("var " + rd.getAccessor() + " = " + raphaelId + ".path('" + path.getPath() + "');\n");
        rd.appendCreator(rd.getAccessor() + ".node.id='" + path.getDebugId() + "';\n");
    }

    @Override
    protected void handleRectangle(Rectangle rect, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        String cr = rect.getCornerRadius() == null ? "" : ", " + rect.getCornerRadius();

        rd.appendCreator("var " + rd.getAccessor() + " = r.rect(" + rect.getX() + ", " + rect.getY() + ", "
                + rect.getWidth() + ", " + rect.getHeight() + cr + ");\n");
    }

    @Override
    protected void handleText(Text t, RaphaelObjectDescription rd, List<RaphaelObjectDescription> rds)
    {
        String stripToEmptyText = t.getText() == null ? StringUtilities.EMPTY : t.getText().trim();
        String jsText = StringEscapeUtils.escapeJavaScript(stripToEmptyText);
        jsText = jsText.replace("\\" + Text.BRAKE_CHARACTER, Text.BRAKE_CHARACTER);

        String title = StringUtilities.EMPTY;
        if (t.getData() != null && t.getData().get("title") instanceof String)
        {
            title = StringEscapeUtils.escapeJavaScript((String)t.getData().get("title"));
        }

        rd.appendCreator("var " + rd.getAccessor() + " = " + raphaelId + ".text(" + t.getCx() + ", " + t.getCy() + ", '"
                + jsText + "');\n");
        rd.appendCreator(rd.getAccessor() + ".node.id = '" + t.getDebugId() + "';\n");
        rd.appendCreator("addQTip(" + rd.getAccessor() + ".node, '" + title + "');\n");
        if (t.getAttributes().containsKey("anchor-y"))
        {
            rd.appendCreator(rd.getAccessor() + ".anchorY = " + t.getAttributes().get("anchor-y") + ";\n");
        }

        //Поворот текста осуществить относительно точки размещения текста (центральной точки)
        for (ModifyerBase m : t.getModyfiers())
        {
            if (m instanceof Rotate)
            {
                ((Rotate)m).setCx(t.getCx());
                ((Rotate)m).setCy(t.getCy());
            }
        }
    }

    @Override
    protected void saveData(RaphaelObjectDescription rd, Map<String, Object> data)
    {
        rd.appendCreator(rd.getAccessor() + ".data = {};\n");
        if (!data.isEmpty())
        {
            rd.appendCreator(rd.getAccessor() + ".data = " + JsonUtils.toJson(data) + ";\n");
        }
        rd.appendCreator(rd.getAccessor() + ".data.id = '" + rd.getAccessor() + "';\n");
    }

    @Override
    protected void setAttributes(RaphaelObjectDescription rd, Map<String, String> attributes)
    {
        for (Map.Entry<String, String> attribute : attributes.entrySet())
        {
            {
                rd.appendCreator(
                        rd.getAccessor() + ".attr('" + attribute.getKey() + "', '" + attribute.getValue() + "');\n");
            }
        }
    }

    private static boolean isUseAjaxObserver()
    {
        return false;
    }

    private static String trimId(String id)
    {
        int i = id.indexOf("tabcnt");
        String tabContentUUID = id.substring(i, i + 32);

        //последние 4 циферки - самые часто изменяющиеся
        return tabContentUUID.substring(28);
    }
}
