package ru.naumen.smp.report.specification.model;

import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Контекст построения отчета.
 * <AUTHOR>
 * @since 17.04.2025
 */
public interface ReportBuildContext
{
    /**
     * @return шаблон, по которому строится отчет
     */
    Template getReportTemplate();

    /**
     * @return заполненные параметры отчета
     */
    List<Parameter> getParameters();

    /**
     * @return контекст скрипта отчета
     */
    Map<String, Object> getScriptContext();

    /**
     * @return информация о текущем пользователе
     */
    @Nullable
    <E extends CoreEmployee> E getCurrentUser();

    /**
     * @return UUID объекта контекста
     */
    @Nullable
    String getSubjectUuid();
}
