package ru.naumen.smp.report.specification.services;

import java.io.OutputStream;

import jakarta.annotation.Nullable;
import ru.naumen.smp.report.specification.model.Report;
import ru.naumen.smp.report.specification.model.ReportBuildContext;

/**
 * Интерфейс компонента, отвечающего за выгрузку отчета в определенном формате.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface ReportExportController
{
    /**
     * Выясняет, можно ли указанный отчет выгружать в потоковом режиме.
     * @param report отчет
     * @return <code>true</code>, если отчет можно выгружать в потоковом режиме, иначе <code>false</code>
     */
    boolean canBeStreamed(Report report);

    /**
     * Выгружает отчет в указанном формате.
     * @param buildContext контекст построения отчета
     * @param report построенный отчет
     * @param format формат выгрузки
     * @param clientTimeZone имя часового пояса клиента, для которого делается выгрузка
     * @param streamMode <code>true</code>, если выгрузка происходит в потоковом режиме, иначе <code>false</code>
     * @param outputStream поток вывода построенного отчета
     */
    void exportReport(ReportBuildContext buildContext, Report report, String format,
            @Nullable String clientTimeZone, boolean streamMode, OutputStream outputStream);

    /**
     * Возвращает расширение файла для определенного формата.
     * @param format формат отчета
     * @return расширение файла
     */
    String getFormatFileExtension(String format);
}
