package ru.naumen.smp.report.specification.model;

import java.util.List;

import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Интерфейс отчета
 *
 * <AUTHOR>
 * @since 08.10.2012
 */
public interface Report extends IUUIDIdentifiable
{
    /**
     * Возвращает код сообщения об ошибке, возникшей при построении или выгрузке отчета
     * @return
     */
    String getErrorMessageCode();

    /**
     * Возвращает параметры, используемые для построения отчета
     * @return
     */
    List<Parameter> getParameters();

    /**
     * Возвращает размер таблицы, содержащей данные отчета
     * @return
     */
    Long getTableSize();

    /**
     * Возвращает код шаблона отчета
     * @return
     */
    String getTemplateCode();

    /**
     * Устанивить код сообщения об ошибке, возникшей при построении или выгрузке отчета
     * @return
     */
    Report setErrorMessageCode(String code);

    /**
     * Установить размер таблицы, содержащей данные отчета
     * @return
     */
    Report setTableSize(Long size);

    /**
     * @return может ли отчет строиться в потоковом режиме
     */
    default boolean canBeStreamed()
    {
        return false;
    }
}
