package ru.naumen.smp.report.specification.services;

import jakarta.annotation.Nullable;

/**
 * Фабрика агрегирующих функций для скриптов.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface ReportScriptFunctionFactory
{
    /**
     * Создает функцию группировки.
     * @param name название функции
     * @param closure замыкание, реализующее функцию
     * @return созданная функция группировки
     */
    @Nullable
    Object createGroupFunction(String name, Object closure);

    /**
     * Создает функцию суммирования.
     * @param name название функции
     * @param closure замыкание, реализующее функцию
     * @return созданная функция суммирования
     */
    @Nullable
    Object createTotalFunction(String name, Object closure);
}
