package ru.naumen.smp.report.specification.services;

/**
 * Компонент для контроля размера (веса) отчета.
 * <AUTHOR>
 * @since Mar 15, 2025
 */
public interface ReportWeightController
{
    /**
     * @return максимальный размер модели данных отчета
     */
    long getMaxSize();

    /**
     * @return максимальный размер модели данных для многопоточного построения
     */
    long getMaxConcurrentSize();

    /**
     * Проверяет размер отчета на соответствие допустимым параметрам.
     * @param weight размер отчета
     */
    void checkReportWeight(int weight);

    /**
     * Определяет возможность оперативно выгрузить отчет.
     * @param weight размер отчета
     * @param format формат выгрузки
     * @return <code>true</code>, если выгрузить отчет оперативно невозможно, иначе <code>false</code>
     */
    boolean needToBeDeferred(int weight, String format);
}
