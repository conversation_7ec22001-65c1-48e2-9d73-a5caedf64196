package ru.naumen.smp.report.specification.model;

import java.io.Serializable;
import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Модель данных отчета.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface ReportDataModel extends IsSerializable, HasReportValues, Serializable
{
    /**
     * Добавляет новую колонку в табличную модель.
     * @param column имя колонки
     */
    ReportDataModel addColumn(String column);

    /**
     * Добавляет новую строку с данными в табличную модель.
     * @param row строка данных
     */
    ReportDataModel addRow(TableModelRow row);

    /**
     * Добавляет новое единичное значение в модель отчета.
     * @param value описание значения
     */
    ReportDataModel addValue(ReportVariable value);

    /**
     * @return колонки в модели отчета
     */
    List<String> getColumns();

    /**
     * @return строки данных модели отчета
     */
    List<TableModelRow> getRows();

    /**
     * @return одиночные значения из модели отчета
     */
    List<ReportVariable> getValues();
}
