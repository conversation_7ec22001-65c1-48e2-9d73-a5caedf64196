package ru.naumen.smp.report.specification.model;

import java.io.Serializable;
import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.reports.server.script.ITableModelWrapper.ITableModelRow;

/**
 * Модель строки табличных данных отчета.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface TableModelRow extends ITableModelRow, IsSerializable, Serializable
{
    /**
     * Добавляет новое значение в строку.
     * @param value новое значение
     */
    TableModelRow addValue(Object value);

    /**
     * @return все значения в строке
     */
    List<Object> getValues();
}
