package ru.naumen.smp.report.specification.model;

import java.util.List;

/**
 * Интерфейс объектов, содержащих значения переменных из отчёта.
 * <AUTHOR>
 * @since 09.11.2012
 */
public interface HasReportValues
{
    /**
     * @return список содержащихся значений переменных
     */
    List<ReportVariable> getReportValues();

    /**
     * Задает значения переменных отчёта.
     * @param values список значений
     */
    void setReportValues(List<ReportVariable> values);
}
