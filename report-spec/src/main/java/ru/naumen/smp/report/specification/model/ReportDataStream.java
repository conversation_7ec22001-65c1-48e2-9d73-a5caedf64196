package ru.naumen.smp.report.specification.model;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.Callable;

/**
 * Модель потоковых данных отчета.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface ReportDataStream
{
    /**
     * Закрывает поток и завершает работу с ним.
     * @throws IOException если закрыть поток должным образом не получилось
     */
    void finish() throws IOException;

    /**
     * @return размер (в ячейках) табличных данных отчета
     */
    long getTableSize();

    /**
     * Выполняет операцию над потоковыми данными и возвращает результат. Поток закрывается после завершения операции.
     * @param callable выполняемая операция
     * @return результат выполнения операции
     * @param <T> тип возвращаемого значения
     * @throws IOException если закрыть поток должным образом не получилось
     */
    <T> T processAndFinish(Callable<T> callable) throws IOException;

    /**
     * Задает размер табличных данных отчета.
     * @param tableSize размер данных (количество ячеек)
     */
    void setTableSize(long tableSize);

    /**
     * Преобразует объект к состоянию, когда с ним можно работать как с потоком ввода.
     */
    InputStream asInputStream();
}
