package ru.naumen.smp.report.specification.model;

import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.CoreHasCode;
import ru.naumen.smp.report.specification.shared.Parameter;
import ru.naumen.smp.report.specification.shared.ReportScript;

/**
 * Шаблон отчета.
 * <AUTHOR>
 * @since Jan 11, 2025
 */
public interface Template extends CoreHasCode
{
    /**
     * @return файл шаблона
     */
    byte[] getFile();

    /**
     * @return скрипт шаблона
     */
    @Nullable
    <S extends ReportScript> S getScript();

    /**
     * @return параметры, объявленные в шаблоне
     */
    List<Parameter> getParameters();
}
