package ru.naumen.smp.report.specification.model;

import java.io.Serializable;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.HasClone;
import ru.naumen.metainfo.shared.CoreHasCode;

/**
 * Отдельное значение в модели данных отчета.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
public interface ReportVariable extends CoreHasCode, Serializable, HasClone
{
    /**
     * @return значение параметра/переменной
     */
    Object getValue();

    /**
     * Задает код для значения.
     */
    ReportVariable setCode(String code);

    /**
     * Устанавливает значение.
     */
    ReportVariable setValue(@Nullable Object value);
}
