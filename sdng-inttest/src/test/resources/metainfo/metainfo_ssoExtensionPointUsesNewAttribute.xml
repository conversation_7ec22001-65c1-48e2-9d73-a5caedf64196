<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
    <head>
        <date>2025-02-07T14:51:49.938+05:00</date>
        <exportMode>partial</exportMode>
        <version>4.20.0-SNAPSHOT</version>
    </head>
    <tags/>
    <sets/>
    <adminProfiles/>
    <system-metaclass seg-detach="true" seg-id="root" seg-type="metaclasses">
        <fqn>
            <id>root</id>
        </fqn>
        <parent>
            <id>abstractBO</id>
        </parent>
        <title lang="de">Firma</title>
        <title lang="en">Company</title>
        <title lang="ru">Компания</title>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute seg-detach="true" seg-id="forSSOTest" seg-type="attribute">
                <code>forSSOTest</code>
                <hiddenAttrCaption>false</hiddenAttrCaption>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">forSSOTest</title>
                <description lang="ru"/>
                <type>
                    <code>string</code>
                    <property code="code">string</property>
                    <property code="inputMask"/>
                    <property code="inputMaskMode"/>
                    <property code="string">255</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>stringView</code>
                </viewPresentation>
                <editPresentation>
                    <code>stringEdit</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting seg-detach="true" seg-id="forSSOTest_forSSOTest" seg-type="search-settings">
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>forSSOTest</code>
                    <declaredMetaClass>root</declaredMetaClass>
                    <attrCode>forSSOTest</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
                <advlistSemanticFiltering>false</advlistSemanticFiltering>
                <editOnComplexFormOnly>false</editOnComplexFormOnly>
                <hideArchived>true</hideArchived>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow seg-detach="true" seg-id="workflow" seg-type="workflow"/>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>7</searchOrder>
    </system-metaclass>
    <mail-processor-rules/>
    <style-templates/>
    <list-templates/>
    <content-templates/>
    <user-events/>
    <event-actions/>
    <system-jmsqueues/>
    <user-jmsqueues/>
    <embedded-applications/>
    <custom-forms/>
    <advimport/>
    <script-modules>
        <module seg-detach="true" seg-id="smpSSOCustomizationOidcRedirectionActionBuilder" seg-type="script-modules">
            <code>smpSSOCustomizationOidcRedirectionActionBuilder</code>
            <description/>
            <active>false</active>
            <script checksum="bef1efe0d59875038424c2e716224dccddd6262e489b77c4c62aedc76a5afb81"><![CDATA[import org.pac4j.oidc.client.OidcClient
import org.pac4j.oidc.redirect.OidcRedirectionActionBuilder

OidcRedirectionActionBuilder getOidcRedirectionActionBuilder(OidcClient oidcClient)
{
  def company = utils.get('root', [:])
  def oldValue = company.forSSOTest ?: ""
  utils.edit(company, ['forSSOTest': oldValue + 'SUCCESS'], true)
  return new OidcRedirectionActionBuilder(oidcClient)
}]]></script>
            <view_by_superusers>false</view_by_superusers>
            <edit_by_superusers>false</edit_by_superusers>
            <rest_allowed>false</rest_allowed>
        </module>
    </script-modules>
    <scripts/>
    <customJSElements/>
    <fast-link-settings/>
    <objects/>
    <transfer-values/>
    <structured-objects-views/>
    <libraries/>
    <eventStorageRules/>
</metainfoContainer>
