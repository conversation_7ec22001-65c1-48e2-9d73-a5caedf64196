<?xml version="1.0" encoding="UTF-8" standalone="no"?><ns2:naumen-license xmlns:ns2="http://naumen.ru/license">
    <baseLevel>server</baseLevel>
    <client>Naumen (Dev-стенд)</client>
    <author><PERSON><PERSON><PERSON><PERSON></author>
    <creationDate>2025.03.04 15:00</creationDate>
    <parameter name="rolesForUnlicensedUsers">
        employee, ouHead, currentUser, commentAuthor, fileAuthor, ServiceCallClient, ouMember, AgreementRecipient, slmServiceRecipient, serviceCallEmployeeOfClientOU
    </parameter>
	<parameter expirationDate="2025.01.20" name="permissionsSetForUnlicensedUsers">
        custom
    </parameter>	
	<parameter expirationDate="2024.09.24" name="visibleAttributesToUnlicensedUser">
       ou: recipientAgreements;       
       slmService: responsible;
       employee: teams;
       *ATTRIBUTES_TYPES*: boLinks;
    </parameter>
	<parameter expirationDate="2024.09.24" name="editableAttributesToUnlicensedUser">
       ou: title, parent, head, recipientAgreements;
       root: head;              
       slmService: stateStartTime, callCases, inventoryNumber, state, agreements;
       employee: teams, license, performer, firstName, lastName, parent, password, recipientAgreements;
       team: recipientAgreements, members;  
       *ATTRIBUTES_TYPES*: boLinks, string, dateTime;
    </parameter>
    <parameter name="isTitleLocalizationAllowed">true</parameter>
    <parameter name="restSessionsControlDisabled">true</parameter>
    <parameter name="accessToSMPInterface">true</parameter>
    <superuser code="superuser" count="10" expirationDate="2025.01.20"/>
    <named accessToSMPInterface="true" code="named" count="10" expirationDate="2025.01.20"/>
    <concurrent accessToSMPInterface="true" code="concurrent" count="10" expirationDate="2025.01.20"/>
    <quantitativeLicense>
        <quota code="quota" delay="true" delayLimit="0" max="10" warn="80">
            <limitedClassesOrTypesWithHeirs>quotaClass</limitedClassesOrTypesWithHeirs>
            <exceptionsOfLimitedClassesOrTypesWithHeirs>quotaClass$exceptedType</exceptionsOfLimitedClassesOrTypesWithHeirs>
        </quota>
    </quantitativeLicense>
    <modules expirationDate="2025.01.20">cti, cmdb, admin-lite, workload, mobile-api, ndap, smia, plannedVersion, omnichannel, portal, dynamicField</modules>
</ns2:naumen-license>