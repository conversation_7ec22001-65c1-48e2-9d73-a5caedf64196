package ru.naumen.selenium.cases.admin.system;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.icegreen.greenmail.imap.ImapConstants;
import com.icegreen.greenmail.imap.ImapHostManager;
import com.icegreen.greenmail.imap.ImapServer;
import com.icegreen.greenmail.user.GreenMailUser;
import com.icegreen.greenmail.util.GreenMail;
import com.icegreen.greenmail.util.ServerSetup;

import jakarta.mail.Folder;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Store;
import jakarta.mail.internet.MimeMessage;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.mail.DSLMailLogRecord;
import ru.naumen.selenium.casesutil.mail.MailTestCase;
import ru.naumen.selenium.casesutil.model.mail.Email;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMailClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTask;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.silent.DSLSilentMode;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.util.Pair;

/**
 * Тестирование получения почты по протоколу IMAP (используя GreenMail)
 *
 * <AUTHOR>
 * @since 15.12.2024
 */
public class ImapTest extends AbstractTestCase
{
    /**
     * По идентификатору письма в таблице возвращает, удалено ли оно с почтового сервера
     */
    private static final String GET_IS_DELETED_FROM_MAIL_SERVER_SCRIPT = """
            return beanFactory.getBean('inboundMailDao').loadItemByCreatedMailID(%s).orElseThrow().isDeletedFromMailServer();
            """;

    /**
     * По идентификатору письма в таблице возвращает уникальный в рамках почтового сервера, с которого письмо выкачано,
     * идентификатор письма
     */
    private static final String GET_CREATED_MESSAGE_ID_SCRIPT = """
            return beanFactory.getBean('inboundMailDao').loadItemByCreatedMailID(%s).orElseThrow().getMailMessageId();
            """;

    /**
     * Помечает письмо с переданным идентификатором (Message-ID) как неудаленное с почтового сервера
     */
    private static final String MARK_UNDELETED_FROM_MAIL_SERVER_SCRIPT = """
            beanFactory.getBean('mailStorageServiceImpl').setDeletedFromMailServer('%s', false)
            """;

    private static final MetaClass SYS_MAIL_CLASS = DAOMailClass.create();

    /**
     * Набор сервисов (поднятых локально серверов входящей и исходящей почты (здесь только IMAP))
     */
    private static GreenMail greenMail;

    /**
     * Сервер входящей почты
     */
    private static ImapServer imapServer;

    /**
     * Зарегистрированный на {@link #imapServer сервере входящей почты} пользователь (тестовый пользователь)
     */
    private static GreenMailUser user;

    /**
     * Менеджер для работы с папками
     */
    private static ImapHostManager imapHostManager;

    /**
     * Модель задачи обработки входящей почты, настроенная на получение писем с тестового почтового сервера
     */
    private static SchedulerTask receiveMailTask;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Настроить исключение Silent mode для хоста по умолчанию (localhost)</li>
     * <li>Создать валидное подключение к серверу входящей почты по протоколу IMAP</li>
     * <li>На основе созданного подключения запустить тестовый сервер входящей почты</li>
     * <li>Зарегистрировать на сервере пользователя с логином и паролем такими же, как в настройке созданного
     * подключения</li>
     * <li>На основе настроек созданного подключения создать папки для успешно и неуспешно обработанных писем у
     * зарегистрированного пользователя</li>
     * <li>Создать задачу для планировщика задач на обработку входящей почты, с созданным подключением и ничего не
     * делающим правилом обработки</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture() throws MessagingException
    {
        DSLSilentMode.setEnabled(true, DAOSchedulerTask.DEFAULT_SERVER);
        InboundMailConnection connection = DAOSchedulerTask.createValidImapInboundMailConnection();
        int port = Integer.parseInt(connection.getPort());
        ServerSetup setup = new ServerSetup(port, connection.getServer(), ServerSetup.PROTOCOL_IMAP);
        greenMail = new GreenMail(setup);
        greenMail.start();
        imapServer = greenMail.getImap();
        imapHostManager = greenMail.getManagers().getImapHostManager();
        user = greenMail.setUser(connection.getLogin(), connection.getPassword());
        createFolders(connection);
        DSLSchedulerTask.addInboundMailConnection(connection);
        createSchedulerInboundMailTask(connection);
    }

    /**
     * Выполняет действия после запуска всех тестов в классе:
     * <ol>
     * <li>Останавливает тестовый сервер входящей почты</li>
     * <li>Выключает исключение silent mode для хоста по умолчанию (localhost)</li>
     * <li>Очищает лог входящей почты</li>
     * </ol>
     */
    @AfterClass
    public static void cleanup()
    {
        greenMail.stop();
        DSLSilentMode.setEnabled(false, DAOSchedulerTask.DEFAULT_SERVER);
        Cleaner.afterAllTest(true, DSLMailLogRecord::clearAllIncomingMailLog);
    }

    /**
     * Создает на тестовом сервере папки для успешно и неуспешно выгруженных с почтового сервера писем, указанных в
     * настройке переданного подключения для созданного ранее пользователя
     * @param connection настройка подключение к серверу входящей почты
     */
    private static void createFolders(InboundMailConnection connection)
            throws MessagingException
    {
        final Session imapSession = imapServer.createSession();
        final Store store = imapSession.getStore(ServerSetup.PROTOCOL_IMAP);
        store.connect(user.getLogin(), user.getPassword());
        store.getFolder(connection.getCorrectMailFolder()).create(Folder.READ_ONLY);
        store.getFolder(connection.getIncorrectMailFolder()).create(Folder.HOLDS_MESSAGES);
    }

    /**
     * Создает задачу планировщика на обработку входящей почты на основе переданного подключения
     * @param inboundConnection настройка подключение к серверу входящей почты
     */
    private static void createSchedulerInboundMailTask(InboundMailConnection inboundConnection)
    {
        final ScriptInfo mailRuleScript = DAOScriptInfo.createNewScriptInfo(MailTestCase.DO_NOTHING_SCRIPT);
        receiveMailTask = DAOSchedulerTask.createReceiveMailTask(true, inboundConnection, mailRuleScript);
        receiveMailTask.setRuleEnable(Boolean.TRUE.toString());
        DSLSchedulerTask.addTask(receiveMailTask);
    }

    /**
     * Тестирование, что при выгрузке письма с сервера IMAP и удалении на почтовом сервере, информация об успешном
     * удалении сохраняется системой, а идентификатор письма (для выявления писем-дублей) берется из заголовка
     * Message-ID, если таковой у письма имеется, а иначе формируется из других заголовков <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00362 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$230806451
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка</li>
     * <b>Действия</b>
     * <br>
     * <li>Для письма с установленным заголовком Message-ID:</li>
     * <li>Доставить письмо на почту тестового пользователя</li>
     * <li>Запустить задачу обработки входящей почты</li>
     * <b>Проверки</b>
     * <li>Проверить, что письмо выгрузилось с сервера</li>
     * <li>Проверить, что в системе письмо отметилось удаленным с почтового сервера</li>
     * <li>Проверить, что Message-ID письма в системе совпадает с ожидаемым</li>
     * <i>Для письма с неустановленным Message-ID аналогично повторить шаги, начиная со 2</i>
     * </ol>
     */
    @Test
    public void testLoadMailWhenSuccessfullyDeletedFromPostServer() throws Exception
    {
        // Подготовка
        List<Pair<String, String>> testCases = List.of(
                new Pair<>(DAOSchedulerTask.EML_FILE,
                        "<<EMAIL>>"),
                new Pair<>(DAOSchedulerTask.EML_WITHOUT_MESSAGE_ID,
                        "<EMAIL><EMAIL>-Mon, 25 Nov 2024 22:57:06 -0800 (PST)")
        );
        for (Pair<String, String> testCase : testCases)
        {
            // Действия
            Set<String> oldMailUuids = DSLBo.getUuidsByFqn(SystemClass.MAIL.getCode());
            deliverResourceMessage(testCase.first());
            DSLSchedulerTask.forceRunAndWaitExecuted(receiveMailTask);

            // Проверки
            Email mail = DSLBo.getNewModel(oldMailUuids, DAOMailClass.create(), Email.class);
            Assert.assertTrue(Boolean.parseBoolean(ScriptRunner.executeScript(
                    GET_IS_DELETED_FROM_MAIL_SERVER_SCRIPT, mail.getIdByUUID())));
            Assert.assertEquals(testCase.second(), getMessageId(mail));
        }
    }

    /**
     * Тестирование обнаружения загрузки письма-дубля, исходя из того, что до этого после загрузки в систему письмо было
     * отмечено как неудаленное с почтового сервера <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00362 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$230806451
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка</li>
     * <li>Очистить таблицу очереди обработки писем и таблицу с письмами</li>
     * <b>Действия</b>
     * <li>Доставить письмо mail на почту тестового пользователя</li>
     * <li>Запустить задачу обработки входящей почты</li>
     * <li>Отметить пришедшее письмо неудаленным с почтового сервера (хак)</li>
     * <li>Доставить письмо mail на почту тестового пользователя (абсолютно такое же, как будто не удалилось)</li>
     * <li>Запустить задачу обработки входящей почты</li>
     * <b>Проверки</b>
     * <li>Проверить, что новых писем в системе не появилось</li>
     * <li>Проверить, что в логе появилось сообщение о получении письма-дубля</li>
     * </ol>
     */
    @Test
    public void testDetectExpectedMailDuplicate() throws Exception
    {
        // Действия
        deliverResourceMessage(DAOSchedulerTask.EML_FILE);
        Set<String> expectedUuids = DSLBo.getUuidsByFqn(SYS_MAIL_CLASS.getCode());
        DSLSchedulerTask.forceRunAndWaitExecuted(receiveMailTask);
        Email mail = DSLBo.getNewModel(expectedUuids, SYS_MAIL_CLASS, Email.class);
        expectedUuids = DSLBo.getUuidsByFqn(SYS_MAIL_CLASS.getCode());
        markMailUndeletedFromPostServer(mail);
        deliverResourceMessage(DAOSchedulerTask.EML_FILE);
        DSLSchedulerTask.forceRunAndWaitExecuted(receiveMailTask);
        // Проверки
        DSLBo.assertNoNewBoWithType(SYS_MAIL_CLASS, expectedUuids);
        DSLLog.assertLog()
                .assertPresent("Message %s with messageID %s wasn’t deleted on server (duplicate mail)"
                        .formatted(mail.getIdByUUID(), getMessageId(mail)));
    }

    /**
     * Помечает письмо (связанный элемент в очереди обработки писем) как неудаленным с почтового сервера
     */
    private static void markMailUndeletedFromPostServer(Email mail)
    {
        ScriptRunner.executeScript(MARK_UNDELETED_FROM_MAIL_SERVER_SCRIPT, getMessageId(mail));
    }

    /**
     * Тестирование, что система может выявить загрузку письма-дубля, исходя из того, что до этого письмо было
     * отмечено как удаленное с почтового сервера после загрузки в систему, при условии, что информация об этом еще не
     * удалилась задачей очистки очереди обработки писем <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00362 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$230806451
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка</li>
     * <li>Очистить таблицу очереди обработки писем и таблицу с письмами</li>
     * <b>Действия</b>
     * <li>Доставить письмо mail на почту тестового пользователя</li>
     * <li>Запустить задачу обработки входящей почты</li>
     * <li>Доставить письмо mail на почту тестового пользователя (абсолютно такое же, как будто не удалилось)</li>
     * <li>Запустить задачу обработки входящей почты</li>
     * <b>Проверки</b>
     * <li>Проверить, что новых писем в системе не появилось</li>
     * <li>Проверить, что в логе появилось сообщение о получении письма-дубля</li>
     * </ol>
     */
    @Test
    public void testDetectUnexpectedMailDuplicate() throws Exception
    {
        // Действия
        deliverResourceMessage(DAOSchedulerTask.EML_FILE);
        Set<String> expectedUuids = DSLBo.getUuidsByFqn(SYS_MAIL_CLASS.getCode());
        DSLSchedulerTask.forceRunAndWaitExecuted(receiveMailTask);
        Email mail = DSLBo.getNewModel(expectedUuids, SYS_MAIL_CLASS, Email.class);
        expectedUuids = DSLBo.getUuidsByFqn(SYS_MAIL_CLASS.getCode());
        deliverResourceMessage(DAOSchedulerTask.EML_FILE);
        DSLSchedulerTask.forceRunAndWaitExecuted(receiveMailTask);
        // Проверки
        DSLBo.assertNoNewBoWithType(SYS_MAIL_CLASS, expectedUuids);
        DSLLog.assertLog()
                .assertPresent(("Message %s with messageID %s wasn’t deleted on server (duplicate mail). The"
                                + "\"deleted_from_mail_server\" parameter was incorrectly set by the server").formatted(
                        mail.getIdByUUID(), getMessageId(mail)));
    }

    /**
     * Доставляет сообщение из ресурсов тестирующей системы в почтовый ящик пользователя, зарегистрированного на
     * тестовом сервере
     */
    private static void deliverResourceMessage(String resourceName)
            throws Exception
    {
        try (InputStream is = Files.newInputStream(Path.of(resourceName)))
        {
            MimeMessage message = new MimeMessage(Session.getInstance(new Properties()), is);
            imapHostManager.getFolder(user, ImapConstants.INBOX_NAME).store(message);
        }
    }

    /**
     * Возвращает Message-ID переданного письма в системе
     */
    private static String getMessageId(Email mail)
    {
        return ScriptRunner.executeScript(GET_CREATED_MESSAGE_ID_SCRIPT, mail.getIdByUUID());
    }
}
