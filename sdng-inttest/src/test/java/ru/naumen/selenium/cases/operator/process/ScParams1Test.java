package ru.naumen.selenium.cases.operator.process;

import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrServicePrs;
import ru.naumen.selenium.casesutil.admin.GUIScParams.OrderScFields;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUISelectCase;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFastFilterForm;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.mail.DAOEmail;
import ru.naumen.selenium.casesutil.model.mail.Email;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection.MailProtocol;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTask;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.TestCaseInfo;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на параметры запроса по умолчанию в интерфейсе оператора
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00284
 * <AUTHOR>
 *
 */
public class ScParams1Test extends AbstractTestCase
{
    private static final String CASES_FILTRATION_SCRIPT = """
            def ATTRS_FOR_UPDATE_ON_FORMS = []
            if (subject == null) { return ATTRS_FOR_UPDATE_ON_FORMS }
            return [%s];""";

    private static MetaClass scCase1, scCase2, scCase3;
    private static Bo agreement, service, employee;

    /**
     * Общая подготовка
     * <br>
     * <ul>
     * <li>Создать типы запроса scCase1, scCase2, scCase3</li>
     * <li>Создать соглашение agreement и связанную с ним услугу service</li>
     * <li>Добавить связь услуги service с типами запроса scCase1, scCase2, scCase3</li>
     * <li>Создать сотрудника employee со всеми правами</li>
     * <li>Связать соглашение с сотрудником</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        scCase1 = DAOScCase.create();
        scCase2 = DAOScCase.create();
        scCase3 = DAOScCase.create();
        DSLMetainfo.add(scCase1, scCase2, scCase3);

        agreement = DAOAgreement.create(SharedFixture.agreementCase());
        service = DAOService.create(SharedFixture.slmCase());
        agreement.setAgrResolutionTimeRuleUuid(SharedFixture.rsResolutionTime().getUuid());
        agreement.setAgrPriorityRuleUuid(SharedFixture.rsPriority().getUuid());
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addToRecipients(agreement, employee);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase1, scCase2, scCase3);
    }

    /**
     * Контрагент не является получателем Соглашения/Услуги которая указана в качестве значения по умолчанию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00284
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>1. Создаем лицензированного сотрудника employee типа employeeCase</li>
     * <li>3. Создаем соглашение agreement типа agreementCase</li>
     * <li>4. Создаем услугу service типа serviceCase </li>
     * <li>5. Создаем тип запроса scCase</li>
     * <li>6. В рамкой service доступен тип запроса scCase</li>
     * <li>7. Связываем соглашение  agreement с услугой  service</li>
     * <li>8. Сотрудник  employee *не является* получателями соглашения  agreement</li>
     *
     * <li>10. Параметры запроса:</li>
     * <li>Значение поля "Соглашение/Услуга" : Соглашение или услуга </li>
     * <li>Представление: Иерархический список</li>
     *
     * <li>11. Для типа employeeCase</li>
     * <li>Параметры запроса по умолчанию :</li>
     * <li>Соглашение/Услуга :  service</li>
     * <li>Тип объекта: scCase</li>
     * <br>
     *
     * <b>Выполнение действия</b>
     * <li>Заходим в систему под employee и нажимаем на его карточке добавить запрос</li>
     * <br>
     * <b>Проверки</b>
     * <li>Соглашение/Услуга : service</li>
     * <li>Нажимаем кнопку «Сохранить».</li>
     * <li>Над полем «Тип объекта» появляется всплывающее сообщение «Поле должно быть заполнено». Запрос
     * зарегистрировать не удалось.</li>
     * </ol>
     */
    @Test
    public void testNoAgreement()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(ouCase, scCase, DAOScCase.create(), serviceCase);

        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        Bo agreement = SharedFixture.agreement();
        Bo ou = SharedFixture.ou();

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        DSLSlmService.addScCases(service, scCase);
        DSLSlmService.addAgreements(service, agreement);
        DSLMetaClass.setScParams(employeeCase, agreement, service, scCase);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        // GUILogon.login(employee);
        GUILogon.asSuper();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, sc.getDescription());
        GUISelect.select(GUIXpath.InputComplex.TIME_ZONE_VALUE, sc.getScTimeZoneUuid());
        GUIForm.applyFormAssertValidation(Div.FORM_CONTAINS);
        assertTrue("Мы переместились с формы добавления запроса",
                tester.waitAppear(GUIXpath.Div.HEADER_TITLE_ANY_FORM_ADD, "Запрос"));
    }

    /**
     * Проверяет отсутсвие ошибки на форме добавления запроса при настроенном скрипте фильтарции соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Параметры запросов: Значение поля "Соглашение/Услуга" : Соглашение, Представление: Простой список, Скрипт
     * фильтрации: return []</li>
     * <li>Создаем соглашение agreement типа agreementCase</li>
     * <li>Создаем услугу service типа serviceCase</li>
     * <li>Создаем тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связываем соглашение agreement с услугой service</li>
     * <li>Создаем тип сотрудника employeeCase</li>
     * <li>Параметры запроса по умолчанию в employeeCase: Соглашение/Услуга : agreement Тип объекта: scCase</li>
     * <li>Создаем сотрудника employee типа employeeCase</li>
     * <li>Добавляем созданного сотрудинка в получатели соглашения agreement</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открываем форму добавления запроса из карточки сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>На форме добавления не должно быть ошибок</li>
     * </ol>
     */
    @Test
    public void testScFormAgreementFilterError()
    {
        //Подготовка       
        MetaClass scCase = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(scCase, serviceCase, employeeCase);
        Bo agreement = SharedFixture.agreement();
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase);
        DSLMetaClass.setScParams(employeeCase, agreement, null, scCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo("return []");
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, filterScript, null);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        // Проверки
        GUIError.waitError(2);
    }

    /**
     * Тестирование перефильтрации услуг на форме смены привязки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00461
     * http://sd-jira.naumen.ru/browse/NSDPRD-5035
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать тип соглашения agrCase</li>
     * <li>Создать тип услуги serviceCase</li>
     * <li>Создать команды team1,team2 типа teamCase</li>
     * <li>Создать employee1 типа employeeCase и добавить в комманду team1</li>
     * <li>Создать employee2 типа employeeCase и добавить в комманду team2</li>
     * <li>Создать соглашения agreement1,agreement2 типа agrCase.</li>
     * <li>Создать услуги serviceShared,serviceForTeam1, serviceForTeam2  типа  serviceCase.</li>
     * <li>Привязать услуги serviceShared,serviceForTeam1, serviceForTeam2 к соглашениям agreement1,agreement2</li>
     * <li>Получателем соглашений agreement1 и agreement2 выбрать команды  team1 и team2 соответсвенно</li>
     * <li>Параметры запросов: Значение поля "Соглашение/Услуга" : услуга, Представление: Простой список, Скрипт
     * фильтрации услуг: </li>
     * <pre>
     logger.info('filter script begin')
     def attrCode = ['client'];
     if (subject == null)
     {
     logger.error('subj--NULL')
     return attrCode;
     }
     def team1 = 'team1.UUID'
     def team2 = 'team2.UUID'
     if (subject?.client?.UUID == team1)
     {
     logger.info('team1')
     return [utils.get('serviceShared.UUID'), utils.get('serviceForTeam1.UUID')]//верни общую услугу и услугу для team1
     }
     else
     {
     if (subject?.client?.UUID == team2)
     {
     logger.info('team2')
     return [utils.get('serviceShared.UUID'), utils.get('serviceForTeam2.UUID')]//верни общую услугу и услугу для team2
     }
     else
     {
     logger.info('любая другая команда')
     return utils.find('slmService',[:])
     }
     }
     * </pre>
     * <li>Создаем запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Нажимаем на кнопку "Изменить привязку"</li>
     * <li>Выбираем в выпадающем списке "Контрагент" team1</li>
     * <li>Проверяем, что доступны для выбора услуги serviceShared и serviceForTeam1</li>
     * <li>Выбираем в выпадающем списке "Контрагент" team2</li>
     * <li>Проверяем, что доступны для выбора услуги serviceShared и serviceForTeam2</li>
     * <li>Проверяем, что скрипт фильтрации был запущен 5 раз
     *     (один раз при добавлении скрипта, а 4 по делу</li>
     * </ol>
     */
    @Test
    public void testScParamAgreementScriptFilterOnChanheAssocForm()
    {
        MetaClass scCase = SharedFixture.scCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agrCase = SharedFixture.agreementCase();
        MetaClass serviceCase = SharedFixture.slmCase();

        Bo team1 = DAOTeam.create(teamCase);
        Bo team2 = DAOTeam.create(teamCase);
        Bo employee1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(team1, team2, employee1, employee2);
        DSLTeam.addEmployees(team1, employee1);
        DSLTeam.addEmployees(team2, employee2);
        Bo agreement1 = DAOAgreement.createWithRules(agrCase, SharedFixture.serviceTime(), SharedFixture.serviceTime(),
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(agreement1);
        Bo agreement2 = DAOAgreement.createWithRules(agrCase, SharedFixture.serviceTime(), SharedFixture.serviceTime(),
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(agreement2);
        Bo serviceShared = DAOService.create(serviceCase);
        Bo serviceForTeam1 = DAOService.create(serviceCase);
        Bo serviceForTeam2 = DAOService.create(serviceCase);
        DSLBo.add(serviceShared, serviceForTeam1, serviceForTeam2);
        DSLSlmService.addToScCase(scCase, serviceShared, serviceForTeam1, serviceForTeam2);
        DSLAgreement.addServices(agreement1, serviceShared, serviceForTeam1, serviceForTeam2);
        DSLAgreement.addServices(agreement2, serviceShared, serviceForTeam1, serviceForTeam2);
        DSLAgreement.addRecipients(team1, agreement1);
        DSLAgreement.addRecipients(team2, agreement2);

        Bo sc = DAOSc.create(scCase, team2, agreement2, SharedFixture.timeZone());
        DSLBo.add(sc);
        String scriptBeginLogRecord = "filter script begin " + TestCaseInfo.getMethodName();

        //@formatter:off
        String scriptFormat = 
                "logger.info('"+scriptBeginLogRecord+"')\n"+
                "def attrCode = ['client'];\n"+
                "if (subject == null) \n"+
                "{\n"+
                "    logger.error('subj--NULL')\n"+
                "    return attrCode;\n"+
                "}\n"+
                "def team1 = '%s'\n"+
                "def team2 = '%s'\n"+
                "if (subject?.client?.UUID == team1)\n"+
                "{\n"+
                "    logger.info('team1')\n"+
                "    return [utils.get('%s'), utils.get('%s')]//верни общую услугу и услугу для team1\n"+
                "}\n"+
                "else\n"+
                "{\n"+
                "    if (subject?.client?.UUID == team2)\n"+
                "    {   \n"+
                "        logger.info('team2')\n"+
                "        return [utils.get('%s'), utils.get('%s')]//верни общую услугу и услугу для team2\n"+
                "    }\n"+
                "    else\n"+
                "    {     \n"+
                "        logger.info('любая другая команда')\n"+
                "        return utils.find('slmService',[:]) \n"+
                "    }\n"+
                "}\n";
        //@formatter:on
        String script = String.format(scriptFormat, team1.getUuid(), team2.getUuid(), serviceShared.getUuid(),
                serviceForTeam1.getUuid(), serviceShared.getUuid(), serviceForTeam2.getUuid());
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(script);
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.TREE_LIST, null, filterScript);

        //Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        BoTree clientTree = new BoTree(String.format(GUIXpath.Any.ANY_VALUE, "client"), false);
        clientTree.setElementInSelectTree(team1);
        GUISelect.assertDisplayedByTitle(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                serviceShared.getTitle(),
                serviceForTeam1.getTitle());
        GUISelect.assertNotDisplayedByTitle(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                serviceForTeam2.getTitle());
        clientTree.setElementInSelectTree(team2);
        GUISelect.assertDisplayedByTitle(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                serviceShared.getTitle(),
                serviceForTeam2.getTitle());
        GUISelect.assertNotDisplayedByTitle(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                serviceForTeam1.getTitle());
        int scriptRunsCount = DSLLog.countOccurrencesInCurrentTestLogAsString(" " + scriptBeginLogRecord);
        Assert.assertEquals("Количество запусков скрипта фильтрации услуг не совпадает с ожидаемым",
                5, scriptRunsCount);
    }

    /**
     * Тестирование корректной работы скрипта фильтрации соглашения, если он возвращает UUID-ы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-3110
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать 2 типа соглашения agrCase1..2</li>
     * <li>Создать соглашения agreement1..2 (по одному каждого типа)</li>
     * <li>Получателем agreement1..2 выбрать сотрудника employee </li>
     * <li>На форме добавления класса Запрос создать контент Выбор типа запроса scCaseSelect</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Фильтрация соглашений при редактировании - да</li>
     * <li>Скрипт: </li>
     * <pre>
     *  if (subject == null) return [] as List; 
     *  def permittedFqns = [api.types.newClassFqn('agreement$agr1')] as List;
     *  return utils.find('agreement',['metaClass':permittedFqns]).UUID; 
     * </pre>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employee</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect проверяем что присутствует только одно соглашение agreement1</li>
     * </ol>
     */
    @Test
    public void testScParamAgreementScriptFilterReturnUUIDs()
    {
        //Подготовка
        //Настраиваем форму добавления в классе Запрос
        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass agrCase1 = DAOAgreementCase.create();
        MetaClass agrCase2 = DAOAgreementCase.create();
        DSLMetaClass.add(employeeCase, scCase, agrCase1, agrCase2);

        Bo agreement1 = DAOAgreement.create(agrCase1);
        Bo agreement2 = DAOAgreement.create(agrCase2);
        DSLBo.add(agreement1, agreement2);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement1, employee);
        DSLAgreement.addToRecipients(agreement2, employee);

        String script =
                "if (subject == null) return [] as List; return utils.find('agreement',['metaClass': api.types"
                + ".newClassFqn('"
                + agrCase1.getFqn() + "')]).UUID;";
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(script);
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, filterScript, null);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        //Проверки
        String selectXPath = String.format(
                GUIXpath.Div.ID_PATTERN + GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                scCaseSelect.getXpathId());
        GUISelect.assertDisplayed(selectXPath, agreement1.getUuid());
        GUISelect.assertNotDisplayed(selectXPath, agreement2.getUuid(), agreement2.getTitle());
    }

    /**
     * Тестирование корректной работы скрипта фильтрации соглашения, если он возвращает более 21 соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3241
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать тип соглашения agrCase</li>
     * <li>Создать 26 соглашений типа  agrCase. У 25 соглашений сделать одинаковое название agrTitle</li>
     * <li>Получателем соглашений выбрать сотрудника employee </li>
     * <li>На форме добавления класса Запрос создать контент Выбор типа запроса scCaseSelect</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Фильтрация соглашений при редактировании - да</li>
     * <li>Скрипт: </li>
     * <pre>
     * if (subject == null) return [] as List; 
     * return utils.find('agreement',['title': 'agrTitle'])
     * </pre>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employee</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect проверяем что присутствует ровно 23 соглашения с названием agrTitle</li>
     * </ol>
     */
    @Test
    public void testScParamAgreementScriptFilterWithManyAgreements()
    {
        //Подготовка
        //Настраиваем форму добавления в классе Запрос
        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(employeeCase, scCase, agrCase);
        String agrTitle = ModelUtils.createTitle();
        Bo[] agreements = new Bo[26];
        for (int i = 0; i < 25; i++)
        {
            Bo agreement = DAOAgreement.create(agrCase);
            agreement.setTitle(agrTitle);
            agreements[i] = agreement;
        }
        Bo agreement2 = DAOAgreement.create(agrCase);
        agreements[25] = agreement2;
        DSLBo.add(agreements);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreements);

        String script = "if (subject == null) return [] as List; return utils.find('agreement',['title': '" + agrTitle
                        + "']);";
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(script);
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, filterScript, null);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        //Проверки
        int countAggrs = GUISelect.getCountElement(
                GUIXpath.Div.ID_PATTERN + GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agrTitle,
                scCaseSelect.getXpathId());
        String errorMessage = "В списке соглашений присутствует неверное количество соглашений с названием " + agrTitle;
        Assert.assertEquals(errorMessage, 23, countAggrs);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос, если кнопка Добавить в верхнем меню ограничена типом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00576
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>Добавить кнопку верхнего меню addButton для добавления запросов типов scCase1 и scCase2</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать на кнопку верхнего меню addButton - scCase1</li>
     * <li>Проверка: находимся на форме добавления типа scCase1 и для выбора в списке типов доступен только scCase1</li>
     * <li>Нажать на кнопку верхнего меню addButton - scCase2</li>
     * <li>Проверка: находимся на форме добавления типа scCase2 и для выбора в списке нет доступных типов</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfAddButtonLimitedByType()
    {
        //Подготовка
        String cases = String.format("'%s'", scCase1.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase1, scCase2);
        DSLNavSettings.editVisibilitySettings(true, true);
        DSLMenuItem.add(addButton);

        //Действия и проверки
        GUILogon.login(employee);
        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase1.getFqn());
        GUIForm.assertFormTitle(scCase1.getTitle() + " / Форма добавления");
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);

        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase2.getFqn());
        GUIForm.assertFormTitle(scCase2.getTitle() + " / Форма добавления");
        GUISelectCase.assertSelect(List.of(), false, true, true);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос, при переходе на форму добавления запроса по ссылке, 
     * сформированной с помощью api.web.add
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1', 'serviceCall$scCase2'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>С помощью api.web.add сформировать ссылку url1 вида
     *     <pre>
     *          Config.get().getWebAddress() + "operator/?anchor=add:serviceCall:employee$xxxx",
     *          где employee$xxxx - uuid employee
     *     </pre>
     * </li>
     * <li>С помощью api.web.add сформировать ссылку url2 вида
     *     <pre>
     *          Config.get().getWebAddress() + "operator/#add:serviceCall$scCase1:employee$xxxx:scCase1",
     *          где employee$xxxx - uuid employee
     *     </pre>
     * </li>
     * <li>В Основном классе создать профиль со всеми правами emplProfile: роль - Сотрудник, Группы пользователей -
     * group</li>
     * <li>У профиля emplProfile в типе scCase2 забрать права на: Добавление запроса для клиента-команды, 
     *     Добавление запроса для клиента-отдела,Добавление запроса для клиента-сотрудника</li>
     * <li>Создать отдел ou, команду team</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под cотрудником employee</li>
     * <li>Перейти на форму добавления запроса по ссылке url1</li>
     * <li>Проверка: для выбора в списке типов доступны scCase1 и scCase2 </li>
     * <li>Перейти на форму добавления запроса по ссылке url1</li>
     * <li>Проверка: для выбора в списке типов доступен только scCase1</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfAddByApiLink()
    {
        //Подготовка
        String cases = String.format("'%s', '%s'", scCase1.getFqn(), scCase2.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        String urlPtrn = Config.get().getWebAddress() + "operator/?anchor=add:%s:%s";
        String url1 = String.format(urlPtrn, SystemClass.SERVICECALL.getCode(), employee.getUuid());
        urlPtrn = Config.get().getWebAddress() + "operator/#add:%s:%s:%s";
        String url2 = String.format(urlPtrn, scCase1.getFqn(), employee.getUuid(), scCase1.getCode());

        //Действия и проверки
        GUILogon.login(employee);
        tester.goToPage(url1);
        GUISc.assertScCasesPresent(scCase1, scCase2);
        GUISc.assertScCasesAbsent(scCase3);

        tester.goToPage(url2);
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase3, scCase2);
    }

    /**
     * Тестирование того, что при создании запроса не отображаются архивные типы запросов, 
     * даже если они возвращаются скриптом фильтрации.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>Поместить тип scCase1 в архив</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1','serviceCall$scCase2','serviceCall$scCase3'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: В поле Тип объекта в выпадающем списке присутствуют типы scCase2, scCase3 и нет scCase1</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfCaseRemoved()
    {
        //Подготовка
        DSLMetaClass.archive(scCase1);

        String cases = String.format("'%s', '%s', '%s'", scCase1.getFqn(), scCase2.getFqn(), scCase3.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase2, scCase3);
        GUISc.assertScCasesAbsent(scCase1);
        DSLMetaClass.restore(scCase1);
    }

    /**
     * Тестирование того, что скрипт фильтрации типов Запроса не учитывается при регистрации запроса, 
     * если на форме добавления нет контента Выбор типа запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1','serviceCall$scCase2'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>В классе Запрос убрать с формы добавления контент Выбор типа запроса</li>
     *<li>В типе класса Отдел установить параметры запроса по умолчанию: scCase3, agreement</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Перейти на карточу oтдела ou</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Заполнить обязательные поля, Сохранить</li>
     * <li>Проверка: Создался запрос типа scCase3</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfNoSelectCaseContent()
    {
        //Подготовка
        String cases = String.format("'%s', '%s'", scCase1.getFqn(), scCase2.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        ContentForm selectCase = DSLContent.getContentByTitle(SystemClass.SERVICECALL.getCode(),
                ContentType.SELECT_SC_CASE.getTypeTitle(), MetaclassCardTab.NEWENTRYFORM.get());
        DSLContent.delete(selectCase);

        Cleaner.afterTest(() ->
        {
            DSLContent.add(selectCase);
        });

        MetaClass ouCase = DAOOuCase.create();
        ouCase.setDefaultScAgreement(agreement.getUuid());
        ouCase.setDefaultScCase(scCase3.getFqn());
        DSLMetaClass.add(ouCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase3);
        sc.setScTimeZoneUuid(SharedFixture.timeZone().getUuid());

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIBo.fillDescription(ModelUtils.createDescription());
        GUIBo.fillTimeZone(sc);
        Set<String> oldUuids = DSLBo.getUuidsByFqn(scCase3.getFqn());

        GUIForm.applyForm();

        Bo newSc = DSLBo.getNewBoModel(oldUuids, scCase3);
        Assert.assertEquals(scCase3.getFqn(), newSc.getMetaclassFqn());
    }

    /**
     * Тестирование того, что скрипт фильтрации типов класса Запрос не учитывается при регистрации запроса по почте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     *
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1', 'serviceCall$scCase2'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Создать правило обработки почты task:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def msg = message.body
     *          def metaClass = 'scCase3.getFqn'
     *          def props =  [
     *            'agreement'   : 'agreement.getUuid',
     *            'description' : msg,
     *            'timeZone' : 'SharedFixture.timeZone().getUuid',
     *            'client' : 'employee.getUuid()',
     *          ];
     *          utils.create(metaClass, props)
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Создать письмо email и обработать его задачей планировщика task</li>
     * <li>Проверка: Успешно добавлен объект класса Запрос типа scCase3</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfScRegisteredByMail()
    {
        //Подготовка
        String cases = String.format("'%s', '%s'", scCase1.getFqn(), scCase2.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        InboundMailConnection conn = DAOSchedulerTask.createInboundMailConnection(MailProtocol.POP3, true);
        DSLSchedulerTask.addInboundMailConnection(conn);

        //@formatter:off
        scriptBody = "def msg = message.body\n" 
                   + "def metaClass = '%s'\n" 
                   + "def props =  [ \n"
                   +     "'agreement' : '%s', \n" 
                   +     "'description' : msg,\n" 
                   +     "'timeZone' : '%s',\n"
                   +     "'client' : '%s',\n" 
                   + "];\n"
                   + "utils.create(metaClass, props)\n";
        //@formatter:on
        ScriptInfo scriptMail = DAOScriptInfo.createNewScriptInfo(String.format(scriptBody, scCase3.getFqn(),
                agreement.getUuid(), SharedFixture.timeZone().getUuid(), employee.getUuid()));
        SchedulerTask task = DAOSchedulerTask.createReceiveMailTask(true, conn, scriptMail);
        DSLSchedulerTask.prepareMailRuleScriptFromPath(task, scriptMail);
        DSLSchedulerTask.addReceiveMailTask(task, scriptMail);
        DSLSchedulerTask.addTask(task);

        Email email = DAOEmail.createEmailForSend();

        Set<String> oldScUuids = DSLBo.getUuidsByFqn(scCase3.getFqn());

        //Действия и проверки
        DSLSchedulerTask.processInboundMailWithMailLog(task, email);
        Bo newSc = DSLBo.getNewBoModel(oldScUuids, scCase3);
        Assert.assertNotEquals("Запрос по письму не зарегистрирован.", newSc.getUuid(), null);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос, 
     * если у пользователя нет прав добавлять запросы определенного типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1', 'serviceCall$scCase2'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Создать сотрудника employee, поместить его в группу group</li>
     * <li>В Основном классе создать профиль со всеми правами emplProfile: роль - Сотрудник, Группы пользователей -
     * group</li>
     * <li>У профиля emplProfile в типе scCase2 забрать права на: Добавление запроса для клиента-команды, 
     *     Добавление запроса для клиента-отдела,Добавление запроса для клиента-сотрудника</li>
     * <li>Создать отдел ou, команду team</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под cотрудником employee</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: для выбора в списке типов доступен только scCase1</li>
     * <li>Перейти на карточку team</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: для выбора в списке типов доступен только scCase1</li>
     * <li>Перейти на карточку ou</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: для выбора в списке типов доступен только scCase1</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringIfUserDoNotHavePermissions()
    {
        //Подготовка
        String cases = String.format("'%s', '%s'", scCase1.getFqn(), scCase2.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee, team);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee);
        SecurityProfile emplProfile = DAOSecurityProfile.create(true, group, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        DSLSecurityProfile.grantAllPermissions(emplProfile);
        DSLSecurityProfile.removeRights(scCase2, emplProfile, ScRights.ADD_TO_EMPLOYEE, ScRights.ADD_TO_OU,
                ScRights.ADD_TO_TEAM);

        DSLAgreement.addToRecipients(agreement, employee, team, ou);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);
        GUIBo.goToCard(team);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);
    }

    /**
     * Тестирование того, что скрипт фильтрации типов класса Запрос не учитывается в интерфейсе администратора
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти в класс Сотрудник</li>
     * <li>Нажать редактировать</li>
     * <li>Проверка: В параметрах запроса по умолчанию убедиться, что доступны для выбора все типы класса запрос</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInAdminInterface()
    {
        //Подготовка
        String cases = String.format("'%s'", scCase1.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Действия и проверки
        GUILogon.asNaumen();
        GUIMetaClass.openEditForm(DAOEmployeeCase.createClass());
        GUISelect.assertDisplayedByTitle(GUIXpath.Any.CASE_PROPERTY_VALUE, scCase1.getTitle(), scCase2.getTitle(),
                scCase3.getTitle());
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос, если результат зависит от user
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     *  <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>Создать двух сотрудников empl1, empl2</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = ['bool']
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          if(user?.UUID == 'empl1')
     *          {
     *              return ['serviceCall$scCase1']
     *          }
     *           return ['serviceCall$scCase2', 'serviceCall$scCase3']
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>У услуги service указать в поле Типы запросов scCase1</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником empl1</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: для выбора в списке типов доступен только scCase1</li>
     * <li>Войти под сотрудником empl2</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверка: для выбора в списке типов доступны scCase2, scCase3</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInDependencyOfEmployee()
    {
        //Подготовка
        Bo empl1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo empl2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(empl1, empl2);

        //@formatter:off
        String scriptPattern = "def ATTRS_FOR_UPDATE_ON_FORMS = []\n "
                             + "if (subject == null) { return ATTRS_FOR_UPDATE_ON_FORMS }\n "
                             + "if (user?.UUID == '%s') { return ['%s'] }\n "
                             + "return ['%s', '%s'];";
        //@formatter:on
        String scriptBody = String.format(scriptPattern, empl1.getUuid(), scCase1.getFqn(), scCase2.getFqn(),
                scCase3.getFqn());
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        DSLAgreement.addToRecipients(agreement, empl1, empl2);

        //Действия и проверки
        GUILogon.login(empl1);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);

        GUILogon.login(empl2);
        GUIButtonBar.addSC();
        GUISc.assertScCasesPresent(scCase2, scCase3);
        GUISc.assertScCasesAbsent(scCase1);
    }

    /**
     *  Тестирование работы скрипта фильтрации типов класса Запрос, 
     *  если результат зависит от других атрибутов на форме добавления запроса 
     *  https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     *  http://sd-jira.naumen.ru/browse/NSDWRK-17459
     *  <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В классе Запрос создать атрибут bool типа Логический, значение по умолчанию = нет.</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = ['bool']
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          if(subject.bool == false)
     *          {
     *              return ['serviceCall$scCase1', 'serviceCall$scCase2', 'serviceCall$scCase3']
     *          }
     *           return ['serviceCall$scCase2']
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>У услуги service указать в поле Типы запросов scCase1</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверки: На форме атрибут bool = false, для выбора в списке типов доступен только scCase1, scCase2,
     * scCase3</li>
     * <li>Изменить значение bool = true</li>
     * <li>Проверки: На форме атрибут bool = true, для выбора в списке типов доступен только scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInDependencyOfOtherAttributes()
    {
        //Подготовка
        Attribute bool = DAOAttribute.createBool(SystemClass.SERVICECALL.getCode());
        bool.setDefaultValue(Boolean.FALSE.toString());
        bool.setEditPresentation(BooleanType.EDIT_CHECKBOX);
        DSLAttribute.add(bool);

        DSLGroupAttr.edit(DAOGroupAttr.createSystem(DAOScCase.createClass()), new Attribute[] { bool },
                new Attribute[] {});

        //@formatter:off
        String scriptPattern = "def ATTRS_FOR_UPDATE_ON_FORMS = ['%s']\n "
                             + "if (subject == null) { return ATTRS_FOR_UPDATE_ON_FORMS }\n "
                             + "if (subject.%s == false) { return ['%s', '%s', '%s'] }\n "
                             + "return ['%s'];";
        //@formatter:on
        String scriptBody = String.format(scriptPattern, bool.getCode(), bool.getCode(), scCase1.getFqn(),
                scCase2.getFqn(), scCase3.getFqn(), scCase2.getFqn());
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUITester.assertCheckboxState(GUIXpath.Any.ANY_VALUE_INPUT_CONTAINS, false, bool.getCode());
        GUISc.assertScCasesPresent(scCase1, scCase2, scCase3);
        tester.setCheckbox(GUIXpath.Any.ANY_VALUE_INPUT_CONTAINS, true, bool.getCode());
        GUISc.assertScCasesPresent(scCase2);
        GUISc.assertScCasesAbsent(scCase1, scCase3);
    }

    /**
     * Тестирование того, что скрипт фильтрации типов класса Запрос не учитывается на формах быстрой и сложной
     * фильтраций
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDWRK-17459
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>На карточку Компании вывести сложный Список объектов (класс - Запрос, группа - Системные атрибуты)</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Перейти на карточку Компании</li>
     * <li>В списке объектов класса Запрос нажать на кнопку Фильтрация</li>
     * <li>На форме фильтрации выбрать: Тип объекта</li>
     * <li>Проверка: в выпадающем списке доступны для выбора все типы класса Запрос scCase2, scCase3 и scCase1</li>
     * <li>Открыть форму быстрой фильтрации по атрибуту Тип объекта, установить Фильтрация по условию</li>
     * <li>Проверка: в выпадающем списке доступны для выбора все типы класса Запрос scCase2, scCase3 и scCase1</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringOnFiltrationForm()
    {
        //Подготовка
        String cases = String.format("'%s'", scCase1.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        ContentForm scList = DAOContentCard.createObjectAdvList(SystemClass.ROOT.getCode(), DAOScCase.createClass());
        DSLContent.add(scList);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        scList.advlist().toolPanel().clickFiltering();
        scList.advlist().toolPanel().filtering().addAttr(SysAttribute.metaClass(DAOScCase.createClass()), 1, 1);
        scList.advlist().toolPanel().filtering().asserts().selectValue(false, false, scCase1.getTitle(),
                scCase2.getTitle(), scCase3.getTitle());

        scList.advlist().content().clickFastFilterButton(SysAttribute.metaClass(DAOScCase.createClass()));
        GUIAdvListFastFilterForm.assertConditionInSelectIsPresent(true, scCase1.getTitle(), scCase2.getTitle(),
                scCase3.getTitle());
    }

    /**
     * Тестирование пересечения системной логики (доступные типы при выборе услуги) 
     * и результатов работы скрипта фильтрации типов класса Запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-5694
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Общая подготовка {@link #prepareClass()} </li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = []
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          return ['serviceCall$scCase1','serviceCall$scCase2'];
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     *<li>У услуги service указать в поле Типы запросов scCase1</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>В поле Соглашение/Услуга выбрать услугу service</li>
     * <li>Проверка: В поле Тип объекта в выпадающем списке присутствует только тип scCase1</li>
     * <li>У услуги service указать в поле Типы запросов scCase1, scCase2, scCase3</li>
     * <li>Обновить страницу</li>
     * <li>В поле Соглашение/Услуга выбрать услугу service</li>
     * <li>Проверка: В поле Тип объекта в выпадающем списке присутствуют типы scCase1, scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringWithAvailableCasesForServiceSelection()
    {
        //Подготовка
        String cases = String.format("'%s', '%s'", scCase1.getFqn(), scCase2.getFqn());
        String scriptBody = String.format(CASES_FILTRATION_SCRIPT, cases);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        DSLSlmService.deleteAllScCases(service);
        DSLSlmService.addScCases(service, scCase1);
        DSLScParams.setOrderingSettings(OrderScFields.AGREEMENTSERVICE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUISc.selectAgrServiceField(service.getTitle());
        GUISc.assertScCasesPresent(scCase1);
        GUISc.assertScCasesAbsent(scCase2, scCase3);

        DSLSlmService.addScCases(service, scCase1, scCase2, scCase3);

        tester.refresh();

        GUISc.selectAgrServiceField(service.getTitle());
        GUISc.assertScCasesPresent(scCase1, scCase2);
        GUISc.assertScCasesAbsent(scCase3);
    }

    /**
     * Тестирование скрипта фильтрации типов запроса на форме добавления.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-5694
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase, тип сотрудника employeeCase, тип услуги serviceCase</li>
     * <li>Создать соглашение agreement и услугу service типа serviceCase</li>
     * <li>Связать соглашение agreement и услугу service</li>
     * <li>Добавить связь для услуги service с типом запроса scCase</li>
     * <li>Указать параметры запроса по умолчанию для employeeCase: Соглашение/Услуга: agreement, Тип объекта:
     * scCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавить сотрудника employee в список получателей соглашения agreement</li>
     * <li>Добавить скрипт фильтрации типов запроса:"return []"</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать на кнопку добавления запроса и проверить, что форма добавления запроса открылась без ошибок</li>
     * <li>Проверить, что поле "Тип запроса" не заполнено</li>
     * <li>Выбрать в поле "Соглашение/Услуга" значение agreement</li>
     * <li>Проверить, что поле "Тип запроса" не заполнено</li>
     * <li>Выйти из системы</li>
     * <li>Установить значения параметров запросов по умолчанию</li>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать на кнопку добавления запроса и проверить, что форма добавления запроса открылась без ошибок</li>
     * <li>Проверить, что поле "Соглашение/Услуга" заполнено значением agreement</li>
     * <li>Проверить, что поле "Тип запроса" заполнено значением scCase</li>
     * </ol>
     */
    @Test
    public void testScParamCasesScriptFilteringOnAddForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(scCase, serviceCase, employeeCase);
        Bo agreement = SharedFixture.agreement();
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase);
        DSLMetaClass.setScParams(employeeCase, agreement, null, scCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        ScriptInfo casesFiltrationScript = DAOScriptInfo.createNewScriptInfo("return []");
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, null, null, casesFiltrationScript);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIError.waitError(2);
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUILogon.logout();
        DSLScParams.setDefaultScParameters();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIError.waitError(2);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase.getTitle());
    }

    /**
     * Тестирование скрипта фильтрации типов запроса на форме смены типа.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-5694
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запроса scCase, scCase2; тип сотрудника employeeCase; тип услуги serviceCase</li>
     * <li>Создать соглашение agreement и услугу service типа serviceCase</li>
     * <li>Связать соглашение agreement и услугу service</li>
     * <li>Добавить связь для услуги service с типами запроса scCase, scCase2</li>
     * <li>Указать параметры запроса по умолчанию для employeeCase: Соглашение/Услуга: agreement, Тип объекта:
     * scCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавить сотрудника employee в список получателей соглашения agreement</li>
     * <li>Создать запрос sc (тип: scCase, сотрудник: employee, соглашение: agreement, часовой пояс: timeZoneItem)</li>
     * <li>Добавить скрипт фильтрации типов запроса:"return []"</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку sc</li>
     * <li>Проверить, что запрос sc имеет тип scCase</li>
     * <li>Нажать на кнопку "Изменить тип"</li>
     * <li>Проверить, что в поле "Тип запроса" отсутствуют значения доступные для выбора</li>
     * <li>Нажать на кнопку "Отмена" на форме смены типа и выйти из системы</li>
     * <li>Установить значения параметров запросов по умолчанию</li>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку sc</li>
     * <li>Нажать на кнопку "Изменить тип"</li>
     * <li>Проверить, что в поле "Тип запроса" присутствует значение scCase2. Выбрать его и нажать на кнопку
     * "Сохранить".</li>
     * <li>Проверить, что запрос sc имеет тип scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesScriptFilteringOnTypeChangeForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(scCase, scCase2, serviceCase, employeeCase);
        Bo agreement = SharedFixture.agreement();
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase, scCase2);
        DSLMetaClass.setScParams(employeeCase, agreement, null, scCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);

        ScriptInfo casesFiltrationScript = DAOScriptInfo.createNewScriptInfo("return []");
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, null, null, casesFiltrationScript);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIBo.assertThatBoCaseCard(scCase);
        GUIButtonBar.changeCase();
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.assertElementsCount(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, 0);
        GUIForm.cancelDialog();
        GUILogon.logout();
        DSLScParams.setDefaultScParameters();
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeCase();
        GUISelect.assertDisplayed(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUIForm.applyModalForm();
        GUIBo.assertThatBoCaseCard(scCase2);
    }

    /**
     * Тестирование скрипта фильтрации типов запросов на форме смены привязки.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-5694
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запроса scCase, scCase2; тип сотрудника employeeCase; тип услуги serviceCase</li>
     * <li>Создать соглашение agreement и услугу service типа serviceCase</li>
     * <li>Связать соглашение agreement и услугу service</li>
     * <li>Добавить связь для услуги service с типами запроса scCase, scCase2</li>
     * <li>Указать параметры запроса по умолчанию для employeeCase: Соглашение/Услуга: agreement, Тип объекта:
     * scCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавить сотрудника employee в список получателей соглашения agreement</li>
     * <li>Создать запрос sc (тип: scCase, сотрудник: employee, соглашение: agreement, часовой пояс: timeZoneItem)</li>
     * <li>Добавить скрипт фильтрации типов запроса:"return []"</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку sc</li>
     * <li>Проверить, что запрос sc имеет тип scCase</li>
     * <li>Нажать на кнопку "Изменить привязку"</li>
     * <li>Проверить, что в поле "Тип запроса" отсутствуют значения доступные для выбора</li>
     * <li>Нажать на кнопку "Отмена" на форме смены привязки и выйти из системы</li>
     * <li>Установить значения параметров запросов по умолчанию</li>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку sc</li>
     * <li>Нажать на кнопку "Изменить привязку"</li>
     * <li>Проверить, что в поле "Тип запроса" присутствует значение scCase2. Выбрать его и нажать на кнопку
     * "Сохранить".</li>
     * <li>Проверить, что запрос sc имеет тип scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesScriptFilterOnAssociationChangeForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(scCase, scCase2, serviceCase, employeeCase);
        Bo agreement = SharedFixture.agreement();
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase, scCase2);
        DSLMetaClass.setScParams(employeeCase, agreement, null, scCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);

        ScriptInfo casesFiltrationScript = DAOScriptInfo.createNewScriptInfo("return []");
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, null, null, casesFiltrationScript);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIBo.assertThatBoCaseCard(scCase);
        GUIButtonBar.changeAssociation();
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.assertElementsCount(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, 0);
        GUIForm.cancelDialog();
        GUILogon.logout();
        DSLScParams.setDefaultScParameters();
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISelect.assertDisplayed(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUIForm.applyModalForm();
        GUIBo.assertThatBoCaseCard(scCase2);
    }

    /**
     * Тестирование корректной работы скрипта фильтрации услуги, если он возвращает UUID-ы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * http://sd-jira.naumen.ru/browse/NSDPRD-3110
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать тип услуги serviceCase1..2</li>
     * <li>Создать service1..2 (по одной каждого типа)</li>
     * <li>Связать service1..2  с соглашением по умолчанию</li>
     * <li>На форме добавления класса Запрос создать контент Выбор типа запроса scCaseSelect</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Фильтрация услуг при редактировании - да</li>
     * <li>Скрипт: </li>
     * <pre>
     *  if (subject == null) return [] as List; 
     *  def permittedFqns = [api.types.newClassFqn('slmService$slmService1')] as List;
     *  return utils.find('slmService',['metaClass':permittedFqns]).UUID; 
     * </pre>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employee</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect проверяем что присутствует только одна услуга service1</li>
     * </ol>
     */
    @Test
    public void testScParamServiceScriptFilterReturnUUIDs()
    {
        //Подготовка
        //Настраиваем форму добавления в классе Запрос
        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass serviceCase1 = DAOServiceCase.create();
        MetaClass serviceCase2 = DAOServiceCase.create();
        DSLMetaClass.add(employeeCase, scCase, serviceCase1, serviceCase2);

        Bo agreement = SharedFixture.agreement();
        Bo service1 = DAOService.create(serviceCase1);
        Bo service2 = DAOService.create(serviceCase2);
        DSLBo.add(service1, service2);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        DSLAgreement.addServices(agreement, service1, service2);

        DSLSlmService.addScCases(service1, scCase);
        DSLSlmService.addScCases(service2, scCase);

        String script =
                "if (subject == null) return [] as List; return utils.find('slmService',['metaClass': api.types"
                + ".newClassFqn('"
                + serviceCase1.getFqn() + "')]).UUID;";
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(script);
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.LIST, null, filterScript);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        //Проверки
        String selectXPath = String.format(
                GUIXpath.Div.ID_PATTERN + GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                scCaseSelect.getXpathId());
        GUISelect.assertDisplayed(selectXPath, agreement.getUuid() + ":" + service1.getUuid());
        GUISelect.assertNotDisplayed(selectXPath, agreement.getUuid() + ":" + service2.getUuid(), service2.getTitle());
    }
}
