package ru.naumen.selenium.cases.operator.process;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.OrderScFields;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование добавления запроса при параметре "Выбирать сначала" = "Тип запроса"
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
 * <AUTHOR>
 * @since 28.08.2014
 */
public class ScFirstParamRegistration1Test extends AbstractTestCase
{
    private final static OrderScFields CASE = OrderScFields.CASE;
    private final static OrderScFields AGREEMENT = OrderScFields.AGREEMENTSERVICE;
    private static Bo team, empl, agree0, agree1, ou, service1, service0;
    private static MetaClass scCase0, scCase1, serviceCase0, serviceCase1, agreementCase0, agreementCase1, ouCase,
            teamCase, emplCase, scClass;
    private static CatalogItem serviceTimeItem, timeZoneItem;
    private static MenuItem addButton;
    private static ContentForm scClientSelect, teamList, emplList, ouList;

    private static final String FIRST_VIEW_AGREEMENT_THEN_CASE = "Первым должно отображаться поле Соглашение/услуга, "
                                                                 + "затем поле Тип объекта";

    /**
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элементы соответвующих справочников: serviceTimeItem - класс обслуживания,
     * timeZoneItem - часовой пояс, priorityItem - приоритет</li>
     * <li>Создать два типа запроса scCase0 (со значениями по умолчанию для атрибутов timeZoneAttr0, descriptionAttr0), 
     * scCase1 (со значениями по умолчанию для атрибутов timeZoneAttr1, descriptionAttr1)</li>
     * <li>Создать соглашения agree0,agree1 типов agreementCase0, agreementCase1 соответственно</li>
     * <li>Создать услуги service0, service1 типов serviceCase0, serviceCase1 соответвенно; и связать 
     * их с agree0 и agree1</li>
     * <li>Добавить scCase0,scCase1 в типы запросов для service0 и service1</li>
     * <li>Добавить: тип отдела ouCase, тип сотрудника emplCase, тип команды teamCase</li>
     * <li>Создать объекты: отдел ou типа ouCase, сотрудника empl типа emplCase в отделе ou, команду team
     * типа teamCase. Сделать их получателями соглашений agree0,agree1</li>
     * <li>Включить верхнее меню, создать в нем addButton - кнопку добавления объектов класса Запрос, включить её</li>
     * <li>На карточку Компании добавить контент teamList Список объектов (Класс = Команда, Представление = Сложный
     * список)</li>
     * <li>На карточку Компании добавить контент ouList Список объектов (Класс = Отдел, Представление = Сложный
     * список)</li>
     * <li>На карточку Компании добавить контент employeeList Список объектов (Класс = Сотрудник, Представление =
     * Сложный список)</li>
     * <li>На форму добавления класса Запрос добавить контент scClientSelect Выбор типа запроса</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        serviceTimeItem = SharedFixture.serviceTime();
        timeZoneItem = SharedFixture.timeZone();

        agreementCase0 = DAOAgreementCase.create();
        agreementCase1 = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase0, agreementCase1);

        scClass = DAOScCase.createClass();
        scCase0 = DAOScCase.create();
        scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase0, scCase1);

        Attribute timeZoneAttr0 = SysAttribute.timeZone(scCase0);
        timeZoneAttr0.setDefaultValue(timeZoneItem.getUuid());
        Attribute descriptionAttr0 = SysAttribute.description(scCase0);
        descriptionAttr0.setDefaultValue(ModelUtils.createDescription());
        Attribute timeZoneAttr1 = SysAttribute.timeZone(scCase1);
        timeZoneAttr1.setDefaultValue(timeZoneItem.getUuid());
        Attribute descriptionAttr1 = SysAttribute.description(scCase1);
        descriptionAttr1.setDefaultValue(ModelUtils.createDescription());
        DSLAttribute.edit(timeZoneAttr0, descriptionAttr0, timeZoneAttr1, descriptionAttr1);

        serviceCase0 = DAOServiceCase.create();
        serviceCase1 = DAOServiceCase.create();
        DSLMetaClass.add(serviceCase0, serviceCase1);
        agree0 = DAOAgreement.createWithRules(agreementCase0, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        agree1 = DAOAgreement.createWithRules(agreementCase1, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        service0 = DAOService.create(serviceCase0);
        service1 = DAOService.create(serviceCase1);
        DSLBo.add(agree0, agree1, service0, service1);

        DSLAgreement.addServices(agree0, service0, service1);
        DSLAgreement.addServices(agree1, service1, service0);
        DSLSlmService.addScCases(service0, scCase0);
        DSLSlmService.addScCases(service1, scCase0);
        DSLSlmService.addScCases(service0, scCase1);
        DSLSlmService.addScCases(service1, scCase1);

        ouCase = DAOOuCase.create();
        emplCase = DAOEmployeeCase.create();
        teamCase = DAOTeamCase.create();
        DSLMetaClass.add(ouCase, emplCase, teamCase);

        ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        empl = DAOEmployee.create(emplCase, ou, false, false);
        team = DAOTeam.create(teamCase, "team");
        DSLBo.add(empl, team);
        DSLTeam.addEmployees(team, empl);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLAgreement.addRecipients(empl, agree1, agree0);
        DSLAgreement.addRecipients(ou, agree1, agree0);
        DSLAgreement.addRecipients(team, agree1, agree0);

        DSLNavSettings.editVisibilitySettings(true, true);
        addButton = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(addButton);

        scClientSelect = DAOContentAddForm.createSelectClient(scClass);
        teamList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), DAOTeamCase.createClass());
        emplList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), DAOEmployeeCase.createClass());
        ouList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), DAOOuCase.createClass());
        DSLContent.add(scClientSelect, teamList, ouList, emplList);
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть 
     * привязка по умолчанию = Тип запроса/Соглашение, значение поля "Соглашение/Услуга" = "Соглашение или услуга", 
     * а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>В типе emplCase настроить привязку запроса по умолчанию = "agree0,scCase0"
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку empl
     * <li>Нажать кнопку добавления запроса
     * <li>Проверить, что на форме поле "Тип объекта" первое и в нем выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" первое и в нем выбрано agree0
     * <li>Нажать "Сохранить"
     * <li>Запрос создался с привязкой к empl, agree0, тип запроса scCase0 
     * </ol>
     */
    @Test
    public void testAddScFromEmployeeWithDefScAgr()
    {
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.BOTH);
        DSLMetaClass.setScParams(emplCase, agree0, null, scCase0);
        afterTest(emplCase, null, null, null);
        Bo sc = DAOSc.create(scCase0);

        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIButtonBar.addSC();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, null);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента нет привязки по умолчанию, 
     * значение поля "Соглашение/Услуга" = "Услуга", а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Общая подготовка
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <li>Изменить параметр "Соглашение/Услуга" = Услуга
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку ou
     * <li>Нажать кнопку добавления запроса
     * <li>Проверить, что на форме поле "Тип объекта" первое и в нем выбрано [не указано]
     * <li>Проверить, что на форме поле "Соглашение/Услуга" второе и оно пустое [нет элементов]
     * <li>В поле "Тип объекта" выбрать scCase1
     * <li>Проверить, что в поле "Соглашение/Услуга" выбрано [не указано]
     * <li>В поле "Соглашение/Услуга" выбрать agree1/service1
     * <li>Нажать "Сохранить"
     * <li>Запрос создался с привязкой к ou, agree1/service1, тип запроса scCase1 
     * </ol>
     */
    @Test
    public void testAddScFromOuNoDefSc()
    {
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.create(scCase1);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUISc.assertEmptyScCase(true);
        GUISc.assertEmptyAgreement(false);
        GUISc.selectScCase(scCase1);
        GUISc.assertEmptyAgreement(true);
        GUISc.selectAssociation(agree1, service1);
        GUIForm.applyForm();

        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree1, service1);
        DSLBo.assertType(sc, scCase1.getFqn());

    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть 
     * привязка по умолчанию = Тип запроса/Услуга, значение поля "Соглашение/Услуга" = "Услуга", 
     * а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>В типе ouCase настроить привязку запроса по умолчанию = "agree0/service0/scCase0"
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <li>Изменить параметр "Соглашение/Услуга" = услуга
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку ou
     * <li>Нажать кнопку добавления запроса
     * <li>Проверить, что на форме поле "Тип объекта" первое и в нем выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" первое и в нем выбрано service0
     * <li>Нажать "Сохранить"
     * <li>Запрос создался с привязкой к ou, agree0/service0, тип запроса scCase0 
     * </ol>
     */
    @Test
    public void testAddScFromOuWithDefScSlm()
    {
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.SERVICE);
        DSLMetaClass.setScParams(ouCase, agree0, service0, scCase0);
        afterTest(ouCase, null, null, null);
        Bo sc = DAOSc.create(scCase0);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(service0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть 
     * привязка по умолчанию = Услуга/Тип запроса, значение поля "Соглашение/Услуга" = "Услуга", 
     * а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>В типе ouCase настроить привязку запроса по умолчанию = "agree0/service0/scCase0"
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <li>Изменить параметр "Соглашение/Услуга" = услуга
     * <br>
     * <b>Действия и проверки</b>
     * <li>Перейти на карточку ou
     * <li>Нажать кнопку добавления запроса
     * <li>Проверить, что на форме поле "Тип объекта" первое и в нем выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" первое и в нем выбрано service0
     * <li>Нажать "Сохранить"
     * <li>Запрос создался с привязкой к ou, agree0/service0, тип запроса scCase0 
     * </ol>
     */
    @Test
    public void testAddScFromOuWithDefSlmSc()
    {
        DSLMetaClass.setScParams(ouCase, agree0, service0, scCase0);
        afterTest(ouCase, null, null, null);
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.create(scCase0);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(service0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть 
     * привязка по умолчанию = Соглашение/Тип запроса, значение поля "Соглашение/Услуга" = "Соглашение или услуга", 
     * а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Общая подготовка
     * <li>В типе teamCase настроить привязку запроса по умолчанию = "agree0,scCase0"
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку team
     * <li>Нажать "Добавить запрос"
     * <li>Проверить, что на форме поле "Тип объекта" первое и в нем выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" первое и в нем выбрано agree0
     * <li>Нажать "Сохранить"
     * <li>Запрос создался с привязкой к team, agree0, тип запроса scCase0 
     * </ol>
     */
    @Test
    public void testAddScFromTeamWithDefAgrSc()
    {
        DSLScParams.editAgrService(AgrService.BOTH);
        DSLMetaClass.setScParams(teamCase, agree0, null, scCase0);
        afterTest(teamCase, null, null, null);
        DSLScParams.setOrderingSettings(CASE);
        Bo sc = DAOSc.create(scCase0);

        GUILogon.asTester();
        GUIBo.goToCard(team);
        GUIButtonBar.addSC();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertSlmAndAgreement(sc, agree0, null);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию 
     * = Тип запроса/Соглашение, значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" 
     * = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе emplCase настроить привязку запроса по умолчанию = scCase0/agree0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Из карточки empl открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к empl, agree0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInEmplCardWithDefScCaseAgreeWhenFirstParamAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(emplCase, agree0, null, scCase0);
        afterTest(emplCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertAgreement(agree0);
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Соглашение, значение поля "Соглашение/Услуга" = "Соглашение", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе emplCase настроить привязку запроса по умолчанию = scCase0/agree0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и "Соглашение/Услуга" = "Соглашение"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки empl открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем  scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к empl, agree0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInEmplCardWithDefScCaseAgreeWhenFirstParamAgreementAndAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(emplCase, agree0, null, scCase0);
        afterTest(emplCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertAgreement(agree0);
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из списка контрагентов, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Услуга, значение поля "Соглашение/Услуга" = "Услуга", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе emplCase настроить привязку запроса по умолчанию = scCase0/agree0/service0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и "Соглашение/Услуга" = "Услуга"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из emplList открыть форму добавления запроса для empl</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0/service0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем выбрано scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к empl, agree0/service0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInEmplListWithDefScCaseAgreeServiceWhenFirstParamAgreementAndService()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(emplCase, agree0, service0, scCase0);
        afterTest(emplCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        emplList.advlist().mass().selectElements(empl);
        emplList.advlist().mass().clickOperation(MassOperation.ADD_SC);
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agree0.getUuid() + ":" + service0.getUuid());
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из списка контрагентов, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Соглашение, значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" = 
     * "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе emplCase настроить привязку запроса по умолчанию = scCase0/agree0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из emplList открыть форму добавления запроса для empl</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем выбрано scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к empl, agree0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInEmplListWithDefScCaseAgreeWhenFirstParamAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(emplCase, agree0, null, scCase0);
        afterTest(emplCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        emplList.advlist().mass().selectElements(empl);
        emplList.advlist().mass().clickOperation(MassOperation.ADD_SC);
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertAgreement(agree0);
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из списка контрагентов, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Соглашение, значение поля "Соглашение/Услуга" = "Соглашение", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе emplCase настроить привязку запроса по умолчанию = scCase0/agree0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и "Соглашение/Услуга" = "Соглашение"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из emplList открыть форму добавления запроса для empl</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем выбрано scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к empl, agree0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInEmplListWithDefScCaseAgreeWhenFirstParamAgreementAndAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(emplCase, agree0, null, scCase0);
        afterTest(emplCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        emplList.advlist().mass().selectElements(empl);
        emplList.advlist().mass().clickOperation(MassOperation.ADD_SC);
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertAgreement(agree0);
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию
     * = Тип запроса/Услуга, значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" = 
     * "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе ouCase настроить привязку запроса по умолчанию = scCase0/agree0/service0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки ou открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0/service0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к ou, agree0/service0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInOuCardWithDefScCaseAgreeServiceWhenFirstParamAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(ouCase, agree0, service0, scCase0);
        afterTest(ouCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agree0.getUuid() + ":" + service0.getUuid());
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Услуга, значение поля "Соглашение/Услуга" = "Услуга", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе ouCase настроить привязку запроса по умолчанию = scCase0/agree0/service0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и  "Соглашение/Услуга" = "Услуга"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки ou открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0/service0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к ou, agree0/service0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInOuCardWithDefScCaseAgreeServiceWhenFirstParamAgreementAndService()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(ouCase, agree0, service0, scCase0);
        afterTest(ouCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agree0.getUuid() + ":" + service0.getUuid());
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из списка контрагентов, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса/Услуга, значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" = 
     * "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе ouCase настроить привязку запроса по умолчанию = scCase0/agree0/service0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из ouList открыть форму добавления запроса для ou</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано agree0/service0</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем выбрано scCase0</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к ou, agree0/service0, тип запроса = scCase0</li>
     * </ol>
     */
    @Test
    public void testAddScInOuListWithDefScCaseAgreeServiceWhenFirstParamAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(ouCase, agree0, service0, scCase0);
        afterTest(ouCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        Bo sc = DAOSc.create(scCase0);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        ouList.advlist().mass().selectElements(ou);
        ouList.advlist().mass().clickOperation(MassOperation.ADD_SC);
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agree0.getUuid() + ":" + service0.getUuid());
        GUISc.assertScCase(scCase0);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию 
     * = Тип запроса, значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" 
     * = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе teamCase настроить привязку запроса по умолчанию = scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки team открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано [не указано]</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем [нет элементов]</li>
     * <li>Выбрать в поле "Соглашение/Услуга" = agree0/service0</li>
     * <li>>Выбрать в поле "Тип запроса" = scCase1</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к team, agree0/service0, тип запроса = scCase1</li>
     * </ol>
     */
    @Test
    public void testAddScInTeamCardWithDefScCaseWhenFirstParamAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(teamCase, null, null, scCase0);
        afterTest(teamCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        Bo sc = DAOSc.create(scCase1);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(team);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertEmptyAgreement(true);
        GUISc.assertEmptyScCase(false);
        GUISc.selectAssociation(agree0, service0);
        GUISc.selectScCase(scCase1);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, null, team);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase1.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса, значение поля "Соглашение/Услуга" = "Соглашение", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе teamCase настроить привязку запроса по умолчанию = scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и "Соглашение/Услуга" = "Соглашение"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки team открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано [не указано]</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем  [нет элементов]</li>
     * <li>Выбрать в поле "Соглашение/Услуга" = agree0</li>
     * <li>Выбрать в поле "Тип запроса" = scCase1</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к  team, agree0, тип запроса = scCase1</li>
     * </ol>
     */
    @Test
    public void testAddScInTeamCardWithDefScCaseWhenFirstParamAgreementAndAgreement()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(teamCase, null, null, scCase0);
        afterTest(teamCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.AGREEMENT);
        Bo sc = DAOSc.create(scCase1);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(team);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertEmptyAgreement(true);
        GUISc.assertEmptyScCase(false);
        GUISc.selectAssociation(agree0, null);
        GUISc.selectScCase(scCase1);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, null, team);
        DSLSc.assertAgreement(sc, agree0);
        DSLBo.assertType(sc, scCase1.getFqn());
    }

    /**
     * Тестирование добавления запроса из карточки контрагента, если в типе контрагента есть привязка по умолчанию = Тип
     * запроса, значение поля "Соглашение/Услуга" = "Услуга", а "Выбирать сначала" = "Соглашение/услугу"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>В типе teamCase настроить привязку запроса по умолчанию = scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/услугу" и "Соглашение/Услуга" = "Услуга"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Из карточки team открыть форму добавления запроса</li>
     * <li>В контенте "Выбор типа запроса" поле "Соглашение/Услуга" первое и в нем выбрано [не указано]</li>
     * <li>В контенте "Выбор типа запроса" поле "Тип объекта" второе и в нем [нет элементов]</li>
     * <li>Выбрать в поле "Соглашение/Услуга" = agree1/service1</li>
     * <li>>В поле "Тип объекта" появилось значение [не указано], выбираем scCase1</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Запрос создался с привязкой к team, agree1/service1, тип запроса = scCase1</li>
     * </ol>
     */
    @Test
    public void testAddScInTeamCardWithDefScCaseWhenFirstParamAgreementAndService()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(CASE);
        DSLMetaClass.setScParams(teamCase, null, null, scCase0);
        afterTest(teamCase, null, null, null);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.create(scCase1);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(team);
        GUIButtonBar.addSC();
        GUITester.assertLocation(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                GUIXpath.InputComplex.CASE_PROPERTY_VALUE,
                FIRST_VIEW_AGREEMENT_THEN_CASE);
        GUISc.assertEmptyAgreement(true);
        GUISc.assertEmptyScCase(false);
        GUISc.selectAssociation(agree1, service1);
        GUISc.assertEmptyScCase(true);
        GUISc.selectScCase(scCase1);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);
        DSLSc.assertClient(sc, null, null, team);
        DSLSc.assertSlmAndAgreement(sc, agree1, service1);
        DSLBo.assertType(sc, scCase1.getFqn());
    }

    /**
     * Установить значение "Соглашение/Услуга" для "Параметры запроса по умолчанию" после выполнения теста
     * @param metaClass метакласc для установки значения
     * @param agreement устанавливаемое значение соглашения (может быть null)
     * @param service устанавливаемое значение услуги (может быть null)
     * @param scCase тип запроса по умолчанию (может быть null)
     */
    private void afterTest(final MetaClass metaClass, final Bo agreement, final Bo service, final MetaClass scCase)
    {
        Cleaner.afterTest(new Runnable()
        {
            @Override
            public void run()
            {
                DSLMetaClass.setScParams(metaClass, agreement, service, scCase);
            }
        });
    }

}
