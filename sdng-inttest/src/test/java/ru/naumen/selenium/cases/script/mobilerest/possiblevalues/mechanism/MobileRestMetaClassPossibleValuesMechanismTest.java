package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.LIST;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.MetaClassType.EDIT_TREE;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута "Тип объекта" на формах через мобильное API.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class MobileRestMetaClassPossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userCase, userCaseWithTree, userCase3, userCase4;
    private static MetaClass[] userCases;
    private static Attribute metaClassAttr;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Подготовка.</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтипы userCase, userCaseWithTree, userCase3</li>
     * <li>Создать в типе userCase3 подтип userCase4</li>
     * <li>Создать в классе userClass дополнительно ещё 20 типов userCases для тестирования пагинации</li>
     * <li>Изменить представление атрибута "Тип объекта" в типе userCaseWithTree на "Дерево выбора"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут "Тип объекта"</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        userCaseWithTree = DAOUserCase.create(userClass);
        userCase3 = DAOUserCase.create(userClass);
        DAOMetaClass.appendTitlePrefixes(0, 24, userCase, userCaseWithTree, userCase3);
        DSLMetainfo.add(userClass, userCase, userCaseWithTree, userCase3);

        userCase4 = DAOUserCase.create(userCase3);
        DAOMetaClass.appendTitlePrefixes(3, 24, userCase4);
        DSLMetainfo.add(userCase4);

        int expectedCount = 20;
        userCases = new MetaClass[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            userCases[i] = DAOUserCase.create(userClass);
        }
        DAOMetaClass.appendTitlePrefixes(4, 24, userCases);
        DSLMetaClass.add(userCases);

        metaClassAttr = SysAttribute.metaClass(userClass);

        addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(Stream.concat(
                Stream.of(userCase, userCaseWithTree, userCase3, userCase4),
                Stream.of(userCases)
        ).toArray(MetaClass[]::new));
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, metaClassAttr);

        Attribute metaClassAttr = SysAttribute.metaClass(userCaseWithTree);
        metaClassAttr.setEditPresentation(EDIT_TREE);
        DSLAttribute.edit(metaClassAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения возможных значений атрибута "Тип объекта" с представлением "Плоский список".
     * До версии 15 должны возвращаться все возможные значения атрибута, в силу того что на клиентах ранее не
     * поддерживалась пагинация.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версий v13.1:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из userCase, userCaseWithTree, userCase3,
     * userCase4 и всех объектов userCases</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из userCase, userCaseWithTree, userCase3,
     * userCase4 и первых 16 объектов из userCases</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут catalogItemAttr имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут catalogItemAttr имеет тип выбора - список</li>
     * </ol>
     */
    @Test
    public void testMetaClass()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr)
                .assertValues(
                        list(
                                element(userCase),
                                element(userCaseWithTree),
                                element(userCase3),
                                element(userCase4)
                        ).with(
                                elements(userCases)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, metaClassAttr, V15)
                .assertValues(
                        list(
                                element(userCase),
                                element(userCaseWithTree),
                                element(userCase3),
                                element(userCase4)
                        ).with(
                                elements(Arrays.copyOf(userCases, 16))
                        )
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(metaClassAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(metaClassAttr)
                .hasEditPresentationSelectType(LIST);
    }

    /**
     * Тестирование получения возможных значений атрибута "Тип объекта" с представлением "Дерево выбора".
     * До версии 15 мобильного API должен возвращаться список, после - дерево.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCaseWithTree</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userCase, userCaseWithTree и userCase3,
     * userCase4 и всех объектов userCases</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, который состоит из userCase, userCaseWithTree и
     * userCase3 и первых 17 объектов из userCases, у userCase3 есть дочерние элементы, уровень дерева возвращён не
     * полностью = true</li>
     * <li>Выполнить повторное получение возможных значений, дополнительно передав родителя - userCase3</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, состоящей из userCase4</li>
     * <li>Получить форму для версии 15:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCaseWithTree</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут "Тип объекта" имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testMetaClassWithTreePresentation()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCaseWithTree)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr)
                .assertValues(
                        list(
                                element(userCase),
                                element(userCaseWithTree),
                                element(userCase3),
                                element(userCase4)
                        ).with(
                                elements(userCases)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, metaClassAttr, V15).assertValues(
                tree(
                        treeElement(userCase),
                        treeElement(userCaseWithTree),
                        treeElement(userCase3).leaf(false)
                ).with(
                        treeElements(Arrays.copyOf(userCases, 17))
                ).hasMore(true)
        );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setParent(userCase3);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, metaClassAttr, V15).assertValues(
                treeElement(userCase4)
        );

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(metaClassAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута "Тип объекта".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userCase,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase4,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий только из userCase4</li>
     * </ol>
     */
    @Test
    public void testMetaClassWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userCase4.getTitle());

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr)
                .assertValues(
                        element(userCase4)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута "Тип объекта" с представлением "Дерево выбора".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userCase4,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCaseWithTree</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, состоящей из userCase4 вложенного в userCase3</li>
     * </ol>
     */
    @Test
    public void testMetaClassWithTreePresentationWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCaseWithTree)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams
                .create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userCase4.getTitle());

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr, V15)
                .assertValues(
                        treeSearch(
                                treeElement(userCase3).children(
                                        treeElement(userCase4)
                                )
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование не возможности получения возможных значений с использованием пагинации для атрибута "Тип объекта".
     * До версии 15 отсутствует пагинация значений.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из последних 14 объектов userCases</li>
     * </ol>
     */
    @Test
    public void testMetaClassWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, metaClassAttr, V15)
                .assertValues(
                        elements(Arrays.copyOfRange(userCases, 6, 20))
                );
    }

    /**
     * Тестирование не возможности получения возможных значений с использованием пагинации для атрибута "Тип объекта"
     * с представлением "Дерево выбора". До версии 15 значения возвращаются списком без пагинации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCaseWithTree</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, состоящий из последних 13 объектов userCases</li>
     * </ol>
     */
    @Test
    public void testMetaClassWithTreePresentationWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCaseWithTree)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, metaClassAttr, V15)
                .assertValues(
                        treeElements(Arrays.copyOfRange(userCases, 7, 20))
                );
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута "Тип объекта". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версий 13.1 и 15:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Строка поиска" = название userCase,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testMetaClassWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userCase.getTitle());
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr).isEmpty();
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута "Тип объекта" с представлением "Дерево выбора". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Тип объекта",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Строка поиска" = название userCase,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся пустой уровень дерева объектов</li>
     * </ol>
     */
    @Test
    public void testMetaClassWithTreePresentationWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCaseWithTree)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(metaClassAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userCase.getTitle());

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, metaClassAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, metaClassAttr, V15).assertValues(treeSearch());
    }
}
