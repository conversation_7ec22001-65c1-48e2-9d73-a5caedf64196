package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.embeddedapplication.JsApiModalDialogBuilderTemplates.withCreateDialogBuilder;
import static ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication.TEST_DIV_ID;
import static ru.naumen.selenium.casesutil.model.bo.Bo.TITLE;
import static ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication.APPLICATION_WS_CONNECT;
import static ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication.APPLICATION_WS_SEND;
import static ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication.APPLICATION_WS_SUBSCRIBE;
import static ru.naumen.selenium.casesutil.websocket.DSLWebSocket.TIMEOUT_FOR_CONNECTION;

import java.io.File;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.interfaze.GUILanguageForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на JS API для встроенных приложений (кроме команд)
 *
 * <AUTHOR>
 * @since 13.07.2018
 */
class JsApi2Test extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userClass;

    private static MetaClass userCase;

    private static final long JS_MAX_SAFE_INTEGER = 9007199254740991L;

    /**
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем пользовательский класс userClass, в нем тип userCase</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
    }

    private static String join(String... strings)
    {
        return String.join("\n", strings);
    }

    /**
     * <p>Тестирование получения кода контента, в котором выведено встроенное приложение
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.findContentCode())</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен код контента content
     * </ol>
     */
    @Test
    void testFindContentCode()
    {
        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.findContentCode())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(content.getCode(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование метода jsApi.getCurrentContextObject для получения значений атрибутов с формы и вызова обработчика
     * событий изменения значений для инициализированных атрибутов на форме
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63028905
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавляем в систему атрибут attr для класса userClass</li>
     * <li>Добавляем файл applicationFile встроенного приложения,
     * в котором добавляем обработчик события изменения значения поля title:
     * <pre>
     *     var changeListener = function(result) {
     *         var testDiv = document.getElementById('test_div')
     *         testDiv.innerText = result.newValue
     *     }
     *     jsApi.addFieldChangeListener('title', changeListener);
     *     jsApi.commands.getCurrentContextObject()
     *         .then(result => {
     *             var testDiv = document.getElementById('test_div');
     *             testDiv.innerText = result['title'];
     *         },
     *         error => {
     *             alert(\"Rejected: \" + error);
     *         });
     * </pre>
     * Данный обработчик изменит текст элемента с идентификатором test_div приложения на значение атрибута title,
     * если он изменится</li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На форму добавления объектов типа userClass добавляем встроенное приложение app</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Заходим под Naumen</li>
     * <li>Переходим на форму добавления объекта класса userClass с указанием инициализации атрибута title со
     * значением TEST</li>
     * <li>Проверяем, что текст элемента с идентификатором test_div
     * приложения стал равен "TEST" - значению атрибута title на форме</li>
     * </ol>
     */
    @Test
    void testGetCurrentContextObject()
    {
        //Подготовка
        Attribute attr = DAOAttribute.createString(userClass);
        DSLAttribute.add(attr);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(attrsGroup, new Attribute[] { attr }, new Attribute[] {});

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(join(
                    "var changeListener = function(result) {",
                    "       var testDiv = document.getElementById('%s')",
                    "       testDiv.innerText = result.newValue",
                    "}",
                    "jsApi.addFieldChangeListener('%s', changeListener);",
                    "jsApi.commands.getCurrentContextObject()",
                    ".then(",
                    "  result => {",
                    "    var testDiv = document.getElementById('%s');",
                    "    testDiv.innerText = result['%s'];",
                    "  },",
                    "  error => {",
                    "    alert(\"Rejected: \" + error);",
                    "  }",
                    ");"
                ),
                TEST_DIV_ID, TITLE, TEST_DIV_ID, TITLE));
        //@formatter:on

        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm app = DAOContentAddForm.createEmbeddedApplication(userClass.getFqn(), model);
        DSLContent.add(app);

        //Выполнение действий и проверка
        GUILogon.asNaumen();
        final String attrValue = "TEST";
        tester.goToPage(String.format(Config.get().getWebAddress()
                                      + GUINavigational.URL_POSTFIX_OPERATOR
                                      + "#add:%s:%s::%s",
                userClass.getFqn(),
                SharedFixture.root().getUuid(),
                Bo.TITLE + "=" + attrValue));

        Assertions.assertEquals(attrValue, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(app));
    }

    /**
     * Тестирование того, что даже если пользователь установил карточку какого-либо объекта своей домашней страницей,
     * то все равно jsApi.extractSubjectUuid() отрабатывает корректно
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Создать тип отдела ouCase</li>
     *     <li>Создать тип сотрудника employeeCase</li>
     *     <li>Создать отдел ou типа ouCase</li>
     *     <li>Создать сотрудника employee типа employeeCase</li>
     *     <li>На карточку типа ouCase добавить вкладку tab</li>
     *     <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     *     jsApi.extractSubjectUuid():
     *         <pre>document.write(jsApi.extractSubjectUuid())</pre>
     *     </li>
     *     <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     *     <li>На вкладку tab добавляем встроенное приложение app</li>
     *
     *     <br/>
     *     <b>Действия</b>
     *     <li>Зайти под employee</li>
     *     <li>Перейти на карточку ou</li>
     *     <li>Перейти на вкладку tab</li>
     *     <li>Сделать текущую страницу домашней</li>
     *     <li>Разлогиниться</li>
     *     <li>Зайти под employee</li>
     *
     *     <br/>
     *     <b>Проверки</b>
     *     <li>Проверить, что во встроенном приложении выведен uuid ou</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidWhenLoggingInUnderEmployeeToTheHomepage()
    {
        //Подготовка
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        ContentTab tab = DAOContentTab.createTab(ouCase.getFqn());
        DSLContent.addTab(tab);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(ouCase.getFqn(), model);
        DSLContent.add(tab, content);

        GUILogon.login(employee);
        GUIBo.goToCard(ou);
        GUITab.clickOnTab(tab);
        GUIBo.addCurrentPageToHome();
        GUILogon.logout();

        GUILogon.login(employee);

        GUIContent.assertPresent(content);
        Assertions.assertEquals(ou.getUuid(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование того, что даже если пользователь установил форму добавления какого-либо объекта своей домашней
     * страницей, то все равно jsApi.extractSubjectUuid() отрабатывает корректно
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     * jsApi.extractSubjectUuid():
     * <pre>document.write(jsApi.extractSubjectUuid())</pre>
     * </li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На форму добавления отдела добавляем встроенное приложение app</li>
     * <br/>
     * <b>Действия</b>
     * <li>Зайти под employee</li>
     * <li>Перейти на форму добавления отдел</li>
     * <li>Сделать текущую страницу домашней</li>
     * <li>Разлогиниться</li>
     * <li>Зайти под employee</li>
     * <br/>
     * <b>Проверки</b>
     * <li>Проверить, что во встроенном приложении выведен "null"</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidWhenLoggingInUnderEmployeeToTheHomepageThatIsAddForm()
    {
        //Подготовка
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentAddForm.createEmbeddedApplication(ouCase.getParentFqn(), model);
        DSLContent.add(content);

        GUILogon.login(employee);
        GUIBo.goToAddForm(ouCase);
        GUIBo.addCurrentPageToHome();
        GUILogon.logout();

        GUILogon.login(employee);

        GUIContent.assertPresent(content);
        Assertions.assertEquals("null", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование того, что даже если пользователь установил форму добавления какого-либо объекта своей домашней
     * страницей, то все равно jsApi.extractSubjectUuid() отрабатывает корректно
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     * jsApi.extractSubjectUuid():
     * <pre>document.write(jsApi.extractSubjectUuid())</pre>
     * </li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На форму реактирования отдела добавляем встроенное приложение app</li>
     * <br/>
     * <b>Действия</b>
     * <li>Зайти под employee</li>
     * <li>Перейти на форму редактирования ou</li>
     * <li>Сделать текущую страницу домашней</li>
     * <li>Разлогиниться</li>
     * <li>Зайти под employee</li>
     * <br/>
     * <b>Проверки</b>
     * <li>Проверить, что во встроенном приложении выведен UUID объекта ou</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidWhenLoggingInUnderEmployeeToTheHomepageThatIsEditForm()
    {
        //Подготовка
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentEditForm.createEmbeddedApplication(ouCase.getParentFqn(), model);
        DSLContent.add(content);

        GUILogon.login(employee);
        GUIBo.goToEditForm(ou);
        GUIBo.addCurrentPageToHome();
        GUILogon.logout();

        GUILogon.login(employee);

        GUIContent.assertPresent(content);
        Assertions.assertEquals(ou.getUuid(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование того, что даже если пользователь не устанавливал домашнюю страницу,
     * то все равно jsApi.extractSubjectUuid() отрабатывает корректно
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Создать тип отдела ouCase</li>
     *     <li>Создать тип сотрудника employeeCase</li>
     *     <li>Создать отдел ou типа ouCase</li>
     *     <li>Создать сотрудника employee типа employeeCase</li>
     *     <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     *     jsApi.extractSubjectUuid():
     *         <pre>document.write(jsApi.extractSubjectUuid())</pre>
     *     </li>
     *     <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     *     <li>На карточку employeeCase добавляем встроенное приложение app</li>
     *
     *     <br/>
     *     <b>Действия</b>
     *     <li>Зайти под employee</li>
     *
     *     <br/>
     *     <b>Проверки</b>
     *     <li>Проверить, что во встроенном приложении выведен uuid employee</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidWhenLoggingInUnderEmployeeWithoutAnyHomepageChanges()
    {
        //Подготовка
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(employeeCase.getFqn(), model);
        DSLContent.add(content);

        GUILogon.login(employee);

        GUIContent.assertPresent(content);
        Assertions.assertEquals(employee.getUuid(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование того, что на форме добавления jsApi.extractSubjectUuid() возвращает null
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Создать тип отдела ouCase</li>
     *     <li>Создать тип сотрудника employeeCase</li>
     *     <li>Создать отдел ou типа ouCase</li>
     *     <li>Создать сотрудника employee типа employeeCase</li>
     *     <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     *     jsApi.extractSubjectUuid():
     *         <pre>document.write(jsApi.extractSubjectUuid())</pre>
     *     </li>
     *     <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     *     <li>На форму добавления класса Сотрудник добавляем встроенное приложение app</li>
     *
     *     <br/>
     *     <b>Действия</b>
     *     <li>Зайти под employee</li>
     *     <li>Открыть форму добавления employeeCase</li>
     *
     *     <br/>
     *     <b>Проверки</b>
     *     <li>Проверить, что во встроенном приложении выведен uuid employee</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidOnAddForm()
    {
        //Подготовка
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentAddForm.createEmbeddedApplication(employeeCase.getParentFqn(), model);
        DSLContent.add(content);

        GUILogon.login(employee);
        GUIBo.goToAddForm(employeeCase);

        GUIContent.assertPresent(content);
        Assertions.assertEquals("null", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование того, что даже если перейти в интерфейс оператора под суперпользователем, у которого интерфейс по
     * умолчанию - админка, то все равно jsApi.extractSubjectUuid() отрабатывает корректно
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/?anchor=uuid:smrmTask$62636605
     *
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Добавляем файл applicationFile встроенного приложения, которое будет просто выводить результат вызова
     *     jsApi.extractSubjectUuid():
     *         <pre>document.write(jsApi.extractSubjectUuid())</pre>
     *     </li>
     *     <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     *     <li>Добавляем встроенное приложение app на карточку Компании</li>
     *
     *     <br/>
     *     <b>Действия</b>
     *     <li>Зайти под naumen</li>
     *     <li>Перейти в интерфейс оператора</li>
     *
     *     <br/>
     *     <b>Проверки</b>
     *     <li>Проверить, что во встроенном приложении выведен uuid Компании</li>
     * </ol>
     */
    @Test
    void testExtractingSubjectUuidWhenLoggingInUnderSuperuser()
    {
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(DAORootClass.create().getFqn(), model);
        DSLContent.add(content);

        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(SharedFixture.root().getUuid(),
                GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование метода jsApi.addCurrentObjectChangeListener
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$81552336
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавляем в систему атрибут attr для типа userCase и контент с ним</li>
     * <li>Добавляем объект bo типа userCase</li>
     * <li>Добавляем файл applicationFile встроенного приложения,
     * в котором добавляем обработчик события изменения значения поля attr:
     * <pre>
     * jsApi.addCurrentObjectChangeListener(attr, function(result) {
     *          var testDiv = document.getElementById('test_div')
     *          testDiv.innerText = result.value
     * })
     * </pre>
     * Данный обработчик изменит текст элемента с идентификатором test_div приложения на значение атрибута attr,
     * если он изменится</li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На карточку объектов типа userCase добавляем встроенное приложение app</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Заходим под Naumen</li>
     * <li>Переходим на карточку объекта bo</li>
     * <li>Открываем форму быстрого редактирования атрибутов</li>
     * <li>Заполняем интересующий нас атрибут attr текстом "TEST"</li>
     * <li>Сохраняем форму</li>
     * <li>Проверяем, что текст элемента с идентификатором test_div
     * приложения стал равен "TEST" - значению атрибута attr на форме</li>
     * </ol>
     */
    @Test
    void testAddCurrentObjectChangeListener()
    {
        //Подготовка
        Attribute attr = DAOAttribute.createString(userCase);
        DSLAttribute.add(attr);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userCase);
        DSLGroupAttr.edit(attrsGroup, new Attribute[] { attr }, new Attribute[] {});

        ContentForm scProperties = DAOContentCard.createPropertyList(userCase, attrsGroup);
        DSLContent.add(scProperties);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(
                join(
                    "jsApi.addCurrentObjectChangeListener('%s', function(result) {",
                    "       var testDiv = document.getElementById('%s')",
                    "       testDiv.innerText = result.value",
                    "})"
                ),
                attr.getCode(), GUIEmbeddedApplication.TEST_DIV_ID));
        //@formatter:on

        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm app = DAOContentCard.createEmbeddedApplication(userCase, model);
        DSLContent.add(app);

        //Выполнение действий и проверка
        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(bo);

        String attrValue = ModelUtils.createText(10);
        GUIContent.clickEdit(scProperties);
        GUIForm.fillAttribute(attr, attrValue);
        GUIForm.applyForm();

        Assertions.assertEquals(attrValue, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(app));
    }

    /**
     * Тестирование методов jsApi.wsSubscribe(destination, callback), jsApi.wsUnsubscribe(destination, callback)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$77148021
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и включить встроенное приложение application, которое подписывается на сообщения, приходящие
     * в dest1 и отписывается, после получения первого сообщения</li>
     * <li>На карточку компании вывести контент "Встроенное приложение" (Приложение - application)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Проверить, что на карточке компании отображается контент "Встроенное приложение"</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Test message body') отправить сообщение в dest1</li>
     * <li>Проверить, что в контенте присутствует текст "Test message body"</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Edited test message body') отправить сообщение в
     * dest1</li>
     * <li>Проверить, что текст в контенте не изменился</li>
     * </ol>
     */
    @Test
    void testWsSubscribeUnsubscribe()
    {
        final String TEST_MSG_BODY = "Test message body";
        final String EDITED_TEST_MSG_BODY = "Edited test message body";

        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                APPLICATION_WS_SUBSCRIBE);
        DSLEmbeddedApplication.add(application);
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIContent.assertPresent(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        WaitTool.waitMills(TIMEOUT_FOR_CONNECTION);
        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", TEST_MSG_BODY)).runScript();
        Assertions.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", EDITED_TEST_MSG_BODY)).runScript();
        Assertions.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование метода jsApi.ws.send(destination, body)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$77148021
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и включить встроенное приложение application, которое подписывается на сообщения, приходящие
     * в dest1, после чего отправляет сообщение в dest1</li>
     * <li>На карточку компании вывести контент "Встроенное приложение" (Приложение - application)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Проверить, что на карточке компании отображается контент "Встроенное приложение"</li>
     * <li>Проверить, что в контенте присутствует текст "Test message body"</li>
     * </ol>
     */
    @Test
    void testWsSend()
    {
        final String TEST_MSG_BODY = "Test message body";
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                APPLICATION_WS_SEND);
        DSLEmbeddedApplication.add(application);
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIContent.assertPresent(content);
        WaitTool.waitMills(TIMEOUT_FOR_CONNECTION);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование методов jsApi.ws.connect(onConnect), jsApi.ws.disconnect()
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$77148021
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и включить встроенное приложение application, которое открывает подключение к вэб-сокету с помощью
     * jsApi.wsConnect(), подписывается на dest1 и закрывает соединение с помощью jsApi.wsDisconnect(), после
     * получения первого сообщения</li>
     * <li>На карточку компании вывести контент "Встроенное приложение" (Приложение - application)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Проверить, что на карточке компании отображается контент "Встроенное приложение"</li>
     * <li>Проверить, что на во встроенном приложении отображается слово subscribed т.е. встройка успела
     * подписаться на событие</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Test message body') отправить сообщение в dest1</li>
     * <li>Проверить, что в контенте присутствует текст "Test message body"</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Edited test message body') отправить сообщение в
     * dest1</li>
     * <li>Проверить, что текст в контенте не изменился</li>
     * </ol>
     */
    @Test
    void testWsConnectDisconnect()
    {
        final String TEST_MSG_BODY = "Test message body";
        final String EDITED_TEST_MSG_BODY = "Edited test message body";
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                APPLICATION_WS_CONNECT);
        DSLEmbeddedApplication.add(application);
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(
                rootClass.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIContent.assertPresent(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        WaitTool.waitMills(TIMEOUT_FOR_CONNECTION);
        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", TEST_MSG_BODY)).runScript();
        Assertions.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", EDITED_TEST_MSG_BODY)).runScript();
        Assertions.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование метода <code>jsApi.restCallModule(moduleName, 'methodName', 'parameters')</code>
     * <ol>
     *     <li>Проверяем что можем вызвать произвольный метод обычного скриптового модуля
     *     (при наличии внутри встроенного приложения модуля с таким же кодом,
     *     но отсутствии вызываемого метода)</li>
     *     <li>Проверяем что можем вызвать произвольный метод скриптового модуля,
     *      загруженного вместе со встроенным приложением и имеющего такой же код, как у обычного</li>
     *     <li>Проверяем что можем передавать аргументы при вызове, в том числе с использованием символа $</li>
     * </ol>
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80781349
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'testRestCallModule' и содержимым:
     *     <pre>
     *     def someMethod() {
     *         'RANDOM_TEXT_HERE'
     *     }
     *     def someCode(num, str, nullValue, jsonObject, bool) {
     *       def gson = new GsonBuilder().create()
     *       try {
     *         assert num == RANDOM_NUMBER_HERE
     *         assert bool == RANDOM_BOOLEAN_HERE
     *         assert str == 'RANDOM_TEXT_HERE'
     *         assert jsonObject.property == 'RANDOM_TEXT_HERE'
     *         assert nullValue == null
     *         return '\"' + str + '\"'"
     *       } catch (Throwable e) {
     *         logger.error('someMethodWithArgs Error: ' + e.message)",
     *         return '\"Error, see log\"'",
     *       }
     *       def testCallModuleInsideApplication() {
     *          '\"This method should not be called\"'
     *       }
     *     }</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *   <pre>jsApi.restCallModule('testRestCallModule', 'someMethod')
     *          .then(function(result) {
     *             var testDiv = document.getElementById('test_div')
     *             testDiv.innerText += result.value
     *   })</pre>
     *   и скриптовым модулем с кодом 'testRestCallModule':
     *   <pre>def testCallModuleInsideApplication() {
     *          'RANDOM_TEXT_INSIDE_APPLICATION_HERE'
     *   })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным
     *   ранее приложением на карточку класса Компании</li>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *   <pre>jsApi.restCallModule('testRestCallModule', 'testCallModuleInsideApplication')
     *          .then(function(result) {
     *             var testDiv = document.getElementById('test_div')
     *             testDiv.innerText += result.value
     *   })</pre>
     *   и скриптовым модулем с кодом 'testRestCallModule':
     *   <pre>def testCallModuleInsideApplication() {
     *          'RANDOM_TEXT_INSIDE_APPLICATION_HERE'
     *   })</pre></li>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     *   приложением на карточку класса Компании</li>
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным
     *   ранее приложением на карточку класса Компании</li>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *   <pre>jsApi.restCallModule('testRestCallModule', 'someMethodWithArgs',
     *      RANDOM_NUMBER_HERE, 'RANDOM_TEXT_HERE', null, {property: 'RANDOM_TEXT_HERE'},
     *      RANDOM_BOOLEAN_HERE)
     *          .then(function(result) {
     *             var testDiv = document.getElementById('test_div')
     *             testDiv.innerText += result.value
     *   })</pre></li>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     *   приложением на карточку класса Компании</li>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во первом встроенном приложении выведен текст 'RANDOM_TEXT_HERE'
     *   <li>Проверить, что во втором встроенном приложении выведен текст 'RANDOM_TEXT_INSIDE_APPLICATION_HERE'
     *   <li>Проверить, что во третьем встроенном приложении выведен текст 'RANDOM_TEXT_HERE'
     * </ol>
     */
    @Test
    void testRestCallModule()
    {
        ModuleConf module = DAOModuleConf.create("testRestCallModule");
        Random random = new Random();
        String expectedNumber = String.valueOf(
                ThreadLocalRandom.current().nextLong(((long)Integer.MAX_VALUE) + 1, JS_MAX_SAFE_INTEGER));
        String expectedBoolean = String.valueOf(random.nextBoolean());
        String expectedText = ModelUtils.createText(10) + "$" + ModelUtils.createText(10);
        String expectedObject = String.format("{property: '%s'}", expectedText);
        module.setScriptBody(String.format(join(
                "def someMethod() {",
                "  '\"%s\"'",
                "}\n",
                "def someMethodWithArgs(num, str, nullValue, jsonObject, bool) {",
                "  try {",
                "    assert num == %s",
                "    assert bool == %s",
                "    assert str == '%s'",
                "    assert jsonObject.property == '%s'",
                "    assert nullValue == null",
                "    return '\"' + str + '\"'",
                "  } catch (Throwable e) {",
                "    logger.error('someMethodWithArgs Error: ' + e.message)",
                "    return '\"Error, see log\"'",
                "  }",
                "}\n",
                "def testCallModuleInsideApplication() {",
                "  '\"This method should not be called\"'",
                "}\n"
        ), expectedText, expectedNumber, expectedBoolean, expectedText, expectedText));
        DSLModuleConf.add(module);

        ModuleConf insideAppModule = DAOModuleConf.create("testRestCallModule");
        String insideAppText = ModelUtils.createText(15);
        insideAppModule.setScriptBody(String.format(join(
                "def testCallModuleInsideApplication() {",
                "  '\"%s\"'",
                "}"
        ), insideAppText));
        //Подготовка
        File appFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(join("jsApi.restCallModule('testRestCallModule', 'someMethod')",
                        ".then(function (result) {",
                        "    var testDiv = document.getElementById('%s')",
                        "    testDiv.innerText += result",
                        "})"), GUIEmbeddedApplication.TEST_DIV_ID),
                insideAppModule);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                appFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        File appFileCallModuleInside = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(join(
                        "jsApi.restCallModule('testRestCallModule', 'testCallModuleInsideApplication')",
                        ".then(function (result) {",
                        "    var testDiv = document.getElementById('%s')",
                        "    testDiv.innerText += result",
                        "})"), GUIEmbeddedApplication.TEST_DIV_ID),
                insideAppModule);

        EmbeddedApplication applicationCallModuleInside = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                appFileCallModuleInside.getAbsolutePath());
        DSLEmbeddedApplication.add(applicationCallModuleInside);

        File appFileWithArgs = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(join(
                                "jsApi.restCallModule('testRestCallModule', 'someMethodWithArgs', %s, '%s', null, %s,"
                                + " %s)",
                                ".then(function (result) {",
                                "    var testDiv = document.getElementById('%s')",
                                "    testDiv.innerText += result",
                                "})"),
                        expectedNumber, expectedText, expectedObject, expectedBoolean,
                        GUIEmbeddedApplication.TEST_DIV_ID));

        EmbeddedApplication applicationWithArgs = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                appFileWithArgs.getAbsolutePath());
        DSLEmbeddedApplication.add(applicationWithArgs);

        String rootClassFqn = DAORootClass.create().getFqn();
        ContentForm appContent = DAOContentCard.createEmbeddedApplication(rootClassFqn, application);
        ContentForm appContentCallModuleInside = DAOContentCard.createEmbeddedApplication(
                rootClassFqn, applicationCallModuleInside);
        ContentForm appContentWithArgs = DAOContentCard.createEmbeddedApplication(
                rootClassFqn, applicationWithArgs);
        DSLContent.add(appContent, appContentCallModuleInside, appContentWithArgs);

        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIContent.assertPresent(appContent);
        GUIContent.assertPresent(appContentCallModuleInside);
        GUIContent.assertPresent(appContentWithArgs);
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
        Assertions.assertEquals(insideAppText,
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContentCallModuleInside));
        Assertions.assertEquals(expectedText,
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContentWithArgs));
    }

    /**
     * Тестирование работы метода jsApi.restCall с лицензированным модулем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104183317
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение с телом:
     * <pre>
     *     jsApi.restCall('exec?func=modules.testRestCallToLicensedModule.test&params=')"
     *         .then(result => document.getElementById('test_div').innerHTML = result)
     * </pre>
     * и скриптовым модулем с кодом 'testRestCallToLicensedModule' и содержимым:
     * <pre>
     *     def str() {
     *         '$randomString'
     *     }
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     * приложением на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении внутри test_div выведен текст $randomString</li>
     * <ol>
     */
    @Test
    void testRestCallToLicensedModule()
    {
        // Подготовка
        String moduleCode = "testRestCallToLicensedModule";
        String randomString = ModelUtils.createCode();
        ModuleConf moduleConf = DAOModuleConf.create(moduleCode, null,
                String.format("def test() {\n"
                              + "   '%s'\n"
                              + "}\n", randomString));

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format("jsApi.restCall('exec?func=modules.%s.test&params=')"
                                + ".then(result => document.getElementById('%s').innerHTML = result)",
                        moduleCode, GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf);
        //@formatter:on
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));
    }

    /**
     * Тестирование работы метода jsApi.restCallAsJson с лицензированным модулем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104183317
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение с телом:
     * <pre>
     *     jsApi.restCallAsJson('exec?func=modules.testRestCallToLicensedModule.test&params=')"
     *         .then(result => document.getElementById('test_div').innerHTML = result)
     * </pre>
     * и скриптовым модулем с кодом 'testRestCallToLicensedModule' и содержимым:
     * <pre>
     *     def str() {
     *         '$randomString'
     *     }
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     * приложением на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении внутри test_div выведен текст $randomString</li>
     * <ol>
     */
    @Test
    void testRestCallAsJsonToLicensedModule()
    {
        // Подготовка
        String moduleCode = "testRestCallAsJsonToLicensedModule";
        String randomString = ModelUtils.createCode();
        ModuleConf moduleConf = DAOModuleConf.create(moduleCode, null,
                String.format("def test() {\n"
                              + "   '\"%s\"'\n"
                              + "}\n", randomString));

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format("jsApi.restCallAsJson('exec?func=modules.%s.test&params=')"
                        + ".then(result => document.getElementById('%s').innerHTML = result)",
                        moduleCode, GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf);
        //@formatter:on
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));
    }

    /**
     * Тестирование работы метода jsApi.findApplicationCode
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104183317
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение с телом:
     * <pre>
     *     document.write(jsApi.findApplicationCode())
     * </pre>
     * </li>
     * <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     * приложением на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении выведен код этого приложения</li>
     * <ol>
     */
    @Test
    void testFindApplicationCode()
    {
        // Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.findApplicationCode())");
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(application.getCode(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения локализованных значений во встроенном приложении
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$115026450
     * <p>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить скриптовый модуль с кодом <code>testLang</code> и текстом:
     *     <pre>
     *         String getData(String u) {
     *         return groovy.json.JsonOutput.toJson(api.metainfo.getMetaClass('ou').getAttribute('title').title)
     *        }
     *     </pre>
     * </li>
     * <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *    <pre>
     *       top.injectJsApi(top, window)
     *       сonst url = `exec/?func=modules.testLang.getData&params='${currentUser.uuid}'`;
     *       jsApi.restCallAsJson(url).then(function (result) {
     *       var testDiv = document.getElementById('test_div')
     *       testDiv.innerText += result
     *      })
     *    </pre>
     * </li>
     * <li>Включить добавленное встроенное приложение/li>
     * <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку Компании</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в ИО под тестером</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что на карточке Компании присутствует встроенное приложение</li>
     * <li>Проверить, что во встроенном приложении выведено: "Название"</li>
     * <li>Изменить локаль текущему пользователю на English</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что во встроенном приложении выведено: "Title"</li>
     * </ol>
     */
    @Test
    void testLocalizeResultInEmbeddedApplication()
    {
        //Подготовка
        ModuleConf module = DAOModuleConf.create("testLang");
        //@formatter:off
        module.setScriptBody(join(
                "String getData(String s) {",
                "  return groovy.json.JsonOutput.toJson(api.metainfo.getMetaClass('ou').getAttribute('title').title)",
                "}"
        ));
        //@formatter:on
        DSLModuleConf.add(module);

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, join("top.injectJsApi(top, window)",
                        "const url = 'exec/?func=modules.testLang.getData&params='",
                        "jsApi.restCallAsJson(url).then(function (result) {",
                        " var testDiv = document.getElementById('test_div')",
                        "testDiv.innerText += result",
                        "})"
                )
        );
        //@formatter:on
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        DSLContent.add(content);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIContent.assertPresent(content);
        Assertions.assertEquals("Название", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        SharedFixture.employee().setEmplLocale(GUILanguageForm.EN_LANGUAGE);
        DSLEmployee.editPersonalSettings(SharedFixture.employee());

        tester.refresh();
        tester.waitAsyncCall();
        Assertions.assertEquals("Title", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование получения КП origin из встроенного приложением на карточке добавленного объекта
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00265
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$282030414
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль testModule:<br>
     *     Код: "testModule"<br>
     *     Текст: <pre>def getOrigin() {
     *        def origin = ru.naumen.core.server.SpringContext.getInstance().getBean(ru.naumen.core.shared.script.places.OriginService.class).getOrigin()
     *        return '"' + origin + '"';
     *     }</pre></li>
     *   <li>Создать встроенное приложение testApp, которое будет вызывать метод <code>jsApi.modals
     *   .getDialogBuilder</code>
     *   для выведения диалогового окна с кнопкой "ОК" по умолчанию, и с обращением к скриптовому модулю
     *   для получения значения КП origin и вывода её в тело встроенного приложения, после нажатия на кнопку:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.modals.getDialogBuilder('$content')
     *           .show()
     *           .then((result) => jsApi.restCallModule('testModule', 'getOrigin').then((o) => document.body.innerHTML = o))
     *   ----------------------------------------------------------
     *     Где:
     *       1) $content - содержание диалогового окна
     *   </pre></li>
     * <li>В типе userCase на карточке объекта создать контент appContent со встроенным приложением testApp</li>
     * <li>Добавить в верхнее меню кнопку добавления объекта типа userCase</li>
     * <li>Включить верхнее меню</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Кликнуть на кнопку добавления объекта типа userCase в верхнем меню</li>
     * <li>Заполнить все необходимые данные и сохранить объект userBo1</li>
     * <li>Кликнуть на кнопку Ок в появившемся диалоговом окне</li>
     * <li>Проверяем, что внутри встроенного приложения появились ожидаемые нами данные "readForm"</li>
     * <li>Кликнуть кнопку "Назад"</li>
     * <li>Перейти на карточку userBo1</li>
     * <li>Повторить 10 раз пункты с 10 по 13</li>
     * </ol>
     */
    @Test
    void testGetOriginInsideEmbeddedApplication()
    {
        // Подготовка
        ModuleConf testModule = DAOModuleConf.create("testModule");
        testModule.setScriptBody("""
                def getOrigin() {
                    def origin = ru.naumen.core.server.SpringContext.getInstance().getBean(ru.naumen.core.shared.script.places.OriginService.class).getOrigin()
                    return '\"' + origin + '\"'
                }
                """);
        DSLModuleConf.add(testModule);

        String content = ModelUtils.createDescription();
        String jsButtonAction = """
                .show()
                .then((result) => jsApi.restCallModule('testModule', 'getOrigin').then((o) => document.body.innerHTML = o))
                """;
        String jsContent = withCreateDialogBuilder(content) + jsButtonAction;
        EmbeddedApplication testApp = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(testApp);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), testApp);
        DSLContent.add(appContent);

        MenuItem addButton = DAOMenuItem.createAddButton(true, userCase);
        DSLNavSettings.editVisibilitySettings(true, true);
        DSLMenuItem.add(addButton);

        // Действия и проверки
        GUILogon.asTester();

        Bo userBo1 = DAOOu.create(userCase);
        GUINavSettingsOperator.clickMenuItem(addButton.getCode(), userCase.getFqn());
        GUIForm.fillAttribute(Bo.TITLE, userBo1.getTitle());
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(userBo1);

        for (int i = 0; i < 10; i++)
        {
            GUIForm.applyInfoDialog();
            String actualCode = GUIEmbeddedApplication.getEmbeddedApplicationContent(appContent);
            Assertions.assertEquals("READ", actualCode);
            GUINavigational.clickBackLink();
            GUIBo.goToCard(userBo1);
        }
    }
}
