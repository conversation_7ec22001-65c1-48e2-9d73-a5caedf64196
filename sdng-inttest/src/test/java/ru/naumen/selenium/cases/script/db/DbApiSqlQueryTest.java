package ru.naumen.selenium.cases.script.db;

import static ru.naumen.selenium.util.Json.GSON;
import static ru.naumen.selenium.util.Json.LIST_TYPE;

import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тесты на <code>api.db.sql.query</code>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 *
 * <AUTHOR>
 * @since 15.11.2024
 */
public class DbApiSqlQueryTest extends AbstractTestCase
{
    /**
     * Тестирование получения данных через метод <code>api.db.sql.query</code>.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$178877731
     * <br>
     * <b>Подготовка.</b>
     * <li>Создать команды team1, team2, team3</li>
     * <li>Создать сотрудников employee1, employee2, employee3</li>
     * <li>Добавить сотрудника employee1 в команду team1, employee2 в team2, employee3 в team3</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Выполнить скрипт с методом установки значения параметра <code>set(String, String)</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.query('''SELECT 'employee$'||id FROM tbl_employee WHERE id = :id''')
     *             .set('id', '$id')
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $id - ID сотрудника employee1.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификатор employee1</li>
     * <li>Выполнить скрипт с методом установки значения параметра <code>set(String, String)</code>
     * для установки имени пользователя:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.query('''SELECT 'employee$'||id FROM tbl_employee WHERE firstName = :name''')
     *             .set('name', "${employee.firstName}")
     *             .list()
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификатор employee1</li>
     * <li>Выполнить скрипт с методом установки значения параметра <code>set(String, List&lt;String>)</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:ids)
     *     ''')
     *             .set('ids', ['$team1Id', '$team2Id'])
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team1Id - ID команды team1;
     *     2) $team2Id - ID команды team2.
     *     </pre>
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификаторы employee1 и employee2</li>
     * <li>Выполнить скрипт с методом установки значения параметра <code>set(Map&gt;String, String>)</code>:</li>
     * <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:id)
     *     ''')
     *             .set(['id', '$team2Id'])
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team2Id - ID команды team2.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификатор employee2</li>
     * <li>Выполнить скрипт с методом установки значения параметра <code>set(Map&gt;String, String>)</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:ids)
     *     ''')
     *             .set(['ids': ['$team1Id', '$team2Id']])
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team1Id - ID команды team1;
     *     2) $team2Id - ID команды team2.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификаторы employee1 и employee2</li>
     * <li>Выполнить скрипт с методом установки максимального количества результатов
     * <code>setMaxResults(int)</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:ids)
     *     ''')
     *             .set('ids', ['$team1Id', '$team2Id'])
     *             .setMaxResults(1)
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team1Id - ID команды team1;
     *     2) $team2Id - ID команды team2.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификатор employee1</li>
     * <li>Выполнить скрипт с методом установки первого возвращаемого результата
     * <code>setFirstResult(int)</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:ids)
     *     ''')
     *             .set('ids', ['$team1Id', '$team2Id'])
     *             .setFirstResult(1)
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team1Id - ID команды team1;
     *     2) $team2Id - ID команды team2.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул идентификатор employee2</li>
     * <li>Выполнить скрипт с методом, выполняющим трансформацию результатов в список словарей (используя в качестве
     * ключей указанные в запросе alias) <code>transformAliasToEntityMap()</code>:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('''
     *             SELECT 'employee$'||e.id AS uuid FROM tbl_employee e
     *             JOIN tbl_employee_teams et ON e.id = et.employee_id
     *             WHERE et.teams_id IN (:id)
     *     ''')
     *             .set('id', '$team2Id')
     *             .transformAliasToEntityMap()
     *             .list()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $team2Id - ID команды team2.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул JSON {"uuid": "%UUID объекта employee2%"}</li>
     * <li>Выполнить скрипт, который вернёт пустой список:</li>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.query("SELECT 'employee\$'||id FROM tbl_employee WHERE firstName = :name")
     *             .set('name', '$randomString')
     *             .list().size()
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $randomString - случайная строка.
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул - 0</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(
            ignoreDb = { DbType.MSSQL, DbType.ORACLE }, cause = "Тестируется API, не базовый функционал работы с БД")
    public void testDbSqlQueryWithSelect()
    {
        // Подготовка:
        Bo team1 = DAOTeam.create(SharedFixture.teamCase());
        Bo team2 = DAOTeam.create(SharedFixture.teamCase());
        Bo team3 = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team1, team2, team3);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo employee3 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee1, employee2, employee3);
        DSLEmployee.addToTeams(employee1, team1);
        DSLEmployee.addToTeams(employee2, team2);
        DSLEmployee.addToTeams(employee3, team3);

        // Выполнение действий и проверки:
        // проверить метод set(String, String)
        String employee1Id = StringUtils.substringAfter(employee1.getUuid(), "$");
        String actualResult1 = ScriptRunner.executeScript("""
                api.db.sql.query('''SELECT 'employee$'||id FROM tbl_employee WHERE id = :id''')
                    .set('id', %s)
                    .list()""", employee1Id);
        Assert.assertEquals(ModelUtils.getUuids(employee1), Json.stringToList(actualResult1));

        String actualResult2 = ScriptRunner.executeScript("""
                api.db.sql.query('''SELECT 'employee$'||id FROM tbl_employee WHERE first_name = :name''')
                    .set('name', '%s')
                    .list()""", employee1.getFirstName());
        Assert.assertEquals(ModelUtils.getUuids(employee1), Json.stringToList(actualResult2));

        // проверить метод set(String, List<String>)
        String team1Id = StringUtils.substringAfter(team1.getUuid(), "$");
        String team2Id = StringUtils.substringAfter(team2.getUuid(), "$");
        String actualResult3 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:ids)
                ''')
                    .set('ids', [%s, %s])
                    .list()""", team1Id, team2Id);
        Assert.assertEquals(ModelUtils.getUuids(employee1, employee2), Json.stringToList(actualResult3));

        // проверить метод set(Map<String, String>)
        String actualResult4 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:id)
                ''')
                    .set(['id', %s])
                    .list()""", team2Id);
        Assert.assertEquals(ModelUtils.getUuids(employee2), Json.stringToList(actualResult4));

        // проверить метод set(Map<String, List<String>)
        String actualResult5 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:ids)
                ''')
                    .set(['ids': [%s, %s]])
                    .list()""", team1Id, team2Id);
        Assert.assertEquals(ModelUtils.getUuids(employee1, employee2), Json.stringToList(actualResult5));

        // проверить метод setMaxResult
        String actualResult6 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:ids)
                ''')
                    .set('ids', [%s, %s])
                    .setMaxResults(1)
                    .list()""", team1Id, team2Id);
        Assert.assertEquals(ModelUtils.getUuids(employee1), Json.stringToList(actualResult6));

        // проверить метод setFirstResult
        String actualResult7 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:ids)
                ''')
                    .set('ids', [%s, %s])
                    .setFirstResult(1)
                    .list()""", team1Id, team2Id);
        Assert.assertEquals(ModelUtils.getUuids(employee2), Json.stringToList(actualResult7));

        // проверить метод transformAliasToEntityMap
        String actualResult8 = ScriptRunner.executeScript("""
                api.db.sql.query('''
                    SELECT 'employee$'||e.id AS uuid FROM tbl_employee e
                    JOIN tbl_employee_teams et ON e.id = et.employee_id
                    WHERE et.teams_id IN (:id)
                ''')
                    .set('id', %s)
                    .transformAliasToEntityMap()
                    .list()""", team2Id);
        Assert.assertEquals(
                List.of(Map.of(ModelUuid.UUID, employee2.getUuid())), GSON.fromJson(actualResult8, LIST_TYPE));

        // проверить кейс с пустым списком
        String actualSize = ScriptRunner.executeScript("""
                api.db.sql.query('''SELECT 'employee$'||id FROM tbl_employee WHERE first_name = :name''')
                    .set('name', '%s')
                    .list().size()""", ModelUtils.createTitle());
        Assert.assertEquals("0", actualSize);
    }

    /**
     * Тестирование обновления данных через метод <code>api.db.sql.query</code>.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$178877731
     * <br>
     * <b>Подготовка.</b>
     * <li>Сгенерировать случайное название oldTitle</li>
     * <li>Создать команды team1, team2, team3, командам team2 и team3 присвоить имя oldTitle</li>
     * <li>Создать сотрудников employee1, employee2, employee3</li>
     * <li>Добавить сотрудника employee1 в команду team1, employee2 в team2, employee3 в team3</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Сгенерировать случайное название newTitle</li>
     * <li>Выполнить скрипт, который выполнит обновление названий команд (oldTitle -> newTitle):
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('UPDATE tbl_team SET title = :newTitle WHERE title = :oldTitle')
     *             .set(['oldTitle': '$oldTitle', 'newTitle': '$newTitle'])
     *             .addSynchronizedEntityName('team')
     *             .executeUpdate()
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить, что скрипт вернул 2</li>
     * <li>Проверить, что название team2 и team3 изменилось на newTitle</li>
     * <li>Проверить, что название team1 не изменилось</li>
     * <li>Выполнить скрипт, который посчитает количество команд с названием newTitle:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.sql.query('SELECT id FROM tbl_team WHERE title = :title')
     *             .set('title', '$newTitle')
     *             .list().size()
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить результат выполнения скрипта - 2</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(
            ignoreDb = { DbType.MSSQL, DbType.ORACLE }, cause = "Тестируется API, не базовый функционал работы с БД")
    public void testDbSqlQueryWithUpdate()
    {
        // Подготовка:
        String oldTitle = ModelUtils.createTitle();
        Bo team1 = DAOTeam.create(SharedFixture.teamCase());
        Bo team2 = DAOTeam.create(SharedFixture.teamCase());
        team2.setTitle(oldTitle);
        Bo team3 = DAOTeam.create(SharedFixture.teamCase());
        team3.setTitle(oldTitle);
        DSLBo.add(team1, team2, team3);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLEmployee.addToTeams(employee, team1, team2);

        // Выполнение действий и проверки:
        String newTitle = ModelUtils.createTitle();
        String incorrectQuery = """
                api.db.sql.query('UPDATE tbl_team SET title = :newTitle WHERE title = :oldTitle')
                        .set(['oldTitle': '%s', 'newTitle': '%s'])
                        .executeUpdate()""".formatted(oldTitle, newTitle);
        ScriptRunner.assertError(incorrectQuery, "Not specified synchronized query spaces");

        String updatedCount = ScriptRunner.executeScript("""
                api.db.sql.query('UPDATE tbl_team SET title = :newTitle WHERE title = :oldTitle')
                        .set(['oldTitle': '%s', 'newTitle': '%s'])
                        .addSynchronizedEntityName('team')
                        .executeUpdate()""", oldTitle, newTitle);
        Assert.assertEquals("2", updatedCount);

        Attribute titleAttr = SysAttribute.title(SharedFixture.teamCase());
        titleAttr.setValue(newTitle);
        DSLBo.assertStringAttr(team2, titleAttr);
        DSLBo.assertStringAttr(team3, titleAttr);

        // проверить, что название первой команды не изменилось
        titleAttr.setValue(team1.getTitle());
        DSLBo.assertStringAttr(team1, titleAttr);

        String actualSize2 = ScriptRunner.executeScript("""
                api.db.sql.query('SELECT id FROM tbl_team WHERE title = :title')
                    .set('title', '%s')
                    .list().size()""", newTitle);
        Assert.assertEquals("2", actualSize2);
    }
}
