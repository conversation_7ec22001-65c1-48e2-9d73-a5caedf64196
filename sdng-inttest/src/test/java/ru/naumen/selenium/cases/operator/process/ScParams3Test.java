package ru.naumen.selenium.cases.operator.process;

import java.io.File;
import java.nio.file.Paths;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrServicePrs;
import ru.naumen.selenium.casesutil.admin.GUIScParams.OrderScFields;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.CoreTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.ActionType;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.settings.ScParameters;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на параметры запросов
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
 * <AUTHOR>
 * @since 25.12.2012
 */
public class ScParams3Test extends AbstractTestCase
{
    /**
     * Тестирование изменения настройки обязательности Контрагента полсле частичной загрузки
     * метаинформации с отключенным параметром "Управлять обязательностью контрагента на уровне типа"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$66563004
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00576
     * <br>
     * <ol>
     * <b>Действия</b>
     * <li>Отключить параметр "Управлять обязательностью контрагента" в настройке бизнес-процессов<li>
     * <li>Выгрузить метаинформацию раздела "Параметры запросов"<li>
     * <li>Включить параметр "Управлять обязательностью контрагента" в настройке бизнес-процессов<li>
     * <li>Войти под суперпользователем<li>
     * <li>Перейти в карточку класса "Запрос"<li>
     * <li>В системном атрибуте типа "Контрагент" изменить признак "Обязательный" на "нет"<li>
     * <li>Загрузить ранее выгруженную метаинформацию<li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что параметр "Обязательный" атрибута "Контрагент" в классе Запрос не редактируемый и имеет
     * значение "да"<li>
     */
    @Test
    public void testClientRequiredChangeAfterImportScParamsMetainfo()
    {
        MetaClass scClass = DAOScCase.createClass();
        DSLScParams.setClientRequiredEditable(false);
        MetainfoExportModel model = new MetainfoExportModel();
        DAOMetainfoExport.selectScParams(model);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(model);
        DSLScParams.setClientRequiredEditable(true);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(scClass);
        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.resetInheriting();
        GUIAttribute.setRequired(false);
        GUIForm.applyModalForm();
        DSLMetainfoTransfer.importMetainfo(metainfo);
        tester.refresh();

        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.assertDisabledRequiredValue(true);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование сохранения настройки обязательности Контрагента полсле полной загрузки
     * метаинформации с включенным параметром "Управлять обязательностью контрагента на уровне типа"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$66563004
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00576
     * <br>
     * <ol>
     * <b>Действия</b>
     * <li>Включить параметр "Управлять обязательностью контрагента" в настройке бизнес-процессов<li>
     * <li>Войти под суперпользователем<li>
     * <li>Перейти в карточку класса "Запрос"<li>
     * <li>В системном атрибуте типа "Контрагент" изменить признак "Обязательный" на "нет"<li>
     * <li>Выгрузить метаинформацию<li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что параметр "Обязательный" атрибута "Контрагент" в классе Запрос имеет значение "нет"<li>
     * <li>Загрузить файл с метаинформацией<li>
     * <li>Проверить, что параметр "Обязательный" атрибута "Контрагент" в классе Запрос имеет значение "нет"<li>
     */
    @Test
    public void testClientRequiredNotChangeAfterImportMetainfo()
    {
        MetaClass scClass = DAOScCase.createClass();
        DSLScParams.setClientRequiredEditable(true);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(scClass);
        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.resetInheriting();
        GUIAttribute.setRequired(false);
        GUIForm.applyModalForm();
        File metainfo = DSLMetainfoTransfer.exportMetainfo();

        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.assertCheckboxEnabled(GUIAttribute.ID_REQUIRED_CHECKBOX_VALUE, true);
        GUITester.assertCheckboxState(GUIAttribute.X_VALUE_REQUIRED_CHECKBOX, false);
        GUIForm.cancelDialog();
        DSLMetainfoTransfer.importMetainfo(metainfo);
        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.assertCheckboxEnabled(GUIAttribute.ID_REQUIRED_CHECKBOX_VALUE, true);
        GUITester.assertCheckboxState(GUIAttribute.X_VALUE_REQUIRED_CHECKBOX, false);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование возможности редактирования настройки автозаполнения поля "Контрагент"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Действия и проверки.</b>
     * <li>Зайти в интерфейсе администратора в Настройку бизнес процессов - параметры запросов.<li>
     * <li>Проверить, что поле "Автоматически заполнять текущим пользователем" имеет значение true</li>
     * <li>Открыть форму редактирования настроек поля "Контрагент"</li>
     * <li>Отключить значение поля "Автоматически заполнять текущим пользователем"</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Проверить, что "Автоматически заполнять текущим пользователем" имеет значение false</li>
     * </ol>
     */
    @Test
    public void testEditClientAutoResolve()
    {
        GUILogon.asSuper();
        GUINavigational.goToScParams();

        GUIScParams.assertClientAutoResolveValue(true);

        GUIScParams.callEditClientForm();
        GUIScParams.clickClientAutoResolve();
        GUIForm.applyModalForm();

        GUIScParams.assertClientAutoResolveValue(false);
    }

    /**
     * Тестирование редактирования параметра "Управлять обязательностью контрагента на уровне типа"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Добавить контент "Выбор контрагента" на форму добавления scCase</li>
     * <li>Добавить кнопку верхнего меню addButton для добавления запроса типа scCase</li>
     * <li>Создать соглашение agreement, отдел client, сделать client получателем agreement</li>
     * <li>Добавить запрос sc типа scCase, клиент - client, соглашение - agreement</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Зайти в интерфейсе администратора в Настройку бизнес процессов - параметры запросов.<li>
     * <li>Проверить, что поле "Управлять обязательностью контрагента на уровне типа" имеет значение false</li>
     * <li>Перейти на карточку scClass, открыть форму редактирования атрибута Контрагент</li>
     * <li>Проверить, что признак "Обязательный" недоступен для изменения и имеет значение true</li>
     * <li>Нажать Отмена</li>
     * <li>Перейти в Параметры запросов<li>
     * <li>Открыть форму редактирования настроек поля "Контрагент"</li>
     * <li>Включить "Управлять обязательностью контрагента на уровне типа"</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Перейти на карточку scClass, открыть форму редактирования атрибута Контрагент</li>
     * <li>Проверить, что признак "Обязательный" доступен для изменения</li>
     * <li>Нажать Отмена</li>
     * <li>Залогиниться под сотрудником</li>
     * <li>Нажать на кнопку верхнего меню addButton</li>
     * <li>Установить в поле "Контрагент" значение "не указано"</li>
     * <li>Проверить, что в поле Соглашение/Услуга присутствует соглашение agreement</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать на кнопку "Изменить привязку"</li>
     * <li>Установить значение "не указано" в поле "Контрагент"</li>
     * <li>Проверить, что значение поля "Соглашение/Услуга" по-прежнему agreement, а поля "Тип запроса" scCase</li>
     * </ol>
     */
    @Test
    public void testEditClientRequiredEditable()
    {
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLNavSettings.editVisibilitySettings(true, true);
        DSLMenuItem.add(addButton);

        ContentForm selectClient = DAOContentAddForm.createSelectClient(scCase);
        DSLContent.add(selectClient);

        Bo client = SharedFixture.clientOu();
        Bo agreement = SharedFixture.clientAgreement();
        CatalogItem timeZone = SharedFixture.timeZone();
        Bo sc = DAOSc.create(scCase, client, agreement, timeZone);
        DSLBo.add(sc);

        // действия и проверки
        GUILogon.asSuper();

        GUINavigational.goToScParams();
        GUIScParams.assertClientRequiredEditableValue(false);

        GUIMetaClass.goToCard(scClass);
        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.resetInheriting();
        GUIAttribute.assertDisabledRequiredValue(true);
        GUIForm.cancelDialog();

        GUINavigational.goToScParams();
        GUIScParams.callEditClientForm();
        GUIScParams.clickClientRequiredEditable();
        GUIForm.applyModalForm();
        GUIScParams.assertClientAutoResolveValue(true);

        GUIMetaClass.goToCard(scClass);
        GUIAttribute.clickEdit(SysAttribute.client(scClass));
        GUIAttribute.resetInheriting();
        GUIAttribute.assertCheckboxEnabled(GUIAttribute.ID_REQUIRED_CHECKBOX_VALUE, true);
        GUIForm.cancelDialog();

        GUILogon.asTester();

        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase.getFqn());
        BoTree clientTree = new BoTree(GUIXpath.Id.CLIENT_VALUE, false);
        clientTree.assertPresentElement(true, CoreTree.EMPTY_OPTION);
        clientTree.setElementInSelectTree(CoreTree.EMPTY_OPTION);
        GUISelect.assertEnable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());

        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        clientTree.setElementInSelectTree(CoreTree.EMPTY_OPTION);
        GUISelect.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement);
        GUISelect.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase);
    }

    /**
     * Тестирование зависимости списка доступных представлений для редактирования от вложенности класса услуга
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00107
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Действия и проверки.</b>
     * <li>Зайти в интерфейсе администратора в Настройку бизнес процессов - параметры запросов.<li>
     * <li>Открыть форму редактирования Поля "Соглашение/Услуга"</li>
     * <li>Выбираем значение поля "Соглашение или услуга"</li>
     * <li>Проверяем наличие представления для редактирования "Иерархический список (соглашение и услуга)"</li>
     * <li>Выбираем значение поля "Услуга"</li>
     * <li>Проверяем отсутствие представления для редактирования "Иерархический список (услуги)"</li>
     * <li>Редактируем класс Услуга, делаем класс вложенным "в объект своего класса"</li>
     * <li>Переходим в интерфейсе администратора в Настройку бизнес процессов - параметры запросов.<li>
     * <li>Открыть форму редактирования Поля "Соглашение/Услуга"</li>
     * <li>Выбираем значение поля "Услуга"</li>
     * <li>Выбираем представление для редактирования "Иерархический список (услуги)"</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем, что представление для редактирования "Иерархический список (услуги)"</li>
     * </ol>
     */
    @Test
    public void testEditPrsDependsOnServiceNesting()
    {
        MetaClass serviceClass = DAOServiceCase.createClass();

        GUILogon.asSuper();

        GUINavigational.goToScParams();
        GUIScParams.callEditFieldAgreementOrSlmServiceForm();

        //проверяем название представления AgrServicePrs.TREE_LIST
        GUIScParams.selectAgrService(AgrService.BOTH);
        GUIScParams.selectAgrServicePrs(AgrServicePrs.TREE_LIST);
        GUITester.assertValue(GUIScParams.AGREEMENT_SERVICE_EDIT_PRS_INPUT, AgrServicePrs.TREE_LIST.getTitle());

        //проверяем отсутствие представления AgrServicePrs.SERVICE_TREE
        GUIScParams.selectAgrService(AgrService.SERVICE);
        GUISelect.assertNotDisplayed(GUIScParams.AGREEMENT_SERVICE_EDIT_PRS_INPUT,
                AgrServicePrs.SERVICE_TREE.getCode());
        GUIForm.cancelDialog();

        // редактируем вложенность услуги
        DSLMetaClass.editParentRelFqn(serviceClass, serviceClass.getFqn());

        tester.refresh();
        GUINavigational.goToScParams();
        GUIScParams.callEditFieldAgreementOrSlmServiceForm();

        //проверяем наличие представления AgrServicePrs.SERVICE_TREE
        GUIScParams.selectAgrService(AgrService.SERVICE);
        GUIScParams.selectAgrServicePrs(AgrServicePrs.SERVICE_TREE);
        GUIForm.applyModalForm();

        GUIScParams.assertAgrServicePrs(AgrServicePrs.SERVICE_TREE);
    }

    /**
     * Редактирование параметров запроса который нельзя отредактировать из за некорректной привязки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Зарегистрировать sc типа scCase на employee/agreement</li>
     * <li>Для компании создать контент content адвлист список запросов типа scCase</li>
     * <li>Для scCase создать контент scParam с атрибутами: контактный email</li>
     * <li>Для scCase создать контент commentList</li>
     * <li>Для scCase создать контент fileList</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в интерфейсе администратора в Настройку бизнес процессов - параметры запросов.<li>
     * <li>Изменить Значение поля "Соглашение/Услуга" на Услуга</li>
     * <br>
     * <b>Проверки</b>
     * <li>Из контента content  нельзя сменить статус, привязку, тип, ответственного</li>
     * <li>Из контента content  можно добавить комментарий</li>
     * <li>Из карточки запроса нельзя сменить статус, привязку, тип, ответственного</li>
     * <li>Из карточки запроса можно добавить комментарий, файл</li>
     * </ol>
     */
    @Test
    public void testNotEditScParam()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase1, scCase2, employeeCase);
        //Создаем статус запроса
        BoStatus registered = DAOBoStatus.createRegistered(scCase1.getFqn());
        BoStatus status = DAOBoStatus.createUserStatus(scCase1.getFqn());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(registered, status, registered);
        //Создаем элементы справочников
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем объекты
        Bo agreement = SharedFixture.agreement();
        Bo team = SharedFixture.team();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(employee, service);
        DSLSlmService.addScCases(service, scCase1, scCase2);
        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addToRecipients(agreement, employee);
        DSLEmployee.addToTeams(employee, team);
        Bo sc = DAOSc.create(scCase1, employee, agreement, timeZoneItem);
        DSLBo.add(sc);
        //Создаем контенты
        ContentForm content = DAOContentCard.createObjectList(DAORootClass.create().getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, DAOScCase.createClass(), DAOGroupAttr.createSystem(),
                scCase1);
        DSLContent.add(content);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase1.getFqn());
        DSLGroupAttr.add(attrGroup, DAOAttribute.createPseudo("Контактный e-mail", "clientEmail", null));
        ContentForm scParam = DAOContentCard.createPropertyList(scCase1, attrGroup);
        ContentForm commentList = DAOContentCard.createCommentList(scCase1.getFqn());
        ContentForm fileList = DAOContentCard.createFileList(scCase1.getFqn());
        DSLContent.add(scParam, commentList, fileList);
        //Выполнение действия;
        DSLScParams.editAgrService(AgrService.SERVICE);
        GUILogon.asTester();
        //Проверки
        //Смена статуса
        GUINavigational.goToOperatorUI();
        content.advlist().toolPanel().setRefreshList(false);
        content.advlist().mass().selectElements(sc);
        content.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUISelect.selectByXpath(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE,
                String.format(GUIXpath.Div.ID_PATTERN, status.getCode()));
        GUIForm.applyFormAssertError(
                String.format(ErrorMessages.CHANGE_STATUS_FAILED, status.getTitle(), scCase1.getTitle(), sc.getTitle())
                + ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        GUIForm.cancelForm();
        //Смена типа
        tester.refresh();
        content.advlist().mass().selectElements(sc);
        content.advlist().mass().clickOperation(MassOperation.CHANGE_CASE);
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUIForm.applyFormAssertError(ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        GUIForm.cancelForm();
        //Смена ответственного
        tester.refresh();
        content.advlist().mass().selectElements(sc);
        content.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISc.selectResponsible(employee, team);
        GUIForm.applyFormAssertError(ErrorMessages.RESPONSIBLE_WAS_NOT_CHANGED + "\"" + sc.getTitle() + "\": "
                                     + ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        //Смена привязки
        tester.refresh();
        content.advlist().mass().selectElements(sc);
        content.advlist().mass().clickOperation(MassOperation.CHANGE_ASSOCIATION);
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUIForm.cancelForm();
        //Добавление комментария
        tester.refresh();
        content.advlist().mass().selectElements(sc);
        content.advlist().mass().clickOperation(MassOperation.ADD_COMMENT);
        String comment = ModelUtils.createDescription();
        GUIRichText.sendKeys(GUIRichText.TEXT, comment);
        GUIForm.applyForm();
        GUIBo.goToCard(sc);
        Set<String> coomentUUIDs = GUIComment.getCommentUUIDs(commentList);
        Assert.assertEquals("Комментарий не добавлен", 1, coomentUUIDs.size());
        Assert.assertEquals("Полученный текст добавленного комментария не совпал с ожидаемым.", comment,
                GUICommentList.getCommentText(commentList, coomentUUIDs.iterator().next()));
        //Карточка запроса
        //Смена статуса
        tester.refresh();
        GUIButtonBar.changeState();
        GUISelect.select(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE, status.getCode());
        GUIForm.applyFormAssertError(
                String.format(ErrorMessages.CHANGE_STATUS_FAILED, status.getTitle(), scCase1.getTitle(), sc.getTitle())
                + ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        //Смена типа
        tester.refresh();
        GUIButtonBar.changeCase();
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUIForm.applyFormAssertError(ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        //Смена ответственного
        tester.refresh();
        GUIButtonBar.changeResponsible();
        GUISc.selectResponsible(employee, team);
        GUIForm.applyFormAssertError("\"" + sc.getTitle() + "\": " + ErrorMessages.SERVICE_INVALID_AGR_SERVICE);
        //Смена привязки
        tester.refresh();
        GUIButtonBar.changeAssociation();
        tester.click(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE);
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        //Добавление комментария
        tester.refresh();
        comment = ModelUtils.createDescription();
        String commentUUID = GUIComment.add(commentList, comment, false);
        Assert.assertEquals("Полученный текст добавленного комментария не совпал с ожидаемым.", comment,
                GUICommentList.getCommentText(commentList, commentUUID));
        //Добавление файла
        String pathToFile = DSLFile.FILE_FOR_UPLOAD;
        GUIFileOperator.add(pathToFile, fileList);
        String fileName = Paths.get(pathToFile).toFile().getName();
        GUIFileList.assertFilePresence(fileList, fileName);
    }

    /**
     * Тестирование порядка полей контента "Выбор типа запроса" в А при изменении настройки "Выбирать сначала"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Зайти на форму добавления в классе Запрос</li>
     * <li>Проверить, что в контенте "Выбор типа запроса" порядок полей:
     * <ul>Тип запроса</ul>
     * <ul>Соглашение/Услуга</ul>
     * </li>
     * <li>Изменить параметр "Выбирать сначала" = "Соглашение/Услугу"</li>
     * <li>Зайти на форму добавления в классе Запрос</li>
     * <li>Проверить, что в контенте "Выбор типа запроса" порядок полей:
     * <ul>Соглашение/Услуга</ul>
     * <ul>Тип запроса</ul>
     * </li>
     * </ol>
     */
    @Test
    public void testOrderFields()
    {
        MetaClass scClass = DAOScCase.createClass();

        // Действия и проверки
        GUILogon.asSuper();
        DSLScParams.setOrderingSettings(OrderScFields.CASE);
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.NEWENTRYFORM);

        GUITester.assertLocation(GUIXpath.Any.CAPTION_CASE_CONTAINS, GUIXpath.Any.CAPTION_AND_CONTAINS_TEXT_AGREEMENT,
                "Первым должно отображаться поле Тип объекта, затем поле Соглашение/услуга");

        DSLScParams.setOrderingSettings(OrderScFields.AGREEMENTSERVICE);
        tester.refresh();
        GUITester.assertLocation(GUIXpath.Any.CAPTION_AND_CONTAINS_TEXT_AGREEMENT, GUIXpath.Any.CAPTION_CASE_CONTAINS,
                "Первым должно отображаться поле Соглашение/услуга, затем поле Тип объекта");
    }

    /**
     * Тестирование автоматического удаления списка вложенных объектов услуг при выключении вложенности класса Услуга
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00107
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Настроить вложенность "в объект своего класса" для класса Услуга</li>
     * <li>Создать тип услуги serviceCase</li>
     * <li>Создать услугу service1 типа serviceCase</li>
     * <li>Создать контент childList "Список вложенных объектов" в типе serviceCase</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Зайти в интерфейс оператора<li>
     * <li>Перейти на карточку услуги service1</li>
     * <li>Проверить, что список childList отображается</li>
     * <li>Убрать вложенность класса Услуга</li>
     * <li>Перейти на карточку услуги service1</li>
     * <li>Проверить, что адвлист childList отсутствует</li>
     * </ol>
     */
    @Test
    public void testRemoveChildListsOnSetServiceNotNested()
    {
        MetaClass serviceClass = DAOServiceCase.createClass();
        MetaClass serviceCase = SharedFixture.slmCase();
        DSLMetaClass.editParentRelFqn(serviceClass, serviceClass.getFqn());

        Bo service1 = DAOService.create(serviceCase);
        DSLBo.add(service1);

        ContentForm childList = DAOContentCard.createChildObjectAdvlist(serviceCase.getFqn(), serviceClass,
                DAOGroupAttr.createSystem(serviceClass));
        DSLContent.add(childList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(serviceCase, MetaclassCardTab.OBJECTCARD));

        // действия и проверки
        GUILogon.asTester();

        GUIBo.goToCard(service1);
        childList.advlist().asserts().presence();

        DSLMetaClass.editParentRelFqn(serviceClass, null);

        tester.refresh();
        GUIBo.goToCard(service1);
        childList.advlist().asserts().absence();
    }

    /**
     * Тестирование представлений поля "Соглашение/услуга": соглашение, Плоский список
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service типа serviceCase </li>
     * <li>Связать соглашение agreement с услугой service</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связать сотрудника employee с соглашением agreement</li>
     * <li>Параметры запроса: "Соглашение/Услуга" = Соглашение, Представление: Плоский список</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Для типа employeeCase выбрать параметры запроса по умолчанию: agreement</li>
     * <br>
     * <b>Проверка</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список
     * (проверяется так: в выпадающем списке есть пункт с названием "Соглашения" - ничем другим эти списки не
     * отличаются)<li>
     * <li>В списке нет услуги service</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть  форму регистрации запроса  с карточки employee </li>
     * <br>
     * <b>Проверки</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список (Проверяется так же как и выше)</li>
     * <li>Соглашение/Услуга = agreement </li>
     * <li>В списке нет услуги service</li>
     * </ol>
     */
    @Test
    public void testScParamAgrFlat()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addRecipients(employee, agreement);
        DSLSlmService.addScCases(service, scCase);
        GUILogon.asSuper();
        GUIScParams.editScParams(AgrService.AGREEMENT, AgrServicePrs.LIST);

        //Привязка по умолчанию
        GUIMetaClass.goToCard(employeeCase);
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Таким образом проверяем, что вид выпадашки- "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements");
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getUuid(),
                service.getTitle());

        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUIForm.applyModalForm();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getTitle());

        //Таким образом проверяем, что вид выпадашки-  "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements");
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getUuid(),
                service.getTitle());
    }

    /**
     * Тестирование представлений поля "Соглашение/услуга": соглашение или услуга, Плоский список
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service типа serviceCase </li>
     * <li>Связать соглашение agreement с услугой service</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связать сотрудника employee с соглашением agreement</li>
     * <li>Параметры запроса: "Соглашение/Услуга" = Соглашение или услуга, Представление: Плоский список</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Для типа employeeCase выбрать параметры запроса по умолчанию: service</li>
     * <br>
     * <b>Проверка</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список
     * (проверяется так: в выпадающем списке есть пункт с названием "Соглашения" - ничем другим эти списки не
     * отличаются)<li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть  форму регистрации запроса  с карточки employee </li>
     * <br>
     * <b>Проверки</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список (Проверяется так же как и выше)</li>
     * <li>Соглашение/Услуга = service </li>
     * </ol>
     */
    @Test
    public void testScParamAgrOrSlmFlat()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addRecipients(employee, agreement);
        DSLSlmService.addScCases(service, scCase);
        GUILogon.asSuper();
        GUIScParams.editScParams(null, AgrServicePrs.LIST);

        //Привязка по умолчанию
        GUIMetaClass.goToCard(employeeCase);
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Таким образом проверяем, что вид выпадашки- "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements");
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "services");

        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUIForm.applyModalForm();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle());

        //Таким образом проверяем, что вид выпадашки-  "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements");
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "services");
        GUISelect.assertEnable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
    }

    /**
     * Тестирование представлений поля "Соглашение/услуга": соглашение или услуга, иерархический список
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service типа serviceCase </li>
     * <li>Связать соглашение agreement с услугой service</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связать сотрудника employee с соглашением agreement</li>
     * <li>Параметры запроса: "Соглашение/Услуга" = Соглашение или услуга, Представление: Иерархический список</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Для типа employeeCase выбрать параметры запроса по умолчанию: service</li>
     * <br>
     * <b>Проверка</b>
     * <li>Выпадающий список Соглашение/Услуга в иерархическом виде
     * (проверяется так: в выпадающем списке нет пункта с названием "Соглашения" - ничем другим эти деревья не
     * отличаются)<li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть  форму регистрации запроса  с карточки employee </li>
     * <br>
     * <b>Проверки</b>
     * <li>Выпадающий список Соглашение/Услуга в иерархическом виде  (Проверяется так же как и выше)</li>
     * <li>Соглашение/Услуга = service </li>
     * </ol>
     */
    @Test
    public void testScParamAgrOrSlmHierar()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addRecipients(employee, agreement);
        DSLSlmService.addScCases(service, scCase);

        //Значение параметров привязки запроса не редактируется, тк соглашение или услуга и иерархический список-
        // значения по умолчанию
        GUILogon.asSuper();
        //Привязка по умолчанию
        GUIMetaClass.goToCard(employeeCase);
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Таким образом проверяем, что вид выпадашки- не "Плоский список"
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements",
                "Соглашения");

        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUIForm.applyModalForm();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle());

        //Таким образом проверяем, что вид выпадашки- не "Плоский список"
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "agreements",
                "Соглашения");
        GUISelect.assertEnable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
    }

    /**
     * Выбор соглашения из каталога
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать тип соглашение agreementCase</li>
     * <li>Создать agreement1..2 </li>
     * <li>Для agreementCase создать папку folder, поместить agreement1  в folder</li>
     * <li>на форме добавления scCase создать контент Выбор типа запроса scCaseSelect</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Соглашение: Выбор из каталога
     * Представление для редактирования: выбор из каталога</li>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку ou</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect открываем выбор соглашения, проверяем, что присутствует folder и agreement находится в
     * folder</li>
     * </ol>
     */
    @Test
    public void testScParamGetFromCatalog()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase, agreementCase);
        //Создаем элементы справочников
        CatalogItem serviceItem = SharedFixture.serviceTime();

        Folder folder = DAOFolder.create(DAOAgreementCase.createClass());
        DSLFolder.add(folder);

        //Создаем объекты
        Bo agreement1 = DAOAgreement.create(agreementCase, serviceItem, serviceItem);
        Bo agreement2 = DAOAgreement.create(agreementCase, serviceItem, serviceItem);
        DSLBo.add(agreement1, agreement2);

        Attribute folders = SysAttribute.folders(agreementCase);
        folders.setValue(Json.listToString(folder.getUuid()));
        DSLBo.editAttributeValue(agreement1, folders);

        //Создаем запросы
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement1, ou);
        DSLAgreement.addToRecipients(agreement2, ou);

        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        //Выполнение действия
        GUILogon.asSuper();
        GUINavigational.goToScParams();
        GUIScParams.callEditFieldAgreementOrSlmServiceForm();
        AgrService value = AgrService.AGREEMENT;
        GUIScParams.selectAgrService(value);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_EDIT_PRS_VALUE, "FoldersTree");
        GUIForm.applyModalForm();
        //Проверки Первая
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        MetaTree tree = new MetaTree("gwt-debug-agreementServiceProperty-value");
        tree.showTree();
        tester.click("//span[@id='" + folder.getUuid() + "']/../../../div/img");
        Assert.assertTrue("Отсутствует папка",
                tester.waitAppear(GUIXpath.Div.VALUE_CELL_TREE + "//*[@id='" + folder.getUuid() + "']"));
        Assert.assertTrue("Отсутствует соглашение в папке",
                tester.waitAppear(GUIXpath.Div.VALUE_CELL_TREE + "//*[@id='" + agreement1.getUuid() + "']"));
    }

    /**
     * Фильтрация соглашений и услуг
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать тип услуги serviceCase1..2</li>
     * <li>Создать service1..2 (по одной каждого типа)</li>
     * <li>Связать service1..2  с соглашением по умолчанию</li>
     * <li>Создать запрос sc</li>
     * <li>на форме добавленияscCase создать контент Выбор типа запроса scCaseSelect</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Услуга: Выбор из каталога
     * Фильтрация услуг при редактировании - да</li>
     * <li>Скрипт: </li>
     * <pre>
     *  if (subject == null) return [] as List;
     *  def permittedFqns = [api.types.newClassFqn('slmService$slmService1')] as List;
     *  return utils.find('slmService',['metaClass':permittedFqns]);
     * </pre>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employee</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect проверяем что присутствует только одна услуга service1</li>
     * <br>
     * <li>Заходим в карточку sc</li>
     * <li>Нажимаем кнопку Сменить привязку</li>
     * <li>В выпадающем списке Соглашение/Услуга присутствует только одна услуга service1</li>
     * </ol>
     */
    @Test
    public void testScParamScriptFiltr()
    {
        //Очистка
        Cleaner.afterTest(() ->
        {
            DSLScParams.setDefaultScParameters();
            DSLScriptInfo.removeUnusedScripts();
        });
        //Подготовка
        //Настраиваем форму добавления в классе Запрос
        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass serviceCase1 = DAOServiceCase.create();
        MetaClass serviceCase2 = DAOServiceCase.create();
        DSLMetaClass.add(employeeCase, scCase, serviceCase1, serviceCase2);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        Bo agreement = SharedFixture.agreement();
        Bo service1 = DAOService.create(serviceCase1);
        Bo service2 = DAOService.create(serviceCase2);
        DSLBo.add(service1, service2);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        DSLAgreement.addServices(agreement, service1, service2);

        DSLSlmService.addScCases(service1, scCase);
        DSLSlmService.addScCases(service2, scCase);

        Bo sc = DAOSc.createWithService(scCase, employee, agreement, service1, timeZoneItem);
        DSLBo.add(sc);

        String script =
                "if (subject == null) return [] as List; return utils.find('slmService',['metaClass': api.types"
                + ".newClassFqn('"
                + serviceCase1.getFqn() + "')]);";

        //Выполнение действия
        GUILogon.asSuper();
        GUINavigational.goToScParams();
        GUIScParams.callEditFieldAgreementOrSlmServiceForm();
        AgrService value = AgrService.SERVICE;
        GUIScParams.selectAgrService(value);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_EDIT_PRS_VALUE, "List");
        tester.setCheckbox(GUIXpath.Input.FILTER_SERVICES_VALUE_INPUT, true);
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        GUIScriptComponentEdit componentEdit = new GUIScriptComponentEdit(
                GUIXpath.Any.SERVICES_FILTRATION_SCRIPT_VALUE);
        componentEdit.fillSingletoneNewScript(scriptInfo);
        GUIForm.applyModalForm();
        //Проверки Первая
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        tester.click("//div[@id='" + scCaseSelect.getXpathId() + "']"
                     + GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE);
        Assert.assertTrue("Отсутствует услуга " + service1.getTitle(),
                tester.waitAppear(GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id='" + agreement.getUuid() + ":"
                                  + service1.getUuid() + "']//span[text()='" + service1.getTitle() + "']"));
        Assert.assertTrue("Присутствует услуга" + service2.getTitle(),
                tester.waitDisappear(GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id='" + agreement.getUuid() + ":"
                                     + service2.getUuid() + "']//span[text()='" + service2.getTitle() + "']"));

        //Вторая проверка
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click("//div[@id='gwt-debug-agreementServiceProperty-value']//input");
        Assert.assertTrue("Отсутствует услуга " + service1.getTitle(),
                tester.waitAppear(GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id='" + agreement.getUuid() + ":"
                                  + service1.getUuid() + "']//span[text()='" + service1.getTitle() + "']"));
        Assert.assertTrue("Присутствует услуга" + service2.getTitle(),
                tester.waitDisappear(GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id='" + agreement.getUuid() + ":"
                                     + service2.getUuid() + "']//span[text()='" + service2.getTitle() + "']"));
    }

    /**
     * Тестирование фильтрации соглашений для представления "Услуга"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>На форме добавления класса "Запрос" создать контент Выбор типа запроса scCaseSelect</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать соглашения agreement1 и agreement2, услугу service1</li>
     * <li>Связать service1 с соглашениями agreement1, agreement2</li>
     * <li>Связать service1 с типом запроса scCase</li>
     * <li>Создать запрос sc</li>
     * <li>Сделать employee получателем услуги service1</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Настроить параметры запроса:
     * Услуга: Иерархический список
     * Фильтрация соглашений при редактировании - да</li>
     * <li>Скрипт: </li>
     * <pre>
     *  if (subject == null) return [] as List;
     *  return ['agreement1.getUuid()'];
     * </pre>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employee</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <li>В scCaseSelect проверяем, что присутствует только услуга service1 только по соглашению agreement1, по
     * соглашению agreement2 отсутствует</li>
     * <br>
     * <li>Заходим в карточку sc</li>
     * <li>Нажимаем кнопку Сменить привязку</li>
     * <li>В выпадающем списке Соглашение/Услуга присутствует только услуга service1 только по соглашению agreement1,
     * по соглашению agreement2 отсутствует</li>
     * </ol>
     */
    @Test
    public void testScParamSelectServiceFilterAgreements()
    {
        //Очистка
        Cleaner.afterTest(() ->
        {
            DSLScParams.setDefaultScParameters();
            DSLScriptInfo.removeUnusedScripts();
        });
        //Подготовка
        //Настраиваем форму добавления в классе Запрос
        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(DAOScCase.createClass());
        DSLContent.add(scCaseSelect);

        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        Bo agreement1 = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        Bo agreement2 = DAOAgreement.createWithRules(SharedFixture.agreementCase(), SharedFixture.serviceTime(),
                SharedFixture.serviceTime(), SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        Bo service1 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement1, agreement2, service1);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement1, employee);
        DSLAgreement.addToRecipients(agreement2, employee);

        DSLAgreement.addServices(agreement1, service1);
        DSLAgreement.addServices(agreement2, service1);

        DSLSlmService.addScCases(service1, scCase);

        Bo sc = DAOSc.createWithService(scCase, employee, agreement1, service1, timeZoneItem);
        DSLBo.add(sc);

        String agreementScript = String.format("if (subject == null) return []; return ['%s'];", agreement1.getUuid());
        ScriptInfo agreementScriptInfo = DAOScriptInfo.createNewScriptInfo(agreementScript);
        DSLScriptInfo.addScript(agreementScriptInfo);

        //Выполнение действия
        GUILogon.asSuper();
        GUINavigational.goToScParams();
        GUIScParams.callEditFieldAgreementOrSlmServiceForm();
        GUIScParams.selectAgrService(AgrService.SERVICE);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_EDIT_PRS_VALUE, "TreeList");

        tester.setCheckbox(GUIXpath.Input.FITER_AGREEMENTS_VALUE_INPUT, true);
        GUIScriptComponentEdit agreementsComponentEdit = new GUIScriptComponentEdit(
                GUIXpath.Any.AGREEMENTS_FILTRATION_SCRIPT_VALUE);
        agreementsComponentEdit.selectScriptByName(agreementScriptInfo);

        GUIForm.applyModalForm();

        //Проверки
        ScParameters scParams = DSLScParams.getParameters();
        Assert.assertEquals(agreementScriptInfo.getCode(), scParams.getAgreementFiltrationScript());

        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        String xpathElement = GUIXpath.Div.POPUP_LIST_SELECT + "//div[@id='%s:%s']//span[text()='%s']";

        tester.click("//div[@id='" + scCaseSelect.getXpathId() + "']"
                     + GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE);
        Assert.assertTrue("Отсутствует услуга " + service1.getTitle() + " или соглашение " + agreement1.getTitle(),
                tester.waitAppear(xpathElement, agreement1.getUuid(), service1.getUuid(), service1.getTitle()));
        Assert.assertTrue("Присутствует соглашение " + agreement2.getTitle(),
                tester.waitDisappear(xpathElement, agreement2.getUuid(), service1.getUuid(), service1.getTitle()));

        //Вторая проверка
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click("//div[@id='gwt-debug-agreementServiceProperty-value']//input");
        Assert.assertTrue("Отсутствует услуга " + service1.getTitle() + " или соглашение " + agreement1.getTitle(),
                tester.waitAppear(xpathElement, agreement1.getUuid(), service1.getUuid(), service1.getTitle()));
        Assert.assertTrue("Присутствует соглашение " + agreement2.getTitle(),
                tester.waitDisappear(xpathElement, agreement2.getUuid(), service1.getUuid(), service1.getTitle()));
    }

    /**
     * Тестирование представлений поля "Соглашение/услуга": услуга, Плоский список
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service типа serviceCase </li>
     * <li>Связать соглашение agreement с услугой service</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связать сотрудника employee с соглашением agreement</li>
     * <li>Параметры запроса: "Соглашение/Услуга" = Соглашение или услуга, Представление: Плоский список</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Для типа employeeCase выбрать параметры запроса по умолчанию: service</li>
     * <br>
     * <b>Проверка</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список
     * (проверяется так: в выпадающем списке есть пункт с названием "Услуги" - ничем другим эти списки не отличаются)
     * <li>
     * <li>В списке нет соглашения agreement</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть  форму регистрации запроса  с карточки employee </li>
     * <br>
     * <b>Проверки</b>
     * <li>Представление выпадающего списка Соглашение/Услуга Плоский список (Проверяется так же как и выше)</li>
     * <li>Соглашение/Услуга = service </li>
     * </ol>
     */
    @Test
    public void testScParamSlmFlat()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addRecipients(employee, agreement);
        DSLSlmService.addScCases(service, scCase);
        GUILogon.asSuper();
        GUIScParams.editScParams(AgrService.SERVICE, AgrServicePrs.LIST);

        //Привязка по умолчанию
        GUIMetaClass.goToCard(employeeCase);
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Таким образом проверяем, что вид выпадашки- "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "services");
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid(),
                agreement.getTitle());

        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUIForm.applyModalForm();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle());

        //Таким образом проверяем, что вид выпадашки-  "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "services");
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid(),
                agreement.getTitle());
    }

    /**
     * Тестирование представлений поля "Соглашение/услуга": услуга, иерархический список
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement типа agreementCase</li>
     * <li>Создать услугу service типа serviceCase </li>
     * <li>Связать соглашение agreement с услугой service</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В рамках service доступен тип запроса scCase</li>
     * <li>Связать сотрудника employee с соглашением agreement</li>
     * <li>Параметры запроса: "Соглашение/Услуга" = Соглашение или услуга, Представление: иерархический список</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Для типа employeeCase выбрать параметры запроса по умолчанию: service</li>
     * <br>
     * <b>Проверка</b>
     * <li>Представление выпадающего списка Соглашение/Услуга иерархический список
     * (проверяется так: в выпадающем списке есть соглашение agreement, но оно задизейблено)</lidsyjcj>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть  форму регистрации запроса  с карточки employee </li>
     * <br>
     * <b>Проверки</b>
     * <li>Представление выпадающего списка Соглашение/Услуга иерархический список (Проверяется так же как и выше)</li>
     * <li>Соглашение/Услуга = service </li>
     * </ol>
     */
    @Test
    public void testScParamSlmHierar()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addServices(agreement, service);
        DSLAgreement.addRecipients(employee, agreement);
        DSLSlmService.addScCases(service, scCase);
        GUILogon.asSuper();
        GUIScParams.editScParams(AgrService.SERVICE, null);

        //Привязка по умолчанию
        GUIMetaClass.goToCard(employeeCase);
        tester.click(ActionType.EDIT.getXpath());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        //Проверка
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());

        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUIForm.applyModalForm();
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle());

        //Таким образом проверяем, что вид выпадашки-  "Плоский список"
        GUISelect.assertDisable(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
    }

    /**
     * Тестирование автоматической смены представления с иерархии услуг на плоский список при выключении вложенности
     * класса Услуга
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00107
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать две услуги service1 и service2</li>
     * <li>Создать отдел ou и соглашение agreement</li>
     * <li>Создать тип запроса scCase, добавить связь с ним в service1 и service2</li>
     * <li>Настроить вложенность "в объект своего класса" для класса Услуга</li>
     * <li>Настроить представление для редактирования "Иерархический список (услуги)" поля Соглашение/Услуга</li>
     * <li>Сделать ou получателем соглашения agreement</li>
     * <li>Услуги service1, service2 привязать к соглашению agreement</li>
     * <li>Вложить услугу service2 в service1</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Зайти в интерфейс оператора<li>
     * <li>Перейти на карточку отдела ou, нажать на кнопку "Добавить запрос" в панели действий</li>
     * <li>Проверить, что в поле Соглашение/Услуга присутсвуют опции service1 и service2, service2 вложена в
     * service1</li>
     * <li>Убрать вложенность класса Услуга</li>
     * <li>Перейти на карточку отдела ou, нажать на кнопку "Добавить запрос" в панели действий</li>
     * <li>Проверить, что в поле Соглашение/Услуга присутсвуют опции service1 и service2, service2 не вложена в
     * service1</li>
     * </ol>
     */
    @Test
    public void testServiceTreePrsToFlatListOnSetServiceNotNested()
    {
        MetaClass serviceClass = DAOServiceCase.createClass();
        MetaClass serviceCase = SharedFixture.slmCase();
        MetaClass scCase = SharedFixture.scCase();

        DSLMetaClass.editParentRelFqn(serviceClass, serviceClass.getFqn());

        Bo service1 = DAOService.create(serviceCase);
        Bo service2 = DAOService.create(serviceCase);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        DSLBo.add(service1, service2, ou, agreement);

        DSLSlmService.addScCases(service1, scCase);
        DSLSlmService.addScCases(service2, scCase);

        DSLAgreement.addRecipients(ou, agreement);
        DSLAgreement.addServices(agreement, service1, service2);

        Attribute parent = SysAttribute.parent(serviceClass);
        parent.setValue(service1.getUuid());
        DSLBo.editAttributeValue(service2, parent);

        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.SERVICE_TREE);

        // действия и проверки
        GUILogon.asTester();

        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();

        GUISelect.assertDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service1.getUuid());
        GUISelect.assertDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                service1.getUuid() + "-" + agreement.getUuid() + ":" + service2.getUuid());
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service2.getUuid());
        GUIForm.cancelForm();

        DSLMetaClass.editParentRelFqn(serviceClass, null);

        tester.refresh();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();

        GUISelect.assertDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service1.getUuid());
        GUISelect.assertDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service2.getUuid());
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                service1.getUuid() + "-" + agreement.getUuid() + ":" + service2.getUuid());
    }

    /**
     * Тестирование вычисления значения по умолчанию для атрибута Нормативное время обработки при смене привязки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00046
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00488
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00325
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114145140
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить тип запроса scCase, в нем scCase1, scCas2</li>
     * <li>Для атрибута "Нормативное время обработки" типа scCase1 установить скрипт для вычисления значения по
     * умолчанию:
     * <pre>
     * def resTime = api.types.newDateTimeInterval(1, 'DAY')
     * if(oldSubject?.resolutionTime != null)
     * {
     *  if(oldSubject.resolutionTime.toMiliseconds() > resTime.toMiliseconds()) resTime = oldSubject.resolutionTime
     * }
     * return resTime</pre>
     * <li>Для атрибута "Нормативное время обработки" типа scCase2 установить скрипт для вычисления значения по
     * умолчанию:
     * <pre>
     * def resTime = api.types.newDateTimeInterval(2, 'DAY')
     * if(oldSubject?.resolutionTime != null)
     * {
     *  if(oldSubject.resolutionTime.toMiliseconds() > resTime.toMiliseconds()) resTime = oldSubject.resolutionTime
     * }
     * return resTime</pre>
     * </li>
     * <li>Для атрибута "Приоритет" типа scCase установить скрипт для вычисления значения по умолчанию:
     * <pre>return api.utils.get('priority', ["code" : "priorityItem"]);</pre>
     * </li>
     * <li>Создать элемент справочника Приоритеты priorityItem</li>
     * <li>Создать соглашения agreement1, agreement2</li>
     * <li>Добавить запрос sc типа scCase1</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Залогиниться под сотрудником, перейти на карточку запроса sc</li>
     * <li>Проверить, что "Нормативное время обработки" у запроса = 1 DAY</li>
     * <li>Нажать кнопку "Изменить привязку", выбрать тип scCase2, нажать сохранить</li>
     * <li>Проверить, что "Нормативное время обработки" у запроса = 1 DAY</li>
     * <li>Нажать кнопку "Изменить привязку", выбрать соглашение agreement2, нажать сохранить</li>
     * <li>Проверить, что "Нормативное время обработки" у запроса = 2 DAY</li>
     * <li>Нажать кнопку "Изменить привязку", выбрать соглашение agreement1, тип scCase1, нажать сохранить</li>
     * <li>Проверить, что "Нормативное время обработки" у запроса = 2 DAY</li>
     * </ol>
     */
    @Test
    public void testSystemAttributeForDefaultComputable()
    {
        // Подготовка
        CatalogItem priorityItem = DAOCatalogItem.createPriority(1);
        CatalogItem serviceItem = DAOCatalogItem.createServiceTime();
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone(TimeZones.MOSCOW, ModelUtils.createTitle());
        DSLCatalogItem.add(timeZoneItem, serviceItem, priorityItem);

        String resolutionTimeScript = "def resTime = api.types.newDateTimeInterval(%s, 'DAY') \n"
                                      + "if(oldSubject?.resolutionTime != null) \n" + "{ \n"
                                      + "  if(oldSubject.resolutionTime.toMiliseconds() > resTime.toMiliseconds()) "
                                      + "resTime = oldSubject"
                                      + ".resolutionTime\n"
                                      + "} \n" + "return resTime";

        ScriptInfo resolutionTimeScriptInfo1 = DAOScriptInfo
                .createNewScriptInfo(String.format(resolutionTimeScript, "1"));
        ScriptInfo resolutionTimeScriptInfo2 = DAOScriptInfo
                .createNewScriptInfo(String.format(resolutionTimeScript, "2"));

        String priorityScript = String.format("return api.utils.get('priority', [\"code\" : \"%s\"]);",
                priorityItem.getCode());
        ScriptInfo priorityScriptInfo = DAOScriptInfo.createNewScriptInfo(priorityScript);

        MetaClass scCase = DAOScCase.create();
        MetaClass scCase1 = DAOScCase.create(scCase);
        MetaClass scCase2 = DAOScCase.create(scCase);
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetainfo.add(scCase, scCase1, scCase2, agreementCase);

        Attribute resolutionTime1 = SysAttribute.resolutionTime(scCase1);
        Attribute resolutionTime2 = SysAttribute.resolutionTime(scCase2);
        Attribute priority = SysAttribute.priority(scCase);

        //Выполнение действий и проверки
        DSLScriptInfo.addScript(resolutionTimeScriptInfo1);
        DSLScriptInfo.addScript(resolutionTimeScriptInfo2);
        DSLScriptInfo.addScript(priorityScriptInfo);
        DAOAttribute.changeToDefaultComputable(resolutionTime1, resolutionTimeScriptInfo1);
        DAOAttribute.changeToDefaultComputable(resolutionTime2, resolutionTimeScriptInfo2);
        DAOAttribute.changeToDefaultComputable(priority, priorityScriptInfo);
        DSLAttribute.edit(resolutionTime1, resolutionTime2, priority);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement1 = DAOAgreement.create(agreementCase, serviceItem, serviceItem);
        Bo agreement2 = DAOAgreement.create(agreementCase, serviceItem, serviceItem);
        DSLBo.add(agreement1, agreement2, employee);
        DSLAgreement.addRecipients(employee, agreement1, agreement2);

        Bo sc = DAOSc.create(scCase1, employee, agreement1, timeZoneItem);
        DSLBo.add(sc);

        priority.setValue(priorityItem.getUuid());
        resolutionTime1.setValue("1 DAY");
        DSLBo.assertAttributes(sc, resolutionTime1);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.selectScCase(scCase2);
        GUIForm.applyModalForm();
        DAOBo.setCase(sc, scCase2);

        resolutionTime1.setValue("1 DAY");
        DSLBo.assertAttributes(sc, resolutionTime1);

        GUIButtonBar.changeAssociation();
        GUISc.selectAssociation(agreement2, null);
        GUIForm.applyModalForm();

        resolutionTime1.setValue("2 DAY");
        DSLBo.assertAttributes(sc, resolutionTime1);

        GUIButtonBar.changeAssociation();
        GUISc.selectAssociation(agreement1, null);
        GUISc.selectScCase(scCase1);
        GUIForm.applyModalForm();
        DAOBo.setCase(sc, scCase1);

        resolutionTime1.setValue("2 DAY");
        DSLBo.assertAttributes(sc, resolutionTime1);
    }

    /**
     * Тестирование значения subject при выполнении скрипта фильтрации типов при смене привязки запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00280
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70315328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить тип запроса scCase</li>
     * <li>В параметрах запроса установить "Фильтрация типов при редактировании" = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *         if (!subject)
     *         {
     *              return []
     *         }
     *         if (!subject.UUID)
     *         {
     *              logger.error("Subject UUID is null!")
     *              return []
     *         }
     *         logger.error('Subject is correct')
     *         return api.filtration.disableFiltration()
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Создать объект boSc типа scCase</li>
     * <br>
     * <b>Выполнения действий и проверки</b>
     * <li>Зайти в ИО под сотрудником</li>
     * <li>Перейти на карточку запроса boSc</li>
     * <li>Нажать на кнопку "Изменить привязку"</li>
     * <li>Проверить, что в выпадающем списке есть тип запроса scCase</li>
     * </ol>
     */
    @Test
    public void testSubjectValueOnChangeAssociationForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        String scriptBody = " if (!subject) {\n"
                            + "  return []\n"
                            + "}\n"
                            + "if (!subject.UUID) {\n"
                            + "  logger.error(\"Subject UUID is null!\")\n"
                            + "  return []\n"
                            + "}\n"
                            + "logger.error('Subject is correct')\n"
                            + "return api.filtration.disableFiltration()";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        Bo boSc = DAOSc.create(scCase);
        DSLBo.add(boSc);

        //Выполнения действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(boSc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCasesPresent(scCase);
    }

    /**
     * Тестирование обновления списка используемых в фильтрации атрибутов,
     * если скрипт изменить через каталог скриптов (На примере зависимости поля
     * Соглашения/Услуги от Часового пояса)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$213085966
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запроса scCase, отдела ouCase, сотрудника employeeCase,
     * услуги serviceCase и соглашения agrCase</li>
     * <li>В параметрах запроса установить скрипт фильтрации соглашений
     * <pre>
     *      def ATTRS_FOR_UPDATE_ON_FORMS = []
     *      if (subject == null) {\n"
     *          return ATTRS_FOR_UPDATE_ON_FORMS
     *      }
     *      return []
     * </pre></li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать соглашение agreement и услугу service</li>
     * <li>Связать услугу с соглашением, а сотрудника сделать получателем соглашения</li>
     * <li>Тип запроса scCase связать с услугой service</li>
     * <li>Настроить параметры запроса по умолчанию для employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Под сотрудником employee зайти на форму добавления запроса</li>
     * <li>Проверить, что в поле Соглашение\Услуга Нет элементов для выбора</li>
     * <li>Указать часовой пояс (любой)</li>
     * <li>Проверить, что в поле Соглашение\Услуга всё так же Нет элементов для выбора</li>
     * <li>Через каталог скриптов изменить скрипт фильтрации соглашений
     * <pre>def ATTRS_FOR_UPDATE_ON_FORMS = ['timeZone']
     *      if (subject == null) {\n"
     *          return ATTRS_FOR_UPDATE_ON_FORMS
     *      }
     *      if (subject?.timeZone) {
     *          return ['" + agreement.getUuid() +  "']
     *      }
     *      return []</pre></li>
     * <li>Обновить страницу с формой добавления запроса</li>
     * <li>Проверить, что в поле Соглашение\Услуга Нет элементов для выбора</li>
     * <li>Указать часовой пояс (любой)</li>
     * <li>Проверить, что в поле Соглашение\Услуга значение изменилось на "Не указано"</li>
     * </ol>
     */
    @Test
    public void testChangeUsageAttrsFromScriptCatalog()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, serviceCase, employeeCase);

        String script = "def ATTRS_FOR_UPDATE_ON_FORMS = []\n"
                        + "if (subject == null) {\n"
                        + "return ATTRS_FOR_UPDATE_ON_FORMS\n"
                        + "}\n"
                        + "return []";

        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(script);
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.LIST, filterScript, null);

        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        DSLBo.add(agreement);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        DSLSlmService.addScCases(service, scCase);
        DSLSlmService.addAgreements(service, agreement);
        DSLMetaClass.setScParams(employeeCase, agreement, service, scCase);
        DSLAgreement.addToRecipients(agreement, employee);

        // Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.select(GUIXpath.InputComplex.TIME_ZONE_VALUE, timeZoneItem.getUuid());
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);

        script = "def ATTRS_FOR_UPDATE_ON_FORMS = ['timeZone']\n"
                 + "if (subject == null) {\n"
                 + "return ATTRS_FOR_UPDATE_ON_FORMS\n"
                 + "}\n"
                 + "if (subject?.timeZone) {\n"
                 + "  return ['" + agreement.getUuid() + "']\n"
                 + "}\n"
                 + "return []";
        filterScript.setBody(script);
        DSLScriptInfo.editScript(filterScript);

        tester.refresh();
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.select(GUIXpath.InputComplex.TIME_ZONE_VALUE, timeZoneItem.getUuid());
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.EMPTY_VALUE);
    }
}