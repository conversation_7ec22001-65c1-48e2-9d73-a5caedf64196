package ru.naumen.selenium.cases.admin.sets;

import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.admin.homepage.DSLHomePage;
import ru.naumen.selenium.casesutil.admin.homepage.GUINavHomePage;
import ru.naumen.selenium.casesutil.advimport.DSLAdvimport;
import ru.naumen.selenium.casesutil.advimport.GUIAdvimportList;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.Interval;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.contenttemplate.GUIContentTemplate;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.listtemplate.GUIListTemplate;
import ru.naumen.selenium.casesutil.mail.rules.GUIMailProcessorRule;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationForm;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationList;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.admin.DAOHomePage;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.HomePage;
import ru.naumen.selenium.casesutil.model.admin.HomePage.HomePageType;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.log.Constants.CategoryCode;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConfig;
import ru.naumen.selenium.casesutil.model.advimport.DAOAdvimport;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.contenttemplate.ContentTemplate;
import ru.naumen.selenium.casesutil.model.contenttemplate.DAOContentTemplate;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.listtemplate.DAOListTemplate;
import ru.naumen.selenium.casesutil.model.listtemplate.ListTemplate;
import ru.naumen.selenium.casesutil.model.mail.rules.DAOMailProcessorRule;
import ru.naumen.selenium.casesutil.model.mail.rules.MailProcessorRule;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.Strategy;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.styletemplate.DAOStyleTemplate;
import ru.naumen.selenium.casesutil.model.styletemplate.StyleTemplate;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.schedulertask.GUISchedulerTaskList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;
import ru.naumen.selenium.casesutil.script.GUIScriptModule;
import ru.naumen.selenium.casesutil.script.GUIScriptModulesList;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.casesutil.styletemplate.GUIStyleTemplate;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.RandomUtils;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование разметки настроек метаинформации комплектами в интерфесе администратора
 *
 * <AUTHOR>
 * @since 20.05.2024
 */
public class MarkMetainfoSettingsBySettingsSet1Test extends AbstractTestCase
{
    private static SettingsSet set1, set2;
    private static MetaClass userClass;
    private static final String X_FORM_SCRIPT_VALUE = String.format(GUIXpath.Any.ANY_VALUE, "edit-script");

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        set1 = DAOSettingsSet.createSettingsSet();
        set1.setTitle(ModelUtils.createTitle());
        set2 = DAOSettingsSet.createSettingsSet();
        set2.setTitle(ModelUtils.createTitle());
        DSLSettingsSet.add(set1, set2);

        userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);
    }

    /**
     * Тестирование разметки пользовательских справочников комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Пользовательские справочники", нажать кнопку добавить</li>
     * <li>Заполнить необходимые поля на форме добавления справочника</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан справочник userCatalog</li>
     * <li>Перейти на карточку справочника userCatalog</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у справочника userCatalog</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена таблица соответствий, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkCatalogWithSet()
    {
        //Выполнение действий и проверки
        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());
        userCatalog.setTitle(ModelUtils.createTitle(64));
        GUILogon.asSuper();
        GUICatalog.callAddForm();
        GUICatalog.fillUserCatalogFields(userCatalog);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        userCatalog.setExists(true);

        GUICatalog.goToCard(userCatalog);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUICatalog.openEditForm();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.CATALOG_EDIT);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен справочник '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                userCatalog.getTitle(), userCatalog.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки элемента пользовательского справочника комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский пользовательский справочник userCatalog</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку справочника userCatalog</li>
     * <li>Нажать кнопку "Добавить элемент"</li>
     * <li>Заполнить необходимые поля на форме добавления элемента справочника</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана элемент item</li>
     * <li>Перейти на карточку элемента item</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать на кнопку редактировать на карточке элемента item</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkCatalogItemWithSet()
    {
        //Подготовка
        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());
        DSLCatalog.add(userCatalog);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUICatalog.goToCard(userCatalog);

        CatalogItem item = DAOCatalogItem.createUser(userCatalog);
        GUICatalogItem.callAddForm(item);
        GUICatalogItem.setTitle(item.getTitle());
        GUICatalogItem.setCode(item.getCode());
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();

        GUICatalogItem.goToCard(item);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUICatalogItem.openEditForm();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки скриптовых модулей комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Каталог модулей", нажать кнопку "Добавить модуль"</li>
     * <li>Заполнить необходимые поля на форме добавления модуля</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан модуль module</li>
     * <li>Перейти на карточку скиптового модуля module</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у скриптового модуля module</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkScriptModuleWithSet()
    {
        //Выполнение действий и проверки
        ModuleConf module = DAOModuleConf.create();
        module.setRestAllowed("false");
        module.setModuleVersion("");

        GUILogon.asSuper();
        GUINavigational.goToScriptModules();
        GUIScriptModulesList.clickAddModule();
        GUIScriptModule.fillAddForm(module, true);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();

        module.setExists(true);

        GUIScriptModule.goToCard(module);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIScriptModule.clickEdit();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки задач планировщика комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Планировщик задач", нажать кнопку "Добавить задачу"</li>
     * <li>Заполнить необходимые поля на форме добавления задачи</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана задача task</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у задачи task</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkSchedulerTaskWithSet()
    {
        //Выполнение действий и проверки
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        SchedulerTaskScript task = DAOSchedulerTask.createScriptRule(script.getCode());
        GUILogon.asSuper();
        GUINavigational.goToScheduler();
        GUISchedulerTaskList.clickAddTask();
        task.setCode(UniqueRandomStringUtils.stringEn(5) + "_");
        GUISchedulerTaskList.fillExecuteScriptTaskFormAddScript(task, script);
        GUIScriptComponentEdit scriptComponentEdit = new GUIScriptComponentEdit(X_FORM_SCRIPT_VALUE);
        scriptComponentEdit.closeProperties();
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        task.setFqn(task.getTaskType() + "$" + task.getCode());
        task.setExists(true);
        script.setExists(true);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUISchedulerTaskList.editTask();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки расписания задач планировщика комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать задачу планировщика task</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку задачи task, нажать кнопку "Добавить"</li>
     * <li>Заполнить необходимые поля на форме добавления триггера</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан триггер trigger</li>
     * <li>Перейти на карточку триггера trigger</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у триггера trigger</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkSchedulerTaskTriggerWithSet()
    {
        //Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        SchedulerTaskScript task = DAOSchedulerTask.createScriptRule(script.getCode());
        DSLSchedulerTask.addTask(task);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUISchedulerTaskList.goToTaskCardWithRefresh(task);
        Trigger trigger = DAOSchedulerTask.createIntervalTrigger(task, Interval.SECOND, 15L,
                String.valueOf(System.currentTimeMillis()), Strategy.FROM_START);
        String pastDate = DateTimeUtils.formatTimeddMMyyyyHHmm(System.currentTimeMillis());

        tester.click(GUIXpath.Div.ADD);
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE, "PERIODIC");
        GUISchedulerTaskList.fillPeriodicIntervalTriggerForm(trigger, pastDate);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();

        Set<String> newIdSet = GUISchedulerTaskList.getRowIdsFromTriggersList();
        trigger.setId(newIdSet.iterator().next());

        GUISchedulerTaskList.goToTriggerCardWithRefresh(trigger);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        tester.click(GUIXpath.Div.EDIT);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки правила обработки почты комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти к списку правил обработки почты</li>
     * <li>Нажать кнопку "Добавить"</li>
     * <li>Заполнить необходимые поля на форме добавления правила</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано правило rule</li>
     * <li>Перейти на карточку правила rule</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у правила rule</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменено правило обработки входящей почты, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMailProcessRuleWithSet()
    {
        Cleaner.afterTest(DSLScriptInfo::removeUnusedScripts);
        //Выполнение действий и проверки
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        GUILogon.asSuper();
        GUINavigational.goToMailProcessors();
        Set<String> oldIdSet = GUISchedulerTaskList.getRowIdsFromSimpleList();
        MailProcessorRule rule = DAOMailProcessorRule.createMailProcessorRule();
        tester.click(GUIXpath.Div.ADD);
        GUIMailProcessorRule.fillMailProcessorFormWithAddScript(rule, script);
        GUIScriptComponentEdit scriptComponentEdit = new GUIScriptComponentEdit(X_FORM_SCRIPT_VALUE);
        scriptComponentEdit.closeProperties();
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        Set<String> newIdSet = GUISchedulerTaskList.getRowIdsFromSimpleList();
        newIdSet.removeAll(oldIdSet);
        rule.setCode(newIdSet.iterator().next());
        rule.setExists(true);

        GUIMailProcessorRule.goToMailRuleCard(rule);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        tester.click(GUIXpath.Div.EDIT);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.MAIL_RULES_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменено правило обработки входящей почты '%s':\n"
                                                   + "Комплект: '%s' -> '%s'.",
                rule.getTitle(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки элементов меню комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти к настройкам навигации</li>
     * <li>Нажать кнопку "Добавить элемент" в левом меню</li>
     * <li>Заполнить необходимые поля на форме добавления кнопки</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан элемент меню addButton</li>
     * <li>Перейти на карточку элемента левого меню addButton</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у элемента меню addButton</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены параметры элемента левого меню, в них
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMenuItemWithSet()
    {
        //Выполнение действий и проверки
        LeftMenuItem addButton = DAOLeftMenuItem.createAddButton(true, null, userClass);

        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        GUINavSettings.clickAddLeftMenuElement();
        String title = addButton.getTitle();
        GUIMenuItem.fillElementTitleField(title);
        GUIMenuItem.selectMenuItemType(MenuItem.MenuItemType.addButton);
        GUIMenuItem.selectMetaClassForAddButton(userClass.getClassTitle());
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();

        String code = GUINavSettings.getLeftMenuIdByTitle(title);
        addButton.setCode(code);
        addButton.setExists(true);

        GUIMenuItem.goToLeftMenuItemCard(addButton);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        tester.click(GUIXpath.Div.EDIT);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.NAVIGATION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменены параметры элемента левого меню '%s':\n"
                                                   + "Комплект: '%s' -> '%s'.",
                addButton.getTitle(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки плиток на панели быстрого доступа комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать элемент левого меню addButton</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти к настройкам навигации</li>
     * <li>Нажать кнопку "Добавить плитку" в панели быстрого доступа</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана плитка для элемента левого меню addButton</li>
     * <li>Нажать кнопку "Редактировать" у плитки для элемента меню addButton</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены параметры плитки левого меню, в них
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkQuickTileWithSet()
    {
        //Выполнение действий и проверки
        LeftMenuItem addButton = DAOLeftMenuItem.createAddButton(true, null, userClass);
        DSLLeftMenuItem.add(addButton);

        QuickAccessTile quickAccessTile = DAOQuickAccessTile.createQuickAccessTile(addButton, false);

        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        GUINavSettings.clickAddQuickTile();
        GUIMenuItem.selectLeftMenuItem(addButton);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();

        String idByTitle = GUINavSettings.getQuickTileIdByTitle(addButton.getTitle());
        quickAccessTile.setCode(idByTitle);
        quickAccessTile.setExists(true);

        GUIMenuItem.clickEditMenuItemQuickTile(addButton);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.NAVIGATION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменены параметры плитки левого меню '%s':\n"
                                                   + "Комплект: '%s' -> '%s'.",
                addButton.getTitle(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки хлебных крошек комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass cоздать атрибут типа "ссылка на бизнес объект" на класс "Сотрудник" attr</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти к настройкам навигации</li>
     * <li>Нажать кнопку "Добавить элемент" в навигационной цепочке "хлебные крошки"</li>
     * <li>Заполнить необходимые поля на форме добавления крошки</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана хлебная крошка crumb</li>
     * <li>Перейти на карточку хлебной крошки crumb</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у хлебной крошки crumb</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkBreadCrumbWithSet()
    {
        Attribute attr = DAOAttribute.createObjectLink(userClass, SharedFixture.employeeCase());
        DSLAttribute.add(attr);

        //Выполнение действий и проверки

        Crumb crumb = DAOBreadCrumb.create(userClass, attr);
        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        List<String> crumbCodes = DSLNavSettings.getCrumbCodes();
        GUINavSettings.clickAddBreadCrumb();
        GUINavSettings.fillCrumbForm(crumb);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        List<String> newCrumbCodes = DSLNavSettings.getCrumbCodes();
        newCrumbCodes.removeAll(crumbCodes);
        crumb.setCode(newCrumbCodes.get(0));
        crumb.setExists(true);

        GUINavSettings.goToCrumb(crumb);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        tester.click(GUIXpath.Div.EDIT);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки домашней страницы комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти к настройкам навигации</li>
     * <li>Нажать кнопку "Добавить элемент" в списке домашних страниц</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана домашняя страница customLink</li>
     * <li>Нажать кнопку "Редактировать" у домашней страницы customLink</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Перейти на карточку домашней страницы</li>
     * <li>Проверить, что на карточке выбран комплект set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены параметры плитки левого меню, в них
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkHomePageWithSet()
    {
        // Подготовка
        SharedFixture.secProfileLic();
        HomePage customLink1 = DAOHomePage.createCustomLink("customLinkText");
        DSLHomePage.add(customLink1);
        //Выполнение действий и проверки
        HomePage customLink = DAOHomePage.createCustomLink("customLinkText");

        SecurityRole absRole = SysRole.employee();
        SecurityGroup emplGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(emplGroup);
        SecurityProfile absoluteProfile = DAOSecurityProfile.create(true, emplGroup, absRole);
        DSLSecurityProfile.add(absoluteProfile);

        GUILogon.asSuper();
        GUINavHomePage
                .goToNavSettings()
                .clickAddHomePageItem()
                .selectHomeView(HomePageType.CUSTOM_LINK)
                .selectProfile(absoluteProfile.getCode());
        GUIForm.fillTitle(customLink.getTitle());
        GUIForm.fillAttribute("customLinkValue", ModelUtils.createText(5));
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();

        String idByTitle = GUINavHomePage.getHomePageIdByTitle(customLink.getTitle());
        customLink.setUuid(idByTitle);
        customLink.setExists(true);

        GUINavHomePage.goToNavSettings().clickEditHomePageItem(customLink);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        GUINavHomePage.goToHomePageCard(customLink);
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.NAVIGATION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменены параметры домашней страницы '%s':\n"
                                                   + "Комплект: '%s' -> '%s'.",
                customLink.getTitle(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки встроенных приложений комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Нажать кнопку "Добавить приложение"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано приложение embeddedApplication</li>
     * <li>Нажать кнопку "Редактировать" в строке приложения embeddedApplication</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Перейти на карточку приложения embeddedApplication</li>
     * <li>Проверить, что на карточке выбран комплект set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменено встроенное приложение, в нем
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkEmbeddedApplicationsWithSet()
    {
        //Выполнение действий и проверки
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication
                .createClientSideApplicationFromFile(DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        embeddedApplication.setEnable(false);

        GUILogon.asSuper();
        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.assertThatList();

        GUIEmbeddedApplicationList.clickAdd();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUIEmbeddedApplicationForm.fillAdd(embeddedApplication);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        embeddedApplication.setExists(true);
        GUIEmbeddedApplicationList.clickEdit(embeddedApplication);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();

        GUIEmbeddedApplicationForm.goToCard(embeddedApplication);
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.EMBEDDED_APPLICATION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменено встроенное приложение '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                embeddedApplication.getTitle(), embeddedApplication.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки конфигураций импорта комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Синхронизация" вкладка "Конфигурации"</li>
     * <li>Нажать кнопку "Добавить конфигурацию"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана конфигурация advConfig</li>
     * <li>Перейти на карточку конфигурации advConfig</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке advConfig</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена конфигурация импорта, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkAdvImportConfigWithSet()
    {
        //Выполнение действий и проверки
        SharedFixture.ouCase();
        AdvimportConfig advConfig = DAOAdvimport.createConfig(FileUtils.readAll(DSLAdvimport.CONFIG_1));
        GUILogon.asSuper();
        GUINavigational.goToAdvimport();
        GUIAdvimportList.clickAddConfig();
        GUIAdvimportList.fillForm(advConfig);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        advConfig.setExist(true);

        GUIAdvimportList.goToCardWithRefresh(advConfig);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIAdvimportList.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.CONFIGURATION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена конфигурация импорта '%s' ('%s'):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                advConfig.getTitle(), advConfig.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки структур комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Нажать кнопку "Добавить структуру"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана структура structuredObjectsView</li>
     * <li>Перейти на карточку структуры structuredObjectsView</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке structuredObjectsView</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена структура, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkStructureObjectViewWithSet()
    {
        // Выполнение действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToStructuredObjectsViews();
        GUIStructuredObjectsView.advlist().toolPanel().clickAdd();

        StructuredObjectsView structuredObjectsView = DAOStructuredObjectsView.create();
        GUIStructuredObjectsView.fillAddForm(structuredObjectsView);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        structuredObjectsView.setExists(true);

        GUIStructuredObjectsView.goToCard(structuredObjectsView);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIStructuredObjectsView.clickEdit();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.STRUCTURED_VIEW_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена структура '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                structuredObjectsView.getTitle(), structuredObjectsView.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки упоминаний комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Упоминания"</li>
     * <li>Нажать кнопку "Добавить"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано упоминание fastLinkSetting</li>
     * <li>Перейти на карточку упоминания fastLinkSetting</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке fastLinkSetting</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменено упоминание объектов, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkFastLinkSettingsWithSet()
    {
        // Выполнение действий и проверки
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("$", GUIFastLinkSetting.UUID,
                userClass);
        //Это добавлено для корректной очистки, иначе не создается скриптовый модуль
        FastLinkSetting fastLinkSetting2 = DAOObjectMentions.createFastLinkSetting("@", GUIFastLinkSetting.UUID,
                userClass);
        DSLFastLinkSetting.add(fastLinkSetting2);

        GUILogon.asSuper();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.clickAdd();
        GUIFastLinkSetting.fillTitle(fastLinkSetting.getTitle());
        GUIFastLinkSetting.fillCode(fastLinkSetting.getCode());
        GUIFastLinkSetting.selectMentionType(userClass);
        GUIFastLinkSetting.fillAlias(fastLinkSetting.getAlias());
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        fastLinkSetting.setExists(true);

        GUIFastLinkSetting.goToFastSettingCard(fastLinkSetting);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIFastLinkSetting.clickEditOnCard();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.FAST_LINK_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменено упоминание объектов '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                fastLinkSetting.getTitle(), fastLinkSetting.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки шаблонов списков комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Шаблоны списков"</li>
     * <li>Нажать кнопку "Добавить шаблон"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан шаблон template</li>
     * <li>Перейти на карточку шаблона template</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке template</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен шаблон списков, в нем
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkListTemplateWithSet()
    {
        ListTemplate template = DAOListTemplate.createTemplate(userClass);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToListTemplates();
        GUIListTemplate.advlist().toolPanel().clickAdd();
        GUIListTemplate.fillAddForm(template);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        template.setExists(true);

        GUIListTemplate.goToCard(template);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIListTemplate.clickEdit();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.LIST_TEMPLATES_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен шаблон списка '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                template.getTitle(), template.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки шаблонов стилей комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Шаблоны стилей"</li>
     * <li>Нажать кнопку "Добавить шаблон"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан шаблон template</li>
     * <li>Перейти на карточку шаблона template</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке template</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен шаблон стилей, в нем
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkStyleTemplateWithSet()
    {
        StyleTemplate template = DAOStyleTemplate.createTemplate(ModelUtils.createDescription());

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToStyleTemplates();
        GUIStyleTemplate.advlist().toolPanel().clickAdd();
        GUIStyleTemplate.fillAddForm(template);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyModalForm();
        template.setExists(true);

        GUIStyleTemplate.goToCard(template.getCode());
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIStyleTemplate.clickEdit();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.STYLE_TEMPLATES_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен шаблон стилей '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'",
                template.getTitle(), template.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки шаблонов контентов комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Шаблоны контентов"</li>
     * <li>Нажать кнопку "Добавить шаблон"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан шаблон newTemplate</li>
     * <li>Перейти на карточку шаблона newTemplate</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке newTemplate</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * </ol>
     */
    @Test
    public void testMarkContentTemplateWithSet()
    {
        StructuredObjectsView structuredObjectsView = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(structuredObjectsView);
        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(DAORootClass.create(), true,
                PositionContent.FULL, structuredObjectsView, false, "");
        DSLContent.add(hierarchyGrid);

        ContentTemplate newTemplate = DAOContentTemplate.createTemplate(hierarchyGrid);
        newTemplate.setCode(ModelUtils.createCode());
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToContentTemplates();
        GUIContentTemplate.advlist().toolPanel().clickAdd();
        GUIForm.fillAttribute(GUIContentTemplate.TITLE_ATTR, newTemplate.getTitle());
        GUIForm.fillAttribute(GUIContentTemplate.CODE_ATTR, newTemplate.getCode());
        GUIContentTemplate.selectContentType(hierarchyGrid);
        GUISelect.selectByTitle(GUIXpath.Div.STRUCTURED_OBJECTS_VIEW_ITEM_CODE_VALUE + GUIXpath.Input.INPUT_PREFIX,
                structuredObjectsView.getTitle());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        newTemplate.setExists(true);

        GUIContentTemplate.goToContentTemplate(newTemplate.getCode());
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIContentTemplate.clickEdit();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
    }
}
