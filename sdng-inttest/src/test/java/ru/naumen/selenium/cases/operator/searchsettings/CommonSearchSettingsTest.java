package ru.naumen.selenium.cases.operator.searchsettings;

import java.io.File;
import java.util.Collection;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Analyzer;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Boost;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.IndexedFormat;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование изменения общих настроек поиска в операторе
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
 * <AUTHOR>
 * @since 10.06.2020
 */
public class CommonSearchSettingsTest extends AbstractTestCase
{
    private static MetaClass userCase;
    private static MetaClass userClass;
    private static final Attribute content = DAOAttribute.createPseudo("Содержимое файла", "@fileContent", null);
    private static final String DOCX_FILE_CONTENT = "Содержимое файла docx";
    private static final String PDF_FILE_CONTENT = "123";

    private static Bo employee;

    /**
     * <ol>
     *      <b>Общая подготовка</b>
     *      <li>Создать пользовательский тип userCase пользовательского класса userClass</li>
     *      <li>Добавить контент "список файлов" fileList на карточку userCase</li>
     *      <li>Изменить настройку поиска для атрибута "содержимое файла" в типе userCase:
     *          <ul>
     *              <li>Доступен для поиска всем пользователям</li>
     *              <li>Анализатор - Русский</li>
     *          </ul>
     *      </li>
     *      <li>Создать сотрудника employee со всеми правами</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);

        DSLMetaClass.add(userClass, userCase);

        final ContentForm fileList = DAOContentCard.createFileList(userCase.getFqn());
        DSLContent.add(fileList);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);

        DSLSearchSettings.editAttributeSearchable(userClass, content, true, true, true, true,
                "", Boost.AVERAGE, Analyzer.RUSSIAN_ANALYZER);

    }

    /**
     * Тестирование поиска по файлам при изменении типов документов, по содержимому которых будет доступен поиск, когда
     * переиндексация выполняется через интерфейс администратора, а так же с помощью метода скриптового API
     * <code>api.search.reindexAll()</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262103451
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить БО <code>userBo</code> типа <code>userCase</code></li>
     * <li>К <code>userBo</code> прикрепить 2 файла:
     * <ul>
     *     <li>Формата docx с содержимым "Содержимое файла docx"</li>
     *     <li>Формата pdf c содержимым "123"</li>
     * </ul>
     * </li>
     * <b>Действия и проверки</b>
     * <li>Изменить типы документов, по содержимому которых будет доступен поиск на [PDF]</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти в интерфейс администратора</li>
     * <li>Нажать кнопку "Переиндексировать" на карточке метакласса <code>userClass</code> и дождаться окончания
     * переиндексации</li>
     * <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     * <li>Проверить, что ничего на найдено</li>
     * <li>Поискать быстрым поиском "123"</li>
     * <li>Проверить, что найден БО <code>userBo</code></li>
     * <li>Изменить типы документов, по содержимому которых будет доступен поиск на [MS_OFFICE]</li>
     * <li>Выполнить переиндексацию с помощью метода скриптового API:</li>
     * <pre>
     *     -------------------------------------------------------------------------------
     *     api.search.reindexAll()
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     * <li>Проверить, что найден БО <code>userBo</code></li>
     * <li>Поискать быстрым поиском "123"</li>
     * <li>Проверить, что ничего на найдено</li>
     * </ol>
     */
    @Test
    public void testIndexingAfterIndexedFormatsEdit()
    {
        // Подготовка
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_PDF);

        // Действия и проверки
        DSLSearchSettings.setIndexedFormats(IndexedFormat.PDF);
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass.getFqn());
        GUIMetaClass.refresh();
        WaitTool.waitSomething(userClass, 5L, 500L, DSLSearch::isReindexing);

        Assert.assertTrue(DSLSearch.simpleSearch(DOCX_FILE_CONTENT).isEmpty());
        Assert.assertTrue(DSLSearch.simpleSearch(PDF_FILE_CONTENT).contains(userBo.getUuid()));

        DSLSearchSettings.setIndexedFormats(IndexedFormat.MS_OFFICE);
        DSLSearch.updateIndex(userBo);

        Collection<String> simpleSearch = DSLSearch.simpleSearch(DOCX_FILE_CONTENT);
        Assert.assertTrue(simpleSearch.contains(userBo.getUuid()));
        Assert.assertTrue(DSLSearch.simpleSearch(PDF_FILE_CONTENT).isEmpty());
    }

    /**
     * Тестирование поиска по файлам после загрузки полной метаинформации с другими типами документов, по
     * содержимому которых будет доступен поиск
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на на [PDF]</li>
     *     <li>Выполнить полную выгрузку метаинформации</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на на [Microsoft Office]</li>
     *     <li>Добавить 2 БО типа userCase userBo,userBo2</li>
     *     <li>К userBo добавить 2 файла
     *          <ul>
     *              <li>Формата docx с содержимым "Содержимое файла docx"</li>
     *              <li>Формата pdf c содержимым "123"</li>
     *          </ul>
     *     </li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     *     <li>Поискать быстрым поиском "123"</li>
     *     <li>Проверить что ничего на найдено</li>
     *     <li>Загрузить ранее выгруженную метаинформацию</li>
     *     <li>Добавить к userBo2 PDF файл с содержимым - "123"</li>
     *     <li>Поискать в быстром поиске 123</li>
     *     <li>Проверить что перешли на карточку userBo2</li>
     *     <li>Добавить БО userBo3 типа userCase</li>
     *     <li>Добавить к userBo3 docx файл с содержимым - "Содержимое файла docx"</li>
     *     <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testSearchAfterFullMetainfoImport()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.PDF);
        final File metainfo = DSLMetainfoTransfer.exportMetainfo();

        DSLSearchSettings.setIndexedFormats(IndexedFormat.MS_OFFICE);
        final Bo userBo = DAOUserBo.create(userCase);
        final Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo, userBo2);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_PDF);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch(DOCX_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo);

        GUISearch.simpleSearch(PDF_FILE_CONTENT);
        GUISearch.assertEmptySearch();

        DSLMetainfoTransfer.importMetainfo(metainfo);
        DSLFile.add(userBo2, DSLFile.FILE_FOR_SEARCH_PDF);
        DSLSearch.updateIndex(userBo2);
        GUISearch.simpleSearch(PDF_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo2);

        final Bo userBo3 = DAOUserBo.create(userCase);
        DSLBo.add(userBo3);
        DSLFile.add(userBo3, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLSearch.updateIndex(userBo3);

        GUISearch.simpleSearch(DOCX_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска по файлам после загрузки частичной метаинформации с другими типами документов, по
     * содержимому которых будет доступен поиск
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на на [PDF]</li>
     *     <li>Выполнить частичную выгрузку метаинформации, выгрузив только общие настройки поиска</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на на [Microsoft Office]</li>
     *     <li>Добавить 2 БО типа userCase userBo,userBo2</li>
     *     <li>К userBo добавить 2 файла
     *          <ul>
     *              <li>Формата docx с содержимым "Содержимое файла docx"</li>
     *              <li>Формата pdf c содержимым "123"</li>
     *          </ul>
     *     </li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     *     <li>Поискать быстрым поиском "123"</li>
     *     <li>Проверить что ничего на найдено</li>
     *     <li>Загрузить ранее выгруженную метаинформацию</li>
     *     <li>Добавить к userBo2 PDF файл с содержимым - "123"</li>
     *     <li>Поискать в быстром поиске 123</li>
     *     <li>Проверить что перешли на карточку userBo2</li>
     *     <li>Добавить БО userBo3 типа userCase</li>
     *     <li>Добавить к userBo3 docx файл с содержимым - "Содержимое файла docx"</li>
     *     <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testSearchAfterPartialMetainfo()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.PDF);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.ALL_SETTINGS, MetainfoElementIdBuilder.PROCESS_SETTINGS,
                        MetainfoElementIdBuilder.COMMON_SEARCH_SETTINGS }));

        DSLSearchSettings.setIndexedFormats(IndexedFormat.MS_OFFICE);
        final Bo userBo = DAOUserBo.create(userCase);
        final Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo, userBo2);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_PDF);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch(DOCX_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo);

        GUISearch.simpleSearch(PDF_FILE_CONTENT);
        GUISearch.assertEmptySearch();

        DSLMetainfoTransfer.importMetainfo(metainfo);
        DSLFile.add(userBo2, DSLFile.FILE_FOR_SEARCH_PDF);
        DSLSearch.updateIndex(userBo2);
        GUISearch.simpleSearch(PDF_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo2);

        final Bo userBo3 = DAOUserBo.create(userCase);
        DSLBo.add(userBo3);
        DSLFile.add(userBo3, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLSearch.updateIndex(userBo3);

        GUISearch.simpleSearch(DOCX_FILE_CONTENT);
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска по файлам при пустом наборе типов документов, по содержимому которых будет доступен поиск
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на []</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo docx файл с содержимым - "Содержимое файла docx"</li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла docx"</li>
     *     <li>Проверить что ничего на найдено</li>
     * </ol>
     */
    @Test
    public void testEmptyIndexedFormats()
    {
        DSLSearchSettings.setIndexedFormats();
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_DOCX);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch(DOCX_FILE_CONTENT);
        GUISearch.assertEmptySearch();
    }

    /**
     * Тестирование поиска объектов с xlsx файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [Microsoft Office]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo xlsx файл с содержимым - "Содержимое файла xlsx"</li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла xlsx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testXlsxSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.MS_OFFICE);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_XLSX);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла xlsx");
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска объектов с pptx файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [Microsoft Office]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo pptx файл с содержимым - "Содержимое файла pptx"</li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла pptx"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testPPTXSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.MS_OFFICE);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_PPTX);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла pptx");
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска объектов с ODF файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [ODF]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo odt файл с содержимым - "Содержимое файла odt"</li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла odt"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testODFSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.ODF);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_ODT);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла odt");
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска объектов с html файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [HTML]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo html файл с содержимым:
     *     <pre>
     *         &lt;!DOCTYPE html&gt;
     *         &lt;html&gt;
     *         &lt;head&gt;
     *              &lt;title&gt;whatever&lt;/title&gt;
     *         &lt;/head&gt;
     *         &lt;body&gt;
     *          &lt;p&gt;Содержимое файла html&lt;/p&gt;
     *         &lt;/body&gt;
     *         &lt;/html&gt;
     *     </pre>
     *     </li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла html"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testHTMLSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.HTML);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_HTML);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла html");
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска объектов с xml файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [XML]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo xml файл с содержимым:
     *     <pre>
     *         &lt;?xml version="1.0" encoding="UTF-8" standalone="no" &gt;
     *         &lt;tikaTest&gt;
     * 	            Содержимое файла xml
     *         &lt;/tikaTest&gt;
     *     </pre>
     *     </li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла xml"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testXMLSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.XML);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_XML);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла xml");
        GUIBo.assertThatBoCard(userBo);
    }

    /**
     * Тестирование поиска объектов с txt файлом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00402
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88159918
     * <ol>
     *     <b>Подготовка</b>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Изменить типы документов, по содержимому которых будет доступен поиск на [TXT]</li>
     *     <li>Добавить БО типа userCase userBo</li>
     *     <li>Добавить к userBo txt файл с содержимым - "Содержимое файла txt"</li>
     *     <b>Действия и проверки</b>
     *     <li>Зайти под сотрудником employee</li>
     *     <li>Поискать быстрым поиском "Содержимое файла txt"</li>
     *     <li>Проверить что перешли на карточку userBo</li>
     * </ol>
     */
    @Test
    public void testTXTSearch()
    {
        DSLSearchSettings.setIndexedFormats(IndexedFormat.TXT);
        final Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_TXT);
        DSLSearch.updateIndex(userBo);

        GUILogon.login(employee);
        GUISearch.simpleSearch("Содержимое файла txt");
        GUIBo.assertThatBoCard(userBo);
    }
}
