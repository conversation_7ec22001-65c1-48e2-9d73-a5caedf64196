package ru.naumen.selenium.cases.admin.adminpermission;

import java.util.Arrays;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListEditableToolPanel.RButton;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListToolPanel;
import ru.naumen.selenium.casesutil.mail.rules.DSLMailProcessorRule;
import ru.naumen.selenium.casesutil.mail.rules.GUIMailProcessorRule;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIWorkflow;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.mail.rules.DAOMailProcessorRule;
import ru.naumen.selenium.casesutil.model.mail.rules.MailProcessorRule;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;

/**
 * Тестирование отображения и сокрытия элементов интерфейса
 * <AUTHOR>
 * @since 10.03.2024
 */
class VisibilityAndHidingTest extends AbstractTestCaseJ5
{
    private static SettingsSet setSettings1, setSettings2;
    private static SuperUser superUser;
    private static MetaClass scClass, scCase1, rootClass;
    private static final String EDIT_MENU_ITEM = "Редактировать";
    private static final String RIGHT_MENU_ITEM = "Переместить вправо";
    private static final String LEFT_MENU_ITEM = "Переместить влево";
    private static final String OFF_MENU_ITEM = "Убрать с панели действий";

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Включить доступность профилей администрирования на стенде</li>
     * <li>Включить доступность комплектов на стенде (beanFactory.getBean('settingsSetStorageServiceConfiguration')
     * .setSettingSetsEnabled(true))</li>
     * <li>Создать профиль администрирования adminProfile и выдать ему все права в Матрице маркеров доступа</li>
     * <li>Создать комплект setSettings1</li>
     * <li>Создать комплект setSettings2 и связать его с профилем администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser</li>
     * <li>Отредактировать суперпользователя superUser, указав ему профиль администрирования adminProfile</li>
     * <li>Для класса Запрос (serviceCall) добавить тип: scCase1</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);
        DSLAdminProfile.addAllRightsToAdminProfile(adminProfile);

        setSettings1 = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(setSettings1);

        setSettings2 = DAOSettingsSet.createSettingsSet();
        setSettings2.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(setSettings2);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        scClass = DAOScCase.createClass();
        scCase1 = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase1);
        rootClass = DAORootClass.create();
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса в типе, который размечен комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <ol>
     * <li>Для класса Запрос (serviceCall) добавить тип: scCase2</li>
     * <li>Для типа scCase1 указать комплект setSettings1</li>
     * <li>Для типа scCase2 указать комплект setSettings2</li>
     * <br>
     * Действия и проверки:
     * <ol>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Атрибуты для типа scCase1</li>
     * <li>Проверить, что на странице отсутствуют кнопки Редактировать, Копировать, Поместить в архив, Удалить,
     * Сбросить настройки системных атрибутов, Сбросить настройки пользовательских атрибутов</li>
     * <li>Открыть вкладку Группы атрибутов для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Карточка объекта для типа scCase1</li>
     * <li>Проверить, что на странице отсутствуют кнопки Редактировать настройки, Управление шаблонами</li>
     * <li>Открыть вкладку Форма добавления для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Редактировать настройки</li>
     * <li>Открыть вкладку Форма редактирования для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Редактировать настройки</li>
     * <li>Открыть вкладку Права доступа для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Жизненный цикл для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Жизненный цикл/Управление параметрами в статусах и переходах для типа scCase1</li>
     * <li>Проверить, что на странице отсутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Атрибуты для типа scCase2</li>
     * <li>Проверить, что на странице присутствуют кнопки Редактировать, Копировать, Поместить в архив, Удалить,
     * Сбросить настройки системных атрибутов, Сбросить настройки пользовательских атрибутов</li>
     * <li>Открыть вкладку Группы атрибутов для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Карточка объекта для типа scCase2</li>
     * <li>Проверить, что на странице присутствуют кнопки Редактировать настройки, Управление шаблонами</li>
     * <li>Открыть вкладку Форма добавления для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Редактировать настройки</li>
     * <li>Открыть вкладку Форма редактирования для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Редактировать настройки</li>
     * <li>Открыть вкладку Права доступа для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Жизненный цикл для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Сбросить настройки</li>
     * <li>Открыть вкладку Жизненный цикл/Управление параметрами в статусах и переходах для типа scCase2</li>
     * <li>Проверить, что на странице присутствует кнопка Сбросить настройки</li>
     * </ol>
     */
    @Test
    void testInterfaceElementsVisibilityForMarkedType()
    {
        //Подготовка
        MetaClass scCase2 = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase2);

        scCase1.setSettingsSet(setSettings1);
        scCase2.setSettingsSet(setSettings2);
        DSLMetaClass.edit(scCase1, scCase2);

        //Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(scCase1, MetaclassCardTab.ATTRIBUTES);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTES);

        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_COPY);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REMOVE);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_DEL);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_SYSTEM_REFRESH);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_FLEX_REFRESH);

        GUIMetaClass.goToTab(scCase1, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTGROUPS);

        GUIMetaClass.goToTab(MetaclassCardTab.OBJECTCARD);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.OBJECTCARD);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_DEFAULT);

        GUIMetaClass.goToTab(MetaclassCardTab.NEWENTRYFORM);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.NEWENTRYFORM);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);

        GUIMetaClass.goToTab(MetaclassCardTab.EDITFORM);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.EDITFORM);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);

        GUIMetaClass.goToTab(MetaclassCardTab.PERMISSIONSETTINGS);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.PERMISSIONSETTINGS);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);

        GUIMetaClass.goToTab(MetaclassCardTab.LIFECYCLE);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.LIFECYCLE);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);

        GUIWorkflow.goToStatesAttrsSettingsTab();
        GUIWorkflow.assertSelectPresent();
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);

        GUIMetaClass.goToTab(scCase2, MetaclassCardTab.ATTRIBUTES);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTES);

        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_COPY);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_REMOVE);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_DEL);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_SYSTEM_REFRESH);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_FLEX_REFRESH);

        GUIMetaClass.goToTab(scCase2, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_REFRESH);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTGROUPS);

        GUIMetaClass.goToTab(MetaclassCardTab.OBJECTCARD);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.OBJECTCARD);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_DEFAULT);

        GUIMetaClass.goToTab(MetaclassCardTab.NEWENTRYFORM);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.NEWENTRYFORM);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_EDIT);

        GUIMetaClass.goToTab(MetaclassCardTab.EDITFORM);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.EDITFORM);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_EDIT);

        GUIMetaClass.goToTab(MetaclassCardTab.PERMISSIONSETTINGS);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.PERMISSIONSETTINGS);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_REFRESH);

        GUIMetaClass.goToTab(MetaclassCardTab.LIFECYCLE);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.LIFECYCLE);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_REFRESH);

        GUIWorkflow.goToStatesAttrsSettingsTab();
        GUIWorkflow.assertSelectPresent();
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_REFRESH);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для групп атрибутов, которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для класса Запрос создать 2 группы атрибутов с произвольными атрибутами: attrGroup1 и attrGroup2</li>
     * <li>Для системной группы Ответственный (responsible) и пользовательской группы attrGroup1 указать комплект
     * setSettings1</li>
     * <li>Для системной группы Кем решен (solved) и пользовательской группы attrGroup2 указать комплект
     * setSettings2</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Группы атрибутов в классе Запрос</li>
     * <li>Проверить, что у группы атрибутов attrGroup1 отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Проверить, что у группы атрибутов attrGroup2 присутствуют кнопки Редактировать, Удалить</li>
     * <li>Проверить, что у группы атрибутов Ответственный (responsible) отсутствует кнопка Редактировать</li>
     * <li>Проверить, что у группы атрибутов Кем решен (solved) присутствует кнопка Редактировать</li>
     * <li>Открыть вкладку Группы атрибутов для типа scCase1</li>
     * <li>Проверить, что у группы атрибутов attrGroup1 отсутствуют кнопки Редактировать, Сбросить настройки</li>
     * <li>Проверить, что у группы атрибутов attrGroup2 присутствуют кнопки Редактировать, Сбросить настройки</li>
     * <li>Проверить, что у группы атрибутов Ответственный (responsible) отсутствуют кнопки Редактировать, Сбросить
     * настройки</li>
     * <li>Проверить, что у группы атрибутов Кем решен (solved) присутствуют кнопки Редактировать, Сбросить
     * настройки</li>
     * </ol>
     */
    @Test
    void testAttributeGroupsInterfaceVisibility()
    {
        //Подготовка
        GroupAttr attrGroup1 = DAOGroupAttr.create(scClass);
        GroupAttr attrGroup2 = DAOGroupAttr.create(scClass);
        GroupAttr responsibleGroup = DAOGroupAttr.createResponsible(scClass);
        attrGroup1.setSettingsSet(setSettings1);
        responsibleGroup.setSettingsSet(setSettings1);

        GroupAttr solvedGroup = DAOGroupAttr.createSolved();
        attrGroup2.setSettingsSet(setSettings2);
        solvedGroup.setSettingsSet(setSettings2);

        DSLGroupAttr.add(attrGroup1, SysAttribute.title(scClass), SysAttribute.metaClass(scClass));
        DSLGroupAttr.add(attrGroup2, SysAttribute.number(scClass), SysAttribute.creationDate(scClass));
        DSLGroupAttr.edit(responsibleGroup, new Attribute[] {}, new Attribute[] {});
        DSLGroupAttr.edit(solvedGroup, new Attribute[] {}, new Attribute[] {});

        //Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTGROUPS);

        GUIGroupAttr.assertPresentGroupButton(false, attrGroup1, GUIButtonBar.BTN_DEL);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup1, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(true, attrGroup2, GUIButtonBar.BTN_DEL);
        GUIGroupAttr.assertPresentGroupButton(true, attrGroup2, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(false, responsibleGroup, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(true, solvedGroup, GUIButtonBar.BTN_EDIT);

        GUIMetaClass.goToTab(scCase1, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIMetaClass.assertSelectedTab(MetaclassCardTab.ATTRIBUTGROUPS);

        GUIGroupAttr.assertPresentGroupButton(false, attrGroup1, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup1, GUIButtonBar.BTN_REFRESH);
        GUIGroupAttr.assertPresentGroupButton(true, attrGroup2, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(true, attrGroup2, GUIButtonBar.BTN_REFRESH);
        GUIGroupAttr.assertPresentGroupButton(false, responsibleGroup, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(false, responsibleGroup, GUIButtonBar.BTN_REFRESH);
        GUIGroupAttr.assertPresentGroupButton(true, solvedGroup, GUIButtonBar.BTN_EDIT);
        GUIGroupAttr.assertPresentGroupButton(true, solvedGroup, GUIButtonBar.BTN_REFRESH);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для списочных контентов, которые размечены комплектом.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <ol>
     * <b>Подготовка:</b>
     * <li>Для контента "Список вложенных объектов Отделы", расположенного на карточке объекта "Компания" (вкладка
     * "Оргструктура"), установить комплект setSettings1</li>
     * <li>Для контента "Список объектов Команды", расположенного на карточке объекта "Компания" (вкладка
     * "Оргструктура"), установить комплект setSettings2</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть карточку объекта Компания, вкладка Оргструктура</li>
     * <li>Проверить, что при наведении указателя мыши на контент Список вложенных объектов Отделы отсутствуют кнопки
     * Редактировать, Удалить, Настройка ограничений при фильтрации, Скопировать настройки из шаблона, Создать шаблон
     * на базе списка</li>
     * <li>Проверить, что при наведении указателя мыши на контент Список объектов Команды присутствуют кнопки
     * Редактировать, Удалить, Настройка ограничений при фильтрации, Скопировать настройки из шаблона, Создать шаблон
     * на базе списка/li>
     * </ol>
     */
    @Test
    void testListContentsInterfaceVisibility()
    {
        // Подготовка
        ContentTab tab = DSLContent.getTab(rootClass.getFqn(), 1);
        ContentForm ousList = DSLContent.getContentByTitle(rootClass.getFqn(), "Отделы",
                MetaclassCardTab.OBJECTCARD.get());
        ContentForm teamList = DSLContent.getContentByTitle(rootClass.getFqn(), "Команды",
                MetaclassCardTab.OBJECTCARD.get());

        GUILogon.asSuper();
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab);

        GUIContent.clickEditContent(ousList);
        GUISettingsSet.fillSettingsSetPropOnForm(setSettings1.getCode());
        GUIForm.applyForm();

        GUIContent.clickEditContent(teamList);
        GUISettingsSet.fillSettingsSetPropOnForm(setSettings2.getCode());
        GUIForm.applyForm();

        // Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab);
        GUITab.assertTabPresent(tab);

        ousList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(false, AGUIAdvListToolPanel.ICON_COPY_TO_TEMPLATE);
        ousList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(false, AGUIAdvListToolPanel.ICON_LIST_TEMPLATE);
        ousList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(false, AGUIAdvListToolPanel.ICON_DELETE);
        ousList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(false, AGUIAdvListToolPanel.ICON_FILTER_SETTINGS);
        ousList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(false, AGUIAdvListToolPanel.ICON_EDIT);

        teamList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(true, AGUIAdvListToolPanel.ICON_COPY_TO_TEMPLATE);
        teamList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(true, AGUIAdvListToolPanel.ICON_LIST_TEMPLATE);
        teamList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(true, AGUIAdvListToolPanel.ICON_DELETE);
        teamList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(true, AGUIAdvListToolPanel.ICON_FILTER_SETTINGS);
        teamList.advlist().toolPanel().asserts()
                .controlIsVisibleOnContentHover(true, AGUIAdvListToolPanel.ICON_EDIT);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса в панелях действий на карточке объекта, если контролы
     * размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для контента Список вложенных объектов Отделы, расположенного на карточке объекта Компания, вкладка
     * Оргструктура, выполнить настройку в Панели действий:
     *     <ul>
     *         <li>Использовать системную логику формирования панели действий (отображаются только системные кнопки)
     *         = false</li>
     *         <li>Для контрола Сохранить вид установить комплект setSettings2</li>
     *         <li>Использовать системную логику формирования панели массовых операций (на панель выводятся кнопки,
     *         видимые в шапке карточки объекта списка) = false</li>
     *         <li>Для контрола Редактировать установить комплект setSettings1</li>
     *         <li>Использовать системную логику действий с элементами списка = false</li>
     *         <li>Для контрола Элемент меню/Открыть карточку объекта установить комплект setSettings2</li>
     *     </ul>
     * </li>
     * <li>Для контента Список объектов Команды, расположенного на карточке объекта Компания, вкладка Оргструктура,
     * выполнить настройку в Панели действий:
     *     <ul>
     *         <li>Использовать системную логику формирования панели действий (отображаются только системные кнопки)
     *         = false</li>
     *         <li>Для контрола Сохранить вид установить комплект setSettings1</li>
     *         <li>Использовать системную логику формирования панели массовых операций (на панель выводятся кнопки,
     *         видимые в шапке карточки объекта списка) = false</li>
     *         <li>Для контрола Редактировать установить комплект setSettings2</li>
     *         <li>Использовать системную логику действий с элементами списка = false</li>
     *         <li>Для контрола Элемент меню/Открыть карточку объекта установить комплект setSettings1</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Карточка объекта класса Компания, вкладка Оргструктура</li>
     * <li>Открыть форму Настройка действий в контенте "Отделы"</li>
     * <li>Проверить, что чек-бокс Использовать системную логику формирования панели действий (отображаются только
     * системные кнопки) доступен</li>
     * <li>Проверить, что в Панели действий присутствуют кнопки Сбросить до системной настройки, Скопировать
     * настройки из шаблона</li>
     * <li>Проверить, что чек-бокс Использовать системную логику формирования панели массовых операций (на панель
     * выводятся кнопки, видимые в шапке карточки объекта списка) недоступен</li>
     * <li>Проверить, что на Панели массовых операций отсутствуют кнопки Сбросить до системной настройки, Скопировать
     * настройки из шаблона</li>
     * <li>Проверить, что чек-бокс Использовать системную логику действий с элементами списка доступен</li>
     * <li>Проверить, что для элемента меню Открыть карточку объекта присутствуют кнопки Редактировать, Удалить</li>
     * <li>Проверить, что радиогруппа Расположение иконок доступна</li>
     * <li>Проверить, что выпадающий список Иконка вызова меню доступен</li>
     * <li>Нажать на контрол Сохранить вид в Панели действий</li>
     * <li>Проверить, что появилось меню с кнопками Редактировать, Переместить влево, Убрать с панели действий</li>
     * <li>Нажать на контрол Редактировать в Панели массовых операций</li>
     * <li>Проверить, что меню не появляется, никакого действия не происходит</li>
     * <li>Нажать на контрол Переместить в Панели массовых операций</li>
     * <li>Проверить, что появилось меню с кнопками Редактировать, Переместить влево, Переместить вправо, Убрать с
     * панели действий</li>
     * <li>Нажать на кнопку Отмена</li>
     * <li>Открыть форму Настройка действий в контенте "Команды"</li>
     * <li>Проверить, что чек-бокс Использовать системную логику формирования панели действий (отображаются только
     * системные кнопки) недоступен для изменения</li>
     * <li>Проверить, что в Панели действий отсутствуют кнопки Сбросить до системной настройки, Скопировать настройки
     * из шаблона</li>
     * <li>Проверить, что чек-бокс Использовать системную логику формирования панели массовых операций (на панель
     * выводятся кнопки, видимые в шапке карточки объекта списка) доступен</li>
     * <li>Проверить, что на Панели массовых операций присутствуют кнопки Сбросить до системной настройки,
     * Скопировать настройки из шаблона</li>
     * <li>Проверить, что чек-бокс Использовать системную логику действий с элементами списка недоступен</li>
     * <li>Проверить, что для элемента меню Открыть карточку объекта отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Проверить, что радиогруппа Расположение иконок недоступна</li>
     * <li>Проверить, что выпадающий список Иконка вызова меню недоступен</li>
     * <li>Нажать на контрол Сохранить вид в Панели действий</li>
     * <li>Проверить, что меню не появляется, никакого действия не происходит</li>
     * <li>Нажать на контрол Сортировка в Панели действий</li>
     * <li>Проверить, что появилось меню с кнопками Редактировать, Переместить вправо, Убрать с панели действий</li>
     * <li>Нажать на контрол Редактировать в Панели массовых операций</li>
     * <li>Проверить, что появилось меню с кнопками Редактировать, Переместить вправо, Убрать с панели действий</li>
     * </ol>
     */
    @Test
    void testVisibilityOfActionPanelControlsWithSettings()
    {
        // Подготовка
        GUILogon.asSuper();
        ContentTab tab = DSLContent.getTab(rootClass.getFqn(), 1);
        ContentForm ouContentForm = DSLContent.getContentByTitle(rootClass.getFqn(), "Отделы",
                MetaclassCardTab.OBJECTCARD.get());
        ContentForm teamsContentForm = DSLContent.getContentByTitle(rootClass.getFqn(), "Команды",
                MetaclassCardTab.OBJECTCARD.get());

        configureContentForm(ouContentForm, tab, setSettings2, setSettings1, setSettings2);
        configureContentForm(teamsContentForm, tab, setSettings1, setSettings2, setSettings1);

        // Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab);
        GUIContent.clickEditToolPanel(ouContentForm);
        ouContentForm.advlist().editableToolPanel().assertUseSystemSettingsAvailable(true);
        ouContentForm.advlist().editableToolPanel().assertsEditMode().presenceOrAbsenceReset(true);
        ouContentForm.advlist().editableToolPanel().assertsEditMode().presenceOrAbsenceCopyFromTemplate(true);
        ouContentForm.advlist().editableMassToolPanel().assertUseSystemSettingsAvailable(false);
        ouContentForm.advlist().editableMassToolPanel().assertsEditMode().presenceOrAbsenceReset(false);
        ouContentForm.advlist().editableMassToolPanel().assertsEditMode().
                presenceOrAbsenceCopyFromTemplate(false);
        ouContentForm.advlist().editableToolPanel().assertUseSystemSettingsAvailable(true);
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode().
                presenceOrAbsenceEdit(0, true);
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode().
                presenceOrAbsenceDelete(0, true);
        checkRadioButtonGroup(ouContentForm, true);
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                .menuCallIconDropdownIsAvailable(true);
        ouContentForm.advlist().editableToolPanel().clickControlSave();
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                .contextMenuItems(EDIT_MENU_ITEM, LEFT_MENU_ITEM, OFF_MENU_ITEM);
        ouContentForm.advlist().editableMassToolPanel().clickControlEdit();
        ouContentForm.advlist().editableMassToolPanel().asserts().absenceContextMenuOptions(false);
        ouContentForm.advlist().editableMassToolPanel().clickControlMove();
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                .contextMenuItems(EDIT_MENU_ITEM, LEFT_MENU_ITEM, RIGHT_MENU_ITEM, OFF_MENU_ITEM);
        GUIForm.cancelForm();

        GUIContent.clickEditToolPanel(teamsContentForm);
        teamsContentForm.advlist().editableToolPanel().assertUseSystemSettingsAvailable(false);
        teamsContentForm.advlist().editableToolPanel().assertsEditMode().presenceOrAbsenceReset(false);
        teamsContentForm.advlist().editableToolPanel().assertsEditMode().presenceOrAbsenceCopyFromTemplate(false);
        teamsContentForm.advlist().editableMassToolPanel().assertUseSystemSettingsAvailable(true);
        teamsContentForm.advlist().editableMassToolPanel().assertsEditMode().presenceOrAbsenceReset(true);
        teamsContentForm.advlist().editableMassToolPanel().assertsEditMode().
                presenceOrAbsenceCopyFromTemplate(true);
        teamsContentForm.advlist().editableToolPanel().assertUseSystemSettingsAvailable(false);
        teamsContentForm.advlist().editableListObjectToolPanel().assertsEditMode().
                presenceOrAbsenceEdit(0, false);
        teamsContentForm.advlist().editableListObjectToolPanel().
                assertsEditMode().presenceOrAbsenceDelete(0, false);
        checkRadioButtonGroup(ouContentForm, false);
        teamsContentForm.advlist().editableListObjectToolPanel().assertsEditMode().
                menuCallIconDropdownIsAvailable(false);
        teamsContentForm.advlist().editableToolPanel().clickControlSave();
        teamsContentForm.advlist().editableMassToolPanel().asserts().absenceContextMenuOptions(false);
        teamsContentForm.advlist().editableToolPanel().clickControlSort();
        ouContentForm.advlist().editableListObjectToolPanel().assertsEditMode().
                contextMenuItems(EDIT_MENU_ITEM, RIGHT_MENU_ITEM, OFF_MENU_ITEM);
        teamsContentForm.advlist().editableMassToolPanel().clickControlEdit();
        teamsContentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                .contextMenuItems(EDIT_MENU_ITEM, RIGHT_MENU_ITEM, OFF_MENU_ITEM);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для маркеров прав, которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Запрос добавить два Маркера прав на Просмотр атрибутов:
     *     <ul>
     *         <li>marker1, размеченный комплектом setSettings1</li>
     *         <li>marker2, размеченный комплектом setSettings2</li>
     *     </ul>
     * </li>
     * <li>В типе scCase1 добавить два Маркера прав на Просмотр атрибутов:
     *     <ul>
     *         <li>markerType1, размеченный комплектом setSettings1</li>
     *         <li>markerType2, размеченный комплектом setSettings2</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Права доступа класса Запрос</li>
     * <li>Навести курсор мыши на marker1</li>
     * <li>Проверить, что кнопки Редактировать, Удалить отсутствуют</li>
     * <li>Навести курсор мыши на marker2</li>
     * <li>Проверить, что кнопки Редактировать, Удалить присутствуют</li>
     * <li>Открыть вкладку Права доступа типа scCase1</li>
     * <li>Навести курсор мыши на marker1</li>
     * <li>Проверить, что кнопки Редактировать, Удалить отсутствуют</li>
     * <li>Навести курсор мыши на marker2</li>
     * <li>Проверить, что кнопка Редактировать присутствует, Удалить отсутствует</li>
     * <li>Навести курсор мыши на markerType1</li>
     * <li>Проверить, что кнопки Редактировать, Удалить отсутствуют</li>
     * <li>Навести курсор мыши на markerType2</li>
     * <li>Проверить, что кнопки Редактировать, Удалить присутствуют</li>
     * </ol>
     */
    @Test
    void testRightsMarkerVisibility()
    {
        //Подготовка
        SecurityMarker marker1 = new SecurityMarkerViewAttrs(scClass).setSettingsSet(setSettings1).apply();
        SecurityMarker marker2 = new SecurityMarkerViewAttrs(scClass).setSettingsSet(setSettings2).apply();

        SecurityMarker markerType1 = new SecurityMarkerViewAttrs(scCase1).setSettingsSet(setSettings1).apply();
        SecurityMarker markerType2 = new SecurityMarkerViewAttrs(scCase1).setSettingsSet(setSettings2).apply();

        //Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.assertMarkerButtonExist(marker1, false, GUIAccessMatrix.DELETE, GUIAccessMatrix.EDIT);
        GUIAccessMatrix.assertMarkerButtonExist(marker2, true, GUIAccessMatrix.DELETE, GUIAccessMatrix.EDIT);

        GUIMetaClass.goToTab(scCase1, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.assertMarkerButtonExist(marker1, false, GUIAccessMatrix.DELETE, GUIAccessMatrix.EDIT);
        GUIAccessMatrix.assertMarkerButtonExist(marker2, false, GUIAccessMatrix.DELETE);
        GUIAccessMatrix.assertMarkerButtonExist(marker2, true, GUIAccessMatrix.EDIT);
        GUIAccessMatrix.assertMarkerButtonExist(markerType1, false, GUIAccessMatrix.EDIT, GUIAccessMatrix.DELETE);
        GUIAccessMatrix.assertMarkerButtonExist(markerType2, true, GUIAccessMatrix.DELETE, GUIAccessMatrix.EDIT);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для правила обработки почты, которое размечено
     * комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <p>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <ol>
     *     <li>Добавить правило обработки почты mpRule1 с произвольным скриптом, размеченное комплектом
     *     setSettings1</li>
     *     <li>Добавить правило обработки почты mpRule2 с произвольным скриптом, размеченное комплектом
     *     setSettings2</li>
     * </ol>
     * <p>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под пользователем superUser</li>
     *     <li>Открыть вкладку "Настройка системы/Почта/Правила обработки".</li>
     *     <li>Проверить, что у правила mpRule1 в списке отсутствуют кнопки "Выключить", "Редактировать", "Удалить"</li>
     *     <li>Проверить, что у правила mpRule2 в списке присутствуют кнопки "Выключить", "Редактировать",
     *     "Удалить"</li>
     *     <li>Открыть карточку правила mpRule1</li>
     *     <li>Проверить, что на карточке объекта mpRule1 отсутствуют кнопки "Выключить", "Редактировать",
     *     "Удалить"</li>
     *     <li>Нажать кнопку "Назад"</li>
     *     <li>Открыть карточку правила mpRule2</li>
     *     <li>Проверить, что на карточке объекта mpRule2 присутствуют кнопки "Выключить", "Редактировать",
     *     "Удалить"</li>
     * </ol>
     */
    @Test
    void testEmailRuleInterfaceVisibility()
    {
        //Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        MailProcessorRule mpRule1 = DAOMailProcessorRule.createMailProcessorRule();
        MailProcessorRule mpRule2 = DAOMailProcessorRule.createMailProcessorRule();

        mpRule1.setSettingsSet(setSettings1);
        mpRule2.setSettingsSet(setSettings2);
        DSLMailProcessorRule.addReceiveMailRule(mpRule1, script);
        DSLMailProcessorRule.addReceiveMailRule(mpRule2, script);

        //Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToMailProcessors();
        GUIMailProcessorRule.assertIconsActionExistsByCode(mpRule1.getCode(), false, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_SWITCH_OFF, GUIButtonBar.BTN_DELETE);
        GUIMailProcessorRule.assertIconsActionExistsByCode(mpRule2.getCode(), true, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_SWITCH_OFF, GUIButtonBar.BTN_DELETE);

        GUIMailProcessorRule.goToMailRuleCard(mpRule1);
        GUIMailProcessorRule.assertTitleOnCard(mpRule1.getTitle());
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_SWITCH);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_DEL);
        GUIMailProcessorRule.goToMailRuleCard(mpRule2);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_SWITCH);
        GUIMetaClass.assertButtonsPresent(GUIMetaClass.BTN_DEL);
    }

    /**
     * Проверить, что радиогруппа доступна
     * @param contentForm форма, на которой находится радиогруппа
     * @param unlocked true - разблокирована, false - заблокирована
     */
    private static void checkRadioButtonGroup(ContentForm contentForm, boolean unlocked)
    {
        Arrays.stream(RButton.values()).forEach(rButton ->
        {
            boolean isSelected = rButton.getDescription().equals(RButton.NONE.getDescription());
            contentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                    .radioButtonIsEnabled(unlocked, rButton.getDescription());
            contentForm.advlist().editableListObjectToolPanel().assertsEditMode()
                    .radioButtonIsSelected(isSelected, rButton.getDescription());
        });
    }

    /**
     * Настраивает указанную форму ({@link ContentForm})
     * @param contentForm форма, которую необходимо настроить
     * @param tab вкладка, на которой находится форма
     * @param toolPanelSettingsSet Комплект для контрола "Сохранить вид"
     * @param massToolPanelSettingsSet Комплект для контрола "Редактировать"
     * @param listObjectToolPanelSettingsSet Комплект для контрола "Элемент меню"/"Открыть карточку"
     */
    private static void configureContentForm(ContentForm contentForm, ContentTab tab, SettingsSet toolPanelSettingsSet,
            SettingsSet massToolPanelSettingsSet, SettingsSet listObjectToolPanelSettingsSet)
    {
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab);
        GUIContent.clickEditToolPanel(contentForm);

        contentForm.advlist().editableToolPanel().setUseSystemSettings(false);
        contentForm.advlist().editableToolPanel().rightClickToolByTitle("Сохранить вид");
        contentForm.advlist().editableToolPanel().clickEditContextMenuOption();
        GUISettingsSet.fillSettingsSetOnForm(toolPanelSettingsSet.getCode());
        GUIForm.applyLastModalForm();

        contentForm.advlist().editableMassToolPanel().setUseSystemSettings(false);
        contentForm.advlist().editableMassToolPanel().rightClickToolByTitle("редактировать");
        contentForm.advlist().editableMassToolPanel().clickEditContextMenuOption();
        GUISettingsSet.fillSettingsSetOnForm(massToolPanelSettingsSet.getCode());
        GUIForm.applyLastModalForm();

        contentForm.advlist().editableListObjectToolPanel().setUseSystemSettings(false);
        contentForm.advlist().editableListObjectToolPanel().clickEditMenuItem(0);
        GUISettingsSet.fillSettingsSetOnForm(listObjectToolPanelSettingsSet.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.applyLastModalForm();
    }
}
