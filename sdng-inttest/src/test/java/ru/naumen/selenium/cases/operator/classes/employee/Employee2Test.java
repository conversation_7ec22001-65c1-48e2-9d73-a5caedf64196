package ru.naumen.selenium.cases.operator.classes.employee;

import static org.junit.Assert.assertEquals;
import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.GUISimpleList;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListToolPanel;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.ToolPanel;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.security.GUIPasswordForm;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityProfile;
import ru.naumen.selenium.security.UserGroup;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование сотрудника
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00053
 * <AUTHOR>
 * @since 25.01.2012
 */
public class Employee2Test extends AbstractTestCase
{
    private final static Gson GSON = new GsonBuilder().serializeNulls().create();

    /**
     * Тестирование удаления сотрудника, используемого в качестве значения ссылочного атрибута типа "Набор ссылок на
     * БО" класса "Компания"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00290
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Добавить атрибут attr типа Набор ссылок на БО в класс Компания (класс объектов - Сотрудник)</li>
     * <li>Создать БО empl класса Сотрудник</li>
     * <li>Добавить empl в значение атрибута attr БО Компания</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку empl</li>
     * <li>Нажать Удалить</li>
     * <li>Нажать Да</li>
     * <br>
     * <b>Проверка</b>
     * <li>На карточке empl появилось сообщение об ошибке</li>
     * <pre>
     * Сотрудник 'employee' не может быть удален по следующим причинам:
     * 1. Сотрудник связан со следующими объектами: компания 'Компания'
     * </pre>
     * <li></li>
     * </ol>
     */
    @Test
    public void testDeleteObjectInRootLink()
    {
        //Подготовка
        Bo company = SharedFixture.root();
        MetaClass companyClass = DAORootClass.create();
        MetaClass emplClass = DAOEmployeeCase.createClass();

        Attribute attr = DAOAttribute.createBoLinks(companyClass, emplClass);
        DSLAttribute.add(attr);

        Bo empl = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(empl);

        attr.setValue(Json.listToString(empl.getUuid()));
        DSLBo.editAttributeValue(company, attr);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIButtonBar.clickDelete();
        GUIForm.clickYes();
        //Проверка
        String message = ErrorMessages.NOT_DEL_EMPLOYEE + '\n' + ErrorMessages.FIRST
                         + ErrorMessages.EMPLOYEE_ROOT_RELATION;
        GUIError.assertDialogError(String.format(message, DSLEmployee.getFullName(empl)));
    }

    /**
     * Тестирование редактирования сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00058
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела А</li>
     * <li>Создать отдела B типа А</li>
     * <li>Создать тип С сотрудника</li>
     * <li>В типе C создать группу атрибутов D, и добавить в эту группу все системные атрибуты типа С
     * </li>
     * <li>В типе C создать контент E, типа "Параметры объекта" (руппа атрибутов D).</li>
     * <li>В типе C создать контент F На форме редактирования, типа "Параметры объекта" (группа атрибутов D).</li>
     * <li>Создать сотрудника G типа C</li> 
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника G</li>
     * <li>На контенте D нажимаем кнопку Редактировать</li>
     * <li>На форме редактирования:</li>
     * <li>Меняем: почту, дату рождения, должность, имя, логин, 4 телефона,  фамилия, лицензия.</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма редактирования закрылась</li>
     * <li>В карточке сотрудника и в контенте D проверяем что изменились поля: почту, дату рождения, должность, имя,
     * логин, 4 телефона,  фамилия, лицензия.</li>
     * </ol>
     */
    @Test
    public void testEdit()
    {
        //Подготовка
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);
        //Создаем группу атрибутов в типе сотрудника и добавляем в нее все системные атрибуты
        GroupAttr atrGroup = DAOGroupAttr.create(emplCase.getFqn());
        DSLGroupAttr.add(atrGroup, DSLGroupAttr.getEmployeeSystemAttr());
        //Создаем контент "Параметры объекта" в карточке типа сотрудника
        ContentForm contentPropertyList = DAOContentCard.createPropertyList(emplCase, atrGroup);
        DSLContent.add(contentPropertyList);
        //настраиваем форму добавления. 
        ContentForm contentEditForm = DAOContentEditForm.createEditablePropertyList(emplCase.getFqn(), atrGroup);
        DSLContent.add(contentEditForm);
        Bo empl = DAOEmployee.create(emplCase, SharedFixture.ou(), true);
        DSLBo.add(empl);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        //Генерируем новые значения для полей сотрудника
        empl.setFirstName(ModelUtils.createTitle());
        empl.setLastName(ModelUtils.createTitle());
        empl.setMiddleName(ModelUtils.createTitle());
        empl.setPost(ModelUtils.createTitle());
        empl.setEmail(ModelUtils.createEmail());
        empl.setDateOfBirth(DateTimeUtils.getRandomDateTimeddMMyyyy());
        empl.setLogin(UniqueRandomStringUtils.stringEn(10));
        empl.setInternalPhoneNumber(String.format("3%s", UniqueRandomStringUtils.serialNumber(8)));
        empl.setCityPhoneNumber(String.format("343%s", UniqueRandomStringUtils.serialNumber(7)));
        empl.setHomePhoneNumber(String.format("3%s", UniqueRandomStringUtils.serialNumber(6)));
        empl.setMobilePhoneNumber(String.format("+7%s", UniqueRandomStringUtils.serialNumber(10)));
        empl.setLicenseCode(GSON.toJson(Sets.newHashSet("Нелицензированный пользователь")));
        //Выставляем новые значения на форме редактирования
        GUIBo.fillEmployeeMainFields(empl);
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "post"), empl.getPost());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "email"), empl.getEmail());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "dateOfBirth") + "//input",
                empl.getDateOfBirth());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "login"), empl.getLogin());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "internalPhoneNumber"),
                empl.getInternalPhoneNumber());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "cityPhoneNumber"),
                empl.getCityPhoneNumber());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "homePhoneNumber"),
                empl.getHomePhoneNumber());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.Any.ANY_VALUE, "mobilePhoneNumber"),
                empl.getMobilePhoneNumber());
        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        GUIForm.applyForm();
        //Проверки
        //Подготавливаем атрибуты, для проверки полей
        Attribute[] attributeforAssert = { SysAttribute.firstName(emplCase).setValue(empl.getFirstName()),
                SysAttribute.lastName(emplCase).setValue(empl.getLastName()),
                SysAttribute.performer(emplCase).setValue("нет"),
                SysAttribute.title(emplCase).setValue(DSLEmployee.getFullName(empl)),
                SysAttribute.post(emplCase).setValue(empl.getPost()),
                SysAttribute.parentOu(SharedFixture.ouCase()).setValue(SharedFixture.ou().getTitle()),
                SysAttribute.removed(emplCase).setValue("нет"),
                SysAttribute.login(emplCase).setValue(empl.getLogin()),
                SysAttribute.internalPhoneNumber(emplCase).setValue(empl.getInternalPhoneNumber()),
                SysAttribute.cityPhoneNumber(emplCase).setValue(empl.getCityPhoneNumber()),
                SysAttribute.homePhoneNumber(emplCase).setValue(empl.getHomePhoneNumber()),
                SysAttribute.mobilePhoneNumber(emplCase).setValue(empl.getMobilePhoneNumber()),
                SysAttribute.metaClass(emplCase).setValue(empl.getMetaclassTitle()),
                SysAttribute.uuid(emplCase).setValue(empl.getUuid()),
                SysAttribute.license(emplCase).setValue(AttributeUtils.prepareLicensesValue(empl.getLicenseCode())),
        };
        GUIPropertyList.assertPropertyListAttribute(contentPropertyList, attributeforAssert);
    }

    /**
     * Тестирование сохранения измененного наименования сотрудника при его редактировании вместе с атрибутами «Фамилия»,
     * «Имя» и «Отчество»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00614
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$115206347
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Изменить атрибуты сотрудника employee, выполнив скрипт:
     * <pre>
     * utils.edit(employee, ['title': '%значение title%',
     *         'firstName': '%значение firstName%',
     *         'lastName': '%значение lastName%',
     *         'middleName': '%значение middleName%'])
     * </pre></li>
     * <br>
     * <b>Проверка</b>
     * <li>Наименование сотрудника соответствует заданному в скрипте %значение title%</li>
     * </ol>
     */
    @Test
    public void testEditTitleWithFirstAndLastName()
    {
        // Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, false);
        DSLBo.add(employee);

        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Attribute firstNameAttr = SysAttribute.firstName(employeeClass);
        Attribute lastNameAttr = SysAttribute.lastName(employeeClass);
        Attribute middleNameAttr = SysAttribute.middleName(employeeClass);
        Attribute titleAttr = SysAttribute.title(employeeClass);
        // Выполнение действий
        firstNameAttr.setValue(ModelUtils.createTitle());
        lastNameAttr.setValue(ModelUtils.createTitle());
        middleNameAttr.setValue(ModelUtils.createTitle());
        titleAttr.setValue(ModelUtils.createTitle());

        String scriptTemplate = "utils.edit('%s', ['%s': '%s', '%s': '%s', '%s': '%s', '%s': '%s'])";
        String script = String.format(scriptTemplate, employee.getUuid(),
                titleAttr.getCode(), titleAttr.getValue(),
                firstNameAttr.getCode(), firstNameAttr.getValue(),
                lastNameAttr.getCode(), lastNameAttr.getValue(),
                middleNameAttr.getCode(), middleNameAttr.getValue());
        new ScriptRunner(script).runScript();
        // Проверка
        DSLBo.assertTitle(employee, titleAttr.getValue());
    }

    /**
     * Нельзя сделать пробел паролем NSDPRD-1314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00054
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника с лицензией</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника, нажимаем кнопку "Сменить пароль"</li>
     * <li>Заполняем форму: пробел, пробел</li>
     * <li>Подтверждаем действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма не исчезла</li>
     * </ol>
     */
    @Test
    public void testEmptyPassword()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.changePassword();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM);
        employee.setPassword(" ");
        tester.sendKeys(GUIXpath.Input.CP_FORM_PASSWORD_VALUE, employee.getPassword());
        tester.sendKeys(GUIXpath.Input.CP_FORM_DUPLICATE_PASSWORD_VALUE, employee.getPassword());
        //Проверяем только валидашку под полем Повторите пароль, т.к. валидашка не появляется по полем Новый пароль 
        //в 62 хроме в Jenkins
        GUIForm.applyFormAssertValidation(DAOAttribute.createPseudo("cpFormDuplicatePassword"),
                ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM);
    }

    /**
     * Проверка получения сообщения об ошибке при попытке смены лицензии у сотрудника на лицензию истекшим сроком
     * действия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00438
     * http://sd-jira.naumen.ru/browse/NSDPRD-2624
     * <ol>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать тип отдела ouCase, тип сотрудника employeeCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника employeeNamed (с лицензией Именная типа employeeCase в отделе ou)</li>
     * <li>Создать сотрудника employeeConcurrent (с лицензией Конкурентная типа employeeCase в отделе ou)</li>
     * <li>Создать контент с атрибутом лицензия в employeeCase</li>
     * <li>Загрузить файл лизензии с истекшим временем действия licenseDev.xml</li>
     * <br>
     * <b>Проверки</b>
     * <li>Заходим в карточку сотрудника employeeNamed, редактируем контент с атрибутом лицензия, выбираем
     * конкурентную лицензию</li>
     * <li>Подтверждаем действие.</li>
     * <li>Ожидаем получить сообщение об ошибке.</li>
     * <li>Заходим в карточку сотрудника employeeConcurrent, редактируем контент с атрибутом лицензия, выбираем
     * именную лицензию</li>
     * <li>Подтверждаем действие.</li>
     * <li>Ожидаем получить сообщение об ошибке.</li>
     * </ol>
     */
    @Test
    public void testExpirationLicense()
    {
        // Действие
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        GroupAttr employeeAttr = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(employeeAttr, createPseudo("Лицензия", "license", null));
        ContentForm employeeProperties = DAOContentCard.createPropertyList(employeeCase, employeeAttr);
        DSLContent.add(employeeProperties);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(employeeCase, MetaclassCardTab.OBJECTCARD));

        Bo employeeNamed = DAOEmployee.create(employeeCase, ou, true);
        employeeNamed.setLicenseCode(DAOBo.NAMED_LICENSE_SET);
        Bo employeeConcurrent = DAOEmployee.create(employeeCase, ou, true);
        employeeConcurrent.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(employeeNamed, employeeConcurrent);
        DSLAdmin.installLicense(DSLAdmin.LICENSE9_PATH);

        // Выполнение действия
        GUILogon.asSuper();

        // Проверка
        GUIBo.goToCard(employeeNamed);
        GUIPropertyList.clickEditLink(employeeProperties);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.InputComplex.LICENSE_VALUE, DAOBo.NO_LICENSE_STRING);
        GUISelect.select(GUIXpath.InputComplex.LICENSE_VALUE, DAOBo.CONCURRENT_LICENSE_STRING);
        String formatErrorMessage = "Срок действия лицензий \"%s\" истёк";
        String expectedErrorMessage = String.format(formatErrorMessage, "Конкурентная");
        GUIForm.applyFormAssertError(expectedErrorMessage);
        GUIForm.cancelDialog();

        GUIBo.goToCard(employeeConcurrent);
        GUIPropertyList.clickEditLink(employeeProperties);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.InputComplex.LICENSE_VALUE, DAOBo.NO_LICENSE_STRING);
        GUISelect.select(GUIXpath.InputComplex.LICENSE_VALUE, DAOBo.NAMED_LICENSE_STRING);
        expectedErrorMessage = String.format(formatErrorMessage, "Именная");
        GUIForm.applyFormAssertError(expectedErrorMessage);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование полей при смене пароля
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee</li>
     * <li>Установить сотруднику employee пароль 123</li>
     * <li>Добавить сотрудника employee в группу пользователей userGroup</li>
     * <li>Для типа сотрудника employeeCase настроить профиль profile (Роль = Сотрудник, Для нелицензированных)</li>
     * <li>Выдать все права типу employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать кнопку "Сменить пароль"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Поля для заполнения пароля пустые</li>
     * </ol>
     */
    @Test
    public void testFieldsByChangePassword()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        employee.setPassword(ModelUtils.createPassword());
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), false);
        RightGroup rights = new RightGroup(profile, employeeCase);
        rights.addAllRights(employeeCase);
        rights.apply();

        // Действие
        GUILogon.login(employee);
        GUIButtonBar.changePassword();
        GUITester.assertValue(GUIXpath.Input.CURRENT_PASSWORD_VALUE, "");
        GUITester.assertValue(GUIXpath.Input.CP_FORM_PASSWORD_VALUE, "");
        GUITester.assertValue(GUIXpath.Input.CP_FORM_DUPLICATE_PASSWORD_VALUE, "");
    }

    /**
     * Тестирование установки лицензии для сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00054
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел А типа В</li>
     * <li>Создать сотрудника E типа employeeCase</li>
     * <li>Создать контент с атрибутом лицензия в employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Загружаем лицензию</li>
     * <li>Заходим в карточку сотрудника , редактируем контент с атрибутом лицензия, выбираем именную лицензию</li>
     * <li>Подтверждаем действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке сотрудника E в контенте значение атрибута лицензия</li>
     * </ol>
     */
    @Test
    public void testLicense()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        GroupAttr employeeAtr = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(employeeAtr, createPseudo("Лицензия", "license", null));
        ContentForm employeeProperties = DAOContentCard.createPropertyList(employeeCase, employeeAtr);
        DSLContent.add(employeeProperties);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(SharedFixture.employeeCase(),
                MetaclassCardTab.OBJECTCARD));
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIPropertyList.clickEditLink(employeeProperties);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.InputComplex.LICENSE_VALUE, "named");
        GUIForm.applyForm();
        GUIBo.goToCard(employee);
        //Подготавливаем атрибуты, для проверки полей
        Attribute[] attributeforAssert = { createPseudo("Лицензия", "license", "named") };
        GUIPropertyList.assertPropertyListAttribute(employeeProperties, attributeforAssert);
    }

    /**
     * Тестирование перемещения сотрудника и ограничение типов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00155
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отделы А,B типа C, создать ou типа ouCase1</li>
     * <li>Создать сотрудника D типа E</li>
     * <li>В типе C создать контент F со списком вложенных объектов типа E</li>
     * <li>Ограничить тип отдела (атрибут parent) на тип C в employeeCase.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в отдел А и проверяем что в контенте E присутствует сотрудник D.<li>
     * <li>Зайти в отдел B и проверяем что в контенте E отсутствует сотрудник D.<li>
     * <li>Заходим в карточку сотрудника D</li>
     * <li>Нажимаем кнопку Переместить</li>
     * <li>На форме перемещения выбираем отдел B</li>
     * <li>Проверка ограничения</li>
     * <li>Нажать Переместить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма перемещения закрылась</li>
     * <li>Зайти в отдел B и проверяем что в контенте E присутствует сотрудник D.<li>
     * <li>Зайти в отдел A и проверяем что в контенте E отсутствует сотрудник D.<li>
     * </ol>
     */
    @Test
    public void testMove()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase, ouCase1);

        //Создаем контент типа "Список вложенных объектов" в карточке отдела для отображения сотрудников заданного типа
        ContentForm contentChildObjectList = DAOContentCard.createChildObjectList(ouCase.getFqn(),
                DAOEmployeeCase.createClass(), employeeCase);
        DSLContent.add(contentChildObjectList);

        Bo ouA = DAOOu.create(ouCase);
        Bo ouB = DAOOu.create(ouCase);
        Bo ou = DAOOu.create(ouCase1);
        DSLBo.add(ouA, ouB, ou);
        Bo employee = DAOEmployee.create(employeeCase, ouA, true);
        DSLBo.add(employee);

        //Ограничиваем тип отделов
        Attribute parent = SysAttribute.parent(employeeCase);
        DSLAttribute.editPermittedLinks(parent, ouCase);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ouA);
        String message = String.format("Сотрудник %s не обнаружен, в списке вложенных объектов, в отделе %s",
                DSLEmployee.getFullName(employee), ouA.getTitle());
        GUISimpleList.assertObjectPresent(contentChildObjectList, employee);
        GUIBo.goToCard(ouB);
        message = String.format("Сотрудник %s обнаружен, в списке вложенных объектов, в отделе %s",
                DSLEmployee.getFullName(employee), ouB.getTitle());
        GUISimpleList.assertObjectAbsence(contentChildObjectList, employee);
        GUIBo.goToCard(employee);
        tester.refresh();//Исключить случайные срабатывания теста. О проблеме известно - открывается не та форма
        // перемещения, отдела не сотрудника
        GUIButtonBar.move();
        Assert.assertTrue("Форма перемещения сотрудника не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        BoTree tree = new BoTree(GUIXpath.Id.DESTINATION_PROPERTY_VALUE, true);
        tree.setElementInSelectTree(ouB);

        //Проверка ограничения
        String ouTitle = tester.findDisplayed("//*[@id='" + GUIXpath.Id.DESTINATION_PROPERTY_VALUE + "']" + "//input")
                .getAttribute("value");
        tree.setElementInSelectTree(ou);
        String actualTitle = tester.findDisplayed(
                        "//*[@id='" + GUIXpath.Id.DESTINATION_PROPERTY_VALUE + "']" + "//input")
                .getAttribute("value");
        message = "Ограничение не работает.";
        assertEquals(message, ouTitle, actualTitle);

        GUIForm.applyForm();
        //Проверки
        GUIBo.goToCard(ouB);
        message = String.format("Сотрудник %s не обнаружен, в списке вложенных объектов, в отделе %s",
                DSLEmployee.getFullName(employee), ouB.getTitle());
        Assert.assertTrue(message,
                GUISimpleList.getUUIDsUnarchive(contentChildObjectList).contains(employee.getUuid()));
        GUIBo.goToCard(ouA);
        message = String.format("Сотрудник %s обнаружен, в списке вложенных объектов, в отделе %s",
                DSLEmployee.getFullName(employee), ouA.getTitle());
        Assert.assertFalse(message,
                GUISimpleList.getUUIDsUnarchive(contentChildObjectList).contains(employee.getUuid()));
    }

    /**
     * Тестирование не совпадения введенных паролей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>В карточке сотрудника employee нажать кнопку "Сменить пароль"</li>
     * <li>Проверить, что на форме отсутствует поле Текущий пароль</li>
     * <li>В поле Пароль ввести A</li>
     * <li>В поле Подтверждение пароля ввести B</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Появилось всплывающее сообщение: "Введенные пароли не совпадают"</li>
     * <li>Форма не закрылась</li>
     * <li>Пароль у сотрудника employee не применился</li>
     * </ol>
     */
    @Test
    public void testNotMatchEnteredPasswords()
    {
        // Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        String password = SdDataUtils.getObjectByUUID(employee).get("password");

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.changePassword();
        GUITester.assertExists(GUIXpath.Input.CURRENT_PASSWORD_VALUE, false,
                "На форме присутствует поле \"Текущий пароль\".");
        tester.sendKeys(GUIXpath.Input.CP_FORM_PASSWORD_VALUE, ModelUtils.createPassword());
        tester.sendKeys(GUIXpath.Input.CP_FORM_DUPLICATE_PASSWORD_VALUE, ModelUtils.createPassword());

        GUIForm.applyFormAssertValidation(GUIXpath.Div.PROPERTY_DIALOG_BOX, "Введенные пароли не совпадают");
        assertEquals("Пароль у сотрудника был изменён", password,
                SdDataUtils.getObjectByUUID(employee).get("password"));
    }

    /**
     * Тестирование не уникальности пароля для сотрудников
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудников employee1..2</li>
     * <li>Каждому из сотрудников установить пароль 123</li>
     * <li>Добавить сотрудников employee1..2 в группу пользователей userGroup</li>
     * <li>Для типа сотрудника employeeCase настроить профиль profile (Роль = Сотрудник, Для нелицензированных)</li>
     * <li>Выдать все права типу employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Заходим поочередно под каждым из сотрудников</li>
     * </ol>
     */
    @Test
    public void testNotUniquePasswordForEmployee()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo employee1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        Bo employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        DSLBo.add(employee1, employee2);

        UserGroup userGroup = new UserGroup(employee1, employee2);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), false);
        RightGroup rights = new RightGroup(profile, employeeCase);
        rights.addAllRights(employeeCase);
        rights.apply();

        // Выполнение действий и проверки
        GUILogon.login(employee1);
        GUIBo.assertThatBoCard(employee1);
        GUILogon.login(employee2);
        GUIBo.assertThatBoCard(employee2);
    }

    /**
     * Тестирование отсутствия видимости пароля в логе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00261
     * http://sd-jira.naumen.ru/browse/NSDPRD-4860 
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Залогиниться под суперпользователем</li>
     * <li>Перейти в карточку сотрудника employee, нажать кнопку "Сменить пароль"</li>
     * <li>Заполнить форму</li>
     * <li>Подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Зайти в консоль, на вкладке лога нажать кнопку Обновить</li>
     * <li>В фильтр ввести новый пароль</li>
     * <li>Проверить, что пароль отсутствует в логе</li>
     * </ol>
     */
    @Test
    public void testPasswordVisibility()
    {
        //подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, emplCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(emplCase, ou, false);
        DSLBo.add(employee);

        //действие
        GUILogon.asNaumen();
        GUIBo.goToCard(employee);
        GUIButtonBar.changePassword();
        String newPassword = ModelUtils.createPassword();
        GUIPasswordForm.setNewPassword(newPassword);
        GUIPasswordForm.confirmPassword(newPassword);
        GUIForm.applyForm();

        //Проверка
        GUINavigational.goToConsole();
        GUIConsole.refreshLog();
        GUIConsole.setFilter(newPassword);
        GUIConsole.assertAbsense(newPassword);
    }

    /**
     * Тестирование атрибута Исполнитель в классе Сотрудник
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать employee1 - с лицензией, неархивный</li>
     * <li>Создать employee2 - без лицензии, неархивный</li>
     * <li>Создать employee3 - без лицензии, архивный</li>
     * <li>Создать employee4 - с лицензией, архивный (прим.: хоть по постановке лицензия и высвобождается, но значение
     * самого атрибута не меняется)</li>
     * <b>Проверки</b>
     * <li>employee1 - исполнитель = true</li>
     * <li>employee2 - исполнитель = false</li>
     * <li>employee3 - исполнитель = false</li>
     * <li>employee4 - исполнитель = false</li>
     * </ol>
     */
    @Test
    public void testPerformerEmployee()
    {
        MetaClass employeeCase = SharedFixture.employeeCase();

        //Создаем сотрудников
        Bo ou = SharedFixture.ou();
        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, false);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, false);
        Bo employee4 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2, employee3, employee4);
        //Помещаем сотрудников 3 и 4 в архив
        DSLBo.editAttributeValue(employee3, createPseudo("Признак архивирования", "removed", "true"));
        DSLBo.editAttributeValue(employee4, createPseudo("Признак архивирования", "removed", "true"));
        //Проверки
        String expected, actual;
        String message = "Полученное значение атрибута 'Исполнитель' не совпало с ожидаемым.";
        expected = "true";
        actual = SdDataUtils.getObject(employeeCase.getFqn(), "UUID", employee1.getUuid()).get("performer");
        assertEquals(message, expected, actual);
        expected = "false";
        actual = SdDataUtils.getObject(employeeCase.getFqn(), "UUID", employee2.getUuid()).get("performer");
        assertEquals(message, expected, actual);
        actual = SdDataUtils.getObject(employeeCase.getFqn(), "UUID", employee3.getUuid()).get("performer");
        assertEquals(message, expected, actual);
        actual = SdDataUtils.getObject(employeeCase.getFqn(), "UUID", employee4.getUuid()).get("performer");
        assertEquals(message, expected, actual);
    }

    /**
     * Тестирование установки пароля сотрудника + проверка, что при входе под сотрудником попадаем в карточку сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел А типа В</li>
     * <li>Создать сотрудника E типа employeeCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логиниваемся под сотрудником</li>
     * <li>Проверяем, что находимся в карточке сотрудника</li>
     * <li>Нажимаем кнопку "Сменить пароль"</li>
     * <li>Заполняем форму</li>
     * <li>Подтверждаем действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Перелогиниваемся под сотрудником</li>
     * <li>Проверяем, что находимся в карточке сотрудника</li>
     * </ol>
     */
    @Test
    public void testSetPassword()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.login(employee);
        GUIBo.assertThatBoCard(employee);
        GUIBo.changePasswd(employee);
        GUILogon.logout();
        //Перелогиниваемся
        GUILogon.login(employee);
        GUIBo.assertThatBoCard(employee);
    }

    /**
     * Тестирование разархивирования сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou1, ou2 типа ouCase1</li>
     * <li>Создать отдел ou3 типа ouCase2</li>
     * <li>Создать сотрудника empl типа emplCase</li>
     * <li>В типе ouCase1 создать контент ChildObjectList со списком вложенных объектов класса сотрудник</li>
     * <li>Заархивировать сотрудника empl</li>
     * <li>Ограничить тип отдела (атрибут parent) на тип ouCase1 в emplCase.</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника empl</li>
     * <li>Нажимаем кнопку Восстановить из архива </li>
     * <li>Проверяем что текущее местоположение ou1.</li>
     * <li>Проверяем что ou3 недоступен</li>
     * <li>Выбираем ou2</li>
     * <li>Подтверждаем действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке вложенных сотрудников отдела ou2 есть сотрудник empl</li>
     * <li>В списке заархивированных вложенных сотрудников отдела ou2 нет сотрудника empl</li>
     * <li>empl не в архиве</li>
     * </ol>
     */
    @Test
    public void testUnArchive()
    {
        //Подготовка
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass ouCase2 = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase1, ouCase2, employeeCase);
        ContentForm content = DAOContentCard.createChildObjectList(ouCase1.getFqn(), DAOEmployeeCase.createClass(),
                employeeCase);
        DSLContent.add(content);

        //Ограничиваем тип отделов
        Attribute parent = SysAttribute.parent(employeeCase);
        DSLAttribute.editPermittedLinks(parent, ouCase1);

        Bo ou1 = DAOOu.create(ouCase1);
        Bo ou2 = DAOOu.create(ouCase1);
        Bo ou3 = DAOOu.create(ouCase2);
        DSLBo.add(ou1, ou2, ou3);
        Bo employee = DAOEmployee.create(employeeCase, ou1, true);
        DSLBo.add(employee);
        DSLBo.archive(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.restore();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.setCheckbox(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Complex.SELECT_PARENT_PROPERTY_VALUE_INPUT, true);
        Assert.assertTrue("Неверное текущее расположение на форме восстановления",
                tester.getText(GUIXpath.Div.FORM_CONTAINS + GUIXpath.SpecificComplex.LOCATION_PROPERTY_CAPTION)
                        .contains(ou1.getTitle()));
        BoTree tree = new BoTree(GUIXpath.Id.DESTINATION_PROPERTY_VALUE, true);
        tree.setElementInSelectTree(ou2);

        //Проверка ограничения
        String ouTitle = tester.findDisplayed("//*[@id='" + GUIXpath.Id.DESTINATION_PROPERTY_VALUE + "']" + "//input")
                .getAttribute("value");
        tree.setElementInSelectTree(ou3);
        String actualTitle = tester.findDisplayed(
                        "//*[@id='" + GUIXpath.Id.DESTINATION_PROPERTY_VALUE + "']" + "//input")
                .getAttribute("value");
        String message = "Ограничение не работает.";
        assertEquals(message, ouTitle, actualTitle);

        GUIForm.applyForm();
        employee.setParentUuid(ou2.getUuid());
        employee.setRemoved(false);
        //Проверки
        GUIBo.goToCard(ou2);
        GUISimpleList.assertObjectPresent(content, employee);
        GUISimpleList.showArchive(content);
        GUISimpleList.assertObjectAbsence(content, employee);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование видимости кнопки "Добавить/Удалить связи" на карточке класса Сотрудники
     * в списке связанных объектов для класса Команды
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$79780762
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00765
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип employeeCase в классе Сотрудник</li>
     * <li>Создать тип ouCase в классе Отдел</li>
     * <li>Получить системный атрибут Команды complexRelation</li>
     * <li>Установить значение "Расширенное редактирование связей" = false для complexRelation </li>
     * <li>Создать контент "Список связанных объектов" contEmpl для emplClass</li>
     * <li>Добавить на карточку contEmpl</li>
     * <br>
     * <b>Выполнение действий и проверка.</b>
     * <li>Зайти в ИА под "Супер пользователем" </li>
     * <li>Перейти на карточку Сотрудники</li>
     * <li>Нажать кнопку "Перейти к настройке панели действий" на тулпанеле contEmpl</li>
     * <li>Добавить кнопку "Добавить/Удалить связь" на тулпанель</li>
     * <li>Сохранить форму</li>
     * <li>Проверить присутствие кнопки "Добавить/Удалить связь"</li>
     * <li>Выйти из ИА</li>
     * <li>Зайти в ИО под "tester"</li>
     * <li>Создать отдел ou и сотрудника empl в этом отделе</li>
     * <li>Перейти на карточку сотрудника empl</li>
     * <li>Проверить отсутствие кнопки "Добавить/Удалить связь"</li>
     * <li>Установка значения атрибута complexRelation=true</li>
     * <li>Проверить присутствие кнопки "Добавить/Удалить связь"</li>
     * </ol>
     */
    @Test
    public void testVisibleButtonOfAddDeleteObjs()
    {
        //Подготовка
        MetaClass team = DAOTeamCase.createClass();
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass ouCase = DAOOuCase.create();

        Attribute complexRelation = SysAttribute.teams(emplClass);
        complexRelation.setComplexRelation(Boolean.FALSE.toString());
        DSLMetaClass.add(ouCase, employeeCase);
        DSLAttribute.edit(complexRelation);

        ContentForm contEmpl = DAOContentCard.createRelatedObjectList(emplClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.DEFAULT,
                emplClass.getCode() + "@" + SysAttribute.teams(team).getCode(),
                DAOGroupAttr.createSystem());
        ToolPanel toolPanel = Json.GSON.fromJson(contEmpl.getToolPanel(), ToolPanel.class);
        toolPanel.setUseSystemSettings(false);
        contEmpl.setToolPanel(toolPanel);
        DSLContent.add(contEmpl);
        GUIAdvListToolPanel guiToolPanel = contEmpl.advlist().toolPanel();

        //Выполнение действий и проверка
        GUILogon.asSuper();
        GUIContent.goToContent(contEmpl);
        GUIContent.clickEditToolPanel(contEmpl);

        contEmpl.advlist().editableToolPanel().rightClickTool(GUIButtonBar.BTN_ADD_DELETE_OBJS);
        contEmpl.advlist().editableToolPanel().clickAddContextMenuOption();
        GUIForm.applyModalForm();
        guiToolPanel.asserts().buttonsPresence(GUIButtonBar.BTN_ADD_DELETE_OBJS);
        GUILogon.logout();

        GUILogon.asTester();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo empl = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(empl);
        GUIBo.goToCard(empl);
        guiToolPanel.asserts().buttonsAbsence(GUIButtonBar.BTN_ADD_DELETE_OBJS);

        complexRelation.setComplexRelation(Boolean.TRUE.toString());
        complexRelation.setComplexTeamAttrGroup(DAOGroupAttr.createSystem().getCode());
        DSLAttribute.edit(complexRelation);
        tester.refresh();
        guiToolPanel.asserts().buttonsPresence(GUIButtonBar.BTN_ADD_DELETE_OBJS);
    }

    /**
     * Тестирование видимости кнопки "Удалить" на карточки класса Сотрудники
     * в списке связанных объектов для класса Команды
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80081433
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00688
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип employeeCase в классе Сотрудник</li>
     * <li>Создать тип ouCase в классе Отдел</li>
     * <li>Создать контент "Список связанных объектов" contEmpl с представлением "Простой список" для emplClass</li>
     * <li>Добавить на карточку contEmpl</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИА под "Суперпользователем" </li>
     * <li>Перейти на карточку Сотрудники</li>
     * <li>Проверить отсутствие кнопки "Удалить" на панели действий</li>
     * <li>Выйти из ИА</li>
     * <li>Зайти в ИО под "tester"</li>
     * <li>Создать отдел ou и сотрудника empl в этом отделе</li>
     * <li>Перейти на карточку сотрудника empl</li>
     * <li>Проверить отсутствие кнопки "Удалить" на панели действий</li>
     * </ol>
     */
    @Test
    public void testVisibleButtonOfDeleteOnObjectList()
    {
        //Подготовка
        MetaClass team = DAOTeamCase.createClass();
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        ContentForm contEmpl = DAOContentCard.createRelatedObjectList(emplClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.DEFAULT,
                emplClass.getCode() + "@" + SysAttribute.teams(team).getCode(),
                DAOGroupAttr.createSystem());
        DSLContent.add(contEmpl);
        GUIAdvListToolPanel guiToolPanel = contEmpl.advlist().toolPanel();

        //Выполнение действий и проверка
        GUILogon.asSuper();
        GUIContent.goToContent(contEmpl);
        guiToolPanel.asserts().buttonsAbsence(GUIButtonBar.BTN_DEL);
        GUILogon.logout();

        GUILogon.asTester();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo empl = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(empl);
        GUIBo.goToCard(empl);
        guiToolPanel.asserts().buttonsAbsence(GUIButtonBar.BTN_DEL);
    }
}
