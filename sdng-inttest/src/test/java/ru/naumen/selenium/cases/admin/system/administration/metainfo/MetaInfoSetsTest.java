package ru.naumen.selenium.cases.admin.system.administration.metainfo;

import java.io.File;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;

/**
 * Тестирование выгрузки метаинформации связанной с комплектами
 * <AUTHOR>
 * @since 29.04.2025
 */
class MetaInfoSetsTest extends AbstractTestCaseJ5
{
    private static MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Выполнить скрипт включения функционала профилей администрирования:
     * beanFactory.getBean('adminProfilesConfiguration').setCustomAdminProfilesEnable(true)</li>
     * <li>Выполнить скрипт включения функционала комплектов:
     * beanFactory.getBean('settingsSetStorageServiceConfiguration').setSettingSetsEnabled(true)</li>
     * <li>Создать класс userClass</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
    }

    /**
     * Тестирование изменения настроек пользовательского класса с настроенным комплектом при загрузке
     * полной метаинформации, в которой отсутствует комплект
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выгрузить полную метаинформацию</li>
     * <li>Добавить комплект setCase.</li>
     * <li>Отредактировать класс userClass: выбрать в атрибуте Комплект setCase.</li>
     * <li>Загрузить метаинформацию, выгруженную в шаге 1.</li>
     * <li>Войти в ИА под naumen.</li>
     * <li><b>Проверить</b>, что комплект setCase присутствует в списке (Настройка системы -> Комплекты).</li>
     * <li><b>Проверить</b>, что у класса userClass в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * </ol>
     */
    @Test
    void testChangeClassSettingsWithCustomSetWhenLoadFullMetaInfoWithoutSet()
    {
        // Выполнение действий и проверки
        File metaInfo = DSLMetainfoTransfer.exportMetainfo();

        SettingsSet setCase = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(setCase);

        userClass.setSettingsSet(setCase);
        DSLMetaClass.edit(userClass);

        DSLMetainfoTransfer.importMetainfo(metaInfo);

        GUILogon.asSuper();
        GUINavigational.goToSets();
        GUISettingsSet.advlist().content().asserts().rowsPresence(setCase);
        GUIMetaClass.goToCard(userClass);
        GUISettingsSet.assertSettingsSetOnCards("");
    }

    /**
     * Тестирование изменения настроек пользовательского класса с настроенным комплектом при загрузке частичной
     * метаинформации, в которой отсутствует комплект
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выгрузить частичную метаинформацию с настройками класса userClass.</li>
     * <li>Добавить комплект setCase.</li>
     * <li>Отредактировать класс userClass: выбрать в атрибуте Комплект setCase.</li>
     * <li>Загрузить метаинформацию, выгруженную в шаге 1.</li>
     * <li>Войти в ИА под naumen.</li>
     * <li><b>Проверить</b>, что комплект setCase присутствует в списке (Настройка системы -> Комплекты).</li>
     * <li><b>Проверить</b>, что у класса userClass в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * </ol>
     */
    @Test
    void testChangeClassSettingsWithCustomSetWhenLoadPartialMetaInfoWithoutSet()
    {
        // Выполнение действий и проверки
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectMetaClasses(exportModel, userClass);
        File metaInfo = DSLMetainfoTransfer.exportMetainfo(exportModel);

        SettingsSet setCase = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(setCase);

        userClass.setSettingsSet(setCase);
        DSLMetaClass.edit(userClass);

        DSLMetainfoTransfer.importMetainfo(metaInfo);

        GUILogon.asSuper();
        GUINavigational.goToSets();
        GUISettingsSet.advlist().content().asserts().rowsPresence(setCase);
        GUIMetaClass.goToCard(userClass);
        GUISettingsSet.assertSettingsSetOnCards("");
    }

    /**
     * Тестирование изменения настроек пользовательского класса с настроенным комплектом при загрузке полной
     * метаинформации с замещением, в которой отсутствует комплект
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выгрузить полную метаинформацию</li>
     * <li>Добавить комплект setCase.</li>
     * <li>Отредактировать класс userClass: выбрать в атрибуте Комплект setCase.</li>
     * <li>Загрузить метаинформацию c замещением, выгруженную в шаге 1.</li>
     * <li>Войти в ИА под naumen.</li>
     * <li><b>Проверить</b>, что комплект setCase отсутствует в списке (Настройка системы -> Комплекты).</li>
     * <li><b>Проверить</b>, что у класса userClass в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * </ol>
     */
    @Test
    void testChangeClassSettingsWithCustomSetWhenLoadFullReloadMetaInfoWithoutSet()
    {
        // Выполнение действий и проверки
        File metaInfo = DSLMetainfoTransfer.exportMetainfo();

        SettingsSet setCase = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(setCase);

        userClass.setSettingsSet(setCase);
        DSLMetaClass.edit(userClass);

        DSLMetainfoTransfer.importFullReloadMetainfo(metaInfo);

        GUILogon.asSuper();
        GUINavigational.goToSets();
        GUISettingsSet.advlist().content().asserts().rowsAbsence(setCase);
        GUIMetaClass.goToCard(userClass);
        GUISettingsSet.assertSettingsSetOnCards("");
    }
}
