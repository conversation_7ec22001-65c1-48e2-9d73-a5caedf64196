package ru.naumen.selenium.cases.operator.embeddedapplication.events;

import static ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication.TEST_DIV_ID;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование возможности подписки на различные события через JS API (jsApi.events)
 *
 * <AUTHOR>
 * @since May 7, 2019
 */
class JsApiEventsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userCase, employeeCase;
    private static Bo employee;

    /**
     * <ol>
     *   <b>Общая подготовка для всех тестов.</b>
     *   <li>Создать пользовательский класс <code>userClass</code> с назначением ответственного и его тип
     *   <code>userCase</code></li>
     *   <li>Создать тип Сотрудника <code>employeeCase</code></li>
     *   <li>Создать сотрудника <code>employee</code> типа <code>employeeCase</code></li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createWithResp();
        userCase = DAOUserCase.create(userClass);
        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(userClass, userCase, employeeCase);

        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
    }

    /**
     * Тестирование корректного вызова функции на событие сворачивания приложения из полного экрана
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72892303
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить свой размер при
     *   сворачивании из полноэкранного режима, и разрешить его раскрытие на полный экран:
     *   <pre>
     *   --------------------------------------------------
     *     jsApi.events.onFullscreenDisabled(
     *         () => document.getElementById('test_div').innerText = jsApi.contents.getHeight())
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия.</b>
     *   <li>Войти под пользователем <code>employee</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Нажать кнопку "На всю страницу"</li>
     *   <li>Подождать переход контента с ВП в полноэкранный режим</li>
     *   <li>Нажать кнопку "Свернуть"</li>
     *   <li>Подождать переход контента с ВП в обычный режим</li>
     *   <br>
     *   <b>Проверки.</b>
     *   <li>Проверить, что во встроенном приложении выведено число, равное высоте контента ВП в нормальном режиме</li>
     * </ol>
     */
    @Test
    void testFullscreenDisabledEvent()
    {
        // Подготовка
        String jsContent = String.format("document.addEventListener('DOMContentLoaded', function() {\n"
                                         + "    var testDiv = document.getElementById('%s')\n"
                                         + "    testDiv.innerText = 0\n"
                                         + "    jsApi.events.onFullscreenDisabled(() => testDiv.innerText = jsApi"
                                         + ".contents.getHeight())"
                                         + "});\n", TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        application.setFullscreenAllowed(true);
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(content);

        // Действия
        GUILogon.login(employee);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        int expectedHeight = GUIEmbeddedApplication.getHeight(content);

        GUIContent.clickFullscreenButton(content);
        GUIEmbeddedApplication.waitForFullscreenHeight(content);

        GUIContent.clickNormalScreenButton(content);
        GUIEmbeddedApplication.waitForHeight(content, expectedHeight);

        // Проверки
        int actualHeight = Integer.parseInt(GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
        GUIEmbeddedApplication.assertHeight(expectedHeight, actualHeight);
    }

    /**
     * Тестирование корректного вызова функции на событие раскрытия приложения на полный экран
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$72892303
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить свой размер при
     *   раскрытии на полный экран, и разрешить его раскрытие на полный экран:
     *   <pre>
     *   --------------------------------------------------
     *     jsApi.events.onFullscreenEnabled(
     *         () => document.getElementById('test_div').innerText = jsApi.contents.getHeight())
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия.</b>
     *   <li>Войти под пользователем <code>employee</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Нажать кнопку "На всю страницу"</li>
     *   <li>Подождать переход контента с ВП в полноэкранный режим</li>
     *   <br>
     *   <b>Проверки.</b>
     *   <li>Проверить, что во встроенном приложении выведено число, равное высоте контента ВП в полноэкранном
     *   режиме</li>
     * </ol>
     */
    @Test
    void testFullscreenEnabledEvent()
    {
        // Подготовка
        String jsContent = String.format("jsApi.events.onFullscreenEnabled(\n"
                                         + "    () => document.getElementById('%s').innerText = jsApi.contents"
                                         + ".getHeight())",
                TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        application.setFullscreenAllowed(true);
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(content);

        // Действия
        GUILogon.login(employee);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);

        GUIContent.clickFullscreenButton(content);
        int expectedHeight = GUIEmbeddedApplication.getFullscreenHeight();
        GUIEmbeddedApplication.waitForHeight(content, expectedHeight);

        // Проверки
        int actualHeight = Integer.parseInt(GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
        GUIEmbeddedApplication.assertHeight(expectedHeight, actualHeight);
    }

    /**
     * Тестирование получения режима отображения ВП (полноэкранный режим или обычный)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$116892153
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить режим отображения
     *   приложения при сворачивании и разворачивании, и разрешить его раскрытие на полный экран:
     *   <pre>
     *   --------------------------------------------------
     *     document.write(jsApi.getViewMode())
     *     jsApi.events.onFullscreenDisabled(() => document.body.innerHTML = jsApi.getViewMode())
     *     jsApi.events.onFullscreenEnabled(() => document.body.innerHTML = jsApi.getViewMode())
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под пользователем <code>employee</code></li>
     *   <li>Проверить, что во встроенном приложении метод <code>jsApi.getViewMode()</code> вернул "normal"</li>
     *   <li>Нажать кнопку "На всю страницу"</li>
     *   <li>Подождать переход контента с ВП в полноэкранный режим</li>
     *   <li>Проверить, что во встроенном приложении метод <code>jsApi.getViewMode()</code> вернул "fullScreen"</li>
     *   <li>Нажать кнопку "Свернуть"</li>
     *   <li>Подождать переход контента с ВП в обычный режим</li>
     *   <li>Проверить, что во встроенном приложении метод <code>jsApi.getViewMode()</code> вернул "normal"</li>
     * </ol>
     */
    @Test
    void testGetViewMode()
    {
        //Подготовка
        String jsContent = "document.write(jsApi.getViewMode());" +
                           "jsApi.events.onFullscreenDisabled(() => document.body.innerHTML = jsApi.getViewMode()); \n"
                           +
                           "jsApi.events.onFullscreenEnabled(() => document.body.innerHTML = jsApi.getViewMode())";
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        application.setFullscreenAllowed(true);
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(content);

        //Действия и проверки
        GUILogon.login(employee);

        GUIContent.assertPresent(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        int expectedHeight = GUIEmbeddedApplication.getHeight(content);

        Assertions.assertEquals("normal", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        GUIContent.clickFullscreenButton(content);
        GUIEmbeddedApplication.waitForFullscreenHeight(content);
        Assertions.assertEquals("fullScreen", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        GUIContent.clickNormalScreenButton(content);
        GUIEmbeddedApplication.waitForHeight(content, expectedHeight);
        Assertions.assertEquals("normal", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование корректного вызова слушателя на событие становления контента приложения видимым
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038543
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Сгенерировать случайные строки <code>randomString</code> и <code>titleString</code></li>
     *   <li>Добавить встроенное приложение, которое будет выводить <code>randomString</code> при первом срабатывании
     *   слушателя на событие становления контента видимым:
     *   <pre>
     *   --------------------------------------------------
     *     var triggered = false
     *     jsApi.events.onContentShow(() => {
     *         if (triggered) {
     *             return
     *         }
     *         triggered = true
     *         document.body.innerHTML = '$randomString'
     *     })
     *   --------------------------------------------------
     *   Где:
     *     1) $randomString - строка <code>randomString</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП на форму редактирования объекта Компания с условием видимости - атрибут Название
     *   должен содержать случайную строку <code>titleString</code></li>
     *   <br>
     *   <b>Действия.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на форму редактирования объекта Компания</li>
     *   <li>Заполнить атрибут Название строкой <code>titleString</code> и перевести фокус с поля</li>
     *   <li>Проверить, что контент с ВП появился</li>
     *   <br>
     *   <b>Проверки.</b>
     *   <li>Проверить, что во встроенном приложении выведен текст <code>randomString</code></li>
     * </ol>
     */
    @Test
    void testContentVisibleEvent()
    {
        // Подготовка
        String expectedText = ModelUtils.createText(10);
        String jsContent = String.format("var triggered = false\n"
                                         + "jsApi.events.onContentShow(() => {\n"
                                         + "    if (triggered) {\n"
                                         + "        return\n"
                                         + "    }\n"
                                         + "    triggered = true\n"
                                         + "    document.body.innerHTML = '%s'\n"
                                         + "})", expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        Attribute titleAttr = SysAttribute.title(DAORootClass.create());
        String title = ModelUtils.createTitle();
        FilterBlockOr condition = new FilterBlockOr(titleAttr, FilterCondition.CONTAINS, false, title);
        ListFilter visibilityCondition = new ListFilter(new FilterBlockAnd(condition));

        String rootFqn = SharedFixture.root().getMetaclassFqn();
        ContentForm applicationContent = DAOContentEditForm.createEmbeddedApplication(rootFqn, application);
        applicationContent.setVisibilityCondition(visibilityCondition);
        DSLContent.add(applicationContent);

        // Действия
        GUILogon.asTester();
        GUIBo.goToEditForm(SharedFixture.root());
        GUIForm.fillAttribute(titleAttr, title);
        GUIForm.cleanFocus();
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);

        // Проверки
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent));
    }

    /**
     * Тестирование корректного вызова слушателя на событие становления контента приложения скрытым
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038543
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Сгенерировать случайные строки <code>randomString</code> и <code>titleString</code></li>
     *   <li>Добавить встроенное приложение, которое будет выводить <code>randomString</code> при первом срабатывании
     *   слушателя на событие становления контента скрытым:
     *   <pre>
     *   --------------------------------------------------
     *     var triggered = false
     *     jsApi.events.onContentHide(() => {
     *         if (triggered) {
     *             return
     *         }
     *         triggered = true
     *         document.body.innerHTML = '$randomString'
     *     })
     *   --------------------------------------------------
     *   Где:
     *     1) $randomString - строка <code>randomString</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП на форму редактирования объекта Компания с условием видимости - атрибут Название
     *   НЕ должен содержать случайную строку <code>titleString</code></li>
     *   <br>
     *   <b>Действия.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на форму редактирования объекта Компания</li>
     *   <li>Заполнить атрибут название строкой <code>titleString</code> и перевести фокус с поля</li>
     *   <li>Проверить, что контент с ВП скрылся</li>
     *   <li>Заполнить атрибут название случайно строкой и перевести фокус с поля</li>
     *   <li>Проверить, что контент с ВП появился</li>
     *   <br>
     *   <b>Проверки.</b>
     *   <li>Проверить, что во встроенном приложении выведен текст <code>randomString</code></li>
     * </ol>
     */
    @Test
    void testContentHiddenEvent()
    {
        // Подготовка
        String expectedText = ModelUtils.createText(10);
        String jsContent = String.format("var triggered = false\n"
                                         + "jsApi.events.onContentHide(() => {\n"
                                         + "    if (triggered) {\n"
                                         + "        return\n"
                                         + "    }\n"
                                         + "    triggered = true\n"
                                         + "    document.body.innerHTML = '%s'\n"
                                         + "})", expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        Attribute titleAttr = SysAttribute.title(DAORootClass.create());
        String title = ModelUtils.createTitle();
        FilterBlockOr condition = new FilterBlockOr(titleAttr, FilterCondition.NOT_CONTAINS, false, title);
        ListFilter visibilityCondition = new ListFilter(new FilterBlockAnd(condition));

        String rootFqn = SharedFixture.root().getMetaclassFqn();
        ContentForm applicationContent = DAOContentEditForm.createEmbeddedApplication(rootFqn, application);
        applicationContent.setVisibilityCondition(visibilityCondition);
        DSLContent.add(applicationContent);

        // Действия
        GUILogon.asTester();
        GUIBo.goToEditForm(SharedFixture.root());
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIForm.fillAttribute(titleAttr, title);
        GUIForm.cleanFocus();
        GUIContent.assertAbsence(applicationContent);
        GUIForm.fillAttribute(titleAttr, ModelUtils.createCode());
        GUIForm.cleanFocus();
        GUIContent.assertPresent(applicationContent);

        // Проверки
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent));
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления прав
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$152373088
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * <ol>
     *   <b>Подготовка.</b>
     *   {@link #prepareFixture() Общая подготовка}
     *   <li>Создать объект <code>userBo</code> типа userCase</li>
     *   <li>Сгенерировать случайную строку <code>randomString</code></li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить
     *   <code>randomString</code> при срабатывании слушателя на событие обновления прав:
     *   <pre>
     *   --------------------------------------------------
     *       jsApi.events.onUpdatePermissions(_ => document.body.innerText = '$randomString')
     *   --------------------------------------------------
     *   Где:
     *     1) $randomString - строка <code>randomString</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Назначить ответственного за объект <code>userBo</code> через форму смены ответственного</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст <code>randomString</code></li>
     * </ol>
     */
    @Test
    void testUpdatePermissionsEvent()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        String randomString = ModelUtils.createText(10);
        String jsContent = String.format(
                "jsApi.events.onUpdatePermissions(_ => document.body.innerText = '%s')", randomString);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIButtonBar.changeResponsible();
        GUISc.selectResponsible(SharedFixture.employee(), SharedFixture.team());
        GUIForm.applyModalForm();
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }
}
