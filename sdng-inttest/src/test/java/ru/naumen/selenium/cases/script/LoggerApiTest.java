package ru.naumen.selenium.cases.script;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.scripts.DSLLoggingApi;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты на методы скриптового API, отвечающие за логирование <br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 *
 * <AUTHOR>
 * @since 30.07.2024
 */
public class LoggerApiTest extends AbstractTestCase
{
    /**
     * Общая подготовка
     * <ol>
     * <li>Включить параметр ru.naumen.scriptClassLoggingEnabled</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.enableScriptClassLogging(true);
    }

    /**
     * Тестирование методов api.logging.setLogLevel() и api.logging.getLogLevel()
     * для скриптового модуля <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$263471607 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$273968057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль с кодом $moduleCode:
     *     Текст модуля:
     *     <pre>
     *      package ru.naumen.modules.test
     *      def logMsg() {
     *        logger.info('logging message: ' + 'MESSAGE FOR LOG')
     *      };
     *      </pre>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.modules.test.$moduleCode', 'ERROR')</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>modules.$moduleCode.logMsg()</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>return api.logging.getLogLevel('ru.naumen.modules.test.$moduleCode')</pre>
     * </li>
     * <li>Проверить, что скрипт вернул "ERROR"</li>
     * <li>Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.modules.test.$moduleCode', 'INFO')</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>modules.$moduleCode.logMsg()</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>return api.logging.getLogLevel('ru.naumen.modules.test.$moduleCode')</pre>
     * </li>
     * <li>Проверить, что скрипт вернул "INFO"</li>
     * <li>Проверить, что в логе только 1 раз встречается запись "test.$moduleCode - logging message: MESSAGE FOR
     * LOG"</li>
     * </ol>
     */
    @Test
    public void testSetLogLevelForScriptModule()
    {
        // Подготовка
        String messageForLog = ModelUtils.createDescription();
        ModuleConf moduleConf = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                def logMsg() {
                    logger.info('logging message: ' + '%s')
                }""".formatted(messageForLog));
        DSLModuleConf.add(moduleConf);

        // Действия и проверки
        String moduleCode = moduleConf.getCode();
        String className = "ru.naumen.modules.test." + moduleConf.getCode();
        String expectedLogMessage = "test." + moduleCode + " - logging message: " + messageForLog;
        String logCommand = "modules.%s.logMsg()".formatted(moduleCode);

        DSLLoggingApi.setLogLevel(className, "ERROR");
        ScriptRunner.executeScript(logCommand);
        DSLLoggingApi.assertLogLevel(className, "ERROR");

        DSLLoggingApi.setLogLevel(className, "INFO");
        ScriptRunner.executeScript(logCommand);
        DSLLoggingApi.assertLogLevel(className, "INFO");

        DSLLog.assertCountOccurrencesInCurrentTestLogAsString("Сообщение должно логироваться только 1 раз",
                1, expectedLogMessage);
    }

    /**
     * Тестирование метода api.logging.setLogLevel() для скрипта у которого есть код<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$263471607 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$273968057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт script с кодом $scriptCode:
     *   <pre>
     *     package ru.naumen.script.test
     *     logger.debug('logging message: ' + 'MESSAGE FOR LOG')
     *    </pre>
     * </li>
     * <li>Создать задачу планировщика schedulerTask типа Скрипт со скриптом script</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.script.test.$scriptCode', 'INFO')</pre>
     * </li>
     * <li>Запустить задачу планировщика schedulerTask</li>
     * <li>
     *     Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.script.test.$scriptCode', 'DEBUG')</pre>
     * </li>
     * <li>Запустить задачу планировщика schedulerTask</li>
     * <li>Проверить, что в логе только 1 раз встречается запись "test.$scriptCode - logging message: MESSAGE FOR
     * LOG"</li>
     * </ol>
     */
    @Test
    public void testSetLogLevelForScript()
    {
        // Подготовка
        String messageForLog = ModelUtils.createDescription();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("""
                package ru.naumen.script.test
                logger.debug('logging message: ' + '%s')"""
                .formatted(messageForLog));
        DSLScriptInfo.addScript(script);
        SchedulerTaskScript schedulerTask = DAOSchedulerTask.createScriptRule(script);
        DSLSchedulerTask.addTask(schedulerTask);

        // Действия и проверки
        String className = "ru.naumen.script.test." + script.getCode();
        String expectedLogMessage = "test." + script.getCode() + " - logging message: " + messageForLog;

        DSLLoggingApi.setLogLevel(className, "INFO");
        DSLSchedulerTask.forceRunAndWaitExecuted(schedulerTask);

        DSLLoggingApi.setLogLevel(className, "DEBUG");
        DSLSchedulerTask.forceRunAndWaitExecuted(schedulerTask);

        DSLLog.assertCountOccurrencesInCurrentTestLogAsString("Сообщение должно логироваться только 1 раз",
                1, expectedLogMessage);
    }

    /**
     * Тестирование метода api.logging.setLogLevel() для класса помеченного &#64InjectApi
     * в скриптовом модуле<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$263471607 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$273968057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль:
     *     <pre>
     *     package ru.naumen.modules.test
     *     &#64InjectApi
     *     class TestClassWithInjectApi {
     *         void logMsg()
     *         {
     *             logger.debug('logging message ' + 'MESSAGE FOR LOG')
     *         }
     *     }
     *      </pre>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.modules.test.TestClassWithInjectApi', 'INFO')
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>new ru.naumen.modules.test.TestClassWithInjectApi().logMsg()</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>api.logging.setLogLevel('ru.naumen.modules.test.TestClassWithInjectApi', 'DEBUG')</pre>
     * </li>
     * <li>Выполнить скрипт:
     *     <pre>new ru.naumen.modules.test.TestClassWithInjectApi().logMsg()</pre>
     * </li>
     * <li>Проверить, что в логе только 1 раз встречается запись  "test.TestClassWithInjectApi - logging message:
     * MESSAGE FOR LOG"</li>
     * </ol>
     */
    @Test
    public void testSetLogLevelForScriptModuleWithInjectApi()
    {
        // Подготовка
        String messageForLog = ModelUtils.createDescription();
        ModuleConf moduleConf = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                @InjectApi
                class TestClassWithInjectApi {
                    void logMsg()
                    {
                        logger.debug('logging message: ' + '%s')
                    }
                }
                def test() { // метод существует просто для того, чтобы модуль был скриптом (является groovy.lang.Script)
                             // Это нужно только для того, чтобы тест работал в режиме компиляции oneByOne
                    new TestClassWithInjectApi().logMsg()
                }
                """.formatted(messageForLog));
        DSLModuleConf.add(moduleConf);

        // Действия и проверки
        String className = "ru.naumen.modules.test.TestClassWithInjectApi";
        String logCommand = "new ru.naumen.modules.test.TestClassWithInjectApi().logMsg()";

        DSLLoggingApi.setLogLevel(className, "INFO");
        ScriptRunner.executeScript(logCommand);

        DSLLoggingApi.setLogLevel(className, "DEBUG");
        ScriptRunner.executeScript(logCommand);

        String expectedLogMessage = "test.TestClassWithInjectApi - logging message: " + messageForLog;
        DSLLog.assertCountOccurrencesInCurrentTestLogAsString("Сообщение должно логироваться только 1 раз",
                1, expectedLogMessage);
    }

    /**
     * Тестирование логирования нескольких скриптовых модулей в одном скрипте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$273968057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать 2 скриптовых модуля firstModule и secondModule с текстом скрипта:<pre>
     *     package ru.naumen.modules.test
     *     def logMsg(def msg) {
     *         logger.info('logging message: ' + msg)
     *     }</pre>
     * </li>
     * <li>Установить уровень логирования модулей на INFO</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт<pre>
     *     modules.%s.logMsg(%s)
     *     modules.%s.logMsg(%s)</pre></li>
     * <li>Проверить, что в логе встречается запись  "INFO  test.firstModule - logging message: MESSAGE FOR LOG"</li>
     * <li>Проверить, что в логе встречается запись  "INFO  test.secondModule - logging message: MESSAGE FOR LOG"</li>
     * </ol>
     */
    @Test
    public void testLogBySeveralModule()
    {
        // Подготовка
        String messageForLog = ModelUtils.createDescription();
        String scriptBody = """
                package ru.naumen.modules.test
                def logMsg() {
                    logger.info('logging message: ' + '%s')
                }""".formatted(messageForLog);
        ModuleConf firstModule = DAOModuleConf.createWithBody(scriptBody);
        ModuleConf secondModule = DAOModuleConf.createWithBody(scriptBody);
        DSLModuleConf.add(firstModule, secondModule);

        String firstModuleCode = firstModule.getCode();
        String secondModuleCode = secondModule.getCode();
        DSLLoggingApi.setLogLevel("ru.naumen.modules.test." + firstModuleCode, "INFO");
        DSLLoggingApi.setLogLevel("ru.naumen.modules.test." + secondModuleCode, "INFO");

        // Действия и проверки
        ScriptRunner.executeScript("""
                        modules.%s.logMsg()
                        modules.%s.logMsg()""",
                firstModuleCode, secondModuleCode);

        String expectedLogMessagePattern = "INFO  test.%s - logging message: " + messageForLog;
        DSLLog.assertLog()
                .assertPresent(expectedLogMessagePattern.formatted(firstModuleCode))
                .assertPresent(expectedLogMessagePattern.formatted(secondModuleCode));
    }
}