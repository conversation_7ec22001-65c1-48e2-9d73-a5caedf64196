package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.AGGREGATED_TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.AggregateType.WITH_FOLDER;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута типа Агрегирующий на формах через мобильное API.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestAggregatePossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userCase;
    private static Attribute aggregateAttr, aggregateFoldersAttr;
    private static Folder ouFolder;
    private static Bo ou, ou2, employee, employee2, employee3;
    private static Bo[] ous;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать папку ouFolder в отделе</li>
     * <li>Создать отделы ou, ou2 и добавить отдел ou в папку ouFolder</li>
     * <li>Создать в отделе ou сотрудников employee, employee2</li>
     * <li>Создать в отделе ou2 сотрудников employee3</li>
     * <li>Создать дополнительно ещё 20 отделов ous для тестирования пагинации</li>
     * <li>Создать в классе userCase атрибут типа Агрегирующий aggregateAttr с представлением "Поле выбора"</li>
     * <li>Создать в классе userCase атрибут типа Агрегирующий aggregateFoldersAttr с представлением "Поле выбора с
     * папками"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибуты aggregateAttr, aggregateFoldersAttr</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        ouFolder = DAOFolder.create(DAOOuCase.createClass());
        DSLFolder.add(ouFolder);

        Attribute folderAttr = SysAttribute.folders(userCase);
        folderAttr.setItemValue(ouFolder);

        ou = DAOOu.create(SharedFixture.ouCase());
        ou.setUserAttribute(folderAttr);
        ou2 = DAOOu.create(SharedFixture.ouCase());
        DAOBo.appendTitlePrefixes(0, 22, ou, ou2);
        DSLBo.add(ou, ou2);
        DSLSearch.updateIndex(ou, ou2);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        employee2 = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        employee3 = DAOEmployee.create(SharedFixture.employeeCase(), ou2, true);
        DAOBo.appendTitlePrefixes(employee, employee2, employee3);
        DSLBo.add(employee, employee2, employee3);
        DSLSearch.updateIndex(employee, employee2, employee3);

        int expectedCount = 20;
        ous = new Bo[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            ous[i] = DAOOu.create(SharedFixture.ouCase());
        }
        DAOBo.appendTitlePrefixes(2, 22, ous);
        DSLBo.add(ous);
        DSLSearch.updateIndex(ous);

        String ousPart = Arrays.stream(ous).map(Bo::getUuid).collect(Collectors.joining("','", "'", "'"));
        ScriptInfo aggregateScriptInfo = DAOScriptInfo.createNewScriptInfo("""
                        if (!subject) return []
                        return [['%s': ['%s', '%s'], '%s': ['%s']], %s]
                        """,
                ou.getUuid(), employee.getUuid(), employee2.getUuid(), ou2.getUuid(), employee3.getUuid(), ousPart);
        DSLScriptInfo.addScript(aggregateScriptInfo);

        aggregateAttr = DAOAttribute.createAggregate(userCase, AggregatedClasses.OU, null, null);
        DAOAttribute.changeToEditFilter(aggregateAttr, aggregateScriptInfo);
        aggregateFoldersAttr = DAOAttribute.createAggregate(userCase, AggregatedClasses.OU, null, null);
        aggregateFoldersAttr.setEditPresentation(WITH_FOLDER);
        DAOAttribute.changeToEditFilter(aggregateFoldersAttr, aggregateScriptInfo);
        DSLAttribute.add(aggregateAttr, aggregateFoldersAttr);

        addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, aggregateAttr, aggregateFoldersAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения возможных значений атрибута типа Агрегирующий с представлением "Поле выбора".
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = aggregateAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из: пустого элемента,
     * отделов ou, ou2 и первых 17 отделов из ous, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее только из отделов ou,
     * ou2, первых 18 отделов из ous, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = ou,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из сотрудников employee,
     * employee2</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут aggregateAttr имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут aggregateAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testAggregate()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm);
        // получаем дерево для старой версии: должен вернуться пустой элемент
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateAttr)
                .assertValues(
                        tree(
                                emptyTreeElement(),
                                treeElement(ou).leaf(false).selectable(false),
                                treeElement(ou2).leaf(false).selectable(false)
                        ).with(
                                treeElements(Arrays.copyOf(ous, 17))
                        ).hasMore(true)
                );

        // получаем дерево для v15: пустой элемент должен отсутствовать, должна возвращаться также цепочка
        // идентификаторов, однозначно определяющая выбираемое значение
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateAttr, V15)
                .assertValues(
                        tree(
                                aggregatedTreeElement(ou).leaf(false).selectable(false),
                                aggregatedTreeElement(ou2).leaf(false).selectable(false)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(ous, 18))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm)
                .setParent(ou);

        // получаемое дерево для дочерних элементов должно быть идентичны
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse3 =
                    DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, version);
            pvResponse3.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse3, aggregateAttr, version)
                    .assertValues(
                            aggregatedTreeElement(ou, employee),
                            aggregatedTreeElement(ou, employee2)
                    );
        }

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(aggregateAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(aggregateAttr)
                .hasEditPresentationSelectType(TREE);

        ValidatableResponse formResponse3 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse3, false)
                .assertForAttribute(aggregateAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута типа Агрегирующий с представлением "Поле выбора с папками".
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулись дерево объектов, на родительском уровне состоящее из пустого элемента,
     * папка ouFolder, отделов ou, ou2 и первых 16 отделов из ous, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из папки ouFolder,
     * отделов ou, ou2 и первых 17 отделов из ous, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = ouFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из отдела ou</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = ou,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из сотрудников employee и
     * employee2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = ou2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из сотрудника employee3</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут aggregateFoldersAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testAggregateWithFolders()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm);
        // получаем дерево для старой версии: должен вернуться пустой элемент
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateFoldersAttr)
                .assertValues(
                        tree(
                                emptyTreeElement(),
                                folderElement(ouFolder),
                                treeElement(ou).leaf(false).selectable(false),
                                treeElement(ou2).leaf(false).selectable(false)
                        ).with(
                                treeElements(Arrays.copyOf(ous, 16))
                        ).hasMore(true)
                );

        // получаем дерево для v15: пустой элемент должен отсутствовать,
        // также должна возвращаться цепочка идентификаторов, однозначно определяющая выбираемое значение
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateFoldersAttr, V15)
                .assertValues(
                        tree(
                                aggregatedFolderElement(ouFolder),
                                aggregatedTreeElement(ou).leaf(false).selectable(false),
                                aggregatedTreeElement(ou2).leaf(false).selectable(false)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(ous, 17))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setParent(ouFolder);
        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setParent(ou);
        MobilePossibleValuesParams pvParams4 = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setParent(ou2);

        // получаемые деревья для дочерних элементов должны быть идентичны
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, version);

            pvResponse4.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse4, aggregateFoldersAttr, version)
                    .assertValues(
                            aggregatedTreeElement(ou).leaf(false).selectable(false)
                    );

            ValidatableResponse pvResponse5 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, version);

            pvResponse5.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse5, aggregateFoldersAttr, version)
                    .assertValues(
                            aggregatedTreeElement(ou, employee),
                            aggregatedTreeElement(ou, employee2)
                    );

            ValidatableResponse pvResponse6 = DSLMobilePossibleValues.getPossibleValues(pvParams4, licAuth, version);

            pvResponse6.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse6, aggregateFoldersAttr, version)
                    .assertValues(
                            aggregatedTreeElement(ou2, employee3)
                    );
        }

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(aggregateFoldersAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Агрегирующий.
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = aggregateAttr</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>"Атрибут (агрегирующий)" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из: пустой элемент, сотрудник employee2, вложенный в
     * не доступный для выбора отдел ou</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее только из сотрудника employee2, вложенного в не
     * доступный для выбора отдел ou</li>
     * </ol>
     */
    @Test
    public void testAggregateWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(employee2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateAttr)
                .assertValues(
                        treeSearch(
                                emptyTreeElement(),
                                treeElement(ou).selectable(false).children(
                                        treeElement(employee2)
                                )
                        ).foundAmount(1)
                );

        Map<String, Object> objectTemplate2 = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateAttr, null)
                .build();
        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate2)
                .setForm(addForm)
                .setSearchString(employee2.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedTreeElement(ou).selectable(false).children(
                                        aggregatedTreeElement(ou, employee2)
                                )
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Агрегирующий с представлением "Поле выбора с папками".
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из: пустой элемент, сотрудник employee2, вложенный в
     * не доступный для выбора отдел ou</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево, первым элементом которого является не доступная для выбора папка
     * ouFolder, в которую вложен не доступный для выбора отдел ou, в которую вложен доступный для выбора сотрудник
     * employee2, а вторым элементом - повторяется отдельно отдел ou и сотрудник employee2</li>
     * </ol>
     */
    @Test
    public void testAggregateWithFoldersWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(employee2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateFoldersAttr)
                .assertValues(
                        treeSearch(
                                emptyTreeElement(),
                                treeElement(ou).selectable(false).children(
                                        treeElement(employee2)
                                )
                        ).foundAmount(1)
                );

        Map<String, Object> objectTemplate2 = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate2)
                .setForm(addForm)
                .setSearchString(employee2.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateFoldersAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedFolderElement(ouFolder).selectable(false).children(
                                        aggregatedTreeElement(ou).selectable(false).children(
                                                aggregatedTreeElement(ou, employee2)
                                        )
                                ),
                                aggregatedTreeElement(ou).selectable(false).children(
                                        aggregatedTreeElement(ou, employee2)
                                )
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа Агрегирующий с
     * представлением "Поле выбора". Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из последних 12
     * отделов ous</li>
     * </ol>
     */
    @Test
    public void testAggregateWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateAttr, version)
                    .assertValues(
                            aggregatedTreeElements(Arrays.copyOfRange(ous, 8, 20))
                    );
        }
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа Агрегирующий с
     * представлением "Поле выбора с папками". Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>"Папки" = 1,</li>
     *         <li>"Отделы" = 9,</li>
     *     </ul></li>
     *     <li>"Объект":<ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из последних 13
     * отделов ous</li>
     * </ol>
     */
    @Test
    public void testAggregateWithFoldersWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateFoldersAttr, objectTemplate)
                .setForm(addForm)
                .addFolderPosition(1)
                .addPosition(DAOOuCase.createClass(), 9);
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateFoldersAttr, version)
                    .assertValues(
                            aggregatedTreeElements(Arrays.copyOfRange(ous, 7, 20))
                    );
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа Агрегирующий. Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateAttr или aggregateFoldersAttr</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>"Атрибут (агрегирующий)" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testAggregateWhenPerformsSearchAndOffsetMoreThenZero()
    {
        for (Attribute attribute : List.of(aggregateAttr, aggregateFoldersAttr))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(userCase)
                    .setAttribute(attribute, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(attribute, objectTemplate)
                    .setForm(addForm)
                    .setOffset(1)
                    .setSearchString(employee2.getTitle());
            for (MobileVersion version : List.of(V13_1, V15))
            {
                ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

                pvResponse.statusCode(HttpStatus.SC_OK);
                DSLMobilePossibleValues.assertForAttribute(pvResponse, attribute, version).assertValues(treeSearch());
            }
        }
    }
}
