package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.cases.operator.embeddedapplication.JsApiDescribeListDescriptorTest.DESCRIBE_FILTRATION;
import static ru.naumen.selenium.cases.operator.embeddedapplication.JsApiDescribeListDescriptorTest.LIST_DESCRIPTION_AS_JSON;
import static ru.naumen.selenium.casesutil.model.content.advlist.ListFiltrationToTextConverter.getDescription;

import java.io.File;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.Interval;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BackTimerType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.ExceededStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.TimerStatus;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на метод jsApi.listdata.describeFiltration(descriptor) с разными типами ограничений и атрибутов
 *
 * <AUTHOR>
 * @since 14.10.2021
 */
class JsApiDescribeListDescriptor2Test extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userClass, userCase;
    private static Attribute timeZoneAttr;
    private static Bo userBo;
    private static ContentForm objectListContent;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать пользовательский класс userClass и подтип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать контент objectListContent типа "Список объектов" с представлением "Сложный список" и пустой
     * группой атрибутов</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWithWF();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        timeZoneAttr = DAOAttribute.createCatalogItem(userCase.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), SharedFixture.timeZone());

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase);
        DSLGroupAttr.add(attrGroup);

        objectListContent = DAOContentCard.createObjectAdvList(userCase.getFqn(), attrGroup, userClass, userCase);
        DSLContent.add(objectListContent);
    }

    /**
     * <ol>
     * <b>Очистка</b>
     * <li>Очистить фильтрацию из контента objectListContent</li>
     * </ol>
     */
    @AfterEach
    public void cleanUp()
    {
        objectListContent.setDefaultListFilter(null);
        DSLContent.edit(objectListContent);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутом "Статус"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут stateAttr "содержит" Зарегистрирован</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[Статус: <span>Зарегистрирован (%название userClass%)</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут stateAttr "содержит любое из значений"
     * Закрыт</li>
     * <li>Проверить, что описание полученное в ВП содержит "[Статус: <span>Закрыт (%название userClass%)</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут stateAttr "название содержит" Закрыт</li>
     * <li>Проверить, что описание полученное в ВП содержит "[Статус: <span>Закрыт</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForStateType()
    {
        // Подготовка
        Attribute stateAttr = SysAttribute.state(userClass);
        BoStatus registeredStatus = DAOBoStatus.createRegistered(userClass);
        BoStatus closedStatus = DAOBoStatus.createClosed(userClass);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(stateAttr, FilterCondition.CONTAINS, false,
                userClass.getFqn() + ":" + registeredStatus.getCode());
        String containsDescription = getDescription(stateAttr, FilterCondition.CONTAINS, registeredStatus);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr containsAnyCondition = new FilterBlockOr(stateAttr, FilterCondition.CONTAINS_IN_SET, true,
                userClass.getFqn() + ":" + closedStatus.getCode());
        String containsAnyDescription = getDescription(stateAttr, FilterCondition.CONTAINS_IN_SET, closedStatus);
        assertDescription(containsAnyCondition, containsAnyDescription);

        FilterBlockOr titleContainsCondition = new FilterBlockOr(stateAttr, FilterCondition.TITLE_CONTAINS, false,
                closedStatus.getTitle());
        String titleContainsDescription = getDescription(stateAttr, FilterCondition.TITLE_CONTAINS, closedStatus);
        assertDescription(titleContainsCondition, titleContainsDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Счётчик времени"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать атрибут timerAttr типа Счетчик времени в типе userCase, учитывающий время проведённое
     * в статусе Зарегистрирован</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут timerAttr "статус содержит"
     * Активен, Ожидает начала, Приостановлен</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%timerAttr%: <span>Активен, Ожидает начала, Приостановлен</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут timerAttr "статус не содержит"
     * Остановлен</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%timerAttr%: не <span>Остановлен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForTimerType()
    {
        // Подготовка
        BoStatus registeredStatus = DAOBoStatus.createRegistered(userCase);
        TimerDefinition counter = DAOTimerDefinition.createAstroTimerByStatus(userCase.getFqn(),
                timeZoneAttr.getCode(), registeredStatus);
        DSLTimerDefinition.add(counter);

        Attribute timerAttr = DAOAttribute.createTimer(userCase.getFqn(), counter, TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(timerAttr, FilterCondition.TIMER_STATUS_CONTAINS,
                true, TimerStatus.ACTIVE.getCode(), TimerStatus.NOT_STARTED.getCode(), TimerStatus.PAUSED.getCode());
        String containsDescription = getDescription(timerAttr, FilterCondition.TIMER_STATUS_CONTAINS,
                TimerStatus.ACTIVE, TimerStatus.NOT_STARTED, TimerStatus.PAUSED);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr notContainsCondition = new FilterBlockOr(timerAttr, FilterCondition.TIMER_STATUS_NOT_CONTAINS,
                true, TimerStatus.STOPPED.getCode());
        String notContainsDescription = getDescription(timerAttr, FilterCondition.TIMER_STATUS_NOT_CONTAINS,
                TimerStatus.STOPPED);
        assertDescription(notContainsCondition, notContainsDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor)
     * с атрибутами типа "Счётчик времени (обратный)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать атрибут backTimerAttr типа Счетчик времени (обратный), учитывающий время проведённое
     * в статусе Зарегистрирован</li>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут backTimerAttr "статус содержит"
     * Активен, Ожидает начала, Приостановлен</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%backTimerAttr%: <span>Активен, Ожидает начала, Приостановлен</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут backTimerAttr "статус не содержит"
     * Кончился запас времени, Остановлен</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%backTimerAttr%: не <span>Кончился запас времени, Остановлен</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут backTimerAttr "просроченность содержит" не
     * просрочен</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%backTimerAttr%: <span>не просрочен</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут backTimerAttr "просроченность содержит"
     * просрочен</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%backTimerAttr%: <span>просрочен</span>]"</li>
     * <li>Настроить в контенте objectListContentContent фильтрацию: атрибут backTimerAttr "время окончания с ... по"
     * с (текущей даты - 2 дня) по (текущую дату + 2 дня)</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%backTimerAttr%: с <span>%dd.MM.yyyy HH:mm - 2 дня%</span> по <span>%dd.MM.yyyy HH:mm + 2 дня%</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForBackTimerType()
    {
        Attribute serviceTimeAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.SERVICETIME), SharedFixture.serviceTime());
        Attribute timeIntervalAttr = DAOAttribute.createTimeInterval(userClass.getFqn());
        DSLAttribute.add(serviceTimeAttr, timeIntervalAttr);

        BoStatus registeredStatus = DAOBoStatus.createRegistered(userCase);
        TimerDefinition backCounter = DAOTimerDefinition.createFloatTimerByStatus(userCase.getFqn(),
                timeZoneAttr.getCode(), serviceTimeAttr.getCode(), timeIntervalAttr.getCode(), registeredStatus);

        Attribute backTimerAttr = DAOAttribute.createBackTimer(userCase.getFqn(), backCounter,
                BackTimerType.BACKTIMER_STATUS_VIEW);
        DSLAttribute.add(backTimerAttr);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(backTimerAttr, FilterCondition.TIMER_STATUS_CONTAINS,
                true, TimerStatus.ACTIVE.getCode(), TimerStatus.NOT_STARTED.getCode(), TimerStatus.PAUSED.getCode());
        String containsDescription = getDescription(backTimerAttr, FilterCondition.TIMER_STATUS_CONTAINS,
                TimerStatus.ACTIVE, TimerStatus.NOT_STARTED, TimerStatus.PAUSED);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr notContainsCondition = new FilterBlockOr(backTimerAttr,
                FilterCondition.TIMER_STATUS_NOT_CONTAINS, true, TimerStatus.EXCEED.getCode(),
                TimerStatus.STOPPED.getCode());
        String notContainsDescription = getDescription(backTimerAttr, FilterCondition.TIMER_STATUS_NOT_CONTAINS,
                TimerStatus.EXCEED, TimerStatus.STOPPED);
        assertDescription(notContainsCondition, notContainsDescription);

        FilterBlockOr deadLineContainsNoExceedCondition = new FilterBlockOr(backTimerAttr,
                FilterCondition.BACK_TIMER_DEADLINE_CONTAINS, false, ExceededStatus.NO_EXCEED.getCode());
        String deadLineContainsNoExceedDescription = getDescription(backTimerAttr,
                FilterCondition.BACK_TIMER_DEADLINE_CONTAINS, ExceededStatus.NO_EXCEED);
        assertDescription(deadLineContainsNoExceedCondition, deadLineContainsNoExceedDescription);

        FilterBlockOr deadLineContainsExceedCondition = new FilterBlockOr(backTimerAttr,
                FilterCondition.BACK_TIMER_DEADLINE_CONTAINS, false, ExceededStatus.EXCEED.getCode());
        String deadLineContainsExceedDescription = getDescription(backTimerAttr,
                FilterCondition.BACK_TIMER_DEADLINE_CONTAINS, ExceededStatus.EXCEED);
        assertDescription(deadLineContainsExceedCondition, deadLineContainsExceedDescription);

        long currentTime = System.currentTimeMillis();
        long daysTime = 2 * Interval.DAY.getFactor();
        FilterBlockOr deadLineFromToCondition = new FilterBlockOr(backTimerAttr,
                FilterCondition.BACK_TIMER_DEADLINE_FROM_TO, true, currentTime - daysTime, currentTime + daysTime);
        String deadLineFromToDescription = getDescription(backTimerAttr, FilterCondition.BACK_TIMER_DEADLINE_FROM_TO,
                currentTime - daysTime, currentTime + daysTime);
        assertDescription(deadLineFromToCondition, deadLineFromToDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с ограничением на несколько атрибутов
     * сразу, которые сгруппированы через "или"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе userCase атрибут objectAttr типа "Ссылка на БО" на тип userCase</li>
     * <li>Создать в типе userCase атрибут stringAttr типа "Строка"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContent фильтрацию: атрибут objectAttr "содержит" employee ИЛИ
     * атрибут stringAttr "не пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%objectAttr%: <span>%ФИО employee%</span> или %stringAttr%: <span>заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorWithSeveralOrBlock()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(objectAttr, stringAttr);

        // Действия и проверки
        FilterBlockOr objectAttrCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS, false,
                userBo.getUuid());
        FilterBlockOr stringAttrCondition = new FilterBlockOr(stringAttr, FilterCondition.NOT_EMPTY, false);

        String objectAttrDescription = getDescription(objectAttr, FilterCondition.CONTAINS, userBo);
        String stringAttrDescription = getDescription(stringAttr, FilterCondition.NOT_EMPTY);

        assertDescription(new FilterBlockAnd(objectAttrCondition, stringAttrCondition),
                objectAttrDescription, stringAttrDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с ограничением на несколько
     * атрибутов, которые сгруппированы через "и"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе userCase атрибут objectAttr типа "Ссылка на БО" на тип userCase</li>
     * <li>Создать в типе userCase атрибут stringAttr типа "Строка"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContent фильтрацию: атрибут objectAttr "содержит" employee И
     * атрибут stringAttr "не пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%objectAttr%: <span>%ФИО employee%</span>] и [%stringAttr%: <span>заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorWithSeveralAndBlock()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(objectAttr, stringAttr);

        // Действия и проверки
        FilterBlockOr objectAttrCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS, false,
                userBo.getUuid());
        FilterBlockOr stringAttrCondition = new FilterBlockOr(stringAttr, FilterCondition.NOT_EMPTY, false);

        String objectAttrDescription = getDescription(objectAttr, FilterCondition.CONTAINS, userBo);
        String stringAttrDescription = getDescription(stringAttr, FilterCondition.NOT_EMPTY);

        ListFilter condition = new ListFilter(new FilterBlockAnd(objectAttrCondition),
                new FilterBlockAnd(stringAttrCondition));
        assertDescription(condition, objectAttrDescription, stringAttrDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с ограничением на несколько атрибутов
     * сразу, когда один из атрибутов выключен меткой
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать выключенные метки tag</li>
     * <li>Создать в типе userCase атрибут objectAttr типа "Ссылка на БО" на тип userCase, выключенный
     * меткой tag</li>
     * <li>Создать в типе userCase атрибут stringAttr типа "Строка"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectListContent фильтрацию: атрибут objectAttr "содержит" employee ИЛИ
     * атрибут stringAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[<span class="disable">%objectAttr%: <span>%ФИО employee%</span></span> или %stringAttr%:
     * <span>заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorWithDisabledAttribute()
    {
        // Подготовка
        Tag tag = DAOTag.createTag(false);
        DSLTag.add(tag);

        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        objectAttr.setTags(tag);
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(objectAttr, stringAttr);

        // Действия и проверки
        FilterBlockOr objectAttrCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS, false,
                userBo.getUuid());
        FilterBlockOr stringAttrCondition = new FilterBlockOr(stringAttr, FilterCondition.NOT_EMPTY, false);

        String objectAttrDescription = getDescription(objectAttr, true, FilterCondition.CONTAINS, userBo);
        String stringAttrDescription = getDescription(stringAttr, FilterCondition.NOT_EMPTY);

        assertDescription(new FilterBlockAnd(objectAttrCondition, stringAttrCondition),
                objectAttrDescription, stringAttrDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с пустым ограничением
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Проверить, что при отсутствии настроек фильтрации описание полученное в ВП содержит "не указано"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorWithEmptyCondition()
    {
        // Действия и проверки
        assertDescription(new ListFilter(), "не указано");
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     * @see #assertDescription(ListFilter, String...)
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescription ожидаемое описание
     */
    private void assertDescription(FilterBlockOr condition, String expectedDescription)
    {
        assertDescription(new FilterBlockAnd(condition), expectedDescription);
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     * @see #assertDescription(ListFilter, String...)
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescriptions ожидаемые описания атрибутов
     */
    private void assertDescription(FilterBlockAnd condition, String... expectedDescriptions)
    {
        assertDescription(new ListFilter(condition), String.join(" или ", expectedDescriptions));
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     *
     * <ol>
     * <b>Действия</b>
     * <li>Заполнить условие фильтрации в контенте objectListContent</li>
     * <li>Выполнить скрипт, который извлекает списочный дескриптор из objectListContent
     * и конвертирует его в JSON descriptorAsJson:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     def descriptor = api.listdata.createListDescriptor('$userCase', '$objectListContent')
     *     api.listdata.listDescriptorAsJson(descriptor)
     *     -------------------------------------------------------------------------------
     *     где employeeCase - FQN типа employeeCase,
     *         objectListContent - код контента objectListContent
     * </pre>
     * <li>Определить js-код jsContent, который преобразует списочный дескриптор конвертированный в JSON и преобразует
     * его в HTML-описание с помощью jsApi.utils.describeListDescriptor:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.utils.describeListDescriptor($descriptorAsJson%)
     *         .then(html => document.getElementById('test_div').innerText = html)
     *     -------------------------------------------------------------------------------
     *     где $descriptorAsJson - JSON-представление списочного дескриптора контента objectListContent
     * </pre>
     * </li>
     * <li>Создать встроенное приложения с jsContent в качестве js-кода</li>
     * <li>Создать контент content для встроенного приложения на карточке типа userCase</li>
     * <b>Проверка</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что описание полученное в ВП содержит значения из expectedDescriptions</li>
     * </ol>
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescriptions ожидаемые описания атрибутов
     */
    private void assertDescription(ListFilter condition, String... expectedDescriptions)
    {
        objectListContent.setDefaultListFilter(condition);
        DSLContent.edit(objectListContent);

        String scriptBody = String.format(LIST_DESCRIPTION_AS_JSON, userCase.getFqn(), objectListContent.getCode());
        String descriptorAsJson = ScriptRunner.executeScript(scriptBody);

        String jsContent = String.format(DESCRIBE_FILTRATION, descriptorAsJson, GUIEmbeddedApplication.TEST_DIV_ID);
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(content);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        String actualDescription = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content);
        String expectedDescription = Arrays.stream(expectedDescriptions)
                .map(description -> "[" + description + "]")
                .collect(Collectors.joining(" и "));
        Assertions.assertEquals(expectedDescription, actualDescription);
    }
}
