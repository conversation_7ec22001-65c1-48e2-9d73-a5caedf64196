package ru.naumen.selenium.cases.admin.permission;

import static ru.naumen.selenium.casesutil.admin.GUIAdmin.EDIT_SUPER_USER_BUTTON;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.DELETE;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.admin.GUIDropDownSettings;
import ru.naumen.selenium.casesutil.admin.GUIExportUtils;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.adminprofiles.GUIAdminProfile;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.schedulertask.GUISchedulerTaskList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование прав на действие в интерфейсе администратора по маркерам доступа в профиле администрирования.
 *
 * <AUTHOR>
 * @since 20.12.2025
 */
public class AdminProfileAccessMarker5Test extends AbstractTestCase
{
    private SuperUser superUser;
    private AdminProfile adminProfile;
    private AdminProfileAccessMarkerMatrix accessMarkerMatrix;
    private MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareAllFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
    }

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Добавить в систему профиль администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser и назначить ему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Архивирование метакласса". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass2 и поместить его в архив</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Нажать кнопку "Поместить в архив", затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку класса userClass2</li>
     * <li>Нажать кнопку "Восстановить из архива", затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что класс userClass - неархивный, а класс userClass2 - архивный</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Архивирование метакласса"</li>
     * <li>Нажать кнопку "Восстановить из архива", затем нажать кнопку "Да" на форме подтверждения
     * и проверить, что форма исчезла</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Нажать кнопку "Поместить в архив", затем нажать кнопку "Да" на форме подтверждения
     * и проверить, что форма исчезла</li>
     * <li>Проверить, что класс userClass - архивный, а класс userClass2 - неархивный</li>
     * </ol>
     */
    @Test
    public void testAllPermissionArchivingMetaclassAccessMarker()
    {
        // Подготовка
        MetaClass userClass2 = DAOUserClass.create();
        DSLMetaClass.add(userClass2);
        DSLMetaClass.archive(userClass2);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        GUIMetaClass.clickArchive();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIMetaClass.goToCard(userClass2);
        GUIMetaClass.clickRestore();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        DSLMetaClass.assertNotArchived(userClass);
        DSLMetaClass.assertArchived(userClass2);

        accessMarkerMatrix.addAccessMarkerPermission(ARCHIVING_METACLASS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIMetaClass.clickRestore();
        GUIForm.confirmByYes();

        GUIMetaClass.goToCard(userClass);
        GUIMetaClass.clickArchive();
        GUIForm.confirmByYes();

        DSLMetaClass.assertArchived(userClass);
        DSLMetaClass.assertNotArchived(userClass2);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных
     * форм". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Метаинформация" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Загрузить" в строке "Метаинформация" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Шаблоны отчетов и печатных форм" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Метаинформация" в блоке "Выгрузка/загрузка"</li>
     * <li>Нажать кнопку "Загрузить" в строке "Метаинформация" в блоке "Выгрузка/загрузка"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Шаблоны отчетов и печатных форм" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "Шаблоны отчетов не могут быть выгружены.
     * В системе отсутствуют шаблоны отчетов."</li>
     * </ol>
     */
    @Test
    public void testAllPermissionImportExportMetainfoAndReportTemplatesAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIAdmin.clickExportMetainfo();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdmin.clickUploadMetainfoButton();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdmin.clickExport(GUIAdmin.X_REPORT_TEMPLATES_UPLOAD_BTN);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.clickExportMetainfo();
        GUIForm.cancelForm();
        GUIAdmin.clickUploadMetainfoButton();
        GUIForm.cancelForm();
        GUIAdmin.clickExport(GUIAdmin.X_REPORT_TEMPLATES_UPLOAD_BTN);
        GUIError.assertDialogError(ErrorMessages.REPORT_TEMPLATES_CANT_BE_UPLOADED);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Логи приложения". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Проверить, что отсутствуют вкладки "Управление системой", "Логи приложения", "Управление логом событий"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Текущий лог приложения" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Текущий набор коротких логов приложения" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Логи приложения" и удалить все права по маркеру доступа
     * "Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствуют вкладки "Управление системой", "Логи приложения",
     * "Управление логом событий"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Текущий лог приложения" в блоке "Выгрузка/загрузка"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionApplicationLogsAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAbsenceSystemControlTab();
        GUIAdmin.assertAbsenceApplicationLogTab();
        GUIAdmin.assertAbsenceEventCleanerTab();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.clickExportWithErrors(GUIAdmin.CURRENT_LOG, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.clickExportWithErrors(GUIAdmin.CURRENT_PERIOD_LOG, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.removeAccessMarkerPermissions(IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES);
        accessMarkerMatrix.addAccessMarkerPermission(APPLICATION_LOGS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertPresentSystemControlTab();
        GUIAdmin.assertPresentApplicationLogTab();
        GUIAdmin.assertPresentEventCleanerTab();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        GUIAdmin.exportAsZip(GUIAdmin.CURRENT_LOG, DSLApplication.isCluster() ? "cluster.log" : "sdng.log");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Лог действий технолога". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Лог технолога"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Лог действий технолога"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует вкладка "Лог технолога"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionAdminLogsAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAbsenceAdminLogTab();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(ADMIN_LOGS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertPresentAdminLogTab();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Выгрузка информации о системе". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Выгрузка информации о системе"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Информация о системе" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Выгрузка информации о системе"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Информация о системе" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что сообщение об ошибке отсутствует</li>
     * </ol>
     */
    @Test
    public void testAllPermissionExportSystemInformationAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(EXPORT_SYSTEM_INFORMATION, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdministration();
        GUIAdmin.clickExportWithErrors(Any.EXPORT_SYSTEM_INFO, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(EXPORT_SYSTEM_INFORMATION, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIExportUtils.export(Any.EXPORT_SYSTEM_INFO, "systeminfo", ".xml");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Выгрузка статистики". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Выгрузка статистики"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Статистика" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Выгрузка статистики"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Статистика" в блоке "Выгрузка/загрузка"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionExportStatisticsAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(EXPORT_STATISTICS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIAdmin.clickExportWithErrors(Any.EXPORT_STATISTICS, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(EXPORT_STATISTICS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIExportUtils.export(Any.EXPORT_STATISTICS, "exportstatistics", ".xml");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Загрузка-выгрузка лицензионных файлов". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Загрузка-выгрузка лицензионных файлов"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Лицензии" в блоке "Выгрузка/загрузка"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Выберите файл" в строке "Лицензии" в блоке "Выгрузка/загрузка"</li>
     * <li>Загрузить файл с лицензией {@link DSLAdmin#DEFAULT_MINIMUM_LICENSE}</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Загрузка-выгрузка лицензионных файлов"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выгрузить" в строке "Лицензии" в блоке "Выгрузка/загрузка"</li>
     * <li>Нажать кнопку "Выберите файл" в строке "Лицензии" в блоке "Выгрузка/загрузка"</li>
     * <li>Загрузить файл с лицензией {@link DSLAdmin#DEFAULT_MINIMUM_LICENSE}</li>
     * </ol>
     */
    @Test
    public void testAllPermissionImportExportLicenseFilesAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_EXPORT_LICENSE_FILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIAdmin.clickExportWithErrors(Any.EXPORT_LICENSE, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.uploadLicenseFailed(DSLAdmin.DEFAULT_MINIMUM_LICENSE, ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_EXPORT_LICENSE_FILES, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIExportUtils.export(Any.EXPORT_LICENSE, "license", ".xml");
        GUIAdmin.uploadLicense();
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Загрузка сертификатов". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Загрузка сертификатов"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Нажать кнопку "Выберите файл" в строке "Сертификаты" в блоке "Выгрузка/загрузка"</li>
     * <li>Загрузить файл {@link DSLAdmin#CERTIFICATE_NAME}</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Загрузка сертификатов"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выберите файл" в строке "Сертификаты" в блоке "Выгрузка/загрузка"</li>
     * <li>Загрузить файл {@link DSLAdmin#CERTIFICATE_NAME}</li>
     * <li>Проверить, что появилось диалоговое окно с сообщением "Сертификат успешно загружен"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionImportCertificatesAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_CERTIFICATES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdministration();
        GUIAdmin.clickUploadWithErrors(GUIAdmin.X_CERTIFICATE_UPLOAD, DSLAdmin.CERTIFICATE_NAME,
                ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(IMPORT_CERTIFICATES, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.uploadCertificateWithAssertDialog(DSLAdmin.CERTIFICATE_NAME, "Сертификат успешно загружен.");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Список активных пользователей". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок с контентом "Активные пользователи"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Список активных пользователей"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует блок с контентом "Активные пользователи"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionActiveUsersAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAbsenceActiveUserBlock();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(ACTIVE_USERS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertPresentActiveUserBlock();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Блокировка входа на время технических работ". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует контент "Блокировка входа на время технических работ"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Блокировка входа на время технических работ"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует контент "Блокировка входа на время технических работ"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionMaintenanceAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAbsenceMaintainsBlock();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(MAINTENANCE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertPresentMaintainsBlock();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Выпадающие списки выбора". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок с контентом "Выпадающие списки выбора"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Выпадающие списки выбора"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует блок с контентом "Выпадающие списки выбора"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionDropdownSelectionListsAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIDropDownSettings.assertAbsenceDropDownSettingsBlock();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(DROPDOWN_SELECTION_LISTS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIDropDownSettings.assertPresentDropDownSettingsBlock();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Локализация". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Локализация системы"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Локализация системы"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Локализация"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Локализация системы"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionLocalizationAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToLocalizationSettings();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("systemLocalizationSettings:");

        accessMarkerMatrix.addAccessMarkerPermission(LOCALIZATION, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("systemLocalizationSettings:");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Консоль". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Консоль"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок с контентом "Лог"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Консоль"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Консоль"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что отсутствует блок с контентом "Лог"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы" и раздел "Консоль"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionConsoleAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToConsole();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIConsole.assertAbsenceLogContainerBlock();

        GUIAdminNavigationTree.assertItemAbsent("console:");
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(CONSOLE, ALL);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);
        tester.refresh();

        GUIConsole.assertPresentLogContainerBlock();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("console:");
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Выполнение скриптов из консоли". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ по маркеру доступа "Консоль"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Консоль"</li>
     * <li>Проверить, что отсутствуют блоки "Выполнение скрипта из файла" и "Выполнение скрипта из поля ввода"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Выполнение скриптов из консоли"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствуют блоки "Выполнение скрипта из файла" и "Выполнение скрипта из поля ввода"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionExecuteConsoleScriptsAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(CONSOLE, ALL);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToConsole();
        GUIConsole.assertAbsenceExecuteScriptFromFIleBlock();
        GUIConsole.assertAbsenceExecuteScriptFromInputFieldBlock();

        accessMarkerMatrix.addAccessMarkerPermission(EXECUTE_CONSOLE_SCRIPTS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIConsole.assertPresentExecuteScriptFromFIleBlock();
        GUIConsole.assertPresentExecuteScriptFromInputFieldBlock();
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Выполнение задач планировщика". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo с текстом:
     * <pre>
     *     return
     * </pre></li>
     * <li>Создать задачу планировщика schedulerTask типа "Скрипт" со скриптом scriptInfo</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Планировщик"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Планировщик задач"</li>
     * <li>Нажать кнопку "Выполнить задачу" у задачи schedulerTask в списке задач планировщика,
     * затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Выполнение задач планировщика"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Выполнить задачу" у задачи schedulerTask в списке задач планировщика,
     * затем нажать кнопку "Да" на форме подтверждения и проверить, что форма исчезла</li>
     * </ol>
     */
    @Test
    public void testAllPermissionRunSchedulerTaskAccessMarker()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(scriptInfo);

        SchedulerTaskScript schedulerTask = DAOSchedulerTask.createScriptRule(scriptInfo);
        DSLSchedulerTask.addTask(schedulerTask);

        accessMarkerMatrix.addAccessMarkerPermission(SCHEDULER, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScheduler();
        GUISchedulerTaskList.clickRunTask(schedulerTask);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(RUN_SCHEDULER_TASK, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUISchedulerTaskList.clickRunTask(schedulerTask);
        GUIForm.confirmByYes();
    }

    /**
     * Тестирование действия "Создание" для маркера доступа "Профили администрирования"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выдать профилю adminProfile право на просмотр для маркера "Профили Администрирования"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что в разделе отсутствует кнопка "Добавить профиль"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в разделе присутствует кнопка "Добавить профиль"</li>
     * <li>Нажать на кнопку "Добавить профиль", заполнить форму значениями profile</li>
     * <li>Сохранить форму, проверить, что ошибок не возникло</li>
     * <li>Перейти на карточку profile, проверить что все поля заполнены правильно</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Нажать на кнопку "Добавить профиль", заполнить форму значениями profile</li>
     * <li>В матрице маркеров доступа accessMarkerMatrix удалить право на создание
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Сохранить форму, проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной
     * операции. Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionAdminProfilesAccessMarker()
    {
        // Подготовка
        AdminProfile profile = DAOAdminProfile.createAdminProfile();
        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().toolPanel().asserts().buttonsAbsence(GUIAdvListXpath.BTN_ADD);

        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, CREATE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        tester.refresh();
        GUIAdminProfile.advlist().toolPanel().asserts().buttonsPresence(GUIAdvListXpath.BTN_ADD);
        GUIAdminProfile.clickAddAdminProfile();
        GUIAdminProfile.fillAddForm(profile);
        GUIForm.applyForm();
        GUIAdminProfile.goToAdminProfileCard(profile);
        GUIAdminProfile.assertThatCard(profile);
        profile.setExists(true);

        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.clickAddAdminProfile();
        GUIAdminProfile.fillAddForm(profile);

        accessMarkerMatrix.removeAccessMarkerPermission(ADMINISTRATION_PROFILES, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIForm.applyFormAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Редактирование" для маркера доступа "Профили администрирования".
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профиль администрирования profile</li>
     * <li>Выдать профилю adminProfile право на просмотр для маркера "Профили Администрирования"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что в списке с профилями администрирования отсутствует колонка действий "Редактированть"</li>
     * <li>Перейти на карточку профиля profile</li>
     * <li>Проверить, что на карточке профиля profile отсутствует кнопка "Редактировать"</li>
     * <li>Проверить, что на карточке профиля profile отсутствует кнопка "Выбрать все"</li>
     * <li>Проверить, что на карточке профиля profile отсутствует кнопка "Сохранить"</li>
     * <li>Проверить, что на карточке профиля profile отсутствует кнопка "Отменить изменения"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что на карточке профиля profile присутствует кнопка "Редактировать"</li>
     * <li>Проверить, что на карточке профиля profile присутствует кнопка "Выбрать все"</li>
     * <li>Проверить, что на карточке профиля profile присутствует кнопка "Сохранить"</li>
     * <li>Проверить, что на карточке профиля profile присутствует кнопка "Отменить изменения"</li>
     * <li>Кликнуть по чекбоксу в матрице маркеров доступа: "Планировщик" - "Просмотр"</li>
     * <li>Сохранить изменённую матрицу прав, проверить, что ошибок не возникло</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Сохранить форму, проверить, что ошибок не возникло</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что для профиля profile присутствует колонка действия "Редактировать"</li>
     * <li>Кликнуть на пиктограмму "Редактировать" для объекта profile</li>
     * <li>Заполнить поле название значением newTitle</li>
     * <li>Сохранить форму, проверить, что в списке с профилями администрирования присутствует профиль с название
     * newTitle</li>
     * <li>Перейти на карточку профиля profile</li>
     * <li>Открыть форму редактирования профиля profile</li>
     * <li>В матрице маркеров доступа accessMarkerMatrix удалить право на редактирование
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Сохранить форму, проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной
     * операции. Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testEditPermissionAdminProfilesAccessMarker()
    {
        // Подготовка
        AdminProfile profile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(profile);
        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().pictAbsence(profile, GUIAdminProfile.PICT_EDIT);
        GUIAdminProfile.goToAdminProfileCard(profile);
        GUITester.assertAbsent(GUIXpath.Div.EDIT, "На панели присутствует кнопка \"Редактировать\"");
        GUITester.assertAbsent(Div.SELECT_ALL, "На панели присутствует кнопка \"Выбрать все\"");
        GUITester.assertAbsent(Div.SAVE, "На панели присутствует кнопка \"Сохранить\"");
        GUITester.assertAbsent(Div.ERASE, "На панели присутствует кнопка \"Отменить изменения\"");

        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, EDIT);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        tester.refresh();
        GUITester.assertPresent(GUIXpath.Div.EDIT, "На панели отсутствует кнопка \"Редактировать\"");
        GUITester.assertPresent(Div.SELECT_ALL, "На панели отсутствует кнопка \"Выбрать все\"");
        GUITester.assertPresent(Div.SAVE, "На панели отсутствует кнопка \"Сохранить\"");
        GUITester.assertPresent(Div.ERASE, "На панели отсутствует кнопка \"Отменить изменения\"");
        GUIAdminProfile.clickAccessMarkerMatrixDataCheckbox(SCHEDULER, VIEW);
        GUIAdminProfile.clickSaveAccessMarkerMatrix();
        GUIAdminProfile.clickEditFromCard();
        GUIForm.applyForm();

        String newTitle = "test";
        profile.setTitle(newTitle);
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().pictPresence(profile, GUIAdminProfile.PICT_EDIT);
        GUIAdminProfile.advlist().content().clickPict(profile, GUIAdminProfile.PICT_EDIT);
        GUIAdminProfile.setTitle(newTitle);
        GUIForm.applyForm();
        GUIAdminProfile.advlist().content().asserts().rowsPresenceByTitle(profile);

        GUIAdminProfile.goToAdminProfileCard(profile);
        GUIAdminProfile.clickEditFromCard();

        accessMarkerMatrix.removeAccessMarkerPermission(ADMINISTRATION_PROFILES, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIForm.applyFormAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Удалить" для маркера доступа "Профили администрирования".
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профили администрирования profile, profile2, profile3</li>
     * <li>Выдать профилю adminProfile право на просмотр для маркера "Профили Администрирования"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что в списке с профилями администрирования отсутствует колонка действий "Удалить"</li>
     * <li>Перейти на карточку профиля profile</li>
     * <li>Проверить, что на карточке профиля profile отсутствует кнопка "Удалить"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что на карточке профиля profile присутствует кнопка "Удалить"</li>
     * <li>Нажать на кнопку "Удалить"</li>
     * <li>Подтвердить удаление объекта, проверить, что профиль администрирования profile удалился без ошибок</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что для профиля profile присутствует колонка действия "Удалить"</li>
     * <li>Кликнуть на пиктограмму "Удалить" для объекта profile2</li>
     * li>Подтвердить удаление объекта, проверить, что профиль администрирования profile2 удалился без ошибок</li>
     * <li>Перейти на карточку профиля profile3</li>
     * <li>Открыть форму удаления профиля profile3</li>
     * <li>В матрице маркеров доступа accessMarkerMatrix удалить право на удаление
     * для маркера доступа "Профили администрирования" у профиля adminProfile</li>
     * <li>Сохранить форму, проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной
     * операции. Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionAdminProfilesAccessMarker()
    {
        // Подготовка
        AdminProfile profile = DAOAdminProfile.createAdminProfile();
        AdminProfile profile2 = DAOAdminProfile.createAdminProfile();
        AdminProfile profile3 = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(profile, profile2, profile3);
        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().pictAbsence(profile, GUIAdminProfile.PICT_DELETE);

        GUIAdminProfile.goToAdminProfileCard(profile);
        GUITester.assertAbsent(Div.DEL, "На панели присутствует кнопка \"Удалить\"");

        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, DELETE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        tester.refresh();
        GUITester.assertPresent(Div.DEL, "На панели отсутствует кнопка \"Удалить\"");
        GUIAdminProfile.clickDeleteFromCard();
        GUIForm.confirmByYes();
        GUIAdminProfile.advlist().content().asserts().rowsAbsence(profile);
        profile.setExists(false);

        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().pictPresence(profile2, GUIAdminProfile.PICT_DELETE);
        GUIAdminProfile.advlist().content().clickPict(profile2, GUIAdminProfile.PICT_DELETE);
        GUIForm.confirmByYes();
        GUIAdminProfile.advlist().content().asserts().rowsAbsence(profile2);
        profile2.setExists(false);

        GUIAdminProfile.goToAdminProfileCard(profile3);
        GUIAdminProfile.clickDeleteFromCard();

        accessMarkerMatrix.removeAccessMarkerPermission(ADMINISTRATION_PROFILES, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Доступ" для маркера доступа "Управление привязкой профилей администрирования"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профили администрирования profile и profile2, выдать им права на доступ в ИА</li>
     * <li>Выдать профилю adminProfile право на просмотр для маркера "Профили Администрирования" и доступ к списку
     * суперпользователей</li>
     * <li>Создать суперпользователя superUser1, выдать ему профиль администрирования profile2</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в интерфейс администратора (вкладка аминистрирование)</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что у суперпользователей superUser и superUser1 отсутствуют кнопки редактирования</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * для маркера "Управление привязкой профилей администрирования" у профиля adminProfile</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что у суперпользователя superUser отсутствует кнопка редактирования, он не может
     * отредактировать сам себя</li>
     * <li>Проверить, что у суперпользователя superUser1 присутствует кнопка редактирования</li>
     * <li>Открыть форму редактирования суперпользователя superUser1</li>
     * <li>Проверить, что поле "Профили аднимистрирования" заполнено значением profile2</li>
     * <li>Убрать profile2 с поля профили администрирования</li>
     * <li>Попытаться сохранить форму с пустым значением поля "Профили аднимистрирования"</li>
     * <li>Проверить, что появилось сообщение валидации "Должно быть выбрано хотя бы одно значение."</li>
     * <li>В выпадающем списке выбрать профиль администрирования profile</li>
     * <li>Сохранить форму, проверить, что ошибок не возникло</li>
     * <li>Открыть форму редактирования суперпользователя superUser1</li>
     * <li>Проверить, что поле "Профили аднимистрирования" заполнено значением profile</li>
     * </ol>
     */
    @Test
    public void testAccessPermissionAdminProfilesManagementAccessMarker()
    {
        // Подготовка
        AdminProfile profile = DAOAdminProfile.createAdminProfile();
        AdminProfileAccessMarkerMatrix accessMarkerMatrix1 = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix1.addAccessMarkerPermission(ADMINISTRATOR_INTERFACE, ALL);
        DSLAdminProfile.add(profile);
        DSLAdminProfile.setAccessMarkerMatrix(profile, accessMarkerMatrix1);

        AdminProfile profile2 = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(profile2);
        DSLAdminProfile.setAccessMarkerMatrix(profile2, accessMarkerMatrix1);

        accessMarkerMatrix.addAccessMarkerPermission(SUPER_USERS, ALL);
        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        SuperUser superUser1 = DAOSuperUser.create();
        superUser1.setAdminProfiles(profile2);
        DSLSuperUser.add(superUser1);

        String editSuperUserButtonXpath = EDIT_SUPER_USER_BUTTON.formatted(superUser.getUuid());
        String editSuperUser1ButtonXpath = EDIT_SUPER_USER_BUTTON.formatted(superUser1.getUuid());
        String buttonPresentError = "Кнопка редактирования суперпользователя присутствует в списке";
        String buttonAbsentError = "Кнопка редактирования суперпользователя отсутствует в списке";

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToAdministration();

        GUIAdmin.assertAbsenceButtonOnXpath(editSuperUserButtonXpath, buttonPresentError);
        GUIAdmin.assertAbsenceButtonOnXpath(editSuperUser1ButtonXpath, buttonPresentError);

        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES_MANAGEMENT, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertAbsenceButtonOnXpath(editSuperUserButtonXpath, buttonPresentError);
        GUIAdmin.assertPresentButtonOnXpath(editSuperUser1ButtonXpath, buttonAbsentError);

        GUIAdmin.openEditSuperUserForm(superUser1);
        GUISelect.assertDefaultItemPrecense(Any.ADMIN_PROFILES, profile2.getCode(), true);
        GUISelect.selectByTitle(Any.ADMIN_PROFILES, profile2.getTitle());
        GUIForm.clickApply();
        GUIValidation.assertValidation("adminProfiles", ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
        GUISelect.selectByTitle(Any.ADMIN_PROFILES, profile.getTitle());
        GUIForm.applyForm();

        GUIAdmin.openEditSuperUserForm(superUser1);
        GUISelect.assertDefaultItemPrecense(Any.ADMIN_PROFILES, profile.getCode(), true);
        GUIForm.cancelForm();
    }
}