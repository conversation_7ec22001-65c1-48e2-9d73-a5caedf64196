package ru.naumen.selenium.cases.script.security;

import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup.GroupUsers;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на методы работы с группами пользователей в api.security.<br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 *
 * <AUTHOR>
 * @since 16.07.2024
 */
public class SecurityApiGroupTest extends AbstractTestCase
{
    private static Bo ou, team, employee;

    /**
     * <b>Общая часть подготовки</b>
     * <ul>
     * <li>Создать отдел ou, команду team</li>
     * <li>Создать сотрудника employee в отделе ou</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team);
        employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);
    }

    /**
     * Тестирование метода api.security.getGroup: получение группы пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44550647
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создаем группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем скрипт:
     * <pre>
     *     ---------------------------------------------------------------------
     *     api.security.getGroup('$groupCode').title;
     *     ---------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в результате вернулось название группы group</li>
     * </ol>
     */
    @Test
    public void testGetSecurityGroups()
    {
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        //Выполнение действий и проверки
        String actual = ScriptRunner.executeScript("api.security.getGroup('%s').title", group.getCode());
        Assert.assertEquals("Полученная группа пользователей не совпала с ожидаемой.", group.getTitle(), actual);
    }

    /**
     * Тестирование метода api.security.getAllEmployees(Group): возвращение списка сотрудников, входящих в указанную
     * группу пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44550647
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать сотрудников employee2, employee3 в отделе ou</li>
     * <li>Создать отдел ou2 и сотрудника employee4 в нём</li>
     * <li>Добавить employee2 в team</li>
     * <li>Создаем группу пользователей group и добавить в неё employee, ou2, team</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.getAllEmployees(api.security.getGroup('$groupCode'))
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в результате вернулся список содержащий идентификаторы сотрудников: employee, employee2,
     * employee4</li>
     * </ol>
     */
    @Test
    public void testGetAllEmployeesFromSecurityGroup()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou2);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, false);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee4 = DAOEmployee.create(employeeCase, ou2, true);
        DSLBo.add(employee2, employee3, employee4);
        DSLEmployee.addToTeams(employee2, team);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee, ou2, team);

        //Выполнение действий и проверки
        String result = ScriptRunner.executeScript(
                "api.security.getAllEmployees(api.security.getGroup('%s'))", group.getCode());

        Set<String> actual = Json.GSON.fromJson(result, Json.SET_TYPE);
        Set<String> expected = Set.of(employee.getUuid(), employee2.getUuid(), employee4.getUuid());
        Assert.assertEquals("Полученный список сотрудников не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Тестирование метода api.security.getAllEmployees(Collection): возвращение списка из всех сотрудников,
     * входящих в переданный список групп пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44550647
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать сотрудников employee2, employee3, employee4 в отделе ou</li>
     * <li>Создать отдел ou2 и сотрудника employee5 в нём</li>
     * <li>Добавить employee4 в team</li>
     * <li>Создаем группу пользователей group и добавить в неё employee, ou2</li>
     * <li>Создаем группу пользователей group2 и добавить в неё employee2, employee3, team</li>
     * <li>Создаем группу пользователей group3 и оставить её пустой</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     def groups = [api.security.getGroup('$groupCode'),
     *         api.security.getGroup('$groupCode2'),
     *         api.security.getGroup('$groupCode3')]
     *     api.security.getAllEmployees(groups)
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $groupCode2 - код группы пользователей group2;
     *     3) $groupCode3 - код группы пользователей group3.
     * </pre></li>
     * <li>Проверить, что в результате вернулся список содержащий идентификаторы сотрудников: employee, employee2,
     * employee3, employee4, employee5</li>
     * </ol>
     */
    @Test
    public void testGetAllEmployeesFromSecurityGroups()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou2);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, false);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee4 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee5 = DAOEmployee.create(employeeCase, ou2, true);
        DSLBo.add(employee2, employee3, employee4, employee5);
        DSLEmployee.addToTeams(employee4, team);

        SecurityGroup group = DAOSecurityGroup.create();
        SecurityGroup group2 = DAOSecurityGroup.create();
        SecurityGroup group3 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group, group2, group3);

        DSLSecurityGroup.addUsers(group, employee, ou2);
        DSLSecurityGroup.addUsers(group, employee2, employee3, team);

        //Выполнение действий и проверки
        String result = ScriptRunner.executeScript("""
                def groups = [api.security.getGroup('%s'), api.security.getGroup('%s'), api.security.getGroup('%s')]
                api.security.getAllEmployees(groups)""", group.getCode(), group2.getCode(), group3.getCode());

        Set<String> actual = Json.GSON.fromJson(result, Json.SET_TYPE);
        Set<String> expected = Set.of(employee.getUuid(), employee2.getUuid(), employee3.getUuid(), employee4.getUuid(),
                employee5.getUuid());
        Assert.assertEquals("Полученный список сотрудников не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Тестирование метода api.security.addEmployeeToGroup: добавление сотрудника в группу пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42016792
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, который добавляет сотрудника в группу:
     * <pre>
     *     -------------------------------------------------------------------
     *     def employee = utils.get('$employeeUuid')
     *     api.security.addEmployeeToGroup('$groupCode', employee)
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     3) $employeeUuid - идентификатор сотрудника employee.
     * </pre></li>
     * <li>Проверить, что сотрудник employee присутствует в группе group</li>
     * </ol>
     */
    @Test
    public void testAddEmployeeToGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        //Выполнение действия
        ScriptRunner.executeScript("api.security.addEmployeeToGroup('%s', utils.get('%s'))", group.getCode(),
                employee.getUuid());

        //Проверки
        GroupUsers users = DSLSecurityGroup.getUsers(group);
        Assert.assertEquals("Сотрудник отсутствие в группе.", users.employees(), Set.of(employee.getUuid()));
    }

    /**
     * Тестирование метода api.security.addMembersToGroup: добавление участника (сотрудника, команду, отдел) в группу
     * пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42016792
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, который добавляет участника в группу:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMemberToGroup('$groupCode', '$ouUuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в группе group присутствует только отдел ou</li>
     * </ol>
     */
    @Test
    public void testAddMemberToGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.addMemberToGroup('%s', '%s')", group.getCode(), ou.getUuid());

        GroupUsers users = DSLSecurityGroup.getUsers(group);
        Assert.assertEquals(users.employees(), Set.of());
        Assert.assertEquals(users.teams(), Set.of());
        Assert.assertEquals(users.ous(), Set.of(ou.getUuid()));
    }

    /**
     * Тестирование метода api.security.addMembersToGroup: добавление участников (сотрудника, команду, отдел) в группу
     * пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42016792
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, который добавляет участников в группу:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMembersToGroup('$groupCode', ['$employeeUuid', '$teamUuid', '$ouUuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employeeUuid - идентификатор сотрудника employee;
     *     3) $teamUuid - идентификатор команды team;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в группе group присутствует сотрудник employee, команда team и отдел ou</li>
     * </ol>
     */
    @Test
    public void testAddMembersToGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.addMembersToGroup('%s', ['%s', '%s', '%s'])", group.getCode(),
                employee.getUuid(), team.getUuid(), ou.getUuid());

        GroupUsers users = DSLSecurityGroup.getUsers(group);
        Assert.assertEquals(users.employees(), Set.of(employee.getUuid()));
        Assert.assertEquals(users.teams(), Set.of(team.getUuid()));
        Assert.assertEquals(users.ous(), Set.of(ou.getUuid()));
    }

    /**
     * Тестирование метода api.security.removeMemberFromGroup: удаление участника (сотрудника, команду, отдел) из группы
     * пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42016792
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать отдел ou2</li>
     * <li>Создаем группу пользователей group и добавить в неё: employee, team, ou, ou2</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, который удаляет участника из группу:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMemberFromGroup('$groupCode', '$ou2Uuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     4) $ou2Uuid - идентификатор отдела ou2.
     * </pre></li>
     * <li>Проверить, что в группе group присутствуют только: сотрудник employee, команда team и отдел ou</li>
     * </ol>
     */
    @Test
    public void testRemoveMemberFromGroup()
    {
        // Подготовка
        Bo ou2 = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou2);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee, team, ou, ou2);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.removeMemberFromGroup('%s', '%s')", group.getCode(), ou2.getUuid());

        GroupUsers users = DSLSecurityGroup.getUsers(group);
        Assert.assertEquals(users.employees(), Set.of(employee.getUuid()));
        Assert.assertEquals(users.teams(), Set.of(team.getUuid()));
        Assert.assertEquals(users.ous(), Set.of(ou.getUuid()));
    }

    /**
     * Тестирование метода api.security.removeMembersFromGroup: удаление участников (сотрудника, команду, отдел) из
     * группы пользователей.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42016792
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать отдел ou2, команду team2</li>
     * <li>Создать сотрудника employee2 в отделе ou2</li>
     * <li>Создаем группу пользователей group и добавить в неё: employee, employee2, team, team2, ou, ou2</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, который удаляет участников из группы:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMembersFromGroup('$groupCode', ['$employee2Uuid', '$team2Uuid', '$ou2Uuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employee2Uuid - идентификатор сотрудника employee2;
     *     3) $team2Uuid - идентификатор команды team2;
     *     4) $ou2Uuid - идентификатор отдела ou2.
     * </pre></li>
     * <li>Проверить, что в группе group присутствуют только: сотрудник employee, команда team и отдел ou</li>
     * </ol>
     */
    @Test
    public void testRemoveMembersFromGroup()
    {
        //Подготовка
        Bo ou2 = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou2);
        Bo team2 = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team2);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), ou2, true);
        DSLBo.add(employee2);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee, employee2, team, team2, ou, ou2);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.removeMembersFromGroup('%s', ['%s', '%s', '%s'])", group.getCode(),
                employee2.getUuid(), team2.getUuid(), ou2.getUuid());

        GroupUsers users = DSLSecurityGroup.getUsers(group);
        Assert.assertEquals(users.employees(), Set.of(employee.getUuid()));
        Assert.assertEquals(users.teams(), Set.of(team.getUuid()));
        Assert.assertEquals(users.ous(), Set.of(ou.getUuid()));
    }

    /**
     * Тестирование метода api.security.addEmployeeToGroup: логирование ошибок при передаче в метод не корректных
     * аргументов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addEmployeeToGroup(null, null)
     *     -------------------------------------------------------------------
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что код группы не может быть пустой строкой</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addEmployeeToGroup('$groupCode', null)
     *     -------------------------------------------------------------------
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что идентификатор сотрудника не может быть пустым</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addEmployeeToGroup('$groupCode', '$ouUuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в группе group отсутствует отдел ou</li>
     * </ol>
     */
    @Test
    public void testLogErrorWhenAddInvalidEmployeeToGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.addEmployeeToGroup(null, null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal group code, must be not empty string.");

        ScriptRunner.executeScript("api.security.addEmployeeToGroup('%s', null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal employee object, must be not null.");

        ScriptRunner.executeScript("api.security.addEmployeeToGroup('%s', utils.get('%s'))", group.getCode(),
                ou.getUuid());
        DSLLog.assertPresentMessageInLog(
                String.format("ERROR api.SecurityApi - Illegal employee object. Passed UUID: '%s'.", ou.getUuid()));
        DSLSecurityGroup.assertObjectsNotInGroup(group, ou);
    }

    /**
     * Тестирование метода api.security.addMemberToGroup: логирование ошибок при передаче в метод не корректных
     * аргументов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMemberToGroup(null, null)
     *     -------------------------------------------------------------------
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что код группы не может быть пустой строкой</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMemberToGroup('$groupCode', null)
     *     -------------------------------------------------------------------
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что идентификатор участника не может быть пустым</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMemberToGroup('$groupCode', '$employeeUuid,$teamUuid,$ouUuid')
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employeeUuid - идентификатор сотрудника employee;
     *     3) $teamUuid - идентификатор команды team;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что идентификатор участника не корректен</li>
     * <li>Проверить, что в группе group отсутствует отдел ou, команда team и сотрудник employee</li>
     * </ol>
     */
    @Test
    public void testLogErrorWhenAddInvalidMemberToGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.addMemberToGroup(null, null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal group code, must be not empty string.");

        ScriptRunner.executeScript("api.security.addMemberToGroup('%s', null)", group.getCode());
        DSLLog.assertPresentMessageInLog("ERROR api.SecurityApi - Illegal group member, must be not null.");

        String brokenUuid = employee.getUuid() + ',' + team.getUuid() + ',' + ou.getUuid();
        ScriptRunner.executeScript("api.security.addMemberToGroup('%s', '%s')", group.getCode(), brokenUuid);
        DSLLog.assertPresentMessageInLog(
                String.format("ERROR api.SecurityApi - Illegal group member UUID: '%s'.", brokenUuid));
        DSLSecurityGroup.assertObjectsNotInGroup(group, employee, team, ou);
    }

    /**
     * Тестирование метода api.security.addMembersToGroup: логирование ошибок при передаче в метод не корректных
     * аргументов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMembersToGroup(null, null)
     *     -------------------------------------------------------------------
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что код группы не может быть пустой строкой</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMembersToGroup('$groupCode', null)
     *     -------------------------------------------------------------------
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что список участников не может быть пустым</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.addMembersToGroup('$groupCode', ['$employeeUuid,$teamUuid,$ouUuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employeeUuid - идентификатор сотрудника employee;
     *     3) $teamUuid - идентификатор команды team;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что список участников содержит не корректный
     * идентификатор</li>
     * <li>Проверить, что в группе group отсутствует отдел ou, команда team и сотрудник employee</li>
     * </ol>
     */
    @Test
    public void testLogErrorWhenAddInvalidMemberToGroups()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.addMembersToGroup(null, null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal group code, must be not empty string.");

        ScriptRunner.executeScript("api.security.addMembersToGroup('%s', null)", group.getCode());
        DSLLog.assertPresentMessageInLog("Illegal group member collection, must be not empty.");

        String brokenUuid = employee.getUuid() + ',' + team.getUuid() + ',' + ou.getUuid();
        ScriptRunner.executeScript("api.security.addMembersToGroup('%s', ['%s'])", group.getCode(), brokenUuid);
        DSLLog.assertPresentMessageInLog(
                String.format("ERROR api.SecurityApi - Illegal group members UUIDs: '%s'.", brokenUuid));
        DSLSecurityGroup.assertObjectsNotInGroup(group, employee, team, ou);
    }

    /**
     * Тестирование метода api.security.removeMemberFromGroup: логирование ошибок при передаче в метод не корректных
     * аргументов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMemberFromGroup(null, null)
     *     -------------------------------------------------------------------
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что код группы не может быть пустой строкой</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMemberFromGroup('$groupCode', null)
     *     -------------------------------------------------------------------
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что идентификатор участника не может быть пустым</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMemberFromGroup('$groupCode', '$employeeUuid,$teamUuid,$ouUuid')
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employeeUuid - идентификатор сотрудника employee;
     *     3) $teamUuid - идентификатор команды team;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что идентификатор участника не корректен</li>
     * <li>Проверить, что в группе group остались ранее добавленные отдел ou, команда team и сотрудник employee</li>
     * </ol>
     */
    @Test
    public void testLogErrorWhenRemoveInvalidMemberFromGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee, team, ou);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.removeMemberFromGroup(null, null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal group code, must be not empty string.");

        ScriptRunner.executeScript("api.security.removeMemberFromGroup('%s', null)", group.getCode());
        DSLLog.assertPresentMessageInLog("ERROR api.SecurityApi - Illegal group member, must be not null.");

        String brokenUuid = employee.getUuid() + ',' + team.getUuid() + ',' + ou.getUuid();
        ScriptRunner.executeScript("api.security.removeMemberFromGroup('%s', '%s')", group.getCode(), brokenUuid);
        DSLLog.assertPresentMessageInLog(
                String.format("ERROR api.SecurityApi - Illegal group member UUID: '%s'.", brokenUuid));
        DSLSecurityGroup.assertUsers(group, employee, team, ou);
    }

    /**
     * Тестирование метода api.security.removeMembersFromGroup: логирование ошибок при передаче в метод не корректных
     * аргументов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать группу пользователей group</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMembersFromGroup(null, null)
     *     -------------------------------------------------------------------
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что код группы не может быть пустой строкой</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMembersFromGroup('$groupCode', null)
     *     -------------------------------------------------------------------
     *     1) $groupCode - код группы пользователей group.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что список участников не может быть пустым</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------
     *     api.security.removeMembersFromGroup('$groupCode', ['$employeeUuid,$teamUuid,$ouUuid'])
     *     -------------------------------------------------------------------
     *     Где:
     *     1) $groupCode - код группы пользователей group;
     *     2) $employeeUuid - идентификатор сотрудника employee;
     *     3) $teamUuid - идентификатор команды team;
     *     4) $ouUuid - идентификатор отдела ou.
     * </pre></li>
     * <li>Проверить, что в логе присутствует ошибка о том, что список участников содержит не корректный
     * идентификатор</li>
     * <li>Проверить, что в группе group остались ранее добавленные отдел ou, команда team и сотрудник employee</li>
     * </ol>
     */
    @Test
    public void testLogErrorWhenRemoveInvalidMembersFromGroup()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee, team, ou);

        // Действия и проверки
        ScriptRunner.executeScript("api.security.removeMembersFromGroup(null, null)", group.getCode());
        DSLLog.assertPresentMessageInLog(
                "ERROR api.SecurityApi - Illegal group code, must be not empty string.");

        ScriptRunner.executeScript("api.security.removeMembersFromGroup('%s', null)", group.getCode());
        DSLLog.assertPresentMessageInLog("Illegal group member collection, must be not empty.");

        String brokenUuid = employee.getUuid() + ',' + team.getUuid() + ',' + ou.getUuid();
        ScriptRunner.executeScript("api.security.removeMembersFromGroup('%s', ['%s'])", group.getCode(), brokenUuid);
        DSLLog.assertPresentMessageInLog(
                String.format("ERROR api.SecurityApi - Illegal group members UUIDs: '%s'.", brokenUuid));
        DSLSecurityGroup.assertUsers(group, employee, team, ou);
    }
}
