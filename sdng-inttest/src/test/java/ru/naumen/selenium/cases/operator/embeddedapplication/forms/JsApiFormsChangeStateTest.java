package ru.naumen.selenium.cases.operator.embeddedapplication.forms;

import java.io.File;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование смены статуса в JS API (jsApi.forms.changeState)
 *
 * <AUTHOR>
 * @since 10.09.2020
 */
class JsApiFormsChangeStateTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private MetaClass userClass, userCase;
    private BoStatus registered, closed, resolved;
    private Bo bo;
    private String resultTag, errorTag;

    public final static Gson GSON = new GsonBuilder().serializeNulls().create();

    /**
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Создать пользовательский класс userClass со сменой статуса</li>
     *   <li>Создать пользовательский тип userCase</li>
     *   <li>Добавить в ЖЦ userClass переходы
     *     <pre>
     *       registered -> resolved -> closed
     *            \           /          /
     *             ---------> --------->
     *     </pre>
     *   </li>
     *   <li>Создать объект bo типа userCase</li>
     * </ol>
     */
    @BeforeEach
    public void prepareFixture()
    {
        userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        registered = DAOBoStatus.createRegistered(userClass);
        closed = DAOBoStatus.createClosed(userClass);
        resolved = DAOBoStatus.createResolved(userClass);
        DSLBoStatus.add(resolved);

        DSLMetaClass.setStatesOrder(userClass, registered, resolved, closed);
        DSLBoStatus.setTransitions(registered, resolved, closed);

        bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        resultTag = ModelUtils.createCode();
        errorTag = ModelUtils.createCode();
    }

    /**
     * Тестирование открытия формы смены статуса с несколькими целевыми статусами из jsApi.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.changeState('$uuid', ['closed', 'resolved'])
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - идентификатор объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Выбрать статус Разрешен</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Разрешен</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithMultipleTargetStates()
    {
        // Подготовка
        DSLMetaClass.setStatesOrder(userClass, registered, resolved, closed);
        DSLBoStatus.setTransitions(registered, resolved, closed);

        //Подготовка
        createApplicationContent(userClass, bo, closed, resolved);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.clickAndSelect(String.format(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, "newStateProperty"),
                resolved.getTitle());
        GUIForm.applyModalForm();

        DSLBo.assertState(bo, resolved.getCode());
    }

    /**
     * Тестирование открытия формы смены статуса с одним целевым статусом из jsApi.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Создать пользовательский тип userCase2</li>
     *   <li>Создать объект bo2 типа userCase2</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.changeState('$uuid', ['closed'])
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением
     *   на карточку типа userCase</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.changeState('$uuid', ['closed'], true)
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo2;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением
     *   на карточку типа userCase2</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Выбрать статус Закрыт</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Закрыт</li>
     *   <li>Перейти на карточку объекта bo2</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Выбрать статус Закрыт</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Закрыт</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithOnlyOneTargetState()
    {
        // Подготовка
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase2);
        Bo bo2 = DAOUserBo.create(userCase2);
        DSLBo.add(bo2);

        createApplicationContent(userCase, bo, closed);
        createApplicationContent(userCase2, bo2, true, closed);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.clickAndSelect(String.format(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, "newStateProperty"),
                closed.getTitle());
        GUIForm.applyModalForm();

        DSLBo.assertState(bo, closed.getCode());

        GUIBo.goToCard(bo2);
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.clickAndSelect(String.format(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, "newStateProperty"),
                closed.getTitle());
        GUIForm.applyModalForm();

        DSLBo.assertState(bo2, closed.getCode());
    }

    /**
     * Тестирование открытия формы смены статуса с одним целевым статусом из jsApi,
     * когда подтверждение перехода не обязательно
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.changeState('$uuid', ['closed'], false)
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo</li>
     *   <li>Проверить, что bo находится в статусе Закрыт</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithOnlyOneTargetStateAndRequiredConfirm()
    {
        // Подготовка
        createApplicationContent(userClass, bo, false, closed);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);

        DSLBo.assertState(bo, closed.getCode());
    }

    /**
     * Тестирование отсутствия системного диалогового окна,
     * когда нет обработки вернувшегося Promise и при открытии формы смены статуса из jsApi произошла ошибка.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.changeState('$uuid', ['closed'])
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid сотрудника;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что не появилось модальное окно с ошибкой</li>
     * </ol>
     */
    @Test
    void testThatErrorOnlyLoggedWhenChangeStateCommandWithoutCallback()
    {
        // Подготовка
        createApplicationContent(userClass, SharedFixture.employee(), closed);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);

        GUIForm.assertDialogDisappear("Не должно появляться диалоговое окно");
    }

    private void createApplicationContent(MetaClass userCase, Bo object, BoStatus... statuses)
    {
        createApplicationContent(userCase, object, null, statuses);
    }

    private void createApplicationContent(MetaClass userCase, Bo object, Boolean requiredConfirm, BoStatus... statuses)
    {
        String statusTemplate = getStatusTemplate(statuses);

        String applicationTemplate = (requiredConfirm != null)
                ? String.format("jsApi.forms.changeState('%s', %s, %b)",
                object.getUuid(), statusTemplate, requiredConfirm)
                : String.format("jsApi.forms.changeState('%s', %s)", object.getUuid(), statusTemplate);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                applicationTemplate);
        EmbeddedApplication app = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(app);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), app);
        DSLContent.add(content);
    }

    /**
     * Тестирование команды смены статуса, когда есть обработка успешного сохранения в Promise
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     document.addEventListener('DOMContentLoaded', function() {
     *       var testDiv = document.getElementById('$testDiv')
     *       var uuidDiv = document.createElement('div')
     *       uuidDiv.id = '$uuidDiv'
     *       testDiv.appendChild(uuidDiv)
     *       var errorDiv = document.createElement('div')
     *       errorDiv.id = '$errorDiv'
     *       testDiv.appendChild(errorDiv)
     *       jsApi.forms.changeState('$uuid', ['closed', 'resolved'])
     *            .then((uuid) => uuidDiv.innerText = uuid)
     *            .catch((error) => errorDiv.innerText = error.message)
     *       })
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $testDiv - id тега div, который уже присутствует во встройке;
     *     2) $uuidDiv - id тега div, который будет содержать результат выполнения then;
     *     3) $errorDiv - id тега div, который будет содержать результат выполнения catch;
     *     4) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Выбрать статус Разрешен</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Разрешен</li>
     *   <li>Проверить, что во встроенном приложении вызвалась функция переданная в then,
     *   в которую единственным параметром передан uuid объекта bo.</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithCallbackWhenFormApplied()
    {
        // Подготовка
        ContentForm content = createWithCallbackApplicationContent(userClass, bo, resultTag, errorTag, closed,
                resolved);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.clickAndSelect(String.format(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, "newStateProperty"),
                resolved.getTitle());
        GUIForm.applyModalForm();

        DSLBo.assertState(bo, resolved.getCode());
        assertResult(content, resultTag, resolved, errorTag, "");
    }

    /**
     * Тестирование того, что команда смены статуса ведёт себя как будто сохранение прошло успешно,
     * когда объект уже находится в требуемом статусе.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     document.addEventListener('DOMContentLoaded', function() {
     *       var testDiv = document.getElementById('$testDiv')
     *       var uuidDiv = document.createElement('div')
     *       uuidDiv.id = '$uuidDiv'
     *       testDiv.appendChild(uuidDiv)
     *       var errorDiv = document.createElement('div')
     *       errorDiv.id = '$errorDiv'
     *       testDiv.appendChild(errorDiv)
     *       jsApi.forms.changeState('$uuid', ['registered'])
     *            .then((uuid) => uuidDiv.innerText = uuid)
     *            .catch((error) => errorDiv.innerText = error.message)
     *       })
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $testDiv - id тега div, который уже присутствует во встройке;
     *     2) $uuidDiv - id тега div, который будет содержать результат выполнения then;
     *     3) $errorDiv - id тега div, который будет содержать результат выполнения catch;
     *     4) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Зарегистрирован</li>
     *   <li>Проверить, что во встроенном приложении вызвалась функция переданная в then,
     *   в которую единственным параметром передан uuid объекта bo.</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithCallbackWhenObjectActuallyHasTargetState()
    {
        // Подготовка
        ContentForm content = createWithCallbackApplicationContent(userClass, bo, resultTag, errorTag, registered);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);

        DSLBo.assertState(bo, registered.getCode());
        assertResult(content, resultTag, registered, errorTag, "");
    }

    /**
     * Тестирование команды смены статуса, когда есть обработка ошибки и вернулась ошибка об отсутствии прав
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     document.addEventListener('DOMContentLoaded', function() {
     *       var testDiv = document.getElementById('$testDiv')
     *       var uuidDiv = document.createElement('div')
     *       uuidDiv.id = '$uuidDiv'
     *       testDiv.appendChild(uuidDiv)
     *       var errorDiv = document.createElement('div')
     *       errorDiv.id = '$errorDiv'
     *       testDiv.appendChild(errorDiv)
     *       jsApi.forms.changeState('$uuid', ['registered'])
     *            .then((uuid) => uuidDiv.innerText = uuid)
     *            .catch((error) => errorDiv.innerText = error.message)
     *       })
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $testDiv - id тега div, который уже присутствует во встройке;
     *     2) $uuidDiv - id тега div, который будет содержать результат выполнения then;
     *     3) $errorDiv - id тега div, который будет содержать результат выполнения catch;
     *     4) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под нелицензированным сотрудником</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что во встроенном приложении вызвалась функция переданная в catch,
     *   в которую единственным параметром передана ошибка с текстом
     *   "Объект "%bo" не может быть изменен по следующим причинам:
     *   у Вас нет прав на смену статуса объекта класса/типа "%metaClass"."</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithCallbackWhenNoRights()
    {
        // Подготовка
        ContentForm content = createWithCallbackApplicationContent(userClass, bo, resultTag, errorTag, registered);

        // Действия и проверки
        GUILogon.asUnlicensed();

        GUIBo.goToCard(bo);

        assertResult(content, resultTag, null, errorTag,
                String.format("Объект \"%s\" не может быть изменен по следующим причинам: "
                              + "у Вас нет прав на смену статуса объекта класса/типа \"%s\".",
                        bo.getTitle(), userCase.getTitle()));
    }

    /**
     * Тестирование команды смены статуса, когда есть обработка отмены смены статуса в Promise
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     document.addEventListener('DOMContentLoaded', function() {
     *       var testDiv = document.getElementById('$testDiv')
     *       var uuidDiv = document.createElement('div')
     *       uuidDiv.id = '$uuidDiv'
     *       testDiv.appendChild(uuidDiv)
     *       var errorDiv = document.createElement('div')
     *       errorDiv.id = '$errorDiv'
     *       testDiv.appendChild(errorDiv)
     *       jsApi.forms.changeState('$uuid', ['closed', 'resolved'])
     *            .then((uuid) => uuidDiv.innerText = uuid)
     *            .catch((error) => errorDiv.innerText = error.message)
     *       })
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $testDiv - id тега div, который уже присутствует во встройке;
     *     2) $uuidDiv - id тега div, который будет содержать результат выполнения then;
     *     3) $errorDiv - id тега div, который будет содержать результат выполнения catch;
     *     4) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Нажать кнопку Отмена на форме</li>
     *   <li>Проверить, что во встроенном приложении вызвалась функция переданная в then,
     *   в которую единственным параметром передан null.</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithCallbackWhenFormCanceled()
    {
        // Подготовка
        ContentForm content = createWithCallbackApplicationContent(userClass, bo, resultTag, errorTag, closed,
                resolved);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.cancelForm();

        assertResult(content, resultTag, null, errorTag, "");
    }

    /**
     * Тестирование команды смены статуса, когда есть обработка ошибки и вернулась ошибка об отсутствии ЖЦ в классе
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     document.addEventListener('DOMContentLoaded', function() {
     *       var testDiv = document.getElementById('$testDiv')
     *       var uuidDiv = document.createElement('div')
     *       uuidDiv.id = '$uuidDiv'
     *       testDiv.appendChild(uuidDiv)
     *       var errorDiv = document.createElement('div')
     *       errorDiv.id = '$errorDiv'
     *       testDiv.appendChild(errorDiv)
     *       jsApi.forms.changeState('$uuid', ['closed'])
     *            .then((uuid) => uuidDiv.innerText = uuid)
     *            .catch((error) => errorDiv.innerText = error.message)
     *       })
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $testDiv - id тега div, который уже присутствует во встройке;
     *     2) $uuidDiv - id тега div, который будет содержать результат выполнения then;
     *     3) $errorDiv - id тега div, который будет содержать результат выполнения catch;
     *     4) $uuid - uuid сотрудника;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что во встроенном приложении вызвалась функция переданная в catch,
     *   в которую единственным параметром передана ошибка с текстом
     *   "Объект "%bo" не может быть изменен по следующим причинам:
     *   у объектов класса "Сотрудник" отсутствует жизненный цикл.".</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandWithCallbackWhenMetaClassNoHaveChangeState()
    {
        // Подготовка
        Bo employee = SharedFixture.employee();
        ContentForm content = createWithCallbackApplicationContent(userClass, employee, resultTag,
                errorTag, closed);

        // Действия и проверки
        GUILogon.asNaumen();

        GUIBo.goToCard(bo);

        assertResult(content, resultTag, null, errorTag,
                String.format("Объект \"%s\" не может быть изменен по следующим причинам: "
                              + "у объектов класса \"Сотрудник\" отсутствует жизненный цикл.", employee.getTitle()));
    }

    /**
     * Тестирование сохранения формы смены статуса открытой после открытия формы смены ответственного через jsApi
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00344
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157134814
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.forms.changeResponsible('$uuid').then(() => {jsApi.forms.changeState('$uuid', ['resolved'])})
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Нажать отмена</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Выбрать статус Разрешен</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что bo находится в статусе Разрешен</li>
     * </ol>
     */
    @Test
    void testChangeStateCommandAfterChangeResponsibleCommandStates()
    {
        // Подготовка
        String jsContent = String.format(
                "jsApi.forms.changeResponsible('%s').then(() => jsApi.forms.changeState('%s', %s))",
                bo.getUuid(), bo.getUuid(), getStatusTemplate(resolved));
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent);
        EmbeddedApplication app = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(app);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), app);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asNaumen();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение ответственного");
        GUIForm.clickCancelTopmostDialog();
        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.clickAndSelect(String.format(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, "newStateProperty"),
                resolved.getTitle());
        GUIForm.applyModalForm();
        DSLBo.assertState(bo, resolved.getCode());
    }

    /**
     * Тестирование доступа к значениям ссылочных атрибутов при открытии формы изменении статуса <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00340 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$236310172 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с включенным ЖЦ</li>
     * <li>Создать пользовательский тип userCase</li>
     * <li>Добавить в ЖЦ userClass переходы
     * <pre>
     *     registered -> resolved -> closed
     *          \         /
     *           ---------
     * </pre>
     * </li>
     * <li>Создать объект bo типа userCase, отдел ou и сотрудника employee</li>
     * <li>В типе userCase создать агрегирующий атрибут с кодом aggregateAttr, агрегировать сотрудник-отдел</li>
     * <li>В типе userCase создать атрибут типа ссылка на БО типа employeeCase с кодом objectLinkAttr</li>
     * <li>В типе userCase создать атрибут типа набор ссылок на БО типа employeeCase с кодом boLinksAttr</li>
     * <li>Сделать атрибут aggregateAttr редактируемым на входе в статус "Разрешен"</li>
     * <li>Сделать атрибуты objectLinkAttr и boLinksAttr редактируемыми на входе в статус "Закрыт"</li>
     * <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     * <pre>
     *     ----------------------------------------------
     *     jsApi.commands.changeState($boUuid, ['resolved', 'closed'])
     *     ----------------------------------------------
     *     Где $boUuid - UUID объекта, статус которого мы меняем
     * </pre>
     * </li>
     * <li>Включить добавленное встроенное приложение</li>
     * <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку Компании</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку Компании</li>
     * <li>В окне смены статуса выбрать статус Разрешен и проверить, что на форме смены статуса агрегирующий атрибут
     * заполнен значениями</li>
     * <li>Выбрать статус Закрыт и проверить, что на форме смены статуса атрибуты objectLinkAttr и boLinksAttr
     * заполнены значениями</li>
     * </ol>
     */
    @Test
    void testAvailabilityLinkedAttributesWhenChangeState()
    {
        // Подготовка
        Bo ou = SharedFixture.ou();
        Bo employee = SharedFixture.employee();

        MetaClass employeeCase = SharedFixture.employeeCase();
        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null);
        Attribute objectLinkAttr = DAOAttribute.createObjectLink(userClass, employeeCase, null);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass.getFqn(), employeeCase);

        DSLAttribute.add(aggregateAttr, objectLinkAttr, boLinksAttr);

        DSLBoStatus.setAttrInState(aggregateAttr.getCode(), resolved, true, true, 1, 0);
        DSLBoStatus.setAttrInState(objectLinkAttr.getCode(), closed, true, true, 1, 0);
        DSLBoStatus.setAttrInState(boLinksAttr.getCode(), closed, true, true, 1, 0);

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "jsApi.forms.changeState('%s', ['%s', '%s'])".formatted(bo.getUuid(), resolved.getCode(),
                        closed.getCode()));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), model);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.clickAndSelect(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE.formatted("newStateProperty"),
                resolved.getTitle());
        BoTree tree = new BoTree(GUIXpath.Any.ANY_VALUE.formatted(aggregateAttr.getCode()), false);
        tree.showTree();
        tree.openTreeWithNode(ou.getUuid());
        tree.assertNodeOfTreeExpanded(false, ou.getUuid(), employee.getUuid());

        GUIForm.clickAndSelect(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE.formatted("newStateProperty"),
                closed.getTitle());
        BoTree tree2 = new BoTree(GUIXpath.Any.ANY_VALUE.formatted(boLinksAttr.getCode()), false);
        tree2.showTree();
        tree2.openTreeWithNode(ou.getUuid());
        tree2.assertNodeOfTreeExpanded(false, ou.getUuid(), employee.getUuid());

        BoTree tree3 = new BoTree(GUIXpath.Any.ANY_VALUE.formatted(objectLinkAttr.getCode()), false);
        tree3.showTree();
        tree3.openTreeWithNode(ou.getUuid());
        tree3.assertNodeOfTreeExpanded(false, ou.getUuid(), employee.getUuid());
    }

    private ContentForm createWithCallbackApplicationContent(MetaClass userCase, Bo object, String resultTag,
            String errorTag, BoStatus... statuses)
    {
        String statusTemplate = Arrays.stream(statuses)
                .map(BoStatus::getCode)
                .collect(Collectors.joining("', '", "['", "']"));

        String jsTemplate = "document.addEventListener('DOMContentLoaded', function() {\n" +
                            "   var testDiv = document.getElementById('%s')\n" +
                            "   var resultDiv = document.createElement('div')\n" +
                            "   resultDiv.id = '%s'\n" +
                            "   testDiv.appendChild(resultDiv)\n" +
                            "   var errorDiv = document.createElement('div')\n" +
                            "   errorDiv.id = '%s'\n" +
                            "   testDiv.appendChild(errorDiv)\n" +
                            "   jsApi.forms.changeState('%s', %s)\n" +
                            "       .then((result) => resultDiv.innerHTML = (result) ? result.state : null)\n" +
                            "       .catch((error) => errorDiv.innerHTML = error.message)\n" +
                            "})";

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(jsTemplate, GUIEmbeddedApplication.TEST_DIV_ID, resultTag, errorTag, object.getUuid(),
                        statusTemplate));
        EmbeddedApplication app = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(app);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), app);
        DSLContent.add(content);

        return content;
    }

    private static String getStatusTemplate(BoStatus... statuses)
    {
        return Arrays.stream(statuses)
                .map(BoStatus::getCode)
                .collect(Collectors.joining("', '", "['", "']"));
    }

    private static void assertResult(ContentForm content, String resultTag, @Nullable BoStatus status,
            String errorTag, String error)
    {
        String result = (status != null) ? status.getCode() : "";
        Assertions.assertEquals(result, GUIEmbeddedApplication.getEmbeddedApplicationDivContent(content, resultTag));
        Assertions.assertEquals(error, GUIEmbeddedApplication.getEmbeddedApplicationDivContent(content, errorTag));
    }
}
