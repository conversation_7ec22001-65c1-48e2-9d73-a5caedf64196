package ru.naumen.selenium.cases.operator.embeddedapplication.forms;

import static ru.naumen.selenium.casesutil.scripts.listdata.DefineListDescriptorScriptTemplates.withAttributeGroup;
import static ru.naumen.selenium.casesutil.scripts.listdata.DefineListDescriptorScriptTemplates.withCreateListDescriptor;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.google.common.collect.Maps;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLFilterRestrictionSettings;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.FilterRestrictionStrategy;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFiltering;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.scripts.DSLListDataApi;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование настройки фильтрации через JS API (jsApi.forms.getFilterFormBuilder и jsApi.commands.filterForm)
 * В силу того, что с точки зрения реализации старая и новая версии сигнатуры - один и тот же код, для старой версии
 * сигнатуры проверяется только возможность её вызвать.
 *
 * <AUTHOR>
 * @since 15.12.2021
 */
class JsApiFormsFilterTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    public static final String OPEN_FILTER_FORM =
            "jsApi.forms.getFilterFormBuilder(%s).openForm()"
            + "    .then((result) => {"
            + "        document.body.innerHTML = JSON.stringify(result)"
            + "    })";
    private static final String OPEN_FILTER_FORM_WITH_LIST_USE_RESTRICTION =
            "jsApi.forms.getFilterFormBuilder(%s)"
            + "    .setAttributeList({ useRestriction: true })"
            + "    .openForm()"
            + "    .then((result) => {"
            + "        document.body.innerHTML = JSON.stringify(result)"
            + "    })";
    private static final String OPEN_FILTER_FORM_WITH_TREE_RESTRICTION =
            "jsApi.forms.getFilterFormBuilder(%s)\n"
            + "    .setAttributeTree({ restriction: %s })\n"
            + "    .openForm()\n"
            + "    .then((result) => {\n"
            + "        document.body.innerText = JSON.stringify(result)\n"
            + "    })";
    private static final String OPEN_FILTER_FORM_WITH_TREE_USE_CONTEXT_RESTRICTION =
            "jsApi.forms.getFilterFormBuilder(%s)\n"
            + "    .setAttributeTree({ useContextRestriction: true })\n"
            + "    .openForm()\n"
            + "    .then((result) => {\n"
            + "        document.body.innerText = JSON.stringify(result)\n"
            + "    })";
    private static final String OPEN_FILTER_FORM_WITH_TREE_RESTRICTION_AND_USE_CONTEXT_RESTRICTION =
            "jsApi.forms.getFilterFormBuilder(%s)\n"
            + "    .setAttributeTree({ useContextRestriction: true, restriction: %s })\n"
            + "    .openForm()\n"
            + "    .then((result) => {\n"
            + "        document.body.innerText = JSON.stringify(result)\n"
            + "    })";

    private static MetaClass employeeClass, employeeCase, userClass, userCase, userCase2;
    private static Attribute employeeOuLinkAttr;
    private static Bo ou, employee, employee2;
    private static GUIAdvListFiltering filterOnForm;

    /**
     * <b>Подготовка:</b>
     * <li>Создать тип сотрудника <code>employeeCase</code></li>
     * <li>Создаем пользовательский класс <code>userClass</code>, а в нем типы <code>userCase</code> и
     * <code>userCase2</code></li>
     * <li>Создать атрибут <code>employeeOuLinkAttr</code> типа Ссылка на БО в типе <code>employeeCase</code> на тип
     * <code>ouCase</code></li>
     * <li>Создать <code>employee</code>, <code>employee2</code> и <code>employee3</code> типа
     * <code>employeeCase</code> в отделе <code>ou</code></li>
     * <li>Заполнить атрибут <code>employeeOuLinkAttr</code> в объектах <code>employee</code> и
     * <code>employee3</code> значением <code>ou</code></li>
     * <li>Добавить контент <code>objectListContent</code> типа "Список объектов" с представлением "Сложный список"
     * на карточку типа <code>employeeCase</code></li>
     * <li>Задать <code>objectList</code> ограничение фильтрации: атрибут <code>employeeOuLinkAttr</code>, условие
     * "содержит", значение <code>ou</code></li>
     * </li>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        employeeClass = DAOEmployeeCase.createClass();
        employeeCase = DAOEmployeeCase.create();
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(employeeCase, userClass, userCase, userCase2);

        employeeOuLinkAttr = DAOAttribute.createObjectLink(employeeCase, SharedFixture.ouCase(), null);
        DSLAttribute.add(employeeOuLinkAttr);

        ou = SharedFixture.ou();
        employeeOuLinkAttr.setValue(ou.getUuid());
        employee = DAOEmployee.create(employeeCase, ou, true);
        employee.setUserAttribute(employeeOuLinkAttr);
        employee2 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, true);
        employee3.setUserAttribute(employeeOuLinkAttr);
        DSLBo.add(employee, employee2, employee3);

        filterOnForm = new GUIAdvListFiltering("");
    }

    /**
     * Тестирование открытия формы фильтрации встроенного приложения, когда в дескрипторе указанны подтипы класса, но
     * не указан класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://projects.naumen.ru/index.php?title=ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$85556604
     * <br>
     * <ol>
     * <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать типы класса <code>userClass</code>: <code>userCase3</code>, <code>userCase4</code></li>
     *   <li>Создать строковый атрибут <code>userCaseAttr</code> в типе <code>userCase</code></li>
     *   <li>Создать строковый атрибут <code>userCase2Attr</code> в типе <code>userCase2</code></li>
     *   <li>Создать группу атрибутов <code>attrGroup</code> в типе <code>userClass</code></li>
     *   <li>Добавить атрибут <code>userCaseAttr</code> в группу <code>attrGroup</code> в типе
     *   <code>userCase</code></li>
     *   <li>Добавить атрибут <code>userCase2Attr</code> в группу <code>attrGroup</code> в типе
     *   <code>userCase2</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>userClass</code>;</li>
     *     <li>Типы объектов: <code>userCase</code>, <code>userCase3</code>, <code>userCase4</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *     <li>Ограничение фильтрации: атрибут <code>userCaseAttr</code> "содержит" случайный текст</li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, ограничить список
     *   атрибутов атрибутом <code>userCaseAttr</code> и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'cases': ['$userCase', '$userCase3', '$userCase4'], attrCodes: ['$userCaseAttr'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userCase - FQN класса <code>userCase</code>,
     *       2) $userCase3 - FQN класса <code>userCase3</code>,
     *       3) $userCase4 - FQN класса <code>userCase4</code>
     *       4) $userCaseAttr - код атрибута <code>userCaseAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson).openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить, что в открывшейся форме установлено значение фильтрации для атрибута
     *   <code>userCaseAttr</code></li>
     *   <li>Проверить, что в открывшейся форме недоступна фильтрация по атрибуту <code>userCase2Attr</code></li>
     *   <li>Проверить, что в списке атрибутов при выборе критерия "Равно атрибуту текущего объекта" для атрибута
     *   "Название" доступны атрибуты класса "Компания", а не класса <code>userClass</code></li>
     *   <li>Проверить, что при выборе фильтрации по типу объекта и критерию "Содержит", в списке доступны типы
     *   <code>userCase1</code>, <code>userCase3</code>, <code>userCase4</code></li>
     *   <li>Проверить, что при выборе фильтрации по типу объекта и критерию "Содержит любое из значений", в списке
     *   доступны типы <code>userCase1</code>, <code>userCase3</code>, <code>userCase4</code></li>
     *   <li>Изменить для фильтрации по атрибуту <code>userCaseAttr</code> новое значение, условие фильтрации
     *   оставить "Содержит"</li>
     *   <li>Нажать на кнопку "Сохранить"</li>
     *   <li>Проверить, что в фильтре для <code>userCaseAttr</code> теперь присутствует новое значение</li>
     * </ol>
     */
    @Test
    void testFilterFormWhenContextContainsCasesAndClazzInNull()
    {
        // Подготовка
        MetaClass userCase3 = DAOUserCase.create(userClass);
        MetaClass userCase4 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase3, userCase4);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup);

        Attribute userCaseAttr = DAOAttribute.createString(userCase);
        Attribute userCase2Attr = DAOAttribute.createString(userCase2);
        DSLAttribute.add(userCaseAttr, userCase2Attr);

        GroupAttr userCaseGroupAttr = DAOGroupAttr.copy(attrGroup, userCase);
        DSLGroupAttr.addToGroup(userCaseGroupAttr, userCaseAttr);
        GroupAttr userCase2GroupAttr = DAOGroupAttr.copy(attrGroup, userCase2);
        DSLGroupAttr.addToGroup(userCase2GroupAttr, userCase2Attr);

        String initialFilterValue = ModelUtils.createText(10);
        String newFilterValue = ModelUtils.createText(10);

        ContentForm objectListContent =
                DAOContentCard.createObjectAdvList(employeeCase, userClass, userCase, userCase3, userCase4);
        FilterBlockOr blockOr = new FilterBlockOr(userCaseAttr, FilterCondition.CONTAINS, false, initialFilterValue);
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String descriptorAsJson =
                DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent, userCaseAttr);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().currentAttr(userCaseAttr);
        filterOnForm.asserts().attrsNotDisplayed(userCase2Attr);
        filterOnForm.asserts().currentCondition(FilterCondition.CONTAINS);
        filterOnForm.asserts().inputText(1, 1, initialFilterValue);

        Attribute titleAttr = SysAttribute.title(userClass);
        filterOnForm.addAttr(titleAttr, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.EQUALS_SUBJECT_ATTRIBUTE);
        filterOnForm.asserts().treeValueAbsent(1, 1, userCase2Attr.getFqn());
        filterOnForm.asserts().treeValuePresent(1, 1, SysAttribute.commentAuthorAlias().getFqn());

        Attribute metaClassAttr = SysAttribute.metaClass(userClass);
        filterOnForm.addAttr(metaClassAttr, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS);
        filterOnForm.asserts().selectValue(false, true,
                GUISelect.EMPTY_VALUE, userCase.getTitle(), userCase3.getTitle(), userCase4.getTitle());

        filterOnForm.addAttr(metaClassAttr, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS_IN_SET);
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase.getFqn());
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase3.getFqn());
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase4.getFqn());
        filterOnForm.asserts().treeValueAbsent(1, 1, userClass.getFqn() + "." + userCase2.getFqn());

        filterOnForm.addAttr(userCaseAttr, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS);
        filterOnForm.setString(1, 1, newFilterValue);
        GUIForm.applyModalForm();

        String actualContent = GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent);
        Assertions.assertTrue(actualContent.contains(newFilterValue));
        Assertions.assertTrue(actualContent.contains(userCaseAttr.getFqn()));
        Assertions.assertTrue(actualContent.contains(FilterCondition.CONTAINS.getCode()));
    }

    /**
     * Тестирование открытия формы фильтрации встроенного приложения, когда в дескрипторе указанны только класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://projects.naumen.ru/index.php?title=ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80149527
     * <br>
     * <ol>
     * <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать строковые атрибуты <code>stringAttr</code>, <code>stringAttr2</code> в классе
     *   <code>userClass</code></li>
     *   <li>Создать группу атрибутов <code>attrGroup</code> в классе <code>userClass</code> с атрибутами
     *   <code>stringAttr</code>, <code>stringAttr2</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>userClass</code>;</li>
     *     <li>Типы объектов: <code>userCase</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *     <li>Ограничение фильтрации: атрибут <code>stringAttr</code> "содержит" случайный текст</li>
     *   </ul>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, ограничить список
     *   атрибутов атрибутом <code>stringAttr</code> и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'clazz': $userClass, attrCodes: ['$stringAttr'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userClass - FQN класса <code>userCase</code>,
     *       4) $stringAttr - код атрибута <code>stringAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson).openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить, что в открывшейся форме установлено значение фильтрации для <code>stringAttr</code></li>
     *   <li>Установить для фильтрации по атрибуту <code>stringAttr2</code> новое значение, условие фильтрации
     *   оставить "Содержит"</li>
     *   <li>Нажать на кнопку "Сохранить"</li>
     *   <li>Проверить что данные вернулись во встроенное приложение</li>
     * </ol>
     */
    @Test
    void testFilterFormWhenContextContainsOnlyClazz()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        Attribute stringAttr2 = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr, stringAttr2);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr, stringAttr2);

        String initialFilterValue = ModelUtils.createText(10);
        String newFilterValue = ModelUtils.createText(10);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, userClass);
        FilterBlockOr blockOr = new FilterBlockOr(stringAttr, FilterCondition.CONTAINS, false, initialFilterValue);
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent, stringAttr);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().currentAttr(stringAttr);
        filterOnForm.asserts().currentCondition(FilterCondition.CONTAINS);
        filterOnForm.asserts().inputText(1, 1, initialFilterValue);

        filterOnForm.addAttr(stringAttr2, 1, 1);
        filterOnForm.setString(1, 1, newFilterValue);

        GUIForm.applyModalForm();
        String actualContent = GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent);
        Assertions.assertTrue(actualContent.contains(newFilterValue));
        Assertions.assertTrue(actualContent.contains(stringAttr2.getFqn()));
        Assertions.assertTrue(actualContent.contains(FilterCondition.CONTAINS.getCode()));
    }

    /**
     * Тестирование открытия формы фильтрации из встроенного приложения, с применением ограничений на список
     * атрибутов, заданных в виде списка доступных атрибутов или группы атрибутов внутри дескриптора.
     * Также тестирует работу старой версии сигнатуры с двумя параметрами: дескриптором и флагом применения
     * ограничений из дескриптора.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114083602
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать типы <code>userCase3</code> и <code>userCase4</code> в пользовательском классе
     *   <code>userClass</code></li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Создать объект <code>userBo2</code> типа <code>userCase2</code></li>
     *   <li>Создать объект <code>userBo3</code> типа <code>userCase3</code></li>
     *   <li>Создать объект <code>userBo4</code> типа <code>userCase4</code></li>
     *   <li>Создать строковый атрибут <code>stringAttr</code> в классе <code>userClass</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>userClass</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, ограничить список
     *   атрибутов атрибутом <code>stringAttr</code> и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'clazz': $userClass, attrCodes: ['$stringAttr'], attrGroupCode: ['system']... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userClass - FQN класса <code>userCase</code>,
     *       2) $stringAttr - код атрибута <code>stringAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application2</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code> с применением ограничений на список атрибутов, заданных в виде списка доступных
     *   атрибутов или группы атрибутов внутри дескриптора:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeList({ useRestriction: true })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code> и конвертировать
     *   дескриптор в урезанный контекст в формате JSON <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'clazz': $userClass, attrCodes: [], attrGroupCode: ['system']... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userClass - FQN класса <code>userCase</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application3</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code> с применением ограничений на список атрибутов, заданных в виде списка доступных
     *   атрибутов или группы атрибутов внутри дескриптора:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson2)
     *           .setAttributeList({ useRestriction: true })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson2 - урезанный списочный дескриптор <code>descriptorAsJson2</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application4</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code> с применением ограничений на список атрибутов, заданных в виде списка доступных
     *   атрибутов или группы атрибутов внутри дескриптора, с использованием старой сигнатуры метода:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.commands.filterForm($descriptorAsJson2, true)
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson2 - урезанный списочный дескриптор <code>descriptorAsJson2</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку
     *   <code>userCase</code></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application2</code> на карточку
     *   <code>userCase2</code></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application3</code> на карточку
     *   <code>userCase3</code></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application4</code> на карточку
     *   <code>userCase4</code></li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить что на форме в выпадающем списке атрибутов среди прочих присутствуют атрибуты
     *   <code>stringAttr</code>, "Название", "Тип объекта"</li>
     *   <li>Закрыть форму</li>
     *   <li>Перейти на карточку <code>userBo2</code></li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить что на форме в выпадающем списке атрибутов присутствует только атрибут
     *   <code>stringAttr</code></li>
     *   <li>Закрыть форму</li>
     *   <li>Перейти на карточку <code>userBo3</code></li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить что на форме в выпадающем списке атрибутов присутствуют только те атрибуты, что входят в
     *   системную группу атрибутов: "Название", "Тип объекта"</li>
     *   <li>Закрыть форму</li>
     *   <li>Перейти на карточку <code>userBo4</code></li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>Проверить что на форме в выпадающем списке атрибутов присутствуют только те атрибуты, что входят в
     *   системную группу атрибутов: "Название", "Тип объекта"</li>
     *   <li>Закрыть форму</li>
     * </ol>
     */
    @Test
    void testFilterFormWhenUsedRestrictionAttributeCodesOrAttributeGroupCode()
    {
        // Подготовка
        MetaClass userCase3 = DAOUserCase.create(userClass);
        MetaClass userCase4 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase3, userCase4);

        Bo userBo = DAOUserBo.create(userCase);
        Bo userBo2 = DAOUserBo.create(userCase2);
        Bo userBo3 = DAOUserBo.create(userCase3);
        Bo userBo4 = DAOUserBo.create(userCase4);
        DSLBo.add(userBo, userBo2, userBo3, userBo4);

        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, userClass);
        DSLContent.add(objectListContent);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent, stringAttr);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);

        String jsContent2 = String.format(OPEN_FILTER_FORM_WITH_LIST_USE_RESTRICTION, descriptorAsJson);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent2);

        String descriptorAsJson2 = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent);
        String jsContent3 = String.format(OPEN_FILTER_FORM_WITH_LIST_USE_RESTRICTION, descriptorAsJson2);
        EmbeddedApplication application3 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent3);

        String jsContent4 = String.format("jsApi.commands.filterForm(%s, true)", descriptorAsJson2); // старая сигнатура
        EmbeddedApplication application4 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent4);

        DSLEmbeddedApplication.add(application, application2, application3, application4);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(userCase2, application2);
        ContentForm content3 = DAOContentCard.createEmbeddedApplication(userCase3, application3);
        ContentForm content4 = DAOContentCard.createEmbeddedApplication(userCase4, application4);
        DSLContent.add(content, content2, content3, content4);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.assertPresent(content);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");
        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute metaClassAttr = SysAttribute.metaClass(userClass);
        filterOnForm.asserts().attrs(false, false, stringAttr, titleAttr, metaClassAttr);
        GUIForm.cancelForm();

        GUIBo.goToCard(userBo2);
        GUIContent.assertPresent(content2);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");
        filterOnForm.asserts().attrs(false, true, stringAttr);
        GUIForm.cancelForm();

        GUIBo.goToCard(userBo3);
        GUIContent.assertPresent(content3);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");
        filterOnForm.asserts().attrs(false, true, titleAttr, metaClassAttr);
        GUIForm.cancelForm();

        GUIBo.goToCard(userBo4);
        GUIContent.assertPresent(content4);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");
        filterOnForm.asserts().attrs(false, true, titleAttr, metaClassAttr);
        GUIForm.cancelForm();
    }

    /**
     * Тестирование открытия формы фильтрации на форме добавления объекта из встроенного приложения.
     * Также тестирует работу старой версии сигнатуры с единственным параметром - дескриптором.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/checkListsSettings
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$178255315
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>userCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code> и конвертировать
     *   дескриптор в урезанный контекст в формате JSON <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'cases': ['$employeeCase'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userCase - FQN типа <code>userCase</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации:
     *   <pre>
     *   --------------------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson).openForm()
     *   --------------------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application2</code>, открывающее форму фильтрации с использованием
     *   старой сигнатуры метода:
     *   <pre>
     *   --------------------------------------------------------------------
     *       jsApi.commands.filterForm($descriptorAsJson)
     *   --------------------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на форму добавления типа
     *   <code>userCase</code></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на форму добавления типа
     *   <code>userCase2</code></li>
     *   <b>Действия и проверки.</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Перейти на форму добавления класса <code>userClass</code></li>
     *   <li>Выбрать тип <code>userCase</code></li>
     *   <li>Проверить, что открылось диалоговое окно с заголовком "Настройка условий фильтрации"</li>
     *   <li>Закрыть форму</li>
     *   <li>Выбрать тип <code>userCase2</code></li>
     *   <li>Проверить, что открылось диалоговое окно с заголовком "Настройка условий фильтрации"</li>
     * </ol>
     */
    @Test
    void testFilterFormOnAddForm()
    {
        // Подготовка
        ContentForm objectListContent = DAOContentCard.createObjectAdvList(userCase, employeeClass, employeeCase);
        DSLContent.add(objectListContent);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(userCase, objectListContent);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);

        String jsContent2 = String.format("jsApi.commands.filterForm(%s)", descriptorAsJson); // старая сигнатура
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent2);

        DSLEmbeddedApplication.add(application, application2);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userCase, application);
        ContentForm applicationContent2 = DAOContentAddForm.createEmbeddedApplication(userCase2, application2);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);

        GUIBo.selectCase(userCase);
        GUIForm.assertDialogCaption("Настройка условий фильтрации");
        GUIForm.clickCancelTopmostDialog();

        GUIBo.selectCase(userCase2);
        GUIForm.assertDialogCaption("Настройка условий фильтрации");
    }

    /**
     * Тестирование открытия формы фильтрации с деревом выбора атрибутов из встроенного приложения с применением
     * ограничений на все уровни списка атрибутов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$136691057
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать группу атрибутов <code>ouAttrGroup</code> в классе <code>ouClass</code> с атрибутами
     *   "Название" и "Родитель"</li>
     *   <li>Сгенерировать случайное название <code>expectedTitle</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *     <li>Фильтрация ро умолчанию:
     *     <ul>
     *         <li>Цепочка атрибутов: <code>employeeOuLinkAttr</code> -> "Название";</li>
     *         <li>Условие фильтрации: "содержит";</li>
     *         <li>Значение: <code>expectedTitle</code></li>
     *     </ul></li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, который содержит
     *   настроенный фильтр по цепочке атрибутов, и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           'filters': [[{
     *               'string': '$expectedTitle',
     *               'properties': {
     *                   'attributeFqn': 'employee@$employeeOuLinkAttr.ou@title',
     *                   ...
     *               }
     *           }}],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $expectedTitle - сгенерированное название,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               'restriction': {
     *                   'employee': [ '$employeeOuLinkAttr', '$employeeAgreementsAttr' ],
     *                   'ou': '$ouAttrGroup'
     *               }
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>,
     *       3) $employeeAgreementsAttr - код атрибута <code>employeeAgreementsAttr</code>,
     *       4) $ouAttrGroup - код группы атрибутов <code>ouAttrGroup</code>
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Выполнение действий</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <br>
     *   <b>Проверки</b>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Проверить, что на форме отображается настроенный фильтр <code>employeeOuLinkAttr</code> -> "Название" и
     *   имеет значение <code>expectedTitle</code></li>
     *   <li>Проверить, что в дереве выбора атрибута присутствует пункт "[не указано]"</li>
     *   <li>Проверить, что в дереве выбора атрибута на верхнем уровне присутствуют только атрибуты указанные в
     *   ограничениях для класса Сотрудник: атрибут «Соглашения» без возможности развернуть поддерево и
     *   атрибут <code>employeeOuLinkAttr</code> с возможностью развернуть поддерево</li>
     * </ol>
     */
    @Test
    void testFilterFormWithAttributeTreeCommand()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        Attribute employeeAgreementsAttr = SysAttribute.recipientAgreements(employeeClass);
        Attribute employeeIsLockedAttr = SysAttribute.isEmployeeLocked(employeeClass);

        GroupAttr ouAttrGroup = DAOGroupAttr.create(ouClass);
        Attribute ouTitleAttr = SysAttribute.title(ouClass);
        DSLGroupAttr.add(ouAttrGroup, ouTitleAttr, SysAttribute.parent(ouClass));

        List<Attribute> filterAttrChain = List.of(employeeOuLinkAttr, ouTitleAttr);
        String expectedTitle = ModelUtils.createTitle();
        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        FilterBlockOr blockOr = new FilterBlockOr(filterAttrChain, FilterCondition.CONTAINS, false, expectedTitle);
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent);
        String restriction = buildRestrictionJson(Map.of(
                employeeClass, List.of(employeeOuLinkAttr, employeeAgreementsAttr),
                ouClass, ouAttrGroup
        ));
        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION, descriptorAsJson, restriction);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        // Действия
        GUILogon.login(employee);

        // Проверки
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().inputText(1, 1, expectedTitle);
        filterOnForm.attrTree(1, 1).assertElementLeaf(true, employeeAgreementsAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertElementLeaf(false, employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentEmptyItem();
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeIsLockedAttr.getFqn());
    }

    /**
     * Тестирование открытия формы фильтрации с деревом выбора атрибутов из встроенного приложения с применением
     * ограничений на указанные уровни дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$237811428
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать тип <code>employeeCase2</code> класса "Сотрудник"</li>
     *   <li>Создать группу атрибутов <code>ouAttrGroup</code> в классе <code>ouClass</code> с атрибутами
     *   "Название" и "Родитель"</li>
     *   <li>Добавить контенты <code>objectListContent</code> и <code>objectListContent2</code> типа "Список
     *   объектов" на карточки типов <code>employeeCase</code> и <code>employeeCase2</code> соответственно.
     *   Параметры:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *   </ul>
     *   <li>Создать списочные дескрипторы по списочным контентам <code>objectListContent</code> и
     *   <code>objectListContent2</code>, и конвертировать их в урезанные контексты в формате JSON
     *   <code>descriptorAsJson</code> и <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code> или <code>employeeCase2</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               'restriction': {
     *                   'employee': {
     *                      'top' : ['$employeeOuLinkAttr', '$employeeAuthorAttr']
     *                      'children' : ['$employeeAuthorAttr']
     *                      },
     *                   'ou': {
     *                      'children' : '$ouAttrGroup'
     *                      }
     *               }
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *       3) $employeeAuthorAttr - код атрибута <code>$employeeAuthorAttr</code>
     *       4) $ouAttrGroup - код группы атрибутов <code>ouAttrGroup</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application2</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson2)
     *           .setAttributeTree({
     *               'restriction': {
     *                   'employee': {
     *                      'top' : ['$employeeOuLinkAttr', '$employeeAuthorAttr']
     *                      'badLevelName' : ['$employeeAuthorAttr']
     *                      },
     *                   'ou': {
     *                      'children' : '$ouAttrGroup'
     *                      }
     *               }
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson2 - урезанный списочный дескриптор <code>$descriptorAsJson2</code> в формате JSON,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *       3) $employeeAuthorAttr - код атрибута <code>$employeeAuthorAttr</code>
     *       4) $ouAttrGroup - код группы атрибутов <code>ouAttrGroup</code>
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> или <code>application2</code>
     *   на карточки типов <code>employeeCase</code> и <code>employeeCase2</code> соответственно </li>
     *   <br>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <br>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Проверить, что в дереве выбора атрибута на верхнем уровне присутствуют только атрибуты указанные в
     *   ограничениях для класса Сотрудник: <code>employeeOuLinkAttr</code> и <code>employeeAuthorAttr</code> с
     *   возможностью развернуть поддерево</li>
     *   <li>Проверить, что на всех дочерних уровнях присутствуют атрибуты <code>employeeOuLinkAttr</code> ("Родитель")
     *   и <code>ouTitleAttr</code> ("Название")</li>
     *   <li>Проверить что на всех дочерних уровнях атрибута <code>employeeAuthorAttr</code> (класс "Сотрудник")
     *   присутствует ссылочный атрибут <code>employeeAuthorAttr</code> (Автор)</li>
     *   <li>Перейти на карточку объекта <code>employeeBo</code></li>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Проверить, что в дереве выбора атрибута на верхнем уровне присутствуют только атрибуты указанные в
     *   ограничениях для класса Сотрудник: <code>employeeOuLinkAttr</code> с возможностью развернуть поддерево и
     *   <code>employeeAuthorAttr</code> без возможности развернуть поддерево</li>
     *   <li>Проверить, что на всех дочерних уровнях присутствуют атрибуты <code>employeeOuLinkAttr</code> ("Родитель")
     *   и <code>ouTitleAttr</code> ("Название")</li>
     * </ol>
     */
    @Test
    void testFilterFormWithAttributeTreeCommandWithLevelRestriction()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass employeeCase2 = DAOUserCase.create(employeeClass);
        DSLMetaClass.add(employeeCase2);

        Attribute employeeAuthorAttr = SysAttribute.author(employeeClass);

        GroupAttr ouAttrGroup = DAOGroupAttr.create(ouClass);
        Attribute ouTitleAttr = SysAttribute.title(ouClass);
        Attribute parent = SysAttribute.parent(ouClass);
        DSLGroupAttr.add(ouAttrGroup, ouTitleAttr, parent);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        ContentForm objectListContent2 = DAOContentCard.createObjectAdvList(employeeCase2, employeeClass, employeeCase);
        DSLContent.add(objectListContent, objectListContent2);

        Bo employeeBo = DAOEmployee.create(employeeCase2, ou, false);
        DSLBo.add(employeeBo);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent);
        String descriptorAsJson2 = DSLListDataApi.createListDescriptorJson(employeeCase2, objectListContent2);

        String restriction = buildRestrictionJson(Map.of(
                employeeClass, Map.of(
                        "top", List.of(employeeOuLinkAttr, employeeAuthorAttr),
                        "children", List.of(employeeAuthorAttr)),
                ouClass, Map.of("children", ouAttrGroup)));
        String badLevelRestriction = buildRestrictionJson(Map.of(
                employeeClass, Map.of("top", List.of(employeeOuLinkAttr, employeeAuthorAttr),
                        "badLevelName", List.of(employeeAuthorAttr)),
                ouClass, Map.of("children", ouAttrGroup)));
        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION, descriptorAsJson, restriction);
        String badLevelRestrictionJsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION,
                descriptorAsJson2, badLevelRestriction);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                badLevelRestrictionJsContent);

        DSLEmbeddedApplication.add(application, application2);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        ContentForm applicationContent2 = DAOContentCard.createEmbeddedApplication(employeeCase2, application2);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия и проверки
        GUILogon.login(employee);

        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.attrTree(1, 1).assertPresentElement(true, employeeAuthorAttr.getFqn(),
                employeeAuthorAttr.getFqn() + "." + employeeAuthorAttr.getFqn(),
                employeeAuthorAttr.getFqn() + "." + employeeAuthorAttr.getFqn() + "." + employeeAuthorAttr.getFqn(),
                employeeAuthorAttr.getFqn() + "." + employeeAuthorAttr.getFqn() + "." + employeeAuthorAttr.getFqn() +
                "." + employeeAuthorAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(true, employeeOuLinkAttr.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn() + "." + parent.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn() + "." + parent.getFqn() + "."
                + ouTitleAttr.getFqn());
        GUIForm.cancelForm();

        GUIBo.goToCard(employeeBo);

        filterOnForm.attrTree(1, 1).assertElementLeaf(true, employeeAuthorAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(true, employeeOuLinkAttr.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn() + "." + parent.getFqn(),
                employeeOuLinkAttr.getFqn() + "." + parent.getFqn() + "." + parent.getFqn() + "."
                + ouTitleAttr.getFqn());
    }

    /**
     * Тестирование открытия формы фильтрации с деревом выбора атрибутов из встроенного приложения,
     * с применением ограничений на верхний уровень списка атрибутов, заданных в виде списка доступных атрибутов или
     * группы атрибутов внутри дескриптора.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$184682052
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать группу атрибутов <code>employeeAttrGroup</code> в классе <code>employeeCase</code> с атрибутом
     *   <code>employeeOuLinkAttr</code></li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Сгенерировать случайное название <code>expectedTitle</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *     <li>Фильтрация ро умолчанию:
     *     <ul>
     *         <li>Цепочка атрибутов: <code>employeeOuLinkAttr</code> -> "Название";</li>
     *         <li>Условие фильтрации: "содержит";</li>
     *         <li>Значение: <code>expectedTitle</code></li>
     *     </ul></li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, который содержит
     *   настроенный фильтр по цепочке атрибутов, и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           'filters': [[{
     *               'string': '$expectedTitle',
     *               'properties': {
     *                   'attributeFqn': 'employee@$employeeOuLinkAttr.ou@title',
     *                   ...
     *               }
     *           }}],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $expectedTitle - сгенерированное название,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               useContextRestriction: true
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, который содержит
     *   настроенный фильтр по цепочке атрибутов и ограничение списка атрибутов группой атрибутов
     *   <code>employeeAttrGroup</code>, и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           'attrGroupCode': '$employeeAttrGroup',
     *           'filters': [[{
     *               'string': '$expectedTitle',
     *               'properties': {
     *                   'attributeFqn': 'employee@$employeeOuLinkAttr.ou@title',
     *                   ...
     *               }
     *           }}],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $employeeAttrGroup - код группы атрибутов <code>employeeAttrGroup</code>,
     *       2) $expectedTitle - сгенерированное название,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               useContextRestriction: true
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson2 - урезанный списочный дескриптор <code>$descriptorAsJson2</code> в формате JSON
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Проверить, что на форме отображается настроенный фильтр <code>employeeOuLinkAttr</code> -> "Название" и
     *   имеет значение <code>expectedTitle</code></li>
     *   <li>Проверить, что в дереве выбора атрибута присутствует пункт "[не указано]"</li>
     *   <li>Проверить, что в дереве выбора атрибута на верхнем уровне присутствуют только атрибуты указанные в
     *   ограничениях для класса Сотрудник: атрибут «Соглашения» без возможности развернуть поддерево и
     *   атрибут <code>employeeOuLinkAttr</code> с возможностью развернуть поддерево. При развораивании поддерева
     *   доступны атрибуты "Название", "Родитель" и "Металкласс"</li>
     *   <li>Перейти на карточку объекта <code>userBo</code></li>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Повторить проверки доступных для выбора атрибутов</li>
     * </ol>
     */
    @Test
    void testFilterFormWithAttributeTreeCommandWhenUseContextRestriction()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        Attribute employeeAgreementsAttr = SysAttribute.recipientAgreements(employeeClass);
        Attribute employeeIsLockedAttr = SysAttribute.isEmployeeLocked(employeeClass);

        GroupAttr employeeAttrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(employeeAttrGroup, employeeOuLinkAttr);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Attribute ouTitleAttr = SysAttribute.title(ouClass);
        List<Attribute> filterAttrChain = List.of(employeeOuLinkAttr, ouTitleAttr);
        String expectedTitle = ModelUtils.createTitle();
        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        FilterBlockOr blockOr = new FilterBlockOr(filterAttrChain, FilterCondition.CONTAINS, false, expectedTitle);
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String descriptorAsJson =
                DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent, employeeOuLinkAttr);
        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_USE_CONTEXT_RESTRICTION, descriptorAsJson);

        String scriptTemplate = "def sourceDescriptor = " + withCreateListDescriptor(employeeCase, objectListContent)
                                + "def descriptor = api.listdata.defineListDescriptor(sourceDescriptor)\n"
                                + withAttributeGroup(employeeAttrGroup)
                                + "    .create()\n"
                                + "return api.listdata.listDescriptorAsJson(descriptor)";
        String descriptorAsJson2 = ScriptRunner.executeScript(scriptTemplate);
        String jsContent2 = String.format(OPEN_FILTER_FORM_WITH_TREE_USE_CONTEXT_RESTRICTION, descriptorAsJson2);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent2);
        DSLEmbeddedApplication.add(application, application2);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        ContentForm applicationContent2 = DAOContentCard.createEmbeddedApplication(userCase, application2);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия и проверки
        GUILogon.login(employee);

        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        Attribute ouParentAttr = SysAttribute.parent(ouClass);
        Attribute ouMetaClassAttr = SysAttribute.metaClass(ouClass);
        filterOnForm.asserts().inputText(1, 1, expectedTitle);
        filterOnForm.attrTree(1, 1).assertElementLeaf(false, employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentEmptyItem();
        filterOnForm.attrTree(1, 1).openTreeWithNode(employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouTitleAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouParentAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouMetaClassAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeIsLockedAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeAgreementsAttr.getFqn());
        GUIForm.cancelForm();

        GUIBo.goToCard(userBo);

        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().inputText(1, 1, expectedTitle);
        filterOnForm.attrTree(1, 1).assertElementLeaf(false, employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentEmptyItem();
        filterOnForm.attrTree(1, 1).openTreeWithNode(employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouTitleAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouParentAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouMetaClassAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeIsLockedAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeAgreementsAttr.getFqn());
    }

    /**
     * Тестирование открытия формы фильтрации с деревом выбора атрибутов из встроенного приложения,
     * с применением:
     * <li>ограничений на верхний уровень списка атрибутов, заданных в виде списка доступных атрибутов или группы
     * атрибутов внутри дескриптора;</li>
     * <li>ограничений на все уровни списка атрибутов.</li>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$184682052
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать группу атрибутов <code>ouAttrGroup</code> в классе <code>ouClass</code> с атрибутами
     *   "Название" и "Родитель"</li>
     *   <li>Создать группу атрибутов <code>employeeAttrGroup</code> в классе <code>employeeCase</code> с атрибутом
     *   <code>employeeOuLinkAttr</code></li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Сгенерировать случайное название <code>expectedTitle</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список";</li>
     *     <li>Фильтрация ро умолчанию:
     *     <ul>
     *         <li>Цепочка атрибутов: <code>employeeOuLinkAttr</code> -> "Название";</li>
     *         <li>Условие фильтрации: "содержит";</li>
     *         <li>Значение: <code>expectedTitle</code></li>
     *     </ul></li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, который содержит
     *   настроенный фильтр по цепочке атрибутов, и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           'filters': [[{
     *               'string': '$expectedTitle',
     *               'properties': {
     *                   'attributeFqn': 'employee@$employeeOuLinkAttr.ou@title',
     *                   ...
     *               }
     *           }}],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $expectedTitle - сгенерированное название,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               useContextRestriction: true,
     *               restriction: {
     *                   'employee': [ '$employeeOuLinkAttr', '$employeeAgreementsAttr' ],
     *                   'ou': '$ouAttrGroup'
     *               }
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>,
     *       3) $employeeAgreementsAttr - код атрибута <code>employeeAgreementsAttr</code>,
     *       4) $ouAttrGroup - код группы атрибутов <code>ouAttrGroup</code>
     *   </pre></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, который содержит
     *   настроенный фильтр по цепочке атрибутов и ограничение списка атрибутов группой атрибутов
     *   <code>employeeAttrGroup</code>, и конвертировать дескриптор в урезанный контекст в формате JSON
     *   <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       {
     *           'cases': ['$employeeCase'],
     *           'attrGroupCode': '$employeeAttrGroup',
     *           'filters': [[{
     *               'string': '$expectedTitle',
     *               'properties': {
     *                   'attributeFqn': 'employee@$employeeOuLinkAttr.ou@title',
     *                   ...
     *               }
     *           }}],
     *           ...
     *       }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $employeeAttrGroup - код группы атрибутов <code>employeeAttrGroup</code>,
     *       2) $expectedTitle - сгенерированное название,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson2</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *           .setAttributeTree({
     *               useContextRestriction: true,
     *               restriction: {
     *                   'employee': [ '$employeeOuLinkAttr', '$employeeAgreementsAttr' ],
     *                   'ou': '$ouAttrGroup'
     *               }
     *           })
     *           .openForm()
     *           .then((result) => {
     *              document.body.innerHTML = JSON.stringify(result)
     *           })
     *   ----------------------------------------------------------
     *     Где:
     *       1) $descriptorAsJson2 - урезанный списочный дескриптор <code>$descriptorAsJson2</code> в формате JSON,
     *       2) $employeeOuLinkAttr - код атрибута <code>employeeOuLinkAttr</code>,
     *       3) $employeeAgreementsAttr - код атрибута <code>employeeAgreementsAttr</code>,
     *       4) $ouAttrGroup - код группы атрибутов <code>ouAttrGroup</code>
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Проверить, что на форме отображается настроенный фильтр <code>employeeOuLinkAttr</code> -> "Название" и
     *   имеет значение <code>expectedTitle</code></li>
     *   <li>Проверить, что в дереве выбора атрибута присутствует пункт "[не указано]"</li>
     *   <li>Проверить, что в дереве выбора атрибута на верхнем уровне присутствуют только атрибуты указанные в
     *   ограничениях для класса Сотрудник: атрибут «Соглашения» без возможности развернуть поддерево и
     *   атрибут <code>employeeOuLinkAttr</code> с возможностью развернуть поддерево. При развораивании поддерева
     *   доступны атрибуты "Название" и "Родитель", но из-за ограничений не доступен "Металкласс"</li>
     *   <li>Перейти на карточку объекта <code>userBo</code></li>
     *   <li>Проверить, что открылась модальная форма фильтрации</li>
     *   <li>Повторить проверки доступных для выбора атрибутов</li>
     * </ol>
     */
    @Test
    void testFilterFormWithAttributeTreeCommandWhenExistsRestrictionAndUseContextRestriction()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        Attribute employeeAgreementsAttr = SysAttribute.recipientAgreements(employeeClass);
        Attribute employeeIsLockedAttr = SysAttribute.isEmployeeLocked(employeeClass);

        GroupAttr ouAttrGroup = DAOGroupAttr.create(ouClass);
        Attribute ouTitleAttr = SysAttribute.title(ouClass);
        Attribute ouParentAttr = SysAttribute.parent(ouClass);
        Attribute ouMetaClassAttr = SysAttribute.metaClass(ouClass);
        DSLGroupAttr.add(ouAttrGroup, ouTitleAttr, ouParentAttr);

        GroupAttr employeeAttrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(employeeAttrGroup, employeeOuLinkAttr);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        List<Attribute> filterAttrChain = List.of(employeeOuLinkAttr, ouTitleAttr);
        String expectedTitle = ModelUtils.createTitle();
        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        FilterBlockOr blockOr = new FilterBlockOr(filterAttrChain, FilterCondition.CONTAINS, false, expectedTitle);
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String descriptorAsJson =
                DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent, employeeOuLinkAttr);
        String restriction = buildRestrictionJson(Map.of(
                employeeClass, List.of(employeeOuLinkAttr, employeeAgreementsAttr),
                ouClass, ouAttrGroup
        ));
        String jsContent = String.format(
                OPEN_FILTER_FORM_WITH_TREE_RESTRICTION_AND_USE_CONTEXT_RESTRICTION, descriptorAsJson, restriction);

        String scriptTemplate = "def sourceDescriptor = " + withCreateListDescriptor(employeeCase, objectListContent)
                                + "def descriptor = api.listdata.defineListDescriptor(sourceDescriptor)\n"
                                + withAttributeGroup(employeeAttrGroup)
                                + "    .create()\n"
                                + "return api.listdata.listDescriptorAsJson(descriptor)";
        String descriptorAsJson2 = ScriptRunner.executeScript(scriptTemplate);
        String jsContent2 = String.format(
                OPEN_FILTER_FORM_WITH_TREE_RESTRICTION_AND_USE_CONTEXT_RESTRICTION, descriptorAsJson2, restriction);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent2);
        DSLEmbeddedApplication.add(application, application2);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        ContentForm applicationContent2 = DAOContentCard.createEmbeddedApplication(userCase, application2);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия и проверки
        GUILogon.login(employee);

        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().inputText(1, 1, expectedTitle);
        filterOnForm.attrTree(1, 1).assertElementLeaf(false, employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentEmptyItem();
        filterOnForm.attrTree(1, 1).openTreeWithNode(employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouTitleAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouParentAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(false,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouMetaClassAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeIsLockedAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeAgreementsAttr.getFqn());
        GUIForm.cancelForm();

        GUIBo.goToCard(userBo);

        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.asserts().inputText(1, 1, expectedTitle);
        filterOnForm.attrTree(1, 1).assertElementLeaf(false, employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentEmptyItem();
        filterOnForm.attrTree(1, 1).openTreeWithNode(employeeOuLinkAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouTitleAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(true,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouParentAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElementWithoutOpen(false,
                employeeOuLinkAttr.getFqn(), employeeOuLinkAttr.getFqn() + '.' + ouMetaClassAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeIsLockedAttr.getFqn());
        filterOnForm.attrTree(1, 1).assertPresentElement(false, employeeAgreementsAttr.getFqn());
    }

    /**
     * Тестирование фильтрации по атрибуту для которого настроено ограничение фильтрации при открытии формы
     * фильтрации из встроенного приложения
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00719
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$152638821
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Создать атрибут <code>objectAttr2</code> типа "Набор ссылок на БО" в типе <code>employeeCase</code>,
     *   ссылающийся на класс Сотрудник и добавить его в системную группу атрибутов</li>
     *   <li>Заполнить атрибут <code>objectAttr2</code> объекта <code>employee</code> значением employee</li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul></li>
     *   <li>Добавить для контента <code>objectListContent</code> ограничения при фильтрации для атрибутов:
     *   <ul>
     *     <li>Контент: <code>objectListContent</code>;</li>
     *     <li>Атрибут: <code>objectAttr2</code>;</li>
     *     <li>Стратегия ограничения: "Ограничение по содержимому в списке"</li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code> и конвертировать
     *   дескриптор в урезанный контекст в формате JSON <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'cases': ['$userCase'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userCase - FQN типа <code>userCase</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *     -------------------------------------------------------------------
     *         jsApi.forms.getFilterFormBuilder($descriptorAsJson).openForm()
     *     -------------------------------------------------------------------
     *       Где:
     *         1) $descriptorAsJson - дескриптор сформированный для контента <code>descriptorAsJson</code>
     *   </pre>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>На открывшейся форме фильтрации выбрать атрибут <code>objectAttr2</code> и условие фильтрации
     *   "содержит"</li>
     *   <li>Проверить, что в дереве возможных значений фильтрации присутствует объект <code>employee</code> и
     *   отсутствует <code>employee2</code></li>
     * </ol>
     */
    @Test
    void testFilterFormWithFilterRestrictionSettings()
    {
        // Подготовка
        Attribute objectAttr2 = DAOAttribute.createObjectLink(employeeCase, employeeClass, null);
        DSLAttribute.add(objectAttr2);
        DSLGroupAttr.addToGroup(DAOGroupAttr.createSystem(employeeCase), objectAttr2);

        objectAttr2.setValue(employee.getUuid());
        DSLBo.editAttributeValue(employee, objectAttr2);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        DSLContent.add(objectListContent);
        DSLFilterRestrictionSettings.addFilterRestrictionSettings(
                objectListContent, objectAttr2, FilterRestrictionStrategy.LIST, null);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        filterOnForm.addAttr(objectAttr2, 1, 1, FilterCondition.CONTAINS);
        filterOnForm.asserts().treeValuePresent(1, 1, ou, employee);
        filterOnForm.asserts().treeValueAbsent(1, 1, ou, employee2);
    }

    /**
     * Тестирование корректной работы атрибутов типов "Ссылка на БО", "Элемент
     * справочника", "Набор элементов справочника", "Набор типов класса" на форме фильтрации, открытой из встроенного
     * приложения. Также тестируется отсутствие ошибок при выборе атрибутов данных типов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Dashboard
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$113902986
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114845920
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Создать атрибут <code>catalogItemAttr</code> типа "Элемент справочника" в типе <code>employeeCase</code>
     *   <li>Создать атрибут <code>catalogItemSetAttr</code> типа "Набор элементов справочника" в типе
     *   <code>employeeCase</code>
     *   <li>Создать атрибут <code>caseListAttr</code> типа "Набор типов класса" в типе <code>employeeCase</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code> и конвертировать
     *   дескриптор в урезанный контекст в формате JSON <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *       { 'cases': ['$employeeCase'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *     -------------------------------------------------------------------
     *         jsApi.forms.getFilterFormBuilder($descriptorAsJson).openForm()
     *     -------------------------------------------------------------------
     *       Где:
     *       1) $descriptorAsJson - урезанный списочный дескриптор <code>$descriptorAsJson</code> в формате JSON
     *   </pre>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти в систему под сотрудником</li>
     *   <li>Проверить, что открылась форма фильтрации</li>
     *   <li>На форме выбрать атрибут <code>employeeOuLinkAttr</code></li>
     *   <li>Проверить отсутствие ошибок</li>
     *   <li>В условии фильтрации выбрать условие "Содержит"</li>
     *   <li>Проверить, что для выбора значения атрибута доступно "[не указано]"</li>
     *   <li>На форме выбрать атрибут <code>catalogItemAttr</code></li>
     *   <li>Проверить отсутствие ошибок</li>
     *   <li>В условии фильтрации выбрать условие "Содержит"</li>
     *   <li>Проверить, что для выбора значения атрибута доступно "[не указано]"</li>
     *   <li>На форме выбрать атрибут <code>catalogItemSetAttr</code></li>
     *   <li>Проверить отсутствие ошибок</li>
     *   <li>В условии фильтрации выбрать условие "Содержит"</li>
     *   <li>На форме выбрать атрибут <code>caseListAttr</code></li>
     *   <li>Проверить отсутствие ошибок</li>
     *   <li>В условии фильтрации выбрать условие "Содержит, включая вложенный"</li>
     *   <li>Проверить, что для выбора значения атрибута доступно значение типа <code>userCase</code></li>
     * </ol>
     */
    @Test
    void testFilterFormWithPermissionsTokens()
    {
        // Подготовка
        Catalog catalog = DAOCatalog.createSystem(SystemCatalog.CLOSURECODE);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(employeeCase, catalog, null);
        Attribute catalogItemSetAttr = DAOAttribute.createCatalogItemSet(employeeCase, catalog);
        Attribute caseListAttr = DAOAttribute.createCaseList(employeeCase, userClass);
        DSLAttribute.add(catalogItemAttr, catalogItemSetAttr, caseListAttr);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        DSLContent.add(objectListContent);

        String descriptorAsJson =
                DSLListDataApi.createListDescriptorJson(employeeCase, objectListContent);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.login(employee);
        GUIForm.assertDialogAppear("Форма настройки фильтрации не появилась.");

        filterOnForm.addAttr(employeeOuLinkAttr, 1, 1);
        GUIError.assertErrorAbsence();
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS);
        filterOnForm.asserts().inputText(1, 1, GUISelect.EMPTY_VALUE);

        filterOnForm.addAttr(catalogItemAttr, 1, 1);
        GUIError.assertErrorAbsence();
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS);
        filterOnForm.asserts().selectValue(false, false, GUISelect.EMPTY_VALUE);

        filterOnForm.addAttr(catalogItemSetAttr, 1, 1);
        GUIError.assertErrorAbsence();
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS);

        filterOnForm.addAttr(caseListAttr, 1, 1);
        GUIError.assertErrorAbsence();
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS_WITH_NESTED);
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase.getFqn());
    }

    /**
     * Тестирование передачи дескриптора в интерфейс и изменения видимой пользователю части фильтрации с помощью
     * формы настройки фильтрации, в результате редактирования невидимая часть должна остаться не тронутой.
     * Результирующий список полученный по такому дескриптору списка должен учитывать как видимую, так и не видимую
     * часть фильтрации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059123
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$146063534
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая часть подготовки}</li>
     *   <li>Создать атрибут <code>objectAttr2</code> типа "Ссылка на БО" в типе <code>employeeCase</code> на тип
     *   <code>employeeCase</code></li>
     *   <li>Заполнить атрибут <code>objectAttr2</code> в объектах <code>employee</code> и <code>employee2</code>
     *   значением employee</li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку типа
     *   <code>employeeCase</code> c параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>employeeClass</code>;</li>
     *     <li>Типы объектов: <code>employeeCase</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul></li>
     *   <li>Выполнить скрипт, который извлекает списочный дескриптор из <code>objectListContent</code>
     *   и конвертирует его в JSON <code>descriptorAsJson</code> делая сложные фильтры не видимыми на форме:
     *   <pre>
     *   ----------------------------------------------------------
     *       def descriptor = api.listdata.createListDescriptor('$employeeCase', '$objectListContent')
     *       def definition = api.listdata.defineDescriptorAsJson()
     *           .filters()
     *               .complex().hide()
     *       return api.listdata.listDescriptorAsJson(descriptor, definition)
     *   ----------------------------------------------------------
     *     Где:
     *       1) $employeeCase - FQN типа <code>employeeCase</code>,
     *       2) $objectListContent - код контента <code>objectListContent</code> на карточке employeeCase
     *   </pre></li>
     *   <li>Добавить скриптовый модуль <code>scriptModule</code> с исходным кодом:
     *   <pre>
     *   ----------------------------------------------------------
     *       def getList(request) {
     *           def descriptorString = request.getReader().lines().collect(Collectors.joining());
     *           def descriptor = api.listdata.createListDescriptor(descriptorString)
     *           return JsonOutput.toJson(api.db.query(api.listdata.createCriteria(descriptor)).list().UUID)
     *       }
     *   ----------------------------------------------------------
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code> с исходным кодом:
     *   <pre>
     *   ----------------------------------------------------------
     *   jsApi.forms.getFilterFormBuilder($serializedContext).openForm()
     *       .then(result => JSON.parse(result.serializedContext))
     *       .then(descriptor => jsApi.forms.getFilterFormBuilder(descriptor).openForm())
     *       .then(result => jsApi.restCallAsJson(
     *           'exec-post?func=modules.$moduleCode.getList&params=request&raw=true', {
     *               method: 'POST',
     *               body: result.serializedContext
     *           }))
     *       .then(result => document.getElementById('test_div').innerText = result)
     *   ----------------------------------------------------------
     *     Где:
     *       1) $serializedContext - списочный дескриптор контента <code>objectListContent</code> в формате JSON
     *       2) $moduleCode - код модуля <code>scriptModule</code>
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку типа
     *   <code>employeeCase</code></li>
     *   <b>Действия и проверки.</b>
     *   <li>Войти в систему под пользователем <code>employee</code></li>
     *   <li>Проверить, что в открывшейся форме фильтрации нет условия фильтрации по подстроке, потому что этот
     *   фильтр сделан невидимым</li>
     *   <li>Задать ограничение фильтрации: атрибут <code>objectAttr2</code>, условие "содержит", значение employee</li>
     *   <li>Сохранить форму</li>
     *   <li>Открыть форму с полученным сериализованным дескриптором</li>
     *   <li>Проверить наличие фильтрации: атрибут <code>objectAttr2</code>, условие "содержит любое из значений"</li>
     *   <li>Проверить отсутствие фильтрации: атрибут <code>objectAttr</code>, условие "содержит"</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что внутри встроенного приложения <code>application</code> появился только идентификатор
     *   <code>employee</code>, который удовлетворяет условиям и видимого и не видимого фильтров</li>
     * </ol>
     */
    @Test
    void testEditVisibleFiltersThroughFilterFormWhenDefinedHiddenFiltration()
    {
        //Подготовка
        Attribute objectAttr2 = DAOAttribute.createObjectLink(employeeCase, employeeCase, null);
        objectAttr2.setValue(employee.getUuid());
        DSLAttribute.add(objectAttr2);

        DSLBo.editAttributeValue(employee, objectAttr2);
        DSLBo.editAttributeValue(employee2, objectAttr2);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(employeeCase, employeeClass, employeeCase);
        FilterBlockOr blockOr = new FilterBlockOr(employeeOuLinkAttr, FilterCondition.CONTAINS, false, ou.getUuid());
        objectListContent.setDefaultListFilter(new ListFilter(new FilterBlockAnd(blockOr)));
        DSLContent.add(objectListContent);

        String scriptTemplate = "def descriptor = api.listdata.createListDescriptor('%s', '%s')\n"
                                + "def definition = api.listdata.defineDescriptorAsJson()\n"
                                + "    .filters()\n"
                                + "        .complex().hide()\n"
                                + "return api.listdata.listDescriptorAsJson(descriptor, definition)";
        String descriptorAsJson = ScriptRunner.executeScript(String.format(scriptTemplate, employeeCase.getFqn(),
                objectListContent.getCode()));

        String moduleCode = "testOpenFilterFormWithHiddenFiltration";
        String moduleTemplate = "import java.util.stream.Collectors\n"
                                + "import groovy.json.JsonOutput\n"
                                + "\n"
                                + "def getList(request) {\n"
                                + "    def descriptorString = request.getReader().lines().collect(Collectors.joining"
                                + "());\n"
                                + "    def descriptor = api.listdata.createListDescriptor(descriptorString)\n"
                                + "    return JsonOutput.toJson(api.db.query(api.listdata.createCriteria(descriptor))"
                                + ".list().UUID)\n"
                                + "}";
        ModuleConf scriptModule = DAOModuleConf.create(moduleCode, null,
                String.format(moduleTemplate, employeeCase.getFqn(), objectListContent.getCode()));

        String jsContent = String.format("jsApi.forms.getFilterFormBuilder(%s).openForm()\n"
                                         + "    .then(result => JSON.parse(result.serializedContext))\n"
                                         + "    .then(descriptor => jsApi.forms.getFilterFormBuilder(descriptor)"
                                         + ".openForm())\n"
                                         + "    .then(result => jsApi.restCallAsJson(\n"
                                         + "        'exec-post?func=modules.%s.getList&params=request&raw=true', {\n"
                                         + "            method: 'POST',\n"
                                         + "            body: result.serializedContext\n"
                                         + "        }))\n"
                                         + "    .then(result => document.getElementById('%s').innerText = result)",
                descriptorAsJson, moduleCode, GUIEmbeddedApplication.TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent, scriptModule);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        //Действия и проверки
        GUILogon.login(employee);
        filterOnForm.asserts().conditionsAbsence(FilterCondition.CONTAINS);

        filterOnForm.addAttr(objectAttr2, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS_IN_SET);
        filterOnForm.setBoTree(1, 1, ou, employee);
        GUIForm.clickApplyTopmostDialog();

        filterOnForm.asserts().assertFilterBlockCount(1, 1);
        filterOnForm.asserts().conditonsPresence(FilterCondition.CONTAINS_IN_SET);
        filterOnForm.asserts().currentAttr(objectAttr2);
        GUIForm.applyModalForm();

        String result = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        Assertions.assertEquals(employee.getUuid(), result);
    }

    /**
     * Тестирование открытия поля выбора атрибутов связи при открытии окна фильтрации через js API
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$168606098
     * <br>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Создать пользовательский класс <code>userClass2</code> с типами: <code>userCase2</code> и
     *   <code>userCase2Empty</code></li>
     *   <li>Создать пользовательский класс <code>userClass3</code> c типом <code>userCase3</code></li>
     *   <li>Создать атрибут <code>objectLink12Attr</code> типа "Ссылка на БО" в типе <code>userClass</code> на тип
     *   <code>userCase2</code></li>
     *   <li>Создать атрибут <code>objectLink23Attr</code> типа "Ссылка на БО" в типе <code>userClass2</code> на тип
     *   <code>userClass3</code></li>
     *   <li>Добавить в группу <code>userClass1AttrGroup</code> атрибут <code>objectLink12Attr</code>, а в группу
     *   <code>userClass2AttrGroup</code> атрибут <code>objectLink23Attr</code></li>
     *   <li>Добавить контент <code>objectListContent</code> типа "Список объектов" на карточку класса Компания c
     *   параметрами:
     *   <ul>
     *     <li>Класс объектов списка: <code>userClass</code>;</li>
     *     <li>Представление: "Сложный список"</li>
     *   </ul></li>
     *   <li>Создать списочный дескриптор по списочному контенту <code>objectListContent</code>, ограничить список
     *   выводимых атрибутов атрибутом <code>objectLink12Attr</code>, и конвертировать дескриптор в урезанный контекст в
     *   формате JSON <code>descriptorAsJson</code>:
     *   <pre>
     *   ----------------------------------------------------------
     *     { 'clazz': '$userClass', 'attrCodes':['$objectLink12Attr'],... }
     *   ----------------------------------------------------------
     *     Где:
     *       1) $userClass - FQN класса <code>userClass</code>,
     *       2) $objectLink12Attr - FQN атрибута <code>objectLink12Attr</code>,
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации по дескриптору
     *   <code>descriptorAsJson</code>:
     *   <pre>
     *     ----------------------------------------------------------
     *         jsApi.forms.getFilterFormBuilder($descriptorAsJson)
     *             .setAttributeTree({
     *                 restriction: {
     *                     '$userClass': '$userClass1AttrGroup',
     *                     '$userClass2': '$userClass2AttrGroup',
     *                     '$userClass3': 'system'
     *                 }
     *             })
     *             .openForm()
     *     ----------------------------------------------------------
     *       Где:
     *         1) $descriptorAsJson - дескриптор в формате JSON <code>descriptorAsJson</code>,
     *         2) $userClass - FQN класса <code>userClass</code>,
     *         3) $userClass1AttrGroup - код группы атрибутов <code>userClass1AttrGroup</code>,
     *         4) $userClass2 - FQN класса <code>userClass2</code>,
     *         5) $userClass2AttrGroup - код группы атрибутов <code>userClass2AttrGroup</code>,
     *         6) $userClass3 - FQN класса <code>userClass3</code>
     *   </pre></li>
     *   <li>Добавить контент типа "Встроенное приложение" c ВП <code>application</code> на карточку Компании</li>
     *   <br>
     *   <li>Войти в систему под пользователем <code>employee</code></li>
     *   <li>Перейти на карточку компании</li>
     *   <li>На открывшейся форме фильтрации проверить, что атрибут "objectLink12 -> objectLink23 -> Название" можно
     *   выбрать</li>
     * </ol>
     */
    @Test
    void testCommunicationAttributesFromFilteringWindowViaJsAPI()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();
        MetaClass userClass2 = DAOUserClass.create();
        MetaClass userCase2 = DAOUserCase.create(userClass2);
        MetaClass userCase2Empty = DAOUserCase.create(userClass2);
        MetaClass userClass3 = DAOUserClass.create();
        MetaClass userCase3 = DAOUserCase.create(userClass3);
        DSLMetaClass.add(userClass2, userCase2, userCase2Empty, userClass3, userCase3);

        Attribute objectLink12Attr = DAOAttribute.createObjectLink(userClass, userClass2, userCase2, null);
        Attribute objectLink23Attr = DAOAttribute.createObjectLink(userClass2, userClass3, null);
        DSLAttribute.add(objectLink12Attr, objectLink23Attr);

        GroupAttr userClass1AttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(userClass1AttrGroup, objectLink12Attr);
        GroupAttr userClass2AttrGroup = DAOGroupAttr.create(userClass2);
        DSLGroupAttr.add(userClass2AttrGroup, objectLink23Attr);

        ContentForm objectListContent = DAOContentCard.createObjectAdvList(rootClass, userClass);
        DSLContent.add(objectListContent);

        String descriptorAsJson =
                DSLListDataApi.createListDescriptorJson(rootClass, objectListContent, objectLink12Attr);
        String restriction = buildRestrictionJson(Map.of(
                userClass, userClass1AttrGroup,
                userCase2, userClass2AttrGroup,
                userClass3, DAOGroupAttr.createSystem()
        ));

        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION, descriptorAsJson, restriction);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(rootClass, application);
        DSLContent.add(applicationContent);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        filterOnForm.addAttrFromTree(1, 1, objectLink12Attr, objectLink23Attr, SysAttribute.title(userClass3));
    }

    /**
     * Тестирование отображения значения в настройке фильтрации по атрибуту "Тип объекта" с критериями "Содержит
     * (включая вложенные)", если фильтрация была вызвана из встроенного приложения"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$196740624
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и его типы userCase1, userCase2</li>
     * <li>Добавить встроенное приложение app1 с исходным кодом:
     * <pre>
     * jsApi.commands.filterForm({ 'attrCodes': ['userEntity@metaClass'],
     *     'attrGroupCode': 'system', 'clazz': 'userClass' }, true).then(function (objectJSON) {
     *         document.body.innerHTML = JSON.stringify(objectJSON)
     *     })
     * </pre></li>
     * <li>Добавить встроенное приложение app2 с исходным кодом:
     * <pre>
     * jsApi.commands.filterForm({ 'attrCodes': ['userEntity@metaClass'],
     *     'attrGroupCode': 'system', 'cases': ['userCase1'] }, true).then(function (objectJSON) {
     *         document.body.innerHTML = JSON.stringify(objectJSON)
     *     })
     * </pre></li>
     * <li>Добавить контент content1 типа "Встроенное приложение" на карточку типа userCase1 (Приложение - app1)</li>
     * <li>Добавить контент content2 типа "Встроенное приложение" на карточку типа userCase2 (Приложение - app2)</li>
     * <li>Создать объект userBo1 типа userCase1</li>
     * <li>Создать объект userBo2 типа userCase2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в ИО под пользователем</li>
     * <li>Перейти на карточку объекта userBo1</li>
     * <li>На открывшейся форме фильтрации выбрать: атрибут - "Тип объекта", условие фильтрации - "содержит (включая
     * вложенные)"</li>
     * <li>Проверить, что в дереве возможных значений фильтрации присутствуют типы userCase1 и userCase2</li>
     * <li>Перейти на карточку объекта userBo2</li>
     * <li>На открывшейся форме фильтрации выбрать: атрибут - "Тип объекта", условие фильтрации - "содержит (включая
     * вложенные)"</li>
     * <li>Проверить, что в дереве возможных значений фильтрации присутствует только тип userCase1</li>
     * </ol>
     */
    @Test
    void testMetaClassFilteringFormEmbeddedApplication()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase1, userCase2);

        String application1Js = """
                jsApi.commands.filterForm({ 'attrCodes': ['userEntity@metaClass'], 'attrGroupCode': 'system', 'clazz': '%s' }, true)
                .then(function (objectJSON) {
                    document.body.innerHTML = JSON.stringify(objectJSON)
                })
                """.formatted(userClass.getFqn());
        EmbeddedApplication app1 = DAOEmbeddedApplication.createClientSideApplication(temp,
                application1Js);

        String application2Js = """
                jsApi.commands.filterForm({ 'attrCodes': ['userEntity@metaClass'], 'attrGroupCode': 'system', 'cases': ['%s'] }, true)
                .then(function (objectJSON) {
                    document.body.innerHTML = JSON.stringify(objectJSON)
                })
                """.formatted(userCase1.getFqn());
        EmbeddedApplication app2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                application2Js);

        DSLEmbeddedApplication.add(app1, app2);
        ContentForm content1 = DAOContentCard.createEmbeddedApplication(userCase1.getFqn(), app1);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(userCase2.getFqn(), app2);
        DSLContent.add(content1, content2);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo1);
        GUIAdvListFiltering filterOnForm = new GUIAdvListFiltering("");
        filterOnForm.addAttr(SysAttribute.metaClass(userClass), 1, 1, FilterCondition.CONTAINS_WITH_NESTED);
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase1.getFqn());
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase2.getFqn());
        GUIForm.cancelForm();
        GUIBo.goToCard(userBo2);
        filterOnForm.addAttr(SysAttribute.metaClass(userCase1), 1, 1, FilterCondition.CONTAINS_WITH_NESTED);
        filterOnForm.asserts().treeValuePresent(1, 1, userClass.getFqn() + "." + userCase1.getFqn());
        filterOnForm.asserts().treeValueAbsent(1, 1, userClass.getFqn() + "." + userCase2.getFqn());
    }

    /**
     * Тестирование настройки фильтрации по критерии "равно атрибуту текущего объекта" на модальной форме, вызванной
     * через jsApi с формы добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI#getFilterFormBuilder
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00848
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$245050006
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить встроенное приложение <code>application</code>, открывающее форму фильтрации:
     * <pre>
     *   -------------------------------------------------------------------
     *       jsApi.forms.getFilterFormBuilder({"clazz": "$userClass"}).openForm()
     *   -------------------------------------------------------------------
     *     Где:
     *     $userClass - FQN класса <code>userClass</code>,
     * </pre></li>
     * <li>Вывести встроенное приложение на форму добавления userClass</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления userClass</li>
     * <li>В открывшемся окне настройки фильтрации выбрать атрибут - Автор, критерия - "равно атрибуту текущего
     * объекта"</li>
     * <li>Проверить, что на форме нет ошибок</li>
     * </ol>
     */
    @Test
    void testFilteringWithCriteriaEqualsSubjectAttrFromEmbeddedApplication()
    {
        // Подготовка
        String jsContent = String.format(OPEN_FILTER_FORM, "{\"clazz\": \"" + userClass.getFqn() + "\"}");
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent);

        // Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToFastAddFormByClass(userClass.getFqn());
        Attribute author = SysAttribute.author(userClass);
        GUIAdvListFiltering filterOnForm = new GUIAdvListFiltering("");
        filterOnForm.addAttr(author, 1, 1);
        filterOnForm.selectCondition(1, 1, FilterCondition.EQUALS_SUBJECT_ATTRIBUTE);
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование создания декскриптора списка подчиненных запросов и открытия формы фильтрации с этим дескриптором.
     * https://start.nau.im/pages/viewpage.action?pageId=97288485
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI#extractSubjectUuid
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$251599370
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип запроса scCase</li>
     * <li>У типа scCase получить группу системных атрибутов systemGroup</li>
     * <li>У типа scCase получить модель системного атрибута "Подчиненные запросы" slavesAttr</li>
     * <li>
     *     На карточку объекта типа scCase добавить контент "Список связанных объектов" relatedObjectList
     *     <pre>
     *         Атрибут связи = slavesAttr
     *         Группа атрибутов для отображения = systemGroup
     *     </pre>
     * </li>
     * <li>Создать БО запроса sc с типом scCase</li>
     * <li>Получить дескриптор descriptorAsJson списка "Подчиненные запросы" класса "Запрос"</li>
     * <li>Создать встроенное приложение application с использованием дескриптора descriptorAsJson</li>
     * <li>На карточку объекта типа scCase добавить контент "ВП" applicationContent с приложением application</li>
     * <br>
     * <b>Действия</b>
     * <li>Авторизоваться под сотрудником</li>
     * <li>Перейти на карточку sc</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверить, что открылась диалоговое окно "Настройка условий фильтрации"</li>
     * </ol>
     */
    @Test
    void testCreatingDescriptorOnRelatedObjectListAndOpenFormWithDescription()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase);
        GroupAttr systemGroup = DAOGroupAttr.createSystem(scCase);
        Attribute slavesAttr = SysAttribute.massProblemSlaves(scCase);

        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(scCase.getFqn(), true,
                PositionContent.FULL,
                PresentationContent.DEFAULT, slavesAttr.getFqn(), systemGroup);
        DSLContent.add(relatedObjectList);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        String scriptBody = "def descriptor = " + withCreateListDescriptor(scCase, relatedObjectList, sc)
                            + "return api.listdata.listDescriptorAsJson(descriptor);";
        String descriptorAsJson = ScriptRunner.executeScript(scriptBody);
        String restriction = buildRestrictionJson(Map.of(
                scClass, systemGroup
        ));

        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION, descriptorAsJson, restriction);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(scCase, application);
        DSLContent.add(applicationContent);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIForm.assertDialogCaption("Настройка условий фильтрации");
        GUIForm.clickCancelTopmostDialog();
    }

    /**
     * Тестирование создания декскриптора списка вложенных объектов в случаях, когда класс вложен сам в себя
     * и открытия формы фильтрации с этим дескриптором.
     * https://start.nau.im/pages/viewpage.action?pageId=97288485
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI#extractSubjectUuid
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$251599370
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе ouCase создать атрибут attrHyperlink</li>
     * <li>У типа ouCase получить группу системных атрибутов systemGroup</li>
     * <li>В системную группу атрибутов systemGroup для типа ouCase добавить атрибут attrHyperlink</li>
     * <li>
     *     На карточку компании добавить контент "Список вложенных объектов" contentChildObjectList
     *     <pre>
     *         Класс объектов списка = класс отдела
     *         Типы объектов = ouCase
     *         Группа атрибутов = systemGroup
     *     </pre>
     * </li>
     * <li>Создать БО отдела ou с типом ouCase</li>
     * <li>Получить дескриптор descriptorAsJson списка "Список вложенных объектов" с фильтрацией по группе атрибутов
     * systemGroup</li>
     * <li>Создать встроенное приложение application с использованием дескриптора descriptorAsJson</li>
     * <li>На карточку компании добавить контент "ВП" applicationContent с приложением application</li>
     * <br>
     * <b>Действия</b>
     * <li>Авторизоваться под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что открылась диалоговое окно "Настройка условий фильтрации"</li>
     * <li>Проверить, что в выпадающем фильтре есть атрибут attrHyperlink</li>
     * </ol>
     */
    @Test
    void testCreatingDescriptorOnChildObjectListAndOpenFormWithDescription()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        Attribute attrHyperlink = DAOAttribute.createHyperlink(ouCase.getFqn());
        DSLMetainfo.add(ouCase, attrHyperlink);
        GroupAttr sysAtrGroup = DAOGroupAttr.createSystem(ouCase);
        DSLGroupAttr.edit(sysAtrGroup, new Attribute[] { attrHyperlink }, new Attribute[] {});
        ContentForm contentChildObjectList = DAOContentCard.createChildObjectList(DAORootClass.create().getCode(),
                DAOOuCase.createClass(), ouCase);
        DSLContent.add(contentChildObjectList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        String scriptBody =
                "def descriptor = " + withCreateListDescriptor(DAORootClass.create(), contentChildObjectList, ou)
                + "return api.listdata.listDescriptorAsJson(descriptor);";
        String descriptorAsJson = ScriptRunner.executeScript(scriptBody);
        String restriction = buildRestrictionJson(Map.of(
                ouCase, sysAtrGroup
        ));

        String jsContent = String.format(OPEN_FILTER_FORM_WITH_TREE_RESTRICTION, descriptorAsJson, restriction);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(DAORootClass.create(), application);
        DSLContent.add(applicationContent);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIForm.assertDialogCaption("Настройка условий фильтрации");
        filterOnForm.attrTree(1, 1).assertElementLeaf(true, attrHyperlink.getFqn());
    }

    /**
     * Тестирование фильтрации по типу объекта ссылочного атрибута с критериями "Содержит (включая вложенные)", "Не
     * Содержит/Содержит любое из значений" отображаются типы текущего объекта, если фильтрация была вызвана из
     * встроенного приложения <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$276021945 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить пользовательский класс userClass2 и его типа userClass2Case</li>
     * <li>В класс userClass2 добавить атрибут objectLinkAttribute типа ссылка на объекты класса userClass</li>
     * <li>В класс userClass добавить атрибут backBoLinksAttribute типа обратная ссылка на объекты класса userClass2
     * через атрибут objectLinkAttribute</li>
     * <li>Добавить встроенное приложение applicationWithJs выполняемое на стороне клиента с JavaScript:<pre>
     *      jsApi.forms.getFilterFormBuilder({clazz: 'userClass', attrCodes: ['backBoLinksAttribute']})
     *          .setAttributeTree({useContextRestriction: true, restriction: {}})
     *          .openForm()
     * </pre></li>
     * <li>Добавить контент встроенного приложения applicationWithJs на каточку компании</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться в системе с правами пользователя и открыть карточку компании</li>
     * <li>В открывшемся окне фильтрации:
     * <ul>
     *     <li>Выбрать атрибут backBoLinksAttribute/Тип объекта</li>
     *     <li>Выбрать условие фильтрации "содержит любое из значений"</li>
     * </ul></li>
     * <li>Проверить, что в выпадающем списке доступен для выбора тип userClass2Case</li>
     * <li>В открывшемся окне фильтрации:
     * <ul>
     *     <li>Выбрать атрибут backBoLinksAttribute/Тип объекта</li>
     *     <li>Выбрать условие фильтрации "не содержит любое из значений"</li>
     * </ul></li>
     * <li>Проверить, что в выпадающем списке доступен для выбора тип userClass2Case</li>
     * <li>В открывшемся окне фильтрации:
     * <ul>
     *     <li>Выбрать атрибут backBoLinksAttribute/Тип объекта</li>
     *     <li>Выбрать условие фильтрации "содержит (включая вложенные)"</li>
     * </ul></li>
     * <li>Проверить, что в выпадающем списке доступен для выбора тип userClass2Case</li>
     * </ol>
     */
    @Test
    void testGetFilterFormBuilderForEmployee()
    {
        //Подготовка
        MetaClass userClass2 = DAOUserClass.create();
        MetaClass userClass2Case = DAOUserCase.create(userClass2);
        DSLMetaClass.add(userClass2, userClass2Case);

        Attribute objectLinkAttribute = DAOAttribute.createObjectLink(userClass2, userClass);
        Attribute backBoLinksAttribute = DAOAttribute.createBackBOLinks(userClass, objectLinkAttribute);
        DSLAttribute.add(objectLinkAttribute, backBoLinksAttribute);

        String embeddedApplicationJS = """
                jsApi.forms.getFilterFormBuilder({clazz: '%s', attrCodes: ['%s']})
                    .setAttributeTree({useContextRestriction: true, restriction: {}})
                    .openForm()
                """
                .formatted(userClass.getCode(), backBoLinksAttribute.getFqn());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                embeddedApplicationJS);
        DSLEmbeddedApplication.add(application);
        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(SystemClass.ROOT.getCode(),
                application);
        DSLContent.add(applicationContent);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        filterOnForm.addAttrFromTree(1, 1, backBoLinksAttribute, SysAttribute.metaClass(userClass2));
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS_IN_SET);

        filterOnForm.asserts().treeValuePresent(1, 1, userClass2.getFqn() + "." + userClass2Case.getFqn());

        filterOnForm.addAttrFromTree(1, 1, backBoLinksAttribute, SysAttribute.metaClass(userClass2));
        filterOnForm.selectCondition(1, 1, FilterCondition.NOT_CONTAINS_IN_SET);

        filterOnForm.asserts().treeValuePresent(1, 1, userClass2.getFqn() + "." + userClass2Case.getFqn());

        filterOnForm.addAttrFromTree(1, 1, backBoLinksAttribute, SysAttribute.metaClass(userClass2));
        filterOnForm.selectCondition(1, 1, FilterCondition.CONTAINS_WITH_NESTED);

        filterOnForm.asserts().treeValuePresent(1, 1, userClass2.getFqn() + "." + userClass2Case.getFqn());
    }

    /**
     * Построить JSON с ограничениями списка доступных на форме атрибутов
     *
     * @param restriction ограничения, ключом выступает метакласс, значением - набор атрибутов, группа атрибутов или
     *                    набор разделенных по уровням групп или наборов атрибутов.
     * @return строка в формате JSON с ограничениями
     */
    private static String buildRestrictionJson(Map<MetaClass, Object> restriction)
    {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(restriction.size());
        for (Map.Entry<MetaClass, Object> entry : restriction.entrySet())
        {
            String fqn = entry.getKey().getFqn();
            Object value = entry.getValue();
            if (value instanceof Map<?, ?> map)
            {
                Map<String, Object> levelRestrictionResult = new HashMap<>();
                for (Entry<?, ?> levelRestriction : map.entrySet())
                {
                    levelRestrictionResult.put((String)levelRestriction.getKey(),
                            resolveRestrictionValue(levelRestriction.getValue()));
                }
                result.put(fqn, levelRestrictionResult);
                continue;
            }
            result.put(fqn, resolveRestrictionValue(value));
        }
        return Json.GSON.toJson(result);
    }

    /**
     * Преобразует полноценный объект в упрощенный для создания из него Json
     *
     * @param value группа атрибутов, либо список атрибутов
     * @return код группы атрибутов, либо список кодов атрибутов
     */
    @Nullable
    private static Object resolveRestrictionValue(Object value)
    {
        if (value instanceof GroupAttr groupAttr)
        {
            return groupAttr.getCode();
        }
        if (value instanceof List<?>)
        {
            return ((List<?>)value).stream()
                    .map(Attribute.class::cast)
                    .map(Attribute::getCode)
                    .toList();
        }
        throw new IllegalArgumentException("Unknown restriction type: " + value.getClass().getName());
    }
}
