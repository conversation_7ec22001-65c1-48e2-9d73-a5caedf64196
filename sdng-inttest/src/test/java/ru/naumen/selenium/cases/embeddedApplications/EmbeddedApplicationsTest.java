package ru.naumen.selenium.cases.embeddedApplications;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.EDITABLE_TOOL_EDIT_BTN;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.DSLXssProtection;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationXpath.Content;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUITestEmbeddedApplication;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication.FormType;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.silent.DSLSilentMode;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование работы встроенных приложений в операторе.
 *
 * <AUTHOR>
 * @since 02.11.16
 */
class EmbeddedApplicationsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userClass;
    private static MetaClass userCase;
    private static Bo userBo;
    private static final String appAddress = GUITestEmbeddedApplication.getEmbeddedAppAddress();

    /**
     * <ol>
     * <li>Создать пользовательский класс userClass жизненным циклом и возможностью назначения ответственного</li>
     * <li>Создать тип userCase</li>
     * <li>Создать бизнес объект bo типа userCase</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        // Подготовка
        userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLSilentMode.setEnabled(false);
    }

    /**
     * Тестирования входных параметров встроенных приложений.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * http://sd-jira.naumen.ru/browse/NSDWRK-18354
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отключить SilentMode</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase класса userClass</li>
     * <li>Добавить приложение application1 - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт: return message</li>
     *  </ul>
     * </li>
     * <li>Добавить приложение application2 - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт: return "$subject.title"</li>
     *  </ul>
     * </li>
     * <li>Добавить приложение application3 - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт:
     *      return
     *      [
     *          "subject":"subject.uuid",
     *          "user":[
     *              "login":"login",
     *              "uuid":"uuid",
     *              "email":"email"
     *          ]
     *      ]
     *      </li>
     *  </ul>
     * </li>
     * <li>На карточку класса userClass добавить контенты:
     *  <ul>
     *      <li>Тип контента: Встроенное приложение;</li>
     *      <li>Приложение: application1..application3</li>
     *  </ul>
     * </li>
     * <li>Создать пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить входные параметры всех приложений</li>
     */
    @Test
    void testEmbeddedApplicationInputParam()
    {
        // Подготовка
        String message1 = ModelUtils.createText(20);
        String message2 = userBo.getTitle();

        Map<String, Object> param = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        Map<String, String> user = new HashMap<>();

        user.put("uuid", SharedFixture.employee().getUuid());
        user.put("login", SharedFixture.employee().getLogin());
        user.put("email", SharedFixture.employee().getEmail());
        map.put("user", user);
        map.put("subject", userBo.getTitle());
        param.put(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_PARAMS, map);

        //@formatter:off
        String message3 = Json.GSON.toJson(map)
                .replace("{", "[")
                .replace("}", "]")
                .replace("$", "\\$");
        //@formatter:on

        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo(String.format("return '%s';", message1));
        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo("return \"$subject.title\";");
        ScriptInfo script3 = DAOScriptInfo.createNewScriptInfo(String.format("return %s;", message3));

        EmbeddedApplication application1 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        EmbeddedApplication application3 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application1, script1);
        DSLEmbeddedApplication.add(application2, script2);
        DSLEmbeddedApplication.add(application3, script3);

        ContentForm content1 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application1);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application2);
        ContentForm content3 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application3);
        DSLContent.add(content1, content2, content3);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUITestEmbeddedApplication testApplication1 = GUIEmbeddedApplication.testEmbeddedApplication(content1);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content1);
        GUITestEmbeddedApplication testApplication2 = GUIEmbeddedApplication.testEmbeddedApplication(content2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content2);
        GUITestEmbeddedApplication testApplication3 = GUIEmbeddedApplication.testEmbeddedApplication(content3);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content3);

        testApplication1.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());
        testApplication2.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());
        testApplication3.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());

        testApplication1.assertInputParam(String.format("\"%s\"", message1));
        testApplication2.assertInputParam(String.format("\"%s\"", message2));
        testApplication3.assertInputParam(param);
    }

    /**
     * Тестирование наличия контекстной переменной contentCode (код контента встроенного приложения) в скрипте
     * встроенного приложения
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$46232431
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отключить SilentMode</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase класса userClass</li>
     * <li>Добавить приложение application - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт: return contentCode</li>
     *  </ul>
     * </li>
     * <li>На карточку класса userClass добавить контент testContent:
     *  <ul>
     *      <li>Тип контента: Встроенное приложение;</li>
     *      <li>Приложение: application1</li>
     *  </ul>
     * </li>
     * <li>Создать пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что входные параметры содержат код контента testContent </li>
     *
     */
    @Test
    void testEmbeddedApplicationScriptContentVariable()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return contentCode;");

        EmbeddedApplication application = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application, script);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUITestEmbeddedApplication testApplication = GUIEmbeddedApplication.testEmbeddedApplication(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);

        testApplication.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());

        testApplication.assertInputParam(String.format("\"%s\"", content.getCode()));
    }

    /**
     * Тестирования входных параметров встроенных приложений. Передача ошибок скрипта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * http://sd-jira.naumen.ru/browse/NSDWRK-18354
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отключить SilentMode</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase класса userClass</li>
     * <li>Добавить приложение application1 - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт: throw new Exception(error);</li>
     *  </ul>
     * </li>
     * <li>Добавить приложение application2 - тестовое приложение;
     *  <ul>
     *      <li>Тип приложения: Приложение запущено на внешнем сервере;</li>
     *      <li>Скрипт: "return " + ModelUtils.createText(2001)</li>
     *  </ul>
     * </li>
     * <li>На карточку класса userClass добавить контенты:
     *  <ul>
     *      <li>Тип контента: Встроенное приложение;</li>
     *      <li>Приложение: application1, application2</li>
     *  </ul>
     * </li>
     * <li>Создать пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить сообщение об ошибке в приложении application1: error</li>
     * <li>Проверить сообщение об ошибке в приложении application2: Limit on request-line length is exceeded.</li>
     */
    @Test
    void testEmbeddedApplicationScriptError()
    {
        // Подготовка
        String errorMessage1 = ModelUtils.createText(20);
        String errorMessage2 = "Limit on request-line length is exceeded.";
        String errorMessage3 = "Атрибут не найден";

        ScriptInfo script1 = DAOScriptInfo
                .createNewScriptInfo(String.format("throw new Exception('%s');", errorMessage1));

        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo("return \"" + ModelUtils.createText(2001) + "\"");

        ScriptInfo script3 = DAOScriptInfo.createNewScriptInfo("subject?.fdefsdfer.title");

        EmbeddedApplication application1 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application1, script1);

        EmbeddedApplication application2 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application2, script2);

        EmbeddedApplication application3 = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application3, script3);

        ContentForm content1 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application1);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application2);
        ContentForm content3 = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), application3);
        DSLContent.add(content1, content2, content3);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUITestEmbeddedApplication testApplication1 = GUIEmbeddedApplication.testEmbeddedApplication(content1);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content1);
        GUITestEmbeddedApplication testApplication2 = GUIEmbeddedApplication.testEmbeddedApplication(content2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content2);
        GUITestEmbeddedApplication testApplication3 = GUIEmbeddedApplication.testEmbeddedApplication(content3);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content3);

        testApplication1.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());
        testApplication2.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());
        testApplication3.getDefaultInputParam().replace(GUITestEmbeddedApplication.APPLICATION_IN_PARAM_SUBJECT,
                userBo.getUuid());

        testApplication1.assertInputParamError(errorMessage1);
        testApplication2.assertInputParamError(errorMessage2);
        testApplication3.assertInputParamError(errorMessage3);
    }

    /**
     * Тестирование того, что встроенные приложения работают при установленном значении параметра
     * ignore.owasp.in.safe.html = false
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00595
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$47637809
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Выполнить скрипт beanFactory.getBean('owaspConfiguration').setIgnoreOwaspInSafeHtml(false)</li>
     * <li>Создать встроенное приложение application (Тип приложения: Приложение запущено на внешнем сервере)</li>
     * <li>На карточку компании добавить контент Встроенное приложение</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под оператором</li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить наличие встроенного приложения</li>
     */
    @Test
    void testEmbeddedApplicationWithOwasp()
    {
        //Подготовка
        DSLXssProtection.setIgnoreOwaspInSafeHtml(false);
        EmbeddedApplication application = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(application, null);
        ContentForm content = DAOContentCard.createEmbeddedApplication(DAORootClass.create().getFqn(), application);
        DSLContent.add(content);
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        GUIFrame.appearFrame(String.format(Content.DIV_PATTERN + Any.IFRAME, content.getCode()));
    }

    /**
     * Тестирование работы встроенного приложения с несколькими вложенными папками внутри архива
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$80781349
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение application из заранее подготовленного архива</li>
     * <li>В типе userCase создать контент appContent со встроенным приложением application</li>
     * <li>Создать бизнес объект bo типа userCase</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку bo</li>
     * <li>Проверить, что присутствует контент appContent</li>
     * <li>Проверить, что в контенте встроенного приложения отображается
     * "test of embedded application with subfolders"</li>
     * </ol>
     */
    @Test
    void testEmbeddedApplicationWithSubfolders()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_WITH_SUBFOLDERS_ZIP);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(appContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIContent.assertPresent(appContent);
        Assertions.assertEquals("test of embedded application with subfolders",
                GUIEmbeddedApplication.getEmbeddedApplicationContent(appContent));
    }

    /**
     * Тестирование корректного удаления действующих ключей авторизации при архивировании сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$85811815
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение embeddedApplication, запущенное на внешнем сервере</li>
     * <li>На карточку класса userClass вывести контент eaContent типа «Встроенное приложение»
     * (Приложение — embeddedApplication)</li>
     * <li>Создать сотрудника employee</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Поместить сотрудника employee в архив</li>
     * <br>
     * <b>Проверки</b>
     * <li>Ошибок нет</li>
     * <li>Сотрудник employee находится в архиве</li>
     * </ol>
     */
    @Test
    void testRemoveEmbeddedApplicationKeyBeforeArchiving()
    {
        // Подготовка
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createExternalApplication(appAddress);
        DSLEmbeddedApplication.add(embeddedApplication);
        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), embeddedApplication);
        DSLContent.add(eaContent);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);
        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(eaContent);
        DSLBo.archive(employee);
        // Проверки
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование отсутствия загрузки на страницу контента встроенного приложения,
     * когда лицензия истекла и проверяем появление предупреждения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00652
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$100831718
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать лицензируемое встроенное приложение application из заранее подготовленного архива</li>
     * <li>В типе userCase создать контент appContent со встроенным приложением application</li>
     * <li>Создать бизнес объект bo типа userCase</li>
     * <li>Изменяем дату окончания действия лицензии (на 2 дня раньше чем сегодня, через скрипт)</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку bo</li>
     * <li>Проверить, что появилось предупреждение “Истекла лицензия на встроенное приложение
     *     "%Название встроенного приложения%”. Обратитесь к администратору системы."</li>
     * <li>Проверить, что присутствует контент appContent</li>
     * <li>Проверяем, что контент не загрузился (пустой)</li>
     * </ol>
     */
    @Test
    void testLicensedEmbeddedApplicationExpired()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.LICENSED_EMBEDDED_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(appContent);

        // изменяем дату окончания действия лицензии на 2 дня раньше чем сегодня
        ScriptRunner.executeScript("""
                        import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService
                        import ru.naumen.commons.server.utils.DateUtils
                        def license = beanFactory.getBean(EmbeddedApplicationService.class)\
                        .getApplication('%s').getLicense()
                        license.setExpirationDate(DateUtils.addDays(new Date(), -2))""",
                application.getCode());

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIForm.assertQuestion("Истекла лицензия на встроенное приложение \"%s\". Обратитесь к администратору системы.",
                application.getTitle());
        GUIForm.assertQuestionDialogTitle("Предупреждение");
        GUIForm.applyQuestion();
        // Проверяем что контент не загрузился
        GUIContent.assertPresent(appContent);
        String content = GUIEmbeddedApplication.getEmbeddedApplicationContent(appContent);
        Assertions.assertEquals(StringUtils.EMPTY, content, "Встроенное приложение загрузилось");
    }

    /**
     * Тестирование вызова метода скриптового модуля из другого скриптового модуля добавленных со встроенным
     * приложением
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$105189083
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль testModule1:<br>
     *     Код: "testModule1"<br>
     *     Текст: <pre>def getData1() {
     *       modules.testModule2.getData2()
     *     }</pre></li>
     * <li>Создать скриптовый модуль testModule2:<br>
     *     Код: "testModule2"<br>
     *     Текст: <pre>def getData2() {
     *         '\"TEXT\"'
     *     }</pre></li>
     * <li>Создать архив appFile встроенного приложения, исполняемого на клиенте, со скриптовыми модулями testModule1 и
     * testModule1 внутри и с js кодом:
     *   <pre>jsApi.restCallModule('testModule1', 'getData1)
     *          .then(function(result) {
     *             var testDiv = document.getElementById('test_div')
     *             testDiv.innerText += result.value
     *   })</pre></li>
     * <li>В типе userCase создать контент appContent со встроенным приложением appFile</li>
     * <li>Создать скриптовый модуль systemModuleWithSameCode,
     *     код которого совпадает с кодом модуля ВП:<br>
     *     Код: "testModule2"<br>
     *     Текст: <pre>def getData2() {
     *         '\"wrong data\"'
     *     }</pre></li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку bo</li>
     * <li>Проверяем, что внутри встроенного приложения появились ожидаемые нами данные insideAppText2</li>
     * </ol>
     */
    @Test
    void testEmbeddedApplicationWithTwoScriptModules()
    {
        // Подготовка
        ModuleConf testModule1 = DAOModuleConf.create("testModule1");
        testModule1.setScriptBody(String.join("\n",
                "def getData() {",
                "  modules.testModule2.getData2()",
                "}"
        ));
        ModuleConf testModule2 = DAOModuleConf.create("testModule2");
        String insideAppText2 = ModelUtils.createText(15);
        testModule2.setScriptBody(String.format(String.join("\n",
                "def getData2() {",
                "  '\"%s\"'",
                "}"
        ), insideAppText2));

        EmbeddedApplication testApp = DAOEmbeddedApplication.createClientSideApplication(temp,
                testModule1, testModule2);
        DSLEmbeddedApplication.add(testApp);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), testApp);
        DSLContent.add(appContent);

        ModuleConf systemModuleWithSameCode = DAOModuleConf.create("testModule2");
        systemModuleWithSameCode.setScriptBody(String.join("\n",
                "def getData2() {",
                "  '\"wrong data\"'",
                "}"));
        DSLModuleConf.add(systemModuleWithSameCode);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals(insideAppText2,
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
    }

    /**
     * Тестирование вызова метода скриптового модуля из другого скриптового модуля с помощью jsApi.restCall(),
     * если оба модуля добавлены со встроенным приложением
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112621254
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль testModule1:<br>
     *     Код: "testModule1"<br>
     *     Текст: <pre>def getData1() {
     *       modules.testModule2.getData2()
     *     }</pre></li>
     * <li>Создать скриптовый модуль testModule2:<br>
     *     Код: "testModule2"<br>
     *     Текст: <pre>def getData2() {
     *         'TEXT'
     *     }</pre></li>
     * <li>Создать архив appFile встроенного приложения, исполняемого на клиенте, со скриптовыми модулями testModule1 и
     * testModule2 внутри и с js кодом:
     *   <pre>jsApi.restCall('exec?func=modules.testModule1.getData1&params=')
     *          .then(function(result) {
     *             var testDiv = document.getElementById('test_div')
     *             testDiv.innerText += result.value
     *   })</pre></li>
     * <li>В типе userCase создать контент appContent со встроенным приложением appFile</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения появились ожидаемые нами данные insideAppText2</li>
     * </ol>
     */
    @Test
    void testCallModuleFunctionFromOtherModuleInEmbeddedApplication()
    {
        // Подготовка
        ModuleConf testModule1 = DAOModuleConf.create("testModule1");
        testModule1.setScriptBody("""
                def getData1() {
                  modules.testModule2.getData2()
                }"""
        );
        ModuleConf testModule2 = DAOModuleConf.create("testModule2");
        String insideAppText2 = ModelUtils.createText(15);
        testModule2.setScriptBody(String.format("""
                def getData2() {
                  '%s'
                }""", insideAppText2));

        EmbeddedApplication testApp = DAOEmbeddedApplication.createClientSideApplication(temp,
                String.format("""
                        jsApi.restCall('exec?func=modules.testModule1.getData1&params=')
                        .then(function (result) {
                            var testDiv = document.getElementById('%s')
                            testDiv.innerText += result
                        })""", GUIEmbeddedApplication.TEST_DIV_ID),
                testModule1, testModule2);
        DSLEmbeddedApplication.add(testApp);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), testApp);
        DSLContent.add(appContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals(insideAppText2,
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
    }

    /**
     * Тестирование работы встроенного приложения с зависимостями между скриптовыми модулями
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112621246
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль compileTestParams</li>
     * <li>Создать скриптовый модуль compileTest, который использует класс, определённый в compileTestParams</li>
     * <li>Создать встроенное приложение application, исполняемого на клиенте, со скриптовыми модулями
     *     compileTest, compileTestParams</li>
     * <li>В типе userCase создать контент appContent со встроенным приложением application</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения появились данные Value = compileTestValue</li>
     * </ol>
     */
    @Test
    void testAppWithModuleDependencies()
    {
        // Подготовка
        ModuleConf compileTestParams = DAOModuleConf.create("compileTestParams");
        String compileTestValue = ModelUtils.createText(15);
        compileTestParams.setScriptBody(String.format("""
                        package ru.naumen.compile.test
                        
                        import groovy.transform.Field
                        
                        class CompileTestParams
                        {
                            String value = '%s'
                        
                            String getValue() {
                                return value
                            }
                        }
                        
                        @Field static final CompileTestParams PARAMS = new CompileTestParams();
                        
                        CompileTestParams get() {
                            return PARAMS;
                        }"""
                , compileTestValue));
        ModuleConf compileTest = DAOModuleConf.create("compileTest");
        compileTest.setScriptBody("""
                package ru.naumen.compile.test
                
                def getData() {
                    def params = modules.compileTestParams.get()
                    return '"Value = ' + params.getValue() + '"'
                }"""
        );
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                compileTest, compileTestParams);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(
                userCase.getFqn(), application);
        DSLContent.add(appContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals("Value = " + compileTestValue,
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
    }

    /**
     * Тестирование работы лицензируемого встроенного приложения, у которого скрытый скриптовый
     * модуль (основная логика) зависит от публичного скриптового модуля (параметры приложения)
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112621246
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить заранее подготовленное лицензируемое встроенное приложение application,
     *     у которого скрытый скриптовый модуль (основная логика) зависит от
     *     публичного модуля (параметры приложения)</li>
     * <li>В типе userCase создать контент appContent со встроенным приложением application</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения появились данные Value = compileTestValue</li>
     * </ol>
     */
    @Test
    void testLicensedAppWithModuleDependencies()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.LICENSED_CLIENTSIDE_APPLICATION_MODULE_DEPENDENCIES_ZIP);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(
                userCase.getFqn(), application);
        DSLContent.add(appContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals("Value = compileTestValue",
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
    }

    /**
     * Тестирование вызова метода скриптового модуля из метода класса помеченного аннотацией @InjectApi из другого
     * модуля, если оба модуля добавлены со встроенным приложением
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$139165036
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль testModule:<br>
     *     Код: "testModule1"<br>
     *     Текст: <pre>
     *      package ru.naumen.modules.test
     *      &#64;InjectApi
     *      class TestClass
     *      {
     *          def method()
     *          {
     *              return modules.testModule.methodFromModule()
     *          }
     *      }
     *      def getData()
     *      {
     *          return new TestClass().method()
     *      }
     *      def methodFromModule()
     *      {
     *          return '"insideAppText"'
     *      }
     *     </pre></li>
     * <li>Создать встроенное приложение application, исполняемого на клиенте, со скриптовым модулем testModule</li>
     * <li>В типе userCase создать контент appContent со встроенным приложением application</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения появились ожидаемые нами данные insideAppText</li>
     * </ol>
     */
    @Test
    void testCallModuleFunctionFromOtherModuleWithInjectApiAnnotationInEmbeddedApplication()
    {
        // Подготовка
        String moduleCode = "testModule";
        ModuleConf testModule = DAOModuleConf.create(moduleCode);
        String content = ModelUtils.createText(10);
        testModule.setScriptBody(String.format("""
                package ru.naumen.modules.test
                @InjectApi
                class TestClass
                {
                    def method()
                    {
                        return modules.%s.methodFromModule()
                    }
                    @Override
                    String toString() {
                        return 'insideAppText'
                    }
                }
                def getData()
                {
                    return new TestClass().method()
                }
                def methodFromModule()
                {
                    return '"%s"'
                }""", moduleCode, content));
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                testModule);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(appContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        Assertions.assertEquals(content, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent));
    }

    /**
     * Тестирование добавление места использования для встроенного приложения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$135823060
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип класса Запрос - scCase</li>
     * <li>Создать новое встроенное приложение: embeddedApplication (HELLO_WORLD_APPLICATION_ZIP)</li>
     * <li>Включить возможность размещения приложения на модальных формах</li>
     * <li>Добавить в embeddedApplication новое место использования usage:
     * Название - любое, Код - любой, Объекты - класс Запрос, Тип формы - Смена статуса</li>
     * <li>Добавить объект sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти на карточку добавленного запроса sc</li>
     * <li>Нажать кнопку "Изменить статус"</li>
     * <li>Проверить, что модальная форма появилась
     * и на ней появилось встроенное приложение с текстом "Hello, world"</li>
     * </ol>
     */
    @Test
    void testApplicationOnModalForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(scCase);

        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.HELLO_WORLD_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(embeddedApplication);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(embeddedApplication);

        UsagePointApplication usage =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.ChangeStateForm, DAOScCase.createClass());
        DSLEmbeddedApplication.edit(embeddedApplication, null, usage);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIButtonBar.changeState();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        String text = GUIEmbeddedApplication.getEmbeddedApplicationContent(usage);
        Assertions.assertEquals("Hello, world", text,
                "Содержимое рабочей области встроенного приложения не совпало с ожидаемым.");
    }

    /**
     * Тестирование отображения формы пользовательского действия по событию без параметров на которой отображается
     * только встроенное приложение
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$169294853
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00657
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить пользовательское действие по событию eventAction1 и eventAction2: <pre>
     *     return;
     * </pre></li>
     * <li>Для eventAction2 добавить строковый параметр stringParam</li>
     * <li>Добавить в систему ВП embeddedApplication</li>
     * <li>Включить возможность размещения приложения embeddedApplication на модальных формах</li>
     * <li>Добавить место использования embeddedApplication, где:
     * объекты: userClass;
     * тип формы: форма пользовательского действия по событию;
     * Пользовательские действия по событию: [eventAction1, eventAction2]</li>
     * <li>У eventAction2 удалить параметр stringParam</li>
     * <li>На карточку userClass добавить кнопки пользовательского действия по событию - eventAction1 и
     * eventAction2</li>
     * <li>Добавить объект userBo пользовательского класса userClass</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Авторизоваться с правами пользователя</li>
     * <li>Открыть карточку userBo</li>
     * <li>Нажать на кнопку eventAction1</li>
     * <li>Проверить, проверить что на форме отобразилось встроенное приложение embeddedApplication и закрыть форму</li>
     * <li>Нажать на кнопку eventAction2</li>
     * <li>Проверить, проверить что на форме отобразилось встроенное приложение embeddedApplication</li>
     * </ol>
     */
    @Test
    void testShowEventActionFormAfterRemoveLastParameter()
    {
        //Подготовка
        ScriptInfo eventActionScriptInfo = DAOScriptInfo.createNewScriptInfo("return;");
        DSLScriptInfo.addScript(eventActionScriptInfo);

        EventAction eventAction1 = DAOEventAction.createEventScript(EventType.userEvent,
                eventActionScriptInfo.getCode(), true, true, userClass);
        EventAction eventAction2 = DAOEventAction.createEventScript(EventType.userEvent,
                eventActionScriptInfo.getCode(), true, true, userClass);
        DSLEventAction.add(eventAction1, eventAction2);

        FormParameter stringParam = DAOFormParameter.createString();
        stringParam.setEventAction(eventAction2.getUuid());
        stringParam.setDefaultValue("");
        stringParam.setDescription("");
        DSLFormParameter.save(stringParam);

        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(embeddedApplication);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(embeddedApplication);

        UsagePointApplication usagePointApplication = DAOEmbeddedApplication.createUsagePointApplication(
                FormType.UserEventActionForm, userClass);
        usagePointApplication.setUserEventActions(eventAction1, eventAction2);
        DSLEmbeddedApplication.edit(embeddedApplication, null, usagePointApplication);
        DSLFormParameter.delete(stringParam);

        ContentForm card = DAOContentCard.createWindow(userClass);
        String eventAction1Title = ModelUtils.createTitle();
        DSLContent.addUserTool(card, eventAction1Title, eventAction1);
        String eventAction2Title = ModelUtils.createTitle();
        DSLContent.addUserTool(card, eventAction2Title, eventAction2);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(userClass, DSLMetaClass.MetaclassCardTab.OBJECTCARD));

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIButtonBar.clickButtonByText(eventAction1Title);
        GUIContent.assertPresent(usagePointApplication);
        GUIForm.cancelForm();

        GUIButtonBar.clickButtonByText(eventAction2Title);
        GUIContent.assertPresent(usagePointApplication);
    }

    /**
     * Тестирование отображения встроенного приложения на форме пользовательского действия по событию (ПДПС), у которой
     * нет параметров, если другое ПДПС перестает отображаться после изменения поля "Объекты" и "Пользовательское
     * действие по событию" <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00657
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180219567
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить скрипт eventActionScriptInfo: return;</li>
     * <li>Добавить пользовательское событие eventAction:<pre>
     *      Тип: скрипт
     *      Скрипт: eventActionScriptInfo
     *      Синхронный: да
     *      Объекты: userClass
     * </pre></li>
     * <li>Добавить пользовательское событие eventActionSc:<pre>
     *      Тип: скрипт
     *      Скрипт: eventActionScriptInfo
     *      Синхронный: да
     *      Объекты: Запрос
     * </pre></li>
     * <li>Добавить встроенное приложение embeddedApplication1
     * {@link DAOEmbeddedApplication#CLIENTSIDE_APPLICATION_ZIP embeddedApplication1}</li>
     * <li>Добавить встроенное приложение embeddedApplication2
     * {@link DAOEmbeddedApplication#CLIENTSIDE_APPLICATION_ZIP embeddedApplication2}</li>
     * <li>Установить список встроенных приложений, которые могут отображаться на модальных формах:
     * embeddedApplication1, embeddedApplication2</li>
     * <li>Добавить место использования usagePoint1 встроенного приложения embeddedApplication1:<pre>
     *     Тип формы: Форма пользовательского действия по событию"
     *     Пользовательские действия по событию: eventAction
     * </pre></li>
     * <li>Добавить место использования usage2 встроенного приложения embeddedApplication2:<pre>
     *     Тип формы: Форма пользовательского действия по событию"
     *     Пользовательские действия по событию: eventAction
     * </pre></li>
     * <li>На карточку объектов класса userClass добавить кнопку вызова eventAction</li>
     * <li>Добавить объект userBo пользовательского типа userCase</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Изменить место использования usagePoint1:<pre>
     *     Объекты: {@link SharedFixture#scCase()}
     *     Пользовательские действия по событию: eventActionSc
     * </pre></li>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Открыть карточку объекта userBo</li>
     * <li>Вызвать действие eventAction с карточки userBo</li>
     * <li>Проверить, что на форме ПДПС отобразился контент usage2 со встроенным приложением embeddedApplication2</li>
     * </ol>
     */
    @Test
    void testChangeMetaClassInOneOfUsedApplicationOnForm()
    {
        //Подготовка
        ScriptInfo eventActionScriptInfo = DAOScriptInfo.createNewScriptInfo("return;");
        DSLScriptInfo.addScript(eventActionScriptInfo);

        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, eventActionScriptInfo.getCode(),
                true, true, userClass);
        EventAction eventActionSc = DAOEventAction.createEventScript(EventType.userEvent,
                eventActionScriptInfo.getCode(), true, true, SharedFixture.scCase());
        DSLEventAction.add(eventAction, eventActionSc);

        EmbeddedApplication embeddedApplication1 = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        EmbeddedApplication embeddedApplication2 = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(embeddedApplication1, embeddedApplication2);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(embeddedApplication1, embeddedApplication2);

        UsagePointApplication usagePoint1 =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.UserEventActionForm, userClass);
        usagePoint1.setUserEventActions(eventAction);
        UsagePointApplication usagePoint2 =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.UserEventActionForm, userClass);
        usagePoint2.setUserEventActions(eventAction);
        DSLEmbeddedApplication.edit(embeddedApplication1, null, usagePoint1);
        DSLEmbeddedApplication.edit(embeddedApplication2, null, usagePoint2);

        ContentForm card = DAOContentCard.createWindow(userClass);
        String eventActionTitle = ModelUtils.createTitle();
        DSLContent.addUserTool(card, eventActionTitle, eventAction);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Действия и проверки
        usagePoint1.setFqns(SharedFixture.scCase().getFqn());
        usagePoint1.setUserEventActions(eventActionSc);
        DSLEmbeddedApplication.edit(embeddedApplication1, null, usagePoint1);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIButtonBar.clickButtonByText(eventActionTitle);
        GUIContent.assertPresent(usagePoint2);
    }

    /**
     * Тестирование отсутствия отображения встроенных приложений на формах быстрого добавления и
     * редактирования с учётом значения параметра "Доступные формы" в местах использования встроенных приложений
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$177245213
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Создать тип scCase класса Запрос</li>
     *   <li>Создать быструю форму добавления quickForm1, quickForm2 для типа scCase с группой аттрибутов attrGroup</li>
     *   <li>Создать новое встроенное приложение: embeddedApplication (HELLO_WORLD_APPLICATION_ZIP)</li>
     *   <li>Включить возможность размещения приложения на модальных формах</li>
     *   <li>Добавить в embeddedApplication новое место использования usage:
     *      Название - любое, Код - любой, Объекты - класс Запрос, Тип формы - Форма быстрого добавления и
     *      редактирования, Форма - quickForm2</li>
     *   <li>Создать контент propertiesListContent типа "Параметры объекта" в типе scCase, содержащий группу
     *   атрибутов attrGroup</li>
     *   <li>Создать контент propertiesListContent2 типа "Параметры объекта" в типе scCase, содержащий группу
     *   атрибутов attrGroup2</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти в настройки карточки типа scCase</li>
     *   <li>Изменить действие кнопки "Редактировать" контента propertiesListContent с параметрами объекта на
     *   открытие быстрой формы редактирования quickForm1</li>
     *   <li>Изменить действие кнопки "Редактировать" контента propertiesListContent2 с параметрами объекта на
     *   открытие быстрой формы редактирования quickForm2</li>
     *   <li>Перейти на карточку объекта sc</li>
     *   <li>Нажать кнопку c быстрой формой редактирования quickForm1 в контенте propertiesListContent</li>
     *   <li>Проверить, что модальная форма появилась и отсутствует встроенное приложение</li>
     *   <li>Закрыть форму</li>
     *   <li>Нажать кнопку c быстрой формой редактирования quickForm2 в контенте propertiesListContent2</li>
     *   <li>Проверить, что модальная форма появилась и встроенное приложение отобразилось</li>
     * </ol>
     */
    @Test
    void testVisibleEmbeddedApplicationOnQuickForm()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase);

        GroupAttr attrGroup = DAOGroupAttr.createSystem(scCase);
        GroupAttr attrGroup2 = DAOGroupAttr.createSystem(scCase);

        CustomForm quickForm1 = DAOCustomForm.createQuickActionForm(attrGroup, scCase);
        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(attrGroup2, scCase);
        DSLCustomForm.add(quickForm1, quickForm2);

        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.HELLO_WORLD_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(embeddedApplication);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(embeddedApplication);

        UsagePointApplication usage =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.QuickAddAndEditForm, scClass);
        usage.setAvailableForms(quickForm2);
        DSLEmbeddedApplication.edit(embeddedApplication, null, usage);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(scCase, MetaclassCardTab.OBJECTCARD);

        ContentForm propertiesListContent = DAOContentCard.createPropertyList(scCase, attrGroup);
        ContentForm propertiesListContent2 = DAOContentCard.createPropertyList(scCase, attrGroup2);
        DSLContent.add(propertiesListContent, propertiesListContent2);

        GUIContent.enableEditProperties();
        GUIContent.visibility(propertiesListContent);
        tester.click(propertiesListContent.advlist().editableToolPanel().getContentXpath() + EDITABLE_TOOL_EDIT_BTN);
        propertiesListContent.advlist()
                .editableToolPanel()
                .setUseSystemSettings(false)
                .rightClickTool(GUIButtonBar.BTN_EDIT)
                .clickEditContextMenuOption()
                .setUseQuickEditForm(true)
                .selectQuickEditForm(quickForm1)
                .clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();

        GUIContent.enableEditProperties();
        GUIContent.visibility(propertiesListContent2);
        tester.click(propertiesListContent2.advlist().editableToolPanel().getContentXpath() + EDITABLE_TOOL_EDIT_BTN);
        propertiesListContent2.advlist()
                .editableToolPanel()
                .setUseSystemSettings(false)
                .rightClickTool(GUIButtonBar.BTN_EDIT)
                .clickEditContextMenuOption()
                .setUseQuickEditForm(true)
                .selectQuickEditForm(quickForm2)
                .clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();

        GUIBo.goToCard(sc);
        GUIContent.clickEdit(propertiesListContent);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIContent.assertAbsence(usage);
        GUIForm.clickCloseOnTheCross();

        GUIContent.clickEdit(propertiesListContent2);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIContent.assertPresent(usage);
    }

    /**
     * Тестирование появления ошибки в логе приложения перед запуском пользовательского ДПС при вызове его из
     * встроенного приложения, если у пользователя нет прав на выполнение данного ДПС
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00681
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813872
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать группу пользователей group</li>
     * <li>Создать лицензированного пользователя employee и установить ему группу пользователей group</li>
     * <li>Создать профиль profile для лицензированных пользователей, группа group, роль - "Сотрудник"</li>
     * <li>Выдать все права во всех классах профилю profile</li>
     * <li>Убрать право "Остальные события" в классе userClass для профиля profile</li>
     * <li>Создать ДПС event:
     * <pre>
     *     Объекты - UserClass
     *     Событие - [Пользовательское событие]
     *     Действие - Уведомление в интерфейсе
     *     Кому - employee
     * </pre>
     * </li>
     * <li>Создать встроенное приложение application исполняемое на клиентской части:
     * <pre>
     *     -----------------------------------------------------------------------
     *     jsApi.eventActions.getEventActionExecutor("{eventUUID}")
     *     .setSubject("{userBoUUID}")
     *     .execute()
     *     -----------------------------------------------------------------------
     *     где:
     *     eventUUID - uuid event
     *     userBoUUID - uuid userBo
     * </pre>
     * </li>
     * <li>Создать контент content типа "Встроенное приложение" на карточке компании с приложением application</li>
     * <br/>
     * <b>Действия и проверки</b>
     * <li>Зайти в ИО под employee</li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить, что в логе приложения ошибка выполнения ВП</li>
     * </ol>
     */
    @Test
    void testCheckPermissionBeforeRunEventFromEmbeddedApplication()
    {
        //Подготовка
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee.setSecurityGroupCode(Json.listToString(group.getCode()));
        DSLBo.add(employee);

        SecurityProfile profile = DAOSecurityProfile.create(true, group, SysRole.employee());
        DSLSecurityProfile.add(profile);

        DSLSecurityProfile.grantAllPermissions(profile);
        DSLSecurityProfile.removeRights(userClass, profile, AbstractBoRights.USER_EVENTS);

        EventAction event = DAOEventAction.createPush(userClass, EventType.userEvent, employee);
        DSLEventAction.add(event);

        String jsContent = String.format("""
                jsApi.eventActions.getEventActionExecutor("%s")
                .setSubject("%s")
                .execute()""", event.getUuid(), userBo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(DAORootClass.create(), application);
        DSLContent.add(content);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(SharedFixture.root());

        DSLLog.assertPresentMessageInLog(String.format(
                "%s: действие по событию %s не выполнено для объектов: %s у пользователя нет прав на выполнение "
                + "действия.", application.getCode(), event.getTitle(), userBo.getUuid()));
        DSLLog.assertPresentMessageInLog("Действие не может быть выполнено по причине: у вас нет прав на "
                                         + "выполнение действия");
    }

    /**
     * Тестирование вызова скриптовых модулей у двух ВП, в которых коды модулей совпадают.<br>
     * Модули обязаны быть доступны по коду "<код_ВП>_<код_модуля>".
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$216772865 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00621 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692 <br>
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение application1, исполняемое на стороне клиента,
     *     со скриптовым модулем moduleWithSameCodeEA1 с кодом 'moduleCode',
     *     который возвращает текстовые данные "ea1Value"</li>
     * <li>Создать встроенное приложение application2, исполняемое на стороне клиента,
     *     со скриптовым модулем moduleWithSameCodeEA2 с кодом 'moduleCode',
     *     который возвращает текстовые данные "ea2Value"</li>
     * <li>В типе userCase создать контент appContent1 со встроенным приложением application1</li>
     * <li>В типе userCase создать контент appContent2 со встроенным приложением application2</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:<pre>"1: ${modules.application1_moduleCode.getData()}; 2: ${modules.application2_moduleCode.getData()}"</pre>
     *     и проверить, что вернулось значение "1: ea1Value; 2: ea2Value"</li>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения application1 появились данные ea1Value</li>
     * <li>Проверяем, что внутри встроенного приложения application2 появились данные ea2Value</li>
     * </ol>
     */
    @Test
    void testCallModulesForEmbeddedApplicationsWithModulesSameCode()
    {
        // Подготовка
        String moduleCode = "EAModuleWithSameCode";
        String ea1Value = ModelUtils.createText(15);
        ModuleConf moduleWithSameCodeEA1 = DAOEmbeddedApplication.createScriptModuleForApplication(ea1Value);
        moduleWithSameCodeEA1.setCode(moduleCode);
        EmbeddedApplication application1 = DAOEmbeddedApplication.createClientSideApplication(temp,
                moduleWithSameCodeEA1);
        String ea2Value = ModelUtils.createText(15);
        ModuleConf moduleWithSameCodeEA2 = DAOEmbeddedApplication.createScriptModuleForApplication(ea2Value);
        moduleWithSameCodeEA2.setCode(moduleCode);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                moduleWithSameCodeEA2);
        DSLEmbeddedApplication.add(application1, application2);

        ContentForm appContent1 = DAOContentCard.createEmbeddedApplication(userCase, application1);
        ContentForm appContent2 = DAOContentCard.createEmbeddedApplication(userCase, application2);
        DSLContent.add(appContent1, appContent2);

        // Действия и проверки
        String result = ScriptRunner.executeScript(
                "\"1: ${modules.%s.getData()}; 2: ${modules.%s.getData()}\"",
                application1.getCode() + '_' + moduleCode,
                application2.getCode() + '_' + moduleCode);
        Assertions.assertEquals("1: " + StringUtils.wrap(ea1Value, '"')
                                + "; 2: " + StringUtils.wrap(ea2Value, '"'), result);
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals(ea1Value, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent1));
        Assertions.assertEquals(ea2Value, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent2));
    }

    /**
     * Тестирование корректной работы аннотации {@code @InjectApi} при работе внутри
     * встроенного приложения
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$216772865 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682 <br>
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение application1, исполняемое на стороне клиента,
     *     со скриптовым модулем moduleEA1 с кодом 'EAModuleWithSameCode'<br>
     *     Скрипт: <pre>
     *     &#64;InjectApi
     *     class TestInjectApiInEA {
     *         def test() {
     *             return "1. module in EA: ${modules.containsKey('moduleCode')}; " +
     *                 "2. module in EA1 with full code: ${modules.containsKey('application1_moduleCode')}; " +
     *                 "3. module in EA2 with full code: ${modules.containsKey('application2_moduleCode')};"
     *         }
     *     }
     *     def getData() {
     *         return '"' + new TestInjectApiInEA().test() + '"'
     *     }
     *     </pre></li>
     * <li>Создать встроенное приложение application2, исполняемое на стороне клиента,
     *     со скриптовым модулем moduleEA2 с кодом 'EAModuleWithSameCode'<br>
     *     Скрипт: <pre>
     *     &#64;InjectApi
     *     class TestInjectApiInEA {
     *         def test() {
     *             return "1. module in EA: ${modules.containsKey('moduleCode')}; " +
     *                 "2. module in EA1 with full code: ${modules.containsKey('application1_moduleCode')}; " +
     *                 "3. module in EA2 with full code: ${modules.containsKey('application2_moduleCode')};"
     *         }
     *     }
     *     def getData() {
     *         return '"' + new TestInjectApiInEA().test() + '"'
     *     }
     *     </pre></li>
     * <li>В типе userCase создать контент appContent1 со встроенным приложением application1</li>
     * <li>В типе userCase создать контент appContent2 со встроенным приложением application2</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверяем, что внутри встроенного приложения application1 появились данные
     *     "1. module in EA: true;
     *      2. module in EA1 with full code: false;
     *      3. module in EA2 with full code: true;"</li>
     * <li>Проверяем, что внутри встроенного приложения application2 появились данные
     *     "1. module in EA: true;
     *      2. module in EA1 with full code: true;
     *      3. module in EA2 with full code: false;"</li>
     * </ol>
     */
    @Test
    void testAnnotationInjectApiInEmbeddedApplication()
    {
        // Подготовка
        String moduleCode = "EAModuleWithSameCode";
        String eaCode1 = ModelUtils.createCode();
        String eaCode2 = ModelUtils.createCode();
        String script = """
                @InjectApi
                class TestInjectApiInEA {
                    def test() {
                        return "1. module in EA: ${modules.containsKey('%s')}; " +
                            "2. module in EA1 with full code: ${modules.containsKey('%s')}; " +
                            "3. module in EA2 with full code: ${modules.containsKey('%s')};"
                    }
                }
                def getData() {
                    return '"' + new TestInjectApiInEA().test() + '"'
                }""".formatted(
                moduleCode,
                eaCode1 + '_' + moduleCode,
                eaCode2 + '_' + moduleCode);
        ModuleConf moduleEA1 = DAOModuleConf.create(moduleCode);
        moduleEA1.setScriptBody(script);
        EmbeddedApplication application1 = DAOEmbeddedApplication.createClientSideApplication(temp,
                moduleEA1);
        application1.setCode(eaCode1);
        moduleEA1.setEmbeddedApplicationCode(eaCode1);
        ModuleConf moduleEA2 = DAOModuleConf.create(moduleCode);
        moduleEA2.setScriptBody(script);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                moduleEA2);
        application2.setCode(eaCode2);
        moduleEA2.setEmbeddedApplicationCode(eaCode2);

        DSLEmbeddedApplication.add(application1, application2);

        ContentForm appContent1 = DAOContentCard.createEmbeddedApplication(userCase, application1);
        ContentForm appContent2 = DAOContentCard.createEmbeddedApplication(userCase, application2);
        DSLContent.add(appContent1, appContent2);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Assertions.assertEquals("1. module in EA: true; "
                                + "2. module in EA1 with full code: false; "
                                + "3. module in EA2 with full code: true;",
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent1));
        Assertions.assertEquals("1. module in EA: true; "
                                + "2. module in EA1 with full code: true; "
                                + "3. module in EA2 with full code: false;",
                GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(appContent2));
    }
}