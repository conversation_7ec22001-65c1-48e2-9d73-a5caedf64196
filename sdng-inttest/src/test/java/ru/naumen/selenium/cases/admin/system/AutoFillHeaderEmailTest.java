package ru.naumen.selenium.cases.admin.system;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIMassProblem;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.mail.DSLSmtpServer;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.mail.DAOEmail;
import ru.naumen.selenium.casesutil.model.mail.Email;
import ru.naumen.selenium.casesutil.model.mail.SmtpServer;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection.MailProtocol;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTask;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование отображение и сокрытие элементов интерфейса
 * <AUTHOR>
 * @since 20.03.2024
 */
class AutoFillHeaderEmailTest extends AbstractTestCaseJ5
{
    private static MetaClass userClass, userCase, scCase, scClass;
    private static Bo employee;
    private static SmtpServer server;
    private static final String REFERENCE_TEMPLATE = "<nsmp:%s>";
    private static final String REFERENCES_TITLE = "References";
    private static final String SET_REFERENCES_HEADER = "beanFactory.getBean('configurationProperties')"
                                                        + ".setReferencesHeaderNeeded(%s)";

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Настроить и включить исходящую почту</li>
     * <li>Создать класс userClass с назначением ответственного и жизненным циклом и унаследованный от него тип
     * userType</li>
     * <li>В консоли выполнить beanFactory.getBean('configurationProperties').setReferencesHeaderNeeded(true)</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     */
    @BeforeAll
    static void prepareFixture()
    {
        //Подготовка
        scClass = DAOScCase.createClass();
        scCase = DAOScCase.create(scClass);
        server = DSLSmtpServer.startSmtpServer();
        userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase, scCase);
        new ScriptRunner(String.format(SET_REFERENCES_HEADER, true)).runScript();
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
    }

    /**
     * Тестирование отсутствия автоматического заполнения UUID-ом объекта заголовка References в исходящем письме,
     * если выключен параметр ru.naumen.mail.outgoing.addReferencesHeader.enabled при отправке оповещения по событию
     * "Добавление объекта"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В консоли выполнить скрипт: beanFactory.getBean('configurationProperties').setReferencesHeaderNeeded
     * (false)</li>
     * <li>Создать и включить ДПС:</li>
     *     <ol>
     *         <li>Название: testDPS</li>
     *         <li>Код: testDPS</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Добавление объекта</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: test</li>
     *         <li>Текст оповещения: test</li>
     *     </ol>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Создать объект класса userClass</li>
     * <li>В отправленном письме в коде письма заголовок References пуст</li>
     * </ol>
     */
    @Test
    void testAbsenceAutoFillHeaderIfParameterOff()
    {
        //Подготовка
        new ScriptRunner(String.format(SET_REFERENCES_HEADER, false)).runScript();
        Cleaner.afterTest(true, () -> new ScriptRunner(String.format(SET_REFERENCES_HEADER, true)).runScript());
        EventAction notification = DAOEventAction.createNotification(userCase, EventType.add);
        notification.setEmails(server.getParams().getSystemMail());
        notification.setSubject("testDPS");
        notification.setMessage("testDPS");
        DSLEventAction.add(notification);

        //Проверки
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLSmtpServer.assertNotStandartHeader(REFERENCES_TITLE, "", server.getPortNumber());
    }

    /**
     * Тестирование автоматического заполнения UUID-ом объекта заголовка References в исходящем письме
     * при отправке оповещения по событиям "Изменение объекта" и "Изменение ответственного"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать лицензированного Сотрудника со всеми правами</li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS1</li>
     *         <li>Код: testDPS1</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Изменение объекта</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: testDPS1</li>
     *         <li>Текст оповещения: testDPS1</li>
     *     </ol>
     * </li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS2</li>
     *         <li>Код: testDPS2</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Смена ответственного</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: testDPS2</li>
     *         <li>Текст оповещения: testDPS2</li>
     *     </ol>
     * </li>
     * <li>Создать объект класса userClass</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Отредактировать атрибут "Ответственный", указав созданного Сотрудника</li>
     * <li>В отправленном письме по ДПС testDPS1 в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор объекта userClass</li>
     * <li>В отправленном письме по ДПС testDPS2 в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор объекта userClass</li>
     * </ol>
     */
    @Test
    void testReferencesHeaderAutoFillOnObjectAndResponsibleChange()
    {
        // Подготовка
        EventAction notification1 = DAOEventAction.createNotification(userCase, EventType.edit);
        notification1.setEmails(server.getParams().getSystemMail());
        notification1.setSubject("testDPS1");
        notification1.setMessage("testDPS1");

        EventAction notification2 = DAOEventAction.createNotification(userCase, EventType.changeResponsible);
        notification2.setEmails(server.getParams().getSystemMail());
        notification2.setSubject("testDPS2");
        notification2.setMessage("testDPS2");

        DSLEventAction.add(notification1, notification2);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Действия и проверки
        Attribute responsibleEmployee = SysAttribute.responsibleEmployee(userCase);
        responsibleEmployee.setBoValue(employee);
        DSLBo.editAttributeValue(userBo, responsibleEmployee);

        DSLSmtpServer.assertNotStandartHeaderBySubject(notification1.getSubject(), REFERENCES_TITLE,
                String.format(REFERENCE_TEMPLATE, userBo.getUuid()),
                2, server.getPortNumber());
        DSLSmtpServer.assertNotStandartHeaderBySubject(notification2.getSubject(), REFERENCES_TITLE,
                String.format(REFERENCE_TEMPLATE, userBo.getUuid()),
                2, server.getPortNumber());
    }

    /**
     * Тестирование автоматического заполнения UUID-ом объекта заголовка References в исходящем письме
     * при отправке оповещения по событию "Добавление комментария к объекту", вызванному действием по событию
     * "Прикрепление файла к объекту"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS1</li>
     *         <li>Код: testDPS1</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Прикрепление файла к объекту</li>
     *         <li>Действие: Скрипт</li>
     *         <li>Выполнять синхронно: да</li>
     *         <li>Название скрипта: test</li>
     *         <li>Код скрипта: test</li>
     *         <li>Текст скрипта: utils.create('comment', ['text': 'Текст комментария', 'source': subject]);</li>
     *     </ol>
     * </li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS2</li>
     *         <li>Код: testDPS2</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Добавление комментария к объекту</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: test</li>
     *         <li>Текст оповещения: test</li>
     *     </ol>
     * </li>
     * <li>Создать объект класса userClass</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Прикрепить файл (ссылка) к объекту (добавлен во вложения)</li>
     * <li>В отправленном письме в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор объекта userClass</li>
     * </ol>
     */
    @Test
    void testAutoFillReferencesHeaderOnCommentEvent()
    {
        //Подготовка
        ScriptInfo scriptForDps = DAOScriptInfo.createNewScriptInfo(
                "utils.create('comment', ['text': 'Текст комментария', 'source' : subject]);");
        DSLScriptInfo.addScript(scriptForDps);
        EventAction scriptActionEvent = DAOEventAction.createEventScript(EventType.addFile, scriptForDps.getCode(),
                true, TxType.Sync, userClass);

        EventAction notification = DAOEventAction.createNotification(userCase, EventType.addComment);
        notification.setSubject("test");
        notification.setMessage("test");
        notification.setEmails(server.getParams().getSystemMail());
        DSLEventAction.add(scriptActionEvent, notification);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Действия и проверки
        DSLFile.add(userBo, employee);
        DSLSmtpServer.assertNotStandartHeader(REFERENCES_TITLE, String.format(REFERENCE_TEMPLATE, userBo.getUuid()),
                server.getPortNumber());
    }

    /**
     * Тестирование автоматического заполнения UUID-ом объекта заголовка References в исходящем письме
     * при отправке оповещения по событию "Редактирование комментария"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS</li>
     *         <li>Код: testDPS</li>
     *         <li>Объекты: userClass</li>
     *         <li>Событие: Редактирование комментария</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: test</li>
     *         <li>Текст оповещения: test</li>
     *     </ol>
     * </li>
     * <li>Создать объект класса userClass</li>
     * <li>Добавить комментарий к созданному объекту</li>
     * <b>Действия и проверки:</b>
     * <li>Отредактировать добавленный комментарий</li>
     * <li>В отправленном письме в коде письма заголовок References содержит текст "\<nsmp:uuid\>", где uuid -
     * уникальный идентификатор объекта userClass</li>
     * </ol>
     */
    @Test
    void testAutoFillReferencesHeaderOnCommentEditEvent()
    {
        //Подготовка
        EventAction notification = DAOEventAction.createNotification(userCase, EventType.editComment);
        notification.setEmails(server.getParams().getSystemMail());
        DSLEventAction.add(notification);

        //Действия и проверки
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        String comment = DSLComment.add(userBo.getUuid());
        DSLComment.edit(comment, "test");

        DSLSmtpServer.assertNotStandartHeader(REFERENCES_TITLE, String.format(REFERENCE_TEMPLATE, userBo.getUuid()),
                server.getPortNumber());
    }

    /**
     * Тестирование автоматического заполнения UUID-ом объекта заголовка References в исходящем письме
     * при отправке оповещения по событиям "Связь с массовым объектом", "Связь с подчиненными объектами" и "Изменение
     * массовости"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип в классе Запрос</li>
     * <li>Создать два произвольных запроса - Запрос1 и Запрос2</li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS1</li>
     *         <li>Код: testDPS1</li>
     *         <li>Объекты: Запрос</li>
     *         <li>Событие: Связь с массовым объектом</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: testDPS1</li>
     *         <li>Текст оповещения: testDPS1</li>
     *     </ol>
     * </li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS2</li>
     *         <li>Код: testDPS2</li>
     *         <li>Объекты: Запрос</li>
     *         <li>Событие: Связь с подчиненными объектами</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: testDPS2</li>
     *         <li>Текст оповещения: testDPS2</li>
     *     </ol>
     * </li>
     * <li>Создать и включить ДПС:
     *     <ol>
     *         <li>Название: testDPS3</li>
     *         <li>Код: testDPS3</li>
     *         <li>Объекты: Запрос</li>
     *         <li>Событие: Изменение признака массовости</li>
     *         <li>Действие: Оповещение</li>
     *         <li>Кому: указать тестовый ящик на @nausd.local</li>
     *         <li>Тема: testDPS3</li>
     *         <li>Текст оповещения: testDPS3</li>
     *     </ol>
     * </li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Сделать Запрос1 массовым</li>
     * <li>Сделать Запрос2 подчиненным Запросу1</li>
     * <li>В отправленном письме по ДПС testDPS1 в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор Запроса2</li>
     * <li>В отправленном письме по ДПС testDPS2 в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор Запроса1</li>
     * <li>В отправленном письме по ДПС testDPS3 в коде письма заголовок References содержит текст "\<nsmp:uuid\>",
     *     где uuid - уникальный идентификатор Запроса1</li>
     * </ol>
     */
    @Test
    void testReferencesHeaderUuidForMassObjectOperations()
    {
        //Подготовка
        Bo scBo1 = DAOSc.create(scCase);
        Bo scBo2 = DAOSc.create(scCase);
        DSLBo.add(scBo1, scBo2);

        EventAction notification1 = DAOEventAction.createNotification(scClass, EventType.bindToMassMaster);
        notification1.setSubject("testDPS1");
        notification1.setMessage("testDPS1");
        notification1.setEmails(server.getParams().getSystemMail());

        EventAction notification2 = DAOEventAction.createNotification(scClass, EventType.bindMassSlave);
        notification2.setSubject("testDPS2");
        notification2.setMessage("testDPS2");
        notification2.setEmails(server.getParams().getSystemMail());

        EventAction notification3 = DAOEventAction.createNotification(scClass, EventType.changeMassAttr);
        notification3.setSubject("testDPS3");
        notification3.setMessage("testDPS3");
        notification3.setEmails(server.getParams().getSystemMail());
        DSLEventAction.add(notification1, notification2, notification3);

        //Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(scBo1);
        GUIMassProblem.setMass(scBo2);

        DSLSmtpServer.assertNotStandartHeaderBySubject(notification1.getSubject(), REFERENCES_TITLE,
                String.format(REFERENCE_TEMPLATE, scBo2.getUuid()), 3, server.getPortNumber());
        DSLSmtpServer.assertNotStandartHeaderBySubject(notification2.getSubject(), REFERENCES_TITLE,
                String.format(REFERENCE_TEMPLATE, scBo1.getUuid()), 3, server.getPortNumber());
        DSLSmtpServer.assertNotStandartHeaderBySubject(notification3.getSubject(), REFERENCES_TITLE,
                String.format(REFERENCE_TEMPLATE, scBo1.getUuid()), 3, server.getPortNumber());
    }

    /**
     * Тестирование добавления комментария к существующему запросу, найденному по заголовку References
     * из входящего письма, отправленного из действия по событию "Смена статуса".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00366
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277923419
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект класса Запрос1</li>
     * <li>Отредактировать в Запросе1 атрибут "Статус" на "В работе"</li>
     * <li>Настроить и включить входящую почту:
     *     <ol>
     *         <li>Сервер: 192.168.112.212</li>
     *         <li>Порт: 143</li>
     *         <li>Протокол: IMAP4</li>
     *         <li>Папки для сканирования: INBOX</li>
     *         <li>Логин: <EMAIL></li>
     *         <li>Пароль: 123qweASD</li>
     *     </ol>
     * </li>
     * <li>Добавить Правило обработки почты со следующим кодом:
     * <pre><code>
     * import ru.naumen.mailreader.server.queue.IInboundMailMessage
     * import java.util.regex.Matcher
     * import ru.naumen.core.shared.utils.UuidHelper
     *
     * //ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
     * public String getReferencesHeader(IInboundMailMessage message) {
     *     String referencesHeader = '';
     *     try {
     *         referencesHeader = message.getHeaders().get('References');
     *         if (api.string.isEmptyTrim(referencesHeader)) {
     *             referencesHeader = message.getHeaders().get('references');
     *         }
     *     } catch (e) {
     *         logger.warn(e.getMessage(), e);
     *     }
     *     return referencesHeader;
     * }
     *
     * public Collection<String> getReferencesIds(IInboundMailMessage message) {
     *     Collection<String> referencesIds = [];
     *     String referencesHeader = getReferencesHeader(message);
     *     if (!api.string.isEmptyTrim(referencesHeader)) {
     *         Collection<String> references = referencesHeader.findAll('<(.*?)>');
     *         for (String reference : references) {
     *             if (!api.string.isEmptyTrim(reference)) {
     *                 referencesIds.add(reference);
     *             }
     *         }
     *     }
     *     return referencesIds;
     * }
     *
     * //ОСНОВНОЙ БЛОК
     * logger.info("MAIL> start mail processing");
     * Collection<String> existingSCsUuids = utils.find('serviceCall', [:])*.UUID;
     * logger.info("MAIL> existingSCsUuids = ${existingSCsUuids}");
     * Collection<String> references = getReferencesIds(message);
     * logger.info("MAIL> references = ${references}");
     * def object
     * for (String reference : references) {
     *     Matcher matcher = reference =~ /nsmp:(\w+\$\d+)/;
     *     if (matcher.size() > 0) {
     *         String uuid = matcher[0][1];
     *
     *         if (UuidHelper.isValid(uuid) && UuidHelper.toPrefix(uuid) == 'serviceCall') {
     *             logger.info("MAIL> uuid from references = ${uuid}");
     *
     *             if (existingSCsUuids.contains(uuid)) {
     *                 logger.info("MAIL> adding comment to ${uuid}");
     *                 object = utils.get(uuid)
     *                 Map<String, Object> commentParams = [:];
     *                 commentParams.put('source', uuid);
     *                 commentParams.put('text', ' test References' );
     *                 commentParams.put('private', false);
     *                 utils.create('comment', commentParams);
     *             }
     *         }
     *     }
     * }
     * logger.info("MAIL> done");
     * </code></pre>
     * </li>
     * <li>В планировщике задач "Обработка входящей почты" изменить:
     *     <ol>
     *         <li>Сервер: 192.168.112.212</li>
     *         <li>Правило обработки: Правило обработки почты из пункта выше</li>
     *     </ol>
     * </li>
     * <li>Запустить планировщик "Обработка входящей почты"</li>
     * <b>Действия:</b>
     * <li>Войти в систему под naumen.</li>
     * <b>Проверка:</b>
     * <li>В Запрос1 добавился комментарий с текстом: "test References"</li>
     * </ol>
     */
    @Test
    void testAddCommentToRequestUsingReferencesHeader()
    {
        String scriptBody = """
                import ru.naumen.mailreader.server.queue.IInboundMailMessage
                import java.util.regex.Matcher
                import ru.naumen.core.shared.utils.UuidHelper
                
                //ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
                public String getReferencesHeader(IInboundMailMessage message)
                {
                    String referencesHeader = '';
                    try
                    {
                        referencesHeader = message.getHeaders().get('References');
                        if (api.string.isEmptyTrim(referencesHeader))
                        {
                            referencesHeader = message.getHeaders().get('references');
                        }
                    }
                    catch (e)
                    {
                        logger.warn(e.getMessage(), e);
                    }
                    return referencesHeader;
                }
                
                public Collection<String> getReferencesIds(IInboundMailMessage message)
                {
                    Collection<String> referencesIds = [];
                    String referencesHeader = getReferencesHeader(message);
                    if (!api.string.isEmptyTrim(referencesHeader))
                    {
                        Collection<String> references = referencesHeader.findAll('<(.*?)>');
                        for (String reference : references)
                        {
                            if (!api.string.isEmptyTrim(reference))
                            {
                                referencesIds.add(reference);
                            }
                        }
                    }
                    return referencesIds;
                }
                
                //ОСНОВНОЙ БЛОК
                logger.info("MAIL> start mail processing");
                Collection<String> existingSCsUuids = utils.find('serviceCall', [:])*.UUID; //крайне неоптимально, только для теста
                logger.info("MAIL> existingSCsUuids = ${existingSCsUuids}");
                Collection<String> references = getReferencesIds(message);
                logger.info("MAIL> references = ${references}");
                def object
                for (String reference : references)
                {
                    Matcher matcher = reference =~ /nsmp:(\\w+\\$\\d+)/;
                    if (matcher.size() > 0)
                    {
                        String uuid = matcher[0][1];
                       \s
                        if (UuidHelper.isValid(uuid) && UuidHelper.toPrefix(uuid) == 'serviceCall')
                        {
                            logger.info("MAIL> uuid from references = ${uuid}");
                           \s
                            if (existingSCsUuids.contains(uuid))
                            {
                                logger.info("MAIL> adding comment to ${uuid}");
                                object = utils.get(uuid)
                                //Далее логика добавления комментария к запросу...
                                Map<String, Object> commentParams = [:];
                                commentParams.put('source', uuid);
                                commentParams.put('text', ' test References' );
                                commentParams.put('private', false);
                                utils.create('comment', commentParams);
                            }
                         }
                    }
                }
                
                logger.info("MAIL> done");
                """;

        //Подготовка
        InboundMailConnection connection = DAOSchedulerTask.createInboundMailConnection(MailProtocol.IMAP4, false);
        Bo scBo1 = DAOSc.create(scCase);
        DSLBo.add(scBo1);

        BoStatus state = DAOBoStatus.createUserStatus(scClass, "inprogress",
                "В работе");
        DSLBoStatus.add(state);
        BoStatus resumed = DAOBoStatus.createResumed(scClass);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scClass), state, resumed);
        DSLSc.changeState(scBo1, state);
        Cleaner.afterTest(true, () -> DSLSc.changeState(scBo1, resumed));

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        SchedulerTask task = DAOSchedulerTask.createReceiveMailTask(true, connection, script);
        DSLSchedulerTask.addReceiveMailTask(task, script);
        DSLSchedulerTask.addTask(task);

        Email email = DAOEmail.createEmailForSend();
        email.setHeader(String.format("%s: " + REFERENCE_TEMPLATE, REFERENCES_TITLE, scBo1.getUuid()));
        DSLSchedulerTask.addMailMessageToQueue(task, email);
        DSLSchedulerTask.forceRunAndWaitExecuted(task);

        //Действия и проверки
        ContentForm commentList = DAOContentCard.createCommentList(scCase.getFqn());
        DSLContent.add(commentList);

        GUILogon.asSuper();
        GUIBo.goToCard(scBo1);
        GUIComment.assertCommentPresent(commentList, "test References");
    }

    /**
     * Очищает почтовый сервер после каждого теста, удаляя все полученные сообщения
     */
    @AfterEach
    void afterEach()
    {
        DSLSmtpServer.deleteMessages(server.getPortNumber());
    }

    @AfterAll
    static void afterAll()
    {
        new ScriptRunner(String.format(SET_REFERENCES_HEADER, false)).runScript();
    }
}
