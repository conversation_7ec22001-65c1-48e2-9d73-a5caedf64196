package ru.naumen.selenium.cases.operator.embeddedapplication.forms;

import java.io.File;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;

/**
 * Тестирование разрешения конфликтов, когда на один и тот же атрибут зарегистрировано несколько обработчиков
 * сохранения атрибутов через jsApi (jsApi.registerAttributeConflictResolver)
 *
 * <AUTHOR>
 * @since 25.10.2021
 */
class JsApiRegisterAttributeConflictResolverTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static final String ATTRIBUTE_RESOLVER_NOT_FOUND = "Сохранение невозможно. Обратитесь к администратору "
                                                               + "системы.";

    private static final String REGISTER_ATTRIBUTE_TO_MODIFICATION_TEMPLATE = "var attributeCode = '%s'\n"
                                                                              + "jsApi"
                                                                              + ".registerAttributeToModification"
                                                                              + "(attributeCode, () => '%s')\n";
    private static final String REGISTER_CONFLICT_RESOLVER_TEMPLATE = REGISTER_ATTRIBUTE_TO_MODIFICATION_TEMPLATE
                                                                      + "jsApi.registerAttributeConflictResolver"
                                                                      + "(attributeCode, (values) => values[0] + ' ' "
                                                                      + "+ values[1])\n";
    private static final String REGISTER_CONFLICT_RESOLVER_WITH_ERROR_TEMPLATE =
            REGISTER_ATTRIBUTE_TO_MODIFICATION_TEMPLATE
            + "jsApi.registerAttributeConflictResolver(attributeCode, (values) => {\n"
            + "  throw new Error('%s')\n"
            + "})\n";

    private static MetaClass userClass, userCase;
    private static Attribute stringAttr;
    private static ContentForm content;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать тип scCase класса Запрос</li>
     * <li>Создать класс <code>userClass</code> и его тип <code>userCase</code></li>
     * <li>Создать строковый атрибут <code>stringAttr</code> без значения по умолчанию в классе
     * <code>userClass</code></li>
     * <li>Добавить <code>stringAttr</code> в системную группу атрибутов класса userClass</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);

        content = DSLContent.getDefaultCardContent(userCase);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.addToGroup(attrsGroup, stringAttr);
    }

    /**
     * Тестирование сохранения формы добавления, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и есть функция разрешения конфликтов
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => '$expectedText')
     *   jsApi.registerAttributeConflictResolver(attributeCode, (values) => values[0] + ' ' + values[1])
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, которую необходимо присвоить атрибуту.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму добавления
     * <code>userClass</code></li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса <code>userClass</code></li>
     * <li>Заполнить поле "Название" случайным значением</li>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что на карточке созданного объекта атрибут <code>stringAttr</code> имеет значение
     * "$expectedText $expectedText"</li>
     * </ol>
     */
    @Test
    void testResolveConflictAttributeEditingViaEmbeddedApplicationOnAddForm()
    {
        // Подготовка
        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_CONFLICT_RESOLVER_TEMPLATE, stringAttr.getCode(), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());

        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);

        GUIForm.applyForm();
        DAOBo.createModelByUuid(GUIBo.getUuidByUrl());

        // Проверки
        stringAttr.setValue(expectedText + " " + expectedText);
        GUIContent.assertContentAttributeValue(content, stringAttr);
    }

    /**
     * Тестирование сохранения формы добавления, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и функция разрешения конфликтов возвращает ошибку
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => %случайная строка%')
     *   jsApi.registerAttributeConflictResolver(attributeCode, (values) => {
     *       throw new Error('$expectedText')
     *   })
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, выбросить в качестве ошибки разрешения конфликта.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму добавления
     * <code>userClass</code></li>
     * <br>
     * <b>Действия.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса <code>userClass</code></li>
     * <li>Заполнить поле "Название" случайным значением</li>
     * <li>Попытаться сохранить форму</li>
     * <br>
     * <b>Проверки.</b>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Проверить, что форма не сохранилась и появилась ошибка с текстом "$expectedText"</li>
     * </ol>
     */
    @Test
    void testResolveConflictAttributeEditingWithErrorViaEmbeddedApplicationOnAddForm()
    {
        // Подготовка
        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_CONFLICT_RESOLVER_WITH_ERROR_TEMPLATE,
                stringAttr.getCode(), ModelUtils.createText(20), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());

        // Проверки
        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);

        GUIForm.applyFormAssertError(expectedText);
    }

    /**
     * Тестирование сохранения формы добавления, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и нет функции разрешения конфликтов
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => '$expectedText')
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, которую необходимо присвоить атрибуту.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму добавления
     * <code>userClass</code></li>
     * <br>
     * <b>Действия.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса <code>userClass</code></li>
     * <li>Заполнить поле "Название" случайным значением</li>
     * <li>Попытаться сохранить форму</li>
     * <br>
     * <b>Проверки.</b>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Проверить, что форма не сохранилась и появилась системная ошибка</li>
     * </ol>
     */
    @Test
    void testConflictAttributeEditingViaEmbeddedApplicationOnAddForm()
    {
        // Подготовка
        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_ATTRIBUTE_TO_MODIFICATION_TEMPLATE,
                stringAttr.getCode(), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentAddForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());

        // Проверки
        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);

        GUIForm.applyFormAssertError(ATTRIBUTE_RESOLVER_NOT_FOUND);
    }

    /**
     * Тестирование сохранения формы редактирования, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и есть функция разрешения конфликтов
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => '$expectedText')
     *   jsApi.registerAttributeConflictResolver(attributeCode, (values) => values[0] + ' ' + values[1])
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, которую необходимо присвоить атрибуту.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму
     * редактирования <code>userClass</code></li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму редактирования объекта <code>userBo</code></li>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что на карточке объекта userBo атрибут <code>stringAttr</code> имеет значение
     * "$expectedText $expectedText"</li>
     * </ol>
     */
    @Test
    void testResolveConflictAttributeEditingViaEmbeddedApplicationOnEditForm()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_CONFLICT_RESOLVER_TEMPLATE, stringAttr.getCode(), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);

        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);
        GUIForm.applyForm();

        stringAttr.setValue(expectedText + " " + expectedText);
        GUIContent.assertContentAttributeValue(content, stringAttr);
    }

    /**
     * Тестирование сохранения формы редактирования, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и функция разрешения конфликтов возвращает ошибку
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => %случайная строка%')
     *   jsApi.registerAttributeConflictResolver(attributeCode, (values) => {
     *       throw new Error('$expectedText')
     *   })
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, выбросить в качестве ошибки разрешения конфликта.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму
     * редактирования <code>userClass</code></li>
     * <br>
     * <b>Действия.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму редактирования объекта <code>userBo</code></li>
     * <li>Попытаться сохранить форму</li>
     * <br>
     * <b>Проверки.</b>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Проверить, что форма не сохранилась и появилась ошибка с текстом "%expectedText%"</li>
     * </ol>
     */
    @Test
    void testResolveConflictAttributeEditingWithErrorViaEmbeddedApplicationOnEditForm()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_CONFLICT_RESOLVER_WITH_ERROR_TEMPLATE,
                stringAttr.getCode(), ModelUtils.createText(20), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);

        // Проверки
        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);

        GUIForm.applyFormAssertError(expectedText);
    }

    /**
     * Тестирование сохранения формы редактирования, когда несколько встроенных приложений пытаются сохранить
     * значение одного и того же атрибута и нет функции разрешения конфликтов
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138038540
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>Создать объект <code>userBo</code> типа userCase</li>
     * <li>Сгенерировать случайную строку <code>expectedText</code></li>
     * <li>Добавить исполняемое на клиенте встроенное приложение application с JavaScript кодом:
     * <pre>
     *   -----------------------------------------------------------------------
     *   var attributeCode = '$stringAttr'
     *   jsApi.registerAttributeToModification(attributeCode, () => '$expectedText')
     *   -----------------------------------------------------------------------
     *   Где:
     *   1) $stringAttr - код атрибута <code>$stringAttr</code>;
     *   2) $expectedText - случайная строка <code>expectedText</code>, которую необходимо присвоить атрибуту.
     * </pre>
     * <li>Включить добавленное встроенное приложение
     * <li>Добавить два контента типа "Встроенное приложение" c приложением <code>application</code> на форму
     * редактирования <code>userClass</code></li>
     * <br>
     * <b>Действия.</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на форму редактирования объекта <code>userBo</code></li>
     * <li>Попытаться сохранить форму</li>
     * <br>
     * <b>Проверки.</b>
     * <li>Проверить, что на форме присутствуют и загрузились оба контента с ВП <code>application</code></li>
     * <li>Проверить, что форма не сохранилась и появилась системная ошибка</li>
     * </ol>
     */
    @Test
    void testConflictAttributeEditingViaEmbeddedApplicationOnEditForm()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        String expectedText = ModelUtils.createText(20);
        String jsContent = String.format(REGISTER_ATTRIBUTE_TO_MODIFICATION_TEMPLATE,
                stringAttr.getCode(), expectedText);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        ContentForm applicationContent2 = DAOContentEditForm.createEmbeddedApplication(userClass, application);
        DSLContent.add(applicationContent, applicationContent2);

        // Действия
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);

        // Проверки
        GUIContent.assertPresent(applicationContent, applicationContent2);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent2);

        GUIForm.applyFormAssertError(ATTRIBUTE_RESOLVER_NOT_FOUND);
    }
}