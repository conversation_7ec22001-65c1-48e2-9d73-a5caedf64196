package ru.naumen.selenium.cases.operator.dynamicfield;

import static ru.naumen.selenium.casesutil.file.DSLFile.DYNFIELD_CATALOG_PATH;

import java.util.List;

import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListContent;
import ru.naumen.selenium.casesutil.dynamicfield.DSLDynamicFieldConfiguration;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.StringType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAODynamicField;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.presentation.DAOPresentation;
import ru.naumen.selenium.casesutil.model.content.presentation.Presentation;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.FileUtils;

/**
 * Тестирование динамических полей
 *
 * <AUTHOR>
 * @since 01.11.2024
 */
class DynamicFields2Test extends AbstractTestCaseJ5
{
    /**
     * Тестирование отображения атрибутов в списках в различных представлениях
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$238328264
     * <ol>
     * <b>Подготовка:</b>
     * <li>Загрузить на стенд лицензионный файл с модулем Динамические поля (dynamicField)</li>
     * <li>Создать пользовательский класс "Пользовательский класс" (код attrs) и вложенными в него типами:<ul>
     *     <li>"Строка" (код attrString)</li>
     *     <li>"Дата/время" (код attrDateTime)</li>
     *     <li>"Логический" (код attrBool)</li>
     * </ul></li>
     * <li>Создать пользовательский справочник "Представление" (код visor) с элементами:<ul>
     *     <li>"Дата/время" (код attrDateTimeView)</li>
     *     <li>"Дата/время (с секундами)" (код attrDateTimeWithSecondsView)</li>
     *     <li>"Дата/время (с миллисекундами)" (код attrDateTimeWithMillisView)</li>
     *     <li>"Логический 1/0" (код attrOneZero)</li>
     *     <li>"Логический Да/Нет" (код attrYesNo)</li>
     *     <li>"Строка" (код attrStringView)</li>
     *     <li>"Строка (пароль)" (код attrPasswordView)</li>
     * </ul></li>
     * <li>В класс Пользовательский класс (attrs) добавить атрибуты:<ul>
     *     <ul>
     *         <li>Название = Очередность</li>
     *         <li>Код = sequenceAttr</li>
     *         <li>Тип = Целое число</li>
     *     </ul>
     *     <ul>
     *         <li>Название = Представление</li>
     *         <li>Код = visor</li>
     *         <li>Тип = Элемент справочника</li>
     *         <li>Справочник = Представление</li>
     *     </ul>
     *     <ul>
     *         <li>Название = Видимый в списках</li>
     *         <li>Код = visible</li>
     *         <li>Тип = Логический</li>
     *         <li>Значение по умолчанию = да</li>
     *     </ul>
     * </ul></li>
     * <li>Загрузить на стенд файл конфигурации динамических атрибутов dynamic-fields_NSDAT-32020.xml</li>
     * <li>Выполнить скрипт загрузки конфигурации из файла, который прикрепили, где file$XXX - UUID этого файла<br>
     * beanFactory.getBean('dynamicFieldConfigurationLoader').reloadFromFile('file$XXX')</li>
     * <li>Создать объекты класса Пользовательский класс, название атрибута совпадает с названием типа объекта, который
     * создаём:<ul>
     *     <li>"Дата/время" атрибут visor заполнить значением "Дата/время"</li>
     *     <li>"Дата/время (с секундами)" атрибут visor заполнить значением "Дата/время (с секундами)"</li>
     *     <li>"Дата/время (с миллисекундами)" атрибут visor заполнить значением "Дата/время (с миллисекундами)"</li>
     *     <li>"Логический 1/0" атрибут visor заполнить значением "Логический 1/0"</li>
     *     <li>"Логический Да/Нет" атрибут visor заполнить значением "Логический Да/Нет"</li>
     *     <li>"Строка" (атрибут visor заполнить значением "Строка")</li>
     *     <li>"Строка (пароль)" атрибут visor заполнить значением "Строка (пароль)"</li>
     * </ul></li>
     * <li>Создать класс userClass с типом userCase</li>
     * <li>В класс userClass добавить атрибут dynAttrs, тип = Значения динамических полей и вывести его в группу
     * атрибутов Системные атрибуты</li>
     * <li>Вывести на карточку Сотрудника, контент типа Список объектов: Название/код = userList, Класс = userClass,
     * Группа = Системные атрибуты</li>
     * <li>Создать объект User типа userCase</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта User<br>
     * utils.edit('UUID объекта User', [dynAttrs: [<br>
     *     'UUID объекта Дата/время': [value: '2024.07.30 17:12'],<br>
     *     'UUID объекта Дата/время (с секундами)': [value: '2024.08.12 17:12:15'],<br>
     *     'UUID объекта Дата/время (с миллисекундами)': [value: '2024.09.15 17:12:15.120'],<br>
     *     'UUID объекта Логический 1/0': [value: 'true'],<br>
     *     'UUID объекта Логический Да/Нет': [value: 'false'],<br>
     *     'UUID объекта Строка': [value: 'Строка простая'],<br>
     *     'UUID объекта Строка (пароль)': [value: 'Пароль']<br>
     * ]])</li>
     * </ol>
     * <b>Действия:</b>
     * <ol>
     * <li>Войти в ИО</li>
     * </ol>
     * <b>Проверки:</b>
     * <ol>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Дата/время установлено значение
     * "30.07.2024 17:12"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Дата/время (с секундами) установлено
     * значение "12.08.2024 17:12:15"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Дата/время (с миллисекундами) установлено
     * значение "15.09.2024 17:12:15.120"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Логический 1/0 установлено значение
     * "1"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Логический Да/Нет установлено значение
     * "Нет"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Строка установлено значение
     * "Строка простая"</li>
     * <li>В контенте userList в ячейке на пересечении строки User и столбца Строка (пароль) нет значения "Пароль"
     * //отображаются 6 точек</li>
     * </ol>
     */
    @Test
    void testDisplayAttributesInListsInAnyViews()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
        MetaClass attrClass = DAOUserClass.createWithCode("attrs");
        MetaClass stringCase = DAOUserCase.create(attrClass, "attrString");
        MetaClass dateTimeCase = DAOUserCase.create(attrClass, "attrDateTime");
        MetaClass boolCase = DAOUserCase.create(attrClass, "attrBool");
        DSLMetaClass.add(attrClass, stringCase, dateTimeCase, boolCase);

        Catalog catalog = DAOCatalog.createUser(false, false);
        catalog.setCode("visor");
        DSLCatalog.add(catalog);
        List<DataForTest> dataForTest = List.of(
                DataForTest.DATE_TIME.setMetaClass(dateTimeCase),
                DataForTest.DATE_TIME_SEC.setMetaClass(dateTimeCase),
                DataForTest.DATE_TIME_MILLS.setMetaClass(dateTimeCase),
                DataForTest.BOOL_ONE_ZERO.setMetaClass(boolCase),
                DataForTest.BOOL_YES_NO.setMetaClass(boolCase),
                DataForTest.STRING.setMetaClass(stringCase),
                DataForTest.PASSWORD.setMetaClass(stringCase)
        );

        for (DataForTest data : dataForTest)
        {
            CatalogItem catalogItem = DAOCatalogItem.createUser(catalog);
            catalogItem.setCode(data.codeAttr);
            DSLCatalogItem.add(catalogItem);
            data.setCatalogItem(catalogItem);
        }

        Attribute intAtr = DAOAttribute.createInteger(attrClass);
        intAtr.setTitle("Очередность");
        intAtr.setCode("sequenceAttr");
        Attribute userCatalog = DAOAttribute.createCatalogItem(attrClass, catalog, null);
        userCatalog.setTitle("Представление");
        userCatalog.setCode("visor");
        Attribute booleanAttr = DAOAttribute.createBool(attrClass, true);
        booleanAttr.setViewPresentation(BooleanType.VIEW_YES_NO);
        booleanAttr.setTitle("Видимый в списках");
        booleanAttr.setCode("visible");
        DSLAttribute.add(intAtr, userCatalog, booleanAttr);

        String configXml = FileUtils.readAll(DYNFIELD_CATALOG_PATH + "dynamic-fields_NSDAT-32020.xml");
        DSLDynamicFieldConfiguration.reload(configXml);
        Cleaner.afterTest(true, () -> ScriptRunner.executeScript("api.dynfield.reloadConfiguration()"));

        for (DataForTest data : dataForTest)
        {
            Bo userBo = DAOUserBo.create(data.metaClass, userCatalog, data.catalogItem.getUuid());
            DSLBo.add(userBo);
            data.setBo(userBo);
        }

        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute dynamicAttr = DAOAttribute.createDynamic(userClass);
        dynamicAttr.setCode("dynAttrs");
        DSLAttribute.add(dynamicAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { dynamicAttr }, new Attribute[] {});
        ContentForm objectList = DAOContentCard.createObjectList(DAOEmployeeCase.createClass().getFqn(), userClass);
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        StringBuilder buf = new StringBuilder();
        buf.append(String.format("utils.edit('%s', [dynAttrs: [\n", userBo.getUuid()));
        for (DataForTest data : dataForTest)
        {
            buf.append(String.format("'%s': [value: '%s'],\n", data.bo.getUuid(), data.valueAttr));
        }
        buf.append("]])");
        ScriptRunner.executeScript(buf.toString());

        // Действия и проверки
        GUILogon.asTester();
        AGUIAdvListContent contentAssert = new GUIAdvListUtil(objectList.getXpathId()).content().asserts();
        for (DataForTest data : dataForTest)
        {
            contentAssert.attrValue(userBo, DAOAttribute.createPseudo("",
                    userClass.getFqn() + "@dynAttrs/" + data.bo.getIdByUUID(), data.expectedValue));
        }
    }

    /**
     * Тестирование отсутствия ошибки "java.lang.IllegalArgumentException:Formatter not registered for…" в ИО, если
     * явно задан тип значения динамического поля, который не соответствует типу шаблона динамических полей, а также
     * настроена фильтрация и сохранен вид списка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00101
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00974
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$306400693
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить на стенд лицензионный файл с модулем Динамические поля (dynamicField)</li>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут jsonAttr типа «Значения динамических полей»</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и jsonAttr</li>
     * <li>Создать шаблон динамических полей dynStringAttr типа «Строка» с представлением для отображения «Строка»</li>
     * <li>На карточке компании создать сложный список объектов listUserClassObjects (класс: userClass, группа
     * атрибутов: attrGroup</li>
     * <li>С помощью скрипта создать объект типа userCase и заполнить в нем динамическое поле dynStringAttrBo атрибута
     * jsonAttr:
     * <pre>
     *    utils.create('%userCase.getFqn()%', [title: 'U1', %jsonAttr.getCode()%: [
     *                 '%dynStringAttrBo.getUuid()%': 'string value']])
     *
     * </pre>
     * </li>
     * <li>С помощью скрипта создать объект типа userCase и заполнить в нем динамическое поле dynStringAttrBo атрибута
     * jsonAttr явно указав тип значения - Integer:
     * <pre>
     *     utils.create('%userCase.getFqn()%', [title: 'U2', %jsonAttr.getCode()%: [
     *                 ['%dynStringAttrBo.getUuid()%': [integer: [value: 42]]]])
     *
     * </pre>
     * </li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти по суперпользователем</li>
     * <li>Перейти на карточку компании</li>
     * <li>В списке объектов listUserClassObjects настроить фильтрацию по системному атрибуту "Название", условие -
     * "Содержит", значение - "U"</li>
     * <li>Сохранить вид списка под именем "U" и обновить страницу</li>
     * <li>Проверить отсутствие ошибок</li>
     * </ol>
     */
    @Test
    void testFilterAdvListWithDynamicFieldsAndSavedView()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute jsonAttr = DAOAttribute.createDynamic(userClass);
        DSLMetainfo.add(userClass, userCase, jsonAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        Attribute titleAttr = SysAttribute.title(userClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(userClass), jsonAttr);

        Bo dynStringAttrBo = DAODynamicField.create(StringType.CODE, StringType.VIEW, StringType.EDIT);
        DSLBo.add(dynStringAttrBo);

        ContentForm listUserClassObjects = DAOContentCard.createObjectList(SystemClass.ROOT.getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup);
        DSLContent.add(listUserClassObjects);

        String scriptPattern = """
                utils.create('%s', [title: 'U1', %s: [
                '%s': 'string value']])
                """;
        String scriptPattern2 = """
                utils.create('%s', [title: 'U2', %s: 
                ['%s': [integer: [value: 42]]]])
                """;

        String scriptTemplate = String.format(scriptPattern, userCase.getFqn(), jsonAttr.getCode(),
                dynStringAttrBo.getUuid());
        String scriptTemplate2 = String.format(scriptPattern2, userCase.getFqn(), jsonAttr.getCode(),
                dynStringAttrBo.getUuid());

        ScriptRunner script = new ScriptRunner(scriptTemplate);
        script.runScript();

        script = new ScriptRunner(scriptTemplate2);
        script.runScript();

        Cleaner.afterTest(true,
                () -> new ScriptRunner(String.format("utils.find('%s', [:]).forEach{utils.delete(it)};",
                        userCase.getFqn())).runScript());

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(SharedFixture.root());
        GUIAdvListUtil advList = listUserClassObjects.advlist();
        advList.toolPanel().clickFiltering();
        advList.toolPanel().filtering().addAttr(titleAttr, 1, 1, FilterCondition.CONTAINS);
        advList.toolPanel().filtering().setString(1, 1, "U");
        advList.toolPanel().filtering().clickApply();

        advList.toolPanel().clickSavePrs();
        advList.prs().saveView().setTitle("U");
        GUIForm.applyModalForm();

        tester.refresh();
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование отсутствия ошибки "Cannot invoke "com.google.gson.JsonObject.entrySet()" because "value" is null"
     * при выставлении значения динамического атрибута равным null
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$287625049
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить на стенд лицензионный файл с модулем Динамические поля (dynamicField)</li>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут jsonAttr типа «Значения динамических полей»</li>
     * <li>С помощью скрипта создать объект типа userCase и заполнить в нем динамический jsonAttr:
     * <pre>
     *     utils.create('%userCase.getFqn()%', [title: 'U1', %jsonAttr.getCode()%: [:])?.UUID
     * </pre>
     * </li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти по суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт
     * <pre>
     *     utils.edit('%objectUuid%', [ %jsonAttr.getCode()%: null)
     * </pre>
     * <li>Проверить отсутствие ошибок</li>
     * </ol>
     */
    @Test
    void testClearDynamicFieldValue()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute jsonAttr = DAOAttribute.createDynamic(userClass);
        DSLMetainfo.add(userClass, userCase, jsonAttr);

        String scriptCreatePattern = "utils.create('%s', ['title': 'U1', '%s': [:]])?.UUID";

        String scriptCreateTemplate = String.format(scriptCreatePattern, userCase.getFqn(), jsonAttr.getCode());

        ScriptRunner script = new ScriptRunner(scriptCreateTemplate);
        List<String> result = script.runScript();
        String objectUuid = result.getFirst();

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIConsole.goToConsole();

        String scriptEditPattern = "utils.edit('%s', [ '%s': null])";
        String scriptEditTemplate = String.format(scriptEditPattern, objectUuid, jsonAttr.getCode());
        script = new ScriptRunner(scriptEditTemplate);
        script.runScript();

        GUIError.expectError();

        Cleaner.afterTest(true,
                () -> new ScriptRunner(String.format("utils.delete('%s')", objectUuid)).runScript());
    }

    /**
     * Тестирование отображение колонок в сохраненном виде в сложного списка, если выведен атрибут типа "Значение
     * динамических полей", в списке с объектами, у которых отсутствует это значение атрибута дин.полей"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$293514105
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить на стенд лицензионный файл с модулем Динамические поля (dynamicField)</li>
     * <li>Создать пользовательский класс userClass и вложенные типы userCase1, userCase2</li>
     * <li>В классе userClass создать атрибут dinAttr типа "Значение динамических полей"</li>
     * <li>Создать группу атрибутов attrGroup в классе userClass и добавить в неё Название и dinAttr</li>
     * <li>На карточку компании добавить контент objectList:
     * <ul>
     *     <li>тип - Список объектов</li>
     *     <li>представление контент - Сложный список</li>
     *     <li>группа атрибутов - attrGroup</li>
     *     <li>классы объектов для отображения в списке - userClass</li>
     * </ul>
     * </li>
     * <li>На карточку компании добавить контент objectListCase:
     * <ul>
     *     <li>тип - Список объектов</li>
     *     <li>представление контент - Сложный список</li>
     *     <li>группа атрибутов - attrGroup</li>
     *     <li>классы объектов для отображения в списке - userClass</li>
     *     <li>типов объектов отображаемых в списке - userCase2</li>
     * </ul>
     * </li>
     * <li>Создать модель шаблона динамического атрибута dynStringAttrBo типа строка</li>
     * <li>Создать объект userBo1 типа userCase1</li>
     * <li>Создать объект userBo3 типа userCase2</li>
     * <li>Заполнить атрибут dynAttrs в userBo1 выполнив скрипт:
     * <pre>
     *     utils.edit({userBo1.uuid}, [dynAttrs: [
     *                   '#commonGroup': [
     *                    {dynStringAttrBo.uuid}: [value: '1111111'],
     *                   ]
     *             ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИО под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>В списке objectList нажать кнопку "Сохранить вид"</li>
     * <li>На форме сохранения вида заполнить название и нажать сохранить</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в списке objectListCase отображается только колонка с названием "Название"</li>
     * <li>В списке objectListCase выбрать вид сохраненный ранее</li>
     * <li>Проверить, что в списке objectListCase отображаются только колонки с названием и в заданном подярке:
     * "Название" и {dynStringAttrBo.title}</li>
     * </ol>
     */
    @Test
    void testPresentEmptyDinColumnInPresentationAdvlist()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        Attribute dinAttr = DAOAttribute.createDynamic(userClass);
        dinAttr.setCode("dynAttrs");
        DSLMetainfo.add(userClass, userCase1, userCase2, dinAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(userClass), dinAttr);

        ContentForm objectList = DAOContentCard.createObjectList(SystemClass.ROOT.getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup);
        DSLContent.add(objectList);

        ContentForm objectListCase = DAOContentCard.createObjectList(SystemClass.ROOT.getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup, userCase2);
        DSLContent.add(objectListCase);

        Bo dynStringAttrBo = DAODynamicField.create(StringType.CODE, StringType.VIEW, StringType.EDIT);
        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(dynStringAttrBo, userBo1, userBo2);

        String scriptPattern = """
                utils.edit('%s', [dynAttrs: [
                  '#commonGroup': [
                   '%s': [value: '1111111'],
                  ]
                ]])""";
        ScriptRunner.executeScript(scriptPattern, userBo1.getUuid(), dynStringAttrBo.getUuid());

        //Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().toolPanel().clickSavePrs();
        String viewTitle = ModelUtils.createText(10);
        objectList.advlist().prs().saveView().setTitle(viewTitle);
        GUIForm.applyForm();

        tester.refresh();
        objectListCase.advlist().content().asserts().columnNames(true, true, "Название");

        Presentation prs = DAOPresentation.createPublic(objectListCase);
        prs.setTitle(viewTitle);
        objectListCase.advlist().prs().selectByTitle(prs);
        objectListCase.advlist().content().asserts().columnNames(true, true, "Название", dynStringAttrBo.getTitle());
    }

    /**
     * Тип действия
     */
    private enum DataForTest
    {
        DATE_TIME("attrDateTimeView", "2024.07.30 17:12", "30.07.2024 17:12"),
        DATE_TIME_SEC("attrDateTimeWithSecondsView", "2024.08.12 17:12:15", "12.08.2024 17:12:15"),
        DATE_TIME_MILLS("attrDateTimeWithMillisView", "2024.09.15 17:12:15.120", "15.09.2024 17:12:15.120"),
        BOOL_ONE_ZERO("attrOneZero", "true", "1"),
        BOOL_YES_NO("attrYesNo", "false", "нет"),
        STRING("attrStringView", "Строка простая", "Строка простая"),
        PASSWORD("attrPasswordView", "Пароль", "••••••••");

        public String codeAttr;
        public String valueAttr;
        public String expectedValue;
        public MetaClass metaClass;
        public CatalogItem catalogItem;
        public Bo bo;

        DataForTest(String code, String valueAttr, String expectedValue)
        {
            this.codeAttr = code;
            this.valueAttr = valueAttr;
            this.expectedValue = expectedValue;
        }

        public DataForTest setMetaClass(MetaClass metaClass)
        {
            this.metaClass = metaClass;
            return this;
        }

        public DataForTest setCatalogItem(CatalogItem catalogItem)
        {
            this.catalogItem = catalogItem;
            return this;
        }

        public DataForTest setBo(Bo bo)
        {
            this.bo = bo;
            return this;
        }
    }
}
