package ru.naumen.selenium.cases.operator.unlicensed;

import org.junit.Before;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityProfile;
import ru.naumen.selenium.security.UserGroup;

/**
 * Тесты на права пользовательских атрибутов у нелицензионного пользователя
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
 * <AUTHOR>
 * @since 19 февр. 2014 г.
 *
 */
public class FlexRightsTest extends AbstractTestCase
{

    private Bo employee;

    /**
     * <b>Общая подготовка</b>
     * <br>
     * <ol>
     *
     * <li>Создать нелицензированного пользователя, под которым будем проверять права</li>
     * </ol>
     */
    @Before
    public void setUp()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        employee.setLicenseCode(DAOBo.NO_LICENSE_SET);
        employee.setPassword(ModelUtils.createPassword());
        DSLBo.add(employee);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип соглашения agrCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyAgreement()
    {
        MetaClass agrClass = DAOAgreementCase.createClass();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(agrCase);
        Bo bo = DAOAgreement.create(agrCase);
        DSLBo.add(bo);
        checkAttrTypePolicy(agrClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyEmployee()
    {
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);
        Bo bo = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(bo);
        checkAttrTypePolicy(employeeClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для отдела
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyOU()
    {
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        Bo bo = DAOOu.create(ouCase);
        DSLBo.add(bo);
        checkAttrTypePolicy(ouClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для Компании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyRoot()
    {
        MetaClass rootClass = DAORootClass.create();
        Bo bo = SharedFixture.root();
        checkAttrTypePolicy(rootClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип запроса scCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyServiceCall()
    {
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        DSLAgreement.addRecipients(employee, SharedFixture.agreement());
        Bo bo = DAOSc.create(scCase, employee, SharedFixture.agreement(), SharedFixture.timeZone());
        DSLBo.add(bo);
        checkAttrTypePolicy(scClass, true, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для услуги
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип услуги slmCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicySlmService()
    {
        MetaClass slmClass = DAOServiceCase.createClass();
        MetaClass slmCase = DAOServiceCase.create();
        DSLMetaClass.add(slmCase);
        Bo bo = DAOService.create(slmCase);
        DSLBo.add(bo);
        checkAttrTypePolicy(slmClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать тип команды teamCase</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyTeam()
    {
        MetaClass teamClass = DAOTeamCase.createClass();
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(teamCase);
        Bo bo = DAOTeam.create(teamCase);
        DSLBo.add(bo);
        checkAttrTypePolicy(teamClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * http://sd-jira.naumen.ru/browse/NSDPRD-2576
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass и пользовательский тип userType</li>
     * <li>{@link #checkAttrTypePolicy() Общие действия }</li>
     * </ol>
     */
    @Test
    public void testAttrTypePolicyUserClass()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
        checkAttrTypePolicy(userClass, false, bo);
    }

    /**
     * Тестирование отсутствия прав на типы пользовательских атрибутов для конкретного класса metaClass и типа
     * metaCase БО
     * <b>Подготовка</b>
     * <li>Добавить в metaClass атрибуты всех возможных типов</li>
     * <li>Добавить группу атрибутов attrGroup, включить в неё все созданные атрибуты</li>
     * <li>Добавить на карточку metaClass контент Параметры объекта propertyList с группой атрибутов attrGroup</li>
     * <li>Создать нелицензированного пользователя employee, выдать ему все права на метакласс metaClass</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти под пользователем employee</li>
     * <li>Перейти на карточку bo</li>
     * <li>Нажать кнопку Редактировать на контенте propertyList</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить отсутствие на форме атрибутов типа Агрегирующий, Набор ссылок на БО, Обратная ссылка,
     * Вещественное число, Файл, Гиперссылка, Ссылка на БО, Текст RTF, Счетчик времени, Счетчик времени (обратный)</li>
     * <li>Проверить присутствие на форме атрибутов типа Логический, Элемент справочника, Набор элементов
     * справочника, Дата, Дата/Время, Целочисленный, Строка, Текст, Временной интервал</li>
     * </ol>
     */
    private void checkAttrTypePolicy(MetaClass metaClass, boolean allPermitted, Bo bo)
    {
        Attribute aggregate = DAOAttribute.createAggregate(metaClass, AggregatedClasses.OU_AND_TEAM,
                SharedFixture.ou(), SharedFixture.employee());
        Attribute boLinks = DAOAttribute.createBoLinks(metaClass.getFqn(), metaClass);
        Attribute backLinks = DAOAttribute.createBackBOLinks(metaClass.getFqn(), boLinks);
        Attribute bool = DAOAttribute.createBool(metaClass);
        Attribute catalogItemTimeZone = DAOAttribute.createCatalogItem(metaClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
        Attribute catalogItemServiceTime = DAOAttribute.createCatalogItem(metaClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.SERVICETIME), null);
        Attribute catalogItemSet = DAOAttribute.createCatalogItemSet(metaClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.CATEGORY));
        Attribute date = DAOAttribute.createDate(metaClass.getFqn());
        Attribute dateTime = DAOAttribute.createDateTime(metaClass.getFqn());
        Attribute doublee = DAOAttribute.createDouble(metaClass.getFqn());
        Attribute file = DAOAttribute.createFile(metaClass.getFqn());
        Attribute hyperlink = DAOAttribute.createHyperlink(metaClass.getFqn());
        Attribute integer = DAOAttribute.createInteger(metaClass.getFqn());
        Attribute boLink = DAOAttribute.createObjectLink(metaClass, metaClass, null);
        Attribute str = DAOAttribute.createString(metaClass);
        Attribute text = DAOAttribute.createText(metaClass.getFqn());
        Attribute textRtf = DAOAttribute.createTextRTF(metaClass.getFqn());
        Attribute dti = DAOAttribute.createTimeInterval(metaClass.getFqn());

        DSLAttribute.add(aggregate, boLinks, backLinks, bool, catalogItemTimeZone, catalogItemServiceTime,
                catalogItemSet, date, dateTime, doublee, file, hyperlink, integer, boLink, str, text, textRtf, dti);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("true");
        DSLScriptInfo.addScript(script);

        TimerDefinition directTimerDefinition = DAOTimerDefinition.createAstroTimerByScript(metaClass.getFqn(),
                catalogItemTimeZone.getCode());
        directTimerDefinition.setStartCondition(script.getCode());

        TimerDefinition backTimerDefinition = DAOTimerDefinition.createServiceTimerByScript(metaClass.getFqn(),
                catalogItemTimeZone.getCode(), catalogItemServiceTime.getCode());
        backTimerDefinition.setStartCondition(script.getCode());

        DSLTimerDefinition.add(directTimerDefinition, backTimerDefinition);

        Attribute timer = DAOAttribute.createTimer(metaClass.getFqn(), directTimerDefinition,
                TimerType.TIMER_STATUS_VIEW);
        Attribute backTimer = DAOAttribute.createBackTimer(metaClass.getFqn(), backTimerDefinition,
                TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timer, backTimer);

        GroupAttr groupAttr = DAOGroupAttr.create(metaClass);
        DSLGroupAttr.add(groupAttr, aggregate, boLinks, backLinks, bool, catalogItemTimeZone, catalogItemServiceTime,
                catalogItemSet, date, dateTime, doublee, file, hyperlink, integer, boLink, str, text, textRtf, dti,
                timer, backTimer);

        ContentForm propertyList = DAOContentCard.createPropertyList(metaClass, groupAttr);
        DSLContent.add(propertyList);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(true), false);
        RightGroup rights = new RightGroup(profile, metaClass);
        rights.addAllRights(metaClass);
        rights.apply();

        //выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(bo);
        GUIPropertyList.clickEditLink(propertyList);
        //проверка
        if (allPermitted)
        {
            GUIForm.assertAttrPresent(aggregate, boLinks, backLinks, doublee, file, hyperlink, boLink, textRtf, bool,
                    catalogItemTimeZone, catalogItemServiceTime, catalogItemSet, date, dateTime, integer, str, text,
                    dti);
            GUIForm.assertAttrAbsence(timer, backTimer);
        }
        else
        {
            GUIForm.assertAttrAbsence(aggregate, boLinks, backLinks, doublee, file, hyperlink, boLink, textRtf, timer,
                    backTimer);
            GUIForm.assertAttrPresent(bool, catalogItemTimeZone, catalogItemServiceTime, catalogItemSet, date,
                    dateTime, integer, str, text, dti);
        }
    }

}
