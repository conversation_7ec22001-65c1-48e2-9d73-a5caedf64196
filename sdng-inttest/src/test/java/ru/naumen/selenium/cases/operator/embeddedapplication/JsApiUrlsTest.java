package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.GUIXpath.Div.IFRAME_PATTERN;
import static ru.naumen.selenium.casesutil.GUIXpath.Other.BODY;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.config.Config;

/**
 * Тесты на генерацию URL из JS API для встроенных приложений
 *
 * <AUTHOR> Biktashev
 * @since 26 дек. 2017 г.
 */
class JsApiUrlsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    /**
     * <p>Тестирование генерации URL ИО
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.wirte(jsApi.urls.base())</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен URL ИО
     * </ol>
     */
    @Test
    void testBaseUrl()
    {
        testSimpleText(Config.get().getWebAddress() + "operator/", "jsApi.urls.base()");
    }

    /**
     * <p>Тестирование генерации URL на форму добавления объекта
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.wirte(jsApi.urls.objectAddForm('asd$qwe'))</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен URL формы добавления объекта с FQN 'asd$qwe'
     * </ol>
     */
    @Test
    void testObjectAddFormUrl()
    {
        testSimpleText(Config.get().getWebAddress() + "operator/#add:asd$qwe", "jsApi.urls.objectAddForm('asd$qwe')");
    }

    /**
     * <p>Тестирование генерации URL на карточку объекта
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.wirte(jsApi.urls.objectCard('asd$123'))</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен URL к карточке объекта с UUID 'asd$123'
     * </ol>
     */
    @Test
    void testObjectCardUrl()
    {
        testSimpleText(Config.get().getWebAddress() + "operator/#uuid:asd$123", "jsApi.urls.objectCard('asd$123')");
    }

    /**
     * <p>Тестирование генерации URL на форму редактирования объекта
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.wirte(jsApi.urls.objectEditForm('asd$123'))</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен URL формы редактирования объекта с UUID 'asd$123'
     * </ol>
     */
    @Test
    void testObjectEditFormUrl()
    {
        testSimpleText(Config.get().getWebAddress() + "operator/#edit:asd$123", "jsApi.urls.objectEditForm('asd$123')");
    }

    /**
     *  <li>Включить добавленное встроенное приложение
     *  <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *  Компании</li><br>
     *
     *  <b>Действия</b>
     *  <li>Войти под naumen
     *  <li>Перейти на карточку Компании
     */
    private void testSimpleText(String expected, String js)
    {
        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format("document.write(%s)", js));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        MetaClass userCase = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), model);
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        String xpath = String.format(IFRAME_PATTERN, content.getXpathId());
        tester.getWebDriver().switchTo().frame(tester.find(xpath));
        Assertions.assertEquals(expected, tester.find(BODY).getText());
    }
}
