package ru.naumen.selenium.cases.admin.permission;

import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.CREATE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.DELETE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.EDIT;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.VIEW;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.admin.GUICommonSearchSettings;
import ru.naumen.selenium.casesutil.admin.GUICommonSearchSettings.EditCommonSearchSettings;
import ru.naumen.selenium.casesutil.admin.GUIFolder;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.GUISearchSettings;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalogs;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList2;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.script.js.CustomJSElement;
import ru.naumen.selenium.casesutil.model.script.js.CustomJSElement.TargetPlace;
import ru.naumen.selenium.casesutil.model.script.js.DAOCustomJSElement;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.role.GUISecurityRoleList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.js.GUICustomJSElement;
import ru.naumen.selenium.casesutil.script.js.GUICustomizationFilesList;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.secgroup.GUISecurityGroupList;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.timer.GUITimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тестирование прав на действие в интерфейсе администратора по маркерам доступа в профиле администрирования.
 *
 * <AUTHOR>
 * @since 19.12.2024
 */
public class AdminProfileAccessMarker2Test extends AbstractTestCase
{
    private SuperUser superUser;
    private AdminProfile adminProfile;
    private AdminProfileAccessMarkerMatrix accessMarkerMatrix;
    private MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareAllFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
    }

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Добавить в систему профиль администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser и назначить ему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
    }

    /**
     * Тестирование действий "Создать" и "Удалить" маркера доступа "Файлы кастомизации". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Скриптовые настройки" и "Счётчики времени"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Каталог файлов кастомизации"</li>
     * <li>Нажать кнопку "Добавить файл" и заполнить необходимые поля на форме добавления файла кастомизации,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый файл кастомизации jsElement отсутствует в списке файлов кастомизации</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Файлы кастомизации"</li>
     * <li>Нажать кнопку "Добавить файл" и заполнить необходимые поля на форме добавления файла кастомизации,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый файл кастомизации jsElement присутствует в списке файлов кастомизации</li>
     * <li>Из матрицы маркеров доступа accessMarkerMatrix убрать право на создание
     * по маркеру доступа "Файлы кастомизации"</li>
     * <li>Нажать кнопку "Удалить" у файла кастомизации jsElement,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в списке файлов кастомизации присутствует файл кастомизации jsElement</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Файлы кастомизации"</li>
     * <li>Нажать кнопку "Удалить" у файла кастомизации jsElement,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что в списке файлов кастомизации отсутствует файл кастомизации jsElement</li>
     * </ol>
     */
    @Test
    public void testCreateAndDeletePermissionsCustomizationFilesAccessMarker()
    {
        // Подготовка
        SdFile jsFile = DAOCustomJSElement.createFile(DSLFile.PREFIX_LOGIN_SIGNED_JS);
        CustomJSElement jsElement = DAOCustomJSElement.create(jsFile, TargetPlace.global);

        accessMarkerMatrix.addAccessMarkerPermission(CUSTOMIZATION_FILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToCustomizationCatalog();
        clickAddCustomJsElementAndFillForm(jsElement, jsFile);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICustomizationFilesList.advlist().content().asserts().rowsAbsence(jsElement);

        accessMarkerMatrix.addAccessMarkerPermission(CUSTOMIZATION_FILES, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddCustomJsElementAndFillForm(jsElement, jsFile);
        GUIForm.applyForm();
        GUICustomizationFilesList.advlist().content().asserts().rowsPresence(jsElement);
        jsElement.setExists(true);

        accessMarkerMatrix.removeAccessMarkerPermission(CUSTOMIZATION_FILES, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUICustomizationFilesList.advlist()
                .content()
                .clickPict(jsElement, GUICustomizationFilesList.DELETE_CUSTOM_JS_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICustomizationFilesList.advlist().content().asserts().rowsPresence(jsElement);

        accessMarkerMatrix.addAccessMarkerPermission(CUSTOMIZATION_FILES, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUICustomizationFilesList.advlist()
                .content()
                .clickPict(jsElement, GUICustomizationFilesList.DELETE_CUSTOM_JS_ACTION);
        GUIForm.confirmByYes();
        GUICustomizationFilesList.advlist().content().asserts().rowsAbsence(jsElement);
        jsElement.setExists(false);
    }

    /**
     * Тестирование действий "Просмотр" и "Редактировать" маркера доступа "Настройки поиска". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Поиск"</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что отсутствует вкладка "Поиск"</li>
     * <li>Перейти в раздел "Поиск"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Настройки поиска"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Каталоги"</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что присутствуют вкладка "Поиск"</li>
     * <li>Перейти на вкладку "Поиск" класса userClass</li>
     * <li>Нажать на иконку редактирования критерия релевантности поиска</li>
     * <li>На форме редактирование нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку редактирования атрибута "Название"</li>
     * <li>На форме редактирование нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку "Поиск"</li>
     * <li>Нажать кнопку "Редактировать" в настройке "Использовать язык продвинутого поиска",
     * затем нажать кнопку "Сохранить" на форме редактирования настройки</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testViewAndEditPermissionsSearchSettingsAccessMarker()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("searchSettings:");

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Присутствует вкладка \"Поиск\"", tester.waitDisappear(Div.CLASS_FTS_TAB));

        GUINavigational.goToSearchSettings();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(SEARCH_SETTINGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("searchSettings:");

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Отсутствует вкладка \"Поиск\"", tester.find(Div.CLASS_FTS_TAB).isDisplayed());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.SEARCHSETTINGS);
        GUISearchSettings.clickMSCEdit();
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUISearchSettings.clickEdit(SysAttribute.title(userClass));
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUINavigational.goToSearchSettings();
        GUICommonSearchSettings.clickEditCommonSearchSettings(EditCommonSearchSettings.editUseAdvancedSearch);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Действия по событиям". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать ДПС eventAction типа "Отслеживание изменений":
     * <ul>
     *     <li>Класс объектов - userClass</li>
     *     <li>Тип события - "Добавление комментария к объекту"</li>
     * </ul></li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Действия по событиям"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Действия по событиям"</li>
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку ДПС eventAction</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Действия по событиям"</li>
     * <li>Обновить страницу</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Проверить, что присутствует вкладка "Действия по событиям"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Действия по событиям"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionEventActionsAccessMarker()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createChangeTracking(userClass, EventType.addComment);
        DSLEventAction.add(eventAction);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEventActions();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Действия по событиям\"", tester.waitDisappear(Div.EVENT_ACTIONS_TAB));

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("eventactions:");

        GUIEventAction.goToCard(eventAction.getUuid());
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);
        tester.refresh();

        GUINavigational.goToEventActions();
        Assert.assertTrue("Отсутствует вкладка \"Действия по событиям\"",
                tester.find(Div.EVENT_ACTIONS_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("eventactions:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Действия по событиям". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Нажать кнопку "Добавить действие" и заполнить поля на форме добавления ДПС,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Действия по событиям"</li>
     * <li>Нажать кнопку "Добавить действие" и заполнить поля на форме добавления ДПС,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что текущая страница - карточка создаваемого ДПС eventAction</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionEventActionsAccessMarker()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createChangeTracking(userClass, EventType.addComment);

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEventActions();
        GUIEventActionList.addAction();
        GUIEventAction.fillEventAction(eventAction);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, CREATE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        GUIEventActionList.addAction();
        GUIEventAction.fillEventAction(eventAction);
        GUIForm.applyForm();
        GUIEventAction.assertThatCard(eventAction);
        eventAction.setExists(true);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Действия по событиям". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать ДПС eventAction типа "Отслеживание изменений":
     * <ul>
     *     <li>Класс объектов - userClass</li>
     *     <li>Тип события - "Добавление комментария к объекту"</li>
     * </ul></li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Действия по событиям"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Перейти на карточку ДПС eventAction</li>
     * <li>Открыть форму редактирование ДПС eventAction и установить название eventActionNewTitle,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название ДПС eventAction не изменилось</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Действия по событиям"</li>
     * <li>Открыть форму редактирование ДПС eventAction и установить название eventActionNewTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название ДПС eventAction - eventActionNewTitle</li>
     * </ol>
     */
    @Test
    public void testEditPermissionEventActionsAccessMarker()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createChangeTracking(userClass, EventType.addComment);
        DSLEventAction.add(eventAction);

        String eventActionNewTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setTitle(eventActionNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEventAction.assertTitle(eventAction.getTitle());

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, EDIT);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.editFromCard();
        GUIEventAction.setTitle(eventActionNewTitle);
        GUIForm.applyForm();
        GUIEventAction.assertTitle(eventActionNewTitle);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Действия по событиям". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать ДПС eventAction типа "Отслеживание изменений":
     * <ul>
     *     <li>Класс объектов - userClass</li>
     *     <li>Тип события - "Добавление комментария к объекту"</li>
     * </ul></li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Действия по событиям"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Нажать на иконку "Удалить" у ДПС eventAction, затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в списке ДПС присутствует ДПС eventAction</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Действия по событиям"</li>
     * <li>Нажать на иконку "Удалить" у ДПС eventAction, затем нажать "Да" на форме подтверждения удаления
     * и проверить, что форма исчезла</li>
     * <li>Проверить, что в списке ДПС отсутствует ДПС eventAction</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionEventActionsAccessMarker()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createChangeTracking(userClass, EventType.addComment);
        DSLEventAction.add(eventAction);

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEventActions();
        GUIEventActionList2.advlist().content().clickPict(eventAction, GUIEventActionList2.PICT_DELETE);
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEventActionList2.advlist().content().asserts().rowsPresence(eventAction);

        accessMarkerMatrix.addAccessMarkerPermission(EVENT_ACTIONS, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIEventActionList2.advlist().content().clickPict(eventAction, GUIEventActionList2.PICT_DELETE);
        GUIForm.confirmByYes();
        GUIEventActionList2.advlist().content().asserts().rowsAbsence(eventAction);
        eventAction.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркеров доступа "Системные справочники" и "Пользовательские справочники". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский справочник userCatalog</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Справочники"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует раздел "Справочники"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку справочника "Иконки"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Системные справочники"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует раздел "Справочники"</li>
     * <li>Раскрыть раздел "Справочники" левого навигационного меню</li>
     * <li>Проверить, что в левом навигационном меню присутствует элемент "Системные справочники"
     * и отсутствует элемент "Пользовательские справочники"</li>
     * <li>Проверить, что блок "Системные справочники" присутствует на карточке "Справочники"
     * и отсутствует блок "Пользовательские справочники"</li>
     * <li>Перейти на карточку пользовательского справочника userCatalog</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Пользовательские справочники" и убрать право на просмотр "Системные справочники"</li>
     * <li>Обновить страницу</li>
     * <li>Перейти в раздел "Справочники"</li>
     * <li>Проверить, что в левом навигационном меню присутствует раздел "Справочники"</li>
     * <li>Раскрыть раздел "Справочники" левого навигационного меню</li>
     * <li>Проверить, что присутствует элемент "Пользовательские справочники"
     * и отсутствует элемент "Системные справочники"</li>
     * <li>Проверить, что блок "Пользовательские справочники" присутствует на карточке "Справочники"
     * и отсутствует блок "Системные справочники"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionSystemCatalogsAndUserCatalogsAccessMarkers()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());
        DSLCatalog.add(userCatalog);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToCatalogs();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.CATALOGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("catalogs:system");
        GUIAdminNavigationTree.assertItemAbsent("catalogs:user");

        GUICatalog.goToCard(DAOCatalog.createSystem(SystemCatalog.SYSTEM_ICON));
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermissions(SYSTEM_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUINavigational.goToCatalogs();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.CATALOGS_ITEM_ID);
        GUIAdminNavigationTree.expandItem("catalogs", "catalogs:");
        GUIAdminNavigationTree.assertItemPresent("catalogs:system");
        GUIAdminNavigationTree.assertItemAbsent("catalogs:user");
        GUICatalogs.assertSystemCatalogsBlockPresent();
        GUICatalogs.assertUserCatalogsBlockAbsence();

        GUICatalog.goToCard(userCatalog);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.removeAccessMarkerPermission(SYSTEM_CATALOGS, VIEW);
        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUINavigational.goToCatalogs();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.CATALOGS_ITEM_ID);
        GUIAdminNavigationTree.expandItem("catalogs", "catalogs:");
        GUIAdminNavigationTree.assertItemAbsent("catalogs:system");
        GUIAdminNavigationTree.assertItemPresent("catalogs:user");

        GUICatalogs.assertSystemCatalogsBlockAbsence();
        GUICatalogs.assertUserCatalogsBlockPresent();
    }

    /**
     * Тестирование действия "Создание" маркера доступа "Пользовательские справочники". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Пользовательские справочники"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Развернуть элемент левого меню "Справочники"</li>
     * <li>Открыть форму добавления пользовательского справочника и заполнить необходимые поля,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый справочник userCatalog отсутствует в системе</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Пользовательские справочники"</li>
     * <li>Развернуть элемент левого меню "Справочники"</li>
     * <li>Открыть форму добавления пользовательского справочника и заполнить необходимые поля,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый справочник userCatalog существует в системе</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionUserCatalogsAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());

        // Действия и проверки
        GUILogon.login(superUser);

        GUIAdminNavigationTree.expandItem("catalogs", "catalogs:");
        clickAddUserCatalogAndFillForm(userCatalog);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICatalog.assertAbsence(userCatalog);

        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIAdminNavigationTree.expandItem("catalogs", "catalogs:");
        clickAddUserCatalogAndFillForm(userCatalog);
        GUIForm.applyForm();
        GUICatalog.assertPresent(userCatalog);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Системные справочники". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать системный справочник systemCatalog и в нем элемент systemCatalogItem</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Пользовательские справочники"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку справочника systemCatalog</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Переиндексировать"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на кнопку "Добавить элемент"</li>
     * <li>Заполнить поля "Название", "Код" и загрузить png-изображение в поле "Изображение"
     * на форме добавления элемента справочника</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Редактировать" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * на форме добавления элемента справочника</li>
     * <li>Нажать на иконку "Копировать" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Поместить в архив" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Да" в окне подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Восстановить из архива" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Да" в окне подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testEditPermissionSystemCatalogsAccessMarker()
    {
        // Подготовка
        Catalog systemCatalog = DAOCatalog.createSystem(SystemCatalog.SYSTEM_ICON);
        CatalogItem systemCatalogItem = DAOCatalogItem.createSystemIcon();
        DSLCatalogItem.add(systemCatalogItem);

        accessMarkerMatrix.addAccessMarkerPermissions(SYSTEM_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUICatalog.goToCard(systemCatalog);
        GUICatalog.openEditForm();
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickRefreshButton();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalogItem.clickAddElement();
        GUICatalogItem.setTitle(ModelUtils.createTitle());
        GUICatalogItem.setCode(ModelUtils.createCode());
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.PNG_ICON_FOR_UPLOAD);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickEditCatalogItem(systemCatalogItem);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickCopyCatalogItem(systemCatalogItem);
        GUICatalog.setCode(ModelUtils.createCode());
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickRemoveCatalogItem(systemCatalogItem);
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Пользовательские справочники". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский справочник userCatalog и в нем элемент userCatalogItem
     * и помещенный в архив элемент userCatalogItem2</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Пользовательские справочники"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку справочника userCatalog</li>
     * <li>Нажать на кнопку "Добавить элемент"</li>
     * <li>Заполнить поля "Название", "Код" и загрузить png-изображение в поле "Изображение"
     * на форме добавления элемента справочника</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Переиндексировать"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Редактировать" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * на форме добавления элемента справочника</li>
     * <li>Нажать на иконку "Копировать" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Поместить в архив" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Да" в окне подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать на иконку "Восстановить из архива" у элемента справочника userCatalogItem</li>
     * <li>Нажать кнопку "Да" в окне подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * </ol>
     */
    @Test
    public void testEditPermissionUserCatalogsAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());
        DSLCatalog.add(userCatalog);

        CatalogItem userCatalogItem = DAOCatalogItem.createUser(userCatalog);
        CatalogItem userCatalogItem2 = DAOCatalogItem.createUser(userCatalog);
        userCatalogItem2.setIsRemoved(Boolean.TRUE.toString());
        DSLCatalogItem.add(userCatalogItem, userCatalogItem2);

        // Действия и проверки
        GUILogon.login(superUser);

        GUICatalog.goToCard(userCatalog);
        GUICatalog.openEditForm();
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickRefreshButton();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.goToCard(userCatalog);

        GUICatalogItem.clickAddElement();
        GUICatalogItem.setTitle(ModelUtils.createTitle());
        GUICatalogItem.setCode(ModelUtils.createCode());
        GUIFileAdmin.uploadFile(GUIXpath.PropertyDialogBoxContent.ICON_VALUE, DSLFile.PNG_ICON_FOR_UPLOAD);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickEditCatalogItem(userCatalogItem);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickCopyCatalogItem(userCatalogItem);
        GUICatalog.setCode(ModelUtils.createCode());
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickRemoveCatalogItem(userCatalogItem);
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUICatalog.clickDeleteCatalogItem(userCatalogItem);
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Пользовательские справочники". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский справочник userCatalog</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Пользовательские справочники"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку справочника userCatalog</li>
     * <li>Нажать на кнопку "Удалить", затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что справочник userCatalog существует в системе</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Пользовательские справочники"</li>
     * <li>Нажать на кнопку "Удалить", затем нажать кнопку "Да" на форме подтверждения
     * и проверить, что форма исчезла</li>
     * <li>Проверить, что справочник userCatalog отсутствует в системе</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionUserCatalogsAccessMarker()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(RandomUtils.nextBoolean(), RandomUtils.nextBoolean());
        DSLCatalog.add(userCatalog);

        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUICatalog.goToCard(userCatalog);
        GUICatalog.clickDelete();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICatalog.assertPresent(userCatalog);

        accessMarkerMatrix.addAccessMarkerPermissions(USER_CATALOGS, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUICatalog.clickDelete();
        GUIForm.confirmByYes();
        GUICatalog.assertAbsence(userCatalog);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Группы пользователей". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку "Группы пользователей и роли"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Группы пользователей"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Группы пользователей и роли"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Группы пользователей"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует вкладка "Группы пользователей"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Группы пользователей и роли"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionSecurityGroupAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToSecGroupsAndRoles();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Группы пользователей\"",
                tester.waitDisappear(Div.SECURITY_GROUPS_TAB));

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("security:");

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_GROUP, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);
        tester.refresh();

        Assert.assertTrue("Отсутствует вкладка \"Группы пользователей\"",
                tester.find(Div.SECURITY_GROUPS_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("security:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Группы пользователей". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Группы пользователей"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку "Группы пользователей и роли"</li>
     * <li>Нажать кнопку "Добавить группу" и заполнить необходимые поля на форме добавления группы,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая группа пользователей securityGroup отсутствует в списке групп пользователей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Группы пользователей"</li>
     * <li>Нажать кнопку "Добавить группу" и необходимые поля на форме добавления группы,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемая группа пользователей securityGroup присутствует в списке групп пользователей</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionSecurityGroupAccessMarker()
    {
        // Подготовка
        SecurityGroup securityGroup = DAOSecurityGroup.create();

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_GROUP, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToSecGroupsAndRoles();
        openSecurityGroupAddFormAndFill(securityGroup);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.assertAbsence(securityGroup);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_GROUP, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        openSecurityGroupAddFormAndFill(securityGroup);
        GUIForm.applyForm();
        GUISecurityGroupList.assertPresence(securityGroup);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Группы пользователей". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Группы пользователей"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Группы пользователей и роли"</li>
     * <li>Нажать на пиктограмму "Удалить" у группы пользователей securityGroup,
     * затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что группа пользователей securityGroup присутствует в списке групп пользователей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Группы пользователей"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать на пиктограмму "Удалить" у группы пользователей securityGroup,
     * затем нажать кнопку "Да" на форме подтверждения и проверить, что форма исчезла</li>
     * <li>Проверить, что группа пользователей securityGroup отсутствует в списке групп пользователей</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionSecurityGroupAccessMarker()
    {
        // Подготовка
        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_GROUP, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.advlist().content().clickPict(securityGroup, GUISecurityGroupList.DELETE_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUISecurityGroupList.assertPresence(securityGroup);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_GROUP, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUISecurityGroupList.advlist().content().clickPict(securityGroup, GUISecurityGroupList.DELETE_ACTION);
        GUIForm.confirmByYes();
        GUISecurityGroupList.assertAbsence(securityGroup);
        securityGroup.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Роли". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку "Группы пользователей и роли"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Роли"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Группы пользователей и роли"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Роли"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует вкладка "Роли"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Группы пользователей и роли"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionSecurityRoleAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToSecGroupsAndRoles();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Роли\"", tester.waitDisappear(Div.SECURITY_ROLES_TAB));

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("security:");

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);
        tester.refresh();

        Assert.assertTrue("Отсутствует вкладка \"Роли\"", tester.find(Div.SECURITY_ROLES_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("security:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Роли". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo</li>
     * <li>Создать роль securityRole</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Роли" и "Скриптовые настройки"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Группы пользователей и роли" вкладка "Роли"</li>
     * <li>Нажать кнопку "Добавить роль" и заполнить необходимые поля на форме добавления роли,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая роль securityRole отсутствует в списке ролей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Роли"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Добавить роль" и заполнить необходимые поля на форме добавления роли,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемая роль securityRole присутствует в списке ролей</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionSecurityRolesAccessMarker()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        SecurityRole securityRole = DAOSecurityRole.create(userClass);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.goToRolesTab();

        clickAddRoleAndFillForm(securityRole, scriptInfo);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUISecurityRoleList.assertAbsenceInList(securityRole);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddRoleAndFillForm(securityRole, scriptInfo);
        GUIForm.applyForm();
        GUISecurityRoleList.assertPresenceInList(securityRole);
        securityRole.setExists(true);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Роли". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать роль securityRole</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Роли"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Группы пользователей и роли" вкладка "Роли"</li>
     * <li>Нажать на пиктограмму "Удалить" у роли securityRole,
     * затем нажать кнопку "Да" на форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что роль securityRole присутствует в списке ролей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Роли"</li>
     * <li>Нажать на пиктограмму "Удалить" у роли securityRole,
     * затем нажать кнопку "Да" на форме подтверждения и проверить, что форма исчезла</li>
     * <li>Проверить, что роль securityRole отсутствует в списке ролей</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionsSecurityRoleAccessMarker()
    {
        // Подготовка
        SecurityRole securityRole = DAOSecurityRole.create(userClass);
        DSLSecurityRole.add(securityRole);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.goToRolesTab();

        GUISecurityRoleList.clickDeleteRole(securityRole);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUISecurityRoleList.assertPresenceInList(securityRole);

        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUISecurityRoleList.clickDeleteRole(securityRole);
        GUIForm.confirmByYes();
        GUISecurityRoleList.assertAbsenceInList(securityRole);
        securityRole.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Каталоги". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать папку folder</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Каталоги"</li>
     * <li>Перейти на карточку "Каталоги"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку папки folder</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Каталоги"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Каталоги"</li>
     * <li>Перейти на карточку "Каталоги"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionCatalogsAccessMarker()
    {
        // Подготовка
        Folder folder = DAOFolder.create(userClass);
        DSLFolder.add(folder);

        // Действия и проверки
        GUILogon.login(superUser);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("catalog:folder");

        GUINavigational.goToCatalogFolder();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIFolder.goToCard(folder);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(CATALOGS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        tester.refresh();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("catalog:folder");

        GUINavigational.goToCatalogFolder();
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Каталоги". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass2</li>
     * <li>Создать каталог folder2 в классе userClass2</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Каталоги"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Каталоги"</li>
     * <li>Открыть форму добавления каталога и заполнить необходимые поля на форме добавления каталога,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемые каталог folder отсутствует в списке каталогов</li>
     * <li>Нажать на пиктограмму "Удалить" у каталога folder2,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что каталог folder2 присутствует в списке каталогов</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Каталоги"</li>
     * <li>Открыть форму добавления каталога и заполнить необходимые поля на форме добавления каталога,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемые каталог folder присутствует в списке каталогов</li>
     * <li>Нажать на пиктограмму "Удалить" у каталога folder2,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что каталог folder2 отсутствует в списке каталогов</li>
     * </ol>
     */
    @Test
    public void testEditPermissionCatalogsAccessMarker()
    {
        // Подготовка
        Folder folder = DAOFolder.create(userClass);

        MetaClass userClass2 = DAOUserClass.create();
        DSLMetainfo.add(userClass2);

        Folder folder2 = DAOFolder.create(userClass2);
        DSLFolder.add(folder2);

        accessMarkerMatrix.addAccessMarkerPermission(CATALOGS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToCatalogFolder();
        clickAddFolderAndFillForm(folder);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIFolder.assertFolderAbsence(folder);

        GUIFolder.clickDelete(folder2);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIFolder.assertFolderPresent(folder2);

        accessMarkerMatrix.addAccessMarkerPermission(CATALOGS, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddFolderAndFillForm(folder);
        GUIForm.applyForm();
        GUIFolder.assertFolderPresent(folder);
        GUIFolder.setUuidById(folder);

        GUIFolder.clickDelete(folder2);
        GUIForm.confirmByYes();
        GUIFolder.assertFolderAbsence(folder2);
        folder2.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Счётчики времени". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать атрибут timeZoneAttr типа "Элемент справочника" в классе userClass
     * <ul>
     *     <li>Тип справочника - "Часовые пояса"</li>
     * </ul></li>
     * <li>Создать счетчик времени userCounter:
     * <ul>
     *     <li>Объекты - userClass</li>
     *     <li>Метрика времени - timeZoneAttr</li>
     * </ul></li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Счётчики времени"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок со списком счётчиков времени</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Счётчики времени"</li>
     * <li>Перейти в карточку счётчика времени userCounter</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок "Свойства"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Счётчики времени"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует блок "Свойства"</li>
     * <li>Перейти в раздел "Счётчики времени"</li>
     * <li>Проверить, что присутствует блок со списком счётчиков времени</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Счётчики времени"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionTimersAccessMarker()
    {
        Attribute timeZoneAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
        DSLAttribute.add(timeZoneAttr);

        TimerDefinition userCounter = DAOTimerDefinition.createAstroTimerByStatus(userClass.getFqn(),
                timeZoneAttr.getCode());
        DSLTimerDefinition.add(userCounter);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToTimers();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUITimerDefinition.assertPresentTimersBlock(false);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("timers:");

        GUITimerDefinition.goToCard(userCounter);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUITimerDefinition.assertPresentInfoBlock(false);

        accessMarkerMatrix.addAccessMarkerPermission(TIMERS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUITimerDefinition.assertPresentInfoBlock(true);

        GUINavigational.goToTimers();
        GUITimerDefinition.assertPresentTimersBlock(true);

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.PROCESS_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("timers:");
    }

    private static void clickAddCustomJsElementAndFillForm(CustomJSElement jsElement, SdFile jsFile)
    {
        GUICustomizationFilesList.advlist().toolPanel().clickAdd();
        GUIAttribute.fillAttrTitle(jsElement.getTitle());
        GUIAttribute.fillAttrCode(jsElement.getCode());
        GUICustomJSElement.uploadAdminCustomizationFile(jsFile.getPath());
        GUICustomJSElement.fillTargetPlace(jsElement.getTargetPlace());
    }

    private static void clickAddUserCatalogAndFillForm(Catalog userCatalog)
    {
        GUICatalog.clickAddForm();
        GUICatalog.fillUserCatalogFields(userCatalog);
    }

    private static void openSecurityGroupAddFormAndFill(SecurityGroup securityGroup)
    {
        GUISecurityGroupList.clickAddFrom();
        GUISecurityGroupList.fillAttrTitle(securityGroup.getTitle());
        GUISecurityGroupList.fillAttrCode(securityGroup.getCode());
    }

    private static void clickAddRoleAndFillForm(SecurityRole securityRole, ScriptInfo scriptInfo)
    {
        GUISecurityRoleList.clickAddRole();
        GUISecurityRoleList.setTitle(securityRole.getTitle());
        GUISecurityRoleList.setCode(securityRole.getCode());
        GUISecurityRoleList.clickShowAccessScript();
        GUISecurityRoleList.setExistingAccessScript(scriptInfo);
    }

    private static void clickAddFolderAndFillForm(Folder folder)
    {
        GUIFolder.clickAddFolder();
        GUIFolder.fillMainFields(folder);
        GUIFolder.fillParent(folder);
    }
}