package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.LIST;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.ObjectType.WITH_FOLDER;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута типа "Ссылка на БО" на формах через мобильное API.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestObjectLinkPossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static Attribute objectLinkAttr, objectLinkFoldersAttr;
    private static Folder userFolder;
    private static Bo userBo, userBo2, userBo3;
    private static Bo[] userBos;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать папку userFolder в классе userClass</li>
     * <li>Создать объекты userBo, userBo2, userBo3 типа userCase и добавить объект userBo, userBo2 в папку
     * userFolder</li>
     * <li>Создать в типе userCase дополнительно ещё 20 объектов userBos для тестирования пагинации</li>
     * <li>Создать атрибут objectLinkAttr типа "Ссылка на БО" в типе userCase, со ссылкой на объекты типа userCase,
     * с представлением "Список выбора"</li>
     * <li>Создать атрибут objectLinkFoldersAttr типа "Ссылка на БО" в типе userCase, со ссылкой на объекты типа
     * userCase, с представлением "Список выбора с папками"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибуты objectLinkAttr, objectLinkFoldersAttr</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userFolder = DAOFolder.create(userClass);
        DSLFolder.add(userFolder);

        Attribute folderAttr = SysAttribute.folders(userClass);
        folderAttr.setItemValue(userFolder);

        userBo = DAOUserBo.create(userCase, folderAttr);
        userBo2 = DAOUserBo.createWithParent(userCase, userBo);
        userBo2.setUserAttribute(folderAttr);
        userBo3 = DAOUserBo.create(userCase);
        DAOBo.appendTitlePrefixes(0, 24, userBo, userBo2, userBo3);
        DSLBo.add(userBo, userBo2, userBo3);
        DSLSearch.updateIndex(userBo, userBo2, userBo3);

        int expectedCount = 20;
        userBos = new Bo[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            userBos[i] = DAOUserBo.create(userCase);
        }
        DAOBo.appendTitlePrefixes(4, 24, userBos);
        DSLBo.add(userBos);
        DSLSearch.updateIndex(userBos);

        objectLinkAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        objectLinkFoldersAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        objectLinkFoldersAttr.setEditPresentation(WITH_FOLDER);
        DSLAttribute.add(objectLinkAttr, objectLinkFoldersAttr);

        addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, objectLinkAttr, objectLinkFoldersAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения списка возможных значений атрибута типа "Ссылка на БО" с представлением "Список выбора".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из userBo, userBo2, userBo3 и первых 17
     * объектов из userBos</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут objectLinkAttr имеет тип выбора - null</li>
     * <li>Получить форму для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут objectLinkAttr имеет тип выбора - список</li>
     * </ol>
     */
    @Test
    public void testObjectLink()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkAttr, objectTemplate)
                .setForm(addForm);
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkAttr)
                    .assertValues(
                            list(
                                    element(userBo),
                                    element(userBo2),
                                    element(userBo3)
                            ).with(
                                    elements(Arrays.copyOf(userBos, 17))
                            )
                    );
        }

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(objectLinkAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(objectLinkAttr)
                .hasEditPresentationSelectType(LIST);
    }

    /**
     * Тестирование получения возможных значений атрибута типа "Ссылка на БО" с представлением "Список выбора с
     * папками". До версии 13.2 мобильного API должен возвращаться список без папок, после - дерево с папками.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo, userBo2, userBo3 и первых 17
     * объектов из userBos</li>
     * <li>Получить возможные значения для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из папки userFolder и
     * объекта userBo3 и первых 18 объектов из userBos, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = userFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на дочернем уровне состоящее из объектов userBo,
     * userBo2</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут objectLinkFoldersAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWithFolders()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkFoldersAttr)
                .assertValues(
                        list(
                                element(userBo),
                                element(userBo2),
                                element(userBo3)
                        ).with(
                                elements(Arrays.copyOf(userBos, 17))
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, objectLinkFoldersAttr)
                .assertValues(
                        tree(
                                folderElement(userFolder),
                                treeElement(userBo3)
                        ).with(
                                treeElements(Arrays.copyOf(userBos, 18))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setParent(userFolder);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, objectLinkFoldersAttr)
                .assertValues(
                        treeElement(userBo),
                        treeElement(userBo2)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(objectLinkFoldersAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Ссылка на БО" с представлением "Список выбора".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список, состоящий из объекта userBo2</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkAttr)
                    .assertValues(
                            element(userBo2)
                    );
        }
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Ссылка на БО" с представлением "Список выбора с
     * папками".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список, состоящий из объекта userBo2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из объекта userBo2, вложенного в папку userFolder</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWithFoldersWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkFoldersAttr)
                .assertValues(
                        element(userBo2)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, objectLinkFoldersAttr)
                .assertValues(
                        treeSearch(
                                folderElement(userFolder).children(
                                        treeElement(userBo2)
                                )
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа "Ссылка на БО" с
     * представлением "Список выбора".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект":<ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из последних 13 объектов userBos</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkAttr)
                    .assertValues(
                            elements(Arrays.copyOfRange(userBos, 7, 20))
                    );
        }
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа "Ссылка на БО" с
     * представлением "Список выбора с папками".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект":<ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из последних 13 объектов userBos</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>"Папки" = 1,</li>
     *         <li>userClass = 9,</li>
     *     </ul></li>
     *     <li>"Объект":<ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из последних 12
     * объектов userBos</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWithFoldersWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkFoldersAttr)
                .assertValues(
                        elements(Arrays.copyOfRange(userBos, 7, 20))
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm)
                .addFolderPosition(1)
                .addPosition(userClass, 9);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, objectLinkFoldersAttr)
                .assertValues(
                        treeElements(Arrays.copyOfRange(userBos, 8, 20))
                );
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Ссылка на БО" с представлением "Список выбора", для метакласса вложенного в себя.
     * Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userBo.getTitle());
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkAttr).isEmpty();
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Ссылка на БО" с представлением "Список выбора с папками", для метакласса вложенного в себя.
     * Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = objectLinkFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>objectLinkFoldersAttr = null</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testObjectLinkWithFoldersWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(objectLinkFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(objectLinkFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userBo.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, objectLinkFoldersAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, objectLinkFoldersAttr).assertValues(tree());
    }
}
