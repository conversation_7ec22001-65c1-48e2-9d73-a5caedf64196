package ru.naumen.selenium.cases.operator.embeddedapplication;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationForm;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationList;
import ru.naumen.selenium.casesutil.metaclass.GUITestEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication.ExternalUrlDefinition;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.silent.DSLSilentMode;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование определения URL для встроенного внешнего приложения
 * <AUTHOR>
 *
 */
public class EmbeddedApplicationUrlDefinitionTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static Bo userBo;
    private static final Bo ou = SharedFixture.ou();

    /**
     * <b>Общая подготовка</b>
     * <br>
     * <li>Создать пользовательский класс и тип: userClass, userCase
     * <li>Добавить объект userBo типа userCase
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass);
        DSLMetaClass.add(userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLSilentMode.setEnabled(false);
    }

    /**
     * Тестирование определия URL: Параметры определены скриптом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$49410294
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под супером на страницу приложения
     * <li>Открыть форму добавления приложения</li>
     * <li>Заполнить поля на форме: Тип приложения: Запущено на внешнем сервере, 
     *                              Метод определения URL: Параметры определены скриптом,
     *                              Адрес приложения: адрес самого приложения
     * <li>Заполнить скрипт <pre>return '/operator/#uuid:${ UUID общего отдела }'</pre>
     * <li>Сохранить и включить приложение
     * <li>На карточку userCase вывести контент "Встроенное приложение", в котором будет только что созданное приложение
     * <li>Зайти под сотрудником на карочку userBo
     * <li>Проверить, что в контенте встроенного приложения отображается карточка общего отдела
     */
    @Test
    public void testScriptDefinedParameters()
    {
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 'operator/#uuid:" + ou.getUuid() + "'");

        EmbeddedApplication extApp = DAOEmbeddedApplication.createExternalApplication(Config.get().getWebAddress());
        extApp.setUrlDefinition(ExternalUrlDefinition.SCRIPT_DEFINED_PARAMETERS);
        extApp.setHeight(500);

        DSLEmbeddedApplication.add(extApp, script);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), extApp);
        DSLContent.add(content);

        checkAsTester(content, ou);
    }

    /**
     * Тестирование определия URL: URL полностью определен скриптом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$49410294
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под супером на страницу приложения
     * <li>Открыть форму добавления приложения</li>
     * <li>Заполнить поля на форме: Тип приложения: Запущено на внешнем сервере, 
     *                              Метод определения URL: URL полностью определен скриптом
     * <li>Нажать сохранить и проверить, что появились сообщения валидации скрипта
     * <li>Заполнить скрипт <pre>return '${адрес текущего приложения}/operator/#uuid:${ UUID общего отдела }'</pre>
     * <li>Сохранить и включить приложение
     * <li>На карточку userCase вывести контент "Встроенное приложение", в котором будет только что созданное приложение
     * <li>Зайти под сотрудником на карочку userBo
     * <li>Проверить, что в контенте встроенного приложения отображается карточка общего отдела
     */
    @Test
    public void testScriptDefinedUrl()
    {
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(
                "return '" + Config.get().getWebAddress() + "operator/#uuid:" + ou.getUuid() + "'");

        EmbeddedApplication extApp = DAOEmbeddedApplication.createExternalApplication("");
        extApp.setUrlDefinition(ExternalUrlDefinition.SCRIPT_DEFINED);
        extApp.setHeight(500);

        GUILogon.asSuper();
        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.assertThatList();

        GUIEmbeddedApplicationList.clickAdd();
        GUIEmbeddedApplicationForm.fillAdd(extApp);

        GUIForm.clickApply();
        GUIForm.assertValidationErrorsMessages(Lists.newArrayList(ConfirmMessages.VALIDATION_REQUIRED_FIELD,
                ConfirmMessages.VALIDATION_INVALID__CODE, ConfirmMessages.VALIDATION_REQUIRED_FIELD));

        GUIEmbeddedApplicationForm.fillNewScript(script);
        GUIForm.applyForm();
        extApp.setExists(true);

        GUIEmbeddedApplicationList.clickEnable(extApp);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), extApp);
        DSLContent.add(content);

        checkAsTester(content, ou);
    }

    /**
     * Тестирование определия URL: Параметры определены скриптом, без указания скрипта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$49410294
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под супером на страницу приложения
     * <li>Открыть форму добавления приложения</li>
     * <li>Заполнить поля на форме: Тип приложения: Запущено на внешнем сервере, 
     *                              Метод определения URL: Параметры определены скриптом,
     *                              Адрес приложения: ${адрес самого приложения}/operator/#uuid:${ UUID общего отдела }
     * <li>Сохранить и включить приложение
     * <li>На карточку userCase вывести контент "Встроенное приложение", в котором будет только что созданное приложение
     * <li>Зайти под сотрудником на карочку userBo
     * <li>Проверить, что в контенте встроенного приложения отображается карточка общего отдела
     */
    @Test
    public void testScriptParametersNoScript()
    {
        EmbeddedApplication extApp = DAOEmbeddedApplication
                .createExternalApplication(Config.get().getWebAddress() + "operator/#uuid:" + ou.getUuid());
        extApp.setUrlDefinition(ExternalUrlDefinition.SCRIPT_DEFINED_PARAMETERS);
        extApp.setHeight(500);

        DSLEmbeddedApplication.add(extApp);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), extApp);
        DSLContent.add(content);

        checkAsTester(content, ou);
    }

    /**
     * Тестирование определия URL: Параметры определяются системной логикой
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$49410294
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под супером на страницу приложения
     * <li>Открыть форму добавления приложения</li>
     * <li>Заполнить поля на форме: Тип приложения: Запущено на внешнем сервере, 
     *                              Метод определения URL: Параметры определяются системной логикой
     *                              Адрес приложения: ${адрес самого приложения}/operator
     * <li>Сохранить и включить приложение
     * <li>На карточку userCase вывести контент "Встроенное приложение", в котором будет только что созданное приложение
     * <li>Зайти под сотрудником на карочку userBo
     * <li>Проверить, что в контенте встроенного приложения отображается карточка сотрудника, под котором осуществлен
     * вход
     */
    @Test
    public void testSystemDefinedLogic()
    {

        EmbeddedApplication extApp = DAOEmbeddedApplication
                .createExternalApplication(Config.get().getWebAddress() + "operator/");
        extApp.setUrlDefinition(ExternalUrlDefinition.SYSTEM_DEFINED);
        extApp.setHeight(500);

        DSLEmbeddedApplication.add(extApp);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), extApp);
        DSLContent.add(content);

        checkAsTester(content, SharedFixture.employee());
    }

    private static void checkAsTester(ContentForm content, Bo bo)
    {
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUITestEmbeddedApplication embApp = GUIEmbeddedApplication.testEmbeddedApplication(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);

        String assertResult = GUIFrame.getValueFromFrame(embApp.getXPathToIFrame(), wt ->
        {
            try
            {
                GUIBo.assertThatBoCard(bo);
                return null;
            }
            catch (Exception e)
            {
                return e.getMessage();
            }
        });
        if (!StringUtils.isEmpty(assertResult))
        {
            Assert.fail(assertResult);
        }
    }
}
