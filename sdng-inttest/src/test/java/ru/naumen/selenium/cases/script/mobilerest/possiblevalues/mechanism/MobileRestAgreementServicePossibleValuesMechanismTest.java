package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.AGGREGATED_TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.aggregatedTreeElement;
import static ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum.AGREEMENTS;
import static ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum.SERVICES;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrServicePrs;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.mobile.SysMobileAttribute;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута "Соглашение/услуга" на формах через мобильное API.
 *
 * <AUTHOR>
 * @since 28.11.2019
 */
@SuppressWarnings({ "ClassWithTooManyMethods", "GrazieInspection" }) // тестов в классе - 20
public class MobileRestAgreementServicePossibleValuesMechanismTest extends AbstractTestCase
{
    private static Attribute clientAttr, agsAttr;
    private static Folder agreementFolder, agreementNestedFolder, serviceFolder, serviceNestedFolder;
    private static Bo employee;
    private static Bo agreement, agreement2, agreement3, agreement4;
    private static Bo service, service2, service3, service4;
    private static Bo[] agreements;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;
    private static String searchPrefix;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать подтипы запроса scCase</li>
     * <li>Создать сотрудника employee и добавить его в команду</li>
     * <li>Создать папку agreementFolder в классе Соглашение и папку serviceFolder в классе Услуга</li>
     * <li>Создать префикс searchPrefix для поиска соглашений и услуг</li>
     * <li>Создать соглашения agreement, agreement2, agreement3 и agreement4, добавить префикс searchPrefix в
     * названия agreement2, agreement3 и agreement4</li>
     * <li>Создать услуги service, service2, service3 и service4, добавить префикс searchPrefix в названия service2,
     * service3 и service4</li>
     * <li>Привязать:
     * <ul>
     *     <li>к соглашению agreement услуги service, service2 и service3,</li>
     *     <li>к соглашению agreement2 услуги service2 и service4,</li>
     *     <li>привязать типы scCase к услугам service, service2, service3 и service4,</li>
     *     <li>к сотруднику employee соглашение agreement, agreement2, agreement3 и agreement4</li>
     * </ul>и </li>
     * <li>Создать дополнительно ещё 20 соглашений agreements для тестирования пагинации, связать их с сотрудником
     * employee</li>
     * <li>Создать группу пользователей secGroup, выдать ей полные права и добавить в неё employee</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа для сотрудника employee</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(scCase);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(SharedFixture.team(), employee);

        agreementFolder = DAOFolder.create(DAOAgreementCase.createClass());
        serviceFolder = DAOFolder.create(DAOServiceCase.createClass());
        DSLFolder.add(agreementFolder, serviceFolder);

        agreementNestedFolder = DAOFolder.create(DAOAgreementCase.createClass(), agreementFolder);
        serviceNestedFolder = DAOFolder.create(DAOServiceCase.createClass(), serviceFolder);
        DSLFolder.add(agreementNestedFolder, serviceNestedFolder);

        Attribute agreementFolderAttr = SysAttribute.folders(DAOAgreementCase.createClass());
        Attribute serviceFolderAttr = SysAttribute.folders(DAOServiceCase.createClass());

        searchPrefix = ModelUtils.createTitle();

        agreement = DAOAgreement.create(SharedFixture.agreementCase());
        agreementFolderAttr.setItemValue(agreementFolder);
        agreement.setUserAttribute(agreementFolderAttr);

        agreement2 = DAOAgreement.create(SharedFixture.agreementCase());
        agreement2.setTitle(searchPrefix + agreement2.getTitle());
        agreementFolderAttr.setItemValue(agreementFolder);
        agreement2.setUserAttribute(agreementFolderAttr);

        agreement3 = DAOAgreement.create(SharedFixture.agreementCase());
        agreement3.setTitle(searchPrefix + agreement3.getTitle());
        agreementFolderAttr.setItemValue(agreementNestedFolder);
        agreement3.setUserAttribute(agreementFolderAttr);

        agreement4 = DAOAgreement.create(SharedFixture.agreementCase());
        agreement4.setTitle(searchPrefix + agreement4.getTitle());

        service = DAOService.create(SharedFixture.slmCase());
        serviceFolderAttr.setItemValue(serviceFolder);
        service.setUserAttribute(serviceFolderAttr);

        service2 = DAOService.create(SharedFixture.slmCase());
        service2.setTitle(searchPrefix + service2.getTitle());
        serviceFolderAttr.setItemValue(serviceFolder);
        service2.setUserAttribute(serviceFolderAttr);

        service3 = DAOService.create(SharedFixture.slmCase());
        service3.setTitle(searchPrefix + service3.getTitle());
        serviceFolderAttr.setItemValue(serviceNestedFolder);
        service3.setUserAttribute(serviceFolderAttr);

        service4 = DAOService.create(SharedFixture.slmCase());
        service4.setTitle(searchPrefix + service4.getTitle());

        DAOBo.appendTitlePrefixes(0, 23, agreement, agreement2, agreement3, agreement4);
        DAOBo.appendTitlePrefixes(service, service2, service3, service4);
        DSLBo.add(agreement, agreement2, agreement3, agreement4, service, service2, service3, service4);
        DSLSearch.updateIndex(agreement2, agreement, agreement3, agreement4, service, service2, service3, service4);

        DSLAgreement.addServices(agreement, service, service2, service3);
        DSLAgreement.addServices(agreement2, service2, service4);
        DSLSlmService.addToScCase(scCase, service, service2, service3, service4);
        DSLAgreement.addRecipients(employee, agreement, agreement2, agreement3, agreement4);

        int expectedCount = 20;
        agreements = new Bo[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            agreements[i] = DAOAgreement.create(SharedFixture.agreementCase());
        }
        DAOBo.appendTitlePrefixes(3, 23, agreements);
        DSLBo.add(agreements);
        DSLAgreement.addRecipients(employee, agreements);
        DSLSearch.updateIndex(agreements);

        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);

        SecurityProfile secProfile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissions(secProfile);

        Attribute metaClassAttr = SysAttribute.metaClass(scCase);
        clientAttr = SysAttribute.client(scCase);
        agsAttr = SysMobileAttribute.agreementService(scCase);

        addForm = DAOMobile.createMobileAddForm(scCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, clientAttr, metaClassAttr, agsAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(employee);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Иерархический список",
     * когда возможными значениями могут являться и соглашения и услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement, agreement2, agreement3, agreement4 и соглашений
     *     agreements,</li>
     *     <li>в agreement вложены услуги service, service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулся головной уровень дерева объектов:
     * <ul>
     *     <li>состоящий из соглашений agreement, agreement2, agreement3, agreement4 и 16 первых соглашений
     *     agreements,</li>
     *     <li>у первых двух соглашений флаг наличия дочерних элементов - true</li>
     * </ul></li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreement,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений service, service2
     * и service3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreement2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений service2 и
     * service4</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementService()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        tree(
                                treeElement(agreement).children(
                                        treeElement(service),
                                        treeElement(service2),
                                        treeElement(service3)
                                ),
                                treeElement(agreement2).children(
                                        treeElement(service2),
                                        treeElement(service4)
                                ),
                                treeElement(agreement3),
                                treeElement(agreement4)
                        ).with(
                                treeElements(agreements)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        tree(
                                aggregatedTreeElement(agreement).leaf(false),
                                aggregatedTreeElement(agreement2).leaf(false),
                                aggregatedTreeElement(agreement3),
                                aggregatedTreeElement(agreement4)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(agreements, 16))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreement2)
                .setForm(addForm);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement2, service2),
                        aggregatedTreeElement(agreement2, service4)
                );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreement)
                .setForm(addForm);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement, service),
                        aggregatedTreeElement(agreement, service2),
                        aggregatedTreeElement(agreement, service3)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Иерархический список",
     * когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement и agreement2,</li>
     *     <li>соглашения не доступны для выбора,</li>
     *     <li>в agreement вложены услуги service, service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулся головной уровень дерева объектов:
     * <ul>
     *     <li>состоящий из соглашений agreement и agreement2,</li>
     *     <li>оба не доступны для выбора,</li>
     *     <li>у обоих флаг наличия дочерних элементов - true</li>
     * </ul></li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreement,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений service, service2
     * и service3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreement2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений service2 и
     * service4</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesAndTreeListPresentation()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.TREE_LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        treeElement(agreement).selectable(false).children(
                                treeElement(service),
                                treeElement(service2),
                                treeElement(service3)
                        ),
                        treeElement(agreement2).selectable(false).children(
                                treeElement(service2),
                                treeElement(service4)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement).leaf(false).selectable(false),
                        aggregatedTreeElement(agreement2).leaf(false).selectable(false)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreement)
                .setForm(addForm);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement, service),
                        aggregatedTreeElement(agreement, service2),
                        aggregatedTreeElement(agreement, service3)
                );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreement2)
                .setForm(addForm);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement2, service2),
                        aggregatedTreeElement(agreement2, service4)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Плоский список",
     * когда возможными значениями могут являться и соглашения и услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить представление для редактирования поля "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement, agreement2, agreement3, agreement4 и соглашений
     *     agreements,</li>
     *     <li>соглашения не доступны для выбора,</li>
     *     <li>в agreement вложены услуги service, service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулся головной уровень дерева объектов, состоящий из папок "Соглашения",
     * "Услуги"</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = "Соглашения",</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений agreement,
     * agreement2, agreement3, agreement4 и 16 первых соглашений agreements</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = "Услуги",</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашений service,
     * service2 (для agreement и agreement2), service3 и service4</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithListPresentation()
    {
        DSLScParams.editAgrService(AgrService.BOTH, AgrServicePrs.LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        tree(
                                treeElement(agreement).children(
                                        treeElement(service),
                                        treeElement(service2),
                                        treeElement(service3)
                                ),
                                treeElement(agreement2).children(
                                        treeElement(service2),
                                        treeElement(service4)
                                ),
                                treeElement(agreement3),
                                treeElement(agreement4)
                        ).with(
                                treeElements(agreements)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        agreementsFolderElement().leaf(false),
                        servicesFolderElement().leaf(false)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(AGREEMENTS.getCode())
                .setForm(addForm);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, agsAttr, V15)
                .assertValues(
                        tree(
                                aggregatedTreeElement(agreement),
                                aggregatedTreeElement(agreement2),
                                aggregatedTreeElement(agreement3),
                                aggregatedTreeElement(agreement4)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(agreements, 16))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(SERVICES.getCode())
                .setForm(addForm);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement, service),
                        aggregatedTreeElement(agreement, service2, true),
                        aggregatedTreeElement(agreement2, service2, true),
                        aggregatedTreeElement(agreement, service3),
                        aggregatedTreeElement(agreement2, service4)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Плоский список",
     * когда возможными значениями могут являться только соглашения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, состоящее из соглашений agreement, agreement2,
     * agreement3, agreement4 и соглашений agreements</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулось дерево объектов, состоящее из соглашений agreement, agreement2,
     * agreement3, agreement4 и 16 первых соглашений agreements</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithAgreementsAndListPresentation()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        tree(
                                treeElement(agreement),
                                treeElement(agreement2),
                                treeElement(agreement3),
                                treeElement(agreement4)
                        ).with(
                                treeElements(agreements)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        tree(
                                aggregatedTreeElement(agreement),
                                aggregatedTreeElement(agreement2),
                                aggregatedTreeElement(agreement3),
                                aggregatedTreeElement(agreement4)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(agreements, 16))
                        ).hasMore(true)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Плоский список",
     * когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement и agreement2,</li>
     *     <li>соглашения не доступны для выбора,</li>
     *     <li>в agreement вложены услуги service, service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, состоящее из соглашений service,
     * service2 (для agreement и agreement2), service3 и service4</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesAndListPresentation()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        treeElement(agreement).selectable(false).children(
                                treeElement(service),
                                treeElement(service2),
                                treeElement(service3)
                        ),
                        treeElement(agreement2).selectable(false).children(
                                treeElement(service2),
                                treeElement(service4)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement, service),
                        aggregatedTreeElement(agreement, service2, true),
                        aggregatedTreeElement(agreement2, service2, true),
                        aggregatedTreeElement(agreement, service3),
                        aggregatedTreeElement(agreement2, service4)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Выбор из каталога",
     * когда возможными значениями могут являться только соглашения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, состоящее из соглашений agreement, agreement2,
     * agreement3, agreement4 и соглашений agreements</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся родительский уровень дерева объектов, состоящий из папки
     * agreementFolder, соглашения agreement4 и 18 первых соглашений agreements</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreementFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из папки agreementNestedFolder и
     * соглашений agreement и agreement2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = agreementNestedFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из услуги agreement3</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithAgreementsAndFoldersTreePresentation()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.FOLDERS_TREE);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        tree(
                                treeElement(agreement),
                                treeElement(agreement2),
                                treeElement(agreement3),
                                treeElement(agreement4)
                        ).with(
                                aggregatedTreeElements(agreements)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        tree(
                                aggregatedFolderElement(agreementFolder),
                                aggregatedTreeElement(agreement4)
                        ).with(
                                aggregatedTreeElements(Arrays.copyOf(agreements, 18))
                        ).hasMore(true)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreementFolder)
                .setForm(addForm);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);
        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, agsAttr, V15)
                .assertValues(
                        aggregatedFolderElement(agreementNestedFolder),
                        aggregatedTreeElement(agreement),
                        aggregatedTreeElement(agreement2)
                );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(agreementNestedFolder)
                .setForm(addForm);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);
        pvResponse4.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement3)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование получения возможных значений атрибута Соглашение/Услуга с представлением "Выбор из каталога",
     * когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement и agreement2,</li>
     *     <li>соглашения не доступны для выбора,</li>
     *     <li>в agreement вложены услуги service, service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся родительский уровень дерева объектов, состоящий из папки serviceFolder и
     * услуги service4</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = serviceFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из папки serviceNestedFolder и
     * соглашений service и service2 (для agreement и agreement2)</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = serviceNestedFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся дочерний уровень дерева объектов, состоящий из соглашения service3</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут Соглашение/услуга имеет тип выбора - дерево</li>
     * </ol>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesAndFoldersTreePresentation()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.FOLDERS_TREE);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        treeElement(agreement).selectable(false).children(
                                treeElement(service),
                                treeElement(service2),
                                treeElement(service3)
                        ),
                        treeElement(agreement2).selectable(false).children(
                                treeElement(service2),
                                treeElement(service4)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        aggregatedFolderElement(serviceFolder),
                        aggregatedTreeElement(agreement2, service4)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(serviceFolder)
                .setForm(addForm);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);
        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, agsAttr, V15)
                .assertValues(
                        aggregatedFolderElement(serviceNestedFolder),
                        aggregatedTreeElement(agreement, service),
                        aggregatedTreeElement(agreement, service2, true),
                        aggregatedTreeElement(agreement2, service2, true)
                );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setParent(serviceNestedFolder)
                .setForm(addForm);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);
        pvResponse4.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElement(agreement, service3)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(agsAttr)
                .hasEditPresentationSelectType(AGGREGATED_TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Иерархический список", когда возможными значениями могут являться и соглашения и услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement, agreement2, agreement3 и agreement4,</li>
     *     <li>в agreement вложены услуги service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement, agreement2, agreement3 и agreement4,</li>
     *     <li>в agreement вложены услуги service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr)
                .assertValues(
                        treeSearch(
                                treeElement(agreement).children(
                                        treeElement(service2),
                                        treeElement(service3)
                                ),
                                treeElement(agreement2).children(
                                        treeElement(service2),
                                        treeElement(service4)
                                ),
                                treeElement(agreement3),
                                treeElement(agreement4)
                        ).foundAmount(7)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedTreeElement(agreement).children(
                                        aggregatedTreeElement(agreement, service2),
                                        aggregatedTreeElement(agreement, service3)
                                ),
                                aggregatedTreeElement(agreement2).children(
                                        aggregatedTreeElement(agreement2, service2),
                                        aggregatedTreeElement(agreement2, service4)
                                ),
                                aggregatedTreeElement(agreement3),
                                aggregatedTreeElement(agreement4)
                        ).foundAmount(7)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Иерархический список", когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из соглашений agreement и agreement2,</li>
     *     <li>соглашения не доступны для выбора,</li>
     *     <li>в agreement вложены услуги service2 и service3,</li>
     *     <li>в agreement2 вложены услуги service2 и service4</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.TREE_LIST);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedTreeElement(agreement).selectable(false).children(
                                        aggregatedTreeElement(agreement, service2),
                                        aggregatedTreeElement(agreement, service3)
                                ),
                                aggregatedTreeElement(agreement2).selectable(false).children(
                                        aggregatedTreeElement(agreement2, service2),
                                        aggregatedTreeElement(agreement2, service4)
                                )
                        ).foundAmount(5)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Плоский список", когда возможными значениями могут являться и соглашения и услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить представление для редактирования поля "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из папок "Соглашения" и "Услуги",</li>
     *     <li>в папку "Соглашения" вложены соглашения agreement2, agreement3 и agreement4,</li>
     *     <li>в папку "Услуги" вложены услуги service2 (для agreement и agreement2), service3 и service4</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithListPresentationWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.BOTH, AgrServicePrs.LIST);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                agreementsFolderElement().children(
                                        aggregatedTreeElement(agreement2),
                                        aggregatedTreeElement(agreement3),
                                        aggregatedTreeElement(agreement4)
                                ),
                                servicesFolderElement().children(
                                        aggregatedTreeElement(agreement, service2, true),
                                        aggregatedTreeElement(agreement2, service2, true),
                                        aggregatedTreeElement(agreement, service3),
                                        aggregatedTreeElement(agreement2, service4)
                                )
                        ).foundAmount(7)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Плоский список", когда возможными значениями могут являться только соглашения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, состоящее из соглашений agreement2, agreement3 и
     * agreement4</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithAgreementsAndListPresentationWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.LIST);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();

        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedTreeElement(agreement2),
                                aggregatedTreeElement(agreement3),
                                aggregatedTreeElement(agreement4)
                        )
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Плоский список", когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, состоящее из соглашений service2 (для agreement и
     * agreement2), service3 и service4</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesAndListPresentationWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.LIST);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedTreeElement(agreement, service2, true),
                                aggregatedTreeElement(agreement2, service2, true),
                                aggregatedTreeElement(agreement, service3),
                                aggregatedTreeElement(agreement2, service4)
                        )
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Выбор из каталога", когда возможными значениями могут являться только соглашения.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из папки agreementFolder и соглашения agreement4,</li>
     *     <li>в agreementFolder вложены папка agreementNestedFolder и соглашения agreement2,</li>
     *     <li>в agreementNestedFolder вложены соглашения agreement3</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithAgreementsAndFoldersTreePresentationWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.FOLDERS_TREE);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedFolderElement(agreementFolder).selectable(false).children(
                                        aggregatedFolderElement(agreementNestedFolder).selectable(false).children(
                                                aggregatedTreeElement(agreement3)
                                        ),
                                        aggregatedTreeElement(agreement2)
                                ),
                                aggregatedTreeElement(agreement4)
                        ).foundAmount(3)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа Соглашение/Услуга с представлением
     * "Выбор из каталога", когда возможными значениями могут являться только услуги.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Услуга", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов:
     * <ul>
     *     <li>на головном уровне состоящее из папки serviceFolder и соглашения service4,</li>
     *     <li>в serviceFolder вложены папка serviceNestedFolder и соглашения service2 (для agreement и agreement2),
     *     </li>
     *     <li>в serviceNestedFolder вложены соглашения service3</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithServicesAndFoldersTreePresentationWhenPerformsSearch()
    {
        DSLScParams.editAgrService(AgrService.SERVICE, AgrServicePrs.FOLDERS_TREE);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(searchPrefix);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        treeSearch(
                                aggregatedFolderElement(serviceFolder).selectable(false).children(
                                        aggregatedFolderElement(serviceNestedFolder).selectable(false).children(
                                                aggregatedTreeElement(agreement, service3)
                                        ),
                                        aggregatedTreeElement(agreement, service2, true),
                                        aggregatedTreeElement(agreement2, service2, true)
                                ),
                                aggregatedTreeElement(agreement2, service4)
                        ).foundAmount(4)
                );
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа Соглашение/Услуга с
     * представлением "Иерархический список".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся родительский уровень дерева объектов, состоящий из последних 14
     * соглашений agreements</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setMetaClass(null)
                .setAttribute(agsAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr).assertValues(tree());

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElements(Arrays.copyOfRange(agreements, 6, 20))
                );
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа Соглашение/Услуга с
     * представлением "Плоский список".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить представление для редактирования поля "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithListPresentationWhenOffsetMoreThenZero()
    {
        DSLScParams.editAgrService(AgrService.BOTH, AgrServicePrs.LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15).assertValues(tree());
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута типа Соглашение/Услуга с
     * представлением "Выбор из каталога".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся родительский уровень дерева объектов, состоящий из последних 12
     * соглашений agreements</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithFoldersTreePresentationWhenOffsetMoreThenZero()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.FOLDERS_TREE);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15)
                .assertValues(
                        aggregatedTreeElements(Arrays.copyOfRange(agreements, 8, 20))
                );
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута Соглашение/Услуга с представлением "Иерархический список". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(searchPrefix);

        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, version).assertValues(treeSearch());
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута Соглашение/Услуга с представлением "Плоский список". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить представление для редактирования поля "Соглашение/Услуга" в значение "Плоский список"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithListPresentationWhenPerformsSearchAndOffsetMoreThenZero()
    {
        DSLScParams.editAgrService(AgrService.BOTH, AgrServicePrs.LIST);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(searchPrefix);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15).assertValues(treeSearch());
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута Соглашение/Услуга с представлением "Выбор из каталога". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$297727224
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Установить поле "Соглашение/Услуга" в значение "Соглашение", а представление для редактирования поля
     * "Соглашение/Услуга" в значение "Выбор из каталога"</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Соглашение/услуга",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = searchPrefix,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Контрагент" = employee (и отдел),</li>
     *         <li>"Соглашение/услуга" = null,</li>
     *         <li>"Тип объекта" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testAgreementServiceWithFoldersTreePresentationWhenPerformsSearchAndOffsetMoreThenZero()
    {
        DSLScParams.editAgrService(AgrService.AGREEMENT, AgrServicePrs.FOLDERS_TREE);

        // Выполнение действий и проверки
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setComplexAttribute(clientAttr, employee, SharedFixture.ou())
                .setAttribute(agsAttr, null)
                .setMetaClass(null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(agsAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(searchPrefix);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, agsAttr, V15).assertValues(treeSearch());
    }
}