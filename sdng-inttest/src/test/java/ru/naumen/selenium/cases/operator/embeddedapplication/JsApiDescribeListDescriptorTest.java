package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.model.content.advlist.ListFiltrationToTextConverter.getDescription;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.DateTimeInterval;
import ru.naumen.selenium.casesutil.attr.Hyperlink;
import ru.naumen.selenium.casesutil.attr.Interval;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тесты на метод jsApi.listdata.describeFiltration(descriptor) с разными типами ограничений и атрибутов
 *
 * <AUTHOR>
 * @since 14.10.2021
 */
class JsApiDescribeListDescriptorTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    public static final String LIST_DESCRIPTION_AS_JSON =
            "def descriptor = api.listdata.createListDescriptor('%s', '%s')\n"
            + "api.listdata.listDescriptorAsJson(descriptor)";
    public static final String DESCRIBE_FILTRATION = "jsApi.listdata.describeFiltration(%s)\n"
                                                     + "    .then(html => document.getElementById('%s').innerText = "
                                                     + "html)";
    private static MetaClass employeeCase, employeeChildCase, employeeChildCase2;
    private static Bo employee, employee2, ou;
    private static ContentForm objectList;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать тип сотрудника employeeCase и два его подтипа: employeeChildCase, employeeChildCase2/li>
     * <li>Создать сотрудников employee, employee2 типа employeeChildCase</li>
     * <li>Создать контент objectList типа "Список объектов" с представлением "Сложный список" и
     * группой атрибутов "Системные атрибуты"</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        employeeCase = DAOEmployeeCase.create();
        employeeChildCase = DAOEmployeeCase.create(employeeCase);
        employeeChildCase2 = DAOEmployeeCase.create(employeeCase);
        DSLMetaClass.add(employeeCase, employeeChildCase, employeeChildCase2);

        ou = SharedFixture.ou();
        employee = DAOEmployee.create(employeeChildCase, ou, true);
        employee2 = DAOEmployee.create(employeeChildCase2, ou, true);
        DAOBo.appendTitlePrefixes(employee, employee2);
        DSLBo.add(employee, employee2);

        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(attrGroup);

        objectList = DAOContentCard.createObjectAdvList(employeeCase.getFqn(), attrGroup, DAOEmployeeCase.createClass(),
                employeeCase);
        DSLContent.add(objectList);
    }

    /**
     * <ol>
     * <b>Очистка</b>
     * <li>Очистить фильтрацию из контента objectList</li>
     * </ol>
     */
    @AfterEach
    public void cleanUp()
    {
        objectList.setDefaultListFilter(null);
        DSLContent.edit(objectList);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Ссылка на БО"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут objectAttr типа "Ссылка на БО" на тип employeeCase</li>
     * <li>Создать в типе employeeCase атрибут objectAttr2 типа "Ссылка на БО" на тип ouCase</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "содержит" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>%ФИО employee%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "не содержит" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: не <span>%ФИО employee%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "содержит любое из значений"
     * [employee, employee2]</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%objectAttr%: <span>%ФИО employee%, %ФИО employee2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "название содержит" название employee2</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>%ФИО employee2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "название не содержит" название employee2</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: не <span>%ФИО employee2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>не заполнен</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "не пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>заполнен</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "содержит (включая архивные)" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>%ФИО employee%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "не содержит (включая архивные)" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: не <span>%ФИО employee%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "равно текущему объекту"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>текущий объект</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr "равно текущему пользователю"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr%: <span>я сам (сама)</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут objectAttr2 "содержит (включая вложенные)" ou</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%objectAttr2% объекта: <span>%название ou%</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForObjectType()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(employeeCase, employeeCase, null);
        Attribute object2Attr = DAOAttribute.createObjectLink(employeeCase, SharedFixture.ouCase(), null);
        DSLAttribute.add(objectAttr, object2Attr);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS, false,
                employee.getUuid());
        String containsDescription = getDescription(objectAttr, FilterCondition.CONTAINS, employee);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr notContainsCondition = new FilterBlockOr(objectAttr, FilterCondition.NOT_CONTAINS, false,
                employee.getUuid());
        String notContainsDescription = getDescription(objectAttr, FilterCondition.NOT_CONTAINS, employee);
        assertDescription(notContainsCondition, notContainsDescription);

        FilterBlockOr containsAnyCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS_IN_SET, true,
                employee.getUuid(), employee2.getUuid());
        String containsAnyDescription = getDescription(objectAttr, FilterCondition.CONTAINS_IN_SET, employee,
                employee2);
        assertDescription(containsAnyCondition, containsAnyDescription);

        FilterBlockOr titleContainsCondition = new FilterBlockOr(objectAttr, FilterCondition.TITLE_CONTAINS, false,
                employee2.getTitle());
        String titleContainsDescription = getDescription(objectAttr, FilterCondition.TITLE_CONTAINS, employee2);
        assertDescription(titleContainsCondition, titleContainsDescription);

        FilterBlockOr titleNotContainsCondition = new FilterBlockOr(objectAttr, FilterCondition.TITLE_NOT_CONTAINS,
                false, employee2.getTitle());
        String titleNotContainsDescription = getDescription(objectAttr, FilterCondition.TITLE_NOT_CONTAINS, employee2);
        assertDescription(titleNotContainsCondition, titleNotContainsDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(objectAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(objectAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);

        FilterBlockOr notEmptyCondition = new FilterBlockOr(objectAttr, FilterCondition.NOT_EMPTY, false);
        String notEmptyDescription = getDescription(objectAttr, FilterCondition.NOT_EMPTY);
        assertDescription(notEmptyCondition, notEmptyDescription);

        FilterBlockOr containsRemovedCondition = new FilterBlockOr(objectAttr, FilterCondition.CONTAINS_WITH_REMOVED,
                false, employee.getUuid());
        assertDescription(containsRemovedCondition, containsDescription);

        FilterBlockOr notContainsWithRemovedCondition = new FilterBlockOr(objectAttr,
                FilterCondition.NOT_CONTAINS_WITH_REMOVED, false, employee.getUuid());
        String notContainsWithRemovedDescription = getDescription(objectAttr, FilterCondition.NOT_CONTAINS_WITH_REMOVED,
                employee);
        assertDescription(notContainsWithRemovedCondition, notContainsWithRemovedDescription);

        FilterBlockOr equalsSubjectCondition = new FilterBlockOr(objectAttr, FilterCondition.EQUALS_SUBJECT, false);
        String equalsSubjectDescription = getDescription(objectAttr, FilterCondition.EQUALS_SUBJECT);
        assertDescription(equalsSubjectCondition, equalsSubjectDescription);

        FilterBlockOr equalsUserCondition = new FilterBlockOr(objectAttr, FilterCondition.MYSELF, false);
        String equalsUserDescription = getDescription(objectAttr, FilterCondition.MYSELF);
        assertDescription(equalsUserCondition, equalsUserDescription);

        FilterBlockOr containsWithNestedCondition = new FilterBlockOr(object2Attr,
                FilterCondition.CONTAINS_WITH_NESTED, false, ou.getUuid());
        String containsWithNestedDescription = getDescription(object2Attr, FilterCondition.CONTAINS_WITH_NESTED, ou);
        assertDescription(containsWithNestedCondition, containsWithNestedDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с особенными условиями для атрибутов
     * типа "Набор ссылок на БО", которые дополняют условия доступные для "Ссылка на БО"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут boLinksAttr типа "Набор ссылок на БО" на тип employeeCase</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут boLinksAttr "содержит текущий объект"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%boLinksAttr%: <span>текущий объект</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут boLinksAttr "содержит текущего пользователя"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%boLinksAttr%: <span>я сам (сама)</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForBoLinksType()
    {
        // Подготовка
        Attribute boLinksAttr = DAOAttribute.createBoLinks(employeeCase, employeeCase);
        DSLAttribute.add(boLinksAttr);

        // Действия и проверки
        FilterBlockOr equalsSubjectCondition = new FilterBlockOr(boLinksAttr, FilterCondition.CONTAINS_SUBJECT, false);
        String equalsSubjectDescription = getDescription(boLinksAttr, FilterCondition.CONTAINS_SUBJECT);
        assertDescription(equalsSubjectCondition, equalsSubjectDescription);

        FilterBlockOr equalsUserCondition = new FilterBlockOr(boLinksAttr, FilterCondition.CONTAINS_USER, false);
        String equalsUserDescription = getDescription(boLinksAttr, FilterCondition.CONTAINS_USER);
        assertDescription(equalsUserCondition, equalsUserDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Элемент справочника"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут catalogItemAttr типа "Элемент справочника" на справочник</li>
     * <li>Создать элементы item, item2 справочника</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут catalogItemAttr "содержит" item</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%catalogItemAttr%: <span>%название item%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут catalogItemAttr "содержит любое из значений"
     * [item, item2]</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%catalogItemAttr%: <span>%название item%, %название item2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут catalogItemAttr "название содержит" название
     * item2</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%catalogItemAttr%: <span>%название item2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут catalogItemAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%catalogItemAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForCatalogItemType()
    {
        Catalog catalog = DAOCatalog.createSystem(SystemCatalog.CLOSURECODE);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(employeeCase, catalog, null);
        DSLAttribute.add(catalogItemAttr);

        CatalogItem item = DAOCatalogItem.createClosureCode();
        CatalogItem item2 = DAOCatalogItem.createClosureCode();
        DSLCatalogItem.add(item, item2);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(catalogItemAttr, FilterCondition.CONTAINS, false,
                item.getUuid());
        String containsDescription = getDescription(catalogItemAttr, FilterCondition.CONTAINS, item);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr containsAnyCondition = new FilterBlockOr(catalogItemAttr, FilterCondition.CONTAINS_IN_SET, true,
                item.getUuid(), item2.getUuid());
        String containsAnyDescription = getDescription(catalogItemAttr, FilterCondition.CONTAINS_IN_SET, item, item2);
        assertDescription(containsAnyCondition, containsAnyDescription);

        FilterBlockOr titleContainsCondition = new FilterBlockOr(catalogItemAttr, FilterCondition.TITLE_CONTAINS, false,
                item2.getTitle());
        String titleContainsDescription = getDescription(catalogItemAttr, FilterCondition.TITLE_CONTAINS, item2);
        assertDescription(titleContainsCondition, titleContainsDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(catalogItemAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(catalogItemAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Агрегирующий"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут aggregateAttr типа "Агрегирующий"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "содержит" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%aggregateAttr%: <span>%ФИО employee%/%название ou%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "содержит любое из значений"
     * [employee, employee2]</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%aggregateAttr%: <span>%ФИО employee%, %ФИО employee2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "название содержит" название employee2</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%aggregateAttr%: <span>%ФИО employee2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "равно текущему объекту"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%aggregateAttr%: <span>текущий объект</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "равно текущему пользователю"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%aggregateAttr%: <span>я сам (сама)</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут aggregateAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%aggregateAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForAggregateType()
    {
        Attribute aggregateAttr = DAOAttribute.createAggregate(employeeCase, AggregatedClasses.OU, null, null);
        DSLAttribute.add(aggregateAttr);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(aggregateAttr, FilterCondition.CONTAINS, false,
                employee.getUuid() + ',' + ou.getUuid());
        String containsDescription = getDescription(aggregateAttr, FilterCondition.CONTAINS, List.of(employee, ou));
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr containsAnyCondition = new FilterBlockOr(aggregateAttr, FilterCondition.CONTAINS_IN_SET, true,
                employee.getUuid() + ',' + ou.getUuid(), employee2.getUuid() + ',' + ou.getUuid());
        String containsAnyDescription = getDescription(aggregateAttr, FilterCondition.CONTAINS_IN_SET,
                List.of(employee, ou), List.of(employee2, ou));
        assertDescription(containsAnyCondition, containsAnyDescription);

        FilterBlockOr titleContainsCondition = new FilterBlockOr(aggregateAttr, FilterCondition.TITLE_CONTAINS, false,
                employee2.getTitle());
        String titleContainsDescription = getDescription(aggregateAttr, FilterCondition.TITLE_CONTAINS, employee2);
        assertDescription(titleContainsCondition, titleContainsDescription);

        FilterBlockOr equalsSubjectCondition = new FilterBlockOr(aggregateAttr, FilterCondition.EQUALS_SUBJECT, false);
        String equalsSubjectDescription = getDescription(aggregateAttr, FilterCondition.EQUALS_SUBJECT);
        assertDescription(equalsSubjectCondition, equalsSubjectDescription);

        FilterBlockOr equalsUserCondition = new FilterBlockOr(aggregateAttr, FilterCondition.MYSELF, false);
        String equalsUserDescription = getDescription(aggregateAttr, FilterCondition.MYSELF);
        assertDescription(equalsUserCondition, equalsUserDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(aggregateAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(aggregateAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутом "Тип объекта"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут metaClassAttr "содержит" employeeChildCase</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[Тип объекта: <span>%название employeeChildCase%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут metaClassAttr "содержит (включая вложенные)"
     * employeeChildCase, employeeChildCase2</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[Тип объекта: <span>[%fqn типа employeeChildCase%, %fqn типа employeeChildCase2%]</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут metaClassAttr "содержит любое из значений"
     * "[employeeChildCase, employeeChildCase2]</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[Тип объекта: <span>%название employeeChildCase%, %название employeeChildCase2%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут metaClassAttr "название содержит" название
     * employeeChildCase2</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[Тип объекта: <span>%название employeeChildCase2%</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForMetaClassType()
    {
        // Действия и проверки
        Attribute metaClassAttr = SysAttribute.metaClass(employeeCase);
        FilterBlockOr containsCondition = new FilterBlockOr(metaClassAttr, FilterCondition.CONTAINS, false,
                employeeChildCase.getFqn());
        containsCondition.setAttributeFqn(employeeCase.getFqn() + "@" + metaClassAttr.getCode());
        String containsDescription = getDescription(metaClassAttr, FilterCondition.CONTAINS, employeeChildCase);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr containsWithNestedCondition = new FilterBlockOr(metaClassAttr,
                FilterCondition.CONTAINS_WITH_NESTED, true, employeeChildCase.getFqn(), employeeChildCase2.getFqn());
        containsWithNestedCondition.setAttributeFqn(employeeCase.getFqn() + "@" + metaClassAttr.getCode());
        String containsWithNestedDescription = getDescription(metaClassAttr, FilterCondition.CONTAINS_WITH_NESTED,
                employeeChildCase, employeeChildCase2);
        assertDescription(containsWithNestedCondition, containsWithNestedDescription);

        FilterBlockOr containsAnyCondition = new FilterBlockOr(metaClassAttr, FilterCondition.CONTAINS_IN_SET, true,
                employeeChildCase.getFqn(), employeeChildCase2.getFqn());
        containsAnyCondition.setAttributeFqn(employeeCase.getFqn() + "@" + metaClassAttr.getCode());
        String containsAnyDescription = getDescription(metaClassAttr, FilterCondition.CONTAINS_IN_SET,
                employeeChildCase, employeeChildCase2);
        assertDescription(containsAnyCondition, containsAnyDescription);

        FilterBlockOr titleContainsCondition = new FilterBlockOr(metaClassAttr, FilterCondition.TITLE_CONTAINS, false,
                employeeChildCase2.getTitle());
        titleContainsCondition.setAttributeFqn(employeeCase.getFqn() + "@" + metaClassAttr.getCode());
        String titleContainsDescription = getDescription(metaClassAttr, FilterCondition.TITLE_CONTAINS,
                employeeChildCase2);
        assertDescription(titleContainsCondition, titleContainsDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Набор типов класса"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут caseListAttr типа "Набор типов класса" на тип employeeCase</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут caseListAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%caseListAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForCaseListType()
    {
        Attribute caseListAttr = DAOAttribute.createCaseList(employeeCase.getFqn(), employeeCase);
        DSLAttribute.add(caseListAttr);

        // Действия и проверки
        FilterBlockOr emptyCondition = new FilterBlockOr(caseListAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(caseListAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Логический"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут booleanAttr типа "Логический" с представлением Да/Нет</li>
     * <li>Создать в типе employeeCase атрибут booleanAttr2 типа "Логический" с представлением 1/0</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут booleanAttr "содержит" false</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%booleanAttr%: <span>нет</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут booleanAttr "содержит" true</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%booleanAttr%: <span>да</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут booleanAttr2 "содержит" false</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%booleanAttr%: <span>0</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут booleanAttr2 "содержит" true</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%booleanAttr%: <span>1</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForBooleanType()
    {
        Attribute booleanAttr = DAOAttribute.createBool(employeeCase);
        booleanAttr.setViewPresentation(BooleanType.VIEW_YES_NO);
        Attribute booleanAttr2 = DAOAttribute.createBool(employeeCase);
        booleanAttr2.setViewPresentation(BooleanType.VIEW_ONE_ZERO);
        DSLAttribute.add(booleanAttr, booleanAttr2);

        // Действия и проверки
        FilterBlockOr containsFalseCondition = new FilterBlockOr(booleanAttr, FilterCondition.CONTAINS, false, false);
        String containsFalseDescription = getDescription(booleanAttr, FilterCondition.CONTAINS, false);
        assertDescription(containsFalseCondition, containsFalseDescription);

        FilterBlockOr containsCondition = new FilterBlockOr(booleanAttr, FilterCondition.CONTAINS, false, true);
        String containsDescription = getDescription(booleanAttr, FilterCondition.CONTAINS, true);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr containsZeroCondition = new FilterBlockOr(booleanAttr2, FilterCondition.CONTAINS, false, false);
        String containsZeroDescription = getDescription(booleanAttr2, FilterCondition.CONTAINS, false);
        assertDescription(containsZeroCondition, containsZeroDescription);

        FilterBlockOr containsOneCondition = new FilterBlockOr(booleanAttr2, FilterCondition.CONTAINS, false, true);
        String containsOneDescription = getDescription(booleanAttr2, FilterCondition.CONTAINS, true);
        assertDescription(containsOneCondition, containsOneDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Файл"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут fileAttr типа "Файл"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут fileAttr "содержит" false</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%fileAttr%: <span>не содержит файлов</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут fileAttr "содержит" true</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%fileAttr%: <span>содержит файлы</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForFileType()
    {
        Attribute fileAttr = DAOAttribute.createFile(employeeCase);
        DSLAttribute.add(fileAttr);

        // Действия и проверки
        FilterBlockOr containsFalseCondition = new FilterBlockOr(fileAttr, FilterCondition.CONTAINS, false,
                false);
        String containsFalseDescription = getDescription(fileAttr, FilterCondition.CONTAINS, false);
        assertDescription(containsFalseCondition, containsFalseDescription);

        FilterBlockOr containsCondition = new FilterBlockOr(fileAttr, FilterCondition.CONTAINS, false,
                true);
        String containsDescription = getDescription(fileAttr, FilterCondition.CONTAINS, true);
        assertDescription(containsCondition, containsDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Строка"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут stringAttr типа "Строка"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут stringAttr "содержит" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%stringAttr%: <span>%ФИО employee%</span>]"<li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут stringAttr "не содержит (и не пусто)" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%stringAttr%: не <span>%ФИО employee%</span>]"<li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут stringAttr "не содержит (включая пустые)" employee</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%stringAttr%: не <span>%ФИО employee%</span>]"<li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут stringAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%stringAttr%: <span>не заполнен/span>]"<li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут stringAttr "не пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%stringAttr%: <span>заполнен</span>]"<li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForStringType()
    {
        Attribute stringAttr = DAOAttribute.createString(employeeCase);
        DSLAttribute.add(stringAttr);

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(stringAttr, FilterCondition.CONTAINS, false,
                employee.getTitle());
        String containsDescription = getDescription(stringAttr, FilterCondition.CONTAINS, employee);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr notContainsCondition = new FilterBlockOr(stringAttr, FilterCondition.NOT_CONTAINS_AND_NOT_EMPTY,
                false, employee.getTitle());
        String notContainsDescription = getDescription(stringAttr, FilterCondition.NOT_CONTAINS_AND_NOT_EMPTY,
                employee);
        assertDescription(notContainsCondition, notContainsDescription);

        FilterBlockOr notContainsWithEmptyCondition = new FilterBlockOr(stringAttr,
                FilterCondition.NOT_CONTAINS_INCLUDE_EMPTY, false, employee.getTitle());
        String notContainsWithEmptyDescription = getDescription(stringAttr, FilterCondition.NOT_CONTAINS_INCLUDE_EMPTY,
                employee);
        assertDescription(notContainsWithEmptyCondition, notContainsWithEmptyDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(stringAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(stringAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);

        FilterBlockOr notEmptyCondition = new FilterBlockOr(stringAttr, FilterCondition.NOT_EMPTY, false);
        String notEmptyDescription = getDescription(stringAttr, FilterCondition.NOT_EMPTY);
        assertDescription(notEmptyCondition, notEmptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Гиперссылка"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут hyperlinkAttr типа "Гиперссылка"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут hyperlinkAttr "содержит"
     * [text=%ФИО employee%, url=%идентификатор employee%]</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%hyperlinkAttr%: <span>%text% (%url%)</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут hyperlinkAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%hyperlinkAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForHyperLinkType()
    {
        Attribute hyperlinkAttr = DAOAttribute.createHyperlink(employeeCase.getFqn());
        DSLAttribute.add(hyperlinkAttr);

        Hyperlink hyperlink = new Hyperlink(employee.getTitle(), employee.getUuid());

        // Действия и проверки
        FilterBlockOr containsCondition = new FilterBlockOr(hyperlinkAttr, FilterCondition.CONTAINS, false,
                hyperlink);
        String containsDescription = getDescription(hyperlinkAttr, FilterCondition.CONTAINS, hyperlink);
        assertDescription(containsCondition, containsDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(hyperlinkAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(hyperlinkAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Целое число"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут integerAttr типа "Целое число"</li>
     * <li>Сгенерировать случайное значение value</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут integerAttr "равно" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%integerAttr%: <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут integerAttr "не равно (и не пусто)" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%integerAttr%: не <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут integerAttr "меньше" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%integerAttr% меньше <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут integerAttr "больше" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%integerAttr% больше <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут integerAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%integerAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForIntegerType()
    {
        // Подготовка
        Attribute integerAttr = DAOAttribute.createInteger(employeeCase);
        DSLAttribute.add(integerAttr);

        // Действия и проверки
        int value = RandomUtils.nextInt();
        FilterBlockOr equalsCondition = new FilterBlockOr(integerAttr, FilterCondition.EQUAL, false, value);
        String equalsDescription = getDescription(integerAttr, FilterCondition.EQUAL, value);
        assertDescription(equalsCondition, equalsDescription);

        FilterBlockOr notContainsCondition = new FilterBlockOr(integerAttr, FilterCondition.NOT_CONTAINS, false, value);
        String notContainsDescription = getDescription(integerAttr, FilterCondition.NOT_CONTAINS, value);
        assertDescription(notContainsCondition, notContainsDescription);

        FilterBlockOr lessCondition = new FilterBlockOr(integerAttr, FilterCondition.LESS, false, value);
        String lessDescription = getDescription(integerAttr, FilterCondition.LESS, value);
        assertDescription(lessCondition, lessDescription);

        FilterBlockOr greaterCondition = new FilterBlockOr(integerAttr, FilterCondition.GREATER, false, value);
        String greaterDescription = getDescription(integerAttr, FilterCondition.GREATER, value);
        assertDescription(greaterCondition, greaterDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(integerAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(integerAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Дата"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут dateAttr типа "Дата"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateAttr "сегодня"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateAttr%: <span>сегодня</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateAttr "с ... по"
     * с (текущей даты - день) по (текущую дату + день)</li>
     * "[%dateAttr%: с <span>%dd.MM.yyyy - день%</span> по <span>%dd.MM.yyyy + день%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateAttr "за последние "n" дней" 3 дня</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateAttr%: за последние <span>3</span> дня]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateAttr "в ближайшие "n" дней" 3 дня</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateAttr%: в ближайшие <span>3</span> дня]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForDateType()
    {
        // Подготовка
        Attribute dateAttr = DAOAttribute.createDate(employeeCase.getFqn());
        DSLAttribute.add(dateAttr);

        long currentTime = System.currentTimeMillis();
        long dayTime = Interval.DAY.getFactor();

        // Действия и проверки
        FilterBlockOr todayCondition = new FilterBlockOr(dateAttr, FilterCondition.TODAY, false);
        String todayDescription = getDescription(dateAttr, FilterCondition.TODAY);
        assertDescription(todayCondition, todayDescription);

        FilterBlockOr fromToCondition = new FilterBlockOr(dateAttr, FilterCondition.FROM_TO, true,
                currentTime - dayTime, currentTime + dayTime);
        String fromToDescription = getDescription(dateAttr, FilterCondition.FROM_TO, currentTime - dayTime,
                currentTime + dayTime);
        assertDescription(fromToCondition, fromToDescription);

        FilterBlockOr lastNCondition = new FilterBlockOr(dateAttr, FilterCondition.LAST_N, false, 3);
        String lastNDescription = getDescription(dateAttr, FilterCondition.LAST_N, 3);
        assertDescription(lastNCondition, lastNDescription);

        FilterBlockOr nextNCondition = new FilterBlockOr(dateAttr, FilterCondition.NEXT_N, false, 3);
        String nextNDescription = getDescription(dateAttr, FilterCondition.NEXT_N, 3);
        assertDescription(nextNCondition, nextNDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(dateAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(dateAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Дата/время"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут dateTimeAttr типа "Дата/время"</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateTimeAttr "с ... по"
     * с (текущей даты - 3 часа) по (текущую дату + 3 часа)</li>
     * <li>Проверить, что описание полученное в ВП содержит
     * "[%dateTimeAttr%: с <span>%dd.MM.yyyy HH:mm - 3 часа%</span> по <span>%dd.MM.yyyy HH:mm + 3 часа%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateTimeAttr "за последние "n" часов" 3 часа</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateTimeAttr%: за последние <span>3</span> часа]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateTimeAttr "в ближайшие "n" часов" 3 часа</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateTimeAttr%: в ближайшие <span>3</span> часа]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dateTimeAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dateTimeAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForDateTimeType()
    {
        // Подготовка
        Attribute dateTimeAttr = DAOAttribute.createDateTime(employeeCase.getFqn());
        DSLAttribute.add(dateTimeAttr);

        long currentTime = System.currentTimeMillis();
        long hourTime = Interval.HOUR.getFactor();

        // Действия и проверки
        FilterBlockOr fromToCondition = new FilterBlockOr(dateTimeAttr, FilterCondition.FROM_TO, true,
                currentTime - hourTime, currentTime + hourTime);
        String fromToDescription = getDescription(dateTimeAttr, FilterCondition.FROM_TO, currentTime - hourTime,
                currentTime + hourTime);
        assertDescription(fromToCondition, fromToDescription);

        FilterBlockOr lastNCondition = new FilterBlockOr(dateTimeAttr, FilterCondition.LAST_N_HOURS, false, 3);
        String lastNDescription = getDescription(dateTimeAttr, FilterCondition.LAST_N_HOURS, 3);
        assertDescription(lastNCondition, lastNDescription);

        FilterBlockOr nextNCondition = new FilterBlockOr(dateTimeAttr, FilterCondition.NEXT_N_HOURS, false, 3);
        String nextNDescription = getDescription(dateTimeAttr, FilterCondition.NEXT_N_HOURS, 3);
        assertDescription(nextNCondition, nextNDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(dateTimeAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(dateTimeAttr, FilterCondition.EMPTY);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с атрибутами типа "Временной интервал"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$140059122
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе employeeCase атрибут dtIntervalAttr типа "Временной интервал"</li>
     * <li>Сгенерировать случайный интервал interval из SECOND, MINUTE, HOUR, DAY, WEEK со случайным значением
     * длины</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dtIntervalAttr "равно" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dtIntervalAttr%: <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dtIntervalAttr "меньше" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dtIntervalAttr% меньше <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dtIntervalAttr "больше" value</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dtIntervalAttr% больше <span>%value%</span>]"</li>
     * <li>Настроить в контенте objectList фильтрацию: атрибут dtIntervalAttr "пусто"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[%dtIntervalAttr%: <span>не заполнен</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForDateTimeIntervalType()
    {
        // Подготовка
        Attribute dtIntervalAttr = DAOAttribute.createTimeInterval(employeeCase.getFqn());
        DSLAttribute.add(dtIntervalAttr);

        DateTimeInterval dtInterval = DateTimeInterval.getRandom();

        // Действия и проверки
        FilterBlockOr equalsCondition = new FilterBlockOr(dtIntervalAttr, FilterCondition.EQUAL, false, dtInterval);
        String equalsDescription = getDescription(dtIntervalAttr, FilterCondition.EQUAL, dtInterval);
        assertDescription(equalsCondition, equalsDescription);

        FilterBlockOr lessCondition = new FilterBlockOr(dtIntervalAttr, FilterCondition.LESS, false, dtInterval);
        String lessDescription = getDescription(dtIntervalAttr, FilterCondition.LESS, dtInterval);
        assertDescription(lessCondition, lessDescription);

        FilterBlockOr greaterCondition = new FilterBlockOr(dtIntervalAttr, FilterCondition.GREATER, false, dtInterval);
        String greaterDescription = getDescription(dtIntervalAttr, FilterCondition.GREATER, dtInterval);
        assertDescription(greaterCondition, greaterDescription);

        FilterBlockOr emptyCondition = new FilterBlockOr(dtIntervalAttr, FilterCondition.EMPTY, false);
        String emptyDescription = getDescription(dtIntervalAttr, FilterCondition.EMPTY, dtInterval);
        assertDescription(emptyCondition, emptyDescription);
    }

    /**
     * Тестирование работы метода jsApi.listdata.describeFiltration(descriptor) с цепочкой атрибутов для фильтрации
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235845619
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Получить системный атрибут parentOu "Отдел" для класса Сотрудник</li>
     * <li>Получить системный атрибут title "Название" для класса Отдел</li>
     * <br>
     * <b>{@link #assertDescription(ListFilter, String...) Действия и проверки}</b>
     * <li>Настроить в контенте objectList фильтрацию: атрибут Отдел / Название содержит "%text%"</li>
     * <li>Проверить, что описание полученное в ВП содержит "[Отдел / Название: <span>"%text%"</span>]"</li>
     * </ol>
     */
    @Test
    void testDescribeListDescriptorForChainAttr()
    {
        Attribute parentOu = SysAttribute.parentOu(DAOEmployeeCase.createClass());
        Attribute title = SysAttribute.title(DAOOuCase.createClass());

        // Действия и проверки
        List<Attribute> attributesChain = List.of(parentOu, title);
        String text = ModelUtils.createText(6);
        FilterBlockOr condition = new FilterBlockOr(attributesChain, FilterCondition.CONTAINS, false, text);
        String description = getDescription(attributesChain, FilterCondition.CONTAINS, text);
        assertDescription(condition, description);
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     * @see #assertDescription(ListFilter, String...)
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescription ожидаемое описание
     */
    private void assertDescription(FilterBlockOr condition, String expectedDescription)
    {
        assertDescription(new FilterBlockAnd(condition), expectedDescription);
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     * @see #assertDescription(ListFilter, String...)
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescriptions ожидаемые описания атрибутов
     */
    private void assertDescription(FilterBlockAnd condition, String... expectedDescriptions)
    {
        assertDescription(new ListFilter(condition), String.join(" или ", expectedDescriptions));
    }

    /**
     * Проверяет описание фильтрации, сформированное в ВП путём вызова метода jsApi.utils.describeListDescriptor, для
     * фильтрации полученной путём конвертации condition в JSON
     *
     * <ol>
     * <b>Действия</b>
     * <li>Заполнить условие фильтрации в контенте objectListContent</li>
     * <li>Выполнить скрипт, который извлекает списочный дескриптор из objectListContent
     * и конвертирует его в JSON descriptorAsJson:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     def descriptor = api.listdata.createListDescriptor('$employeeCase', '$objectListContent')
     *     api.listdata.listDescriptorAsJson(descriptor)
     *     -------------------------------------------------------------------------------
     *     где employeeCase - FQN типа employeeCase,
     *         objectListContent - код контента objectListContent
     * </pre>
     * <li>Определить js-код jsContent, который преобразует списочный дескриптор конвертированный в JSON и преобразует
     * его в HTML-описание с помощью jsApi.utils.describeListDescriptor:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.utils.describeListDescriptor($descriptorAsJson)
     *         .then(html => document.getElementById('test_div').innerText = html)
     *     -------------------------------------------------------------------------------
     *     где $descriptorAsJson - JSON-представление списочного дескриптора контента objectListContent
     * </pre>
     * </li>
     * <li>Создать встроенное приложения с jsContent в качестве js-кода</li>
     * <li>Создать контент content для встроенного приложения на карточке типа employeeCase</li>
     * <b>Проверка</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Проверить, что описание полученное в ВП содержит значения из expectedDescriptions</li>
     * </ol>
     *
     * @param condition условие фильтрации объектов
     * @param expectedDescriptions ожидаемые описания атрибутов
     */
    private void assertDescription(ListFilter condition, String... expectedDescriptions)
    {
        objectList.setDefaultListFilter(condition);
        DSLContent.edit(objectList);

        String scriptBody = String.format(LIST_DESCRIPTION_AS_JSON, employeeCase.getFqn(), objectList.getCode());
        String descriptorAsJson = ScriptRunner.executeScript(scriptBody);

        String jsContent = String.format(DESCRIBE_FILTRATION, descriptorAsJson, GUIEmbeddedApplication.TEST_DIV_ID);
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(employeeCase.getFqn(), application);
        DSLContent.add(content);

        GUILogon.login(employee, true);
        String actualDescription = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content);
        String expectedDescription = Arrays.stream(expectedDescriptions)
                .map(description -> "[" + description + "]")
                .collect(Collectors.joining(" и "));
        Assertions.assertEquals(expectedDescription, actualDescription);
    }
}
