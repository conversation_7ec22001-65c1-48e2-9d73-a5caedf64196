package ru.naumen.selenium.cases.operator.classes.quickaction;

import java.util.HashSet;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.InputComplex;
import ru.naumen.selenium.casesutil.admin.Constants.DropDownSettings.Forms;
import ru.naumen.selenium.casesutil.admin.DSLDropDownSettings;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionInvocationMethod;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionType;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionsMenuPosition;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.Tool;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.util.RandomStringUtils;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тестирование быстрых действий, вызываемых со списков
 * <AUTHOR>
 * @since Apr 06, 2018
 */
public class QuickActionsFromListTest extends AbstractTestCase
{
    private static final String FILTER_SCRIPT = "def ATTRS_FOR_UPDATE_ON_FORMS = ['parent']\n" +
                                                "if (subject == null)\n" +
                                                "{\n" +
                                                "   return ATTRS_FOR_UPDATE_ON_FORMS\n" +
                                                "}\n" +
                                                "def filial = subject?.%s ?: subject?.parent?.%s\n" +
                                                "return []";

    /**
     * Тестирование добавления связи в списке на форме редактирования, если атрибут связи выведен на форму
     * в контенте "Параметры объекта"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00102
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64848138
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут linkAttr типа "Набор ссылок на БО" (Класс объектов - userClass)</li>
     * <li>В классе userClass создать группу атрибутов linkAttrGroup и добавить в нее атрибут linkAttr</li>
     * <li>На форму добавления объекта класса userClass вывести контент listOnForm типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>На форму добавления объекта класса userClass вывести контент propertyList типа "Параметры объекта"
     * (Группа атрибутов - linkAttrGroup)</li>
     * <li>На карточку объекта класса userClass вывести контент listOnCard типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>Создать объекты userBo1 и userBo2 типа userCase</li>
     * <li>В панель действий контента listOnForm добавить кнопку "Добавить связь"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа userCase</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступны для выбора объекты userBo1 и userBo2</li>
     * <li>Ввести в поле поиска название объекта userBo1</li>
     * <li>Проверить, что в результатах поиска присутствует userBo1 и отсутствует userBo2</li>
     * <li>Выбрать объект userBo1</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствует только userBo1</li>
     * <li>Проверить, что в контенте propertyList linkAttr = userBo1</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступен для выбора объект userBo2, а userBo1 отсутствует</li>
     * <li>Выбрать объект userBo2</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствуют объекты userBo1 и userBo2</li>
     * <li>Проверить, что в контенте propertyList linkAttr = userBo1, userBo2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке listOnCard на карточке созданного объекты присутствуют объекты userBo1 и userBo2</li>
     * </ol>
     */
    @Test
    public void testAddRelationOnAttributeInPropertyList()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute linkAttr = DAOAttribute.createBoLinks(userClass, userClass);
        DSLMetainfo.add(userClass, userCase, linkAttr);
        GroupAttr linkAttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(linkAttrGroup, linkAttr);

        String listLink = userClass.getFqn() + '@' + linkAttr.getCode();
        ContentForm listOnForm = DAOContentAddForm.createRelatedObjectAdvList(userClass.getFqn(), listLink);
        ContentForm propertyList = DAOContentAddForm.createPropertyList(userClass.getFqn(), linkAttrGroup);
        ContentForm listOnCard = DAOContentCard.createRelatedObjectList(userClass.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, listLink, DAOGroupAttr.createSystem(userClass));
        DSLContent.add(listOnForm, propertyList, listOnCard);

        Bo mainBo = DAOUserBo.create(userCase);
        Bo userBo1 = DAOUserBo.create(userCase);
        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo1, userBo2);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(listOnForm);
        GUIAdvListEditableToolPanel editableToolPanel = listOnForm.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIAdvListXpath.BTN_LINK);
        editableToolPanel.clickAddContextMenuOption();
        GUIForm.applyModalForm();
        GUILogon.logout();

        //для стабилизации теста
        DSLSearch.updateIndex(userBo1.getUuid());

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIBo.fillMainFields(mainBo);
        listOnForm.advlist().toolPanel().clickAddLink();
        String selectXpath = String.format(GUIXpath.InputComplex.ANY_VALUE, linkAttr.getCode());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo2.getUuid());
        GUISelect.search(selectXpath, userBo1.getTitle());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementAbsent(selectXpath, userBo2.getUuid());
        GUIMultiSelect.select(selectXpath, userBo1.getUuid());
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1);
        GUIPropertyList.assertPropertyListAttributeValue(propertyList, true, linkAttr, userBo1.getTitle());

        listOnForm.advlist().toolPanel().clickAddLink();
        GUIMultiSelect.assertElementAbsent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo2.getUuid());
        GUIMultiSelect.select(selectXpath, userBo2.getUuid());
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1, userBo2);
        GUIPropertyList.assertPropertyListAttributeValue(propertyList, true, linkAttr, userBo1.getTitle());
        GUIPropertyList.assertPropertyListAttributeValue(propertyList, true, linkAttr, userBo2.getTitle());

        GUIForm.applyForm();
        GUIBo.setUuidByUrl(mainBo);
        listOnCard.advlist().content().asserts().rowsPresence(userBo1, userBo2);
    }

    /**
     * Тестирование добавления связи в списке на форме редактирования, если атрибут связи не выведен на форму
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00102
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382233
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут linkAttr типа "Набор ссылок на БО" (Класс объектов - userClass)</li>
     * <li>На форму добавления объекта класса userClass вывести контент listOnForm типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>На карточку объекта класса userClass вывести контент listOnCard типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>Создать объекты userBo1 и userBo2 типа userCase</li>
     * <li>В панель действий контента listOnForm добавить кнопку "Добавить связь"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа userCase</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступны для выбора объекты userBo1 и userBo2</li>
     * <li>Ввести в поле поиска название объекта userBo1</li>
     * <li>Проверить, что в результатах поиска присутствует userBo1 и отсутствует userBo2</li>
     * <li>Выбрать объект userBo1</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствует только userBo1</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступен для выбора объект userBo2, а userBo1 отсутствует</li>
     * <li>Выбрать объект userBo2</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствуют объекты userBo1 и userBo2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке listOnCard на карточке созданного объекты присутствуют объекты userBo1 и userBo2</li>
     * </ol>
     */
    @Test
    public void testAddRelationOnNotShownAttribute()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute linkAttr = DAOAttribute.createBoLinks(userClass, userClass);
        DSLMetainfo.add(userClass, userCase, linkAttr);

        String listLink = userClass.getFqn() + '@' + linkAttr.getCode();
        ContentForm listOnForm = DAOContentAddForm.createRelatedObjectAdvList(userClass.getFqn(), listLink);
        ContentForm listOnCard = DAOContentCard.createRelatedObjectList(userClass.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, listLink, DAOGroupAttr.createSystem(userClass));
        DSLContent.add(listOnForm, listOnCard);

        Bo mainBo = DAOUserBo.create(userCase);
        Bo userBo1 = DAOUserBo.create(userCase);
        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo1, userBo2);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(listOnForm);
        GUIAdvListEditableToolPanel editableToolPanel = listOnForm.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIAdvListXpath.BTN_LINK);
        editableToolPanel.clickAddContextMenuOption();
        GUIForm.applyModalForm();
        GUILogon.logout();

        //для стабилизации теста
        DSLSearch.updateIndex(userBo1.getUuid());

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIBo.fillMainFields(mainBo);
        listOnForm.advlist().toolPanel().clickAddLink();
        String selectXpath = String.format(GUIXpath.InputComplex.ANY_VALUE, linkAttr.getCode());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo2.getUuid());
        GUISelect.search(selectXpath, userBo1.getTitle());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementAbsent(selectXpath, userBo2.getUuid());
        GUIMultiSelect.select(selectXpath, userBo1.getUuid());
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1);

        listOnForm.advlist().toolPanel().clickAddLink();
        GUIMultiSelect.assertElementAbsent(selectXpath, userBo1.getUuid());
        GUIMultiSelect.assertElementPresent(selectXpath, userBo2.getUuid());
        GUIMultiSelect.select(selectXpath, userBo2.getUuid());
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        GUIForm.applyForm();
        GUIBo.setUuidByUrl(mainBo);
        listOnCard.advlist().content().asserts().rowsPresence(userBo1, userBo2);
    }

    /**
     * Тестирование добавления связи в списке на форме редактирования, если атрибут связи имеет представление для
     * редактирования "Поле быстрого выбора" и не выведен на форму
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00102
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382233
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс ownerClass и унаследованный от него тип onwerCase</li>
     * <li>Создать пользовательский класс userClass, вложенный в ownerClass, и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут linkAttr типа "Набор ссылок на БО" (Класс объектов - userClass,
     * Представление для редактирования - Поле быстрого выбора)</li>
     * <li>На форму добавления объекта класса userClass вывести контент listOnForm типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>На карточку объекта класса userClass вывести контент listOnCard типа "Список связанных объектов"
     * (Атрибут связи - linkAttr, Представление - Сложный список, Группа атрибутов - Системные атрибуты)</li>
     * <li>Создать объект parentBo типа ownerCase</li>
     * <li>Создать объекты userBo1 и userBo2 типа userCase, вложенные в parentBo</li>
     * <li>В панель действий контента listOnForm добавить кнопку "Добавить связь"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа userCase, вложенного в parentBo</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступны для выбора объекты userBo1 и userBo2</li>
     * <li>Ввести в поле поиска название объекта userBo1</li>
     * <li>Проверить, что в результатах поиска присутствует userBo1 и отсутствует userBo2</li>
     * <li>Выбрать объект userBo1</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствует только userBo1</li>
     * <li>В панели действий списка listOnForm нажать на кнопку "Добавить связь"</li>
     * <li>Проверить, что на форме добавления связи доступен для выбора объект userBo2, а userBo1 отсутствует</li>
     * <li>Выбрать объект userBo2</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления связи</li>
     * <li>Проверить, что в списке listOnForm присутствуют объекты userBo1 и userBo2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке listOnCard на карточке созданного объекты присутствуют объекты userBo1 и userBo2</li>
     * </ol>
     */
    @Test
    public void testAddRelationOnNotShownAttributeWithFastSelection()
    {
        // Подготовка
        MetaClass ownerClass = DAOUserClass.create();
        MetaClass ownerCase = DAOUserCase.create(ownerClass);
        MetaClass userClass = DAOUserClass.create(ownerClass.getFqn());
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute linkAttr = DAOAttribute.createBoLinks(userClass, userClass);
        linkAttr.setEditPresentation(AttributeConstant.BOLinksType.FAST_SELECTION_FIELD);
        DSLMetainfo.add(ownerClass, ownerCase, userClass, userCase, linkAttr);

        String listLink = userClass.getFqn() + '@' + linkAttr.getCode();
        ContentForm listOnForm = DAOContentAddForm.createRelatedObjectAdvList(userClass.getFqn(), listLink);
        ContentForm listOnCard = DAOContentCard.createRelatedObjectList(userClass.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, listLink, DAOGroupAttr.createSystem(userClass));
        DSLContent.add(listOnForm, listOnCard);

        Bo parentBo = DAOUserBo.create(ownerCase);
        DSLBo.add(parentBo);
        Bo mainBo = DAOUserBo.createWithParent(userCase, parentBo);
        Bo userBo1 = DAOUserBo.createWithParent(userCase, parentBo);
        Bo userBo2 = DAOUserBo.createWithParent(userCase, parentBo);
        DSLBo.add(userBo1, userBo2);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(listOnForm);
        GUIAdvListEditableToolPanel editableToolPanel = listOnForm.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIAdvListXpath.BTN_LINK);
        editableToolPanel.clickAddContextMenuOption();
        GUIForm.applyModalForm();
        GUILogon.logout();
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), parentBo.getUuid(), userCase.getFqn());
        GUIBo.fillMainFields(mainBo);
        listOnForm.advlist().toolPanel().clickAddLink();
        BoTree fastSelectionTree = new BoTree(GUIXpath.Any.ANY_VALUE, false, linkAttr.getCode());

        fastSelectionTree.assertPresentElement(true, parentBo, userBo1);
        fastSelectionTree.assertPresentElement(true, parentBo, userBo2);
        fastSelectionTree.search(userBo1.getTitle());
        fastSelectionTree.assertPresentElementWithoutOpen(true, parentBo, userBo1);
        fastSelectionTree.assertPresentElementWithoutOpen(false, parentBo, userBo2);

        fastSelectionTree.setElementInMultiSelectTree(parentBo, userBo1);
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1);

        listOnForm.advlist().toolPanel().clickAddLink();
        fastSelectionTree.assertPresentElement(false, parentBo, userBo1);
        fastSelectionTree.assertPresentElement(true, parentBo, userBo2);
        fastSelectionTree.setElementInMultiSelectTree(parentBo, userBo2);
        GUIForm.clickApplyTitledDialog(listOnForm.getTitleRelatedAddForm());
        GUIForm.assertDialogDisappear("Форма добавления связи не закрылась.");
        listOnForm.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        GUIForm.applyForm();
        GUIBo.setUuidByUrl(mainBo);
        listOnCard.advlist().content().asserts().rowsPresence(userBo1, userBo2);
    }

    /**
     * Тестирование добавления объекта с формы быстрого добавления, открытой с формы сбтрого добавления,
     * открытой по нажатию на кнопку "Добавить" в списке объектов на карточке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382233
     * <br>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс ownerClass и унаследованный от него тип ownerCase</li>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе ownerClass добавить форму быстрого добавления и редактирования ownerQuickForm
     * (Для типов - ownerCase, Группа атрибутов - Системные атрибуты)</li>
     * <li>В классе userClass добавить форму быстрого добавления и редактирования userQuickForm
     * (Для типов - userCase, Группа атрибутов - Системные атрибуты)</li>
     * <li>В классе ownerClass создать атрибут linkAttr типа "Ссылка на бизнес-объект" (Класс объектов - userClass,
     * Форма быстрого добавления - userQuickForm)</li>
     * <li>Добавить атрибут linkAttr в системную группу атрибутов</li>
     * <li>На карточку объекта типа ownerClass вывести контент objectList типа "Список объектов"
     * (Класс объектов - ownerClass, Представление - Сложный список)</li>
     * <li>В панели действий контента objectList настроить кнопку "Добавить":
     * Использовать форму быстрого добавления - да, Форма быстрого добавления - ownerQuickForm</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта ownerBo</li>
     * <li>В панели действий списка objectList нажать на кнопку "Добавить"</li>
     * <li>Заполнить обязательные поля на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Добавить" над полем значения атрибута linkAttr</li>
     * <li>Заполнить обязательные поля на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить" на корневой форме быстрого добавления</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что выполнен переход на карточку созданного объекта</li>
     * <li>Проверить, что в качестве значения атрибута linkAttr - другой созданный объект</li>
     */
    @Test
    public void testCascadeAddObjectFromListOnObjectCard()
    {
        // Подготовка
        MetaClass ownerClass = DAOUserClass.create();
        MetaClass ownerCase = DAOUserCase.create(ownerClass);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(ownerClass, ownerCase, userClass, userCase);
        GroupAttr ownerAttrGroup = DAOGroupAttr.createSystem(ownerClass);
        CustomForm ownerQuickForm = DAOCustomForm.createQuickActionForm(ownerAttrGroup, ownerCase);
        CustomForm userQuickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(ownerQuickForm, userQuickForm);
        Attribute linkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        linkAttr.setQuickAddForm(userQuickForm.getUuid());
        DSLAttribute.add(linkAttr);
        DSLGroupAttr.edit(ownerAttrGroup, new Attribute[] { linkAttr }, new Attribute[0]);
        Attribute userTitleAttr = SysAttribute.title(userClass);
        userTitleAttr.setDefaultValue(ModelUtils.createTitle());
        DSLAttribute.edit(userTitleAttr);

        ContentForm objectList = DAOContentCard.createObjectAdvList(ownerClass.getFqn(), ownerClass);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(
                DAOTool.createSystemAddButtonWithQuickAddForm(ownerQuickForm)
        ));
        ContentForm propertyList = DAOContentCard.createPropertyList(ownerClass, ownerAttrGroup);
        DSLContent.add(objectList, propertyList);
        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);

        Bo createdOwner = DAOUserBo.create(ownerCase);
        Bo createdBo = DAOUserBo.create(userCase);
        createdBo.setTitle(userTitleAttr.getDefaultValue());

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ownerBo);
        objectList.advlist().toolPanel().clickAdd();
        GUIBo.fillUserMainFields(createdOwner);
        GUIForm.clickQuickAddForm(linkAttr);
        GUIForm.clickApplyTopmostDialog();
        GUIForm.applyModalForm();
        GUIBo.setUuidByUrl(createdOwner);
        createdBo.setUuid(DSLBo.getCreatedObjectUuid(userCase.getFqn(), new HashSet<>()));
        createdBo.setExists(null != createdBo.getUuid());
        linkAttr.setValue(userTitleAttr.getDefaultValue());
        // Проверки
        GUIPropertyList.assertPropertyListAttribute(propertyList, linkAttr);
        GUIPropertyList.clickAttributeValueLink(propertyList, linkAttr);
        GUIBo.assertThatBoCard(createdBo);
    }

    /**
     * Тестирование повторного открытия формы быстрого редактирования из списка объектов, расположенного на форме
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00718
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm (Для типов - userCase,
     * Группа атрибутов - attrGroup)</li>
     * <li>На форму редактирования отделов типа ouCase вывести контент objectList типа "Список объектов"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup, Представление - Сложный список)</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В строку списка objectList вывести действие редактирования объекта
     * (Форма быстрого редактирования - quickForm)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования отдела ou</li>
     * <li>В списке objectList нажать на иконку редактирования в строке объекта userBo</li>
     * <li>На форме быстрого редактирования изменить значение атрибута stringAttr</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого редактирования</li>
     * <li>В списке objectList нажать на иконку редактирования в строке объекта userBo</li>
     * <br>
     * <b>Проверка</b>
     * <li>В поле ввода атрибута stringAttr находится измененное значение</li>
     * </ol>
     */
    @Test
    public void testDoubleQuickEditFromObjectListOnForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(userClass, userCase, ouCase, stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass.getFqn());
        DSLGroupAttr.add(attrGroup, stringAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);

        ContentForm objectList = DAOContentEditForm.createObjectAdvList(ouCase.getFqn(), attrGroup, userClass);
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(userBo, ou);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(ouCase, MetaclassCardTab.EDITFORM);
        GUIContent.clickEditToolPanel(objectList);
        objectList.advlist().objectActions().setUseSystemSettings(false);
        GUIAdvlistObjectActions.clickAddTool();
        objectList.advlist().editableToolPanel().setTitleOnForm(ModelUtils.createTitle());
        objectList.advlist().objectActions().selectInvocationMethod(ActionInvocationMethod.ICON_CLICK);
        objectList.advlist().objectActions().selectActionByCode("edit");
        objectList.advlist().editableToolPanel().selectQuickEditForm(quickForm);
        objectList.advlist().objectActions().applyAddToolForm();
        objectList.advlist().objectActions().selectMenuPosition(ActionsMenuPosition.LEFT);
        GUIForm.applyModalForm();
        GUILogon.logout();
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToEditForm(ou);
        objectList.advlist().content().clickObjectActionIcon(userBo, "userEvent", "edit");
        String newValue = ModelUtils.createTitle();
        GUIForm.fillAttribute(stringAttr, newValue);
        GUIForm.applyModalForm();
        objectList.advlist().content().clickObjectActionIcon(userBo, "userEvent", "edit");
        // Проверка
        GUIForm.assertAttribute(stringAttr, newValue);
    }

    /**
     * Тестирование автоматического заполнения родительского объекта на форме быстрого добавления, вызванной со списка
     * вложенных объектов, если для формы включен параметр «Добавлять и редактировать объекты при сохранении быстрой
     * формы»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$122626426
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass (объекты вложены в объект своего класса) и унаследованный от него
     * тип userCase</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и «Родитель»</li>
     * <li>Создать форму быстрого добавления и редактирования quickAddForm для типа userCase (Группа атрибутов —
     * attrGroup, Добавлять и редактировать объекты при сохранении быстрой формы — да)</li>
     * <li>На форму добавления объекта класса userClass вывести контент childObjectList типа «Список связанных
     * объектов» (Класс объектов — userClass, Группа атрибутов — attrGroup, Представление — Сложный список)</li>
     * <li>На панель действий списка childObjectList вывести кнопку «Добавить», вызывающую форму quickAddForm</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа userCase</li>
     * <li>На панели действий списка childObjectList нажать на кнопку «Добавить»</li>
     * <br>
     * <b>Проверка</b>
     * <li>Атрибут «Родитель» на форме быстрого добавления не заполнен</li>
     * </ol>
     */
    @Test
    public void testFillParentOnImmediateQuickAddFormChildObjectList()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);
        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute parentAttr = SysAttribute.parent(userClass);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, titleAttr, parentAttr);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        quickAddForm.setImmediateObjectSavingEnabled(true);
        DSLCustomForm.add(quickAddForm);
        ContentForm childObjectList = DAOContentAddForm.createChildObjectList(userClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup);
        DSLContent.add(childObjectList);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(childObjectList);
        GUIAdvListEditableToolPanel editableToolPanel = childObjectList.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.assertsEditMode().availableActionsInToolBar(2, GUIButtonBar.BTN_ADD,
                GUIButtonBar.BTN_RESET_GLOBAL_DEFAULT_SETTINGS);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        childObjectList.advlist().toolPanel().clickAdd();
        // Проверка
        GUISelect.assertValue(InputComplex.ANY_VALUE, null, parentAttr.getCode());
    }

    /**
     * Тестирование автоматического заполнения родительского объекта на форме быстрого добавления, вызванной со списка
     * вложенных объектов, если для формы выключен параметр «Добавлять и редактировать объекты при сохранении быстрой
     * формы»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$122626426
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass (объекты вложены в объект своего класса) и унаследованный от него
     * тип userCase</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и «Родитель»</li>
     * <li>Создать форму быстрого добавления и редактирования quickAddForm для типа userCase (Группа атрибутов —
     * attrGroup, Добавлять и редактировать объекты при сохранении быстрой формы — нет)</li>
     * <li>На форму добавления объекта класса userClass вывести контент childObjectList типа «Список связанных
     * объектов» (Класс объектов — userClass, Группа атрибутов — attrGroup, Представление — Сложный список)</li>
     * <li>На панель действий списка childObjectList вывести кнопку «Добавить», вызывающую форму quickAddForm</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа userCase</li>
     * <li>На панели действий списка childObjectList нажать на кнопку «Добавить»</li>
     * <br>
     * <b>Проверки</b>
     * <li>Атрибут «Родитель» на форме быстрого добавления заполнен значением «[новый объект]»</li>
     * <li>В дереве выбора атрибута «Родитель» доступно значение «[новый объект]»</li>
     * </ol>
     */
    @Test
    public void testFillParentOnQuickAddFormChildObjectList()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);
        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute parentAttr = SysAttribute.parent(userClass);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, titleAttr, parentAttr);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickAddForm);
        ContentForm childObjectList = DAOContentAddForm.createChildObjectList(userClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup);
        DSLContent.add(childObjectList);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(childObjectList);
        GUIAdvListEditableToolPanel editableToolPanel = childObjectList.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.assertsEditMode().availableActionsInToolBar(2, GUIButtonBar.BTN_ADD,
                GUIButtonBar.BTN_RESET_GLOBAL_DEFAULT_SETTINGS);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        childObjectList.advlist().toolPanel().clickAdd();
        // Проверки
        ModelUuid newObject = new ModelUuid();
        newObject.setTitle("[новый объект]");
        GUISelect.assertValue(InputComplex.ANY_VALUE, newObject, parentAttr.getCode());
        BoTree tree = new BoTree(Any.ANY_VALUE, false, parentAttr.getCode());
        tree.assertElementsPresentByTitle(new ModelUuid[0], newObject);
    }

    /**
     * Тестирование редактирования атрибутов в списках на формах редактирования объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00604
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382233
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>В классе userClass создать строковый атрибут stringAttr (Редактируемый в списках - да)</li>
     * <li>В классе userClass создать атрибут integerAttr типа "Целое число" (Редактируемый в списках - да)</li>
     * <li>В классе userClass создать атрибут doubleAttr типа "Вещественное число" (Редактируемый в списках - да)</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты "Название",
     * stringAttr, integerAttr, doubleAttr</li>
     * <li>На форму редактирования объекта типа ouCase добавить контент objectList типа "Список объектов"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup, Представление - Сложный список)</li>
     * <li>На карточку объекта класса userClass вывести контент propertyList типа "Параметры объекта"
     * (Группа атрибутов - attrGroup)</li>
     * <li>Создать объект bo типа userCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования отдела ou</li>
     * <li>Проверить, что в списке objectList присутствует объект bo</li>
     * <li>Активировать ячейку атрибута stringAttr в строке объекта bo и изменить значение в поле и нажать
     * CTRL+Enter</li>
     * <li>Активировать ячейку атрибута integerAttr в строке объекта bo и изменить значение в поле
     * на произвольную строку, не являющуюся числом и нажать CTRL+Enter</li>
     * <li>Проверить, что появилось сообщение об ошибке "Поле должно содержать целое число"</li>
     * <li>Ввести в поле корректное числовое значение и нажать CTRL+Enter</li>
     * <li>Активировать ячейку атрибута doubleAttr в строке объекта bo и изменить значение в поле
     * на произвольную строку, не являющуюся числом и нажать иконку "Применить"</li>
     * <li>Проверить, что появилось сообщение об ошибке "Поле должно содержать вещественное число"</li>
     * <li>Ввести в поле корректное числовое значение и применить изменения</li>
     * <li>Проверить, что в списке отображаются новые значения атрибутов stringAttr, integerAttr, doubleAttr</li>
     * <li>Активировать ячейку атрибута stringAttr в строке объекта bo</li>
     * <li>Проверить, что для редактирования доступно актуальное значение атрибута stringAttr</li>
     * <li>Выйти из режима редактирования ячейки</li>
     * <li>Нажать на кнопку "Отмена" на форме редактирования</li>
     * <li>Проверить, что на экране появилось окно подтверждения действия с текстом:<br>
     * Вы действительно хотите закрыть форму?<br>
     * На форме были внесены следующие изменения: отредактированы объекты "%bo%".<br>
     * При закрытии формы данные изменения будут утеряны.</li>
     * <li>Нажать на кнопку "Нет"</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке объекта в контенте propertyList отображаются измененные значения
     * атрибутов stringAttr, integerAttr, doubleAttr</li>
     * </ol>
     */
    @Test
    public void testInlineEditOnForms()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();

        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        stringAttr.setEditableInLists(Boolean.TRUE.toString());
        Attribute integerAttr = DAOAttribute.createInteger(userClass.getFqn());
        integerAttr.setEditableInLists(Boolean.TRUE.toString());
        Attribute doubleAttr = DAOAttribute.createDouble(userClass);
        doubleAttr.setEditableInLists(Boolean.TRUE.toString());
        DSLMetainfo.add(userClass, userCase, ouCase, stringAttr, integerAttr, doubleAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, titleAttr, stringAttr, integerAttr, doubleAttr);

        ContentForm objectList = DAOContentEditForm.createObjectAdvList(ouCase.getFqn(), attrGroup, userClass);
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass, attrGroup);
        DSLContent.add(objectList, propertyList);

        Bo bo = DAOUserBo.create(userCase);
        Bo ou = DAOUserBo.create(ouCase);
        DSLBo.add(bo, ou);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ou);
        stringAttr.setValue(ModelUtils.createTitle());
        integerAttr.setValue(String.valueOf(RandomUtils.nextInt()));
        doubleAttr.setValue(RandomStringUtils.createDoubleValue());
        objectList.advlist().content().asserts().rowsPresence(bo);
        objectList.advlist().editCell().activate(bo, stringAttr);
        objectList.advlist().editCell().sendKeys(stringAttr.getValue());
        tester.actives().pressingCtrlEnterKey();
        objectList.advlist().editCell().activate(bo, integerAttr);
        objectList.advlist().editCell().sendKeys(ModelUtils.createTitle());
        tester.actives().pressingCtrlEnterKey();
        GUIValidation.assertValidation(integerAttr.getCode(), "Поле должно содержать целое число.");
        objectList.advlist().editCell().sendKeys(integerAttr.getValue());
        tester.actives().pressingCtrlEnterKey();
        objectList.advlist().editCell().activate(bo, doubleAttr);
        objectList.advlist().editCell().sendKeys(ModelUtils.createTitle());
        objectList.advlist().editCell().clickApply();
        GUIValidation.assertValidation(doubleAttr.getCode(), "Поле должно содержать вещественное число.");
        objectList.advlist().editCell().sendKeys(doubleAttr.getValue());
        objectList.advlist().editCell().clickApply();

        objectList.advlist().content().asserts().attrValue(bo, stringAttr);
        objectList.advlist().content().asserts().attrValue(bo, integerAttr);
        objectList.advlist().content().asserts().attrValue(bo, doubleAttr);

        objectList.advlist().editCell().activate(bo, stringAttr);
        objectList.advlist().editCell().asserts().cellValue(stringAttr.getValue());
        objectList.advlist().editCell().clickCancel();

        GUIForm.clickCancel();
        String questionFormat = "Вы действительно хотите закрыть форму?\n\n"
                                + "На форме были внесены следующие изменения: отредактированы объекты \"%s\".\n\n"
                                + "При закрытии формы данные изменения будут утеряны.";
        GUIForm.assertQuestion(questionFormat, bo.getTitle());
        GUIForm.clickNo();

        GUIForm.applyForm();
        GUIBo.goToCard(bo);
        GUIPropertyList.assertPropertyListAttribute(propertyList, stringAttr, integerAttr, doubleAttr);
    }

    /**
     * Тестирование формы быстрого добавления объекта для списка вложенных объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00747
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00461
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$79367799
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Создать пользовательский вложенный сам в себя класс userClass и типы userCase1, userCase2</li>
     *     <li>Добавить атрибут attr1 СБО (Отдел) в userClass</li>
     *     <li>Добавить атрибут attr2 СБО (Отдел) в userCase2 со скриптом фильтрации:
     *     def ATTRS_FOR_UPDATE_ON_FORMS = ['parent']
     *     if (subject == null)
     *     {
     *          return ATTRS_FOR_UPDATE_ON_FORMS
     *     }
     *     def filial = subject?.attr1 ?: subject?.parent?.attr1
     *     return []
     *     </li>
     *     <li>Добавить группу атрибутов groupAttr(attr1, attr2) для userCase2</li>
     *     <li>Для userCase2 добавить форму быстрого редактирования и добавления (groupAttr) quickForm</li>
     *     <li>На карточку типа userCase1 вывести Список вложенных объектов (case2List), класс: userClass, тип:
     *     userCase2, группа атрибутов: groupAttr, представление: Сложный список</li>
     *     <li>Указать у case2List для кнопки добавления - быструю форму quickForm</li>
     *     <li>Вывести на карточку Компании список вложенных объектов класса userClass</li>
     *     <b>Действия</b>
     *     <li>Авторизоваться с правами оператора</li>
     *     <li>На карточке компании нажать в списке rootCase2List кнопку добавить, заполнить тип: userCase1, название и
     *     нажать кнопку "Сохранить"</li>
     *     <li>На карточке нового объекта нажать кнопку добавить в списке case2List</li>
     *     <b>Проверки</b>
     *     <li>Проверить, что нет JS ошибки</li>
     * </ol>
     */
    @Test
    public void testQuickAddFromListInSelfObject()
    {
        //Подготовка
        String attr1Code = ModelUtils.createCode();
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(String.format(FILTER_SCRIPT, attr1Code, attr1Code));

        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase1, userCase2);

        Attribute attr1 = DAOAttribute.createObjectLink(userClass, DAOOuCase.createClass(), null);
        attr1.setCode(attr1Code);
        Attribute attr2 = DAOAttribute.createObjectLink(userCase2, DAOOuCase.createClass(), null);

        DSLScriptInfo.addScript(filterScript);
        attr2.setFilteredByScript(Boolean.TRUE.toString());
        attr2.setScriptForFiltration(filterScript.getCode());
        DSLAttribute.add(attr1, attr2);

        GroupAttr groupAttr = DAOGroupAttr.create(userCase2);
        DSLGroupAttr.add(groupAttr, attr1, attr2);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(groupAttr, userCase2);
        DSLCustomForm.add(quickForm);

        ContentForm case2List = DAOContentCard.createChildObjectList(userCase1, userClass, userCase2);
        case2List.setToolPanel(DAOToolPanel.createCustomToolPanel(
                DAOTool.createSystemAddButtonWithQuickAddForm(quickForm)
        ));
        ContentForm rootCase2List = DAOContentCard.createChildObjectList(DAORootClass.create(), userClass, userCase1,
                userCase2);
        DSLContent.add(case2List, rootCase2List);

        //Действия
        GUILogon.asTester();
        Bo bo = DAOUserBo.create(userCase1);
        GUIBo.goToCard(SharedFixture.root());
        GUIAdvListEditableToolPanel editableToolPanel = case2List.advlist().editableToolPanel();
        editableToolPanel.clickAddButton();
        GUIBo.fillUserMainFieldsMultipleCases(bo);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(bo);
        editableToolPanel.clickAddButton();
        //Проверки
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование добавления вложенного объекта со списка, расположенного на форме добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382233
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс ownerClass и унаследованный от него тип ownerCase</li>
     * <li>Создать пользовательский класс nestedClass, вложенный в ownerClass, и унаследованный
     * от него тип nestedCase</li>
     * <li>В классе ownerClass создать атрибут nestedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - nestedClass, Представление для редактирования - Список выбора)</li>
     * <li>В классе nestedClass создать строковый атрибут stringAttr</li>
     * <li>В классе ownerClass создать группу атрибутов ownerGroup и добавить в нее атрибут nestedLinkAttr</li>
     * <li>В классе nestedClass создать группу атрибутов nestedGroup и добавить в нее атрибуты
     * "Название" и stringAttr</li>
     * <li>В классе nestedClass создать форму быстрого добавления и редактирования quickAddForm
     * (Для типов - nestedCase, Группа атрибутов - nestedGroup)</li>
     * <li>На карточку объекта класса ownerClass добавить контент cardPropertyList типа "Параметры объекта"
     * (Группа атрибутов - ownerGroup)</li>
     * <li>На карточку объекта класса ownerClass добавить контент cardObjectList типа "Список вложенных объектов"
     * (Класс объектов - nestedClass, Группа атрибутов - nestedGroup, Представление - Сложный список)</li>
     * <li>На форму добавления объекта класса ownerClass добавить контент formPropertyList типа "Параметры на форме"
     * (Группа атрибутов - ownerGroup)</li>
     * <li>На форму добавления объекта класса ownerClass добавить контент formObjectList типа "Список вложенных
     * объектов"
     * (Класс объектов - nestedClass, Группа атрибутов - nestedGroup, Представление - Сложный список)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на вкладку "Форма добавления" карточки класса ownerClass</li>
     * <li>Открыть окно настройки панелей инструментов контента formObjectList</li>
     * <li>Снять флажок "Использовать системную логику формирования панели действий"</li>
     * <li>Проверить, что в панели доступных действий есть кнопка "Добавить"</li>
     * <li>Проверить, что кнопка "Добавить" выглядит неактивной</li>
     * <li>Добавить кнопку "Добавить" на панель действий</li>
     * <li>Открыть форму редактирования кнопки "Добавить"</li>
     * <li>Выбрать Форма быстрого добавления = quickAddForm</li>
     * <li>Нажать "Сохранить" на форме редактирования кнопки "Добавить"</li>
     * <li>Проверить, что кнопка "Добавить" теперь выглядит активной</li>
     * <li>Нажать "Сохранить" на форме настройки панелей инструментов</li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса ownerClass</li>
     * <li>Заполнить обязательные поля формы</li>
     * <li>Проверить, что в списке formObjectList нет элементов</li>
     * <li>Нажать на кнопку "Добавить" в панели действий списка</li>
     * <li>На форме быстрого добавления заполнить название объекта и атрибут stringAttr</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления объекта nestedBo</li>
     * <li>Проверить, что в списке formObjectList появился новый объект nestedBo с заполненными значениями
     * атрибутов</li>
     * <li>Проверить, что в списке выбора значения атрибута nestedAttr появился созданный объект nestedBo</li>
     * <li>Выбрать объект nestedBo в списке выбора значения атрибута nestedAttr</li>
     * <li>Нажать на кнопку "Сохранить" на форме добавления объекта ownerBo</li>
     * <li>Проверить, что значением атрибута nestedLinkAttr созданного объекта является объект nestedBo</li>
     * <li>Проверить, что в списке cardObjectList появился новый объект nestedBo с заполненными значениями
     * атрибутов</li>
     * </ol>
     */
    @Test
    public void testQuickAddNestedObjectFromListOnAddForm()
    {
        // Подготовка
        MetaClass ownerClass = DAOUserClass.create();
        MetaClass ownerCase = DAOUserCase.create(ownerClass);
        MetaClass nestedClass = DAOUserClass.create(ownerClass.getFqn());
        MetaClass nestedCase = DAOUserCase.create(nestedClass);

        Attribute nestedLinkAttr = DAOAttribute.createObjectLink(ownerClass, nestedClass, null);
        nestedLinkAttr.setEditPresentation(AttributeConstant.ObjectType.EDIT_LIST);
        Attribute stringAttr = DAOAttribute.createString(nestedClass);
        Attribute nestedTitleAttr = SysAttribute.title(nestedClass);

        DSLMetainfo.add(ownerClass, ownerCase, nestedClass, nestedCase, nestedLinkAttr, stringAttr);
        GroupAttr ownerGroup = DAOGroupAttr.create(ownerClass);
        DSLGroupAttr.add(ownerGroup, nestedLinkAttr);
        GroupAttr nestedGroup = DAOGroupAttr.create(nestedClass);
        DSLGroupAttr.add(nestedGroup, nestedTitleAttr, stringAttr);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(nestedGroup, nestedCase);
        DSLCustomForm.add(quickAddForm);
        ContentForm cardPropertyList = DAOContentCard.createPropertyList(ownerClass, ownerGroup);
        ContentForm cardObjectList = DAOContentCard.createChildObjectAdvlist(ownerClass.getFqn(), nestedClass,
                nestedGroup);
        ContentForm formPropertyList = DAOContentAddForm.createEditablePropertyList(ownerClass, ownerGroup);
        ContentForm formObjectList = DAOContentAddForm.createChildObjectAdvlist(ownerClass.getFqn(), nestedClass,
                nestedGroup);
        DSLContent.add(cardPropertyList, cardObjectList, formPropertyList, formObjectList);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(ownerClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(formObjectList);
        GUIAdvListEditableToolPanel editableToolPanel = formObjectList.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.assertsEditMode().availableActionsInToolBar(2, GUIButtonBar.BTN_ADD,
                GUIButtonBar.BTN_RESET_GLOBAL_DEFAULT_SETTINGS);
        editableToolPanel.assertsEditMode().disabledByTitle("Добавить");
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.assertsEditMode().disabledByTitle("Добавить");
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        editableToolPanel.assertsEditMode().enabledByTitle("Добавить");
        GUIForm.applyModalForm();
        GUILogon.logout();

        Bo ownerBo = DAOUserBo.create(ownerCase);
        Bo nestedBo = DAOUserBo.create(nestedCase);
        stringAttr.setValue(ModelUtils.createTitle());
        nestedBo.setUserAttribute(stringAttr);
        GUILogon.asTester();
        GUIBo.goToAddForm(ownerClass);
        GUIBo.fillMainFields(ownerBo);

        formObjectList.advlist().content().asserts().rowsNumberOnCurrentPage(0);
        formObjectList.advlist().toolPanel().clickAdd();

        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, nestedBo.getTitle(), "title");
        GUIForm.fillAttribute(stringAttr, stringAttr.getValue());
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        formObjectList.advlist().content().asserts().columnCells(nestedTitleAttr, false, nestedBo.getTitle());
        formObjectList.advlist().content().asserts().columnCells(stringAttr, false, stringAttr.getValue());
        GUISelect.assertDisplayedByTitle(String.format(GUIXpath.InputComplex.ANY_VALUE, nestedLinkAttr.getCode()),
                nestedBo.getTitle());
        GUISelect.selectByTitle(GUIXpath.InputComplex.ANY_VALUE, nestedBo.getTitle(), nestedLinkAttr.getCode());

        GUIForm.applyForm();
        GUIBo.setUuidByUrl(ownerBo);

        GUIPropertyList.assertPropertyListAttributeValue(cardPropertyList, true, nestedLinkAttr, nestedBo.getTitle());
        cardObjectList.advlist().content().asserts().columnCells(nestedTitleAttr, false, nestedBo.getTitle());
        cardObjectList.advlist().content().asserts().columnCells(stringAttr, false, stringAttr.getValue());
    }

    /**
     * Тестирование добавления вложенного объекта с помощью пользовательской кнопки, настроенной на форму быстрого
     * добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass (Объекты вложены в - объект своего класса) и унаследованный от него
     * тип userCase</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты "Родитель", "Название" и
     * stringAttr</li>
     * <li>В классе userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов - attrGroup, Для типов - userCase)</li>
     * <li>На карточку объекта userClass вывести контент objectList типа "Список вложенных объектов"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup, Представление - Сложный список)</li>
     * <li>Создать объект rootBo типа userCase</li>
     * <li>Для панели действий списка objectList установить значение параметра
     * Использовать системную логику формирования панели действий = нет</li>
     * <li>На панель действий списка objectList добавить пользовательскую кнопку button (Использовать форму быстрого
     * добавления = да, Форма быстрого добавления - quickForm)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта rootBo</li>
     * <li>На панели действий списка objectList нажать на кнопку button</li>
     * <li>Проверить, что на форме быстрого добавления атрибут "Родитель" предзаполнен значением rootBo</li>
     * <li>Заполнить остальные атрибуты на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке отображается созданный объект с заполненными значениями атрибутов</li>
     * </ol>
     */
    @Test
    public void testQuickAddNestedObjectFromUserEventButton()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(userClass, userCase, stringAttr);
        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute parentAttr = SysAttribute.parent(userClass);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, parentAttr, titleAttr, stringAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);

        ContentForm objectList = DAOContentCard.createChildObjectAdvlist(userClass.getFqn(), userClass, attrGroup);
        Tool quickAddFormButton = DAOTool.createUserQuickAddFormButton(quickForm);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(
                quickAddFormButton
        ));
        DSLContent.add(objectList);

        Bo rootBo = DAOUserBo.create(userCase);
        DSLBo.add(rootBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(rootBo);
        objectList.advlist().toolPanel().clickUserEventButton(quickAddFormButton.getTitle());
        GUIForm.assertDialogAppear("Форма быстрого добавления не появилась.");
        GUIAttribute.assertValue(parentAttr, rootBo.getTitle());
        Bo userBo = DAOUserBo.create(userCase);
        GUIBo.fillUserMainFields(userBo);
        stringAttr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(stringAttr, stringAttr.getValue());
        userBo.setUserAttribute(stringAttr);
        GUIForm.applyModalForm();
        objectList.advlist().content().asserts().columnCells(titleAttr, userBo.getTitle());
        objectList.advlist().content().asserts().columnCells(stringAttr, stringAttr.getValue());
        String uuid = DSLBo.getCreatedObjectUuid(userCase.getFqn(), Sets.newHashSet(rootBo.getUuid()));
        Assert.assertNotNull("Объект не был создан.", uuid);
        userBo.setUuid(uuid);
        userBo.setExists(true);
        objectList.advlist().content().asserts().rows(Lists.newArrayList(userBo), true, true);
    }

    /**
     * Тестирование быстрого добавления нескольких объектов из разных списков связанных объектов,
     * выведенных по разным обратным ссылкам и размещенных на форме добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64848138
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать пользовательский класс relatedClass и унаследованный от него тип relatedCase</li>
     * <li>В классе userClass создать атрибуты directLinkAttr1 и directLinkAttr2 типа "Набор ссылок на БО"
     * (Класс объектов - relatedClass)</li>
     * <li>В классе relatedClass создать атрибут backLinkAttr1 типа "Обратная ссылка"
     * (Прямая ссылка - directLinkAttr1)</li>
     * <li>В классе relatedClass создать атрибут backLinkAttr2 типа "Обратная ссылка"
     * (Прямая ссылка - directLinkAttr2)</li>
     * <li>Для типа userCase создать форму быстрого добавления quickAddForm
     * (Группа атрибутов - Системные атрибуты)</li>
     * <li>На форму добавления объекта класса relatedClass вывести контент relatedList1 типа "Список связанных объектов"
     * (Атрибут - backLinkAttr1, Представление - Простой список)</li>
     * <li>На форму добавления объекта класса relatedClass вывести контент relatedList2 типа "Список связанных объектов"
     * (Атрибут - backLinkAttr2, Представление - Простой список)</li>
     * <li>В панель действий контента relatedList1 добавить кнопку "Добавить" и настроить ее:
     * Использовать форму быстрого добавления - да, Форма быстрого добавления - quickAddForm</li>
     * <li>В панель действий контента relatedList2 добавить кнопку "Добавить" и настроить ее:
     * Использовать форму быстрого добавления - да, Форма быстрого добавления - quickAddForm</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа relatedCase</li>
     * <li>Заполнить название объекта</li>
     * <li>В списке relatedList1 нажать на кнопку "Добавить" на панели действий</li>
     * <li>Заполнить название объекта userBo1 на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке relatedList1 появился объект userBo1</li>
     * <li>Проверить, что в списке relatedList2 нет объектов</li>
     * <li>В списке relatedList1 нажать на кнопку "Добавить" на панели действий</li>
     * <li>Заполнить название объекта userBo2 на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке relatedList1 отображаются объекты userBo1 и userBo2</li>
     * <li>Проверить, что в списке relatedList2 нет объектов</li>
     * <li>В списке relatedList2 нажать на кнопку "Добавить" на панели действий</li>
     * <li>Заполнить название объекта userBo3 на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке relatedList1 отображаются объекты userBo1 и userBo2</li>
     * <li>Проверить, что в списке relatedList2 отображается только объект userBo3</li>
     * </ol>
     */
    @Test
    public void testQuickAddObjectsFromDifferentRelatedListsOnAddForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass relatedClass = DAOUserClass.create();
        MetaClass relatedCase = DAOUserCase.create(relatedClass);
        Attribute directLinkAttr1 = DAOAttribute.createBoLinks(userClass, relatedClass);
        Attribute directLinkAttr2 = DAOAttribute.createBoLinks(userClass, relatedClass);
        Attribute backLinkAttr1 = DAOAttribute.createBackBOLinks(relatedClass, directLinkAttr1);
        Attribute backLinkAttr2 = DAOAttribute.createBackBOLinks(relatedClass, directLinkAttr2);
        Attribute userClassTitleAttr = SysAttribute.title(userClass);
        DSLMetainfo.add(userClass, userCase, relatedClass, relatedCase, directLinkAttr1, directLinkAttr2, backLinkAttr1,
                backLinkAttr2);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(quickAddForm);
        ContentForm relatedList1 = DAOContentAddForm.createRelatedObjectList(relatedClass.getFqn(),
                String.format("%s@%s", relatedClass.getFqn(), backLinkAttr1.getCode()));
        ContentForm relatedList2 = DAOContentAddForm.createRelatedObjectList(relatedClass.getFqn(),
                String.format("%s@%s", relatedClass.getFqn(), backLinkAttr2.getCode()));
        DSLContent.add(relatedList1, relatedList2);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(relatedClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(relatedList1);
        GUIAdvListEditableToolPanel editableToolPanel = relatedList1.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        GUIContent.clickEditToolPanel(relatedList2);
        editableToolPanel = relatedList2.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        GUILogon.logout();
        // Выполнение действий и проверки
        GUILogon.asTester();
        Bo relatedBo = DAOUserBo.create(relatedCase);
        GUIBo.goToAddForm(relatedClass);
        GUIBo.fillMainFields(relatedBo);
        relatedList1.advlist().toolPanel().clickAdd();
        Bo userBo1 = DAOUserBo.create(userCase);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo1.getTitle(), "title");
        GUIForm.applyModalForm();
        relatedList1.advlist().content().asserts().columnCells(userClassTitleAttr, false, userBo1.getTitle());
        relatedList1.advlist().content().asserts().rowsNumberOnCurrentPage(1);
        relatedList2.advlist().content().asserts().rowsNumberOnCurrentPage(0);

        relatedList1.advlist().toolPanel().clickAdd();
        Bo userBo2 = DAOUserBo.create(userCase);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo2.getTitle(), "title");
        GUIForm.applyModalForm();
        relatedList1.advlist().content().asserts()
                .columnCells(userClassTitleAttr, false, userBo1.getTitle(), userBo2.getTitle());
        relatedList1.advlist().content().asserts().rowsNumberOnCurrentPage(2);
        relatedList2.advlist().content().asserts().rowsNumberOnCurrentPage(0);

        relatedList2.advlist().toolPanel().clickAdd();
        Bo userBo3 = DAOUserBo.create(userCase);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo3.getTitle(), "title");
        GUIForm.applyModalForm();
        relatedList1.advlist().content().asserts()
                .columnCells(userClassTitleAttr, false, userBo1.getTitle(), userBo2.getTitle());
        relatedList2.advlist().content().asserts().columnCells(userClassTitleAttr, false, userBo3.getTitle());
        relatedList1.advlist().content().asserts().rowsNumberOnCurrentPage(2);
        relatedList2.advlist().content().asserts().rowsNumberOnCurrentPage(1);
    }

    /**
     * Тестирование быстрого добавления нескольких объектов из списка связанных объектов,
     * выведенного по обратной ссылке и размещенного на форме добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64848138
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать пользовательский класс relatedClass и унаследованный от него тип relatedCase</li>
     * <li>В классе userClass создать атрибут directLinkAttr типа "Набор ссылок на БО"
     * (Класс объектов - relatedClass)</li>
     * <li>В классе relatedClass создать атрибут backLinkAttr типа "Обратная ссылка"
     * (Прямая ссылка - directLinkAttr)</li>
     * <li>Для типа userCase создать форму быстрого добавления quickAddForm
     * (Группа атрибутов - Системные атрибуты)</li>
     * <li>На форму добавления объекта класса relatedClass вывести контент relatedList типа "Список связанных объектов"
     * (Атрибут - backLinkAttr)</li>
     * <li>В панель действий контента relatedList добавить кнопку "Добавить" и настроить ее:
     * Использовать форму быстрого добавления - да, Форма быстрого добавления - quickAddForm</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа relatedCase</li>
     * <li>Заполнить название объекта</li>
     * <li>В списке relatedList нажать на кнопку "Добавить" на панели действий</li>
     * <li>Заполнить название объекта userBo1 на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке relatedList появился объект userBo1</li>
     * <li>В списке relatedList нажать на кнопку "Добавить" на панели действий</li>
     * <li>Заполнить название объекта userBo2 на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в списке relatedList отображаются объекты userBo1 и userBo2</li>
     * </ol>
     */
    @Test
    public void testQuickAddObjectsFromRelatedListOnAddForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass relatedClass = DAOUserClass.create();
        MetaClass relatedCase = DAOUserCase.create(relatedClass);
        Attribute directLinkAttr = DAOAttribute.createBoLinks(userClass, relatedClass);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(relatedClass, directLinkAttr);
        Attribute userClassTitleAttr = SysAttribute.title(userClass);
        DSLMetainfo.add(userClass, userCase, relatedClass, relatedCase, directLinkAttr, backLinkAttr);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(quickAddForm);
        ContentForm relatedList = DAOContentAddForm.createRelatedObjectAdvList(relatedClass.getFqn(),
                String.format("%s@%s", relatedClass.getFqn(), backLinkAttr.getCode()));
        DSLContent.add(relatedList);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(relatedClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(relatedList);
        GUIAdvListEditableToolPanel editableToolPanel = relatedList.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.selectQuickAddForm(quickAddForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        GUILogon.logout();
        // Выполнение действий и проверки
        GUILogon.asTester();
        Bo relatedBo = DAOUserBo.create(relatedCase);
        GUIBo.goToAddForm(relatedClass);
        GUIBo.fillMainFields(relatedBo);
        relatedList.advlist().toolPanel().clickAdd();
        Bo userBo1 = DAOUserBo.create(userCase);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo1.getTitle(), "title");
        GUIForm.applyModalForm();
        relatedList.advlist().content().asserts().columnCells(userClassTitleAttr, false, userBo1.getTitle());

        relatedList.advlist().toolPanel().clickAdd();
        Bo userBo2 = DAOUserBo.create(userCase);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo2.getTitle(), "title");
        GUIForm.applyModalForm();
        relatedList.advlist().content().asserts().columnCells(userClassTitleAttr, false, userBo1.getTitle(),
                userBo2.getTitle());
    }

    /**
     * Тестирование отображения атрибутов, объявленных и включенных в группу в типе, на форме быстрого редактирования,
     * вызванной со списка объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00747
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$62742223
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В типе userCase создать строковый атрибут stringAttr</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов типа userCase</li>
     * <li>Создать форму быстрого редактирования quickEditForm
     * (Для типов - userCase, Группа атрибутов - Системные атрибуты)</li>
     * <li>На карточку компании вывести контент objectList типа "Список объектов" (Класс объектов - userClass,
     * Группа атрибутов - Системные атрибуты, Представление - Сложный список)</li>
     * <li>Настроить кнопку "редактировать" панели массовых операций списка objectList:
     * Использовать форму быстрого редактирования - да, Форма быстрого редактирования - quickEditForm</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>В списке objectList выбрать объект userBo</li>
     * <li>В панели массовых операций нажать на кнопку "редактировать"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что на открывшейся форме быстрого редактирования присутствует атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testQuickEditFormWithAttributesDeclaredInCase()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLMetainfo.add(userClass, userCase, stringAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userCase), new Attribute[] { stringAttr }, new Attribute[0]);
        CustomForm quickEditForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userCase), userCase);
        DSLCustomForm.add(quickEditForm);
        ContentForm objectList = DAOContentCard.createObjectAdvList(SystemClass.ROOT.getCode(),
                DAOGroupAttr.createSystem(userClass), userClass);
        DSLContent.add(objectList);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(SystemClass.ROOT.getCode(), MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(objectList);
        GUIAdvListEditableToolPanel editableToolPanel = objectList.advlist().editableMassToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setUseQuickEditForm(true);
        editableToolPanel.selectQuickEditForm(quickEditForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        GUILogon.logout();

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().mass().selectElements(userBo);
        objectList.advlist().mass().clickOperation(MassOperation.EDIT);
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование быстрого редактирования объекта, если на форму выведена прямая ссылка, на которую настроена
     * обратная ссылка в родительском объекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$186238931
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать тип команды teamCase</li>
     * <li>В классе «Команда» создать атрибут linkAttr типа «Набор ссылок на БО» (Класс объектов — userClass)</li>
     * <li>В классе userClass создать атрибут backLinkAttr типа «Обратная ссылка» (Прямая ссылка — linkAttr)</li>
     * <li>В классе «Команда» создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и linkAttr</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типа teamCase по группе атрибутов
     * attrGroup</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку объекта класса userClass вывести список объектов objectList (Класс объектов — Команда,
     * Типы объектов — teamCase, Представление — Сложный список)</li>
     * <li>Настроить отображение действий с объектом в строке списка слева</li>
     * <li>Добавить действие редактирования в списке (Способ вызова — Нажатие на иконку/кнопку действия в строке
     * списка, Использовать форму быстрого редактирования — да, Форма быстрого редактирования — quickForm)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>В списке objectList кликнуть по иконке действия редактирования в строке команды team</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма открылась без ошибок</li>
     * <li>Атрибут linkAttr не заполнен</li>
     * </ol>
     */
    @Test
    public void testQuickEditWithBackLinkWithDirectLinkPermissions()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass teamClass = DAOTeamCase.createClass();
        MetaClass teamCase = DAOTeamCase.create();
        Attribute linkAttr = DAOAttribute.createBoLinks(teamClass, userClass);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userClass, linkAttr);
        DSLMetainfo.add(userClass, userCase, teamCase, linkAttr, backLinkAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(teamClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(teamClass), linkAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, teamCase);
        DSLCustomForm.add(quickForm);

        Bo team = DAOTeam.create(teamCase);
        Bo userBo = DAOTeam.create(userCase);
        DSLBo.add(team, userBo);

        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getFqn(), attrGroup, teamClass, teamCase);
        DSLContent.add(objectList);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(objectList);
        objectList.advlist().objectActions().setUseSystemSettings(false);
        GUIAdvlistObjectActions.clickAddTool();
        objectList.advlist().objectActions().setTitleOnForm(ModelUtils.createTitle());
        objectList.advlist().objectActions().selectActionByType(ActionType.EDIT);
        objectList.advlist().objectActions().selectInvocationMethod(ActionInvocationMethod.ICON_CLICK);
        objectList.advlist().editableToolPanel().setUseQuickEditForm(true);
        objectList.advlist().editableToolPanel().selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        tester.selectRadiobutton(GUIXpath.Input.LEFT_INPUT);
        GUIForm.applyForm();
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        objectList.advlist().content().clickObjectActionIcon(team, "userEvent", "edit");
        // Проверки
        GUIError.assertErrorAbsence();
        GUIMultiSelect.assertSelectedSize(String.format(InputComplex.ANY_VALUE, linkAttr.getCode()), 0);
    }

    /**
     * Тестирование быстрого редактирования объекта, если на форму выведена прямая ссылка, на которую у пользователя
     * нет прав на редактирование и настроена обратная ссылка в родительском объекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$186238931
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать тип команды teamCase</li>
     * <li>В классе «Команда» создать атрибут linkAttr типа «Набор ссылок на БО» (Класс объектов — userClass)</li>
     * <li>В классе userClass создать атрибут backLinkAttr типа «Обратная ссылка» (Прямая ссылка — linkAttr)</li>
     * <li>В классе «Команда» создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и linkAttr</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типа teamCase по группе атрибутов
     * attrGroup</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать сотрудника employee, обладающего профилем со всеми правами</li>
     * <li>В классе «Команда» создать маркер прав marker на редактирование атрибута linkAttr</li>
     * <li>Отобрать права на маркер marker у всех профилей, включая профиль со всеми правами для сотрудника
     * employee</li>
     * <li>На карточку объекта класса userClass вывести список объектов objectList (Класс объектов — Команда,
     * Типы объектов — teamCase, Представление — Сложный список)</li>
     * <li>Настроить отображение действий с объектом в строке списка слева</li>
     * <li>Добавить действие редактирования в списке (Способ вызова — Нажатие на иконку/кнопку действия в строке
     * списка, Использовать форму быстрого редактирования — да, Форма быстрого редактирования — quickForm)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>В списке objectList кликнуть по иконке действия редактирования в строке команды team</li>
     * <li>Нажать на кнопку «Сохранить» на открывшейся форме</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма закрылась без ошибок</li>
     * <li>Атрибут linkAttr объекта team не изменился</li>
     * </ol>
     */
    @Test
    public void testQuickEditWithBackLinkWithoutDirectLinkPermissions()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass teamClass = DAOTeamCase.createClass();
        MetaClass teamCase = DAOTeamCase.create();
        Attribute linkAttr = DAOAttribute.createBoLinks(teamClass, userClass);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userClass, linkAttr);
        DSLMetainfo.add(userClass, userCase, teamCase, linkAttr, backLinkAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(teamClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(teamClass), linkAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, teamCase);
        DSLCustomForm.add(quickForm);

        Bo team = DAOTeam.create(teamCase);
        Bo userBo = DAOTeam.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(team, userBo, employee);

        SecurityMarker marker = new SecurityMarkerEditAttrs(teamClass).addAttributes(linkAttr).apply();
        DSLSecurityProfile.removeRights(teamClass, SharedFixture.secProfileLic(), marker);

        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getFqn(), attrGroup, teamClass, teamCase);
        DSLContent.add(objectList);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(objectList);
        objectList.advlist().objectActions().setUseSystemSettings(false);
        GUIAdvlistObjectActions.clickAddTool();
        objectList.advlist().objectActions().setTitleOnForm(ModelUtils.createTitle());
        objectList.advlist().objectActions().selectActionByType(ActionType.EDIT);
        objectList.advlist().objectActions().selectInvocationMethod(ActionInvocationMethod.ICON_CLICK);
        objectList.advlist().editableToolPanel().setUseQuickEditForm(true);
        objectList.advlist().editableToolPanel().selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        tester.selectRadiobutton(GUIXpath.Input.LEFT_INPUT);
        GUIForm.applyForm();
        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        objectList.advlist().content().clickObjectActionIcon(team, "userEvent", "edit");
        GUIForm.applyForm();
        // Проверки
        GUIError.assertErrorAbsence();
        DSLBo.assertAttribute(team, linkAttr.getCode(), "[]"::equals);
    }

    /**
     * Тестирование быстрого редактирования объекта, если на форму не выведена прямая ссылка, на которую настроена
     * обратная ссылка в родительском объекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$186238931
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать тип команды teamCase</li>
     * <li>В классе «Команда» создать атрибут linkAttr типа «Набор ссылок на БО» (Класс объектов — userClass)</li>
     * <li>В классе userClass создать атрибут backLinkAttr типа «Обратная ссылка» (Прямая ссылка — linkAttr)</li>
     * <li>В классе «Команда» создать группу атрибутов attrGroup и добавить в нее атрибут «Название»</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типа teamCase по группе атрибутов
     * attrGroup</li>
     * <li>Создать команду team типа teamCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку объекта класса userClass вывести список объектов objectList (Класс объектов — Команда,
     * Типы объектов — teamCase, Представление — Сложный список)</li>
     * <li>Настроить отображение действий с объектом в строке списка слева</li>
     * <li>Добавить действие редактирования в списке (Способ вызова — Нажатие на иконку/кнопку действия в строке
     * списка, Использовать форму быстрого редактирования — да, Форма быстрого редактирования — quickForm)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>В списке objectList кликнуть по иконке действия редактирования в строке команды team</li>
     * <li>Нажать на кнопку «Сохранить» на открывшейся форме</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма закрылась без ошибок</li>
     * <li>Атрибут linkAttr объекта team не изменился</li>
     * </ol>
     */
    @Test
    public void testQuickEditWithBackLinkWithoutDirectLinkOnForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass teamClass = DAOTeamCase.createClass();
        MetaClass teamCase = DAOTeamCase.create();
        Attribute linkAttr = DAOAttribute.createBoLinks(teamClass, userClass);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userClass, linkAttr);
        DSLMetainfo.add(userClass, userCase, teamCase, linkAttr, backLinkAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(teamClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(teamClass));
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, teamCase);
        DSLCustomForm.add(quickForm);

        Bo userBo = DAOTeam.create(userCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team, userBo);

        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getFqn(), attrGroup, teamClass, teamCase);
        DSLContent.add(objectList);
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(objectList);
        objectList.advlist().objectActions().setUseSystemSettings(false);
        GUIAdvlistObjectActions.clickAddTool();
        objectList.advlist().objectActions().setTitleOnForm(ModelUtils.createTitle());
        objectList.advlist().objectActions().selectActionByType(ActionType.EDIT);
        objectList.advlist().objectActions().selectInvocationMethod(ActionInvocationMethod.ICON_CLICK);
        objectList.advlist().editableToolPanel().setUseQuickEditForm(true);
        objectList.advlist().editableToolPanel().selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        tester.selectRadiobutton(GUIXpath.Input.LEFT_INPUT);
        GUIForm.applyForm();
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        objectList.advlist().content().clickObjectActionIcon(team, "userEvent", "edit");
        GUIForm.applyForm();
        // Проверки
        GUIError.assertErrorAbsence();
        DSLBo.assertAttribute(team, linkAttr.getCode(), "[]"::equals);
    }

    /**
     * Тестирование подстановки единственного значения ссылочного атрибута на форме быстрого добавления, вызванной
     * по нажатию кнопки "Добавить" списка объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00747
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00688
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00674
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$77017614
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass1 и унаследованный от него тип userCase1</li>
     * <li>Создать пользовательский класс userClass2 и унаследованный от него тип userCase2</li>
     * <li>В классе userClass2 создать атрибут linkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass1)</li>
     * <li>В классе userClass2 создать группу атрибутов attrGroup и добавить в нее атрибут linkAttr</li>
     * <li>Создать форму быстрого добавления и редактирования quickAddForm (Группа атрибутов - attrGroup,
     * Для типов - userCase2)</li>
     * <li>На карточку компании вывести контент objectList типа "Список объектов" (Класс объектов - userClass2,
     * Представление - Сложный список)</li>
     * <li>Изменить параметры кнопки "Добавить" на панели действйи списка objectList: Использовать форму быстрого
     * добавления - да, Форма быстрого добавления - quickAddForm</li>
     * <li>Включить автоматическую постановку единственного значения на форме добавления для всех атрибутов</li>
     * <li>Создать объект singleBo типа userCase1</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>На панели действий списка objectList нажать на кнопку "Добавить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>На форме быстрого добавления значение атрибута linkAttr = singleBo</li>
     * </ol>
     */
    @Test
    public void testSingleValueSelectionOnQuickAddFormCalledFromList()
    {
        // Подготовка
        MetaClass userClass1 = DAOUserClass.create();
        MetaClass userClass2 = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass1);
        MetaClass userCase2 = DAOUserCase.create(userClass2);
        Attribute linkAttr = DAOAttribute.createObjectLink(userClass2.getFqn(), userClass1, null);
        DSLMetainfo.add(userClass1, userClass2, userCase1, userCase2, linkAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass2);
        DSLGroupAttr.add(attrGroup, linkAttr);

        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase2);
        DSLCustomForm.add(quickAddForm);
        ContentForm objectList = DAOContentCard.createObjectAdvList(SystemClass.ROOT.getCode(), userClass2);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(
                DAOTool.createSystemAddButtonWithQuickAddForm(quickAddForm)
        ));
        DSLContent.add(objectList);

        DSLDropDownSettings.setSubstituteAllAttrsOnForms(Forms.ADD_FORM);
        Bo singleBo = DAOUserBo.create(userCase1);
        DSLBo.add(singleBo);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().toolPanel().clickAdd();
        // Проверка
        GUIForm.assertAttribute(linkAttr, singleBo.getTitle());
    }
}
