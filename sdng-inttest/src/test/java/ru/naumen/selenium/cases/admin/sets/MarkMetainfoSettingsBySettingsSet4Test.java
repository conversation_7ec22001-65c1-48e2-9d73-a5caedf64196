package ru.naumen.selenium.cases.admin.sets;

import java.io.File;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.MetaInfoXml;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование разметки комплектами на стенде
 *
 * <AUTHOR>
 * @since 28.01.2025
 */
public class MarkMetainfoSettingsBySettingsSet4Test extends AbstractTestCase
{
    private static final String SET_METAINFO_TAG = "set";

    private static MetaClass employeeCase;
    private static SettingsSet productSet1;
    private static SettingsSet productSet2;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles=true)</li>
     * <li>Включить доступность комплектов на стенде (ru.naumen.settingSets.show=true)</li>
     * <li>Создать комплекты productSet1, productSet2</li>
     * <li>Создать тип класса Сотрудник employeeCase</li>
     * <li></li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        productSet1 = DAOSettingsSet.createSettingsSet();
        productSet2 = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(productSet1, productSet2);

        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);
    }

    /**
     * Тестирование ограничения настройки комплектами, если у суперпользователя есть доступный комплект
     * по профилям администрирования
     * Golden Case 5. Ограничение возможности разметки настроек комплектом на формах. <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$288673031
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профиль администрирования oneSetAdminProfile со всеми маркерами доступа</li>
     * <li>Связать комплект productSet1 с профилем администрирования oneSetAdminProfile</li>
     * <li>Создать суперпользователя oneSetSuperUser, профиль администрирования - oneSetAdminProfile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Авторизоваться под oneSetSuperUser</li>
     * <li>Перейти на карточку типа employeeCase</li>
     * <li>Проверить, что у типа employeeCase доступно открытие модальной формы редактирования</li>
     * <li>Открыть модальную форму редактирования типа employeeCase</li>
     * <li>Проверить, что на форме отображается поле одиночного выбора "Комплект"</li>
     * <li>Нажать на поле "Комплект"</li>
     * <li>Проверить, что в выпадающем списке содержится комплект с названием productSet1</li>
     * <li>Проверить, что в выпадающем списке НЕ содержится комплект с названием productSet2</li>
     * <li>Выбрать productSet1, сохранить форму</li>
     * <li>Проверить, что форма редактирования закрылась, ошибки не возникло</li>
     * <li>Проверить, что на карточке employeeCase свойство "Комплект" заполнено объектом-ссылкой "Комплект"
     * с названием productSet1</li>
     * <li>Выгрузить метаинформацию по типу employeeCase</li>
     * <li>Проверить, что в метаинформации типа тег set заполнен кодом productSet1</li>
     * <li>Открыть форму редактирования типа employeeCase</li>
     * <li>Проверить, что поле Комплект заполнено объектом с названием productSet1</li>
     * </ol>
     */
    @Test
    public void testRestrictionsOneSettingsSet()
    {
        // Подготовка
        AdminProfile oneSetAdminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(oneSetAdminProfile);
        DSLAdminProfile.addAllRightsToAdminProfile(oneSetAdminProfile);

        productSet1.setAdminProfiles(oneSetAdminProfile);
        DSLSettingsSet.edit(productSet1);

        SuperUser oneSetSuperUser = DAOSuperUser.create();
        oneSetSuperUser.setAdminProfiles(oneSetAdminProfile);
        DSLSuperUser.add(oneSetSuperUser);

        //Выполнение действий и проверки
        GUILogon.login(oneSetSuperUser);
        GUIMetaClass.goToCard(employeeCase);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUIMetaClass.openEditForm(employeeCase);
        GUISettingsSet.assertSelectable(productSet1);
        GUISettingsSet.assertNotSelectable(productSet2);
        GUISettingsSet.fillSettingsSetPropOnForm(productSet1.getCode());
        GUIForm.applyForm();

        GUISettingsSet.assertSettingsSetOnCards(productSet1.getTitle());

        MetainfoExportModel model = new MetainfoExportModel();
        List<String> path = MetainfoElementIdBuilder.listOfNodesToClassSettings(employeeCase.getFqn());
        model.getElements().add(StringUtils.join(path, "."));
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(model);

        MetaInfoXml metainfo = new MetaInfoXml(metainfoFile);
        metainfo.assertValue(productSet1.getCode(), SET_METAINFO_TAG);

        GUIMetaClass.openEditForm(employeeCase);
        GUISettingsSet.assertSettingsSetPropOnForm(productSet1);
    }

    /**
     * Тестирование ограничения настройки комплектами, если у суперпользователя нет комплектов
     * по профилям администрирования <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$288673031
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профиль администрирования noSetsAdminProfile со всеми маркерами доступа</li>
     * <li>Создать суперпользователя noSetsSuperUser, профиль администрирования - noSetsAdminProfile</li>
     * <li>Создать тип класса Сотрудник employeeCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Авторизоваться под noSetsSuperUser</li>
     * <li>Перейти на карточку типа employeeCase</li>
     * <li>Открыть модальную форму редактирования типа employeeCase</li>
     * <li>Проверить, что на форме отображается не отображается поле одиночного выбора "Комплект"</li>
     * <li>Сохранить форму</li>
     * <li>Проверить, что форма редактирования закрылась, ошибки не возникло</li>
     * </ol>
     */
    @Test
    public void testPropertyNotVisibleWithoutSettingsSets()
    {
        // Подготовка
        AdminProfile noSetAdminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(noSetAdminProfile);
        DSLAdminProfile.addAllRightsToAdminProfile(noSetAdminProfile);

        SuperUser noSetSuperUser = DAOSuperUser.create();
        noSetSuperUser.setAdminProfiles(noSetAdminProfile);
        DSLSuperUser.add(noSetSuperUser);

        //Выполнение действий и проверки
        GUILogon.login(noSetSuperUser);
        GUIMetaClass.goToCard(employeeCase);

        GUIMetaClass.openEditForm(employeeCase);
        GUISettingsSet.assertPropertyNotVisibleOnForm();
        GUIForm.applyForm();
    }

    /**
     * Тестирование ограничения настройки комплектами, если у суперпользователя профиль администрирования vendor <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$288673031
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Авторизоваться под naumen</li>
     * <li>Перейти на карточку типа employeeCase</li>
     * <li>Проверить, что у типа employeeCase доступно открытие модальной формы редактирования</li>
     * <li>Открыть модальную форму редактирования типа employeeCase</li>
     * <li>Проверить, что на форме отображается поле одиночного выбора "Комплект"</li>
     * <li>Нажать на поле "Комплект"</li>
     * <li>Проверить, что в выпадающем списке содержится комплект с названием productSet1</li>
     * <li>Проверить, что в выпадающем списке содержится комплект с названием productSet2</li>
     * <li>Выбрать productSet2, сохранить форму</li>
     * <li>Проверить, что форма редактирования закрылась, ошибки не возникло</li>
     * <li>Проверить, что на карточке employeeCase свойство "Комплект" заполнено объектом-ссылкой "Комплект"
     * с названием productSet2</li>
     * <li>Выгрузить метаинформацию по типу employeeCase</li>
     * <li>Проверить, что в метаинформации типа тег set заполнен кодом productSet2</li>
     * <li>Открыть форму редактирования типа employeeCase</li>
     * <li>Проверить, что поле Комплект заполнено объектом с названием productSet2</li>
     * </ol>
     */
    @Test
    public void testNoRestrictionsForVendor()
    {
        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(employeeCase);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUIMetaClass.openEditForm(employeeCase);
        GUISettingsSet.assertSelectable(productSet1);
        GUISettingsSet.assertSelectable(productSet2);
        GUISettingsSet.fillSettingsSetPropOnForm(productSet2.getCode());
        GUIForm.applyForm();

        GUISettingsSet.assertSettingsSetOnCards(productSet2.getTitle());

        MetainfoExportModel model = new MetainfoExportModel();
        List<String> path = MetainfoElementIdBuilder.listOfNodesToClassSettings(employeeCase.getFqn());
        model.getElements().add(StringUtils.join(path, "."));
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(model);

        MetaInfoXml metainfo = new MetaInfoXml(metainfoFile);
        metainfo.assertValue(productSet2.getCode(), SET_METAINFO_TAG);

        GUIMetaClass.openEditForm(employeeCase);
        GUISettingsSet.assertSettingsSetPropOnForm(productSet2);
    }
}