package ru.naumen.selenium.cases.operator.classes.quickaction;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistCopyFromTemplateForm.OTHERS_TEMPLATE;

import java.io.File;
import java.util.HashSet;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIChangeCaseForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionInvocationMethod;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionsMenuPosition;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.listtemplate.GUIListTemplate;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm.CommentOnFormProperty;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.Tool;
import ru.naumen.selenium.casesutil.model.listtemplate.DAOListTemplate;
import ru.naumen.selenium.casesutil.model.listtemplate.ListTemplate;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование быстрых действий на формах в интерфейсе оператора.
 * <AUTHOR>
 * @since Jan 22, 2018
 */
public class QuickActionsTest extends AbstractTestCase
{
    private static MetaClass userClass;
    private static MetaClass userCase1;
    private static MetaClass userCase2;
    private static MetaClass ownerClass;
    private static MetaClass ownerCase;
    private static Attribute stateAttr;
    private static CustomForm quickForm;
    private static ContentForm userPropertyList;
    private static ContentForm ownerPropertyList;

    private static final String FILTER_SCRIPT = "def ATTRS_FOR_UPDATE_ON_FORMS = ['metaClass']\n" +
                                                "if (subject == null)\n" +
                                                "{\n" +
                                                "   return ATTRS_FOR_UPDATE_ON_FORMS\n" +
                                                "}\n" +
                                                "return []";

    /**
     * Общая подготовка для тестов класса
     * <br>
     * <ol>
     * <li>Создать пользовательский класс userClass и два унаследованных типа userCase1 и userCase2</li>
     * <li>Создать пользовательский класс ownerClass и унаследованный от него тип ownerCase</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типов userCase1 и userCase2
     * (Группа атрибутов - Системные атрибуты)</li>
     * <li>На карточку объекта класса userClass вывести контент userPropertyList типа "Параметры объекта"
     * (Группа атрибутов - Системные атрибуты)</li>
     * <li>На карточку объекта класса ownerClass вывести контент ownerPropertyList типа "Параметры объекта"
     * (Группа атрибутов - Системные атрибуты)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase1 = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        ownerClass = DAOUserClass.createWithWF();
        ownerCase = DAOUserCase.create(ownerClass);
        DSLMetainfo.add(userClass, userCase1, userCase2, ownerClass, ownerCase);

        stateAttr = SysAttribute.state(ownerClass);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { stateAttr }, new Attribute[0]);

        quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase1, userCase2);
        DSLCustomForm.add(quickForm);

        userPropertyList = DAOContentCard.createPropertyList(userClass, DAOGroupAttr.createSystem(userClass));
        ownerPropertyList = DAOContentCard.createPropertyList(ownerClass, DAOGroupAttr.createSystem(ownerClass));
        DSLContent.add(userPropertyList, ownerPropertyList);
    }

    /**
     * Тестирование отсутствия в списке форм быстрого добавления форм класса Запрос.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В типе scCase добавить форму fastSC:
     * <br>Тип формы - форма быстрого добавления и редактирования
     * <br>Группа атрибутов - Системные атрибуты.</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку scCase.</li>
     * <li>В верхней панели действий кликнуть на кнопку "Перейти к настройке действий".</li>
     * <li>Снять галку в чекбоксе "Использовать системную логику формирования панели действий". Кликнуть на кнопку
     * "Новая кнопка" -> Добавить на панель действий.</li>
     * <li>В открывшемся модальном окне установить чекбокс "Использовать форму быстрого добавления" = true.</li>
     * <li>Проверить, что в поле Форма быстрого выбора присутствует форма quickForm и отсутствует форма fastSC.</li>
     * </ol>
     */
    @Test
    public void testAbsentQuickAddEditFormInQuickAddFormsList()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        CustomForm fastSc = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(), scCase);
        DSLCustomForm.add(fastSc);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(scCase, MetaclassCardTab.OBJECTCARD);

        GUIContent.enableEditProperties();

        ContentForm windowContent = DSLContent.getWindowContent(scCase);
        GUIAdvListEditableToolPanel editableToolPanel = windowContent.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanelOriginal();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.setUseQuickAddForm(true);
        editableToolPanel.assertPresentQuickAddFormByTitle(quickForm.getTitle());
        editableToolPanel.assertAbsentQuickAddFormByTitle(fastSc.getTitle());
    }

    /**
     * Тестирование отсутствия кнопки, на которую настроена форма быстрого добавления определенного типа объекта,
     * если у сотрудника отсутствует право на добавление объекта этого типа.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока</li>
     * <li>Добавить форму быстрого добавления и редактирования quickForm2:
     * <br>Группа атрибутов - Системные атрибуты.
     * <br>Типы объектов - userCase2</li>
     * <li>Добавить группу пользователей securityGroup</li>
     * <li>В классе userClass перейти на вкладку "Права доступа", добавить профиль profile:
     * <br>Роли пользователей: Сотрудник.
     * <br>Группа пользователей - securityGroup
     * <br>Выдать профилю все права</li>
     * <li>В типе userCase1 у профиля profile отнять право на Добавление объекта.</li>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса userClass. В верхней панели действий кликнуть на кнопку "Перейти к настройке
     * действий".
     * <br>Снять галку "Использовать системную логику панели действий" = false.</li>
     * <li>Кликнуть на кнопку "Новая кнопка" -> Добавить на панель действий:
     * <br>Название - action
     * <br>Использовать форму быстрого добавления = true
     * <br>Форма быстрого добавления - quickForm2.
     * <br>Сохранить.</li>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Добавить объект employee типа employeeCase. </li>
     * <li>Добавить объект employee в группу пользователей securityGroup</li>
     * <li>Добавить объект bo1 класса userClass (типа userCase1).</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под сотрудником employee.</li>
     * <li>Перейти на карточку объекта bo1.</li>
     * <li>Проверить, что в панели действий отсутствует кнопка action</li>
     * </ol>
     */
    @Test
    public void testAbsentQuickAddFormButtonWithRemovedRights()
    {
        //Подготовка
        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase1);
        DSLCustomForm.add(quickForm2);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        SecurityProfile securityProfile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(securityProfile);

        DSLSecurityProfile.grantAllPermissions(securityProfile);
        DSLSecurityProfile.removeRights(userCase1, securityProfile, AbstractBoRights.ADD);

        ContentForm windowContent = DSLContent.getWindowContent(userClass);
        Tool quickAddFormButton = DAOTool.createUserQuickAddFormButton(quickForm2);
        windowContent.setToolPanel(DAOToolPanel.createCustomToolPanel(quickAddFormButton));
        DSLContent.edit(windowContent);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        DSLSecurityGroup.addUsers(securityGroup, employee);

        Bo bo1 = DAOUserBo.create(userCase1);
        DSLBo.add(bo1);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(bo1);
        GUITester.assertAbsent(GUIXpath.Any.TEXT_PATTERN,
                "В панели действий присутствует кнопка вызова формы быстрого добавления",
                quickAddFormButton.getTitle());
    }

    /**
     * Тестирование предотвращения потери данных при обрыве сессии, на быстрой форме добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$68740801
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>Добавить атрибут relatedLinkAttr в системную группу атрибутов</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа ownerCase</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <li>Навести указатель на поле выбора значения атрибута relatedLinkAttr</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Выбрать тип userCase1</li>
     * <li>Заполнить поля обязательных атрибутов на форме</li>
     * <li>Разрываем текущую сессию и проверяем что появилось диалоговое окно с предложением остаться на странице</li>
     * </ol>
     */
    @Test
    public void testAddQuickFormIfSessionAreExpired()
    {
        // Подготовка
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        DSLMetainfo.add(relatedLinkAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinkAttr }, new Attribute[0]);

        // Выполнение действий и проверки
        GUILogon.asTester();
        Bo ownerBo = DAOUserBo.create(ownerCase);
        GUIBo.goToAddForm(ownerClass.getFqn(), ownerCase.getFqn());
        GUIBo.fillUserMainFields(ownerBo);
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        Bo relatedBo = DAOUserBo.create(userCase2);
        GUIBo.selectCase(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedBo.getTitle(), "title");
        DSLSession.disconectTesterSession(tester);
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        tester.waitAsyncCall();
        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.confirmByYes();
    }

    /**
     * Тестирование быстрого добавления объекта и использования его в качестве значения ссылочного атрибута
     * другого добавляемого объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>В типа userCase2 создать строковый атрибут stringAttr</li>
     * <li>Добавить атрибут relatedLinkAttr в системную группу атрибутов</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа ownerCase</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <li>Навести указатель на поле выбора значения атрибута relatedLinkAttr</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Нажать на кнопку "Сохранить" в диалоговом окне</li>
     * <li>Проверить, что для поля "Тип объекта" появилось сообщение валидации "Поле должно быть заполнено"</li>
     * <li>Выбрать тип userCase1</li>
     * <li>Проверить, что на форме отсутствует атрибут stringAttr</li>
     * <li>Выбрать тип userCase2</li>
     * <li>Проверить, что на форме присутствует атрибут stringAttr</li>
     * <li>Заполнить поля обязательных атрибутов на форме</li>
     * <li>Заполнить значение атрибута stringAttr</li>
     * <li>Нажать на кнопку "Сохранить" в диалоговом окне</li>
     * <li>Проверить, что значением атрибута relatedLinkAttr является только что добавленный объект</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке созданного объекта relatedLinkAttr заполнен названием созданного
     * на форме быстрого добавления объекта</li>
     * <li>Перейти по ссылке в поле значения атрибута relatedLinkAttr</li>
     * <li>Проверить, что переход произошел на карточку созданного на форме быстрого добавления
     * объекта (совпадает название объекта, тип)</li>
     * <li>Проверить, что атрибут stringAttr на карточке заполнен введенным на форме быстрого добавления значением</li>
     * </ol>
     */
    @Test
    public void testAddRelatedObject()
    {
        // Подготовка
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        Attribute stringAttr = DAOAttribute.createString(userCase2);
        DSLMetainfo.add(relatedLinkAttr, stringAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userCase2), new Attribute[] { stringAttr }, new Attribute[0]);
        // Выполнение действий и проверки
        GUILogon.asTester();
        Bo ownerBo = DAOUserBo.create(ownerCase);
        GUIBo.goToAddForm(ownerClass.getFqn(), ownerCase.getFqn());
        GUIBo.fillUserMainFields(ownerBo);

        GUIForm.clickQuickAddForm(relatedLinkAttr);
        Bo relatedBo = DAOUserBo.create(userCase2);
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIValidation.assertValidation("caseProperty", "Поле должно быть заполнено.");
        GUIBo.selectCase(userCase1);
        GUIForm.assertAttrAbsence(stringAttr);
        GUIBo.selectCase(userCase2);
        GUIForm.assertAttrPresent(stringAttr);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedBo.getTitle(), "title");
        stringAttr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(stringAttr, stringAttr.getValue());
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedBo.getTitle());
        GUIForm.applyForm();
        relatedBo.setUuid(DSLBo.getCreatedObjectUuid(userCase2.getFqn(), new HashSet<>()));
        relatedBo.setExists(null != relatedBo.getUuid());
        ownerBo.setUuid(DSLBo.getCreatedObjectUuid(ownerCase.getFqn(), new HashSet<>()));
        ownerBo.setExists(null != ownerBo.getUuid());

        relatedLinkAttr.setValue(relatedBo.getTitle());
        GUIPropertyList.assertPropertyListAttribute(ownerPropertyList, relatedLinkAttr);
        GUIPropertyList.clickAttributeValueLink(ownerPropertyList, relatedLinkAttr);
        GUIBo.assertThatBoCard(relatedBo);
        GUIPropertyList.assertPropertyListAttribute(userPropertyList, stringAttr);
    }

    /**
     * Тестирование отображения добавленного объекта в качестве возможного значения атрибута
     * после изменения типа объекта на форме добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать форму быстрого добавления и редактирования quickAddForm (Для типов - userCase1,
     * Группа атрибутов - Системные атрибуты)</li>
     * <li>Создать тип ownerCase2 в классе ownerClass</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickAddForm)</li>
     * <li>Добавить relatedLinkAttr в системную группу атрибутов класса ownerClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса ownerClass</li>
     * <li>Открыть форму быстрого добавления объекта по кнопке-ссылке "Добавить" над полем атрибута relatedLinkAttr</li>
     * <li>Заполнить значение "Название" на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления</li>
     * <li>Проверить, что новый объект доступен для выбора в качестве значения атрибута relatedLinkAttr</li>
     * <li>Проверить, что новый объект выбран в качестве значения атрибута relatedLinkAttr</li>
     * <li>Выбрать тип объекта ownerCase2</li>
     * <li>Проверить, что новый объект доступен для выбора в качестве значения атрибута relatedLinkAttr</li>
     * <li>Проверить, что новый объект выбран в качестве значения атрибута relatedLinkAttr</li>
     * </ol>
     */
    @Test
    public void testChangeCaseAfterQuickAdd()
    {
        // Подготовка
        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase1);
        DSLCustomForm.add(quickAddForm);
        MetaClass ownerCase2 = DAOUserCase.create(ownerClass);
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickAddForm.getUuid());
        DSLMetainfo.add(ownerCase2, relatedLinkAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(ownerClass);

        GUIForm.clickQuickAddForm(relatedLinkAttr);
        String relatedObjectTitle = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedObjectTitle, "title");
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUISelect.assertDisplayedByTitle(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedObjectTitle);
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedObjectTitle);
        GUIBo.selectCase(ownerCase2);
        GUISelect.assertDisplayedByTitle(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedObjectTitle);
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedObjectTitle);
    }

    /**
     * Тестирование изменения названия выбранного объекта в поле "Список выбора с папками" после изменения атрибута
     * "Название" с помощью формы быстрого редактирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinksAttr типа "Набор ссылоу на БО" (Класс объектов - userClass,
     * Представление для редактирования - Список выбора с папками, Форма быстрого редактирования - quickForm)</li>
     * <li>Добавить relatedLinksAttr в системную группу атрибутов</li>
     * <li>Создать объект relatedBo типа userCase1</li>
     * <li>Создать объект ownerBo типа ownerCase (relatedLinksAttr = relatedBo)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Проверить, что в поле атрибуты relatedLinksAttr выведено название relatedBo</li>
     * <li>Навести указатель на плашку выбранного значения relatedBo атрибута relatedLinksAttr</li>
     * <li>Нажать на появившуюся на плашке иконку "Редактировать"</li>
     * <li>Изменить название объекта на форме быстрого редактирования</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого редактирования</li>
     * <li>Проверить, что в поле атрибуты relatedLinksAttr выведено обновленное название relatedBo</li>
     * </ol>
     */
    @Test
    public void testChangeTitleInFieldAfterQuickEdit()
    {
        // Подготовка
        Attribute relatedLinksAttr = DAOAttribute.createBoLinks(ownerClass, userClass);
        relatedLinksAttr.setQuickEditForm(quickForm.getUuid());
        relatedLinksAttr.setEditPresentation(AttributeConstant.BOLinksType.LIST_WITH_FOLDER);
        DSLMetainfo.add(relatedLinksAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinksAttr },
                new Attribute[0]);

        Bo relatedBo = DAOUserBo.create(userCase1);
        DSLBo.add(relatedBo);
        Bo ownerBo = DAOUserBo.create(ownerCase);
        relatedLinksAttr.setValue(Json.listToString(relatedBo.getUuid()));
        DAOBo.addAttributeToModel(ownerBo, relatedLinksAttr);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        GUITester.assertValue(GUIXpath.InputComplex.ANY_VALUE, relatedBo.getTitle(), relatedLinksAttr.getCode());
        GUIForm.clickQuickEditForm(relatedLinksAttr, relatedBo.getUuid());
        String newTitle = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, newTitle, "title");
        GUIForm.clickApplyTitledDialog("Редактирование объекта");
        GUIForm.assertDialogDisappear("Форма быстрого редактирования не закрылась.");
        GUITester.assertValue(GUIXpath.InputComplex.ANY_VALUE, newTitle, relatedLinksAttr.getCode());
    }

    /**
     * Тестирование копирования в шаблон и из шаблона списка формы быстрого добавления, настроенной в панели массовых
     * операций.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>Создать модель класс компания rootClass</li>
     * <li>На карточке класса rootClass добавить контент content Список объектов:
     * <br>Класс - userClass
     * <br>Группа атрибутов - Системные атрибуты
     * <br>Представление - Сложный список.</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса rootClass. В контенте content кликнуть на кнопку "Перейти к настройке действий"
     * .</li>
     * <li>В блоке "Панель массовых операций" снять галку в чекбоксе "Использовать системную логику формирования
     * панели массовых операций".</li>
     * <li>Кликнуть на кнопку "Новая кнопка" -> Добавить на панель действий:
     * <br>Название - action
     * <br>Использовать форму быстрого добавления = true
     * <br>Перейти в карточку добавленного объекта = true
     * <br>Форма быстрого добавления - quickForm
     * <br>Сохранить.</li>
     * <li>В списке кликнуть на кнопку "Создать шаблон на базе списка":
     * <br>Название -template
     * <br>Класс объектов списка - Сотрудник.
     * <br>Сохранить.</li>
     * <li>На карточке созданного шаблона в блоке "Настройка списка" кликнуть на кнопку "Перейти к настройке
     * действий".</li>
     * <li>Проверить, что в блоке "Панель массовых операций" есть активная кнопка action.</li>
     * <li>Кликнуть на кнопку action -> Редактировать.</li>
     * <li>Проверить, что Использовать форму быстрого добавления = true, Перейти в карточку добавленного объекта =
     * true, поле Форма быстрого добавления = quickForm</li>
     * </ol>
     */
    @Test
    public void testCopyQuickAddFormFromAndToTemplateMassOperation()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createObjectAdvList(rootClass.getFqn(), DAOGroupAttr.createSystem(
                rootClass), userClass);
        DSLContent.add(content);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(content);

        GUIAdvListEditableToolPanel contentEditableMassToolPanel = content.advlist().editableMassToolPanel();
        contentEditableMassToolPanel.setUseSystemSettings(false);
        contentEditableMassToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        contentEditableMassToolPanel.clickAddContextMenuOption();
        contentEditableMassToolPanel.setTitleOnForm("action");
        contentEditableMassToolPanel.setUseQuickAddForm(true);
        contentEditableMassToolPanel.setGoToAddedObject(true);
        contentEditableMassToolPanel.selectQuickAddForm(quickForm, userClass.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        GUIContent.clickCopyToTemplateIcon(content);
        ListTemplate template = DAOListTemplate.createTemplate(DAOEmployeeCase.createClass());
        template.setExists(true);
        GUIListTemplate.fillAddForm(template);
        GUIForm.applyModalForm();

        GUIAdvListEditableToolPanel templateEditableMassToolPanel = GUIListTemplate.cardSettingAdvlist()
                .editableMassToolPanel();
        templateEditableMassToolPanel.clickEditToolPanelOriginal();
        templateEditableMassToolPanel.assertsEditMode().toolPresenceLinkInToolPanelByTitle("action");
        templateEditableMassToolPanel.rightClickToolByTitle("action");
        templateEditableMassToolPanel.clickEditContextMenuOption();

        templateEditableMassToolPanel.assertUseQuickAddForm(true);
        templateEditableMassToolPanel.assertGoToAddedObject(true);
        templateEditableMassToolPanel.assertQuickAddForm(quickForm, userClass.getCode());
    }

    /**
     * Тестирование копирования из шаблона в список формы быстрого редактирования, настроенной на другой класс.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>Создать модель класса компания rootClass</li>
     * <li>Создать модель системной группы атрибутов класса rootClass, название - sysAttrGroup</li>
     * <li>На карточке класса rootClass добавить контент contentObject Список объектов:
     * <br>Класс - userClass
     * <br>Группа атрибутов - sysAttrGroup
     * <br>Представление - Сложный список.</li>
     * <li>На карточке класса rootClass добавить контент contentChildObject Список вложенных объектов:
     * <br>Класс - Отдел
     * <br>Группа атрибутов - sysAttrGroup
     * <br>Представление - Сложный список.</li>
     * </ol>
     * <br> 
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса rootClass. В контенте contentObject кликнуть на кнопку "Перейти к настройке
     * действий".</li>
     * <li>В блоке "Панель массовых операций" снять галку в чекбоксе "Использовать системную логику формирования
     * панели массовых операций".</li>
     * <li>Кликнуть на кнопку "Редактировать" -> Редактировать:
     * <br>Использовать форму быстрого добавления = true
     * <br>Форма быстрого редактирования - quickForm
     * <li>В блок "Действия из списка" добавить действие action с использованием формы быстрого редактирования -
     * quickForm</li>
     * <br>Сохранить.</li>
     * <li>В списке кликнуть на кнопку "Создать шаблон на базе списка":
     * <br>Название - template
     * <br>Класс объектов списка - userClass.
     * <br>Сохранить.</li>
     * <br>Зарегистрировать модель шаблона в очередь на удаление</li>
     * <li>Перейти на карточку rootClass. В контенте contentChildObject кликнуть на кнопку "Скопировать настройки из
     * шаблона".
     * <br>Выбрать Другие шаблоны -> template. Сохранить. Кликнуть ОК в появившемся информационном сообщении.</li>
     * <li>В контенте contentChildObject кликнуть на иконку "Перейти к настройке действий".</li>
     * <li>Проверить, что в блоке "Панель массовых операций" кнопка "Редактировать" задизейблена.</li>
     * <li>Кликнуть на кнопку "Редактировать" -> Редактировать: Проверить, что в поле Форма быстрого редактирования
     * [не указано].</li>
     * <li>Кликнуть на кнопку "Редактировать" для действия из списка action и проверить, что в поле Форма быстрого
     * редактирования [не указано].</li>
     * </ol>
     */
    @Test
    public void testCopyQuickAddFormFromTemplateWithAnotherClass()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        MetaClass ouCase = DAOOuCase.createClass();
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(rootClass);
        ContentForm contentObject = DAOContentCard.createObjectAdvList(rootClass.getFqn(), sysAttrGroup, userClass);
        DSLContent.add(contentObject);

        ContentForm contentChildObject = DAOContentCard.createChildObjectAdvlist(rootClass.getFqn(), ouCase,
                sysAttrGroup);
        DSLContent.add(contentChildObject);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(contentObject);

        GUIAdvListEditableToolPanel editableMassToolPanel = contentObject.advlist().editableMassToolPanel();
        editableMassToolPanel.setUseSystemSettings(false);
        editableMassToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableMassToolPanel.clickEditContextMenuOption();
        editableMassToolPanel.setUseQuickEditForm(true);
        editableMassToolPanel.selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();

        GUIAdvlistObjectActions objectActions = contentObject.advlist().objectActions();
        objectActions.setUseSystemSettings(false);
        objectActions.selectMenuPosition(ActionsMenuPosition.LEFT);
        GUIAdvlistObjectActions.clickAddTool();
        objectActions.setTitleOnForm("action");
        objectActions.selectActionByCode("edit");
        objectActions.selectInvocationMethod(ActionInvocationMethod.ACTION_LIST);
        GUIAdvListEditableToolPanel editableListObjectToolPanel = contentObject.advlist().editableListObjectToolPanel();
        editableListObjectToolPanel.setUseQuickEditForm(true);
        editableListObjectToolPanel.selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        GUIContent.clickCopyToTemplateIcon(contentObject);
        ListTemplate template = DAOListTemplate.createTemplate(userClass);
        GUIListTemplate.fillAddForm(template);
        GUIForm.applyModalForm();
        template.setExists(true);

        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickCopyFromTemplateIcon(contentChildObject);
        contentChildObject.advlist().copyFromTemplateForm().selectTemplate(OTHERS_TEMPLATE, template.getCode());
        GUIForm.applyInfoDialog();

        GUIContent.clickEditToolPanel(contentChildObject);
        editableMassToolPanel.assertDisabledContentTool(GUIButtonBar.BTN_EDIT);
        editableMassToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableMassToolPanel.clickEditContextMenuOption();
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickEditForm"),
                GUISelect.EMPTY_VALUE);
        GUIForm.applyLastModalForm();

        objectActions.editToolByTitle("action");
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickEditForm"),
                GUISelect.EMPTY_VALUE);
    }

    /**
     * Тестирование быстрого редактирования объекта, выбранного в качестве значения ссылочного атрибута
     * на форме смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>В типа userCase2 создать строковый атрибут stringAttr</li>
     * <li>Добавить атрибут relatedLinkAttr в системную группу атрибутов</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов</li>
     * <li>В классе ownerClass создать пользовательский статус state</li>
     * <li>Настроить обязательное заполнение атрибута relatedLinkAttr при переходе в статус state</li>
     * <li>Настроить переход из статуса "Зарегистрирован" в state</li>
     * <li>Создать объект relatedBo типа userCase2</li>
     * <li>Создать объект ownerBo типа ownerCase (relatedLinkAttr = relatedBo)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта ownerBo</li>
     * <li>Нажать на кнопку "Изменить статус" на панели действий карточки объекта</li>
     * <li>Выбрать статус state</li>
     * <li>Навести указатель на поле выбора значения атрибута relatedLinkAttr</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Редактировать"</li>
     * <li>Изменить значение атрибута stringAttr</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого редактирования</li>
     * <li>Нажать на кнопку "Отмена" на форме смены статуса</li>
     * <li>Проверить, что на экране появилось окно подтверждения действия с текстом:<br>
     * Вы действительно хотите закрыть форму?<br>
     * На форме были внесены следующие изменения: отредактированы объекты "%relatedBo%".<br>
     * При закрытии формы данные изменения будут утеряны.</li>
     * <li>Нажать на кнопку "Нет"</li>
     * <li>Нажать на кнопку "Сохранить" на форме смены статуса</li>
     * <li>Проверить, что статус объекта изменился на state</li>
     * <li>Перейти по ссылке в поле значения атрибута relatedLinkAttr</li>
     * <li>Проверить, что переход произошел на карточку объекта relatedBo</li>
     * <li>Проверить, что атрибут stringAttr на карточке заполнен измененным
     * на форме быстрого редактирования значением</li>
     * </ol>
     */
    @Test
    public void testEditRelatedObjectOnChangeState()
    {
        // Подготовка
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickEditForm(quickForm.getUuid());
        Attribute stringAttr = DAOAttribute.createString(userCase2);
        DSLMetainfo.add(relatedLinkAttr, stringAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userCase2), new Attribute[] { stringAttr }, new Attribute[0]);

        BoStatus state = DAOBoStatus.createUserStatus(ownerClass);
        DSLBoStatus.add(state);
        DSLBoStatus.setAttrInState(relatedLinkAttr, state, true, true, 2, 0);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(ownerClass), state);

        Bo relatedBo = DAOUserBo.create(userCase2);
        stringAttr.setValue(ModelUtils.createTitle());
        DAOBo.addAttributeToModel(relatedBo, stringAttr);
        DSLBo.add(relatedBo);
        Bo ownerBo = DAOUserBo.create(ownerCase);
        relatedLinkAttr.setValue(relatedBo.getUuid());
        DAOBo.addAttributeToModel(ownerBo, relatedLinkAttr);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ownerBo);
        GUIButtonBar.changeState();
        GUISc.selectStatus(state);
        GUIForm.clickQuickEditForm(relatedLinkAttr);
        stringAttr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(stringAttr, stringAttr.getValue());
        GUIForm.clickApplyTitledDialog("Редактирование объекта");

        GUIForm.clickCancel();
        String questionFormat = "Вы действительно хотите закрыть форму?\n\n"
                                + "На форме были внесены следующие изменения: отредактированы объекты \"%s\".\n\n"
                                + "При закрытии формы данные изменения будут утеряны.";
        GUIForm.assertQuestion(questionFormat, relatedBo.getTitle());
        GUIForm.clickNo();

        GUIForm.applyModalForm();
        stateAttr.setValue(state.getTitle());
        GUIPropertyList.assertPropertyListAttribute(ownerPropertyList, stateAttr);
        relatedLinkAttr.setValue(relatedBo.getTitle());
        GUIPropertyList.assertPropertyListAttribute(ownerPropertyList, relatedLinkAttr);
        GUIPropertyList.clickAttributeValueLink(ownerPropertyList, relatedLinkAttr);
        GUIBo.assertThatBoCard(relatedBo);
        GUIPropertyList.assertPropertyListAttribute(userPropertyList, stringAttr);
    }

    /**
     * Тестирование сообщения об ошибке при попытке быстрого добавления двух объектов с одинаковым
     * значением уникального атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут uniqueStringAttr (Уникальный - Да) и добавить
     * его в системную группу атрибутов</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Набор ссылок на БО" (Класс объектов - userClass,
     * Форма быстрого добавления - quickForm) и добавить его в системную группу атрибутов</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте relatedLinkAttr</li>
     * <li>Заполнить поля формы (Тип объекта - userCase1, uniqueStringAttr - произвольная строка uniqueAttrValue)</li>
     * <li>Нажать "Сохранить" на форме быстрого добавления</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте relatedLinkAttr</li>
     * <li>Заполнить поля формы (Тип объекта - userCase1, uniqueStringAttr - uniqueAttrValue)</li>
     * <li>Нажать "Сохранить" на форме быстрого добавления</li>
     * <li>Нажать "Сохранить" на форме редактирования</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме появилось сообщение об ошибке:<br>
     * %userClass% не может быть добавлен по следующим причинам:<br>
     * 1. Атрибут %uniqueStringAttr% должен быть уникальным.
     * Объект %userClass% с таким значением атрибута уже существует: %название последнего добавляемого объекта%.</li>
     * </ol>
     */
    @Test
    public void testErrorOnQuickAddNonUniqueObjects()
    {
        // Подготовка
        Attribute uniqueStringAttr = DAOAttribute.createString(userClass);
        uniqueStringAttr.setUnique(Boolean.TRUE.toString());
        Attribute relatedLinkAttr = DAOAttribute.createBoLinks(ownerClass, userClass);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        DSLMetainfo.add(uniqueStringAttr, relatedLinkAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { uniqueStringAttr }, new Attribute[0]);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { relatedLinkAttr }, new Attribute[0]);

        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        String uniqueAttrValue = ModelUtils.createTitle();

        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, ModelUtils.createTitle(), "title");
        GUIForm.fillAttribute(uniqueStringAttr, uniqueAttrValue);
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        String lastAddedTitle;
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, lastAddedTitle = ModelUtils.createTitle(),
                "title");
        GUIForm.fillAttribute(uniqueStringAttr, uniqueAttrValue);
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");
        // Проверка
        String messageFormat = "%1$s не может быть добавлен по следующим причинам:\n "
                               + "1. Атрибут %2$s должен быть уникальным. "
                               + "Объект %1$s с таким значением атрибута уже существует: %3$s.";
        GUIForm.applyFormAssertError(
                String.format(messageFormat, userClass.getTitle(), uniqueStringAttr.getTitle(), lastAddedTitle));
    }

    /**
     * Тесирования открытия формы быстрого добавления для обратной ссылки, для которая прямая ссылка
     * объявлена в типе, а не в классе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В типе userCase1 создать атрибут directLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - ownerClass)</li>
     * <li>В классе ownerClass создать атрибут backLinkAttr типа "Обратная ссылка" (Прямая ссылка - directLinkAttr,
     * Формы быстрого добавления - quickForm, Представление для редактирования - Список выбора с папками)</li>
     * <li>Добавить атрибут backLinkAttr в системную группу атрибутов</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Навести указатель на поле выбора значения атрибута backLinkAttr</li>
     * <li>Нажать на появившуюся над полем кнопку-ссылку "Добавить"</li>
     * <li>На форме быстрого добавления заполнить название нового объекта</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления</li>
     * <li>Проверить, что только что созданный объект стал значенем атрибута backLinkAttr на форме</li>
     * </ol>
     */
    @Test
    public void testOpenQuickAddFormForBackLinkWithOnlyCasePermitted()
    {
        // Подготовка
        Attribute directLinkAttr = DAOAttribute.createObjectLink(userCase1, ownerClass, null);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(ownerClass.getFqn(), directLinkAttr);
        backLinkAttr.setQuickAddForm(quickForm.getUuid());
        backLinkAttr.setEditPresentation(AttributeConstant.BackLinkType.LIST_WITH_FOLDER);
        DSLMetainfo.add(directLinkAttr, backLinkAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { backLinkAttr }, new Attribute[0]);
        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        GUIForm.clickQuickAddForm(backLinkAttr);
        String objectTitle = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, objectTitle, "title");
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");
        GUITester.assertValue(GUIXpath.InputComplex.ANY_VALUE, objectTitle, backLinkAttr.getCode());
    }

    /**
     * Тестирование отображения изменений в контенте "Параметры связанного объекта" на форме после выполнения
     * быстрого добавления или редактирования в поле атрибута связи
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект" (Класс объектов -
     * userClass, Форма быстрого добавления - quickForm Форма быстрого редактирования - quickForm)</li>
     * <li>Добавить relatedLinkAttr в системную группу атрибутов класса ownerClass</li>
     * <li>На форму добавления объекта класса ownerClass вывести контент relatedPropertyList типа
     * "Параметры связанного объекта" (Атрибут связи - relatedLinkAttr, Группа атрибутов - Системные атрибуты)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>Проверить, что relatedPropertyList отсутствует на форме</li>
     * <li>Открыть форму быстрого добавления объекта по атрибуту relatedLinkAttr</li>
     * <li>Выбрать тип объекта userCase1</li>
     * <li>Заполнить название объекта произвольной строкой title1</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления</li>
     * <li>Проверить, что relatedPropertyList присутствует на форме</li>
     * <li>Проверить, что в контенте relatedPropertyList Название = %title1%</li>
     * <li>Открыть форму быстрого редактирования объекта по атрибуту relatedLinkAttr</li>
     * <li>Заполнить название объекта произвольной строкой title2</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого редактирования</li>
     * <li>Проверить, что relatedPropertyList присутствует на форме</li>
     * <li>Проверить, что в контенте relatedPropertyList Название = %title2%</li>
     * </ol>
     */
    @Test
    public void testQuickAddEditInRelatedPropertyList()
    {
        // Подготовка
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        relatedLinkAttr.setQuickEditForm(quickForm.getUuid());
        DSLMetainfo.add(relatedLinkAttr);
        GroupAttr systemAttrGroup = DAOGroupAttr.createSystem(ownerClass);
        Attribute relatedTitleAttr = SysAttribute.title(userClass);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        ContentForm relatedPropertyList = DAOContentAddForm.createRelObjPropList(ownerClass, relatedLinkAttr,
                systemAttrGroup);
        DSLContent.add(relatedPropertyList);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(ownerCase);
        GUIContent.assertAbsence(relatedPropertyList);

        relatedTitleAttr.setValue(ModelUtils.createTitle());
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedTitleAttr.getValue(), "title");
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");
        GUIContent.assertPresent(relatedPropertyList);
        GUIPropertyList.assertPropertyListAttributeValue(relatedPropertyList, relatedTitleAttr);

        relatedTitleAttr.setValue(ModelUtils.createTitle());
        GUIForm.clickQuickEditForm(relatedLinkAttr);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedTitleAttr.getValue(), "title");
        GUIForm.clickApplyTitledDialog("Редактирование объекта");
        GUIForm.assertDialogDisappear("Форма быстрого редактирования не закрылась.");
        GUIContent.assertPresent(relatedPropertyList);
        GUIPropertyList.assertPropertyListAttributeValue(relatedPropertyList, relatedTitleAttr);
    }

    /**
     * Тестирование быстрого добавления объекта на форме смены типа, если этот объект является
     * значением атрибута, объявленного в новом типе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать форму быстрого добавления и редактирования quickAddForm
     * (Для типов - userCase1, Группа атрибутов - Системные атрибуты)</li>
     * <li></li>
     * <li>Добавить новый тип ownerCase2 в классе ownerClass</li>
     * <li>В типе ownerCase2 создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Формы быстрого добавления - quickAddForm)</li>
     * <li>В системную группу атрибутов типа ownerCase2 добавить атрибут relatedLinkAttr</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта ownerBo</li>
     * <li>Нажать на кнопку "Изменить тип"</li>
     * <li>Выбрать тип onwerCase2</li>
     * <li>Навести указатель на поле выбора значения атрибута relatedLinkAttr</li>
     * <li>Нажать на появившуюся над полем кнопку-ссылку "Добавить"</li>
     * <li>На форме быстрого добавления заполнить название нового объекта</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого добавления</li>
     * <li>Нажать на кнопку "Отмена" на форме смены статуса</li>
     * <li>Проверить, что на экране появилось окно подтверждения действия с текстом:<br>
     * Вы действительно хотите закрыть форму?<br>
     * На форме были внесены следующие изменения: добавлены объекты "%название созданного объекта%".<br>
     * При закрытии формы данные изменения будут утеряны.</li>
     * <li>Нажать на кнопку "Нет"</li>
     * <li>Нажать на кнопку "Сохранить" на форме смены типа</li>
     * <li>Проверить, что тип объекта изменился на ownerCase2</li>
     * <li>Проверить, что на карточке созданного объекта relatedLinkAttr заполнен названием созданного
     * на форме быстрого добавления объекта</li>
     * <li>Перейти по ссылке в поле значения атрибута relatedLinkAttr</li>
     * <li>Проверить, что переход произошел на карточку созданного на форме быстрого добавления
     * объекта (совпадает название объекта, тип)</li>
     * </ol>
     */
    @Test
    public void testQuickAddForAttributeInDestinationCase()
    {
        // Подготовка
        CustomForm quickAddForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userCase1), userCase1);
        DSLCustomForm.add(quickAddForm);
        MetaClass ownerCase2 = DAOUserCase.create(ownerClass);
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerCase2, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickAddForm.getUuid());
        DSLMetainfo.add(ownerCase2, relatedLinkAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerCase2), new Attribute[] { relatedLinkAttr }, new Attribute[0]);

        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ownerBo);
        GUIButtonBar.changeCase();
        GUIChangeCaseForm.selectCase(ownerCase2);

        GUIForm.clickQuickAddForm(relatedLinkAttr);
        Bo relatedBo = DAOUserBo.create(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedBo.getTitle(), "title");
        GUIForm.clickApplyTitledDialog("Добавление объекта");

        GUIForm.clickCancel();
        String questionFormat = "Вы действительно хотите закрыть форму?\n\n"
                                + "На форме были внесены следующие изменения: добавлены объекты \"%s\".\n\n"
                                + "При закрытии формы данные изменения будут утеряны.";
        GUIForm.assertQuestion(questionFormat, relatedBo.getTitle());
        GUIForm.clickNo();

        GUIForm.applyModalForm();
        relatedBo.setUuid(DSLBo.getCreatedObjectUuid(userCase1.getFqn(), new HashSet<>()));
        relatedBo.setExists(null != relatedBo.getUuid());

        ownerBo.setMetaclassTitle(ownerCase2.getTitle());
        GUIBo.assertThatBoCard(ownerBo);
        relatedLinkAttr.setValue(relatedBo.getTitle());
        GUIPropertyList.assertPropertyListAttribute(ownerPropertyList, relatedLinkAttr);
        GUIPropertyList.clickAttributeValueLink(ownerPropertyList, relatedLinkAttr);
        GUIBo.assertThatBoCard(relatedBo);
    }

    /**
     * Тестирование редактирования объекта с помощью формы быстрого действия, на которую настроена кнопка
     * "Редактировать" панели действий карточки объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов</li>
     * <li>Разорвать наследование настроек интерфейса в типе userCase1</li>
     * <li>Для панели действий карточки объекта типа userCase1 установить значение параметра
     * Использовать системную логику формирования панели действий = нет</li>
     * <li>Изменить кнопку "Редактировать" панели действий карточки объекта типа userCase: Использовать форму быстрого
     * редактирования = да, Форма быстрого редактирования - quickForm</li>
     * <li>Создать объект userBo типа userCase1</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку "Редактировать" панели действий карточки</li>
     * <li>На форме быстрого редактирования изменить значение атрибута stringAttr</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма закрылась, на карточке объекта обновилось значение атрибута stringAttr</li>
     * </ol>
     */
    @Test
    public void testQuickEditObjectFromSystemEditButton()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { stringAttr }, new Attribute[0]);
        DSLContent.editSettings(userCase1, MetaclassCardTab.OBJECTCARD);
        Cleaner.afterTest(() -> DSLContent.resetContentSettings(userCase1, MetaclassCardTab.OBJECTCARD));

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase1, MetaclassCardTab.OBJECTCARD);

        ContentForm window = DSLContent.getWindowContent(userCase1);
        GUIContent.clickEditToolPanel(window);
        GUIAdvListEditableToolPanel editableToolPanel = window.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setUseQuickEditForm(true);
        editableToolPanel.selectQuickEditForm(quickForm);
        editableToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        GUILogon.logout();

        Bo userBo = DAOUserBo.create(userCase1);
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.edit();
        GUIForm.assertDialogAppear("Форма быстрого редактирования не появилась.");
        stringAttr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(stringAttr, stringAttr.getValue());
        GUIForm.applyModalForm();
        // Проверки
        GUIBo.assertAttributeValueOnBoCard(stringAttr, stringAttr.getValue());
    }

    /**
     * Тестирование выполнения быстрого редактирования, вызванного с формы массового редактирования, если это
     * единственное изменение
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать форму массового редактирования massEditForm (Группа атрибутов - Системные атрибуты,
     * Для типов -  ownerCase, Использовать как форму по умолчанию - да)</li>
     * <li>В классе ownerClass создать атрибут linkAttr типа "Ссылка на бизнес-объект" (Класс объектов - userClass,
     * Форма быстрого редактирования - quickForm)</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>Добавить атрибут linkAttr в системную группу атрибутов</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов</li>
     * <li>Создать объект linkedBo типа userCase1</li>
     * <li>Создать объект ownerBo1 типа ownerCase (linkAttr = linkedBo)</li>
     * <li>Создать объект ownerBo2 типа ownerCase (linkAttr = linkedBo)</li>
     * <li>На карточку объекта ownerClass вывести контент objectList типа "Список объектов"
     * (Класс объектов - ownerClass, Представление - Сложный список)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта ownerBo1</li>
     * <li>В списке objectList выбрать все объекты</li>
     * <li>На панели массовых операций нажать на кнопку-ссылку "массовое редактирование"</li>
     * <li>Вызвать форму быстрого редактирования для атрибута linkAttr, нажав на кнопку-ссылку "Редактировать"</li>
     * <li>Изменить значение атрибута stringAttr</li>
     * <li>Нажать на кнопку "Сохранить" на форме быстрого редактирования</li>
     * <li>Нажать на кнопку "Отмена" на форме массового редактирования</li>
     * <li>Проверить, что на экране появилось окно подтверждения действия с текстом:<br>
     * Вы действительно хотите закрыть форму?<br>
     * На форме были внесены следующие изменения: отредактированы объекты "%linkedBo%".<br>
     * При закрытии формы данные изменения будут утеряны.</li>
     * <li>Нажать на кнопку "Нет"</li>
     * <li>Нажать на кнопку "Сохранить" на форме массового редактирования</li>
     * <li>Проверить, что значение атрибута stringAttr объекта linkedBo было изменено</li>
     * </ol>
     */
    @Test
    public void testQuickEditObjectOnMassEditForm()
    {
        // Подготовка
        GroupAttr systemAttrGroup = DAOGroupAttr.createSystem(ownerClass);
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(systemAttrGroup, CommentOnFormProperty.NOT_FILL,
                true, ownerCase);
        DSLCustomForm.add(massEditForm);

        Attribute linkAttr = DAOAttribute.createObjectLink(ownerClass.getFqn(), userClass, null);
        linkAttr.setQuickEditForm(quickForm.getUuid());
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(linkAttr, stringAttr);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { linkAttr }, new Attribute[0]);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { stringAttr }, new Attribute[0]);

        Bo linkedBo = DAOUserBo.create(userCase1);
        DSLBo.add(linkedBo);
        linkAttr.setValue(linkedBo.getUuid());
        Bo ownerBo1 = DAOUserBo.create(ownerCase);
        Bo ownerBo2 = DAOUserBo.create(ownerCase);
        DAOBo.addAttributeToModel(ownerBo1, linkAttr);
        DAOBo.addAttributeToModel(ownerBo2, linkAttr);
        DSLBo.add(ownerBo1, ownerBo2);

        ContentForm objectList = DAOContentCard.createObjectAdvList(ownerClass.getFqn(), systemAttrGroup, ownerClass);
        DSLContent.add(objectList);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ownerBo1);
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().clickOperation(MassOperation.MASS_EDIT);
        GUIForm.clickQuickEditForm(linkAttr);
        String newStringValue = ModelUtils.createTitle();
        stringAttr.setValue(newStringValue);
        GUIForm.fillAttribute(stringAttr, newStringValue);
        GUIForm.applyLastModalForm();

        GUIForm.clickCancel();
        String questionFormat = "Вы действительно хотите закрыть форму?\n\n"
                                + "На форме были внесены следующие изменения: отредактированы объекты \"%s\".\n\n"
                                + "При закрытии формы данные изменения будут утеряны.";
        GUIForm.assertQuestion(questionFormat, linkedBo.getTitle());
        GUIForm.clickNo();

        GUIForm.applyModalForm();
        DSLBo.assertAttributes(linkedBo, stringAttr);
    }

    /**
     * Тестирование выгрузки\загрузки частичной метаинформации с настроенными быстрыми формами в контенте "Параметры
     * объекта".
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>На карточке типа userCase 1 добавить контент content "Параметры объекта":
     * <br>Группа атрибутов - системные атрибуты.
     * <li>Залогиниться под суперюзером.</li>
     * <li>Перейти на карточку объекта в типе userCase1</li>
     * <li>В контенте content кликнуть на иконку "Перейти к настройке действий". 
     * <br>Установить чекбокс "Использовать системную логику формирования панели действий" = false.</li>
     * <li>Кликнуть на кнопку "Редактировать" -> Редактировать:
     * <br>Использовать форму быстрого добавления = true
     * <br>Форма быстрого редактирования - quickForm
     * <br>Сохранить.</li>
     * <li>Кликнуть на кнопку "Новая кнопка" -> Добавить на панель действий:
     * <br>Название\код - action1
     * <br>Использовать форму быстрого добавления = true
     * <br>Перейти в карточку добавленного объекта = true
     * <br>Форма быстрого добавления - quickForm
     * <br>Сохранить.</li>
     * <li>Кликнуть на кнопку "Новая кнопка" -> Добавить на панель действий:
     * <br>Название\код - action2
     * <br>Использовать форму быстрого добавления = true
     * <br>Перейти в карточку добавленного объекта = false
     * <br>Форма быстрого добавления - quickForm
     * <br>Сохранить.</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Создать модель для выгрузки метаинформации exportModel</li>
     * <li>Выгрузить метаинформацию контента content в файл metainfo</li>
     * <li>Кликнуть на кнопку "Сбросить настройки" на карточке типа userCase1. В появившемся информационном сообщении
     * нажать "Да".</li>
     * <li>Загрузить метаинформацию из файла metainfo</li>
     * <li>Перейти на карточку типа userCase1. Кликнуть на иконку "Перейти к настройке действий" в контенте content
     * .</li>
     * <li>Кликнуть на кнопку "Редактировать" -> Редактировать.
     * <br>Проверить, что Использовать форму быстрого добавления = true, в поле Форма быстрого редактирования выбрана
     * форма quickForm.</li>
     * <li>Кликнуть на кнопку "action1" -> Редактировать.
     * <br>Проверить, что Использовать форму быстрого добавления = true, Перейти в карточку добавленного объекта =
     * true, в поле Форма быстрого добавления выбрана форма quickForm.</li>
     * <li>Кликнуть на кнопку "action2" -> Редактировать.
     * <br>Проверить, что Использовать форму быстрого добавления = true, Перейти в карточку добавленного объекта =
     * false, в поле Форма быстрого добавления выбрана форма quickForm.</li>
     * </ol>
     */
    @Test
    public void testQuickFormsWithMetainfoImport()
    {
        //Подготовка
        ContentForm content = DAOContentCard.createPropertyList(userCase1, DAOGroupAttr.createSystem(userCase1));
        DSLContent.add(content);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userCase1, MetaclassCardTab.OBJECTCARD);

        GUIContent.clickEditToolPanel(content);
        GUIAdvListEditableToolPanel editableToolPanel = content.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);

        editableToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setUseQuickEditForm(true);
        editableToolPanel.selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();

        editableToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.setTitleOnForm("action1");
        editableToolPanel.setUseQuickAddForm(true);
        editableToolPanel.setGoToAddedObject(true);
        editableToolPanel.selectQuickAddForm(quickForm, userClass.getCode());
        GUIForm.applyLastModalForm();

        editableToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        editableToolPanel.clickAddContextMenuOption();
        editableToolPanel.setTitleOnForm("action2");
        editableToolPanel.setUseQuickAddForm(true);
        editableToolPanel.setGoToAddedObject(false);
        editableToolPanel.selectQuickAddForm(quickForm, userClass.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        //Выполнение действий и проверки
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectContentsOnObjectCard(exportModel, content);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLContent.resetContentSettings(userCase1, MetaclassCardTab.OBJECTCARD);

        DSLMetainfoTransfer.importMetainfo(metainfo);

        GUIContent.clickEditToolPanel(content);

        editableToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.assertUseQuickEditForm(true);
        editableToolPanel.assertQuickEditForm(quickForm);
        GUIForm.clickCancelTopmostDialog();

        editableToolPanel.rightClickUserTool("action1");
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.assertUseQuickAddForm(true);
        editableToolPanel.assertGoToAddedObject(true);
        editableToolPanel.assertQuickAddForm(quickForm, userClass.getCode());
        GUIForm.clickCancelTopmostDialog();

        editableToolPanel.rightClickUserTool("action2");
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.assertUseQuickAddForm(true);
        editableToolPanel.assertGoToAddedObject(false);
        editableToolPanel.assertQuickAddForm(quickForm, userClass.getCode());
        GUIForm.clickCancelTopmostDialog();
    }

    /**
     * Тестирование формы быстрого добавления, настроенной на группы атрибутов с одинаковым кодом,
     * объявленные в разных типах
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В типе userCase1 создать строковый атрибут stringAttr1</li>
     * <li>В типе userCase2 создать строковый атрибут stringAttr2</li>
     * <li>В типе userCase1 создать группу атрибутов group1 и добавить в нее атрибут stringAttr1</li>
     * <li>В типе userCase2 создать группу атрибутов group2 (с таким же кодом, как и у group1)
     * и добавить в нее атрибут stringAttr2</li>
     * <li>В классе userClass создать форму быстрого добавления и редактирования quickForm (Группа атрибутов - group1,
     * Для типов - userCase1, userCase2)</li>
     * <li>В классе ownerClass создать атрибут linkAttr типа "Ссылка на бизнес-объект" (Класс объектов - userClass,
     * Форма быстрого редактирования - quickForm)</li>
     * <li>Создать группу атрибутов ownerGroup и добавить в нее атрибут linkAttr</li>
     * <li>На форму редактирования объектов класса ownerClass вывести контент "Параметры на форме"
     * (Группа атрибутов - ownerGroup)</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Навести указатель на поле выбора значения атрибута linkAttr</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Проверить, что на форме отсутствуют атрибуты, есть только поле выбора типа</li>
     * <li>Выбрать тип userCase1</li>
     * <li>Проверить, что на форме появился атрибут stringAttr1</li>
     * <li>Выбрать тип userCase2</li>
     * <li>Проверить, что на форме появился атрибут stringAttr2</li>
     * </ol>
     */
    @Test
    public void testQuickFormWithAttributeGroupDeclaredInCases()
    {
        // Подготовка
        Attribute stringAttr1 = DAOAttribute.createString(userCase1);
        Attribute stringAttr2 = DAOAttribute.createString(userCase2);
        DSLMetainfo.add(stringAttr1, stringAttr2);

        GroupAttr group1 = DAOGroupAttr.create(userCase1);
        GroupAttr group2 = DAOGroupAttr.create(userCase2);
        group2.setCode(group1.getCode());
        DSLGroupAttr.add(group1, stringAttr1);
        DSLGroupAttr.add(group2, stringAttr2);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(group1, userCase1, userCase2);
        DSLCustomForm.add(quickForm);
        Attribute linkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        linkAttr.setQuickAddForm(quickForm.getUuid());
        DSLMetainfo.add(linkAttr);
        GroupAttr ownerGroup = DAOGroupAttr.create(ownerClass);
        DSLGroupAttr.add(ownerGroup, linkAttr);
        DSLContent.add(DAOContentEditForm.createEditablePropertyList(ownerClass.getFqn(), ownerGroup));
        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        GUIForm.clickQuickAddForm(linkAttr);
        GUIForm.assertAttrAbsence(stringAttr1);
        GUIForm.assertAttrAbsence(stringAttr2);
        GUIBo.selectCase(userCase1);
        GUIForm.assertAttrAbsence(stringAttr2);
        GUIForm.assertAttrPresent(stringAttr1);
        GUIBo.selectCase(userCase2);
        GUIForm.assertAttrAbsence(stringAttr1);
        GUIForm.assertAttrPresent(stringAttr2);
    }

    /**
     * Тестирование обязательного в интерфейсе атрибута, размещенного на форме быстрого добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>В классе userClass создать строковый атрибут stringAttr (Обязательный для заполнения в интерфейсе - да,
     * Значение по умолчанию - пусто)</li>
     * <li>Добавить атрибут relatedLinkAttr в системную группу атрибутов класса ownerClass</li>
     * <li>Добавить атрибут stringAttr в системную группу атрибутов класса userClass</li>
     * <li>Создать объект ownerBo типа ownerCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте relatedLinkAttr</li>
     * <li>Выбрать тип объекта userCase1</li>
     * <li>Заполнить название объекта</li>
     * <li>Нажать "Сохранить" на форме быстрого добавления</li>
     * <li>Проверить, форма не закрылась, а около поля атрибута stringAttr появилось сообщение валидатора
     * "Поле должно быть заполнено"</li>
     * <li>Заполнить значение атрибута stringAttr</li>
     * <li>Нажать "Сохранить" на форме быстрого добавления</li>
     * <li>Проверить, что значение атрибута relatedLinkAttr заполнилось созданным объектом</li>
     * </ol>
     */
    @Test
    public void testRequiredInInterfaceAttributeOnQuickForm()
    {
        // Подготовка
        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        Attribute stringAttr = DAOAttribute.createString(userClass);
        stringAttr.setRequiredInInterface(Boolean.TRUE.toString());
        stringAttr.setDefaultValue(StringUtils.EMPTY_STRING);
        DSLMetainfo.add(relatedLinkAttr, stringAttr);

        GroupAttr ownerSystemAttrGroup = DAOGroupAttr.createSystem(ownerClass);
        DSLGroupAttr.edit(ownerSystemAttrGroup, new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        GroupAttr systemAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { stringAttr }, new Attribute[0]);

        Bo ownerBo = DAOUserBo.create(ownerCase);
        Bo relatedBo = DAOUserBo.create(userCase1);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relatedBo.getTitle(), "title");
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIValidation.assertNotEmptyValidation(stringAttr);
        GUIForm.fillAttribute(stringAttr, ModelUtils.createTitle());
        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, relatedLinkAttr.getCode()),
                relatedBo.getTitle());
    }

    /**
     * Тестирование контекстной переменной sourceForm в скриптах фильтрации атрибутов на форме быстрого добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$54850517
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить сотрудников employee1 и employee2</li>
     * <li>В классе ownerClass создать атрибут relatedLinkAttr типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>В классе userClass создать атрибут employeeComplexFormAttr типа "Набор ссылок на БО"
     * (Класс объектов - Сотрудник, Сложная форма добавления связи - да)</li>
     * <li>В классе userClass создать атрибут employeeFastSelectionAttr типа "Набор ссылок на БО"
     * (Класс объектов - Сотрудник, Представление для редактирования - Поле быстрого выбора)</li>
     * <li>Атрибутам employeeComplexFormAttr и employeeFastSelectionAttr задать скрипт фильтрации значений
     * при редактировании:<br>
     * <pre>
     * if (null == subject) {
     *     return []
     * }
     * return '%specialValue%' == sourceForm?.title ? ['%employee1%'] : ['%employee2%']</pre></li>
     * <li>В системную группу атрибутов класса ownerClass добавить атрибут relatedLinkAttr</li>
     * <li>В системную группу атрибутов класса userClass добавить атрибуты employeeComplexFormAttr и
     * employeeFastSelectionAttr</li>
     * <li>Создать объект onwerBo типа ownerCase</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования объекта ownerBo</li>
     * <li>Изменить значение атрибута "Название" на specialValue</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте relatedLinkAttr</li>
     * <li>Выбрать тип объекта userCase1</li>
     * <li>Проверить, что среди возможных значений employeeComplexFormAttr есть только employee1</li>
     * <li>Открыть сложную форму добавления связи для атрибута employeeComplexFormAttr</li>
     * <li>Проверить, что на сложной форме доступен для выбора только employee1</li>
     * <li>Нажать на кнопку "Отмена" на сложной форме</li>
     * <li>Проверить, что среди возможных значений employeeFastSelectionAttr есть только employee1</li>
     * <li>Нажать на кнопку "Отмена" на форме быстрого добавления</li>
     * <li>Изменить значение атрибута "Название" на любое другое</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте relatedLinkAttr</li>
     * <li>Выбрать тип объекта userCase1</li>
     * <li>Проверить, что среди возможных значений employeeComplexFormAttr есть только employee2</li>
     * <li>Открыть сложную форму добавления связи для атрибута employeeComplexFormAttr</li>
     * <li>Проверить, что на сложной форме доступен для выбора только employee2</li>
     * <li>Нажать на кнопку "Отмена" на сложной форме</li>
     * <li>Проверить, что среди возможных значений employeeFastSelectionAttr есть только employee2</li>
     * </ol>
     */
    @Test
    public void testSourceFormInFiltrationScript()
    {
        //@formatter:off
        final String scriptTemplate =
                "if (null == subject) {\n"
              + "    return []\n"
              + "}\n"
              + "return '%s' == sourceForm?.title ? ['%s'] : ['%s']";
        //@formatter:on
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Attribute ownerTitle = SysAttribute.title(ownerClass);
        // Подготовка
        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee1, employee2);
        String specialValue = ModelUtils.createTitle();
        ScriptInfo filtrationScript = DAOScriptInfo.createNewScriptInfo(
                String.format(scriptTemplate, specialValue, employee1.getUuid(), employee2.getUuid()));
        DSLScriptInfo.addScript(filtrationScript);

        Attribute relatedLinkAttr = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        relatedLinkAttr.setQuickAddForm(quickForm.getUuid());
        Attribute employeeComplexFormAttr = DAOAttribute.createBoLinks(userClass, employeeClass);
        DAOAttribute.addComplexHierarchyRelationForm(employeeComplexFormAttr, DAOGroupAttr.createSystem(employeeClass));
        DAOAttribute.changeToEditFilter(employeeComplexFormAttr, filtrationScript);
        Attribute employeeFastSelectionAttr = DAOAttribute.createBoLinks(userClass, employeeClass);
        employeeFastSelectionAttr.setEditPresentation(AttributeConstant.BOLinksType.FAST_SELECTION_FIELD);
        DAOAttribute.changeToEditFilter(employeeFastSelectionAttr, filtrationScript);
        DSLMetainfo.add(relatedLinkAttr, employeeComplexFormAttr, employeeFastSelectionAttr);

        GroupAttr ownerSystemAttrGroup = DAOGroupAttr.createSystem(ownerClass);
        DSLGroupAttr.edit(ownerSystemAttrGroup, new Attribute[] { relatedLinkAttr }, new Attribute[0]);
        GroupAttr systemAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { employeeComplexFormAttr, employeeFastSelectionAttr },
                new Attribute[0]);

        Bo ownerBo = DAOUserBo.create(ownerCase);
        DSLBo.add(ownerBo);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(ownerBo);
        GUIForm.fillAttribute(ownerTitle, specialValue);
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        BoTree simpleTree = new BoTree(GUIXpath.Any.ANY_VALUE, false, employeeComplexFormAttr.getCode());
        simpleTree.assertPresentElement(true, SharedFixture.ou(), employee1);
        simpleTree.assertPresentElement(false, SharedFixture.ou(), employee2);

        GUIComplexRelationForm.openComplexRelationForm(employeeComplexFormAttr.getCode());
        GUIComplexRelationForm.assertBoVisible(true, employee1);
        GUIComplexRelationForm.assertBoVisible(false, employee2);
        GUIComplexRelationForm.clickCancel();

        BoTree fastSelectionTree = new BoTree(GUIXpath.Any.ANY_VALUE, false, employeeFastSelectionAttr.getCode());
        fastSelectionTree.assertPresentElement(true, SharedFixture.ou(), employee1);
        fastSelectionTree.assertPresentElement(false, SharedFixture.ou(), employee2);

        GUIForm.clickCancelTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUIForm.fillAttribute(ownerTitle, ModelUtils.createTitle());
        GUIForm.clickQuickAddForm(relatedLinkAttr);
        GUIBo.selectCase(userCase1);
        simpleTree.assertPresentElement(false, SharedFixture.ou(), employee1);
        simpleTree.assertPresentElement(true, SharedFixture.ou(), employee2);

        GUIComplexRelationForm.openComplexRelationForm(employeeComplexFormAttr.getCode());
        GUIComplexRelationForm.assertBoVisible(false, employee1);
        GUIComplexRelationForm.assertBoVisible(true, employee2);
        GUIComplexRelationForm.clickCancel();

        fastSelectionTree.assertPresentElement(false, SharedFixture.ou(), employee1);
        fastSelectionTree.assertPresentElement(true, SharedFixture.ou(), employee2);
    }

    /**
     * Тестирование добавления связи с объектом, созданным через быструю форму добавления, у ссылочных атрибутов со
     * скриптом фильтрации с представлением для редактирования "Список выбора с папками", "Дерево выбора c папками",
     * "Дерево выбора"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94195045
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создадим скрипт фильтрации filterScript</li>
     * <br>
     * <li>В классе ownerClass создать атрибут attrListWitFolder типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>Для атрибута attrListWitFolder установим скрипт фильтрации filterScript и представление для редактирования
     * "Список выбора с папками"</li>
     * <li>Добавить атрибут attrListWitFolder в системную группу атрибутов</li>
     * <br>
     * <li>В классе ownerClass создать атрибут attrTreeWithFolder типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>Для атрибута attrTreeWithFolder установим скрипт фильтрации filterScript и представление для
     * редактирования "Дерево выбора с папками"</li>
     * <li>Добавить атрибут attrTreeWithFolder в системную группу атрибутов</li>
     * <br>
     * <li>В классе ownerClass создать атрибут attrNboTree типа "НБО"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>Для атрибута attrNboTree установим скрипт фильтрации filterScript и представление для
     *  редактирования "Дерево выбора"</li>
     * <li>Добавить атрибут attrNboTree в системную группу атрибутов</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта типа ownerCase</li>
     * <li>Заполнить обязательные поля на форме</li>
     * <br>
     * <li>Навести указатель на поле выбора значения атрибута attrListWitFolder</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Заполнить поля обязательных атрибутов на форме</li>
     * <li>Нажать на кнопку "Сохранить" в диалоговом окне</li>
     * <li>Проверить, что значением атрибута attrTreeWithFolder является только что добавленный объект</li>
     * <br>
     * <li>Навести указатель на поле выбора значения атрибута attrTreeWithFolder</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Заполнить поля обязательных атрибутов на форме</li>
     * <li>Нажать на кнопку "Сохранить" в диалоговом окне</li>
     * <li>Проверить, что значением атрибута attrTreeWithFolder является только что добавленный объект</li>
     * <br>
     * <li>Навести указатель на поле выбора значения атрибута attrNboTree</li>
     * <li>Нажать на появившуюся над полем выбора кнопку-ссылку "Добавить"</li>
     * <li>Заполнить поля обязательных атрибутов на форме</li>
     * <li>Нажать на кнопку "Сохранить" в диалоговом окне</li>
     * <li>Проверить, что значением атрибута attrNboTree является только что добавленный объект</li>
     * </ol>
     */
    @Test
    public void testAddObjectWithFiltrationAndDifferentEditPresentation()
    {
        // Подготовка
        String attr1Code = ModelUtils.createCode();
        ScriptInfo filterScript = DAOScriptInfo.createNewScriptInfo(String.format(FILTER_SCRIPT, attr1Code, attr1Code));
        DSLScriptInfo.addScript(filterScript);

        Attribute attrListWithFolder = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        attrListWithFolder.setQuickAddForm(quickForm.getUuid());
        attrListWithFolder.setFilteredByScript(Boolean.TRUE.toString());
        attrListWithFolder.setScriptForFiltration(filterScript.getCode());
        attrListWithFolder.setEditPresentation(AttributeConstant.ObjectType.EDIT_LIST_WITH_FOLDERS);

        Attribute attrTreeWithFolder = DAOAttribute.createObjectLink(ownerClass, userClass, null);
        attrTreeWithFolder.setQuickAddForm(quickForm.getUuid());
        attrTreeWithFolder.setFilteredByScript(Boolean.TRUE.toString());
        attrTreeWithFolder.setScriptForFiltration(filterScript.getCode());
        attrTreeWithFolder.setEditPresentation(AttributeConstant.ObjectType.WITH_FOLDER);

        Attribute attrNboTree = DAOAttribute.createBoLinks(ownerClass, userClass);
        attrNboTree.setEditPresentation(BOLinksType.EDIT); //FAST_SELECTION_FIELD);
        attrNboTree.setQuickAddForm(quickForm.getUuid());
        attrNboTree.setFilteredByScript(Boolean.TRUE.toString());
        attrNboTree.setScriptForFiltration(filterScript.getCode());

        DSLMetainfo.add(attrListWithFolder, attrTreeWithFolder, attrNboTree);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ownerClass), new Attribute[] { attrListWithFolder,
                        attrTreeWithFolder, attrNboTree },
                new Attribute[0]);
        // Выполнение действий и проверки
        GUILogon.asTester();
        Bo ownerBo = DAOUserBo.create(ownerCase);
        GUIBo.goToAddForm(ownerClass.getFqn(), ownerCase.getFqn());
        GUIBo.fillUserMainFields(ownerBo);

        // Проверим список выбора с папками
        GUIForm.clickQuickAddForm(attrListWithFolder);
        Bo relBoForListWithFolder = DAOUserBo.create(userCase2);
        GUIBo.selectCase(userCase1);

        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relBoForListWithFolder.getTitle(), "title");

        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, attrListWithFolder.getCode()),
                relBoForListWithFolder.getTitle());

        // Проверим дерево выбора c папками
        GUIForm.clickQuickAddForm(attrTreeWithFolder);
        Bo relBoForTreeWithFolder = DAOUserBo.create(userCase2);
        GUIBo.selectCase(userCase1);

        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relBoForTreeWithFolder.getTitle(), "title");

        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, attrTreeWithFolder.getCode()),
                relBoForTreeWithFolder.getTitle());

        // Проверим дерево выбора в НБО атрибуте
        GUIForm.clickQuickAddForm(attrNboTree);
        Bo relBoForTree = DAOUserBo.create(userCase2);
        GUIBo.selectCase(userCase1);

        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, relBoForTree.getTitle(), "title");

        GUIForm.clickApplyTitledDialog("Добавление объекта");
        GUIForm.assertDialogDisappear("Форма быстрого добавления не закрылась.");

        GUIMultiSelect.assertSelectedSize(String.format(GUISelect.SELECT_INPUT, attrNboTree.getCode()), 1);
        GUIMultiSelect.assertValuesPresentOnForm(attrNboTree.getCode(), relBoForTree.getTitle());
    }

}
