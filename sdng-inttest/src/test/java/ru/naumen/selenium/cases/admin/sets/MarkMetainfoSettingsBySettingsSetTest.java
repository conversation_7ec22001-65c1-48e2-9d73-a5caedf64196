package ru.naumen.selenium.cases.admin.sets;

import static ru.naumen.selenium.casesutil.attr.GUIAttribute.InfoFormXPath.X_ATTR_SETTINGS_SET_CAPTION;
import static ru.naumen.selenium.casesutil.attr.GUIAttribute.InfoFormXPath.X_ATTR_SETTINGS_SET_VALUE;

import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalationLevel;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUITransition;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIProfileForm;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.log.Constants.CategoryCode;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.role.GUISecurityRoleList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.secgroup.GUISecurityGroupList;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.tag.GUITag;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.timer.GUITimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.Transition;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тестирование разметки настроек метаинформации комплектами в интерфесе администратора
 * <AUTHOR>
 * @since 25.10.2023
 */
public class MarkMetainfoSettingsBySettingsSetTest extends AbstractTestCase
{
    private static final String X_FORM_SCRIPT_VALUE = String.format(GUIXpath.Any.ANY_VALUE, "edit-script");
    private static SettingsSet set1, set2;
    private static MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        set1 = DAOSettingsSet.createSettingsSet();
        set1.setTitle(ModelUtils.createTitle());
        set2 = DAOSettingsSet.createSettingsSet();
        set2.setTitle(ModelUtils.createTitle());
        DSLSettingsSet.add(set1, set2);

        userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);
    }

    /**
     * Тестирование разметки метакласса комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Нажать кнопку добавления метакласса, заполнить произвольными значениями поля: Наименование, Код,
     * Описание</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан класс userClass</li>
     * <li>Перейти на карточку созданного класса userClass</li>
     * <li>Проверить, что заполнено поле "Комплект" значением set1</li>
     * <li>Открыть форму редактирования класса userClass</li>
     * <li>Проверить, что в выпадающем списке на форме в поле "Комплект" выбрано значение set1</li>
     * <li>В поле "Комплект" выбрать другое значение set2</li>
     * <li>Нажать "Сохранить" на форме редактирования класса</li>
     * <li>На карточке класса userClass проверить, что поле "Комплект" изменилось на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMetaClassWithSet()
    {
        // Выполнение действий и проверки
        GUILogon.asSuper();
        MetaClass userClass1 = DAOUserClass.create();
        userClass1.setSettingsSet(set1);
        GUIMetaClass.addMetaClassWithSettingsSetFill(userClass1);
        GUIMetaClass.goToCard(userClass1);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMetaClass.openEditForm(userClass1);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки атрибута комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку созданного класса userClass</li>
     * <li>Нажать кнопку добавления атрибута, выбрать тип строка, заполнить поля: Наименование, Код</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан атрибут stringAttr</li>
     * <li>Открыть модальную форму просмотра информации об атрибуте stringAttr, кликнув по строке</li>
     * <li>Проверить, что заполнено поле "Комплект" значением set1</li>
     * <li>Закрыть модальную форму просмотра информации об атрибуте</li>
     * <li>Открыть форму редактирования атрибута stringAttr</li>
     * <li>Проверить, что в выпадающем списке на форме в поле "Комплект" выбрано значение set1</li>
     * <li>В поле "Комплект" выбрать другое значение set2</li>
     * <li>Нажать "Сохранить" на форме редактирования атрибута</li>
     * <li>Открыть модальную форму просмотра информации об атрибуте stringAttr, кликнув по строке</li>
     * <li>На карточке атрибута stringAttr проверить, что поле "Комплект" изменилось на set2</li>
     * </ol>
     */
    @Test
    public void testMarkAttributeWithSet()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass);
        GUIAttribute.clickAdd();
        GUIAttribute.fillBaseFieldsOnAddForm(stringAttr);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        stringAttr.setExists(true);

        GUIAttribute.clickShowAttributeInfo(stringAttr);
        GUIForm.assertFormAppear(Any.ATTRIBUTE_INFO_MODAL_FORM_CAPTION);
        GUITester.assertTextContains(X_ATTR_SETTINGS_SET_CAPTION, "Комплект");
        GUITester.assertTextContains(X_ATTR_SETTINGS_SET_VALUE, set1.getTitle());
        GUIForm.closeDialog();

        GUIAttribute.clickEdit(stringAttr);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        GUIAttribute.clickShowAttributeInfo(stringAttr);
        GUIForm.assertFormAppear(Any.ATTRIBUTE_INFO_MODAL_FORM_CAPTION);
        GUITester.assertTextContains(X_ATTR_SETTINGS_SET_CAPTION, "Комплект");
        GUITester.assertTextContains(X_ATTR_SETTINGS_SET_VALUE, set2.getTitle());
    }

    /**
     * Тестирование разметки скрипта в атрибуте комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку созданного класса userClass</li>
     * <li>Нажать кнопку добавления атрибута, выбрать тип "Строка", заполнить поля: Наименование, Код</li>
     * <li>Установить опцию по умолчанию вычислимое и ввести данные для нового скрипта scriptInfo:
     * <pre>return '%произвольная строка%'</pre></li>
     * <li>У скрипта заполнить поле "Комплект" значением set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан атрибут stringAttr</li>
     * <li>Открыть форму редактирования атрибута, развернуть свойства скрипта вычисления значения по умолчанию
     * scriptInfo</li>
     * <li>Проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>В поле "Комплект" выбрать set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Открыть форму редактирования атрибута, развернуть свойства скрипта вычисления значения по умолчанию
     * scriptInfo</li>
     * <li>Проверить, что поле "Комплект" заполнено значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkScriptInAttributeWithSet()
    {
        //Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        String randomString = RandomUtils.randomUUID();
        scriptInfo.setBody(String.format("return '%s'", randomString));
        scriptInfo.setSettingsSet(set1);

        Attribute stringAttr = DAOAttribute.createString(userClass);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass);
        GUIAttribute.clickAdd();
        GUIAttribute.fillBaseFieldsOnAddForm(stringAttr);

        GUIAttribute.setDefaultByScriptWithSettingsSet(true, scriptInfo);
        GUIForm.applyForm();
        scriptInfo.setExists(true);
        stringAttr.setExists(true);

        GUIAttribute.clickEdit(stringAttr);
        GUIScriptComponentEdit componentEdit = new GUIScriptComponentEdit(Any.SCRIPT_FOR_DEFAULT_VALUE);
        componentEdit.selectScriptByName(scriptInfo);
        componentEdit.showProperties();

        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        componentEdit.fillSettingsSetForScript(set2.getCode());
        GUIForm.applyForm();

        GUIAttribute.clickEdit(stringAttr);
        componentEdit.selectScriptByName(scriptInfo);
        componentEdit.showProperties();
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
    }

    /**
     * Тестирование разметки групп атрибутов комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать строковый атрибут stringAttr в классе userClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку созданного класса userClass вкладка "Группы атрибутов"</li>
     * <li>Нажать кнопку добавления группы атрибутов, заполнить поля: Наименование, Код</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана группа атрибутов attrGroup</li>
     * <li>Открыть форму редактирования группы атриутов attrGroup</li>
     * <li>Проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>В поле "Комплект" выбрать другое значение set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Открыть форму редактирования группы атрибутов attrGroup</li>
     * <li>Проверить, что поле "Комплект" заполнено значением set2</li>
     * </ol>
     */
    @Test
    public void testMarkAttributeGroupWithSet()
    {
        //Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass.getFqn());
        DSLMetainfo.add(stringAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass.getFqn());

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIGroupAttr.goToGroup(attrGroup);
        GUIGroupAttr.clickAdd();
        GUIGroupAttr.setTitle(attrGroup.getTitle());
        GUIForm.fillAttribute(GUIGroupAttr.CODE_ATTRIBUTE, attrGroup.getCode());
        GUIGroupAttr.addAttributesOnEditform(stringAttr);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        GUIGroupAttr.openEditForm(attrGroup);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();

        GUIGroupAttr.openEditForm(attrGroup);
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
    }

    /**
     * Тестирование разметки статуса комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку системного статуса закрыт closedState для класса userClass</li>
     * <li>Нажать кнопку "Редактировать" на карточке статуса closedState</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке статуса closedState выбран комплект set1</li>
     * </ol>
     */
    @Test
    public void testMarkStateWithSet()
    {
        //Подготовка
        BoStatus closedState = DAOBoStatus.createClosed(userClass.getFqn());

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(closedState);
        GUIBoStatus.edit();
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
    }

    /**
     * Тестирование разметки перехода из статуса в статус комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать переход registeredClosedTransition из статуса "Зарегистрирован" в "Закрыт"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку перехода registeredClosedTransition для класса
     * userClass</li>
     * <li>Нажать кнопку "Редактировать" на карточке перехода registeredClosedTransition</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке перехода registeredClosedTransition выбран комплект set1</li>
     * </ol>
     */
    @Test
    public void testMarkTransitionWithSet()
    {
        //Подготовка
        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(userClass.getFqn());
        Transition registeredClosedTransition = DAOTransition.createTransition(userClass, registered, closed, "", false,
                false);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUITransition.goToCard(registeredClosedTransition);
        GUITransition.clickEditSettings();
        GUITransition.setTitleForTransition(ModelUtils.createTitle());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
    }

    /**
     * Тестирование разметки контента комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку метакласса userClass</li>
     * <li>Нажать кнопку "Добавить контент" и заполнить поля: Наименование, Код, Группа атрибутов, тип контента -
     * параметры объекта</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан контент propertyList</li>
     * <li>Нажать на кнопку "Редактировать" у контента propertyList</li>
     * <li>Проверить, что заполнено поле "Комплект" значением set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать на кнопку "Редактировать" у контента propertyList</li>
     * <li>Проверить, что в выпадающем списке на форме в поле "Комплект" выбрано значение set2</li>
     * </ol>
     */
    @Test
    public void testMarkContentWithSet()
    {
        //Подготовка
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass);
        Set<String> oldIdSet = DSLContent.searchContentsByType(propertyList);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickAdd();
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE, ContentType.PROPERTY_LIST.getType());
        tester.sendKeys(Any.CAPTION_VALUE, propertyList.getTitle());
        tester.sendKeys(Any.CODE_VALUE, propertyList.getCode());
        GUISelect.select(Any.ATTRIBUTE_GROUP_VALUE, "system");
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        propertyList.setExists(true);
        GUIContent.setId(oldIdSet, propertyList);

        GUIContent.clickEditContent(propertyList);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        GUIContent.clickEditContent(propertyList);
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
    }

    /**
     * Тестирование разметки панели вкладок и вкладок на ней комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку метакласса userClass</li>
     * <li>Нажать кнопку "Добавить контент" и заполнить поля: Наименование, Код, Группа атрибутов, тип контента -
     * панель вкладок</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан контент tabBar</li>
     * <li>Нажать на кнопку "Редактировать" у контента tabBar</li>
     * <li>Проверить, что заполнено поле "Комплект" значением set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать на кнопку "Редактировать" у контента tabBar</li>
     * <li>Проверить, что в выпадающем списке на форме в поле "Комплект" выбрано значение set2</li>
     * <li>Нажать нопку "Отмена"</li>
     * <li>Нажать кнопку "Редактировать вкладки" у контента панель вкладок tabBar</li>
     * <li>Нажать кнопку "Добавить вкладку" и заполнить наименование вкладки, в поле комплект выбрать set1</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" напротив только что созданной вкладки (вкладка с индексом 2)</li>
     * <li>Проверить, что поле "Комплект" заполнено комплектом set1</li>
     * <li>В поле "Комплект" выбрать set2</li>
     * <li>Нажать кнопку "Редактировать" напротив только что созданной вкладки (вкладка с индексом 2)</li>
     * <li>Проверить, что поле "Комплект" заполнено комплектом set2</li>
     * </ol>
     */
    @Test
    public void testMarkTabPanelWithSet()
    {
        //Подготовка
        ContentForm tabBar = DAOContentCard.createTabBar(userClass.getFqn());
        Set<String> oldIdSet = DSLContent.searchContentsByType(tabBar);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickAdd();
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE, ContentType.TAB_BAR.getType());
        tester.sendKeys(Any.CAPTION_VALUE, tabBar.getTitle());
        tester.sendKeys(Any.CODE_VALUE, tabBar.getCode());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        tabBar.setExists(true);
        GUIContent.setId(oldIdSet, tabBar);

        GUIContent.clickEditContent(tabBar);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        GUIContent.clickEditContent(tabBar);
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
        GUIForm.cancelDialog();

        GUIContent.clickEditTabs(tabBar);
        GUITab.clickAdd();
        String tabTitle = ModelUtils.createTitle();
        GUITab.setTabTitle(tabTitle);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyLastModalForm();

        GUITab.clickEditByIndex(2);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyLastModalForm();

        GUITab.clickEditByIndex(2);
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
    }

    /**
     * Тестирование разметки пользовательской кнопки на панели инструментов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточке класса userClass создать контент типа "Параметры объекта" propertyList</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку метакласса userClass</li>
     * <li>У контента propertyList нажать кнопку "Перейти к настройке панели действий"</li>
     * <li>Снять галку "Использовать системную логику формирования панели действий"</li>
     * <li>Кликнуть правой кнопкой мыши на "Новая кнопка" - "Добавить на панель действий"</li>
     * <li>Заполнить наименование кнопки buttonTitle и выбрать действие для кнопки: системной дейстие -
     * редактировать</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать правой кнопкой мыши на кнопке buttonTitle - Редактировать</li>
     * <li>Проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать правой кнопкой мыши на кнопке buttonTitle - Редактировать</li>
     * <li>Проверить, что поле "Комплект" заполнено комплектом set2</li>
     * <li>Нажать на кнопку "Сохранить" на форме редактирования элемента/li>
     * <li>Нажать на кнопку "Сохранить" на форме редактирования настроек панели инструментов/li>
     * </ol>
     */
    @Test
    public void testMarkToolslWithSet()
    {
        //Подготовка
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass);
        DSLContent.add(propertyList);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);

        GUIContent.clickEditToolPanel(propertyList);
        GUIAdvListEditableToolPanel panel = propertyList.advlist().editableToolPanel();
        panel.setUseSystemSettings(false);
        panel.rightClickTool(GUIButtonBar.BTN_NEW_TEMPLATE);
        panel.clickAddContextMenuOption();

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        String buttonTitle = ModelUtils.createTitle();
        panel.setTitleOnForm(buttonTitle);
        panel.selectSystemAction("редактировать");
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        panel.clickApplyOnDialogByTitle("Добавление элемента");

        panel.rightClickToolByTitle(buttonTitle);
        panel.clickEditContextMenuOption();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        panel.clickApplyOnDialogByTitle("Редактирование элемента");

        panel.rightClickToolByTitle(buttonTitle);
        panel.clickEditContextMenuOption();
        GUISettingsSet.assertSettingsSetOnForm(set2);
        panel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
    }

    /**
     * Тестирование разметки групп пользователей комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Группы пользователей и роли"</li>
     * <li>Нажать кнопку "Добавить группу"</li>
     * <li>Заполнить поля: Наименование и Код</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана группа secGroup</li>
     * <li>Перейти на карточку созданной группы secGroup, проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>Нажать кнопку "Редактировать"</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>На карточке группы пользователей проверить, что поле "Комплект" заполнено значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена группа пользователей, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkSecureGroupWithSet()
    {
        //Подготовка
        SecurityGroup secGroup = DAOSecurityGroup.create();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.clickAddFrom();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUISecurityGroupList.fillAttrTitle(secGroup.getTitle());
        GUISecurityGroupList.fillAttrCode(secGroup.getCode());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        secGroup.setExists(true);

        GUISecurityGroupList.goToCard(secGroup);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());

        GUISecurityGroupList.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.SEC_GROUP_AND_ROLES);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена группа пользователей '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                secGroup.getTitle(), secGroup.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);

    }

    /**
     * Тестирование разметки ролей комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Группы пользователей и роли"</li>
     * <li>Нажать кнопку "Добавить роль"</li>
     * <li>Заполнить поля: Наименование и Код</li>
     * <li>Заполнить поле "Скрипт определения прав доступа" значением из скрипта accessScriptInfo</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана роль role</li>
     * <li>Перейти на карточку созданной роли role, проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>Нажать кнопку "Редактировать"</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>На карточке роли проверить, что поле "Комплект" заполнено значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена роль, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkSecureRoleWithSet()
    {
        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.goToRolesTab();
        GUISecurityRoleList.clickAddRole();

        ScriptInfo accessScriptInfo = DSLSecurityRole.createDefaultAccessScriptInfo();

        SecurityRole role = DAOSecurityRole.create(DAOScCase.createClass());

        GUISecurityRoleList.setTitle(role.getTitle());
        GUISelect.selectByXpath(String.format(GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_INPUT, "metaClass"),
                String.format(GUIXpath.Any.ID_PATTERN, SystemClass.SERVICECALL.getCode()));

        GUISecurityRoleList.clickShowAccessScript();
        GUISecurityRoleList.setExistingAccessScript(accessScriptInfo);
        GUISecurityRoleList.accessScriptComponentEdit().closeProperties();

        GUISecurityRoleList.setCode(role.getCode());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());

        GUIForm.applyModalForm();
        role.setExists(true);

        GUISecurityRoleList.goToCard(role);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());

        GUISecurityRoleList.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.SEC_GROUP_AND_ROLES);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена роль '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                role.getTitle(), role.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки меток комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Метки"</li>
     * <li>Нажать кнопку "Добавить метку"</li>
     * <li>Заполнить поля: Наименование, Код, Описание</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана метка newTag</li>
     * <li>Перейти на карточку созданной метки newTag, проверить, что поле "Комплект" заполнено значением set1</li>
     * <li>Нажать кнопку "Редактировать"</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>На карточке метки проверить, что поле "Комплект" заполнено значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена метка, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkTagWithSet()
    {
        // Выполнение действий и проверки
        Tag newTag = DAOTag.createTag();
        GUILogon.asSuper();
        GUINavigational.goToTags();
        GUITag.advlist().toolPanel().clickAdd();
        GUITag.fillAddForm(newTag);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        newTag.setExists(true);
        GUITag.goToTagCard(newTag.getCode());
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUITag.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUITag.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set2);

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.TAGS_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена метка '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                newTag.getTitle(), newTag.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки профилей прав доступа комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку метакласса userClass, вкладка "Права доступа"</li>
     * <li>Нажать кнопку "Добавить"</li>
     * <li>Заполнить поля: Наименование, Код. Выбрать роль "Сотрудник"</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан профиль profile</li>
     * <li>Нажать кнопку "Редактировать" у профиля profile</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен профиль прав доступа, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkSecureProfileWithSet()
    {
        //Выполнение действий и проверки
        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.pressButtonAddProfile();
        GUIProfileForm.setTitle(profile.getTitle());
        GUIProfileForm.setCode(profile.getCode());
        GUIProfileForm.setRole(true, SysRole.employee());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        Cleaner.afterTest(() ->
        {
            if (DSLSecurityProfile.isPresentByTitle(profile))
            {
                DSLSecurityProfile.delete(profile);
            }
        });
        GUIAccessMatrix.callEditForm(profile);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.SEC_PROFILE_EDIT);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен профиль прав доступа '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                profile.getTitle(), profile.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки счетчиков времени комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип scCase в системном классе "Запрос" scClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Создать модель счетчика времени userCounter (Метрика времени:"Астрономическое время";Тип условия:"По смене
     * статуса"; Время считается в статусе "Зарегистрирован")</li>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Счетчики времени"</li>
     * <li>Нажать кнопку "Добавить"</li>
     * <li>Заполнить необходимые поля счетчика из модели userCounter</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан счетчик времени userCounter</li>
     * <li>Перейти на карточку счетчика времени userCounter</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у счетчика userCounter</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" у счетчика userCounter</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены параметры счетчика, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkTimerDefenitionWithSet()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed();
        TimerDefinition userCounter = DAOTimerDefinition.createAstroTimerByStatus(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode(), registered);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToTimers();
        GUITimerDefinition.clickAdd();
        GUITimerDefinition.fillTitle(userCounter.getTitle());
        GUITimerDefinition.fillCode(userCounter.getCode());
        GUITimerDefinition.fillDescription(userCounter.getDescription());
        GUITimerDefinition.selectTargetTypes(DAOUserClass.createAbstractBo(), scClass, scCase);
        GUITimerDefinition.selectTimeMetric(userCounter.getTimeMetric());
        GUITimerDefinition.selectConditionType(userCounter);
        GUITimerDefinition.selectSelectedState(registered, scCase);
        GUITimerDefinition.selectSelectedStopedState(closed, scCase);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        userCounter.setExists(true);

        GUITimerDefinition.goToCardWithRefresh(userCounter);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();
        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetOnForm(set2);

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.TIMER_DEFINITION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменены параметры счетчика '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                userCounter.getTitle(), userCounter.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки ДПС комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Нажать кнопку "Добавить"</li>
     * <li>Заполнить необходимые поля ДПС из модели eventAction</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано ДПС eventAction</li>
     * <li>Перейти на карточку ДПС eventAction</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у ДПС eventAction</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" у ДПС eventAction</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены параметры ДПС, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkUserAventActionWithSet()
    {
        // Выполнение действий и проверка
        GUILogon.asSuper();
        GUINavigational.goToEventActions();
        GUIEventActionList.addAction();
        EventAction eventAction = DAOEventAction.createPush(DAOOuCase.createClass(), EventType.addComment,
                SharedFixture.employee());
        GUIEventAction.fillEventAction(eventAction);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();
        eventAction.setExists(true);

        GUIEventAction.goToCardWithRefresh(eventAction);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIEventAction.editFromCard();

        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        GUIEventAction.editFromCard();
        GUISettingsSet.assertSettingsSetPropOnForm(set2);

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.EVENT_ACTION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменено действие по событию '%s' "
                                                   + "('%s') типа 'Уведомление в интерфейсе':\n"
                                                   + "Комплект: '%s' -> '%s' (локаль: \"ru\").",
                eventAction.getTitle(), eventAction.getUuid(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки условий ДПС комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <li>Создать скрипт scriptActionCondition
     * <pre>
     *     return true;
     * </pre>
     * </li>
     * <li>Создать ДПС eventAction</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку ДПС eventAction</li>
     * <li>Нажать кнопку "Добавить условие"</li>
     * <li>Заполнить необходимые поля: Наименование conditionTitle и скрипт scriptActionCondition</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано условие conditionTitle</li>
     * <li>Перейти на карточку условия conditionTitle</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у условия conditionTitle</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" у условия conditionTitle</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменены условия для ДПС, в нем
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkEventActionConditionsWithSet()
    {
        //Подготовка
        ScriptInfo scriptActionCondition = DAOScriptInfo.createNewScriptInfo("return true;");
        DSLScriptInfo.addScript(scriptActionCondition);

        EventAction eventAction = DAOEventAction.createPush(DAOOuCase.createClass(), EventType.addComment,
                SharedFixture.employee());
        DSLEventAction.add(eventAction);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToList();
        GUIEventActionList.clickEventActionRow(eventAction);
        String conditionTitle = ModelUtils.createTitle();
        GUIEventAction.clickAddCondition();
        GUIEventAction.fillConditionForm(conditionTitle, scriptActionCondition);
        GUIScriptComponentEdit scriptComponentEdit = new GUIScriptComponentEdit(X_FORM_SCRIPT_VALUE);
        scriptComponentEdit.closeProperties();
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        GUIEventAction.clickRowCondition(conditionTitle);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());

        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();

        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetPropOnForm(set2);

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.EVENT_ACTION_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменено условие '%s' действия по событию "
                                                   + "'%s' ('%s'):\n"
                                                   + "Комплект: '%s' -> '%s'.", conditionTitle,
                eventAction.getTitle(), eventAction.getUuid(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);

    }

    /**
     * Тестирование разметки схем эскалации комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс с жизненным циклом userClass1</li>
     * <li>Создать счетчик времени timerDefinition</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Схемы эскалации"</li>
     * <li>Нажать кнопку "Добавить схему"</li>
     * <li>Заполнить необходимые поля на форме добавления схемы эскалации</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана схема эскалации scheme</li>
     * <li>Перейти на карточку схемы эскалации scheme</li>
     * <li>Проверить, что на карточке заполнен комплект значением set1</li>
     * <li>Нажать кнопку "Редактировать" у схемы scheme</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" у схеммы scheme</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена схема эскалации, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkEscalationSchemeWithSet()
    {
        //Подготовка
        MetaClass userClass1 = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass1);

        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(userClass1.getFqn(),
                SysAttribute.timeZone(userClass1).getCode(), SysAttribute.serviceTime(userClass1).getCode(),
                SysAttribute.resolutionTime(userClass1).getCode(), DAOBoStatus.createRegistered(userClass1.getFqn()));
        DSLTimerDefinition.add(timerDefinition);

        EscalationScheme scheme = DAOEscalationSheme.create(timerDefinition, false, userClass1);
        //Это добавление схемы эскалации scheme1 костыль, чтобы тестовая система создала модуль ModuleEscalation и потом
        // корректно проходила очистка теста
        EscalationScheme scheme1 = DAOEscalationSheme.create(timerDefinition, false, userClass1);
        DSLEscalation.add(scheme1);

        // Выполнение действия
        GUILogon.asSuper();
        GUIEscalation.goToSchemes();
        GUIEscalation.clickAdd();
        GUIEscalation.setTitle(scheme.getTitle());
        GUIEscalation.setDescription(scheme.getDescription());
        GUIEscalation.setObjects(userClass1.getCode());
        GUIEscalation.setTimer(scheme.getTimerDefinitionCode());
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIEscalation.applyAddForm(scheme);

        GUIEscalation.goToSchemeWithRefresh(scheme);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());

        GUIEscalation.clickEditButton();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();
        GUIEscalation.clickEditButton();
        GUISettingsSet.assertSettingsSetOnForm(set2);
        GUIForm.clickCancel();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.ESCALATION_SCHEME_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена схема эскалации '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                scheme.getTitle(), scheme.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки уровней эскалации комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass1</li>
     * <li>Создать счетчик времени timerDefinition</li>
     * <li>Содать схему эскалации scheme для класса userClass1 используется счетчик времени timerDefinition</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку схемы эскалации scheme</li>
     * <li>Нажать кнопку "Добавить уровень"</li>
     * <li>Заполнить необходимые поля на форме добавления уровня</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан уровень 1</li>
     * <li>Нажать кнопку "Редактировать" у уровня 1</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * </ol>
     */
    @Test
    public void testMarkEscalationSchemeLevelWithSet()
    {
        //Подготовка
        MetaClass userClass1 = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass1);

        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(userClass1.getFqn(),
                SysAttribute.timeZone(userClass1).getCode(), SysAttribute.serviceTime(userClass1).getCode(),
                SysAttribute.resolutionTime(userClass1).getCode(), DAOBoStatus.createRegistered(userClass1.getFqn()));
        DSLTimerDefinition.add(timerDefinition);

        EscalationScheme scheme = DAOEscalationSheme.create(timerDefinition, false, userClass1);
        DSLEscalation.add(scheme);

        // Выполнение действия
        GUILogon.asSuper();
        GUIEscalation.goToSchemeWithRefresh(scheme);

        GUIEscalationLevel.clickAddLevelButton();
        GUIEscalationLevel.setPercentConditionOnForm();
        GUIEscalationLevel.setPercentValueOnForm("50");
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyModalForm();

        GUIEscalationLevel.clickEditLevel(1);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUIForm.cancelDialog();
    }
}
