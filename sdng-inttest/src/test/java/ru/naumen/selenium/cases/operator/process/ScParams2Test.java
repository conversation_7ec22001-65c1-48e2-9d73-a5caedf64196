package ru.naumen.selenium.cases.operator.process;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.OrderScFields;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.GUISelectCase;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на параметры запроса по умолчанию в интерфейсе оператора
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00284
 * <AUTHOR>
 *
 */
public class ScParams2Test extends AbstractTestCase
{
    private static MetaClass scCase1, scCase2, scCase3;
    private static Bo agreement, service, employee;

    /**
     * Общая подготовка
     * <br>
     * <ul>
     * <li>Создать типы запроса scCase1, scCase2, scCase3</li>
     * <li>Создать соглашение agreement и связанную с ним услугу service</li>
     * <li>Добавить связь услуги service с типами запроса scCase1, scCase2, scCase3</li>
     * <li>Создать сотрудника employee со всеми правами</li>
     * <li>Связать соглашение с сотрудником</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        scCase1 = DAOScCase.create();
        scCase2 = DAOScCase.create();
        scCase3 = DAOScCase.create();
        DSLMetainfo.add(scCase1, scCase2, scCase3);

        agreement = DAOAgreement.create(SharedFixture.agreementCase());
        service = DAOService.create(SharedFixture.slmCase());
        agreement.setAgrResolutionTimeRuleUuid(SharedFixture.rsResolutionTime().getUuid());
        agreement.setAgrPriorityRuleUuid(SharedFixture.rsPriority().getUuid());
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(agreement, service, employee);

        DSLAgreement.addToRecipients(agreement, employee);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase1, scCase2, scCase3);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос, 
     * если результат зависит от других атрибутов на форме добавления запроса и выбранной услуги.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * sd-jira.naumen.ru/browse/NSDPRD-5694
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Запрос создать атрибут bool типа Логический, значение по умолчанию = нет.</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = ['bool']
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          if(subject.bool == false)
     *          {
     *              return ['serviceCall$scCase1', 'serviceCall$scCase2', 'serviceCall$scCase3']
     *          }
     *           return ['serviceCall$scCase2']
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать кнопку "Добавить запрос"</li>
     * <li>Проверки: На форме атрибут bool = false, для выбора в списке типов доступны типы scCase1, scCase2,
     * scCase3</li>
     * <li>Изменить значение bool = true</li>
     * <li>Проверки: На форме атрибут bool = true, для выбора в списке типов доступен только scCase2</li>
     * <li>Выбрать услугу service</li>
     * <li>Проверки: На форме атрибут bool = true, для выбора в списке типов доступен только scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInDependencyOfService()
    {
        //Подготовка
        Attribute bool = DAOAttribute.createBool(SystemClass.SERVICECALL.getCode());
        bool.setDefaultValue(Boolean.FALSE.toString());
        bool.setEditPresentation(BooleanType.EDIT_CHECKBOX);
        DSLAttribute.add(bool);

        DSLGroupAttr.edit(DAOGroupAttr.createSystem(DAOScCase.createClass()), new Attribute[] { bool },
                new Attribute[] {});

        String scriptPattern = """
                def ATTRS_FOR_UPDATE_ON_FORMS = ['%s']
                if (subject == null) { return ATTRS_FOR_UPDATE_ON_FORMS }
                if (subject.%s == false) { return ['%s', '%s', '%s'] }
                return ['%s'];""";
        String scriptBody = String.format(scriptPattern, bool.getCode(), bool.getCode(), scCase1.getFqn(),
                scCase2.getFqn(), scCase3.getFqn(), scCase2.getFqn());
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUITester.assertCheckboxState(GUIXpath.Any.ANY_VALUE_INPUT_CONTAINS, false, bool.getCode());
        GUISc.assertScCasesPresent(scCase1, scCase2, scCase3);
        tester.setCheckbox(GUIXpath.Any.ANY_VALUE_INPUT_CONTAINS, true, bool.getCode());
        GUISelectCase.assertSelect(List.of(scCase2.getTitle()), false, true, true);
        GUISc.selectScCase(scCase2);
        GUISc.selectAgrServiceField(service.getTitle());
        GUISelectCase.assertSelect(List.of(scCase2.getTitle()), false, true, true);
    }

    /**
     * Тестирование использования текущей выбранной услуги на ФДоб запроса в скрипте 
     * фильтрации типов запросов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64557030
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В параметрах запроса установить скрипт фильтрации типов при редактировании:
     * <pre>
     * ---------------------------------------------------------------------
     * if(!subject)
     * {
     *      return ['$str1'];
     * }
     * if(subject?.$str1)
     * {
     *      return ['$str2'];
     * }
     * return ['$str2', '$str3', '$str4'];
     * ---------------------------------------------------------------------
     * где $str1 - код системного атрибута "Услуга" класса "Запрос";
     *     $str2 - fqn типа scCase1
     *     $str3 - fqn типа scCase2
     *     $str4 - fqn типа scCase3
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Нажать на кнопку "Добавить запрос" на карточке сотрудника</li>
     * <li>На форме добавления запроса выбрать тип scCase1</li>
     * <li>В поле "Соглашение/услуга" выбрать соглашение agreement</li>
     * <li>Проверить, что для выбора в списке типов доступны типы запросов scCase1, scCase2, scCase3</li>
     * <li>В поле "Соглашение/услуга" выбрать услугу service</li>
     * <li>Проверить, что для выбора в списке типов отсутствуют типы запросов scCase2, scCase3</li>
     * </ol>
     */
    @Test
    public void testScParamCasesScriptFilterServiceValue()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        String scriptPattern = """
                if (!subject) { return ['%s'] }
                if (subject?.%s) { return ['%s'] }
                return ['%s','%s','%s'];""";
        Attribute serviceAttr = SysAttribute.service(scCase);
        String scriptBody = String.format(scriptPattern, serviceAttr.getCode(), serviceAttr.getCode(), scCase1.getFqn(),
                scCase1.getFqn(), scCase2.getFqn(), scCase3.getFqn());
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);
        DSLScParams.setOrderingSettings(OrderScFields.CASE);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIButtonBar.addSC();
        GUISc.selectScCase(scCase1);
        GUISc.selectAgrServiceField(agreement.getTitle());
        GUISc.assertScCasesPresent(scCase1, scCase2, scCase3);
        GUISc.selectAgrServiceField(service.getTitle());
        GUISc.assertScCasesAbsent(scCase2, scCase3);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос,
     * при изменении значения зависимого атрибута на форме добавления запроса,
     * если настроена опция выбирать сначала Соглашение/услугу<br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235701405 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094 <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Запрос создать атрибут strAttr типа Строка, значение по умолчанию 'default'</li>
     * <li>Добавить атрибут strAttr в системную группу атрибутов</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = ['strAttr']
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          if (!subject.strAttr) // если атрибут не заполнен
     *          {
     *              return api.filtration.disableFiltration()
     *          }
     *          if (subject.strAttr == 'test')
     *          {
     *              return ['serviceCall$scCase1']
     *          }
     *          return ['serviceCall$scCase2']
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Перейти на форму добавления запроса</li>
     * <li>Выбрать соглашение agreement</li>
     * <li>Проверить, что в списке типов доступен для выбора только тип scCase2</li>
     * <li>Изменить значение strAttr на 'test'</li>
     * <li>Проверить, что в списке типов доступен для выбора только тип scCase1</li>
     * <li>Убрать значение strAttr</li>
     * <li>Проверить, что в списке типов доступны для выбора типы scCase1, scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInDependencyOfRelatedAttr()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();

        Attribute strAttr = DAOAttribute.createString(scClass);
        strAttr.setDefaultValue("default");
        DSLAttribute.add(strAttr);

        DSLGroupAttr.edit(DAOGroupAttr.createSystem(scClass),
                new Attribute[] { strAttr }, new Attribute[] {});

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("""
                def ATTRS_FOR_UPDATE_ON_FORMS = ['%1$s']
                if (subject == null)
                {
                    return ATTRS_FOR_UPDATE_ON_FORMS
                }
                if (!subject.%1$s) // если атрибут не заполнен
                {
                    return api.filtration.disableFiltration()
                }
                if (subject.%1$s == 'test')
                {
                    return ['%2$s']
                }
                return ['%3$s']"""
                .formatted(strAttr.getCode(), scCase1.getFqn(), scCase2.getFqn()));
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        //Действия и проверки
        GUILogon.login(employee);

        GUIButtonBar.addSC();
        GUISc.selectAgrServiceField(agreement.getTitle());
        GUISelectCase.assertSelect(List.of(scCase2.getTitle()), false, true, true);

        GUIForm.fillAttribute(strAttr, "test");
        GUISelectCase.assertSelect(List.of(scCase1.getTitle()), false, true, true);

        GUIForm.fillAttribute(strAttr, "");
        GUISelectCase.assertSelect(List.of(scCase1.getTitle(), scCase2.getTitle(), scCase3.getTitle()), false, false,
                true);
    }

    /**
     * Тестирование работы скрипта фильтрации типов класса Запрос,
     * при изменении значения зависимого атрибута на форме добавления запроса,
     * когда скрипт фильтрации изначально возвращает пустой список<br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235701405 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094 <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Запрос создать атрибут strAttr типа Строка, значение по умолчанию пустое</li>
     * <li>Добавить атрибут strAttr в системную группу атрибутов</li>
     * <li>В параметрах запроса установить Фильтрация типов при редактировании = true, скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *          def ATTRS_FOR_UPDATE_ON_FORMS = ['strAttr']
     *          if (subject == null)
     *          {
     *              return ATTRS_FOR_UPDATE_ON_FORMS
     *          }
     *          if (subject.strAttr != null) // если атрибут заполнен
     *          {
     *              return ['serviceCall$scCase1', 'serviceCall$scCase2']
     *          }
     *          return []
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Перейти на форму добавления запроса</li>
     * <li>Выбрать соглашение agreement</li>
     * <li>Проверить, что в списке типов нет доступных для выбора типов</li>
     * <li>Изменить значение strAttr на 'test'</li>
     * <li>Проверить, что в списке типов доступны для выбора только типы scCase1, scCase2</li>
     * </ol>
     */
    @Test
    public void testScParamCasesFilteringInDependencyOfRelatedAttrWithEmptyList()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();

        Attribute strAttr = DAOAttribute.createString(scClass);
        strAttr.setDefaultValue("");
        DSLAttribute.add(strAttr);

        DSLGroupAttr.edit(DAOGroupAttr.createSystem(scClass),
                new Attribute[] { strAttr }, new Attribute[] {});

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("""
                def ATTRS_FOR_UPDATE_ON_FORMS = ['%1$s']
                if (subject == null)
                {
                    return ATTRS_FOR_UPDATE_ON_FORMS
                }
                if (subject.%1$s != null) // если атрибут заполнен
                {
                    return ['%2$s', '%3$s']
                }
                return []"""
                .formatted(strAttr.getCode(), scCase1.getFqn(), scCase2.getFqn()));
        DSLScriptInfo.addScript(script);
        DSLScParams.setFilterCases(true, script);

        //Действия и проверки
        GUILogon.login(employee);

        GUIButtonBar.addSC();
        GUISc.selectAgrServiceField(agreement.getTitle());
        GUISelectCase.assertSelect(List.of(), false, true, true);

        GUIForm.fillAttribute(strAttr, "test");
        GUISelectCase.assertSelect(List.of(scCase1.getTitle(), scCase2.getTitle()), false, true, true);
    }
}
