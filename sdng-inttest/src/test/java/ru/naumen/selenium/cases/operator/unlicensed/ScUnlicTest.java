package ru.naumen.selenium.cases.operator.unlicensed;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.ScClientRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityProfile;
import ru.naumen.selenium.security.UserGroup;

/**
 * Тестирование запроса для нелицензионного пользователя
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
 * <AUTHOR>
 * @since 21.03.2013
 *
 */
public class ScUnlicTest extends AbstractTestCase
{

    /**
     * Добавление запроса под нелицензированным пользователем.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * http://sd-jira.naumen.ru/browse/NSDPRD-2369
     * http://sd-jira.naumen.ru/browse/NSDPRD-4197
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>Добавляем в класс "Запрос" тип scCase, scCase2</li>
     * <li>Устанавливаем значения по умолчанию для атрибутов Часовой пояс, Приоритет и Нормативное время обработки
     * типа scCase</li>
     * <li>Создаем группу атрибутов attrGroup в типе scCase, добавляем в нее атрибуты Контрагент (сотрудник) и
     * Контрагент (отдел)</li>
     * <li>Добавляем на карточку запроса контент Параметры объекта с группой атрибутов attrGroup</li>
     * <li>Добавляем кнопку добавления объектов класса "Запрос" в верхнее меню</li>
     * <li>Добавляем услугу service с атрибутами "Типы запросов" = scCase,scCase2</li>
     * <li>Добавляем соглашение agreement с атрибутами "Услуги" = service</li>
     * <li>Добавляем в класс "Сотрудник" тип employeeCase с параметрами запроса по умолчанию: "Соглашение" =
     * agreement, "Услуга" = service, "Тип объекта" = scCase</li>
     * <li>Добавляем в тип запроса scCase, scCase2 профиль прав доступа profile с параметрами: "Для нелицензированных
     * пользователей" = "да", "Роли пользователей" = "Контрагент запроса", "Группы пользователей" = <не выбрано></li>
     * <li>Для профиля profile назначаем права на просмотр и редактирование всех атрибутов, просмотр карточки
     * объекта, добавление запроса для клиента-отдела</li>
     * <li>В компанию добавляем отдел ou</li>
     * <li>В отдел ou добавляем сотрудника employee с атрибутами: "Тип" = employeeCase, "Лицензия" =
     * "Нелицензированный пользователь", "Соглашения" = agreement</li>
     * <br>
     * <li>Действие 1</li>
     * <li>Входим в систему от имени сотрудника employee</li>
     * <br>
     * <li>Проверка 1</li>
     * <li>В верхней части интерфейса системы не отображается кнопка-ссылка "Добавить"</li>
     * <br>
     * <li>Действие 2</li>
     * <li>Для профиля profile назначаем права на добавление запроса для клиента-сотрудника</li>
     * <br>
     * <li>Проверка 2</li>
     * <li>В верхней части интерфейса системы отображается кнопка-ссылка "Добавить"</li>
     * <br>
     * <li>Действие 3</li>
     * <li>Нажимаем на кнопку-ссылку "Добавить"</li>
     * <li>Нажимаем на ссылку "Запрос" из выпадающего списка</li>
     * <br>
     * <li>Проверка 3</li>
     * <li>Открывается форма добавления типа запроса scCase</li>
     * <li>В поле "Соглашение/Услуга" выбраны соглашение agreement и услуга service</li>
     * <li>В поле "Тип объекта" выбран тип запроса scCase</li>
     * <li>На форме отсутствует контент Выбор контрагента</li>
     * <br>
     * <li>Действие 4</li>
     * <li>Заполняем обязательные атрибуты</li>
     * <li>Нажимаем кнопку "Сохранить"</li>
     * <br>
     * <li>Проверка 4</li>
     * <li>Запрос с указанными значениями атрибутов создан</li>
     * <li>Входим в систему от имени суперпользователя, переходим на карточку созданного запроса</li>
     * <li>В атрибуте "Контрагент (сотрудник)" указан сотрудник employee</li>
     * <li>В атрибуте "Контрагент (отдел)" указан отдел ou</li>
     * </ol>
     */
    @Test
    public void testAddFromTopMenuForUnlicUser()
    {
        //Настройка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase, scCase2);
        DSLAttribute.setDefaultValue(scCase, "timeZone", SharedFixture.timeZone().getUuid());
        DSLAttribute.setDefaultValue(scCase, "priority", SharedFixture.priority().getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("1", "HOUR"));

        Attribute clientEmployee = SysAttribute.clientEmployee(scCase);
        Attribute clientOU = SysAttribute.clientOU(scCase);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, clientEmployee, clientOU);

        ContentForm scCaseSelect = DAOContentAddForm.createSelectScCase(scCase);
        ContentForm properties = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(scCaseSelect, properties);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, DAOScCase.createClass());
        DSLMenuItem.add(addButton);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase, scCase2);

        MetaClass employeeCase = DAOEmployeeCase.create();
        employeeCase.setDefaultScAgreement(agreement.getUuid());
        employeeCase.setDefaultScCase(scCase.getFqn());
        employeeCase.setDefaultScService(service.getUuid());
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        clientEmployee.setValue(employee.getTitle());
        clientOU.setValue(SharedFixture.ou().getTitle());

        UserGroup group = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(group, new ScClientRole(false), false);
        RightGroup rights = new RightGroup(profile, scCase, scCase2);
        rights.addRight(scCase, AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD, ScRights.ADD_TO_OU);
        rights.addRight(scCase2, AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD, ScRights.ADD_TO_OU);
        rights.apply();

        //Действие 1
        GUILogon.login(employee);
        //Проверка 1
        GUINavSettingsOperator.assertMenuItemExists(false, addButton);
        //Действие 2
        // Кнопка настроена на класс запрос, для открытия формы нужны права в классе запрос
        rights.addRight(DAOScCase.createClass(), ScRights.ADD_TO_EMPLOYEE);
        rights.addRight(scCase, ScRights.ADD_TO_EMPLOYEE);
        rights.addRight(scCase2, ScRights.ADD_TO_EMPLOYEE);
        rights.apply();
        GUILogon.logout();
        GUILogon.login(employee);
        //Проверка 2
        GUINavSettingsOperator.assertMenuItemExists(true, addButton);
        //Действие 3
        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem("serviceCall");
        //Проверка 3
        GUIForm.assertFormTitle(scCase.getTitle() + " / Форма добавления");
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle(),
                scCaseSelect.getXpathId());
        GUITester.assertValue(GUIXpath.Complex.METACLASS_SELECT_PATTERN, scCase.getTitle(), scCaseSelect.getXpathId());
        GUIContent.assertAbsenceByType(ContentType.SELECT_CLIENT);
        //Действие 4
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "description");
        GUIForm.applyForm();
        //Проверка 4
        Bo sc = DAOSc.create(scCase);
        GUIBo.setUuidByUrl(sc);
        GUILogon.asSuper();
        GUIBo.goToCard(sc.getUuid());
        GUIPropertyList.assertPropertyListAttribute(properties, clientEmployee, clientOU);
    }

    /**
     * Тестирование подстановки параметров запроса по умолчанию при добавлении запроса нелицензировнным пользователем
     * через общую кнопку добавить
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * http://sd-jira.naumen.ru/browse/NSDPRD-2369
     *
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>Добавляем в класс "Запрос" тип scCase</li>
     * <li>Добавляем кнопку добавления объектов типа scCase в верхнее меню</li>
     * <li>Добавляем услугу service с атрибутами "Типы запросов" = scCase</li>
     * <li>Добавляем соглашение agreement с атрибутами "Услуги" = service</li>
     * <li>Добавляем в класс "Сотрудник" тип employeeCase с параметрами запроса по умолчанию: "Соглашение" =
     * agreement, "Услуга" = service, "Тип объекта" = scCase</li>
     * <li>Добавляем в тип запроса scCase профиль прав доступа profile с параметрами: "Для нелицензированных
     * пользователей" = "да", "Роли пользователей" = "Контрагент запроса", "Группы пользователей" = <не выбрано></li>
     * <li>Для профиля profile назначаем все права, кроме добавления запроса для клиента-сотрудника</li>
     * <li>В компанию добавляем отдел ou</li>
     * <li>В отдел ou добавляем сотрудника employee с атрибутами: "Тип" = employeeCase, "Лицензия" =
     * "Нелицензированный пользователь", "Соглашения" = agreement</li>
     * <br>
     * <li>Действие 1</li>
     * <li>Входим в систему от имени сотрудника employee</li>
     * <br>
     * <li>Проверка 1</li>
     * <li>Кнопка добавления не отображается</li>
     * <br>
     * </ol>
     */
    @Test
    public void testAddFromTopMenuForUnlicUserAddToEmployeeRight()
    {
        //Настройка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        DSLAttribute.setDefaultValue(scCase, "timeZone", SharedFixture.timeZone().getUuid());
        DSLAttribute.setDefaultValue(scCase, "priority", SharedFixture.priority().getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("1", "HOUR"));

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLMenuItem.add(addButton);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase);

        MetaClass employeeCase = DAOEmployeeCase.create();
        employeeCase.setDefaultScAgreement(agreement.getUuid());
        employeeCase.setDefaultScCase(scCase.getFqn());
        employeeCase.setDefaultScService(service.getUuid());
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        UserGroup group = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(group, new ScClientRole(false), false);
        RightGroup rights = new RightGroup(profile, scCase);
        rights.addAllRights(scCase);
        rights.removeRight(scCase, ScRights.ADD_TO_EMPLOYEE);
        rights.apply();

        //Действие 1
        GUILogon.login(employee);
        //Проверка 1
        GUINavSettingsOperator.assertMenuItemExists(false, addButton);
    }

    /**
     * Тестирование подстановки параметров запроса по умолчанию при добавлении запроса нелицензировнным пользователем
     * через общую кнопку добавить
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * http://sd-jira.naumen.ru/browse/NSDPRD-2369
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>Добавляем в класс "Запрос" тип scCase, scCase2</li>
     * <li>Устанавливаем значения по умолчанию для атрибутов Часовой пояс, Приоритет и Нормативное время обработки
     * типа scCase</li>
     * <li>Создаем группу атрибутов attrGroup в типе scCase, добавляем в нее атрибуты Контрагент (сотрудник) и
     * Контрагент (отдел),
     * Соглашение, Услуга и Тип запроса</li>
     * <li>Добавляем на карточку запроса контент Параметры объекта с группой атрибутов attrGroup</li>
     * <li>Добавляем кнопку добавления объектов типа scCase в верхнее меню</li>
     * <li>Добавляем услугу service с атрибутами "Типы запросов" = scCase,scCase2</li>
     * <li>Добавляем соглашение agreement с атрибутами "Услуги" = service</li>
     * <li>Добавляем в класс "Сотрудник" тип employeeCase с параметрами запроса по умолчанию: 
     * "Соглашение" = agreement, "Услуга" = service, "Тип объекта" = scCase</li>
     * <li>Добавляем в тип запроса scCase, scCase2 профиль прав доступа profile с параметрами: 
     * "Для нелицензированных пользователей" = "да", "Роли пользователей" = "Контрагент запроса", "Группы
     * пользователей" = <не выбрано></li>
     * <li>Для профиля profileSC назначаем права на просмотр и редактирование всех атрибутов, 
     * просмотр карточки объекта, добавление запроса для клиента-сотрудника</li>
     * <li>Добавляем в тип employeeCase профиль profileEmpl и назначаем права на просмотр и редактирование всех
     * атрибутов,
     *  просмотр карточки объекта(без них не будут заполняться поля контактных данных контрагента )</li>
     * <li>В компанию добавляем отдел ou</li>
     * <li>В отдел ou добавляем сотрудника employee с атрибутами: "Тип" = employeeCase, 
     * "Лицензия" = "Нелицензированный пользователь", "Соглашения" = agreement</li>
     * <br>
     * <li>Действие 1</li>
     * <li>Входим в систему от имени сотрудника employee</li>
     * <li>Нажимаем на кнопку-ссылку "Добавить"</li>
     * <li>Нажимаем на ссылку scCase из выпадающего списка</li>
     * <br>
     * <li>Проверка 1</li>
     * <li>Открывается форма добавления типа запроса scCase</li>
     * <li>В поле "Соглашение/Услуга" выбраны соглашение agreement и услуга service</li>
     * <li>В поле "Тип объекта" выбран тип запроса scCase</li>
     * <li>Контактные данные контрагента (Контактное лицо, Контактный e-mail, Контактный телефон) заполнены данными
     * employee </li>
     * <br>
     * <li>Действие 2</li>
     * <li>Заполняем обязательные атрибуты</li>
     * <li>Нажимаем кнопку "Сохранить"</li>
     * <br>
     * <li>Проверка 2</li>
     * <li>Запрос с указанными значениями атрибутов создан</li>
     * <li>Входим в систему от имени суперпользователя, переходим на карточку созданного запроса</li>
     * <li>В атрибуте "Контрагент (сотрудник)" указан сотрудник employee</li>
     * <li>В атрибуте "Контрагент (отдел)" указан отдел ou</li>
     * <li>В атрибуте "Соглашение" указано соглашение agreement</li>
     * <li>В атрибуте "Услуга" указана услуга service</li>
     * <li>В атрибуте "Тип запроса" указан тип scCase</li>
     * <li>Контактные данные контрагента (Контактное лицо, Контактный e-mail, Контактный телефон) заполнены данными
     * employee </li>
     * </ol>
     */
    @Test
    public void testAddFromTopMenuForUnlicUserHasDefaultSCParams()
    {
        //Настройка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase, scCase2);
        DSLAttribute.setDefaultValue(scCase, "timeZone", SharedFixture.timeZone().getUuid());
        DSLAttribute.setDefaultValue(scCase, "priority", SharedFixture.priority().getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("1", "HOUR"));

        Attribute clientEmployeeAttr = SysAttribute.clientEmployee(scCase);
        Attribute clientOUAttr = SysAttribute.clientOU(scCase);
        Attribute agreementAttr = SysAttribute.agreement(scCase);
        Attribute serviceAttr = SysAttribute.service(scCase);
        Attribute metaClassAttr = SysAttribute.metaClass(scCase);
        Attribute clientPhone = SysAttribute.clientPhone(scCase);
        Attribute clientName = SysAttribute.clientName(scCase);
        Attribute clientEmail = SysAttribute.clientEmail(scCase);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, clientEmployeeAttr, clientOUAttr, agreementAttr, serviceAttr, metaClassAttr,
                clientPhone, clientName, clientEmail);

        ContentForm properties = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(properties);

        ContentForm editablePropertyList = DAOContentAddForm.createEditablePropertyList(scCase, attrGroup);
        DSLContent.add(editablePropertyList);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLMenuItem.add(addButton);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase, scCase2);

        MetaClass employeeCase = DAOEmployeeCase.create();
        employeeCase.setDefaultScAgreement(agreement.getUuid());
        employeeCase.setDefaultScCase(scCase.getFqn());
        employeeCase.setDefaultScService(service.getUuid());
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        clientEmployeeAttr.setValue(employee.getTitle());
        clientOUAttr.setValue(SharedFixture.ou().getTitle());
        agreementAttr.setValue(agreement.getTitle());
        serviceAttr.setValue(service.getTitle());
        metaClassAttr.setValue(scCase.getTitle());
        clientEmail.setValue(employee.getEmail());
        clientName.setValue(employee.getTitle());
        String phones = DSLEmployee.getPhones(employee);
        clientPhone.setValue(phones);

        UserGroup group = new UserGroup(employee);
        SecurityProfile profileSC = new SecurityProfile(group, new ScClientRole(false), false);
        RightGroup rightsSC = new RightGroup(profileSC, scCase, scCase2);
        rightsSC.addRight(scCase, AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD, ScRights.ADD_TO_EMPLOYEE);
        rightsSC.addRight(scCase2, AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD, ScRights.ADD_TO_EMPLOYEE);
        rightsSC.apply();
        SecurityProfile profileEmpl = new SecurityProfile(group, new EmployeeRole(false), false);
        RightGroup rightsEmpl = new RightGroup(profileEmpl, employeeCase);
        rightsEmpl.addRight(employeeCase, AbstractBoRights.VIEW_REST_ATTRIBUTES, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_CARD);
        rightsEmpl.apply();

        //Действие 1
        GUILogon.login(employee);
        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase.getFqn());
        //Проверка 1
        GUIForm.assertFormTitle(scCase.getTitle() + " / Форма добавления");
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, service.getTitle());
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase.getTitle());
        GUIContent.assertValue(editablePropertyList, clientEmail.getCode(), clientEmail.getTitle(),
                employee.getEmail());
        GUIContent.assertValue(editablePropertyList, clientName.getCode(), clientName.getTitle(), employee.getTitle());
        GUIContent.assertValue(editablePropertyList, clientPhone.getCode(), clientPhone.getTitle(),
                DSLEmployee.getPhones(employee));
        //Действие 2
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "description");
        GUIForm.applyForm();
        //Проверка 2
        Bo sc = DAOSc.create(scCase);
        GUIBo.setUuidByUrl(sc);
        GUILogon.asSuper();
        GUIBo.goToCard(sc.getUuid());
        GUIPropertyList.assertPropertyListAttribute(properties, clientEmployeeAttr, clientOUAttr, agreementAttr,
                serviceAttr, metaClassAttr, clientEmail, clientName, clientPhone);
    }

    /**
     * Тестирование подстановки параметров запроса по умолчанию при добавлении запроса нелицензировнным пользователем
     * через общую кнопку добавить, если тип добавляемого запроса не связан с услугой, указанной в параметрах по
     * умолчанию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * http://sd-jira.naumen.ru/browse/NSDPRD-2369
     *
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>Добавляем в класс "Запрос" тип scCase</li>
     * <li>Устанавливаем значения по умолчанию для атрибутов Часовой пояс, Приоритет и Нормативное время обработки
     * типа scCase</li>
     * <li>Создаем группу атрибутов attrGroup в типе scCase, добавляем в нее атрибуты Контрагент (сотрудник) и
     * Контрагент (отдел), Соглашение, Услуга и Тип запроса</li>
     * <li>Добавляем на карточку запроса контент Параметры объекта с группой атрибутов attrGroup</li>
     * <li>Добавляем кнопку добавления объектов типа scCase в верхнее меню</li>
     * <li>Добавляем услугу service с атрибутами "Типы запросов" = scCase</li>
     * <li>Добавляем соглашение agreement с атрибутами "Услуги" = service</li>
     * <li>Добавляем в класс "Сотрудник" тип employeeCase</li>
     * <li>Добавляем в тип запроса scCase профиль прав доступа profile с параметрами: "Для нелицензированных
     * пользователей" = "да", "Роли пользователей" = "Контрагент запроса", "Группы пользователей" = <не выбрано></li>
     * <li>Для профиля profile назначаем все права</li>
     * <li>В компанию добавляем отдел ou</li>
     * <li>В отдел ou добавляем сотрудника employee с атрибутами: "Тип" = employeeCase, "Лицензия" =
     * "Нелицензированный пользователь", "Соглашения" = agreement</li>
     * <br>
     * <li>Действие 1</li>
     * <li>Входим в систему от имени сотрудника employee</li>
     * <li>Нажимаем на кнопку-ссылку "Добавить"</li>
     * <li>Нажимаем на ссылку scCase из выпадающего списка</li>
     * <br>
     * <li>Проверка 1</li>
     * <li>Открывается форма добавления типа запроса scCase</li>
     * <li>В поле "Соглашение/Услуга" ничего не выбрано</li>
     * <li>В поле "Тип объекта" ничего не выбрано</li>
     * <br>
     * <li>Действие 2</li>
     * <li>Выбираем соглашение agreement и услугу service, тип объекта scCase</li>
     * <li>Заполняем обязательные атрибуты</li>
     * <li>Нажимаем кнопку "Сохранить"</li>
     * <br>
     * <li>Проверка 2</li>
     * <li>Запрос с указанными значениями атрибутов создан</li>
     * <li>Входим в систему от имени суперпользователя, переходим на карточку созданного запроса</li>
     * <li>В атрибуте "Контрагент (сотрудник)" указан сотрудник employee</li>
     * <li>В атрибуте "Контрагент (отдел)" указан отдел ou</li>
     * <li>В атрибуте "Соглашение" указано соглашение agreement</li>
     * <li>В атрибуте "Услуга" указана услуга service</li>
     * <li>В атрибуте "Тип запроса" указан тип scCase</li>
     * </ol>
     */
    @Test
    public void testAddFromTopMenuForUnlicUserHasDefaultSCParamsTypeNotConnectedWithService()
    {
        //Настройка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase, scCase2);
        DSLAttribute.setDefaultValue(scCase, "timeZone", SharedFixture.timeZone().getUuid());
        DSLAttribute.setDefaultValue(scCase, "priority", SharedFixture.priority().getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("1", "HOUR"));

        Attribute clientEmployeeAttr = SysAttribute.clientEmployee(scCase);
        Attribute clientOUAttr = SysAttribute.clientOU(scCase);
        Attribute agreementAttr = SysAttribute.agreement(scCase);
        Attribute serviceAttr = SysAttribute.service(scCase);
        Attribute metaClassAttr = SysAttribute.metaClass(scCase);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, clientEmployeeAttr, clientOUAttr, agreementAttr, serviceAttr, metaClassAttr);

        ContentForm properties = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(properties);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLMenuItem.add(addButton);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        Bo service2 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service, service2);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addAgreements(service2, agreement);
        DSLSlmService.addScCases(service, scCase);
        DSLSlmService.addScCases(service2, scCase2);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        employeeCase.setDefaultScAgreement(agreement.getUuid());
        employeeCase.setDefaultScCase(scCase2.getFqn());
        employeeCase.setDefaultScService(service2.getUuid());
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        clientEmployeeAttr.setValue(employee.getTitle());
        clientOUAttr.setValue(SharedFixture.ou().getTitle());
        agreementAttr.setValue(agreement.getTitle());
        serviceAttr.setValue(service.getTitle());
        metaClassAttr.setValue(scCase.getTitle());

        UserGroup group = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(group, new ScClientRole(false), false);
        RightGroup rights = new RightGroup(profile, scCase);
        rights.addAllRights(scCase);
        rights.apply();

        //Действие 1
        GUILogon.login(employee);
        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase.getFqn());
        //Проверка 1
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.EMPTY_VALUE);
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        GUISelect.assertNotDisplayed(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service2.getUuid(),
                service2.getTitle());
        //Действие 2
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase.getFqn());
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "description");
        GUIForm.applyForm();
        //Проверка 2
        Bo sc = DAOSc.create(scCase);
        GUIBo.setUuidByUrl(sc);
        GUILogon.asSuper();
        GUIBo.goToCard(sc.getUuid());
        GUIPropertyList.assertPropertyListAttribute(properties, clientEmployeeAttr, clientOUAttr, agreementAttr,
                serviceAttr, metaClassAttr);
    }

    /**
     * Тестирование подстановки значения [не указано] в поле Соглашение/Услуга при добавлении запроса под
     * нелицензировнным пользователем через общую кнопку добавить, если у сотрудника не указаны параметры запроса по
     * умолчанию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * http://sd-jira.naumen.ru/browse/NSDPRD-2369
     *
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>Добавляем в класс "Запрос" тип scCase</li>
     * <li>Устанавливаем значения по умолчанию для атрибутов Часовой пояс, Приоритет и Нормативное время обработки
     * типа scCase</li>
     * <li>Создаем группу атрибутов attrGroup в типе scCase, добавляем в нее атрибуты Контрагент (сотрудник) и
     * Контрагент (отдел), Соглашение, Услуга и Тип запроса</li>
     * <li>Добавляем на карточку запроса контент Параметры объекта с группой атрибутов attrGroup</li>
     * <li>Добавляем кнопку добавления объектов типа scCase в верхнее меню</li>
     * <li>Добавляем услугу service с атрибутами "Типы запросов" = scCase</li>
     * <li>Добавляем соглашение agreement с атрибутами "Услуги" = service</li>
     * <li>Добавляем в класс "Сотрудник" тип employeeCase</li>
     * <li>Добавляем в тип запроса scCase профиль прав доступа profile с параметрами: "Для нелицензированных
     * пользователей" = "да", "Роли пользователей" = "Контрагент запроса", "Группы пользователей" = <не выбрано></li>
     * <li>Для профиля profile назначаем все права</li>
     * <li>В компанию добавляем отдел ou</li>
     * <li>В отдел ou добавляем сотрудника employee с атрибутами: "Тип" = employeeCase, "Лицензия" =
     * "Нелицензированный пользователь", "Соглашения" = agreement</li>
     * <br>
     * <li>Действие 1</li>
     * <li>Входим в систему от имени сотрудника employee</li>
     * <li>Нажимаем на кнопку-ссылку "Добавить"</li>
     * <li>Нажимаем на ссылку scCase из выпадающего списка</li>
     * <br>
     * <li>Проверка 1</li>
     * <li>Открывается форма добавления типа запроса scCase</li>
     * <li>В поле "Соглашение/Услуга" ничего не выбрано</li>
     * <li>В поле "Тип объекта" ничего не выбрано</li>
     * <br>
     * <li>Действие 2</li>
     * <li>Выбираем соглашение agreement и услугу service, тип объекта scCase</li>
     * <li>Заполняем обязательные атрибуты</li>
     * <li>Нажимаем кнопку "Сохранить"</li>
     * <br>
     * <li>Проверка 2</li>
     * <li>Запрос с указанными значениями атрибутов создан</li>
     * <li>Входим в систему от имени суперпользователя, переходим на карточку созданного запроса</li>
     * <li>В атрибуте "Контрагент (сотрудник)" указан сотрудник employee</li>
     * <li>В атрибуте "Контрагент (отдел)" указан отдел ou</li>
     * <li>В атрибуте "Соглашение" указано соглашение agreement</li>
     * <li>В атрибуте "Услуга" указана услуга service</li>
     * <li>В атрибуте "Тип запроса" указан тип scCase</li>
     * </ol>
     */
    @Test
    public void testAddFromTopMenuForUnlicUserHasNoDefaultSCParams()
    {
        //Настройка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        DSLAttribute.setDefaultValue(scCase, "timeZone", SharedFixture.timeZone().getUuid());
        DSLAttribute.setDefaultValue(scCase, "priority", SharedFixture.priority().getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("1", "HOUR"));

        Attribute clientEmployeeAttr = SysAttribute.clientEmployee(scCase);
        Attribute clientOUAttr = SysAttribute.clientOU(scCase);
        Attribute agreementAttr = SysAttribute.agreement(scCase);
        Attribute serviceAttr = SysAttribute.service(scCase);
        Attribute metaClassAttr = SysAttribute.metaClass(scCase);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, clientEmployeeAttr, clientOUAttr, agreementAttr, serviceAttr, metaClassAttr);

        ContentForm properties = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(properties);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLMenuItem.add(addButton);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo service = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(agreement, service);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        clientEmployeeAttr.setValue(employee.getTitle());
        clientOUAttr.setValue(SharedFixture.ou().getTitle());
        agreementAttr.setValue(agreement.getTitle());
        serviceAttr.setValue(service.getTitle());
        metaClassAttr.setValue(scCase.getTitle());

        UserGroup group = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(group, new ScClientRole(false), false);
        RightGroup rights = new RightGroup(profile, scCase);
        rights.addAllRights(scCase);
        rights.apply();

        //Действие 1
        GUILogon.login(employee);
        GUINavSettingsOperator.clickMenuItem(addButton);
        GUINavSettingsOperator.clickMenuItem(scCase.getFqn());
        //Проверка 1
        GUITester.assertValue(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, GUISelect.EMPTY_VALUE);
        GUITester.assertValue(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, GUISelect.NO_ELEMENTS);
        //Действие 2
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE,
                agreement.getUuid() + ":" + service.getUuid());
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase.getFqn());
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "description");
        GUIForm.applyForm();
        //Проверка 2
        Bo sc = DAOSc.create(scCase);
        GUIBo.setUuidByUrl(sc);
        GUILogon.asSuper();
        GUIBo.goToCard(sc.getUuid());
        GUIPropertyList.assertPropertyListAttribute(properties, clientEmployeeAttr, clientOUAttr, agreementAttr,
                serviceAttr, metaClassAttr);
    }

    /**
     * Тестирование наличия кнопок смены статуса под нелицензионным NSDPRD-1578
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou, нелицензионного сотрудника employee типа
     * employeeCase, соглашение agreement, зарегистрировать запрос на employee.</li>
     * <li>Создать код закрытия closeCode</li>
     * <li>Создать запрос sc (регистрация из контрагента employee)</li>
     * <li>Создать команду team, сотрудника (с лицензией, конкурентной)employee2
     * типа employeeCase, связать с team</li>
     * <br>
     * <b>Выполнение действия</b>
     *  <li>Залогиниться под нелицензионным сотрудником</li>
     * <li>Запрос в состоянии Зарегистрирован</li>
     * <li>Проверяем, что для нелицензионного пользователя не отображается кнопка смены статуса</li>
     *
     * <li>Скриптом меняем состояние запроса на Разрешен</li>
     * <li>Проверяем, что для нелицензионного пользователя отображается кнопка смены статуса</li>
     *
     * <li>Нелицензионным пользователем меняем состояние запроса на Возобновлен</li>
     * <li>Проверяем, что для нелицензионного пользователя не отображается кнопка смены статуса</li>
     * </ol>
     */
    @Test
    public void testStatusRights()
    {
        //Создаем типы
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(employeeCase, scCase, teamCase);

        //Создаем элементы справочников
        CatalogItem closureCode = DAOCatalogItem.createClosureCode();
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("Chile/Continental");
        DSLCatalogItem.add(timeZoneItem, closureCode);

        //Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        Bo agreement = SharedFixture.agreement();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        Bo solvedByEmployee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, solvedByEmployee);

        DSLTeam.addEmployees(team, solvedByEmployee);

        DSLAgreement.addToRecipients(agreement, employee);

        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия
        GUILogon.asUnlicensed();
        GUIBo.goToCard(sc);
        GUITester.assertPresent(String.format(Div.ANY_CONTAINS, "PropertyList"), "Отсутствует контент на карточке");
        Assert.assertTrue("Присутствует кнопка смены статуса",
                tester.waitDisappear(GUIXpath.Div.CHANGE_STATE_CONTAINS));
        DSLSc.resolve(sc, team, solvedByEmployee);
        tester.refresh();
        Assert.assertTrue("Отсутствует кнопка смены статуса",
                tester.find(GUIXpath.Div.CHANGE_STATE_CONTAINS).isDisplayed());
        GUISc.resume();
        tester.refresh();
        GUITester.assertPresent(String.format(Div.ANY_CONTAINS, "PropertyList"), "Отсутствует контент на карточке");
        Assert.assertTrue("Присутствует кнопка смены статуса",
                tester.waitDisappear(GUIXpath.Div.CHANGE_STATE_CONTAINS));
    }
}
