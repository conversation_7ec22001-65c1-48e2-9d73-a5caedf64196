package ru.naumen.selenium.cases.admin.mobile;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.DSLMobileVersion;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContentActions;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContents;
import ru.naumen.selenium.casesutil.mobile.contents.listcontent.GUIMobileActionSettingsAction;
import ru.naumen.selenium.casesutil.mobile.contents.listcontent.MobileListContentActionHolder;
import ru.naumen.selenium.casesutil.mobile.contents.listcontent.MobileListContentActionType;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.AttrReference;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.MobileChildObjectsListContent;
import ru.naumen.selenium.casesutil.model.mobile.MobileRelObjectsListContent;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на настройку действий в контентах "Список связанных объектов" и "Список вложенных объектов" на карточках
 * объектов МК в ИА
 *
 * <AUTHOR>
 * @since 13.05.2024
 */
public class MobileObjectCardListContentActionsTest extends AbstractTestCase
{
    private static MetaClass userClassSelf, userCase, userCase2, userCase3;
    private static MobileCard mobileCard;
    private static MobileChildObjectsListContent childListContent;
    private static MobileRelObjectsListContent relListContent;
    private static Attribute objectLinkAttr;

    /**
     * <ol>
     * <b>Общая часть подготовки:</b>
     * <li>Загрузить лицензию с МК</li>
     * <li>Создать пользовательский класс userClass вложенный сам в себя и 3 его типа: userCase, userCase2 и
     * userCase3</li>
     * <li>Создать атрибут objectLinkAttr типа "Ссылка на бизнес-объект" employeeCase в классе userClassSelf</li>
     * <li>Создать карточку объектов mobileCard в настройках МК</li>
     * <li>Добавить на карточку mobileCard контенты:
     * <ul>
     *     <b>relObjectsList</b>
     *     <li>Тип контента: "Список связанных объектов"</li>
     *     <li>Атрибут связи: userClass@objectLinkAttr</li>
     * </ul>
     * <ul>
     *     <b>childObjectsList</b>
     *     <li>Тип контента: "Список вложенных объектов"</li>
     *     <li>Класс объектов, отображаемых в списке: userClassSelf</li>
     * </ul></li>
     * <li>Добавить версию мобильного API 15 в список поддерживаемых</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        userClassSelf = DAOUserClass.createInSelf();
        userCase = DAOUserCase.create(userClassSelf);
        userCase2 = DAOUserCase.create(userClassSelf);
        userCase3 = DAOUserCase.create(userClassSelf);
        DSLMetaClass.add(userClassSelf, userCase, userCase2, userCase3);

        objectLinkAttr = DAOAttribute.createObjectLink(userClassSelf, SharedFixture.employeeCase());
        DSLAttribute.add(objectLinkAttr);

        mobileCard = DAOMobile.createMobileCard(userClassSelf);
        DSLMobile.add(mobileCard);

        childListContent = DAOMobile.createMobileChildObjectsListContent(userClassSelf);
        relListContent = DAOMobile.createMobileRelObjectsListContent(userClassSelf,
                new AttrReference(userClassSelf, objectLinkAttr));
        DSLMobile.addContents(mobileCard, childListContent, relListContent);

        DSLMobileVersion.addSupportedVersionOnPrepareTest(MobileVersion.V15);
    }

    /**
     * Тестирование присутствия по умолчанию действия "Сортировка" в списке действий контентов "Список вложенных
     * объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Проверить, что в списке действий присутствует единственное действие - "Сортировка"</li>
     * <li>Сохранить список действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Проверить, что в списке действий присутствует единственное действие - "Сортировка"</li>
     * <li>Сохранить список действий</li>
     * </ol>
     */
    @Test
    public void testActionsListHasSortByDefault()
    {
        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        //@formatter:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .table()
                    .assertSize(1)
                    .actionAt(0)
                    .assertType(MobileListContentActionType.SORT)
                .toForm()
                .apply();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .table()
                    .assertSize(1)
                    .actionAt(0)
                        .assertTitle(MobileListContentActionType.SORT.getActionTitle())
                        .assertType(MobileListContentActionType.SORT)
                        .assertRequiredGeo(false)
                .toForm()
                .apply();
        //@formatter:on
    }

    /**
     * Тестирование создания действия "Добавить объект" в списке действий контентов "Список вложенных
     * объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления addForm для класса userClassSelf в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и нажать "Сохранить":
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     *     <li>"Передавать геопозицию устройства": true</li>
     * </ul></li>
     * <li>Проверить, что в списке действий появилось действие "Добавить объект" с заполненными параметрами</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и нажать "Сохранить":
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     *     <li>"Передавать геопозицию устройства": true</li>
     * </ul></li>
     * <li>Проверить, что в списке действий появилось действие "Добавить объект" с заполненными параметрами</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * </ol>
     */
    @Test
    public void testCreateAddObjectAction()
    {
        //Подготовка:
        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClassSelf);
        DSLMobile.add(addForm);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        //@formatter:off
        MobileListContentActionHolder holder = new MobileListContentActionHolder();

        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction(holder)
                    .fillTitle()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .selectAddForm(addForm)
                    .setRequiredGeo()
                    .apply()
                .table()
                    .actionAt(1).assertAction(holder)
                .toForm()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction(holder)
                    .fillTitle()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .selectAddForm(addForm)
                    .setRequiredGeo()
                    .apply()
                .table()
                    .actionAt(1).assertAction(holder)
                .toForm()
                .cancel();
        //@formatter:on
    }

    /**
     * Тестирование фильтрации выпадающего списка поля "Форма добавления" на форме "Добавить действие" в списке
     * действий контентов "Список вложенных объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления parentEmptyAddForm для класса userClassSelf в МК</li>
     * <li>Добавить форму добавления parentNotEmptyAddForm для класса userClassSelf и типов userCase, userCase2 в
     * МК</li>
     * <li>Добавить форму добавления parentWithChildrenAddForm для класса userClassSelf и тип userCase в МК</li>
     * <li>Добавить форму добавления parentWithChildrenAddForm2 для класса userClassSelf и тип userCase в МК</li>
     * <li>Добавить форму добавления parentWithChildrenAddForm3 для класса userClassSelf и тип userCase в МК</li>
     * <li>Добавить форму добавления childAddForm1 для класса userClassSelf дочернюю форме parentWithChildrenAddForm
     * в МК</li>
     * <li>Добавить форму добавления childAddForm2 для класса userClassSelf и типа userCase2 дочернюю форме
     * parentWithChildrenAddForm2 в МК</li>
     * <li>Добавить форму добавления childAddForm3 для класса userClassSelf и типа userCase дочернюю форме
     * parentWithChildrenAddForm3 в МК</li>
     * <li>Добавить на карточку mobileCard контенты:
     * <ul>
     *     <b>relObjectsList2</b>
     *     <li>Тип контента: "Список связанных объектов"</li>
     *     <li>Типы: userCase, userCase2</li>
     *     <li>Атрибут связи: userClass@objectLinkAttr</li>
     * </ul>
     * <ul>
     *     <b>childObjectsList2</b>
     *     <li>Тип контента: "Список вложенных объектов"</li>
     *     <li>Класс объектов, отображаемых в списке: userClassSelf</li>
     *     <li>Типы: userCase, userCase2</li>
     * </ul></li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и заполнить поле "Действие": "Добавить объект"</li>
     * <li>Проверить, что выпадающем списке поля "Форма добавления" есть только формы parentEmptyAddForm и
     * parentWithChildrenAddForm</li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий контента</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и заполнить поле "Действие": "Добавить объект"</li>
     * <li>Проверить, что выпадающем списке поля "Форма добавления" есть только формы parentEmptyAddForm и
     * parentWithChildrenAddForm</li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий контента</li>
     * <li>Открыть список действий контента childListContent2</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и заполнить поле "Действие": "Добавить объект"</li>
     * <li>Проверить, что выпадающем списке поля "Форма добавления" есть только формы parentEmptyAddForm,
     * parentNotEmptyAddForm, parentWithChildrenAddForm и parentWithChildrenAddForm2</li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий контента</li>
     * <li>Открыть список действий контента relListContent2</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму и заполнить поле "Действие": "Добавить объект"</li>
     * <li>Проверить, что выпадающем списке поля "Форма добавления" есть только формы parentEmptyAddForm,
     * parentNotEmptyAddForm, parentWithChildrenAddForm и parentWithChildrenAddForm2</li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий контента</li>
     * </ol>
     */
    @Test
    public void testFiltrationAddFormsForAddObjectAction()
    {
        //Подготовка:
        MobileAddForm parentEmptyAddForm = DAOMobile.createMobileAddForm(userClassSelf);
        MobileAddForm parentNotEmptyAddForm = DAOMobile.createMobileAddForm(userCase, userCase2);
        MobileAddForm parentWithChildrenAddForm = DAOMobile.createMobileAddForm(userCase);
        MobileAddForm parentWithChildrenAddForm2 = DAOMobile.createMobileAddForm(userCase);
        MobileAddForm parentWithChildrenAddForm3 = DAOMobile.createMobileAddForm(userCase);
        MobileAddForm parentOtherClassAddForm = DAOMobile.createMobileAddForm(DAOEmployeeCase.createClass());
        DSLMobile.add(parentEmptyAddForm, parentNotEmptyAddForm, parentWithChildrenAddForm,
                parentWithChildrenAddForm2, parentWithChildrenAddForm3, parentOtherClassAddForm);

        MobileAddForm childAddForm1 = DAOMobile.createMobileAddForm(parentWithChildrenAddForm, userClassSelf);
        MobileAddForm childAddForm2 =
                DAOMobile.createMobileAddForm(parentWithChildrenAddForm2, userCase2);
        MobileAddForm childAddForm3 =
                DAOMobile.createMobileAddForm(parentWithChildrenAddForm3, userCase);
        DSLMobile.add(childAddForm1, childAddForm2, childAddForm3);

        MobileChildObjectsListContent childListContent2 = DAOMobile.createMobileChildObjectsListContent(userClassSelf);
        childListContent2.setCases(userCase, userCase2);
        MobileRelObjectsListContent relListContent2 = DAOMobile.createMobileRelObjectsListContent(
                userClassSelf, new AttrReference(userClassSelf, objectLinkAttr));
        relListContent2.setCases(userCase, userCase2);
        DSLMobile.addContents(mobileCard, childListContent2, relListContent2);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        //@formatter:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .assertPresentAddForms(parentEmptyAddForm, parentWithChildrenAddForm)
                    .assertAbsentAddForms(parentNotEmptyAddForm, parentWithChildrenAddForm2,
                            parentWithChildrenAddForm3, parentOtherClassAddForm)
                    .cancel()
                .cancel();
        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .assertPresentAddForms(parentEmptyAddForm, parentWithChildrenAddForm)
                    .assertAbsentAddForms(parentNotEmptyAddForm, parentWithChildrenAddForm2,
                            parentWithChildrenAddForm3, parentOtherClassAddForm)
                    .cancel()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(childListContent2)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .assertPresentAddForms(parentEmptyAddForm, parentNotEmptyAddForm, parentWithChildrenAddForm,
                            parentWithChildrenAddForm2)
                    .assertAbsentAddForms(parentWithChildrenAddForm3, parentOtherClassAddForm)
                    .cancel()
                .cancel();
        GUIMobileContentActions.openContentActionsSettings(relListContent2)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .assertPresentAddForms(parentEmptyAddForm, parentNotEmptyAddForm, parentWithChildrenAddForm,
                            parentWithChildrenAddForm2)
                    .assertAbsentAddForms(parentWithChildrenAddForm3, parentOtherClassAddForm)
                    .cancel()
                .cancel();
        //@formatter:on
    }

    /**
     * Тестирование появления предупреждения при выборе формы добавления, отключённой меткой, во время создания
     * действия типа "Добавить объект" у контентов "Список вложенных объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить тег tag и выключить его</li>
     * <li>Добавить форму добавления addForm для класса userClassSelf с тегом tag в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму:
     * <ul>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>Проверить, что в форме появилось предупреждение "Форма добавления объектов [addForm] была выключена метками."
     * </li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить открывшуюся форму:
     * <ul>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>Проверить, что в форме появилось предупреждение "Форма добавления объектов [addForm] была выключена метками."
     * </li>
     * <li>Нажать кнопку "Отмена" на форме "Добавить действие"</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * </ol>
     */
    @Test
    public void testAttentionWhenChooseDisabledByTagAddForm()
    {
        //Подготовка:
        Tag tag = DAOTag.createTag(false);
        DSLTag.add(tag);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClassSelf);
        addForm.setTags(tag);
        DSLMobile.add(addForm);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        String attentionMessage = "Форма добавления объектов [%s] была выключена метками.".formatted(addForm.getCode());
        //@formatter:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .selectAddForm(addForm)
                    .assertAttention(attentionMessage)
                    .cancel()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction()
                    .selectType(MobileListContentActionType.ADD_OBJECT)
                    .selectAddForm(addForm)
                    .assertAttention(attentionMessage)
                    .cancel()
                .cancel();
        //@formatter:on
    }

    /**
     * Тестирование отображения недоступности действия "Добавить объект" в списке действий контентов "Список вложенных
     * объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить тег tag и выключить его</li>
     * <li>Добавить форму добавления addForm для класса userClassSelf в МК</li>
     * <li>Добавить форму добавления addForm2 для класса userClassSelf с тегом tag в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Добавить в список действия:
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm2</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     * </ul></li>
     * <li>Проверить, что в списке действий первое добавленное действие имеет обычный текст, а текст остальных
     * добавленных действий выделен серым</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Добавить в список действия:
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm2</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     * </ul>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     * </ul></li>
     * <li>Проверить, что в списке действий первое добавленное действие имеет обычный текст, а текст остальных
     * добавленных действий выделен серым</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * </ol>
     */
    @Test
    public void testDisabledAddObjectActions()
    {
        //Подготовка:
        Tag tag = DAOTag.createTag(false);
        DSLTag.add(tag);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClassSelf);
        MobileAddForm addForm2 = DAOMobile.createMobileAddForm(userClassSelf);
        addForm2.setTags(tag);
        DSLMobile.add(addForm, addForm2);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        //@formatted:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .selectAddForm(addForm)
                .apply()
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .selectAddForm(addForm2)
                .apply()
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .apply()
                .addAction()
                .fillTitle()
                .apply()
                .table()
                .actionAt(1).assertEnabled().toTable()
                .actionAt(2).assertDisabled().toTable()
                .actionAt(3).assertDisabled().toTable()
                .actionAt(4).assertDisabled().toTable()
                .toForm()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .selectAddForm(addForm)
                .apply()
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .selectAddForm(addForm2)
                .apply()
                .addAction()
                .fillTitle()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .apply()
                .addAction()
                .fillTitle()
                .apply()
                .table()
                .actionAt(1).assertEnabled().toTable()
                .actionAt(2).assertDisabled().toTable()
                .actionAt(3).assertDisabled().toTable()
                .actionAt(4).assertDisabled().toTable()
                .toForm()
                .cancel();
        //@formatted:on
    }

    /**
     * Тестирование удаления действий "Добавить объект" в списке действий контентов "Список вложенных
     * объектов" и "Список связанных объектов".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления addForm для класса userClassSelf в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Добавить 3 действия:</li>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>В списке действий удалить все добавленные действия</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Добавить 3 действия:</li>
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>В списке действий удалить все добавленные действия</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * </ol>
     */
    @Test
    public void testDeleteContentListActions()
    {
        //Подготовка:
        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClassSelf);
        DSLMobile.add(addForm);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        MobileListContentActionHolder holder = new MobileListContentActionHolder();
        holder.setTitle(ModelUtils.createTitle());
        holder.setType(MobileListContentActionType.ADD_OBJECT);
        holder.setAddForm(addForm);

        //@formatters:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction(holder).fillByAction().apply()
                .addAction(holder).fillByAction().apply()
                .addAction(holder).fillByAction().apply()
                .table()
                .allActions(GUIMobileActionSettingsAction::delete)
                .assertSize(1)
                .actionAt(0)
                .assertType(MobileListContentActionType.SORT)
                .toForm()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction(holder).fillByAction().apply()
                .addAction(holder).fillByAction().apply()
                .addAction(holder).fillByAction().apply()
                .table()
                .allActions(GUIMobileActionSettingsAction::delete)
                .assertSize(1)
                .actionAt(0)
                .assertType(MobileListContentActionType.SORT)
                .toForm()
                .cancel();
        //@formatters:on
    }

    /**
     * Тестирование отображения действий контентов "Список вложенных объектов" и "Список связанных объектов" в их
     * информации о контенте на карточке объекта МК.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления addForm для класса userClassSelf в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Добавить 2 действия с параметрами:
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>Нажать кнопку "Сохранить" на форме списка действий</li>
     * <li>Проверить, информации о контенте childListContent появилась подстрока "| Действия: " после которой через
     * запятую перечислены названия добавленных действий</li>
     * <li>Удалить все добавленные действия в контенте childListContent</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Добавить 2 действия с параметрами:
     * <ul>
     *     <li>"Название": произвольная строка</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавление": addForm</li>
     * </ul></li>
     * <li>Нажать кнопку "Сохранить" на форме списка действий</li>
     * <li>Проверить, информации о контенте relListContent появилась подстрока "| Действия: " после которой через
     * запятую перечислены названия добавленных действий</li>
     * <li>Удалить все добавленные действия в контенте relListContent</li>
     * </ol>
     */
    @Test
    public void testActionTitlesInContentInfoPanel()
    {
        //Подготовка:
        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClassSelf);
        DSLMobile.add(addForm);

        //Действия и проверки:
        MobileListContentActionHolder sortAction = MobileListContentActionHolder.createSortAction();

        MobileListContentActionHolder actionHolder1 = new MobileListContentActionHolder();
        actionHolder1.setTitle(ModelUtils.createTitle());
        actionHolder1.setType(MobileListContentActionType.ADD_OBJECT);
        actionHolder1.setAddForm(addForm);

        MobileListContentActionHolder actionHolder2 = new MobileListContentActionHolder();
        actionHolder2.setTitle(ModelUtils.createTitle());
        actionHolder2.setType(MobileListContentActionType.ADD_OBJECT);
        actionHolder2.setAddForm(addForm);

        String actionsTitles = Stream.of(sortAction, actionHolder1, actionHolder2)
                .map(MobileListContentActionHolder::getTitle)
                .collect(Collectors.joining(", ", "| Действия: ", ""));

        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        Cleaner.afterTest(true, () ->
        {
            GUILogon.asSuper();
            GUIMobileCard.goToCard(mobileCard);
            GUIMobileContentActions.openContentActionsSettings(childListContent).clearActions();
            GUIMobileContentActions.openContentActionsSettings(relListContent).clearActions();
        });

        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction(actionHolder1).fillByAction().apply()
                .addAction(actionHolder2).fillByAction().apply()
                .apply();
        GUIMobileContents.assertContainsTextInInfoPanel(childListContent, actionsTitles);

        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .table().allActions(GUIMobileActionSettingsAction::delete).toForm().apply();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction(actionHolder1).fillByAction().apply()
                .addAction(actionHolder2).fillByAction().apply()
                .apply();
        GUIMobileContents.assertContainsTextInInfoPanel(relListContent, actionsTitles);

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .table().allActions(GUIMobileActionSettingsAction::delete).toForm().apply();
    }

    /**
     * Тестирование доступности для контентов "Список вложенных объектов" и "Список связанных объектов" добавления
     * действия "Добавить объект", ссылающегося на форму, сумма типов иерархии которой составляет полный список типов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления parentAddForm для класса userClassSelf и типов userCase, userCase2 в МК</li>
     * <li>Добавить дочернюю для parentAddForm форму добавления childAddForm для класса userClassSelf и типа
     * userCase3 в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить на открывшейся форме: "Действие" = "Добавить объект"
     * <li>Проверить, что в списке для выбора поля "Форма добавления" есть форма parentAddForm</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить на открывшейся форме: "Действие" = "Добавить объект"
     * <li>Проверить, что в списке для выбора поля "Форма добавления" есть форма parentAddForm</li>
     * <li>Нажать кнопку "Отмена" на форме списка действий</li>
     * </ol>
     */
    @Test
    public void testUnionCasesByAddFormsHierarchyIsAllClassCases()
    {
        //Подготовка:
        MobileAddForm parentAddForm = DAOMobile.createMobileAddForm(userCase, userCase2);
        DSLMobile.add(parentAddForm);

        MobileAddForm childAddForm = DAOMobile.createMobileAddForm(parentAddForm, userCase3);
        DSLMobile.add(childAddForm);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        //@formatters:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .assertPresentAddForms(parentAddForm)
                .cancel()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction()
                .selectType(MobileListContentActionType.ADD_OBJECT)
                .assertPresentAddForms(parentAddForm)
                .cancel()
                .cancel();
        //@formatters:on
    }

    //Тестирование сбрасывания формы добавления в действиях контентов

    /**
     * Тестирование сбрасывания формы добавления в действиях контентов "Список вложенных объектов" и "Список
     * связанных объектов", если сумма типов иерархии формы перестала покрывать типы контентов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00856
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00601
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00631
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$210491856
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить форму добавления parentAddForm для класса userClassSelf и типов userCase, userCase2 в МК</li>
     * <li>Добавить дочернюю для parentAddForm форму добавления childAddForm для класса userClassSelf и типа
     * userCase3 в МК</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем naumen</li>
     * <li>Открыть карточку mobileCard в ИА в разделе "Настройка системы" -> "Мобильное приложение" -> "Карточки
     * объектов"</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить на открывшейся форме:
     * <ul>
     *     <li>"Название": произвольно</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавления": parentAddForm</li>
     * </ul>
     * <li>Нажать кнопку "Сохранить" на форме списка действий</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Нажать кнопку "Добавить действие"</li>
     * <li>Заполнить на открывшейся форме:
     * <ul>
     *     <li>"Название": произвольно</li>
     *     <li>"Действие": "Добавить объект"</li>
     *     <li>"Форма добавления": parentAddForm</li>
     * </ul>
     * <li>Нажать кнопку "Сохранить" на форме списка действий</li>
     * <li>Изменить список типов формы добавления childListContent на userCase2</li>
     * <li>Обновить страницу с настройкой карточки mobileCard</li>
     * <li>Открыть список действий контента childListContent</li>
     * <li>Проверить, что ранее добавленное действие стало серым</li>
     * <li>Открыть список действий контента relListContent</li>
     * <li>Проверить, что ранее добавленное действие стало серым</li>
     * </ol>
     */
    @Test
    public void testChangeCasesInChildAddFormUsedInListContentActions()
    {
        //Подготовка:
        MobileAddForm parentAddForm = DAOMobile.createMobileAddForm(userCase, userCase2);
        DSLMobile.add(parentAddForm);

        MobileAddForm childAddForm = DAOMobile.createMobileAddForm(parentAddForm, userCase3);
        DSLMobile.add(childAddForm);

        //Действия и проверки:
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        Cleaner.afterTest(true, () ->
        {
            GUILogon.asSuper();
            GUIMobileCard.goToCard(mobileCard);
            GUIMobileContentActions.openContentActionsSettings(childListContent).clearActions();
            GUIMobileContentActions.openContentActionsSettings(relListContent).clearActions();
        });

        MobileListContentActionHolder holder = new MobileListContentActionHolder();
        holder.setTitle(ModelUtils.createTitle());
        holder.setType(MobileListContentActionType.ADD_OBJECT);
        holder.setAddForm(parentAddForm);

        //@formatters:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .addAction(holder).fillByAction().apply()
                .apply();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .addAction(holder).fillByAction().apply()
                .apply();
        //@formatters:on

        childAddForm.setCases(userCase2);
        DSLMobile.edit(childAddForm);

        tester.refresh();

        //@formatters:off
        GUIMobileContentActions.openContentActionsSettings(childListContent)
                .table()
                .actionAt(1)
                .assertDisabled()
                .toForm()
                .cancel();

        GUIMobileContentActions.openContentActionsSettings(relListContent)
                .table()
                .actionAt(1)
                .assertDisabled()
                .toForm()
                .cancel();
        //@formatters:on
    }
}
