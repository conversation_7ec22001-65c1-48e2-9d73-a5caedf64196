package ru.naumen.selenium.cases.operator.session;

import java.nio.file.Paths;
import java.util.Set;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.GUISession;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLPresentation;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.GUISimpleList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.presentation.DAOPresentation;
import ru.naumen.selenium.casesutil.model.content.presentation.Presentation;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.report.DAOReportInstance;
import ru.naumen.selenium.casesutil.model.report.DAOReportTemplate;
import ru.naumen.selenium.casesutil.model.report.ReportInstance;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.operator.GUIFavorites;
import ru.naumen.selenium.casesutil.report.DSLReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportInstance;
import ru.naumen.selenium.casesutil.report.GUIReportInstanceList;
import ru.naumen.selenium.casesutil.security.GUIPasswordForm;
import ru.naumen.selenium.casesutil.security.GUIPasswordForm.IndicatorState;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на повторное выполнении действия при востановлении сессии при ее обрыве 
 * при выполнении действия в интерфейсе оператора.
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
 * <AUTHOR>
 * @since 11.06.2014
 */
@IgnoreConfig(cause = "NSDPRD-10607")
public class ReconnectSessionAndApply1Test extends AbstractTestCase
{

    @AfterClass
    public static void postTestAction()
    {
        tester.enableWaitAsyncCall();
    }

    @BeforeClass
    public static void prepareFixture()
    {
        tester.disableWaitAsyncCall();
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создаем контент типа "Список вложенных объектов" contentChildObjectLis в карточке компании для ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под пользователем</li>
     * <li>Переходим на карточку на компании, в контенте contentChildObjectLis нажимаем "Добавить"</li>
     * <li>Происходит редирект на форму добавления</li>
     * <li>На форме заполняем поле Название</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что отдел типа ouCase с указанным названием был создан</li>
     * </ol>
     */
    @Test
    public void testAddBo()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        //Создаем контент типа "Список вложенных объектов" в карточке компании для отображения отделов заданного типа
        ContentForm contentChildObjectLis = DAOContentCard.createChildObjectList(DAORootClass.create().getCode(),
                DAOOuCase.createClass(), ouCase);
        DSLContent.add(contentChildObjectLis);
        //Выполнение действия
        GUILogon.asTester();
        Bo ou = DAOOu.create(ouCase);
        GUINavigational.goToOperatorUI();
        GUISimpleList.clickAdd(contentChildObjectLis);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, ou.getTitle());
        GUISession.reconnectionWithApplyForm(tester, false);
        GUIBo.setUuidByUrl(ou);
        ModelMap map = SdDataUtils.getObject(ou.getMetaclassFqn(), ModelUuid.UUID, ou.getUuid());
        Assert.assertEquals("Тип некорректен", ou.getTitle(), map.get("title"));
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении комментария
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип услуги slmCase</li>
     * <li>Создать на карточке slmCase контент типа Комментарии к объекту commentContent</li>
     * <li>Создать услугу slm типа slmCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под пользователем</li>
     * <li>Переходим на карточку услуги, в контенте commentContent нажимаем "Добавить комментарий"</li>
     * <li>В появившемся модальном окне заполняем поле "Текст"</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что комментарий с указанным текстом был создан</li>
     * </ol>
     */
    @Test
    public void testAddComment()
    {
        MetaClass slmCase = DAOServiceCase.create();
        DSLMetaClass.add(slmCase);

        ContentForm commentContent = DAOContentCard.createCommentList(slmCase.getFqn());
        DSLContent.add(commentContent);

        Bo slm = DAOOu.create(slmCase);
        DSLBo.add(slm);
        //Выполнение действий и проверки
        String text = ModelUtils.createTitle();
        GUILogon.asTester();
        GUIBo.goToCard(slm);
        GUICommentList.clickAddLink(commentContent);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIComment.fillCommentAddForm(text, false);
        GUISession.reconnectionWithApplyForm(tester, false);
        GUIComment.assertCommentsCount(commentContent, 1);
        GUIComment.assertCommentPresent(commentContent, text);
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении в избранное
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou, в этом отделе создать сотрудника employee со всемии правами.</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Нажимаем пиктограмму "Добавить в избранное"</li>
     * <li>Ждем появления формы добавления в избранное.</li>
     * <li>В форме заполняем название</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что ссылка с указанным названием добавлена в избранное</li>
     * </ol>
     */
    @Test
    public void testAddFavorites()
    {
        //Подготовка
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);
        //Выполнение действия
        String favoriteTitle = ModelUtils.createTitle();
        GUILogon.login(employee);
        //Добавляем ссылку в избранное
        GUIBo.goToCard(ou);
        GUIFavorites.openAddFavoritesForm();
        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TITLE_VALUE, favoriteTitle);
        GUISession.reconnectionWithApplyForm(tester, false);
        Assert.assertTrue("Ссылка в избранном отсутствует", tester.waitAppear(GUIXpath.Any.FAVORITES_NAVIGATION_TREE
                                                                              + "//a[@id='gwt-debug-title' and "
                                                                              + "contains(@href,'#uuid:"
                                                                              + ou.getUuid() + "')]"));
        Assert.assertEquals("Название ссылки в избранном не совпадает с введенным", favoriteTitle,
                tester.getText("//div[contains(@id,'gwt-debug-NTreeItemContent.uuid:" + ou.getUuid() + "')]"));
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении файла
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass в нем тип userCase</li>
     * <li>Добавить на карточку типа userCase контент типа список файлов cardFilesContent</li>
     * <li>Добавить пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В контенте cardFilesContent нажимаем ссылку добавить, ожидаем форму добавления файла</li>
     * <li>В форме выбираем файл и загружае его</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что файл был добавлен</li>
     * </ol>
     */
    @Test
    public void testAddFile()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm cardFilesContent = DAOContentCard.createFileList(userCase.getFqn());
        DSLContent.add(cardFilesContent);

        Bo userBo = DAOOu.create(userCase);
        DSLBo.add(userBo);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.assertPresent(cardFilesContent);
        GUIContent.assertLinkPresent(cardFilesContent, GUIContent.LINK_ADD);
        GUIContent.clickAdd(cardFilesContent);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIFileAdmin.uploadFile(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Div.EDITABLE_PROPERTY_LIST_CONTAINS,
                DSLFile.FILE_FOR_UPLOAD_ADD);
        Assert.assertTrue("Файл не был загружен", tester.waitAppear(GUIXpath.Other.DEL_FILE_BUTTON));
        GUIForm.assertApplyBtnEnabled(true);
        GUISession.reconnectionWithApplyForm(tester, false);
        String fileName = Paths.get(DSLFile.FILE_FOR_UPLOAD_ADD).toFile().getName();
        GUIFileList.assertFilePresence(cardFilesContent, fileName);
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении представления сложного
     * списка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass в нем тип userCase</li>
     * <li>Добавить на карточку типа userCase контент типа список объектов objectList (представление - сложный
     * список)</li>
     * <li>Добавить пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В контенте objectList нажимаем ссылку "Сохранить представление"</li>
     * <li>Ожидаем появления формы сохранения представления</li>
     * <li>на форме заполняем название.</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что представление с указаным названием было добавлено</li>
     * </ol>
     */
    @Test
    public void testAddPresentation()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm objectList = DAOContentCard.createObjectList(userCase.getFqn(), userClass, userCase);
        objectList.setPresentation(PresentationContent.ADVLIST.get());
        DSLContent.add(objectList);

        Bo userBo = DAOOu.create(userCase);
        DSLBo.add(userBo);
        //Выполнение действий и проверки
        Presentation prs1 = DAOPresentation.createPrivate(objectList);
        Set<String> beforeIds = DSLPresentation.getPresentationIds(prs1.getMetaclassCode());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickSavePrs();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        objectList.advlist().prs().saveView().setTitle(prs1.getTitle());
        GUISession.reconnectionWithApplyForm(tester, false);
        objectList.advlist().prs().asserts().selectPrs(false, false, prs1);
        prs1.setExists(true);
        Set<String> afterIds = DSLPresentation.getPresentationIds(prs1.getMetaclassCode());
        afterIds.removeAll(beforeIds);
        Assert.assertEquals("Было добавлено больше одного представления (либо ниодного).", 1, afterIds.size());
        prs1.setUuid(afterIds.iterator().next());
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении связи
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип услуги slmServiceCase и соглашения agreementCase</li>
     * <li>Добавить на карточку типа agreementCase контент типа список связанных объектов content</li>
     * <li>Добавить услугу agreement и соглашение slmService</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку соглашения agreement</li>
     * <li>В контенте content нажимаем ссылку "Связать"</li>
     * <li>Ожидаем появления формы добавления связи</li>
     * <li>На форме связываем соглашение с услугой slmService</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что соглашение agreement связано с услугой slmService</li>
     * <li>Проверяем, что услуга slmService связана с соглашением agreement</li>
     * </ol>
     */
    @Test
    public void testAddRelation()
    {
        //Подготовка
        MetaClass slmServiceCase = DAOServiceCase.create();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(slmServiceCase, agreementCase);

        //Создаем контены
        String agreementFqn = agreementCase.getFqn();
        ContentForm content = DAOContentCard.createRelatedObjectList(agreementFqn,
                String.format("%s@services", agreementFqn));
        DSLContent.add(content);
        //Создаем услугу, соглашение        
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        Bo agreement = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        Bo slmService = DAOService.create(slmServiceCase);
        DSLBo.add(slmService, agreement);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(agreement);
        GUISimpleList.clickAddRelation(content);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIXpath.Div.FORM + GUIXpath.InputComplex.ANY_VALUE, "services");
        GUIMultiSelect.select(GUIXpath.Any.POPUP_LIST_SELECT, slmService.getUuid());
        GUISession.reconnectionWithApplyForm(tester, false);
        //Проверки
        tester.waitAsyncCall(2);
        ModelMap agreementMap = SdDataUtils.getObject(agreement.getMetaclassFqn(), ModelUuid.UUID, agreement.getUuid());
        Assert.assertEquals("Между услугой и соглашением нет связи", slmService.getTitle(),
                SdDataUtils.getMapsArrayValue(agreementMap, "services").get(0).get("title"));

        ModelMap slmMap = SdDataUtils.getObject(slmService.getMetaclassFqn(), ModelUuid.UUID, slmService.getUuid());
        Assert.assertEquals("Между услугой и соглашением нет связи", agreement.getTitle(),
                SdDataUtils.getMapsArrayValue(slmMap, "agreements").get(0).get("title"));
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при добавлении отчета
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать шаблон отчета template</li>
     * <li>Создать тип отдела ouCase  и отдел данного типа ou</li>
     * <li>Добавить на карточку типа ouCase контент типа Отчет content</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В контенте content нажимаем на шаблон отчета template, куда и переходим</li>
     * <li>Нажимает кнопку "Добавить" в списке отчетов, проверяет, что появилась форма добавления отчета</li>
     * <li>На форме заполняем название отчета</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что отчет добавился</li>
     * </ol>
     */
    @Test
    public void testAddReport()
    {
        //Подготовка
        ReportTemplate template = DAOReportTemplate.createReportTemplate(DAOReportTemplate.TEMPLATE1);
        DSLReportTemplate.add(template);

        MetaClass ouCase = SharedFixture.ouCase();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        ContentForm content = DAOContentCard.createReportList(ouCase.getFqn(), template);
        DSLContent.add(content);

        ReportInstance report = DAOReportInstance.create(ou, content, template);
        //Действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIReportInstance.clickReportInList(template, content);
        GUIReportInstanceList.createNewReport();
        GUIReportInstance.fillReportTitle(report);
        GUISession.reconnectionWithApplyForm(tester, false);

        //Проверки
        GUIBo.assertBoTitle(report.getTitle());

        GUIBo.goToCard(ou);
        GUIReportInstance.clickReportInList(template, content);
        GUIReportInstanceList.assertReportExists(report);
        GUIReportInstanceList.gotoReportCard(report);

        GUIReportInstance.setUuidByUrl(report);
        report.setUuid("reportInstance$" + report.getUuid());
        report.setExists(true);
        GUIReportInstance.assertReportCard(report);
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при смене привязки запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать два типа запроса scCase1 и scCase2</li>
     * <li>Добавить запрос sc типа scCase1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Нажимаем кнопку "Изменить привязку" ожидаем форму смены привязки</li>
     * <li>На форме смены привязки выбираем тип scCase2</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что тип запроса sc является scCase2</li>
     * </ol>
     */
    @Test
    public void testChangeAssociation()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase1, scCase2);

        Bo sc = DAOSc.create(scCase1);
        DSLBo.add(sc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIButtonBar.changeAssociation();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        GUISession.reconnectionWithApplyForm(tester, false);
        // Проверки
        DSLBo.assertType(sc, scCase2.getFqn());
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при смене типа БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать два типа отдела ouCase1 и ouCase2</li>
     * <li>Добавить отдел ou типа ouCase1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Нажимаем кнопку "Изменить тип" ожидаем форму смены типа</li>
     * <li>На форме смены типа выбираем тип ouCase2</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что тип отдела ou является ouCase2</li>
     * </ol>
     */
    @Test
    public void testChangeCase()
    {
        //Подготовка
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass ouCase2 = DAOOuCase.create();
        DSLMetaClass.add(ouCase1, ouCase2);

        Bo ou = DAOOu.create(ouCase1);
        DSLBo.add(ou);
        //Выполнение действия

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.changeCase();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, ouCase2.getFqn());
        GUISession.reconnectionWithApplyForm(tester, false);
        DAOBo.setCase(ou, ouCase2);
        GUIBo.assertThatBoCard(ou);
        ModelMap map = SdDataUtils.getObject(ouCase2.getFqn(), ModelUuid.UUID, ou.getUuid());
        Assert.assertEquals("Тип некорректен", ouCase2.getFqn(), map.get("metaClass"));
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при смене пароля сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать сотрудника со всеми правами employee</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под сотрудником</li>
     * <li>Переходим на карточку сотрудника employee</li>
     * <li>Нажимаем кнопку "Сменить пароль" ожидаем форму смены пароля</li>
     * <li>Вводим текущий пароль employee</li>
     * <li>На форме выбираем новый пароль</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что сотруднику employee установлен новый пароль (логаут и логинимся под employee испольуя новый
     * пароль)</li>
     * </ol>
     */
    @Test
    public void testChangePassword()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.changePassword();

        GUIForm.assertFormAppear(GUIXpath.Div.FORM);

        GUIPasswordForm.inputCurrentPassword(employee.getPassword());
        employee.setPassword(ModelUtils.createPassword());
        GUIPasswordForm.setNewPassword(employee.getPassword());
        //Дожидаемся окончания проверки
        GUIPasswordForm.assertIndicatorState(IndicatorState.VALID_RGB);
        GUIPasswordForm.confirmPassword(employee.getPassword());

        GUISession.reconnectionWithApplyForm(tester, false);
        GUILogon.logout();
        //Перелогиниваемся
        GUILogon.login(employee);
        GUIBo.assertThatBoCard(employee);
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при смене ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс (с ответственным) userClass и тип userCase</li>
     * <li>Создать команду team и сотрудника со всеми правами employee. Поместить сотрудника в команду.</li>
     * <li>Создать пользовательский БО userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под сотрудником</li>
     * <li>Переходим на карточку пользовательского БО userBo</li>
     * <li>Нажимаем кнопку "Изменить ответственного" ожидаем форму смены ответственного</li>
     * <li>На форме смены ответственного выбираем сотрудника employee в команде team</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что сотруднику employee из команды team ответственный за userBo</li>
     * </ol>
     */
    @Test
    public void testChangeResponsible()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setHasResponsible("true");
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(userClass, userCase);

        //Создаем объекты
        Bo team = SharedFixture.team();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        DSLTeam.addEmployees(team, employee);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действия
        GUILogon.asTester();

        GUIBo.goToCard(userBo);
        GUIButtonBar.changeResponsible();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUISelect.selectByXpath(
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.SpecificComplex.RESPONSIBLE_VALUE_INPUT,
                "//div[@id='employeeResponsibleStrategy:" + team.getUuid() + ":" + employee.getUuid() + "']");
        GUISession.reconnectionWithApplyForm(tester, false);
        //Проверки
        tester.waitAsyncCall(2);
        ModelMap map = SdDataUtils.getObject(userBo.getMetaclassFqn(), ModelUuid.UUID, userBo.getUuid());
        Assert.assertEquals("Некорректное значение атрибута ответственный", team.getTitle(),
                SdDataUtils.getMapValue(map, "responsibleTeam").get("title"));
        Assert.assertEquals("Некорректное значение атрибута ответственный", DSLEmployee.getFullName(employee),
                SdDataUtils.getMapValue(map, "responsibleEmployee").get("title"));
    }

    /**
     * Тестирование предотвращение потери введенных данных при обрыве сессии при смене статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс (с ЖЦ) userClass и тип userCase</li>
     * <li>В типе userCase создаем статус status. Настраиваем переходы: Registered -> status -> Closed</li>
     * <li>Создать пользовательский БО userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логинимся в системе под сотрудником</li>
     * <li>Переходим на карточку пользовательского БО userBo</li>
     * <li>Нажимаем кнопку "Сменить статус", ожидаем форму смены статуса</li>
     * <li>На форме смены статуса выбираем status</li>
     * <li>Обрываем сессию текущего пользователя</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Нажимаем, в всплывающем сообщении, "Остаться на этой странице"</li>
     * <li>Всплывающее сообщение исчезло</li>
     * <li>Востанавливаем сессию текущему пользователю</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <li>Проверяем, что userBo в статусе status</li>
     * </ol>
     */
    @Test
    public void testChangeState()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setHasResponsible("true");
        MetaClass userCase = DAOUserCase.create(userClass);

        DSLMetaClass.add(userClass, userCase);

        //Создаем статус
        BoStatus status = DAOBoStatus.createUserStatus(userCase.getFqn());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(userCase.getFqn()), status,
                DAOBoStatus.createClosed(userCase.getFqn()));

        //Создаем объекты
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUISc.openChangeStateFormAndSetState(status.getCode());
        GUISession.reconnectionWithApplyForm(tester, false);
        //Проверки
        tester.waitAsyncCall(2);
        ModelMap map = SdDataUtils.getObject(userBo.getMetaclassFqn(), ModelUuid.UUID, userBo.getUuid());
        Assert.assertEquals("Атрибут БО имеет другое значение.", status.getCode(), map.get("state"));
    }

}
