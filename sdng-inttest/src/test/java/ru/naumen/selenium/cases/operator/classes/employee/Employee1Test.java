package ru.naumen.selenium.cases.operator.classes.employee;

import static org.junit.Assert.assertEquals;
import static ru.naumen.selenium.casesutil.security.GUIPasswordForm.CHANGE_PASSWORD_ATTENTION;
import static ru.naumen.selenium.casesutil.user.GUILogon.CHANGE_PASSWORD_ERROR_USER_SUSPENDED;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIEmployee;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUIUserBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.GUISimpleList;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.rights.matrix.EmployeeActionsRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.security.DAOSecurityPolicy;
import ru.naumen.selenium.casesutil.security.DSLSecurityPolicy;
import ru.naumen.selenium.casesutil.security.GUIPasswordForm;
import ru.naumen.selenium.casesutil.security.SecurityPolicy;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityProfile;
import ru.naumen.selenium.security.UserGroup;
import ru.naumen.selenium.util.RandomUtils;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование сотрудника
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00053
 * <AUTHOR>
 * @since 25.01.2012
 */
public class Employee1Test extends AbstractTestCase
{
    /**
     * Тестирование отсутствие прав на смену пароля
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>В типе employeeCase созданному сотруднику employee запрещаем Смену пароля</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке сотрудника отсутствует кнопка "Сменить пароль"</li>
     * </ol>
     */
    @Test
    public void testAbsenceRightOfChangePassword()
    {
        // Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), false);
        RightGroup rights = new RightGroup(profile, employeeCase);
        rights.addAllRights(employeeCase).removeRight(employeeCase, EmployeeActionsRights.CHANGE_PASSWD);
        rights.apply();

        // Действие
        GUILogon.login(employee);

        // Проверка
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_PASSWD);
    }

    /**
     * Тестирование добавления сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00056
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип emplCase сотрудника</li>
     * <li>В типе emplCase создать группу атрибутов emplAttrGroup: в нее занести все системные атрибуты </li>
     * <li>В типе emplCase на форму добавления добавить изменить контент по умолчанию - установить группу атрибутов
     * emplAttrGroup</li>
     * <li>В типе emplCase добавить контент emplPropList Параметры объекта с группой атрибутов emplAttrGroup</li>
     * <li>В типе ouCase создать контент ouChildList со списком вложенных объектов типа emplCase(группа атрибутов
     * emplAttrGroup)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку отдела</li>
     * <li>В  контенте ouChildList нажимаем кнопку Добавить</li>
     * <li>На форме добавления сотрудника  вводим:</li>
     * <li>адрес почты, дату рождения, должность, имя, Логин, 4 номера телефона, Отчество, фамилия</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма добавления закрылась</li>
     * <li>В контенте ouChildList появился сотрудник: тип emplCase, адрес почты, дату рождения, должность, имя,
     * Логин, 4 номера телефона, Отчество, фамилия</li>
     * <li>На панели навигации появился сотрудник</li>
     * <li>Карточка созданного сотрудника:  тип emplCase, адрес почты, дату рождения, должность, имя, Логин, 4 номера
     * телефона, Отчество, фамилия</li>
     * </ol>
     */
    @Test
    public void testAdd()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);
        //Создаем группу атрибутов в типе сотрудника и добавляем в нее все системные атрибуты
        GroupAttr atrGroup = DAOGroupAttr.create(emplCase.getFqn());
        DSLGroupAttr.add(atrGroup, DSLGroupAttr.getEmployeeSystemAttr());
        //настраиваем форму добавления. 
        ContentForm contentAddForm = DAOContentAddForm.createEditablePropertyList(emplCase.getFqn(), atrGroup);
        DSLContent.add(contentAddForm);
        //Добавляем контент 
        ContentForm emplPropList = DAOContentCard.createPropertyList(emplCase, atrGroup);
        DSLContent.add(emplPropList);
        //Добавляем контент со списком вложенных объектов
        ContentForm ouChildList = DAOContentCard.createChildObjectList(ouCase.getFqn(), DAOEmployeeCase.createClass(),
                atrGroup, emplCase);
        DSLContent.add(ouChildList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));
        //Создаем отдел
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        //Выполнение действия
        GUILogon.asTester();
        Bo empl = DAOEmployee.create(emplCase, ou, true);
        GUIBo.goToCard(ou);
        GUISimpleList.clickAdd(ouChildList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getFirstName(), "firstName");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getLastName(), "lastName");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getPost(), "post");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getMiddleName(), "middleName");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getEmail(), "email");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, empl.getDateOfBirth(),
                "dateOfBirth");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getLogin(), "login");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getHomePhoneNumber(),
                "homePhoneNumber");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getMobilePhoneNumber(),
                "mobilePhoneNumber");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getInternalPhoneNumber(),
                "internalPhoneNumber");
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.ANY_VALUE, empl.getCityPhoneNumber(),
                "cityPhoneNumber");
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(empl);
        GUIBo.goToCard(ou);
        //Проверки
        //Подготавливаем атрибуты, для проверки полей
        Attribute[] attributeforAssert = { SysAttribute.firstName(emplCase).setValue(empl.getFirstName()),
                SysAttribute.lastName(emplCase).setValue(empl.getLastName()),
                SysAttribute.performer(emplCase).setValue("нет"),
                SysAttribute.title(emplCase).setValue(DSLEmployee.getFullName(empl)),
                SysAttribute.post(emplCase).setValue(empl.getPost()),
                SysAttribute.parentOu(ouCase).setValue(ou.getTitle()), SysAttribute.removed(emplCase).setValue("нет"),
                SysAttribute.login(emplCase).setValue(empl.getLogin()),
                SysAttribute.internalPhoneNumber(emplCase).setValue(empl.getInternalPhoneNumber()),
                SysAttribute.cityPhoneNumber(emplCase).setValue(empl.getCityPhoneNumber()),
                SysAttribute.homePhoneNumber(emplCase).setValue(empl.getHomePhoneNumber()),
                SysAttribute.mobilePhoneNumber(emplCase).setValue(empl.getMobilePhoneNumber()),
                SysAttribute.metaClass(emplCase).setValue(empl.getMetaclassTitle()),
                SysAttribute.uuid(emplCase).setValue(empl.getUuid())
        };
        GUISimpleList.assertSimpleListAttributeValue(ouChildList, empl, attributeforAssert);
        //Переходим на карточку сотрудника для проверки атрибутов
        GUIBo.goToCard(empl);
        GUIPropertyList.assertPropertyListAttribute(emplPropList, attributeforAssert);
    }

    /**
     * Тестирование заполнения атрибута "Лицензии" для наследующихся типов сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00385
     * http://sd-jira.naumen.ru/browse/NSDPRD-4070
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Установить лицензию с именованной группой</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип emplCase сотрудника</li>
     * <li>Создать тип emplCase1 сотрудника, наследуемый от типа emplCase</li>
     * <li>Создать тип emplCase11 сотрудника, наследуемый от типа emplCase1</li>
     * <li>В типе emplCase создать группу атрибутов attrGroup: в нее занести все системные атрибуты </li>
     * <li>Значение атрибута "Лицензия" по умолчанию для типа emplCase сделать равным "Нелицензированный
     * пользователь"</li>
     * <li>Значение атрибута "Лицензия" по умолчанию для типа emplCase1 сделать равным "Именная"</li>
     * <li>Значение атрибута "Лицензия" по умолчанию для типа emplCase11 сделать равным пустому</li>
     * <li>В типе emplCase на форму добавления добавить контент типа "Параметры на форме", по умолчанию - установить
     * группу атрибутов attrGroup</li>
     * <li>В типе ouCase создать контент ouChildList со списком вложенных объектов класса employee</li>
     * <li>Создаем отдел ou</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником в карточку отдела ou</li>
     * <li>В контенте ouChildList нажимаем кнопку Добавить</li>
     * <li>Выбираем тип emplCase</li>
     * <li>Значение атрибута "Лицензия" стало равным "Нелицензированный пользователь"</li>
     * <li>Выбираем тип emplCase1</li>
     * <li>Значение атрибута "Лицензия" стало равным "Именная"</li>
     * <li>Выбираем тип emplCase11</li>
     * <li>Значение атрибута "Лицензия" стало пустым</li>
     * </ol>
     */
    @Test
    public void testAddFormLicenseInheritance()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE8_PATH);

        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass emplCase1 = DAOEmployeeCase.create(emplCase);
        MetaClass emplCase11 = DAOEmployeeCase.create(emplCase1);
        DSLMetaClass.add(emplCase, emplCase1, emplCase11);

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase.getFqn());
        Attribute license = SysAttribute.license(emplCase);
        license.setDefaultValue(DAOBo.NO_LICENSE_STRING);
        DSLAttribute.edit(license);
        DSLGroupAttr.add(attrGroup, DSLGroupAttr.getEmployeeSystemAttr());

        Attribute license1 = SysAttribute.license(emplCase1);
        license1.setDefaultValue(DAOBo.NAMED_LICENSE_STRING);
        DSLAttribute.edit(license1);

        Attribute license11 = SysAttribute.license(emplCase11);
        license11.setDefaultValue(null);
        DSLAttribute.edit(license11);

        ContentForm contentAddForm = DAOContentAddForm.createEditablePropertyList(emplCase.getFqn(), attrGroup);
        DSLContent.add(contentAddForm);

        ContentForm ouChildList = DAOContentCard.createChildObjectList(ouCase.getFqn(), DAOEmployeeCase.createClass());
        DSLContent.add(ouChildList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUISimpleList.clickAdd(ouChildList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIForm.selectCase(emplCase);
        GUIMultiSelect.assertValuesPresentOnForm(license, "Нелицензированный пользователь");
        GUIMultiSelect.assertValuesNotPresentOnForm(license, "Именная");
        GUIForm.selectCase(emplCase1);
        GUIMultiSelect.assertValuesPresentOnForm(license1, "Именная");
        GUIMultiSelect.assertValuesNotPresentOnForm(license1, "Нелицензированный пользователь");
        GUIForm.selectCase(emplCase11);
        GUIMultiSelect.assertValuesNotPresentOnForm(license11, "Именная", "Нелицензированный пользователь");
    }

    /**
     * Тестирование добавления связи сотрудника и соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00102
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника emplCase</li>
     * <li>Создать тип соглашения agrCase</li>
     * <li>В типе emplCase создать контент emplRelObjectList со списком связанных объектов типа agrCase</li>
     * <li>Создать класс обслуживания serviceTimeItem</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника empl типа emplCase в отделе ou</li>
     * <li>Создать соглашение agr типа agrCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника empl</li>
     * <li>На контенте emplRelObjectList нажимаем "Добавить связь"</li>
     * <li>Выбираем соглашение agr</li>
     * <li>Нажимаем сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка соглашения: в контенте agrPropList появилось ФИО сотрудника </li>
     * <li>Карточка сотрудника: в контенте emplPropList появилось соглашение</li>
     * </ol>
     */
    @Test
    public void testAddRelationToAgreement()
    {
        //Подготовка
        //Добавляем системные типы классов "Отдел", "Сотрудник", "Соглашение"
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(emplCase, agrCase);

        //Добавляем контенты в типы
        ContentForm emplRelObjectList = DAOContentCard.createRelatedObjectList(emplCase.getFqn(),
                String.format("%s@recipientAgreements", emplCase.getFqn()), agrCase);
        DSLContent.add(emplRelObjectList);
        //Добавляем класс обслуживания
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        //Добавляем отдел, сотрудника, соглашение
        Bo empl = DAOEmployee.create(emplCase, SharedFixture.ou(), true);
        Bo agr = DAOAgreement.create(agrCase, serviceTimeItem, serviceTimeItem);
        DSLBo.add(empl, agr);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUISimpleList.clickAddRelation(emplRelObjectList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIMultiSelect.select(
                GUIXpath.Div.FORM
                + "//*[@id='gwt-debug-container']//*[@id='gwt-debug-recipientAgreements-value']//input",
                agr.getUuid());
        GUIForm.applyForm();
        //Проверки
        tester.waitAsyncCall(2);
        ModelMap employeeMap = SdDataUtils.getObject(empl.getMetaclassFqn(), ModelUuid.UUID, empl.getUuid());
        assertEquals("Между сотрудником и соглашением нет связи", agr.getTitle(),
                SdDataUtils.getMapsArrayValue(employeeMap, "recipientAgreements").get(0).get("title"));

        tester.waitAsyncCall(2);
        ModelMap agreementMap = SdDataUtils.getObject(agr.getMetaclassFqn(), ModelUuid.UUID, agr.getUuid());
        assertEquals("Между сотрудником и соглашением нет связи", DSLEmployee.getFullName(empl),
                SdDataUtils.getMapsArrayValue(agreementMap, "recipients").get(0).get("title"));
    }

    /**
     * Тестирование архивирования сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел А типа В</li>
     * <li>Создать сотрудника E типа C</li>
     * <li>В типе В создать контент D со списком вложенных объектов типа С</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника E</li>
     * <li>Нажимаем кнопку Поместить в архив </li>
     * <li>Подтверждаем действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке вложенных сотрудников отдела А нет сотрудника E</li>
     * <li>В списке заархивированных вложенных сотрудников отдела А есть сотрудник E</li>
     * <li>На карточке сотрудника E нет кнопок: добавить запрос. переместить, редактировать, сменить пароль. Есть
     * кнопка Восстановить из Архива.</li>
     * <li>На карточке сотрудника E логин сброшен</li>
     * <li>Присутствует дата архивирования. День сегодняшний</li>
     * </ol>
     */
    @Test
    public void testArchive()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        ContentForm content = DAOContentCard.createChildObjectList(ouCase.getFqn(), DAOEmployeeCase.createClass(),
                employeeCase);
        DSLContent.add(content);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIBo.archive(employee);
        //Проверки
        GUIBo.goToCard(ou);
        GUISimpleList.assertObjectAbsence(content, employee);
        GUISimpleList.showArchive(content);
        GUISimpleList.assertObjectPresent(content, employee);
        DSLBo.assertArchived(employee);

        GUIBo.goToCard(employee);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_RESTORE);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_ADD_SC, GUIButtonBar.BTN_MOVE, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_CHANGE_PASSWD);
    }

    /**
     * Тестирование изменения типа сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы сотрудника employeeCase1..2</li>
     * <li>В employeeCase2 создать атрибут stringAtr1</li>
     * <li>В employeeCase2 создать атрибут stringAtr2, обязательный</li>
     * <li>В employeeCase2 cоздать контент c атрибутами "Тип объекта", stringAtr1, stringAtr2 </li>
     * <li>Cоздать сотрудника employee типа employeeCase1</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку employee, Нажимаем кнопку Изменение типа</li>
     * <li>Проверяем на форме, что текущий тип employeeCase1</li>
     * <li>Проверяем на форме, что в вариантах выбора нового типа нет текущего</li>
     * <li>Выбираем новый тип employeeCase2</li>
     * <li>Проверяем на форме, что появились поля ввода для заполнения stringAtr1, stringAtr2</li>
     * <li>Заполняем stringAtr1, stringAtr2</li>
     * <li>Подтверждаем.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Значение атрибута Тип сотрудника изменилось</li>
     * <li>Значения атрибутов stringAtr1, stringAtr2 заполнены корректно</li>
     * </ol>
     */
    @Test
    public void testChangeCase()
    {
        //Подготовка
        MetaClass emploueeCase1 = DAOEmployeeCase.create();
        MetaClass employeeCase2 = DAOEmployeeCase.create();
        DSLMetaClass.add(emploueeCase1, employeeCase2);

        Attribute stringAtr1 = DAOAttribute.createString(employeeCase2.getFqn());
        Attribute stringAtr2 = DAOAttribute.createString(employeeCase2.getFqn());
        stringAtr2.setRequired(String.valueOf(true));
        DSLAttribute.add(stringAtr1, stringAtr2);

        Bo employee = DAOEmployee.create(emploueeCase1, SharedFixture.ou(), true);
        DSLBo.add(employee);

        //Выполнение действия
        stringAtr1.setValue(UniqueRandomStringUtils.stringEnNumRuSpace(RandomUtils.nextInt(255) + 1));
        stringAtr2.setValue(UniqueRandomStringUtils.stringEnNumRuSpace(RandomUtils.nextInt(255) + 1));

        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.changeCase();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        assertEquals("Неверно указан текущий тип сотрудника", emploueeCase1.getTitle(),
                tester.getText(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Div.OLD_CASE_PROPERTY_CAPTION + "//span"));
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, employeeCase2.getFqn());
        Assert.assertTrue("Первый атрибут, который должен появиться при смене типа не появился на форме смены типа",
                tester.waitAppear(
                        GUIXpath.Div.FORM_CONTAINS + "//input[@id='gwt-debug-" + stringAtr1.getCode() + "-value']"));
        Assert.assertTrue("Второй атрибут, который должен появиться при смене типа не появился на форме смены типа",
                tester.waitAppear(
                        GUIXpath.Div.FORM_CONTAINS + "//input[@id='gwt-debug-" + stringAtr2.getCode() + "-value']"));
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + "//input[@id='gwt-debug-" + stringAtr1.getCode() + "-value']",
                stringAtr1.getValue());
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + "//input[@id='gwt-debug-" + stringAtr2.getCode() + "-value']",
                stringAtr2.getValue());
        GUIForm.applyForm();
        //Проверки
        employee.setMetaclassFqn(employeeCase2.getFqn());
        tester.waitAsyncCall(2);
        ModelMap map = SdDataUtils.getObject(employee.getMetaclassFqn(), ModelUuid.UUID, employee.getUuid());
        assertEquals("Тип некорректен", employeeCase2.getFqn(), map.get("metaClass"));
        assertEquals("Значение атрибута неверно", stringAtr1.getValue(), map.get(stringAtr1.getCode()));
        assertEquals("Значение атрибута неверно", stringAtr2.getValue(), map.get(stringAtr2.getCode()));
    }

    /**
     * Тестирование на наличие проверки при смены лицензии у сотрудника на отвественность за объект NSDPRD-635
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00447
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел ou</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника employee с лицензией concurrent</li>
     * <li>Добавить сотрудника employee в команду</li>
     * <li>Создать пользовательский объект userBo</li>
     * <li>Создать группу атрибутов attrGroup с атрибутом Лицензия</li>
     * <li>В типе employeeCase создать контент contentEditForm На форме редактирования, типа "Параметры объекта"
     * (группа атрибутов attrGroup).</li>
     * <li>Сделать сотрудника ответственным за userBo</li>
     * <br/>
     * <b>Выполнение</b>
     * <li>Перейти в карточку сотрудника</li>
     * <li>Нажать кнопку Редактировать</li>
     * <li>Выставить лицензию "notLicensed"</li>
     * <li>Нажать сохранить</li>
     * <br/> 
     * <b>Проверки</b>
     * <li>Появилось сообщение "Сотрудник 'employee' не может быть изменен. Сотрудник является ответственным за
     * объекты: userClass 'userBo'"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseFromResponsibleEmployee()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();
        DSLMetaClass.add(userClass, userCase);

        //Создаем группу атрибутов в типе сотрудника и добавляем в нее все системные атрибуты
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, DSLGroupAttr.getEmployeeSystemAttr());
        //настраиваем форму добавления. 
        ContentForm contentEditForm = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(contentEditForm);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));

        //Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        DSLTeam.addEmployees(team, employee);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIUserBo.setResponsibleEmployee(employee, team);

        //Выполнение действия
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        //Проверки
        String expected = String
                .format(ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                        + String.format(ErrorMessages.IS_RESPONSIBLE_WITHIN_TEAM, team.getTitle(), employee.getTitle())
                        + "%s '%s'", employee.getTitle(), userClass.getTitle(), userBo.getTitle());

        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование смены пароля у сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee</li>
     * <li>Установить сотруднику employee пароль 123</li>
     * <li>Добавить сотрудника employee в группу пользователей userGroup</li>
     * <li>Для типа сотрудника employeeCase настроить профиль profile (Роль = Сотрудник, Для нелицензированных)</li>
     * <li>Выдать все права типу employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Изменить пароль на 321</li>
     * <li>Нажать "Сохранить", система автоматически разлогинила</li>
     * <li>Войти в систему с паролем 123</li>
     * <li>Появилось сообщение: "Невозможно войти в систему. Неправильные имя пользователя и/или пароль. 
     * Попробуйте повторить ввод."</li>
     * <li>Войти в систему с паролем 321</li>
     * <li>Находимся на карточке сотрудника employee<li>
     * </ol>
     */
    @Test
    public void testChangePasswordInEmployee()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();

        String firstPassword = ModelUtils.createPassword();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        employee.setPassword(firstPassword);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), false);
        RightGroup rights = new RightGroup(profile, employeeCase);
        rights.addAllRights(employeeCase);
        rights.apply();

        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIButtonBar.changePassword();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM);
        tester.sendKeys(Input.CURRENT_PASSWORD_VALUE, employee.getPassword());
        employee.setPassword(ModelUtils.createPassword());
        tester.sendKeys(GUIXpath.Input.CP_FORM_PASSWORD_VALUE, employee.getPassword());
        tester.sendKeys(GUIXpath.Input.CP_FORM_DUPLICATE_PASSWORD_VALUE, employee.getPassword());
        tester.actives().pressingTabKey();
        GUIForm.clickApply();

        GUILogon.tryLoginAssertError(employee.getLogin(), firstPassword, GUILogon.LOGIN_ERROR_BY_USER);
        GUILogon.login(employee);
        GUIBo.assertThatBoCard(employee);
    }

    /**
     * Тестирование удаления сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать отдел А типа В</li>
     * <li>Создать сотрудника E типа C</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника E. Нажимаем кнопку Удалить на панели инструментов</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * Проверяем, что отдел c UUID-ом удаленного отдела отсутствует в системе
     * </ol>
     */
    @Test
    public void testDelete()
    {
        //Подготовка
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);
        Bo empl = DAOEmployee.create(emplCase, SharedFixture.ou(), true);
        DSLBo.add(empl);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUIBo.delete(empl);

        //Проверки
        DSLBo.assertAbsence(empl);
    }

    /**
     * Тестирование удаления связи сотрудника и соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00103
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника emplCase</li>
     * <li>Создать тип соглашения agrCase</li>
     * <li>В типе emplCase создать группу атрибутов emplSysAtr, куда занести все системные атрибуты типа сотрудника</li>
     * <li>В типе agrCase создать группу атрибутов agrSysAtr, куда занести все системные атрибуты типа соглашения</li>
     * <li>В типе emplCase создать контент emplPropList параметры объекта. (группа атрибутов: emplSysAtr)</li>
     * <li>В типе emplCase создать контент emplRelObjectList со списком связанных объектов типа agrCase</li>
     * <li>В типе agrCase создать контент agrPropList параметры объекта. (группа атрибутов: agrSysAtr)</li>
     * <li>Создать класс обслуживания serviceTimeItem</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника empl типа emplCase в отделе ou</li>
     * <li>Создать соглашение agr типа agrCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку сотрудника empl</li>
     * <li>На контенте emplRelObjectList нажимаем писктограмму "Разорвать связь" напротив соглашения agr</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка сотрудника: в контенте emplPropList отсутствует соглашение</li>
     * <li>Карточка соглашения: в контенте agrPropList отсутствует ФИО сотрудника </li>
     * </ol>
     */
    @Test
    public void testDeleteRelationToAgreement()
    {
        //Добавляем системные типы классов "Отдел", "Сотрудник", "Соглашение"
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(emplCase, agrCase);

        //Добавляем контенты в типы
        ContentForm emplRelObjectList = DAOContentCard.createRelatedObjectList(emplCase.getFqn(),
                String.format("%s@recipientAgreements", emplCase.getFqn()), agrCase);
        DSLContent.add(emplRelObjectList);
        //Добавляем класс обслуживания
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        //Добавляем отдел, сотрудника, соглашение
        Bo empl = DAOEmployee.create(emplCase, SharedFixture.ou(), true);
        Bo agr = DAOAgreement.create(agrCase, serviceTimeItem, serviceTimeItem);
        DSLBo.add(empl, agr);
        //Связываем соглашение с сотрудником
        DSLAgreement.addToRecipients(agr, empl);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(empl);
        GUISimpleList.deleteRelation(emplRelObjectList, agr);
        //Проверки
        tester.waitAsyncCall(2);
        ModelMap mapEmployee = SdDataUtils.getObject(empl.getMetaclassFqn(), ModelUuid.UUID, empl.getUuid());
        assertEquals("Непустой список соглашений", "[]", mapEmployee.get("recipientAgreements"));
        ModelMap mapAgreement = SdDataUtils.getObject(agr.getMetaclassFqn(), ModelUuid.UUID, agr.getUuid());
        assertEquals("Непустой список сотрудников получателей", "[]", mapAgreement.get("recipients"));
    }

    /**
     * Тестирование невозможности добавления сотрудника без лицензии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00385
     * http://sd-jira.naumen.ru/browse/NSDPRD-4070
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Установить лицензию с именованной группой</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать тип emplCase сотрудника</li>
     * <li>В типе emplCase создать группу атрибутов attrGroup: в нее занести все системные атрибуты </li>
     * <li>Значение атрибута "Лицензия" по умолчанию сделать равным пустому</li>
     * <li>В типе emplCase на форму добавления добавить контент типа "Параметры на форме", по умолчанию - установить
     * группу атрибутов attrGroup</li>
     * <li>В типе ouCase создать контент ouChildList со списком вложенных объектов типа emplCase(группа атрибутов
     * attrGroup)</li>
     * <li>Создаем отдел ou</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником в карточку отдела ou</li>
     * <li>В контенте ouChildList нажимаем кнопку Добавить</li>
     * <li>На форме добавления сотрудника вводим Фамилию (обязательное поле)</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма добавления не закрылась</li>
     * <li>Появилось всплывающее окно "Должно быть выбрано хотя бы одно значение"</li>
     * </ol>
     */
    @Test
    public void testTryingToAddWithoutLicense()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE8_PATH);

        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);

        GroupAttr attrGroup = DAOGroupAttr.create(emplCase.getFqn());
        Attribute license = SysAttribute.license(emplCase);
        license.setDefaultValue(null);
        DSLAttribute.edit(license);
        DSLGroupAttr.add(attrGroup, DSLGroupAttr.getEmployeeSystemAttr());

        ContentForm contentAddForm = DAOContentAddForm.createEditablePropertyList(emplCase.getFqn(), attrGroup);
        DSLContent.add(contentAddForm);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        ContentForm ouChildList = DAOContentCard.createChildObjectList(ouCase.getFqn(), DAOEmployeeCase.createClass(),
                attrGroup, emplCase);
        DSLContent.add(ouChildList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.asTester();
        Bo empl = DAOEmployee.create(emplCase, ou, true);
        GUIBo.goToCard(ou);
        GUISimpleList.clickAdd(ouChildList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIBo.fillEmployeeMainFields(empl);

        //Проверки
        GUIForm.applyFormAssertValidation(GUIXpath.Div.FORM_CONTAINS,
                ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
    }

    /**
     * Тестирование появления сообщения об ошибке при попытке добавления сотрудника с неуникальным логином и заполненным
     * атрибутом "Группы пользователей сотрудника"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00319
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00054
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$201873810
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить тип emplCase класса Сотрудник</li>
     * <li>У атрибута "Группы пользователей сотрудника" в типе emplCase включить редактирование</li>
     * <li>Добавить атрибуты "Группы пользователей сотрудника" и "Логин" в системную группу атрибутов типа emplCase</li>
     * <li>Создать группы пользователей group</li>
     * <li>Создать отдел ou</li>
     * <li>Создать сотрудника employee типа emplCase, вложенного в отдел ou</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИО под сотрудником</li>
     * <li>Перейти на форму добавления сотрудника</li>
     * <li>Заполнить поля: Фамилия, Имя, Отчество</li>
     * <li>Выбрать группу пользователей group из списка</li>
     * <li>Заполнить поле Логин значением равным логину пользователя employee</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что сообщение от ошибки соответствует тексту: "Атрибут Логин должен быть уникальным. Объект
     * Сотрудник с таким значением атрибута уже существует: {Фамилия} {Имя} {Отчество}."</li>
     * </ol>
     */
    @Test
    public void testTryAddEmplWithNotUniqueLoginAndFillSecGroup()
    {
        //Подготовка
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);

        Attribute attribute = SysAttribute.employeeSecGroups(emplCase);
        attribute.setEditable(Boolean.TRUE.toString());
        DSLAttribute.edit(attribute);

        GroupAttr systemGroup = DAOGroupAttr.createSystem(emplCase);
        DSLGroupAttr.edit(systemGroup, new Attribute[] { attribute, SysAttribute.login(emplCase) }, new Attribute[0]);
        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        Bo ou = SharedFixture.ou();
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(employee);

        //Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOEmployeeCase.createClass().getFqn(), ou.getUuid(), emplCase.getFqn());

        GUIEmployee.fillSurname(ModelUtils.createTitle());
        GUIEmployee.fillName(ModelUtils.createTitle());
        GUIEmployee.fillMiddleName(ModelUtils.createTitle());
        GUIEmployee.selectSecGroups(group.getCode());
        GUIEmployee.fillLogin(employee.getLogin());
        GUIForm.applyFormAssertError(String.format(
                "Атрибут Логин должен быть уникальным. Объект Сотрудник с таким значением атрибута уже существует: %s"
                + " %s %s.", employee.getLastName(), employee.getFirstName(), employee.getMiddleName()));
    }

    /**
     * Тестирование временной блокировки пользователя при вводе некорректного Текущего пароля на модальной форме
     * смены пароля
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00144
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$137259816
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Настроить и включить политику безопасности:
     *      - "Количество неудачных попыток входа в систему без временной блокировки учетной записи" = 2",
     *      - "Время блокировки учетной записи между неудачными попытками входа в систему (сек)" = "3",
     *      - "Максимальное количество неудачных попыток входа в систему" = "3". </li>
     * <li>Создать сотрудника employee</li>
     * <li>Добавить сотрудника employee в группу пользователей userGroup</li>
     * <li>Для типа сотрудника employeeCase настроить профиль profile (Роль = Сотрудник, Для нелицензированных)</li>
     * <li>Выдать все права типу employeeCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Нажать на кнопку "Сменить пароль"</li>
     * <li>Появилась форма смены пароля с предупреждением:
     * "Внимание! Пароль изменится только для аутентификации внутри приложения."</li>
     * <li>Ввести текущий пароль неверно и нажать Tab</li>
     * <li>Появилась ошибка валидации: "Неверный пароль. Для изменения пароля необходимо ввести корректный текущий
     * пароль."</li>
     * <li>Ввести текущий пароль неверно ещё раз и нажать Tab</li>
     * <li>Система автоматически разлогинила и нас перебросило на форму авторизации со следующим сообщением:
     * "Невозможно изменить пароль. Ваша учетная запись временно заблокирована в приложении. Попробуйте повторить
     * смену пароля позже."</li>
     * </ol>
     */
    @Test
    public void testChangePasswordAndSuspendEmployee()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();

        SecurityPolicy policy = DAOSecurityPolicy.create();
        policy.setEnabled(true);
        policy.setMaxFailedTriesBeforeSuspension(2);
        policy.setSuspensionTime(3);
        policy.setMaxFailedTries(3);
        DSLSecurityPolicy.edit(policy);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        SecurityProfile profile = new SecurityProfile(userGroup, new EmployeeRole(), false);
        RightGroup rights = new RightGroup(profile, employeeCase);
        rights.addAllRights(employeeCase);
        rights.apply();

        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIButtonBar.changePassword();
        GUIForm.assertFormAppear();
        GUIForm.assertAttention(CHANGE_PASSWORD_ATTENTION);
        GUIPasswordForm.inputCurrentPassword("try1");
        tester.actives().pressingTabKey();
        GUIPasswordForm.inputCurrentPassword("try2");
        tester.actives().pressingTabKey();

        GUILogon.assertLoginPage();
        GUILogon.assertErrorMessageOnLoginForm(CHANGE_PASSWORD_ERROR_USER_SUSPENDED);
    }
}