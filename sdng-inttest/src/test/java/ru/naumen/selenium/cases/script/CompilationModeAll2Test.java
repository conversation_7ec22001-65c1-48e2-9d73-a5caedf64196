package ru.naumen.selenium.cases.script;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;

/**
 * Тесты на компиляцию скриптовых модулей в режиме ALL
 *
 * <AUTHOR>
 * @since 16.01.2025
 */
@IgnoreConfig(cause = "Тесты работают только в режиме компиляции all",
        compilationMode = "oneByOne")
class CompilationModeAll2Test extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    /**
     * Тестирование применения изменений модуля, который имеет родителя (класс модуля, от которого он зависит),
     * при выключенном построении зависимостей между модулями
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$287313338 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00621 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выключить функционал построения зависимостей для режима компиляции ALL
     * (ru.naumen.script_modules.compilation.all.useModuleDependencies=false)</li>
     * <li>Создать модуль abstractQueryGetter с текстом:<pre>
     *     package ru.naumen.modules.test
     *     abstract class AbstractQueryGetter {
     *         String test() {
     *             return 'INITIAL'
     *         }
     *     }</pre></li>
     * <li>Создать модуль defaultQueryGetter с текстом:<pre>
     *     package ru.naumen.modules.test
     *     class DefaultQueryGetter extends AbstractQueryGetter {
     *     }</pre></li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт: <pre>
     *     import ru.naumen.modules.test.DefaultQueryGetter
     *     return new DefaultQueryGetter().test()</pre>
     * </li>
     * <li>Проверить, что скрипт вернул сообщение "INITIAL"</li>
     * <li>Отредактировать модуль abstractQueryGetter - поменять возвращаемое значение с INITIAL на EDITED.
     *     Должно получиться так:<pre>
     *     package ru.naumen.modules.test
     *     abstract class AbstractQueryGetter {
     *         String test() {
     *             return 'EDITED'
     *         }
     *     }</pre></li>
     * <li>Снова выполнить скрипт:<pre>
     *     import ru.naumen.modules.test.DefaultQueryGetter
     *     return new DefaultQueryGetter().test()</pre>
     * </li>
     * <li>Проверить, что скрипт вернул сообщение "EDITED"</li>
     * </ol>
     */
    @Test
    void testEditModuleWithParentWithoutUseModuleDependencies()
    {
        // Подготовка
        setUseModuleDependenciesForCompileAllMode(false);

        String textForEdit = ModelUtils.createCode();
        ModuleConf abstractQueryGetter = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                abstract class AbstractQueryGetter {
                    String test() {
                        return 'INITIAL'
                    }
                }""");
        ModuleConf defaultQueryGetter = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                class DefaultQueryGetter extends AbstractQueryGetter {
                }""");
        DSLModuleConf.add(abstractQueryGetter, defaultQueryGetter);

        // Действия и проверки
        String script = """
                import ru.naumen.modules.test.DefaultQueryGetter
                return new DefaultQueryGetter().test()""";
        Assertions.assertEquals("INITIAL", ScriptRunner.executeScript(script));

        abstractQueryGetter.setScriptBody(
                abstractQueryGetter.getScriptBody().replace(
                        "return 'INITIAL'", "return '%s'".formatted(textForEdit)));
        DSLModuleConf.edit(abstractQueryGetter);
        Assertions.assertEquals(textForEdit, ScriptRunner.executeScript(script));
    }

    /**
     * Тестирование редактирования дочернего модуля, после редактирования родительского модуля,
     * при выключенном построении зависимостей между модулями
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$287313338 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00621 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выключить функционал построения зависимостей для режима компиляции ALL
     * (ru.naumen.script_modules.compilation.all.useModuleDependencies=false)</li>
     * <li>Создать модуль parentModule с текстом:<pre>
     *     package ru.naumen.modules.test
     *     abstract class ParentClass {
     *         abstract void test()
     *         abstract String test2()
     *     }</pre></li>
     * <li>Создать дочерний модуль childModule с текстом:<pre>
     *     package ru.naumen.modules.test
     *     class ChildClass extends ParentClass {
     *        void test() {}
     *        String test2() {
     *          return "methodResult"
     *        }
     *     }</pre></li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Отредактировать модуль parentModule - удалить метод test().
     *     Должно получиться так:<pre>
     *     package ru.naumen.modules.test
     *     abstract class ParentClass {
     *         abstract String test2()
     *     }</pre></li>
     * <li>Отредактировать модуль childModule - удалить метод test().
     *     Должно получиться так:<pre>
     *     package ru.naumen.modules.test
     *     class ChildClass extends ParentClass {
     *        String test2() {
     *          return "methodResult"
     *        }
     *     }</pre></li>
     * <li>Выполнить скрипт:<pre>
     *     new ru.naumen.modules.test.ChildClass().test2()</pre>
     * </li>
     * <li>Проверить, что скрипт вернул сообщение "methodResult"</li>
     * </ol>
     */
    @Test
    void testEditChildClassAfterEditParentModuleWithoutUseModuleDependencies()
    {
        // Подготовка
        setUseModuleDependenciesForCompileAllMode(false);

        ModuleConf parentModule = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                abstract class ParentClass {
                    abstract void test()
                    abstract String test2()
                }""");
        String methodResult = ModelUtils.createCode();
        ModuleConf childModule = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                class ChildClass extends ParentClass {
                   void test() {}
                   String test2() {
                     return "%s"
                   }
                }""".formatted(methodResult));
        DSLModuleConf.add(parentModule, childModule);

        // Действия и проверки
        // Из модуля parentModule удалить метод test()
        parentModule.setScriptBody(parentModule.getScriptBody().replace("abstract void test()", ""));
        DSLModuleConf.edit(parentModule);
        // Удаляем метод test() из модуля childModule
        childModule.setScriptBody(childModule.getScriptBody().replace("void test() {}", ""));
        DSLModuleConf.edit(childModule);

        Assertions.assertEquals(methodResult,
                ScriptRunner.executeScript("new ru.naumen.modules.test.ChildClass().test2()"));
    }

    /**
     * Тестирование выключения ВП, модуль которого используется в обычном пользовательском модуле<br>
     * Исходная проблема:
     * <a href="https://naupp.naumen.ru/sd/operator/#uuid:domesticsup$285598411">ASK-71128 Не удаётся выключить ВП Портал</a><br>
     * <b>ВАЖНО:</b> проблема пока не исправлена, просто зафиксировано текущее поведение<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00496 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00621 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$291022214 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать встроенное приложение embeddedApplication с модулем embeddedAppModule<pre>
     *     def getData() {
     *         '"text"'
     *     }</pre></li>
     * <li>Создать модуль module, который использует модуль ВП<pre>
     *     package ru.naumen.modules.test
     *     modules.embeddedApplication_embeddedAppModule.getData()</pre></li>
     * <b>Действия и проверки</b>
     * <li>Выключить приложение embeddedApplication</li>
     * <li>Проверить, что приложение не выключилось и произошла ошибка:
     *     "Приложение 'embeddedApplication' не может быть выключено по следующим причинам:
     *      - Ресурсы модулей используются в других модулях: 'module'"</li>
     * </ol>
     */
    @SuppressWarnings("java:S2699") // Все проверки в методе trySwitchApplication
    @Test
    void testSwitchOffEmbeddedApplicationWithModuleUsedInUserModule()
    {
        // Подготовка
        ModuleConf embeddedAppModule = DAOEmbeddedApplication.createScriptModuleForApplication();
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createClientSideApplication(
                temp, embeddedAppModule);
        DSLEmbeddedApplication.add(embeddedApplication);

        ModuleConf module = DAOModuleConf.createWithBody("""
                package ru.naumen.modules.test
                modules.%s.getData()""".formatted(embeddedAppModule.getCode()));
        DSLModuleConf.add(module);

        // Действия и проверки
        DSLEmbeddedApplication.trySwitchApplication(embeddedApplication, false, """
                Приложение '%s' не может быть выключено по следующим причинам:
                - Ресурсы модулей используются в других модулях: '%s'"""
                .formatted(embeddedApplication.getTitle(), module.getCode()));
        /* Как должно быть:
           В идеале вообще должно быть запрещено обращаться из обычных модулей к модулям ВП
           либо
           1) Приложение должно выключиться нормально
           2) При выполнении скрипта modules.module.run() должна произойти ошибка из-за того,
              что модуль embeddedAppModule не найден, т.к. он был удалён при выключении встройки
              ("NullPointerException, message: Cannot invoke method getData() on null object")
        */
    }

    /**
     * Тестирование изменения скриптов модулей встроенного приложения, когда экземпляр класса одного модуля
     * используется в другом <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00621 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$171304132 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать скриптовый модуль moduleWithClass</li>
     * <li>Создать скриптовый модуль moduleUsingClass, который использует класс, определённый в moduleWithClass</li>
     * <li>Создать встроенное приложение application, исполняемого на клиенте, со скриптовыми модулями
     *     moduleWithClass, moduleUsingClass</li>
     * <b>Действия и проверки</b>
     * <li>Отредактировать модуль moduleUsingClass проставив версию "2"</li>
     * <li>Модуль должен сохраниться без ошибок</li>
     * </ol>
     */
    @SuppressWarnings("java:S2699") // assert'ы не нужны
    @Test
    void testEditModuleWithDependencies()
    {
        // Подготовка
        ModuleConf moduleWithClass = DAOModuleConf.createWithBody("""
                package ru.naumen.compile.test
                class CompileTest {}""");
        ModuleConf moduleUsingClass = DAOModuleConf.createWithBody("""
                package ru.naumen.compile.test
                def getData() {
                    def ctp = new CompileTest()
                }"""
        );
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(
                temp, moduleUsingClass, moduleWithClass);
        DSLEmbeddedApplication.add(application);

        // Действия и проверки
        moduleUsingClass.setModuleVersion("2");
        DSLModuleConf.edit(moduleUsingClass);
    }

    /**
     * Установить значение параметра "Использовать построение зависимостей между модулями
     * для компиляции в режиме ALL"
     * @param enabled включить или выключить построение зависимостей
     */
    @SuppressWarnings("SameParameterValue")
    private static void setUseModuleDependenciesForCompileAllMode(boolean enabled)
    {
        String scriptPattern = "beanFactory.getBean('configurationProperties')"
                               + ".setUseModuleDependenciesForCompileAllMode(%b)";
        ScriptRunner.executeScript(scriptPattern, enabled);
        Cleaner.afterTest(() -> ScriptRunner.executeScript(scriptPattern, Boolean.TRUE));
    }
}
