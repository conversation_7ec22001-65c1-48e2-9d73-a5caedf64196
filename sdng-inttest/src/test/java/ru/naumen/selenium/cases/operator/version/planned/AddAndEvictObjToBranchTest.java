package ru.naumen.selenium.cases.operator.version.planned;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLBranch;
import ru.naumen.selenium.casesutil.bo.DSLBranch.AttributePolicy;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBranchCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Добавление объектов в плановую версию и исключение объектов из плановой версии
 *
 * <AUTHOR>
 * @since 25.05.2021
 */
public class AddAndEvictObjToBranchTest extends AbstractTestCase
{
    private static MetaClass branchCase;
    private static MetaClass userClass;
    private static MetaClass userCase;
    private static MetaClass employeeCase;
    private static Bo testBranch;
    private static Bo employee;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Установить лицензию с модулем версионирования</li>
     * <li>В классе Ветка создать тип branchCase</li>
     * <li>Создать лицензированного пользователя testEmpl со всеми доступными правами в основном режиме, и в режиме
     * версионирования. Убедиться, что в классе Ветка сотруднику доступен переход в режим планирования.</li>
     * <li>Создать класс userClass, тип userCase, разрешить создание плановых версий = true</li>
     * <li>Глубина уровней окружения = 3. (для этого нужно задать в dbaccess параметр ru.naumen.version.planned.max
     * .depth.environment=3, изменение в рантайме beanFactory.getBean('plannedVersionConfigurationImpl')
     * .setMaxDepthEnvironment(3)</li>
     * <li>Создать объект класса Ветка с именем testBranch.</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.PLANNED_VERSION_LICENSE);

        branchCase = DAOBranchCase.create();
        userClass = DAOUserClass.create();
        userClass.setPlannedVersionsAllowed(true);
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(branchCase, userClass, userCase);

        final String setMaxDepthEnvFormat = "beanFactory.getBean('plannedVersionConfigurationImpl')"
                                            + ".setMaxDepthEnvironment(%s)";
        final String oldMaxDepthEnv = new ScriptRunner(
                "beanFactory.getBean('plannedVersionConfigurationImpl').getMaxDepthEnvironment()").runScript().get(0);
        new ScriptRunner(String.format(setMaxDepthEnvFormat, 3)).runScript();
        Cleaner.afterTest(() -> new ScriptRunner(String.format(setMaxDepthEnvFormat, oldMaxDepthEnv)).runScript());

        testBranch = DAOBranch.create(branchCase);
        testBranch.setTitle("testBranch");
        DSLBo.add(testBranch);

        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true, true);
        DSLBo.add(employee);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee);

        SecurityProfile profile = DAOSecurityProfile.create(true, group, SysRole.employee());
        profile.setIsVersioning("true");
        profile.setTitle(SharedFixture.secProfileVers().getTitle() + "_test");
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userClass);
    }

    /**
     * Тестирование добавления объекта методом с использованием переменной current
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект testBranch2 класса Ветка</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Залогиниться под employee.</li>
     * <li>Переключиться в режим планирования на ветку testBranch.</li>
     * <li>В режиме планирования создать объект testBranchObj класса userClass.</li>
     * <li>Добавить объект testBranchObj в ветку testBranch2</li>
     * <li>Проверить, что объект testBranchObj присутствует в ветке testBranch2, ошибок при выполнении метода не
     * упало.</li>
     * </ol>
     */
    @Test
    public void testAddObjectFromBranchToOtherBranch()
    {
        /** Подготовка */
        Bo testBranch2 = DAOBranch.create(branchCase);
        testBranch2.setTitle("testBranch2");
        DSLBo.add(testBranch2);

        /** Выполнение действий и проверки */
        GUILogon.login(employee);
        GUIPlannedVersion.goToBranch(testBranch);

        Bo testBranchObj = DAOUserBo.create(userCase);
        testBranchObj.setTitle("TestBranchObj");

        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIForm.fillTitle(testBranchObj.getTitle());
        GUIForm.applyForm();
        testBranchObj.setBranch(testBranch);
        GUIBo.setUuidByUrl(testBranchObj);

        Bo testBranch2Object = DSLBranch.addToBranch(testBranch, testBranch2, Arrays.asList(testBranchObj)).get(0);

        GUIPlannedVersion.goToBranch(testBranch2);
        GUIBo.goToCard(testBranch2Object);
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование добавления объекта в версию через интерфейс.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать список объектов objList по классу userClass</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Залогиниться под пользователем testEmpl</li>
     * <li>Переключиться в режим планирования на ветку testBranch</li>
     * <li>Кликнуть кнопку Добавить в контенте objList</li>
     * <li>В режиме планирования добавить объект типа userCase</li>
     * <li>Проверить, что во время создания объекта, и после сохранения формы ошибок не падает.</li>
     * </ol>
     */
    @Test
    public void testAddObjectToBranchInGUI()
    {
        /** Подготовка */
        ContentForm objList = DAOContentCard.createObjectList(employeeCase.getFqn(), userClass);
        DSLContent.add(objList);

        /** Выполнение действий и проверки */
        GUILogon.login(employee);
        GUIPlannedVersion.goToBranch(testBranch);

        Bo userBo = DAOUserBo.create(userCase);
        userBo.setTitle("userBo");

        GUIContent.clickAdd(objList);
        GUIForm.fillTitle(userBo.getTitle());
        GUIForm.applyForm();
        userBo.setBranch(testBranch);
        GUIBo.setUuidByUrl(userBo);

        GUIError.assertErrorAbsence();
        DSLBo.delete(userBo);
    }

    /**
     * Тестирование добавления объекта в ветку с политикой api.branch.KEEP
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс versionClass, тип versionCase</li>
     * <li>В классе versionClass создать атрибут objectLinkVC типа Ссылка на бизнес-объект на класс userClass.</li>
     * <li>В классе userClass создать атрибут типа backBoLinkUC, где прямой ссылкой является атрибут objectLinkVC.</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>В основной версии создать объект versionObject класса versionClass.</li>
     * <li>Добавить объект versionObject  в ветку testBranch при помощи метода api.branch.addToBranch(api.branch.MASTER,
     * 'testBranch  UUID', 'versionObject UUID');</li>
     * <li>В основной версии создать объект userObject1 класса userClass, заполнить атрибут backBoLinkUC значением
     * объекта versionObject.</li>
     * <li>В ветке  testBranch создать объект userObject2 класса userClass заполнить атрибут backBoLinkUC значением
     * объекта versionObject значением из плановой версии</li>
     * <li>Добавить объект userObject1 в ветку testBranch при помощи метода api.branch.addToBranch(api.branch.MASTER,
     * 'testBranch  UUID', 'userObject1 UUID', api.branch.KEEP);</li>
     * <li>Переключиться в режим планирования на ветку testBranch. Проверить, что в объекте versionObject значение
     * атрибута objectLinkVC осталось заполнено значением userObject2.</li>
     * </ol>
     */
    @Test
    public void testAddObjectToBranchWithKeepPolicy()
    {
        /** Подготовка */
        MetaClass versionClass = DAOUserClass.create();
        versionClass.setPlannedVersionsAllowed(true);
        MetaClass versionCase = DAOUserCase.create(versionClass);
        DSLMetaClass.add(versionClass, versionCase);

        Attribute objectLinkVC = DAOAttribute.createObjectLink(versionClass, userClass, null);
        DSLAttribute.add(objectLinkVC);
        GroupAttr groupAttrVers = DAOGroupAttr.createSystem(versionClass);
        DSLGroupAttr.edit(groupAttrVers, new Attribute[] { objectLinkVC }, new Attribute[] {});

        Attribute backBoLinkUC = DAOAttribute.createBackBOLinks(userClass, objectLinkVC);
        DSLAttribute.add(backBoLinkUC);
        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { backBoLinkUC }, new Attribute[] {});

        /** Выполнение действий и проверки */
        Bo versionObject = DAOUserBo.create(versionCase);
        DSLBo.add(versionObject);
        Bo versionObjectInBranch = DSLBranch.addToBranch(testBranch, versionObject).get(0);

        Bo userObject1 = DAOUserBo.create(userCase);
        userObject1.setUserAttribute(backBoLinkUC, Json.listToString(versionObject.getUuid()));
        DSLBo.add(userObject1);

        Bo userObject2 = DAOUserBo.create(userCase);
        userObject2.setBranch(testBranch);
        userObject2.setUserAttribute(backBoLinkUC, Json.listToString(versionObjectInBranch.getUuid()));
        DSLBo.add(userObject2);

        GUILogon.login(employee);

        DSLBranch.addToBranchWithPolicy(testBranch, userObject1, AttributePolicy.KEEP);

        GUIPlannedVersion.goToBranch(testBranch);
        GUIBo.goToCard(versionObjectInBranch);
        GUIForm.assertAttributeValue(objectLinkVC, GUIBo.VERSION_INLINE_BADGE + userObject2.getTitle());
    }

    /**
     * Тестирование добавления объекта в ветку с политикой api.branch.REWRITE
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс versionClass, тип versionCase</li>
     * <li>В классе versionClass создать атрибут objectLinkVC типа Ссылка на бизнес-объект на класс userClass.</li>
     * <li>В классе userClass создать атрибут типа backBoLinkUC, где прямой ссылкой является атрибут objectLinkVC.</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>В основной версии создать объект versionObject класса versionClass.</li>
     * <li>Добавить объект versionObject  в ветку testBranch/li>
     * <li>В основной версии создать объект userObject1 класса userClass, заполнить атрибут backBoLinkUC значением
     * объекта versionObject.</li>
     * <li>В ветке  testBranch создать объект userObject2 класса userClass заполнить атрибут backBoLinkUC значением
     * объекта versionObject значением из плановой версии</li>
     * <li>Добавить объект userObject1 в ветку testBranch при помощи метода api.branch.addToBranch(api.branch.MASTER,
     * 'testBranch  UUID', 'userObject1 UUID', api.branch.REWRITE);</li>
     * <li>Переключиться в режим планирования на ветку testBranch. Проверить, что в объекте versionObject значение
     * атрибута objectLinkVC заполнено значением userObject1.</li>
     * </ol>
     */
    @Test
    public void testAddObjectToBranchWithRewritePolitic()
    {
        /** Подготовка */
        MetaClass versionClass = DAOUserClass.create();
        versionClass.setPlannedVersionsAllowed(true);
        MetaClass versionCase = DAOUserCase.create(versionClass);
        DSLMetaClass.add(versionClass, versionCase);

        Attribute objectLinkVC = DAOAttribute.createObjectLink(versionClass, userClass, null);
        DSLAttribute.add(objectLinkVC);
        GroupAttr groupAttrVers = DAOGroupAttr.createSystem(versionClass);
        DSLGroupAttr.edit(groupAttrVers, new Attribute[] { objectLinkVC }, new Attribute[] {});

        Attribute backBoLinkUC = DAOAttribute.createBackBOLinks(userClass, objectLinkVC);
        DSLAttribute.add(backBoLinkUC);
        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { backBoLinkUC }, new Attribute[] {});

        /** Выполнение действий и проверки */
        Bo versionObject = DAOUserBo.create(versionCase);
        DSLBo.add(versionObject);
        Bo versionObjectInBranch = DSLBranch.addToBranch(testBranch, versionObject).get(0);

        Bo userObject1 = DAOUserBo.create(userCase);
        userObject1.setUserAttribute(backBoLinkUC, Json.listToString(versionObject.getUuid()));
        DSLBo.add(userObject1);

        Bo userObject2 = DAOUserBo.create(userCase);
        userObject2.setBranch(testBranch);
        userObject2.setUserAttribute(backBoLinkUC, Json.listToString(versionObjectInBranch.getUuid()));
        DSLBo.add(userObject2);

        GUILogon.login(employee);

        new ScriptRunner(String.format("api.branch.addToBranch(api.branch.MASTER, '%s', '%s', api.branch.REWRITE)",
                testBranch.getUuid(), userObject1.getUuid())).runScript().get(0);

        GUIPlannedVersion.goToBranch(testBranch);
        GUIBo.goToCard(versionObjectInBranch);
        GUIForm.assertAttributeValue(objectLinkVC, GUIBo.VERSION_INLINE_BADGE + userObject1.getTitle());
    }

    /**
     * Тестирование добавления объектов окружения при заданной глубине уровней окружения больше чем 1.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать атрибут boLinks типа Набор ссылок на БО на класс userClass.</li>
     * <li>Создать следующую структуру объектов класса userClass по связи через boLinks (то есть у объекта mainObj
     * значение атрибута заполнено объектами obj1, obj2 и так далее):</li>
     * <li> |- mainObj --- obj1 --- obj11 --- obj111 --- obj1111</li>
     * <li> |- ------- --- ---- --- ----- --- obj112</li>
     * <li> |- ------- --- ---- --- obj12 --- obj121</li>
     * <li> |- ------- --- ---- --- ----- --- obj122</li>
     * <li> |- ------- --- obj2 --- obj21</li>
     * <li> |- ------- --- ---- --- obj22</li>
     * <li>Добавить объект mainObj в ветку testBranch</li>
     * <li>Добавить контент objList на карточку объекта класса userClass</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Проверить, что все объекты (кроме объекта obj1111) добавлены в ветку testBranch как объекты
     * окружения</li>
     * <li>Проверить что объект obj1111 отсутствует в ветке testBranch</li>
     * </ol>
     */
    @Test
    public void testAddObjectsWithDeepMoreThanOne()
    {
        /** Подготовка */
        Attribute boLinks = DAOAttribute.createBoLinks(userClass, userClass);
        DSLAttribute.add(boLinks);

        Bo mainObject = DAOUserBo.create(userCase);

        Bo object1 = DAOUserBo.create(userCase);
        Bo object11 = DAOUserBo.create(userCase);
        Bo object111 = DAOUserBo.create(userCase);
        Bo object112 = DAOUserBo.create(userCase);
        Bo object1111 = DAOUserBo.create(userCase);

        Bo object12 = DAOUserBo.create(userCase);
        Bo object121 = DAOUserBo.create(userCase);
        Bo object122 = DAOUserBo.create(userCase);

        Bo object2 = DAOUserBo.create(userCase);
        Bo object21 = DAOUserBo.create(userCase);
        Bo object211 = DAOUserBo.create(userCase);
        Bo object212 = DAOUserBo.create(userCase);
        Bo object22 = DAOUserBo.create(userCase);
        Bo object221 = DAOUserBo.create(userCase);
        Bo object222 = DAOUserBo.create(userCase);

        DSLBo.add(
                mainObject,
                object1, object2,
                object11, object12, object21, object22,
                object111, object112, object121, object122, object211, object212, object221, object222);

        DSLBo.editAttributeValue(mainObject, boLinks.setValue(Json.listToString(object1.getUuid(), object2.getUuid())));
        DSLBo.editAttributeValue(object1, boLinks.setValue(Json.listToString(object11.getUuid(), object12.getUuid())));
        DSLBo.editAttributeValue(object11,
                boLinks.setValue(Json.listToString(object111.getUuid(), object112.getUuid())));
        DSLBo.editAttributeValue(object12,
                boLinks.setValue(Json.listToString(object121.getUuid(), object122.getUuid())));
        DSLBo.editAttributeValue(object111, boLinks.setValue(object1111.getUuid()));

        DSLBo.editAttributeValue(object2, boLinks.setValue(Json.listToString(object21.getUuid(), object22.getUuid())));
        DSLBo.editAttributeValue(object21,
                boLinks.setValue(Json.listToString(object211.getUuid(), object212.getUuid())));
        DSLBo.editAttributeValue(object22,
                boLinks.setValue(Json.listToString(object221.getUuid(), object222.getUuid())));
        DSLBranch.addToBranch(testBranch, mainObject);

        ContentForm objList = DAOContentCard.createObjectList(userClass.getFqn(), userClass);
        DSLContent.add(objList);

        /** Выполнение действий и проверки */
        GUILogon.login(employee);
        GUIPlannedVersion.goToBranch(testBranch);
        GUIBo.goToCardWithContinueDialog(mainObject);
        objList.advlist().content().asserts().rowsPresence(
                mainObject,
                object1, object2,
                object11, object12, object21, object22,
                object111, object112, object121, object122, object211, object212, object221, object222);
        objList.advlist().content().asserts().rowsAbsence(object1111);
    }

    /**
     * Тестирование удаления ветки, содержащей объекты окружения.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В основной версии создать объект delBranch класса Ветка</li>
     * <li>В основной версии создать объект envObj класса userClass</li>
     * <li>Добавить объект envObj в ветку delBranch как объект окружения</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Залогиниться под пользователем employee, в основном режиме удалить ветку delBranch.</li>
     * <li>Проверить, что ветка удалилась, ошибок не упало.</li>
     * <li>Проверить, что объект envObj отсутствует в плановых версиях как объект окружения.</li>
     * </ol>
     */
    @Test
    public void testDeleteBranchWithEnvObjects()
    {
        /** Подготовка */
        Bo delBranch = DAOBranch.create(branchCase);
        delBranch.setTitle("DelBranch");
        Bo envObj = DAOUserBo.create(userCase);
        DSLBo.add(delBranch, envObj);
        DSLBranch.addEnvironmentToBranch(null, delBranch, envObj);

        /** Выполнение действий и проверки */
        GUILogon.login(employee);

        GUIBo.goToCard(delBranch);
        GUIBo.delete(delBranch);

        GUIError.assertErrorAbsence();
        Assert.assertFalse("envObj является объектом окружения", DSLBranch.isEnv(envObj));
    }

    /**
     * Тестирование исключения объекта из плановой версии методом api.branch.evictFromBranch, а затем повторного
     * добавления в ветку.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В основной версии создать объект object типа userCase</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Добавить объект object в ветку testBranch</li>
     * <li>Исключить объект object из ветки testBranch при помощи метода evictFromBranch</li>
     * <li>Повторно добавить объект object в ветку testBranch</li>
     * <li>Проверить, что объект object присутствует в ветке testBranch, ошибок при выполнении метода не упало.</li>
     * </ol>
     */
    @Test
    public void testEvictObjectFollowedByAddObject()
    {
        /** Подготовка */
        Bo object = DAOUserBo.create(userCase);
        DSLBo.add(object);

        /** Выполнение действий и проверки */
        Bo objectInBranch = DSLBranch.addToBranch(testBranch, object).get(0);
        DSLBranch.evictFromBranch(testBranch, objectInBranch);
        objectInBranch = DSLBranch.addToBranch(testBranch, object).get(0);

        GUILogon.login(employee);
        GUIPlannedVersion.goToBranch(testBranch);
        GUIBo.goToCard(objectInBranch);

        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование попытки добавить объект в версию, если объект уже присутствует в целевой версии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120249
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить контент список объектов objList на карточку объекта userClass</li>
     * <li>В основной версии создать объект object класса userClass.</li>
     * <b>Выполнения действий и проверки.</b>
     * <li>Добавить объект object в ветку testBranch</li>
     * <li>Повторно добавить object в ветку testBranch при помощи этого же метода.</li>
     * <li>Проверить, что вернулся тот же UUID объекта что и в первый раз.</li>
     * <li>Авторизоваться пользователем employee</li>
     * <li>Перейти в режим планирования на ветку testBranch</li>
     * <li>Убедиться, что отображается один объект object в контенте objList</li>
     * </ol>
     */
    @Test
    public void testReAddingObjectToBranch()
    {
        /** Подготовка */
        ContentForm objList = DAOContentCard.createObjectList(userClass.getFqn(), userClass);
        DSLContent.add(objList);

        Bo object = DAOUserBo.create(userCase);
        DSLBo.add(object);

        /** Выполнение действий и проверки */
        Bo expectedObjectInBranch = DSLBranch.addToBranch(testBranch, object).get(0);
        Bo actualObjectInBranch = DSLBranch.addToBranch(testBranch, object).get(0);

        Assert.assertEquals("Повторно вернувшийся uuid объекта не совпал с предыдущим",
                expectedObjectInBranch.getUuid(), actualObjectInBranch.getUuid());

        GUILogon.login(employee);
        GUIPlannedVersion.goToBranch(testBranch);
        GUIBo.goToCardWithContinueDialog(object);

        objList.advlist().content().asserts().rowsPresence(object);
        objList.advlist().content().asserts().rowsNumberOnCurrentPage(1);
    }
}