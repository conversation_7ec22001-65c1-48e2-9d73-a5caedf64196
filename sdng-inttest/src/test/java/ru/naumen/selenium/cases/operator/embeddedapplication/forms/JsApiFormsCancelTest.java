package ru.naumen.selenium.cases.operator.embeddedapplication.forms;

import static ru.naumen.selenium.cases.operator.embeddedapplication.forms.JsApiFormsFilterTest.OPEN_FILTER_FORM;

import java.io.File;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.DSLListDataApi;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тестирование отмены форм открытых через JS API (jsApi.forms.cancel)
 *
 * <AUTHOR>
 * @since 10.09.2020
 */
class JsApiFormsCancelTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private MetaClass userClass, userCase;
    private BoStatus closed;
    private Bo bo;

    public final static Gson GSON = new GsonBuilder().serializeNulls().create();

    /**
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Создать пользовательский класс userClass со сменой статуса и сменой ответственного</li>
     *   <li>Создать пользовательский тип userCase</li>
     *   <li>Создать объект bo типа userCase</li>
     * </ol>
     */
    @BeforeEach
    public void prepareFixture()
    {
        userClass = DAOUserClass.createWith().workFlow().responsible().create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        closed = DAOBoStatus.createClosed(userClass);

        bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
    }

    /**
     * Тестирование отмены формы смены статуса через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js-кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.forms.changeState('$uuid', ['closed'])
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма смены статуса исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithChangeStateForm()
    {
        // Подготовка
        String jsContent = String.format("jsApi.forms.changeState('%s', ['%s'])", bo.getUuid(), closed.getCode());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение статуса");

        cancelForm(application);
        GUIForm.assertDialogDisappear("Форма редактирования статуса не исчезла");
    }

    /**
     * Тестирование отмены формы смены ответственного через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js-кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.forms.changeResponsible('$uuid')
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - идентификатор объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма смены ответственного исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithChangeResponsibleForm()
    {
        // Подготовка
        String jsContent = String.format("jsApi.forms.changeResponsible('%s')", bo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Изменение ответственного");

        cancelForm(application);
        GUIForm.assertDialogDisappear("Форма редактирования ответственного не исчезла");
    }

    /**
     * Тестирование отмены формы настройки условий фильтрации через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.forms.filterForm('%контекст фильтрации%')
     *     -------------------------------------------------------------------------------
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылось модальная форма настройки условий фильтрации</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма настройки условий фильтрации исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithFilterForm()
    {
        // Подготовка
        ContentForm objectListContent = DAOContentCard.createObjectAdvList(userCase, userClass);
        DSLContent.add(objectListContent);

        String descriptorAsJson = DSLListDataApi.createListDescriptorJson(userCase, objectListContent);
        String jsContent = String.format(OPEN_FILTER_FORM, descriptorAsJson);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Настройка условий фильтрации");

        cancelForm(application);
        GUIForm.assertDialogDisappear("Форма настройки условий фильтрации не исчезла");
    }

    /**
     * Тестирование отмены формы добавление связи через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js-кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.selectObjectDialog('$uuid', 'system')
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - идентификатор объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылось модальная форма добавление связи</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма добавление связи исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithSelectObjectForm()
    {
        // Подготовка
        String jsContent = String.format("jsApi.commands.selectObjectDialog('%s', 'system')", userClass.getFqn());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIComplexRelationForm.assertComplexRelationFormAppear();

        cancelForm(application);
        GUIComplexRelationForm.assertComplexRelationFormAbsent();
    }

    /**
     * Тестирование отмены формы быстрого добавления объекта через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты "Название"</li>
     *   <li>Создать форму быстрого добавления и редактирования quickForm (Группа атрибутов - attrGroup,
     *   Для типов - userCase)</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js-кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.quickAddObject('$fqn', quickForm, {})
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $fqn - код метакласса userClass;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылось модальная форма быстрого добавления объекта</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма быстрого добавления объекта исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithQuickAddForm()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        Attribute titleAttr = SysAttribute.title(userClass);
        DSLGroupAttr.add(attrGroup, titleAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);

        String jsContent = String.format("jsApi.commands.quickAddObject('%s', '%s', {})",
                userClass.getFqn(), StringUtils.substringAfter(quickForm.getUuid(), "$"));
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Добавление объекта");

        cancelForm(application);
        GUIForm.assertDialogDisappear("Форма добавление объекта не исчезла");
    }

    /**
     * Тестирование отмены формы быстрого редактирования объекта через jsApi.forms.cancel().
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128046901
     * <ol>
     *   <b>Подготовка</b>
     *   <li>{@link #prepareFixture() Общая часть настройки}</li>
     *   <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты "Название"</li>
     *   <li>Создать форму быстрого добавления и редактирования quickForm (Группа атрибутов - attrGroup,
     *   Для типов - userCase)</li>
     *   <li>Добавить и включить встроенное приложение, исполняемое на клиенте, с js-кодом:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     jsApi.commands.quickEditObject('$uuid', quickForm, {})
     *     -------------------------------------------------------------------------------
     *     Где:
     *     1) $uuid - uuid объекта bo;
     *     </pre>
     *   </li>
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   userClass</li>
     *   <br>
     *   <b>Действия и проверки</b>
     *   <li>Войти под naumen</li>
     *   <li>Перейти на карточку объекта bo<</li>
     *   <li>Проверить, что открылось модальная форма быстрого редактирования объекта</li>
     *   <li>Вызвать отмену формы через jsApi.forms.cancel()</li>
     *   <li>Проверить, что модальная форма быстрого редактирования объекта исчезла</li>
     * </ol>
     */
    @Test
    void testCloseWithQuickEditForm()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        Attribute titleAttr = SysAttribute.title(userClass);
        DSLGroupAttr.add(attrGroup, titleAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);

        String jsContent = String.format("jsApi.commands.quickEditObject('%s', '%s', {})",
                bo.getUuid(), StringUtils.substringAfter(quickForm.getUuid(), "$"));
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertDialogCaption("Редактирование объекта");

        cancelForm(application);
        GUIForm.assertDialogDisappear("Форма редактирование объекта не исчезла");
    }

    /**
     * Вызывает метод jsApi.forms.cancel() для указанного ВП
     * @param application встроенное приложение
     */
    private static void cancelForm(EmbeddedApplication application)
    {
        String jsTemplate = "for (var i = 0; i < window.frames.length; ++i) {\n" +
                            "    var jsApi = window.frames[i].jsApi\n" +
                            "    if (jsApi && jsApi.findApplicationCode() == '%s') {\n" +
                            "        jsApi.forms.cancel()\n" +
                            "    }\n" +
                            "}\n";
        tester.runJavaScript(String.format(jsTemplate, application.getCode()));
    }
}
