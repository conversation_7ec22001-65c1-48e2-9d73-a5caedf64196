package ru.naumen.selenium.cases.operator.classes.quickaction;

import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.InputComplex;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.GUISelectCase;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.Tool;
import ru.naumen.selenium.casesutil.model.content.ToolPanel;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;

/**
 * Тестирование быстрых действий на формах в интерфейсе оператора.
 * <AUTHOR>
 * @since May 31, 2021
 */
public class QuickActions3Test extends AbstractTestCase
{
    private static MetaClass userClass;
    private static MetaClass userCase;

    /**
     * Общая подготовка для тестов:
     * <ol>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте проверки прав на
     * редактирование атрибута при открытии формы быстрого добавления с формы редактирования объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Выдать профилю profile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Настроить скрипт для проверки прав маркера marker для профиля profile:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>В классе userClass создать атрибут linkAttr типа «Ссылка на бизнес-объект» (Класс объектов — userClass,
     * Форма быстрого добавления — quickForm)</li>
     * <li>В классе userClass создать группу атрибутов linkAttrGroup и добавить в нее атрибут linkAttr</li>
     * <li>На карточку объекта класса userClass вывести контент propertyList типа «Параметры объекта»
     * (Группа атрибутов — linkAttrGroup)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку-ссылку «Редактировать» на панели действий контента propertyList</li>
     * <li>Нажать на кнопку-ссылку «Добавить» над полем атрибута linkAttr</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributePermissionContextVariablesCalledFromEditForm()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setScriptRight(userClass, profile, marker, permissionScript.getCode());

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);
        Attribute linkAttr = DAOAttribute.createObjectLink(userClass.getFqn(), userClass, null);
        linkAttr.setQuickAddForm(quickForm.getUuid());
        DSLAttribute.add(linkAttr);
        GroupAttr linkAttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(linkAttrGroup, linkAttr);
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass, linkAttrGroup);
        DSLContent.add(propertyList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIPropertyList.clickEditLink(propertyList);
        GUIForm.clickQuickAddForm(linkAttr);
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте проверки прав на
     * редактирование атрибута при открытии формы быстрого добавления из списка по кнопке «Добавить»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Выдать профилю profile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Настроить скрипт для проверки прав маркера marker для профиля profile:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>На карточку объекта класса userClass вывести контент objectList типа «Список объектов»
     * (Класс объектов — userClass, Представление — Сложный список)</li>
     * <li>Изменить параметры кнопки «Добавить» на панели действий списка objectList:
     * Использовать форму быстрого добавления — да, Форма быстрого добавления — quickForm</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку «Добавить» на панели действий списка objectList</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributePermissionContextVariablesCalledFromList()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setScriptRight(userClass, profile, marker, permissionScript.getCode());

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);

        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getCode(), attrGroup, userClass);
        ToolPanel toolPanel = DAOToolPanel.createCustomToolPanel(
                DAOTool.createSystemAddButtonWithQuickAddForm(quickForm));
        objectList.setToolPanel(toolPanel);
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickAdd();
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте проверки прав на
     * редактирование атрибута при открытии формы быстрого добавления из списка по пользовательской кнопке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Выдать профилю profile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Настроить скрипт для проверки прав маркера marker для профиля profile:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>На карточку объекта класса userClass вывести контент objectList типа «Список объектов»
     * (Класс объектов — userClass, Представление — Сложный список)</li>
     * <li>Добавить пользовательскую кнопку userTool на панель действий списка objectList
     * (Использовать форму быстрого добавления — да, Форма быстрого добавления — quickForm)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку userTool на панели действий списка objectList</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributePermissionContextVariablesCalledFromUserTool()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setScriptRight(userClass, profile, marker, permissionScript.getCode());

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);
        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getCode(), attrGroup, userClass);
        Tool quickAddFormButton = DAOTool.createUserQuickAddFormButton(quickForm);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(
                quickAddFormButton
        ));
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickUserEventButton(quickAddFormButton.getTitle());
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте роли при проверке прав на
     * редактирование атрибута при открытии формы быстрого добавления с формы редактирования объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать роль role в классе userClass и задать для нее право доступа с помощью скрипта:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Создать профиль fullProfile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup, Роли — role)</li>
     * <li>Выдать профилю fullProfile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Выдать права на маркер marker для профиля profile</li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>В классе userClass создать атрибут linkAttr типа «Ссылка на бизнес-объект» (Класс объектов — userClass,
     * Форма быстрого добавления — quickForm)</li>
     * <li>В классе userClass создать группу атрибутов linkAttrGroup и добавить в нее атрибут linkAttr</li>
     * <li>На карточку объекта класса userClass вывести контент propertyList типа «Параметры объекта»
     * (Группа атрибутов — linkAttrGroup)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку-ссылку «Редактировать» на панели действий контента propertyList</li>
     * <li>Нажать на кнопку-ссылку «Добавить» над полем атрибута linkAttr</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributeRolePermissionContextVariablesCalledFromEditForm()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityRole role = DAOSecurityRole.create(userClass, permissionScript, null);
        DSLSecurityRole.add(role);
        SecurityProfile fullProfile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, role);
        DSLSecurityProfile.add(fullProfile, profile);
        DSLSecurityProfile.grantAllPermissionsForCase(fullProfile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setRights(userClass, profile, marker);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);
        Attribute linkAttr = DAOAttribute.createObjectLink(userClass.getFqn(), userClass, null);
        linkAttr.setQuickAddForm(quickForm.getUuid());
        DSLAttribute.add(linkAttr);
        GroupAttr linkAttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(linkAttrGroup, linkAttr);
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass, linkAttrGroup);
        DSLContent.add(propertyList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIPropertyList.clickEditLink(propertyList);
        GUIForm.clickQuickAddForm(linkAttr);
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте роли при проверке прав на
     * редактирование атрибута при открытии формы быстрого добавления из списка по кнопке «Добавить»
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать роль role в классе userClass и задать для нее право доступа с помощью скрипта:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Создать профиль fullProfile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup, Роли — role)</li>
     * <li>Выдать профилю fullProfile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Выдать права на маркер marker для профиля profile</li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>На карточку объекта класса userClass вывести контент objectList типа «Список объектов»
     * (Класс объектов — userClass, Представление — Сложный список)</li>
     * <li>Изменить параметры кнопки «Добавить» на панели действий списка objectList:
     * Использовать форму быстрого добавления — да, Форма быстрого добавления — quickForm</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку «Добавить» на панели действий списка objectList</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributeRolePermissionContextVariablesCalledFromList()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityRole role = DAOSecurityRole.create(userClass, permissionScript, null);
        DSLSecurityRole.add(role);
        SecurityProfile fullProfile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, role);
        DSLSecurityProfile.add(fullProfile, profile);
        DSLSecurityProfile.grantAllPermissionsForCase(fullProfile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setRights(userClass, profile, marker);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);
        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getCode(), attrGroup, userClass);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(
                DAOTool.createSystemAddButtonWithQuickAddForm(quickForm)
        ));
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickAdd();
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование заполнения контекстных переменных initialValues и sourceObject в скрипте роли при проверке прав на
     * редактирование атрибута при открытии формы быстрого добавления из списка по пользовательской кнопке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$148815062
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в нее сотрудника employee</li>
     * <li>Создать роль role в классе userClass и задать для нее право доступа с помощью скрипта:
     * <pre>return !initialValues.isEmpty() && sourceObject != null</pre></li>
     * <li>Создать профиль fullProfile для лицензированных пользователей (Группа пользователей — secGroup,
     * Роли — Сотрудник)</li>
     * <li>Создать профиль profile для лицензированных пользователей (Группа пользователей — secGroup, Роли — role)</li>
     * <li>Выдать профилю fullProfile все права в классе userClass</li>
     * <li>В матрице прав класса userClass создать новый маркер marker на редактирование атрибута stringAttr</li>
     * <li>Выдать права на маркер marker для профиля profile</li>
     * <li>Для всех типов класса userClass создать форму быстрого добавления и редактирования quickForm
     * (Группа атрибутов — attrGroup)</li>
     * <li>На карточку объекта класса userClass вывести контент objectList типа «Список объектов»
     * (Класс объектов — userClass, Представление — Сложный список)</li>
     * <li>Добавить пользовательскую кнопку userTool на панель действий списка objectList
     * (Использовать форму быстрого добавления — да, Форма быстрого добавления — quickForm)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку userTool на панели действий списка objectList</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме быстрого добавления отображается атрибут stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttributeRolePermissionContextVariablesCalledFromUserTool()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        ScriptInfo permissionScript = DAOScriptInfo.createNewScriptInfo(
                "return !initialValues.isEmpty() && sourceObject != null");
        DSLScriptInfo.addScript(permissionScript);
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee);
        SecurityRole role = DAOSecurityRole.create(userClass, permissionScript, null);
        DSLSecurityRole.add(role);
        SecurityProfile fullProfile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, role);
        DSLSecurityProfile.add(fullProfile, profile);
        DSLSecurityProfile.grantAllPermissionsForCase(fullProfile, userClass);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(stringAttr);
        marker.apply();
        DSLSecurityProfile.setRights(userClass, profile, marker);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase);
        DSLCustomForm.add(quickForm);
        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass.getCode(), attrGroup, userClass);
        Tool quickAddFormButton = DAOTool.createUserQuickAddFormButton(quickForm);
        objectList.setToolPanel(DAOToolPanel.createCustomToolPanel(quickAddFormButton));
        DSLContent.add(objectList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickUserEventButton(quickAddFormButton.getTitle());
        // Проверка
        GUIForm.assertAttrPresent(stringAttr);
    }

    /**
     * Тестирование автоматического заполнения родителя при открытии формы быстрого добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$122626426
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass, объекты которого вложены в себя, и унаследованный от него тип
     * userCase</li>
     * <li>Создать группу атрибутов parentAttrGroup и добавить в нее атрибуты «Название» и «Родитель»</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типа userCase (Группа атрибутов —
     * parentAttrGroup)</li>
     * <li>Создать форму быстрого добавления и редактирования quickFormImmediate для типа userCase (Группа атрибутов —
     * parentAttrGroup, Добавлять и редактировать объекты при сохранении быстрой формы — да)</li>
     * <li>В классе userClass создать атрибут linkAttr типа «Ссылка на бизнес-объект» (Класс объектов — userClass,
     * Форма быстрого добавления — quickForm)</li>
     * <li>В классе userClass создать атрибут linkAttrImmediate типа «Ссылка на бизнес-объект» (Класс объектов —
     * userClass, Форма быстрого добавления — quickFormImmediate)</li>
     * <li>Создать группу атрибутов attrGroup и добавить в нее атрибуты linkAttr и linkAttrImmediate</li>
     * <li>На форму добавления объекта класса userClass вывести контент propertyList типа «Параметры на форме»
     * (Группа атрибутов — attrGroup)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>Навести на поле выбора атрибута linkAttr и нажать на кнопку-ссылку «Добавить» над ним</li>
     * <li>Проверить, что поле выбора атрибута «Родитель» заполнено значением «[новый объект]»</li>
     * <li>Нажать на кнопку «Отмена»</li>
     * <li>Навести на поле выбора атрибута linkAttrImmediate и нажать на кнопку-ссылку «Добавить» над ним</li>
     * <li>Проверить, что поле выбора атрибута «Родитель» не заполнено</li>
     * <li>Заполнить название объекта</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>Проверить, что объект сохранен в системе, атрибут «Родитель» у него не заполнен</li>
     * </ol>
     */
    @Test
    public void testFillParentOnQuickAddForm()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute titleAttr = SysAttribute.title(userClass);
        Attribute parentAttr = SysAttribute.parent(userClass);
        DSLMetainfo.add(userClass, userCase);
        GroupAttr parentAttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(parentAttrGroup, titleAttr, parentAttr);
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(parentAttrGroup, userCase);
        CustomForm quickFormImmediate = DAOCustomForm.createQuickActionForm(parentAttrGroup, userCase);
        quickFormImmediate.setImmediateObjectSavingEnabled(true);
        DSLCustomForm.add(quickForm, quickFormImmediate);

        Attribute linkAttr = DAOAttribute.createObjectLink(userClass, userClass, null);
        linkAttr.setQuickAddForm(quickForm.getUuid());
        Attribute linkImmediateAttr = DAOAttribute.createObjectLink(userClass, userClass, null);
        linkImmediateAttr.setQuickAddForm(quickFormImmediate.getUuid());
        DSLMetainfo.add(linkAttr, linkImmediateAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, linkAttr, linkImmediateAttr);

        ContentForm propertyList = DAOContentAddForm.createEditablePropertyList(userClass, attrGroup);
        DSLContent.add(propertyList);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        GUIForm.clickQuickAddForm(linkAttr);
        Model model = new Model();
        model.setTitle("[новый объект]");
        GUISelect.assertValue(InputComplex.ANY_VALUE, model, parentAttr.getCode());
        GUIForm.clickCancelTopmostDialog();

        GUIForm.clickQuickAddForm(linkImmediateAttr);
        GUISelect.assertValue(InputComplex.ANY_VALUE, null, parentAttr.getCode());
        String objectTitle = ModelUtils.createTitle();
        tester.sendKeys(Div.PROPERTY_DIALOG_BOX + Any.ANY_VALUE, objectTitle, titleAttr.getCode());
        GUIForm.applyModalForm();
        GUIForm.assertDialogDisappear("Форма не исчезла.");

        Set<String> uuids = DSLBo.getUuidsByFqn(userCase.getFqn());
        Assert.assertEquals("Количество объектов не совпало с ожидаемым.", 1, uuids.size());
        Bo userBo = DAOUserBo.create(userCase);
        userBo.setUuid(uuids.iterator().next());
        userBo.setExists(true);
        DSLBo.assertTitle(userBo, objectTitle);
        parentAttr.setValue(null);
        DSLBo.assertObjectAttr(userBo, parentAttr);
    }

    /**
     * Тестирование обновления списка доступных для выбора соглашений и услуг на форме добавления запроса после
     * быстрого редактирования сотрудника, выбранного в качестве контрагента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00529
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$122626426
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать соглашение agreement</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Сделать сотрудника employee получателем соглашения agreement</li>
     * <li>В типе employeeCase создать группу атрибутов agreementAttrGroup и добавить в нее атрибут «Соглашения»</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm для типа employeeCase (Группа атрибутов —
     * agreementAttrGroup, Добавлять и редактировать объекты при сохранении быстрой формы — да)</li>
     * <li>В типе scCase создать атрибут linkAttr типа «Ссылка на бизнес-объект» (Класс объектов — Сотрудник,
     * Типы объектов — employeeCase, Форма быстрого редактирования — quickForm)</li>
     * <li>В типе scCase создать группу атрибутов linkGroup и добавить в нее атрибут linkAttr</li>
     * <li>На форму добавления запроса типа scCase вывести контент propertyList типа «Параметры на форме» (Группа
     * атрибутов — linkGroup)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Нажать на кнопку «Добавить запрос» на панели действий</li>
     * <li>Заполнить Соглашение/услуга = agreement, Тип запроса = scCase</li>
     * <li>Навести на поле выбора атрибута linkAttr и нажать на кнопку-ссылку «Редактировать» над ним</li>
     * <li>Разорвать связь сотрудника с соглашением agreement</li>
     * <li>Нажать на кнопку «Сохранить» на форме быстрого редактирования</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке доступных для выбора соглашений и услуг на форме добавления запроса нет элементов</li>
     * </ol>
     */
    @Test
    public void testUpdatePossibleAgreementsAfterEmployeeEdit()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, employeeCase);
        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase());
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(agreement, employee);
        DSLAgreement.addRecipients(employee, agreement);

        GroupAttr agreementAttrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(agreementAttrGroup, SysAttribute.recipientAgreements(DAOEmployeeCase.createClass()));
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(agreementAttrGroup, employeeCase);
        quickForm.setImmediateObjectSavingEnabled(true);
        DSLCustomForm.add(quickForm);

        Attribute linkAttr = DAOAttribute.createObjectLink(scCase, DAOEmployeeCase.createClass(), employeeCase,
                employee);
        linkAttr.setQuickEditForm(quickForm.getUuid());
        DSLAttribute.add(linkAttr);
        GroupAttr linkGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(linkGroup, linkAttr);
        ContentForm propertyList = DAOContentAddForm.createEditablePropertyList(scCase, linkGroup);
        DSLContent.add(propertyList);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUISc.selectAgrServiceField(agreement.getTitle());
        GUISc.selectScCase(scCase);
        GUIForm.clickQuickEditForm(linkAttr);
        GUIMultiSelect.unselectNotClean(InputComplex.RECEPIENT_AGREEMENTD_VALUE, agreement.getUuid());
        GUIForm.applyModalForm();
        // Проверки
        GUITester.assertValue(InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, "[нет элементов]");
    }

    /**
     * Тестирование открытия формы быстрого редактирования,
     * если на форме присутствует атрибут типа Обратная ссылка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$220134211
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать в типе userCase группу атрибутов attrGroup1</li>
     * <li>Создать быструю форму редактирования quickEditForm для типа userCase с группой атрибутов attrGroup1</li>
     * <li>Создать в типе userCase атрибут ссылка на БО objectLinkAttr на тип userCase, с формой быстрого
     * редактирования quickEditForm</li>
     * <li>Создать в типе userCase атрибут обратная ссылка на БО backBOLinksLinkAttr на атрибут objectLinkAttr</li>
     * <li>Добавить атрибут backBOLinksLinkAttr в группу атрибутов attrGroup1</li>
     * <li>Создать в типе userCase группу атрибутов attrGroup2</li>
     * <li>Добавить атрибут objectLinkAttr в группу атрибутов attrGroup2</li>
     * <li>Создать контент типа Параметры объекта propertyList с группой атрибута attrGroup2</li>
     * <li>Создать 2 объекта userBo1, userBo2 типа userCase</li>
     * <li>В объекте userBo1 установить значение для атрибута objectLinkAttr равное userBo2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo1</li>
     * <li>Нажать на кнопку «Редактировать» у контента propertyList</li>
     * <li>Навести на поле выбора атрибута objectLinkAttr и нажать на кнопку-ссылку «Редактировать» над ним</li>
     * <li>Нажать на кнопку «Сохранить» на форме быстрого редактирования</li>
     * <li>Нажать на кнопку «Сохранить» на модальной форме</li>
     * <li>Проверить, что не возникает ошибок</li>
     * </ol>
     */
    @Test
    public void testQuickEditFormContainsBackBoLinksAttr()
    {
        // Подготовка
        GroupAttr attrGroup1 = DAOGroupAttr.create(userCase);
        DSLGroupAttr.add(attrGroup1, SysAttribute.title(userCase));

        CustomForm quickEditForm = DAOCustomForm.createQuickActionForm(attrGroup1, userCase);
        DSLCustomForm.add(quickEditForm);

        Attribute objectLinkAttr = DAOAttribute.createObjectLink(userCase, userCase);
        objectLinkAttr.setQuickEditForm(quickEditForm.getUuid());
        Attribute backBOLinksLinkAttr = DAOAttribute.createBackBOLinks(userCase, objectLinkAttr);
        DSLAttribute.add(objectLinkAttr, backBOLinksLinkAttr);

        DSLGroupAttr.edit(attrGroup1, new Attribute[] { backBOLinksLinkAttr }, new Attribute[] {});

        GroupAttr attrGroup2 = DAOGroupAttr.create(userCase);
        DSLGroupAttr.add(attrGroup2, SysAttribute.title(userCase), objectLinkAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(userCase, attrGroup2);
        DSLContent.add(propertyList);

        Bo userBo1 = DAOOu.create(userCase);
        Bo userBo2 = DAOOu.create(userCase);
        DSLBo.add(userBo1, userBo2);
        objectLinkAttr.setValue(userBo2.getUuid());
        DSLBo.editAttributeValue(userBo1, objectLinkAttr);

        // Выполнение действий и проверки
        GUILogon.asTester();

        GUIBo.goToCard(userBo1);
        GUIPropertyList.clickEditLink(propertyList);
        GUIForm.clickQuickEditForm(objectLinkAttr);
        GUIForm.clickApplyTopmostDialog();
        GUIForm.applyModalForm();
    }

    /**
     * Тестирование контекстной переменной sourceForm в скрипте фильтрации атрибута "Тип объекта"
     * на форме быстрого добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/scriptsUsing
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00747
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00461
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$266149652
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить типы userCase2 пользовательского класса userClass и ouCase класса отдел</li>
     * <li>Для системного атрибута "Тип объекта" класса userClass задать скрипт фильтрации значений
     * при редактировании:<br>
     * <pre>
     * if (null == subject) {
     *     return []
     * }
     * return '%specialValue%' == sourceForm?.title ? ['%userCase%', '%userCase2%'] : []</pre></li>
     * <li>В классе userClass создать формы для быстрого добавления quickForm с ситменой группой атрибутов</li>
     * <li>В типе ouCase создать атрибут objectLink типа "Ссылка на бизнес-объект"
     * (Класс объектов - userClass, Форма быстрого добавления - quickForm)</li>
     * <li>В системную группу атрибутов типа ouCase добавить атрибут objectLink</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта ouCase</li>
     * <li>Изменить значение атрибута "Название" на specialValue</li>
     * <li>Открыть форму быстрого добавления объекта в атрибуте objectLink</li>
     * <li>Проверить, что список возможных значенией для атрибута "Тип Объекта": userCase, userCase2</li>
     * </ol>
     */
    @Test
    public void testSourceFormInFiltrationScriptForMetaClassAttr()
    {
        // Подготовка
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        DSLMetainfo.add(userCase2, ouCase);

        String specialValue = ModelUtils.createTitle();
        String scriptTemplate = """
                if (null == subject)
                {
                    return []
                }
                return '%s' == sourceForm?.title ? ['%s', '%s'] : []
                """.formatted(specialValue, userCase.getFqn(), userCase2.getFqn());

        ScriptInfo filtrationScript = DAOScriptInfo.createNewScriptInfo(scriptTemplate);
        DSLScriptInfo.addScript(filtrationScript);

        Attribute metaClassAttr = SysAttribute.metaClass(userClass);
        DSLAttribute.setEditFilter(metaClassAttr, filtrationScript, true);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userClass);
        DSLCustomForm.add(quickForm);

        Attribute objectLink = DAOAttribute.createObjectLink(ouCase, userClass, null);
        objectLink.setQuickAddForm(quickForm.getUuid());
        DSLMetainfo.add(objectLink);

        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ouCase), new Attribute[] { objectLink }, new Attribute[0]);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(ouCase);
        GUIForm.fillAttribute(SysAttribute.title(ouClass), specialValue);
        GUIForm.clickQuickAddForm(objectLink);

        GUISelectCase.assertSelect(ModelUtils.getTitles(userCase, userCase2), false, true, true);
    }
}
