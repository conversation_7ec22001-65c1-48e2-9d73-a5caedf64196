package ru.naumen.selenium.cases.admin.sets;

import java.io.File;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLMobileMenuItem;
import ru.naumen.selenium.casesutil.admin.GUIWfProfile;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIRulesSettingsEscalation;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLTransition;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIStatusAction;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileAddForm;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.mobile.GUIMobileEditForm;
import ru.naumen.selenium.casesutil.mobile.GUIMobileList;
import ru.naumen.selenium.casesutil.mobile.GUIMobileMenuItem;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContents;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMobileMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOWfProfile;
import ru.naumen.selenium.casesutil.model.admin.MobileMenuItem;
import ru.naumen.selenium.casesutil.model.admin.WfProfile;
import ru.naumen.selenium.casesutil.model.admin.log.Constants.CategoryCode;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOStatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.MobileEditForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileList;
import ru.naumen.selenium.casesutil.model.mobile.MobilePropertiesList;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.report.DAOReportTemplate;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.report.GUIReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportTemplateList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.Transition;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.MetaInfoXml;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тестирование разметки настроек метаинформации комплектами в интерфесе администратора
 *
 * <AUTHOR>
 * @since 29.05.2024
 */
public class MarkMetainfoSettingsBySettingsSet2Test extends AbstractTestCase
{
    private static SettingsSet set1, set2;
    private static MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        set1 = DAOSettingsSet.createSettingsSet();
        set1.setTitle(ModelUtils.createTitle());
        set2 = DAOSettingsSet.createSettingsSet();
        set2.setTitle(ModelUtils.createTitle());
        DSLSettingsSet.add(set1, set2);

        userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * Тестирование разметки мобильных списков комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Мобильное приложение"</li>
     * <li>Нажать кнопку "Добавить список"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан список mobileList</li>
     * <li>Проверить, что на карточке mobileList выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке mobileList</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен список объектов mobileList в мобильном
     * приложении, в нем  изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileListWithSet()
    {
        //Выполнение действий и проверки
        MobileList mobileList = DAOMobile.createMobileList(userClass);
        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileList.clickAddButton();
        GUIMobileList.fillFieldsOnAddForm(mobileList);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        mobileList.setUuid(GUIMobileList.getUuid());
        mobileList.setExists(true);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMobileList.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.MOBILE_LIST_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен список объектов '%s' ('%s') в мобильном приложении:\n"
                                                   + "Комплект: '%s' -> '%s'.",
                mobileList.getTitle(), mobileList.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки мобильных карточек комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Мобильное приложение" - "Карточки объектов"</li>
     * <li>Нажать кнопку "Добавить карточку"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана карточка card</li>
     * <li>Проверить, что на карточке card выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке card</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена карточка с кодом %card.getCode()% в
     * мобильном приложении, в ней изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileCardsWithSet()
    {
        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileCard.clickObjectCardsTab();
        GUIMobileCard.clickAddButton();
        MobileCard card = DAOMobile.createMobileCard(userClass);
        String cardCode = ModelUtils.createCode();
        GUIMobileCard.setCode(cardCode);
        GUISelect.selectById(GUIMobileCard.X_INPUT_CLASS, userClass.getFqn());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        String uuid = GUIMobileCard.getUuid();
        card.setUuid(uuid);
        card.setExists(true);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMobileCard.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.MOBILE_CARD_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена карточка объектов с кодом '%s' в мобильном "
                                                   + "приложении:\n"
                                                   + "Комплект: '%s' -> '%s'.",
                card.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки контентов на мобильных картчках комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать карточку в мобильном приложении cardUserClass</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку cardUserClass</li>
     * <li>Нажать кнопку "Добавить контент"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан контент propertiesList</li>
     * <li>Нажать кнопку "Редактировать контент" на контенте propertiesList</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать контент" на контенте propertiesList</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileCardsContentWithSet()
    {
        //Подготовка
        MobileCard cardUserClass = DAOMobile.createMobileCard(userClass);
        DSLMobile.add(cardUserClass);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileCard.goToCard(cardUserClass);
        GUIMobileContents.clickAdd();
        MobilePropertiesList propertiesList = DAOMobile.createMobilePropertiesList();

        GUIMobileCard.selectContentType(GUIMobileCard.PROPERTIES_LIST);
        GUIMobileCard.setTitle(propertiesList.getTitle());
        GUIMobileCard.setCode(propertiesList.getCode());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyModalForm();

        GUIMobileContents.clickEditContent(propertiesList);
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUIMobileContents.clickEditContent(propertiesList);
        GUISettingsSet.assertSettingsSetPropOnForm(set2);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование разметки форм добавления в мобильном приложении комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Мобильное приложение" - "Формы добавления объектов"</li>
     * <li>Нажать кнопку "Создать форму добавления"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана форма addForm</li>
     * <li>Проверить, что на карточке формы addForm выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке addForm</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена форма добавления объектов addForm в
     * мобильном
     приложении, в ней изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileAddFormWithSet()
    {
        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileAddForm.clickAddFormsTab();
        GUIMobileAddForm.clickAddButton();

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        GUIMobileAddForm.fillAllFieldsOnAddForm(addForm);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        addForm.setUuid(GUIMobileAddForm.getUuid());
        addForm.setExists(true);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMobileAddForm.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.MOBILE_ADD_FORM_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена форма добавления объектов '%s' ('%s') в мобильном "
                                                   + "приложении:\n"
                                                   + "Комплект: '%s' -> '%s'.",
                addForm.getTitle(), addForm.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки форм редактирования в мобильном приложении комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Мобильное приложение" - "Формы редактирования объектов"</li>
     * <li>Нажать кнопку "Создать форму редактирования"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана форма editForm</li>
     * <li>Проверить, что на карточке формы editForm выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке editForm</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена форма редактирования объектов editForm в
     * мобильном приложении, в ней изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileEditFormWithSet()
    {
        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileEditForm.clickEditFormsTab();

        MobileEditForm editForm = DAOMobile.createMobileEditForm(userClass);
        GUIMobileEditForm.clickAddButton();
        GUIMobileEditForm.fillAllFieldsOnAddForm(editForm);
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        editForm.setUuid(GUIMobileEditForm.getUuid());
        editForm.setExists(true);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMobileEditForm.clickEditButton();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.MOBILE_EDIT_FORM_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменена форма редактирования объектов с кодом '%s' в "
                                                   + "мобильном "
                                                   + "приложении:\n"
                                                   + "Комплект: '%s' -> '%s'.",
                editForm.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки элементов навигационного меню в мобильном приложении комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Мобильное приложение" - "Навигация"</li>
     * <li>Нажать кнопку "Добавить элемент"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан элемент меню elementTitle</li>
     * <li>Перейти на карточку элемента меню elementTitle</li>
     * <li>Проверить, что на карточке elementTitle выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке elementTitle</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке elementTitle выбран комплект set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileNavigationItemWithSet()
    {
        //Подготовка
        String elementTitle = ModelUtils.createSubject();

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileCard.clickNavigationTab();
        GUIMobileCard.clickAddElement();

        GUIMobileMenuItem.fillTitle(elementTitle);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyModalForm();
        GUIMobileMenuItem.clickMenuItemByTitle(elementTitle);

        String itemCode = DSLMobileMenuItem.getItemCodeOnCard();
        Cleaner.afterTest(() -> DSLMobileMenuItem.delete(elementTitle, itemCode));

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIMobileMenuItem.clickEditButton();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки элементов управления на карточке в мобильном приложении комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать карточку в мобильном приложении cardUserClass</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку cardUserClass</li>
     * <li>Кликнуть по кнопке "Редактировать" на карточке объекта в разделе элементы управления в меню</li>
     * <li>Снять галочку с чекбокса "Использовать системную настройку меню"</li>
     * <li>Нажать кнопку "Добавить элемент"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано действие actionTitle</li>
     * <li>Кликнуть по кнопке "Редактировать" на карточке объекта в разделе элементы управления в меню</li>
     * <li>Нажать кнопку "Редактировать" у действия actionTitle</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Кликнуть по кнопке "Редактировать" на карточке объекта в разделе элементы управления в меню</li>
     * <li>Нажать кнопку "Редактировать" у действия actionTitle</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * </ol>
     */
    @Test
    public void testMarkMobileActionElementsWithSet()
    {
        //Подготовка
        MobileCard cardUserClass = DAOMobile.createMobileCard(userClass);
        DSLMobile.add(cardUserClass);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileCard.goToCard(cardUserClass);
        GUIMobileCard.clickEditMenuButton();
        GUIMobileCard.setUseSystemSettings(false);
        tester.click(GUIXpath.Div.ADD);
        String actionTitle = RandomUtils.randomUUID();
        GUIMobileAddForm.fillTitle(actionTitle);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIMobileCard.clickSaveElement();
        GUIForm.applyForm();

        GUIMobileCard.clickEditMenuButton();
        GUIMobileCard.editMenuElement(actionTitle);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIMobileCard.clickSaveElement();
        GUIForm.applyForm();

        GUIMobileCard.clickEditMenuButton();
        GUIMobileCard.editMenuElement(actionTitle);
        GUISettingsSet.assertSettingsSetOnForm(set2);
    }

    /**
     * Тестирование разметки параметров пользовательских действий по событию комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательское действие по событию userEvent типа срипт со скриптом script</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку userEvent</li>
     * <li>Кликнуть по кнопке "Добавить параметр"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан параметр param</li>
     * <li>Нажать кнопку "Редактировать" у параметра param</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Нажать кнопку "Редактировать" у параметра param</li>
     * <li>В поле выбора комплекта проверить, что выбрано set2</li>
     * </ol>
     */
    @Test
    public void testMarkUserEventActionParametersWithSet()
    {
        //Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);

        EventAction userEvent = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true, true,
                userClass);
        DSLEventAction.add(userEvent);

        FormParameter param = DAOFormParameter.createString();
        param.setEventAction(userEvent.getUuid());

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(userEvent);
        GUIEventAction.clickAddParameter();
        GUIAttribute.fillAttrTitle(param.getTitle());
        GUIAttribute.fillAttrCode(param.getCode());
        GUIAttribute.selectAttrType(BooleanType.CODE);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        param.setExists(true);

        GUIEventAction.clickEditParameter(param);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();

        GUIEventAction.clickEditParameter(param);
        GUISettingsSet.assertSettingsSetOnForm(set2);
    }

    /**
     * Тестирование разметки действий при входе/выходе из статуса комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский статус userState в классе userClass</li>
     * <li>Создать скрипт для действия входа в статус script</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку статуса userState</li>
     * <li>Кликнуть по кнопке "Добавить" в "Действия при входе в статус"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создано действие preAction</li>
     * <li>Перейти на карточку действия preAction</li>
     * <li>Проверить, что на карточке заполнен комплект set1</li>
     * <li>Нажать кнопку "Редактировать"</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на карточке заполнен комплект set2</li>
     * </ol>
     */
    @Test
    public void testMarkActionsInStatusWithSet()
    {
        //Подготовка
        BoStatus userState = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(userState);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);

        StatusAction preAction = DAOStatusAction.createPreAction(userState, script.getCode());

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(userState);

        Set<String> oldIdSet = GUIStatusAction.getRowIdsFromSimpleList(GUIStatusAction.PRE_ACTION_TABLE_ROW_ID);
        GUIStatusAction.clickAddPreAction();
        GUIStatusAction.fillFieldsOnFormAdd(preAction.getTitle(), script);
        GUIScriptComponentEdit componentEdit = new GUIScriptComponentEdit(GUIXpath.Any.SCRIPT_VALUE);
        componentEdit.closeProperties();
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        Set<String> newIdSet = GUIStatusAction.getRowIdsFromSimpleList(GUIStatusAction.PRE_ACTION_TABLE_ROW_ID);
        newIdSet.removeAll(oldIdSet);
        preAction.setCode(newIdSet.iterator().next());
        preAction.setExists(true);
        GUIStatusAction.goToStatusActionCard(preAction);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());

        tester.click(GUIXpath.Div.EDIT);

        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUISettingsSet.fillSettingsSetPropOnForm(set2.getCode());
        GUIForm.applyForm();

        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());
    }

    /**
     * Тестирование разметки шаблонов отчетов комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Шаблоны отчетов и печатных форм"</li>
     * <li>нажать кнопку "Добавить шаблон"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан шаблон template</li>
     * <li>Перейти на карточку шаблона template</li>
     * <li>Проверить, что на карточке выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке template</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен шаблон, в нем
     *  изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkReportTemplateWithSet()
    {
        //Выполнение действий и проверки
        ReportTemplate template = DAOReportTemplate.createReportTemplate(DAOReportTemplate.TEMPLATE1);

        GUILogon.asSuper();
        GUINavigational.goToReportTemplates();
        GUIReportTemplateList.clickAdd();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, template.getTitle());
        tester.sendKeys(GUIXpath.Input.CODE_VALUE, template.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.DESCR_VALUE, template.getDescription());
        GUIFileAdmin.uploadFile(GUIXpath.Any.TEMPLATE_FILE_VALUE, template.getFilename());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();
        template.setExists(true);

        GUIReportTemplate.goToCard(template);
        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIReportTemplate.clickEdit();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.REPORT_TEMPLATES_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен шаблон '%s' (%s):\n"
                                                   + "Комплект: '%s' -> '%s'",
                template.getTitle(), template.getCode(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки профилей связанных жизненных циклов комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Настройка бизнес процессов" - "Профили связанных жиненнных циклов"</li>
     * <li>Нажать кнопку "Добавить профиль"</li>
     * <li>Заполнить необходимые поля на форме добавления</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создан профиль profile</li>
     * <li>Проверить, что на карточке профилья profile выбран комплект set1</li>
     * <li>Нажать кнопку "Редактировать" на карточке profile</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменен профиль связанного жизненного цикла, в
     * нем
     *  изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkWfProfilesWithSet()
    {
        //Выполнение действий и проверки
        MetaClass scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase1);

        GUILogon.asSuper();
        GUIWfProfile.goToList();
        tester.click(GUIXpath.Div.ADD_ELEMENT);
        GUIForm.assertDialogAppear("Форма на добавление профиля связанных жизненных циклов не появилась.");

        //Заполняем поля на форме добавления
        WfProfile profile = DAOWfProfile.create(scCase1, scCase1);
        GUIWfProfile.fillAddOrEditForm(profile);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyModalForm();
        //Добавляем профиль в очередь на удаление
        GUIWfProfile.setUuidByUrl(profile);

        GUISettingsSet.assertSettingsSetOnCards(set1.getTitle());
        GUIWfProfile.clickEdit();
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyModalForm();

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.WF_PROFILE_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен профиль связанных жизненных циклов '%s':\n"
                                                   + "Комплект: '%s' -> '%s'",
                profile.getTitle(), set1.getTitle(), set2.getTitle());
        Assert.assertTrue("Запись лога технолога не соответствует ожидаемой:",
                actualDescription.contains(expectedDecsription));
    }

    /**
     * Тестирование разметки таблиц соответствий в эскалациях комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Схемы эскалации" вкладка "Таблицы соответствий"</li>
     * <li>Нажать кнопку "Добавить элемент"</li>
     * <li>Заполнить необходимые поля на форме добавления таблицы соответствий</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана таблица соответствий escalationRule</li>
     * <li>Нажать кнопку "Редактировать" у таблицы escalationRule</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * <li>В поле выбора комплекта выбрать комплект set2</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Перейти на карточку таблицы соответствий escalationRule</li>
     * <li>Проверить, что на карточке заполнен комплект значением set2</li>
     * <li>Проверить, что в логе технолога появилась запись о том, что изменена таблица соответствий, в ней
     * изменился комлект с set1 на set2</li>
     * </ol>
     */
    @Test
    public void testMarkRulesSettingsWithSet()
    {
        MetaClass scClass = DAOScCase.createClass();
        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(scClass,
                SysAttribute.metaClass(scClass).getCode());

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToRuleSettings();
        Set<String> oldElementsUuids = DSLCatalogItem.getRuleSettingsEscalationUuids();
        GUIRulesSettingsEscalation.clickAdd();
        GUIRulesSettingsEscalation.setTitle(escalationRule.getTitle());
        GUIRulesSettingsEscalation.setCode(escalationRule.getCode());
        GUIRulesSettingsEscalation.setDescription(escalationRule.getDescription());
        GUIRulesSettingsEscalation.setObjects(scClass);
        GUICatalogItem.addRulesSettingsSourcesOnAddForm(escalationRule);
        GUISettingsSet.fillSettingsSetOnForm(set1.getCode());
        GUIForm.applyForm();
        GUIRulesSettingsEscalation.setUuidByList(escalationRule, oldElementsUuids);

        GUIRulesSettingsEscalation.clickEditIcon(escalationRule);
        GUISettingsSet.assertSettingsSetOnForm(set1);
        GUISettingsSet.fillSettingsSetOnForm(set2.getCode());
        GUIForm.applyForm();
        GUIRulesSettingsEscalation.goToCardFromList(escalationRule);
        GUISettingsSet.assertSettingsSetOnCards(set2.getTitle());

        LogEntry lastByCategoryCode = DSLAdminLog.getLastByCategoryCode(CategoryCode.ESCALATION_TABLE_SETTINGS);
        String actualDescription = lastByCategoryCode.getDescription();
        String expectedDecsription = String.format("Изменен элемент таблицы соответствий '%s' "
                                                   + "(%s):\n"
                                                   + "Комплект: '%s' -> '%s'.",
                escalationRule.getTitle(), escalationRule.getCode(), set1.getCode(), set2.getCode());
        Assert.assertEquals("Запись лога технолога не соответствует ожидаемой:", expectedDecsription,
                actualDescription);
    }

    /**
     * Тестирование разметки строки таблицы соответствий в эскалациях комплектами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241300955
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <li>Создать пользовательский класс userClass1</li>
     * <li>Создать счетчик времени timerDefinition</li>
     * <li>Содать схему эскалации scheme для класса userClass1 используется счетчик времени timerDefinition</li>
     * <li>Создать таблицу соответствий escalationRule</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку таблицы соответсвий escalationRule</li>
     * <li>Нажать кнопку "Добавить строку"</li>
     * <li>Заполнить необходимые поля на форме добавления строки</li>
     * <li>В поле выбора комплекта выбрать комплект set1</li>
     * <li>Нажать на кнопку "Сохранить", будет создана строка</li>
     * <li>Нажать кнопку "Редактировать" у первой строки</li>
     * <li>В поле выбора комплекта проверить, что выбрано set1</li>
     * </ol>
     */
    @Test
    public void testMarkRullesSettingsItemWithSet()
    {
        MetaClass userClass1 = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass1);

        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(userClass1.getFqn(),
                SysAttribute.timeZone(userClass1).getCode(), SysAttribute.serviceTime(userClass1).getCode(),
                SysAttribute.resolutionTime(userClass1).getCode(), DAOBoStatus.createRegistered(userClass1.getFqn()));
        DSLTimerDefinition.add(timerDefinition);

        EscalationScheme scheme = DAOEscalationSheme.create(timerDefinition, false, userClass1);
        DSLEscalation.add(scheme);

        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(userClass1,
                SysAttribute.metaClass(userClass1).getCode());
        DSLCatalogItem.add(escalationRule);

        // Выполнение действия
        GUILogon.asSuper();
        GUIEscalation.goToRuleSettings();
        GUIRulesSettingsEscalation.goToCardFromList(escalationRule);

        GUIRulesSettings.clickAddRSRow();
        GUIMultiSelect.select(Div.VALUE + Input.INPUT_PREFIX, scheme.getCode());
        GUISettingsSet.fillSettingsSetPropOnForm(set1.getCode());
        GUIForm.applyForm();

        GUIRulesSettings.clickRSEditRow();
        GUISettingsSet.assertSettingsSetPropOnForm(set1);
        GUIForm.cancelDialog();
    }

    /**
     * Тестирование добавления и удаления мест использования у комплекта при добавлении и удалении комплекта из
     * настройки метакласса и вложенных для метакласса настроек
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <li>Создать комплект set</li>
     * <li>Создать пользовательский класс userClass1 размеченный комплектом set</li>
     * <li>В классе userClass1 создать строковый атрибут stringAttr, размеченный комплектом</li>
     * <li>В классе userClass1 создать группу атрибутов attrGroup, размеченную комплектом и добавить в нее атрибут
     * stringAttr</li>
     * <li>На карточке класса userClass1 создать контент "параметры объекта" propertyList, размеченный комплектом
     * set</li>
     * <li>В классе userClass1 создать форму быстрого добавления и редактирования fastForm, размеченную комплектом
     * set</li>
     * <li>В классе userClass1 создать пользовательский статус userState, размеченный комплектом set</li>
     * <li>В классе userClass1 настроить переход из статуса registered в userState и разметить его комплектом set</li>
     * <li>Для статуса userState настроить действие на входе в статус statusAction со скриптом scriptInfo</li>
     * <li>Действие statusAction и скрипт scriptInfo разметить комплектом set</li>
     * <li>Для статуса userState настроить условие на выходе из статуса statusCondition и разметить его комплектом
     * set</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить все места использования комплекта set и проверить, что их 10 штук</li>
     * <li>Удалить метакласс userClass1</li>
     * <li>Удалить скрипт scriptInfo</li>
     * <li>Получить все места использования комплекта set и проверить, что их 0 штук</li>
     * </ol>
     */
    @Test
    public void testSettingsSetUsagePoint()
    {
        //Подготовка
        SettingsSet set = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(set);

        MetaClass userClass1 = DAOUserClass.createWithWF();
        userClass1.setSettingsSet(set);
        DSLMetaClass.add(userClass1);

        Attribute stringAttr = DAOAttribute.createString(userClass1);
        stringAttr.setSettingsSet(set);
        DSLAttribute.add(stringAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass1);
        attrGroup.setSettingsSet(set);
        DSLGroupAttr.add(attrGroup, stringAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(userClass1);
        propertyList.setSettingsSet(set);
        DSLContent.add(propertyList);

        CustomForm fastForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass1), userClass1);
        fastForm.setSettingsSet(set);
        DSLCustomForm.add(fastForm);

        BoStatus registered = DAOBoStatus.createRegistered(userClass1);
        BoStatus userState = DAOBoStatus.createUserStatus(userClass1);
        userState.setSettingsSet(set);
        DSLBoStatus.add(userState);

        Transition transition = DAOTransition.createTransition(userClass1, registered, userState, "", false,
                false);
        transition.setSettingsSet(set);
        DSLBoStatus.setTransitions(registered, userState);
        DSLTransition.editTransitions(transition);

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return");
        scriptInfo.setSettingsSet(set.getCode());
        StatusAction statusAction = DSLBoStatus.addPreAction(userState, scriptInfo, set);
        StatusAction statusCondition = DSLBoStatus.addPostCondition(userState, scriptInfo, set);

        //Выполнение действий и проверки
        String result = DSLSettingsSet.getSettingsSetUsagePoints(set);

        List<String> usagePoints = Json.GSON.fromJson(result, Json.LIST_STRING_TYPE);
        Assert.assertEquals("У комплекта должно быть 10 мест использования ", 10, usagePoints.size());

        DSLMetaClass.delete(userClass1);
        DSLScriptInfo.deleteScript(scriptInfo);

        attrGroup.setExists(false);
        propertyList.setExists(false);
        fastForm.setExists(false);
        userState.setExists(false);
        transition.setExists(false);
        statusAction.setExists(false);
        statusCondition.setExists(false);

        result = DSLSettingsSet.getSettingsSetUsagePoints(set);
        usagePoints = Json.GSON.fromJson(result, Json.LIST_STRING_TYPE);
        Assert.assertEquals("У комплекта должно быть 0 мест использования ", 0, usagePoints.size());
    }

    /**
     * Тестирование удаления комплекта из настроек скриптов и элемнтоа навигационного меню МК, если элемент вложен в
     * родительский
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <li>Создать комплект set</li>
     * <li>Создать скрипт scriptInfo размеченный комплектом set</li>
     * <li>Создать родительский элемент навигационного меню в МК parentChapter</li>
     * <li>Создать элемент навигационного меню menuItem2, размеченный комплектом set, вложенный в parentChapter</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Удалить комплект set</li>
     * <li>Выгрузить частичную метаинформацию со скриптом scriptInfo и настройками МК в файл metainfoFile</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы "<set>"</li>
     * </ol>
     */
    @Test
    public void testSettingsSetScriptUsagePoint()
    {
        //Подготовка
        SettingsSet set = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(set);

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return");
        scriptInfo.setSettingsSet(set.getCode());
        DSLScriptInfo.addScript(scriptInfo);

        MobileMenuItem parentChapter = DAOMobileMenuItem.createChapter();
        MobileMenuItem menuItem2 = DAOMobileMenuItem.createBarcodeScanButton();
        menuItem2.setParent(parentChapter);
        menuItem2.setSettingsSet(set.getCode());
        DSLMobileMenuItem.add(parentChapter);
        DSLMobileMenuItem.add(menuItem2);

        //Выполнение действий и проверки
        DSLSettingsSet.delete(set);
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectScripts(exportModel, scriptInfo);
        DAOMetainfoExport.selectMobileApp(exportModel);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }
}
