package ru.naumen.selenium.cases.operator.embeddedapplication.forms;

import static ru.naumen.selenium.cases.operator.embeddedapplication.forms.JsApiFormsValidationTest.VALIDATION_DROPS_ERROR;
import static ru.naumen.selenium.cases.operator.embeddedapplication.forms.JsApiFormsValidationTest.VALIDATION_FAILURE;
import static ru.naumen.selenium.cases.operator.embeddedapplication.forms.JsApiFormsValidationTest.VALIDATION_SUCCESS;
import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.EDITFORM;
import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.NEWENTRYFORM;
import static ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication.FormType.QuickAddAndEditForm;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.file.DAOSdFile;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование валидации значений внутри встроенного приложения через JS API при сохранении формы
 * (jsApi.forms.registerValidator)
 *
 * <AUTHOR>
 * @since 14.01.2022
 */
class JsApiFormsValidation2Test extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass scCase;
    private static Attribute boLinksAttr;
    private static CustomForm quickForm;
    private Bo sc;

    /**
     * <ol>
     *   <b>Общая подготовка для всех тестов.</b>
     *   <li>Создать тип scCase в классе Запрос</li>
     *   <li>Создать группу атрибутов attrGroup в типе scCase</li>
     *   <li>Создать контент content типа "Параметры на форме" в типе scCase, содержащий группу атрибутов attrGroup</li>
     *   <li>Создать форму быстрого добавления quickForm в типе scCase, содержащую группу атрибутов attrGroup</li>
     *   <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО", с формой быстрого добавления - quickForm</li>
     *   <li>Добавить атрибут boLinksAttr в группу attrGroup</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        scCase = DAOScCase.create();
        DSLMetainfo.add(scCase);

        MetaClass userCase = SharedFixture.userCase();
        quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userCase), userCase);
        DSLCustomForm.add(quickForm);

        boLinksAttr = DAOAttribute.createBoLinks(scCase, userCase);
        boLinksAttr.setQuickAddForm(quickForm.getUuid());
        DSLMetainfo.add(boLinksAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(attrGroup, boLinksAttr);
        ContentForm content = DAOContentAddForm.createEditablePropertyList(scCase, attrGroup);
        DSLContent.add(content);
    }

    /**
     * <ol>
     *   <b>Общая подготовка.</b>
     *   <li>{@link #prepareFixture() Общая подготовка для всех тестов}</li>
     *   <li>Создать запрос sc типа scCase</li>
     * </ol>
     */
    @BeforeEach
    public void setUp()
    {
        sc = DAOSc.create(scCase);
        DSLBo.add(sc);
    }

    /**
     * Тестирование успешной валидации внутри ВП на форме Добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет
     *   успешно выполнять валидацию, и добавить его на форму Добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Заполнить основные атрибуты запроса</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что произошёл переход на карточку запроса типа scCase</li>
     * </ol>
     */
    @Test
    void testSuccessValidationOnAddForm()
    {
        // Подготовка
        ContentForm applicationContent = createApplication(VALIDATION_SUCCESS, NEWENTRYFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        Bo sc = DAOSc.create(scCase);
        GUIBo.fillScMainFields(sc);
        GUIForm.applyForm();

        GUIBo.assertThatBoCaseCard(scCase);
        DAOBo.createModelByUuid(GUIBo.getUuidByUrl());
    }

    /**
     * Тестирование успешной валидации внутри ВП на форме Редактирования объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет
     *   успешно выполнять валидацию, и добавить его на форму Редактирования объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму редактирования запроса sc</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что произошёл переход на карточку запроса sc</li>
     * </ol>
     */
    @Test
    void testSuccessValidationOnEditForm()
    {
        // Подготовка
        ContentForm applicationContent = createApplication(VALIDATION_SUCCESS, EDITFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(sc);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIForm.applyForm();

        GUIBo.assertThatBoCard(sc);
    }

    /**
     * Тестирование успешной валидации внутри ВП на форме Быстрого добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String) Создать встроенное приложение}, которое будет успешно выполнять
     *   валидацию, и добавить его на форму Быстрого добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Нажать кнопку быстрого добавления объекта в атрибуте boLinksAttr</li>
     *   <li>Проверить, что модальная форма появилась</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что форма исчезла</li>
     * </ol>
     */
    @Test
    void testSuccessValidationOnQuickForms()
    {
        // Подготовка
        UsagePointApplication applicationUsage = createApplication(VALIDATION_SUCCESS);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIForm.clickQuickAddForm(boLinksAttr);
        GUIForm.assertFormAppear();
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationUsage);
        GUIForm.fillTitle(ModelUtils.createTitle());
        GUIForm.applyModalForm();
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое не будет
     *   успешно выполнять валидацию, и добавить его на форму Добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Заполнить основные атрибуты запроса</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что с формы не был выполнен переход на карточку запроса</li>
     *   <li>Проверить, что появился блокирующий экран на форме</li>
     * </ol>
     */
    @Test
    void testFailValidationOnAddForm()
    {
        // Подготовка
        ContentForm applicationContent = createApplication(VALIDATION_FAILURE, NEWENTRYFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        Bo sc = DAOSc.create(scCase);
        GUIBo.fillScMainFields(sc);
        GUIForm.clickApply();

        GUIBo.assertThatAddForm(scCase);
        GUIForm.assertFormBlockerAbsence();
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Редактирования объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое не будет
     *   успешно выполнять валидацию, и добавить его на форму Редактирования объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму редактирования запроса sc</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что с формы не был выполнен переход на карточку запроса sc</li>
     *   <li>Проверить, что появился блокирующий экран на форме</li>
     * </ol>
     */
    @Test
    void testFailValidationOnEditForm()
    {
        // Подготовка
        ContentForm applicationContent = createApplication(VALIDATION_FAILURE, EDITFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(sc);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIForm.clickApply();

        GUIBo.assertThatEditForm(sc);
        GUIForm.assertFormBlockerAbsence();
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Быстрого добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String) Создать встроенное приложение}, которое не будет успешно выполнять
     *   валидацию, и добавить его на форму Быстрого добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Нажать кнопку быстрого добавления объекта в атрибуте boLinksAttr</li>
     *   <li>Проверить, что модальная форма появилась</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что форма не исчезла</li>
     *   <li>Проверить, что появился блокирующий экран на форме</li>
     * </ol>
     */
    @Test
    void testFailValidationOnQuickForms()
    {
        // Подготовка
        UsagePointApplication applicationUsage = createApplication(VALIDATION_FAILURE);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIForm.clickQuickAddForm(boLinksAttr);
        GUIForm.assertFormAppear();
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationUsage);
        GUIForm.clickApplyTopmostDialog();

        GUIForm.assertDialogAppear("Форма добавление объекта исчезла");
        GUIForm.assertFormBlockerAbsence();
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Добавления объекта, когда в ходе валидации
     * выброшена ошибка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет в ходе
     *   валидации выбрасывать ошибку c с текстом errorText, и добавить его на форму Добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Заполнить основные атрибуты запроса</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что на форме появилось сообщение об ошибке с текстом errorText<li>
     * </ol>
     */
    @Test
    void testValidationDropsErrorOnAddForm()
    {
        // Подготовка
        String errorText = ModelUtils.createTitle();
        String applicationTemplate = String.format(VALIDATION_DROPS_ERROR, errorText);
        ContentForm applicationContent = createApplication(applicationTemplate, NEWENTRYFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        Bo sc = DAOSc.create(scCase);
        GUIBo.fillScMainFields(sc);
        GUIForm.applyFormAssertError(errorText);
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Редактирования объекта, когда в ходе валидации
     * выброшена ошибка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет в ходе
     *   валидации выбрасывать ошибку c с текстом errorText, и добавить его на форму Редактирования объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму редактирования запроса sc</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что на форме появилось сообщение об ошибке с текстом errorText<li>
     * </ol>
     */
    @Test
    void testValidationDropsErrorOnEditForm()
    {
        // Подготовка
        String errorText = ModelUtils.createTitle();
        String applicationTemplate = String.format(VALIDATION_DROPS_ERROR, errorText);
        ContentForm applicationContent = createApplication(applicationTemplate, EDITFORM);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(sc);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIForm.applyFormAssertError(errorText);
    }

    /**
     * Тестирование не успешной валидации внутри ВП на форме Быстрого добавления объекта, когда в ходе валидации
     * выброшена ошибка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$138199606
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>{@link #createApplication(String) Создать встроенное приложение}, которое будет в ходе
     *   валидации выбрасывать ошибку c с текстом errorText, и добавить его на форму Быстрого добавления объекта</li>
     * </ol>
     * <ol>
     *   <b>Выполнение действий и проверки</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления запроса для типа scCase</li>
     *   <li>Нажать кнопку быстрого добавления объекта в атрибуте boLinksAttr</li>
     *   <li>Проверить, что модальная форма появилась</li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что на форме появилось сообщение об ошибке с текстом errorText<li>
     * </ol>
     */
    @Test
    void testValidationDropsErrorOnQuickForms()
    {
        // Подготовка
        String errorText = ModelUtils.createTitle();
        String applicationTemplate = String.format(VALIDATION_DROPS_ERROR, errorText);
        UsagePointApplication applicationUsage = createApplication(applicationTemplate);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOScCase.createClass(), scCase);
        GUIForm.clickQuickAddForm(boLinksAttr);
        GUIForm.assertFormAppear();
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationUsage);
        GUIForm.fillTitle(ModelUtils.createTitle());
        GUIForm.clickApplyTopmostDialog();
        GUIForm.assertErrorMessageOnForm(errorText);
    }

    /**
     * Тестирование отсутствия валидации на модальной форме без ВП, если она была запущена из ВП, на которой
     * настроена валидация
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257262146
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет
     * выполнять валидацию и запускать модальную форму, и добавить его на карточку типа scCase. Код встроенного
     * приложения:
     * <pre>
     *     ----------------------------------------------------------------
     *     jsApi.forms.registerValidator(() => {
     *         return false
     *     })
     *     jsApi.forms.changeResponsible('$scUuid')
     *     ----------------------------------------------------------------
     *     Где scUuid - UUID объекта sc
     * </pre></li>
     * <b>Действия и проверки.</b>
     * <li>Войти под пользователем</li>
     * <li>Перейти на карточку типа scCase</li>
     * <li>Проверить, что модальная форма появилась</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что форма успешно закрылась</li>
     * </ol>
     */
    @Test
    void testValidationDoesNotStartOnModalFormWithoutEmbeddedApplication()
    {
        //Подготовка
        ContentForm applicationContent = createApplication("""
                        jsApi.forms.registerValidator(() => {
                            return false
                        })
                        jsApi.forms.changeResponsible('%s')""".formatted(sc.getUuid()),
                MetaclassCardTab.OBJECTCARD);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIForm.assertFormAppear();
        GUIForm.applyForm();
    }

    /**
     * Тестирование отсутствия валидации на модальной форме добавления файла без ВП, если форма была запущена из
     * таба, одним из контентов которого является ВП с настроенной валидацией
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257262146
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>{@link #createApplication(String, MetaclassCardTab) Создать встроенное приложение}, которое будет
     * выполнять валидацию, и добавить его на карточку типа scCase. Код встроенного
     * приложения:
     * <pre>
     *     ----------------------------------------------------------------
     *     jsApi.forms.registerValidator(() => {
     *         return false
     *     })
     *     ----------------------------------------------------------------
     * </pre></li>
     * <li>Вывести рядом со встроенным приложением контент "Список файлов"</li>
     * <b>Действия и проверки.</b>
     * <li>Войти под пользователем</li>
     * <li>Перейти на карточку типа scCase</li>
     * <li>Нажать кнопку "Добавить файл"</li>
     * <li>На открывшейся форме заполнить все обязательные поля</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что форма успешно закрылась</li>
     * </ol>
     */
    @Test
    void testValidationDoesNotStartOnAddFileFormWithoutEmbeddedApplication()
    {
        //Подготовка
        ContentForm applicationContent = createApplication("""
                        jsApi.forms.registerValidator(() => {
                            return false
                        })""",
                MetaclassCardTab.OBJECTCARD);
        ContentForm fileList = DAOContentCard.createFileList(scCase);
        DSLContent.add(fileList);

        SdFile sdFile = DAOSdFile.create(DSLFile.IMG_FOR_UPLOAD);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        GUIFileList.addFile(fileList, sdFile);
        GUIForm.applyForm();
    }

    /**
     * Тестирование валидации на форме добавления записи, если встроенное приложение вложено в панель вкладок <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$266477342 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #setUp() Общая подготовка}</li>
     * <li>На форму добавления типа запроса scCase добавить панель вкладок tabBar</li>
     * <li>Добавить встроенное приложение исполняемое на стороне клиента содержащее скрипт:
     * <pre>
     *                 jsApi.forms.registerValidator(() => {
     *                     document.getElementById('test_div').innerText = 'registerValidator called'
     *                     return false;
     *                 })
     *                 Где test_div - id тега DIV из встроенного приложения
     * </pre></li>
     * <br>
     * <li>Добавить встроенное приложение на первую вкладку панели вкладок tabBar</li>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Перейти на форму добавления типа запроса scCase</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Убедиться, что во встроенном приложении отображено сообщение "registerValidator called"</li>
     * </ol>
     */
    @Test
    void testValidationOnModalFormTab()
    {
        //Подготовка
        ContentForm tabBar = DAOContentAddForm.createTabBar(scCase);
        DSLContent.add(tabBar);
        ContentTab applicationTab = DSLContent.getFirstTab(tabBar);

        String validationMessage = ModelUtils.createTitle();
        EmbeddedApplication application = createApplicationWithJs("""
                jsApi.forms.registerValidator(() => {
                    document.getElementById('%s').innerText = '%s'
                    return false;
                })
                """.formatted(GUIEmbeddedApplication.TEST_DIV_ID, validationMessage));
        ContentForm applicationContent = DAOContentForm.createEmbeddedApplication(scCase, application, NEWENTRYFORM);
        DSLContent.add(applicationTab, applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(scCase);

        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);

        //Результат не будет сохранен, т.к. сработает валидация, в т.ч. в ВП
        GUIForm.clickApply();

        Assertions.assertEquals(validationMessage,
                GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent),
                "Скрипт валидации из встроенного приложения не был вызван");
    }

    /**
     * <ol>
     *   <b>Создать встроенное приложение на формы быстрого добавления или редактирования объектов.</b>
     *   <li>Создать новое встроенное приложение application с js-кодом applicationTemplate</li>
     *   <li>Включить возможность размещения приложения application на модальных формах</li>
     *   <li>Добавить для application место использования в классе Запрос для формы добавления или редактирования
     *   объектов</li>
     * </ol>
     *
     * @param applicationTemplate js-код встроенного приложения
     */
    private UsagePointApplication createApplication(String applicationTemplate)
    {
        EmbeddedApplication application = createApplicationWithJs(applicationTemplate);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(application);

        UsagePointApplication applicationUsage =
                DAOEmbeddedApplication.createUsagePointApplication(QuickAddAndEditForm, SharedFixture.userClass());
        applicationUsage.setAvailableForms(quickForm);
        DSLEmbeddedApplication.edit(application, null, applicationUsage);

        return applicationUsage;
    }

    /**
     * <ol>
     *   <b>Создать встроенное приложение и вывести его на форму добавления/редактирования или карточку</b>
     *   <li>{@link #createApplication(String) Создать новое ВП application с js-кодом applicationTemplate}</li>
     *   <li>Добавить application на форму добавления/редактирования в типе scCase</li>
     * </ol>
     *
     * @param applicationTemplate js-код встроенного приложения
     * @param tab формы добавления/редактирования или карточка
     */
    private ContentForm createApplication(String applicationTemplate, MetaclassCardTab tab)
    {
        EmbeddedApplication application = createApplicationWithJs(applicationTemplate);

        ContentForm applicationContent = DAOContentForm.createEmbeddedApplication(scCase, application, tab);
        DSLContent.add(applicationContent);

        return applicationContent;
    }

    /**
     * Создать новое встроенное приложение application с js-кодом applicationTemplate
     *
     * @param applicationTemplate js-код встроенного приложения
     */
    private EmbeddedApplication createApplicationWithJs(String applicationTemplate)
    {
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                applicationTemplate);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(application);

        return application;
    }
}
