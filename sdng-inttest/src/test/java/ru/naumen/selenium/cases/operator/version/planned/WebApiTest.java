package ru.naumen.selenium.cases.operator.version.planned;

import static ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion.BRANCH_NOT_FOUND_LIST_MSG;
import static ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion.NOT_AVAILABLE_AT_CURRENT_MODE_ATTR_VALUE_MSG;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIAttention;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLBranch;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBranchCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование api.web для работы с плановыми версиями
 *
 * <AUTHOR>
 * @since 15.10.2020
 */
public class WebApiTest extends AbstractTestCase
{
    private static MetaClass userVersionedClass;
    private static MetaClass userVersionedCase;
    private static MetaClass branchCase;

    /**
     * Общая подготовка для тестов
     * <br>
     * <ol>
     * <li>Установить лицензию с модулем Планового версионирования</li>
     * <li>Добавить версионируемый пользовательский класс userVersionedClass и его тип userVersionedCase</li>
     * <li>Добавить тип ветки branchCase</li>
     * <li>Добавить тип сотрудника emplCase</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.PLANNED_VERSION_LICENSE);
        userVersionedClass = DAOUserClass.createWithPlannedVersions();
        userVersionedCase = DAOUserCase.create(userVersionedClass);
        branchCase = DAOBranchCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(userVersionedClass, userVersionedCase, emplCase, branchCase);
    }

    /**
     * Тестирование перехода по ссылкам, сгенерированным на карточку объекта из другой ветки и форму редактирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95141524
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Добавить ветку branch1, branch2 (branchCase), bo (userVersionedCase)</li>
     * <li>Создать версию boVersion1 объекта bo в ветке branch1</li>
     * <li>Создать версию boVersion2 объекта bo в ветке branch2</li>
     * <li>С помощью метода api.web.open('%s') сгенерировать ссылки на карточки объектов bo, boVersion1, boVersion2</li>
     * <li>С помощью метода api.web.edit('%s') сгенерировать ссылки на формы редактирования объектов bo, boVersion1,
     * boVersion2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <b>Войти под тестировщиком</b>
     * <b>Перейти по ссылке на карточку объекта boVersion1 в ветке branch1</b>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch1</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch1</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch1</li>
     * <b>Перейти по ссылке на карточку объекта boVersion2 в ветке branch2</b>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch2</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch2</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch2</li>
     * <b>Перейти по ссылке на карточку объекта bo в основной ветке</b>
     * <li>Проверить, что находимся основной ветке</li>
     * <b>Перейти по ссылке на форму редактирования объекта boVersion1 в ветке branch1</b>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch1</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch1</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch1</li>
     * <b>Перейти по ссылке на форму редактирования объекта boVersion2 в ветке branch2</b>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch2</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch2</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch2</li>
     * <b>Перейти по ссылке на форму редактирования объекта bo в основной ветке</b>
     * <li>Проверить, что находимся основной ветке</li>
     * </ol>
     */
    @Test
    public void testGoToLinkByObjectFromAnotherBranch()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo);
        Bo boVersion1 = DSLBranch.addToBranch(branch1, bo).get(0);
        Bo boVersion2 = DSLBranch.addToBranch(branch2, bo).get(0);

        String scriptPattern = "return api.web.open('%s');";
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, boVersion1.getUuid()));
        String openLinkToBoBranch1 = script.runScript().get(0).trim();
        script = new ScriptRunner(String.format(scriptPattern, boVersion2.getUuid()));
        String openLinkToBoBranch2 = script.runScript().get(0).trim();
        script = new ScriptRunner(String.format(scriptPattern, bo.getUuid()));
        String openLinkToBoMasterBranch = script.runScript().get(0).trim();

        scriptPattern = "return api.web.edit('%s');";
        script = new ScriptRunner(String.format(scriptPattern, boVersion1.getUuid()));
        String editLinkToBoBranch1 = script.runScript().get(0).trim();
        script = new ScriptRunner(String.format(scriptPattern, boVersion2.getUuid()));
        String editLinkToBoBranch2 = script.runScript().get(0).trim();
        script = new ScriptRunner(String.format(scriptPattern, bo.getUuid()));
        String editLinkToBoMasterBranch = script.runScript().get(0).trim();

        //Выполнение действий и проверки
        GUILogon.asTester();
        tester.goToPage(openLinkToBoBranch1);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch1);
        GUIPlannedVersion.assertCurrentBranch(branch1);

        tester.goToPage(openLinkToBoBranch2);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch2);
        GUIPlannedVersion.assertCurrentBranch(branch2);

        tester.goToPage(openLinkToBoMasterBranch);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertCurrentBranch(null);

        tester.goToPage(editLinkToBoBranch1);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch1);
        GUIPlannedVersion.assertCurrentBranch(branch1);

        tester.goToPage(editLinkToBoBranch2);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch2);
        GUIPlannedVersion.assertCurrentBranch(branch2);

        tester.goToPage(editLinkToBoMasterBranch);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertCurrentBranch(null);
    }

    /**
     * Тестирование перехода по ссылке, сгенерированной с помощью api.web.add на форму добавления, с указанием
     * в параметрах в качестве значений ссылочных атрибутов, объектов из ветки, которая отличается от текущей ветки
     * пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95141524
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Добавить ветку branch1, branch2 (branchCase), bo1 (userVersionedCase), bo2 (userVersionedCase)</li>
     * <li>Создать версию boVersion1 объекта bo1 в ветке branch1</li>
     * <li>Создать версию boVersion2 объекта bo1 в ветке branch2</li>
     * <li>Создать версию bo2Version1 объекта bo2 в ветке branch1</li>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В класс userClass добавить атрибут objectLinkAttr типа "СБО" и boLinksAttr типа "НБО" на класс
     * userVersionedClass</li>
     * <li>Добавить атрибуты в группу "Системные атрибуты" для класса userClass</li>
     * <li>С помощью метода api.web.add сгенерировать ссылку url1 на форму добавления для типа userCase, указав в
     * качестве значения атрибута objectLinkAttr объект boVersion1 из ветки branch1, а в качестве значения атрибута
     * boLinksAttr объекты  boVersion1 и boVersion2 из веток branch1 и branch2</li>
     * <li>С помощью метода api.web.add сгенерировать ссылку url2 на форму добавления для типа userCase, указав
     * в качестве значения атрибута boLinksAttr объекты  boVersion1 и bo2Version1 из ветки branch1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под тестировщиком</li>
     * <li>Перейти в ветку branch1</li>
     * <li>Перейти по сгенерированной ссылке url1 на форму добавления</li>
     * <li>Проверить, что на форме добавления присутствует предупреждение о недоступных в текущем режиме работы
     * значениях атрибута, а также о недоступных для редактирования в текущем режиме атрибутов</li>
     * <li>Перейти по сгенерированной ссылке url2 на форму добавления</li>
     * <li>Проверить, что на форме добавления отсутствует предупреждение</li>
     * </ol>
     */
    @Test
    public void testGoToLinkToAddFromWithNotAvailableObjects()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo1 = DAOUserBo.create(userVersionedCase);
        Bo bo2 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo1, bo2);
        Bo boVersion1 = DSLBranch.addToBranch(branch1, bo1).get(0);
        Bo boVersion2 = DSLBranch.addToBranch(branch2, bo1).get(0);
        Bo bo2Version1 = DSLBranch.addToBranch(branch1, bo2).get(0);

        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Attribute objectLinkAttr = DAOAttribute.createObjectLink(userClass, userVersionedClass, null);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, userVersionedClass);
        DSLAttribute.add(objectLinkAttr, boLinksAttr);

        GroupAttr sysGroupNonVersions = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysGroupNonVersions, new Attribute[] { objectLinkAttr, boLinksAttr }, new Attribute[0]);

        String scriptPattern1 = "return api.web.add('%s', null, ['%s' : '%s', '%s' : ['%s', '%s']], false);";
        ScriptRunner script1 = new ScriptRunner(String.format(scriptPattern1, userCase.getFqn(),
                objectLinkAttr.getCode(), boVersion1.getUuid(), boLinksAttr.getCode(), boVersion1.getUuid(),
                boVersion2.getUuid()));
        String url1 = script1.runScript().get(0).trim();

        String scriptPattern2 = "return api.web.add('%s', null, ['%s' : ['%s', '%s']], false);";
        ScriptRunner script2 = new ScriptRunner(String.format(scriptPattern2, userCase.getFqn(),
                boLinksAttr.getCode(), boVersion1.getUuid(), bo2Version1.getUuid()));
        String url2 = script2.runScript().get(0).trim();

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch1);

        tester.goToPage(url1);
        GUIAttention.assertAttentionMessage(String.format(NOT_AVAILABLE_AT_CURRENT_MODE_ATTR_VALUE_MSG,
                boVersion2.getTitle(), boLinksAttr.getTitle(), objectLinkAttr.getTitle()));
        tester.goToPage(url2);
        GUIForm.assertAttentionAbsence();
    }

    /**
     * Тестирование открытия ссылки на список, сгенерированной с помощью api.web.list в контексте указанной ветки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00831
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95141524
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Добавить ветку branch1, branch2 (branchCase), bo (userVersionedCase)</li>
     * <li>Создать версию boVersion1 объекта bo в ветке branch1</li>
     * <li>Создать версию boVersion2 объекта bo в ветке branch2</li>
     * <li>Сгенерировать ссылку linkToList на список объектов на отдельной странице класса userVersionedClass, указав в
     * качестве ветки branch1</li>
     * <li>На карточку компании добавить контент objectList "Список объектов" класса userVersionedClass</li>
     * <li>Сгенерировать ссылку linkToListOnCard на список объектов objectList на карточке компании, указав в
     * качестве ветки
     * branch2</li>
     * <li>Сгенерировать ссылку linkToListWithoutBranch на список объектов objectList на карточке компании, указав в
     * качестве ветки branch2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под тестировщиком</li>
     * <b>Перейти по ссылке linkToList на список на отдельной странице в ветке branch1</b>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch1</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch1</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch1</li>
     * <li>Проверить, что в списке отображается объект boVersion1</li>
     * <li>Перейти по ссылке linkToListOnCard на список на карточке компании в ветке branch2</li>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в плановую ветку branch2</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что появилось информационное окно с сообщением о переходе в ветку branch2</li>
     * <li>Закрыть информационное окно</li>
     * <li>Проверить, что находимся в ветке branch2</li>
     * <li>Проверить, что в списке отображается объект boVersion2</li>
     * <li>Перейти по ссылке linkToListWithoutBranch на список на отдельной странице в основной ветке</li>
     * <li>Проверить, что появилось диалоговое окно с предложением переключиться в основную ветку</li>
     * <li>Нажать кнопку "Продолжить"</li>
     * <li>Проверить, что находимся в основной ветке</li>
     * <li>Проверить, что в списке отображается объект bo</li>
     * <li>Удалить ветки branch1 и branch2</li>
     * <li>Перейти по ссылке на список на отдельной странице в ветке branch1</li>
     * <li>Проверить, что появилось информационное сообщение о том, что ветка branch1 не найдена и список открыт в
     * текущем режиме работы</li>
     * <li>Проверить, что находимся в основной ветке</li>
     * <li>Перейти по ссылке на список на карточке компании в ветке branch2</li>
     * <li>Проверить, что появилось информационное сообщение о том, что ветка branch1 не найдена и список открыт в
     * текущем режиме работы</li>
     * <li>Проверить, что находимся в основной ветке</li>
     * </ol>
     */
    @Test
    public void testLinkToListInBranch()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo);
        Bo boVersion1 = DSLBranch.addToBranch(branch1, bo).get(0);
        Bo boVersion2 = DSLBranch.addToBranch(branch2, bo).get(0);

        String scriptPattern = "def builder = api.web.defineListLink(false).setTitle('Title').setClassCode('%s');"
                               + "builder.setAttrGroup('%s').setBranch('%s');"
                               + "api.web.list(builder);";
        ScriptRunner linkScript = new ScriptRunner(String.format(scriptPattern,
                userVersionedClass.getCode(), DAOGroupAttr.createSystem(userVersionedClass).getCode(),
                branch1.getUuid()));
        String linkToList = linkScript.runScript().get(0).trim();

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), userVersionedClass);
        DSLContent.add(objectList);
        scriptPattern = "def builder = api.web.defineListLink(true).setUuid('%s').setListCode('%s');"
                        + "builder.setBranch('%s');"
                        + "api.web.list(builder);";
        linkScript = new ScriptRunner(String.format(scriptPattern, SharedFixture.root().getUuid(),
                objectList.getCode(), branch2.getUuid()));
        String linkToListOnCard = linkScript.runScript().get(0).trim();

        scriptPattern = "def builder = api.web.defineListLink(false).setTitle('Title').setClassCode('%s');"
                        + "builder.setAttrGroup('%s');"
                        + "api.web.list(builder);";
        linkScript = new ScriptRunner(String.format(scriptPattern,
                userVersionedClass.getCode(), DAOGroupAttr.createSystem(userVersionedClass).getCode()));
        String linkToListWithoutBranch = linkScript.runScript().get(0).trim();

        //Выполнение действий и проверки
        GUILogon.asTester();
        tester.goToPage(linkToList);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch1);
        GUIPlannedVersion.assertCurrentBranch(branch1);
        GUIAdvListUtil advlistUtil = GUIAdvListUtil.forStandaloneList(linkToList);
        advlistUtil.content().asserts().rows(Lists.newArrayList(boVersion1), false, true);

        tester.goToPage(linkToListOnCard);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertGoToBranchInfoAndOk(branch2);
        GUIPlannedVersion.assertCurrentBranch(branch2);
        objectList.advlist().content().asserts().rows(Lists.newArrayList(boVersion2), false, true);

        tester.goToPage(linkToListWithoutBranch);
        GUIPlannedVersion.assertPageNotAvailableDialogAndContinue();
        GUIPlannedVersion.assertCurrentBranch(null);
        advlistUtil = GUIAdvListUtil.forStandaloneList(linkToListWithoutBranch);
        advlistUtil.content().asserts().rows(Lists.newArrayList(bo), false, true);

        DSLBo.delete(branch1, branch2);
        tester.goToPage(linkToList);
        GUIForm.assertDialog(BRANCH_NOT_FOUND_LIST_MSG);
        GUIPlannedVersion.assertCurrentBranch(null);
        tester.goToPage(linkToListOnCard);
        GUIForm.assertDialog(BRANCH_NOT_FOUND_LIST_MSG);
        GUIPlannedVersion.assertCurrentBranch(null);
    }
}
