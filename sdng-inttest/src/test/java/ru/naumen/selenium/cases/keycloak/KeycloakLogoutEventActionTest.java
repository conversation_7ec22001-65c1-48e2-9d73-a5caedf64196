package ru.naumen.selenium.cases.keycloak;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.user.sso.GUILogonKeycloak;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.keycloak.DSLSso;

/**
 * Тестирование работы действий по событию "Выход из системы" при использовании авторизации SSO keycloak
 *
 * <AUTHOR>
 * @since 05.02.2025
 */
public class KeycloakLogoutEventActionTest extends AbstractTestCase
{
    private static final String ADD_USER_ATTRIBUTE_VALUE_SUFFIX_SCRIPT_TEMPLATE = """
            api.tx.call {
                utils.edit(user,['%1$s': (user.%1$s ? user.%1$s.trim() : '') + '%2$s']);
            }
            """;
    private static final String EDIT_USER_ATTRIBUTE_VALUE_SCRIPT_TEMPLATE = """
            api.tx.call {
                utils.edit(user,['%s': '%s']);
            }
            """;

    private Bo employee;

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Создать лицензированного сотрудника employee с почтой пользователя из keycloak</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        employee.setEmail(KeycloakAuthorizationTest.KEYCLOAK_EMAIL);
        DSLBo.add(employee);
        List<String> extensionPointsOnStand = DSLSso.getExtensionPointsOnStand();
        if (!extensionPointsOnStand.isEmpty())
        {
            Assert.fail("""
                    На стенде есть точки расширения SSO (скриптовые модули с кодами: %s). Для запуска тестов \
                    необходимо удалить эти модули из системы (и при необходимости сохранить куда-нибудь \
                    для возможности их использования после прохождения тестов).
                    """.formatted(extensionPointsOnStand));
        }
    }

    /**
     * Тестирование последовательного выполнения синхронных ДПС "Вход в систему" и "Выход из системы" <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$292146883 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00309 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00307 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Добавить строковый атрибут strAttr в тип сотрудника {@link SharedFixture#employeeCase()}</li>
     * <li>Добавить ДПС eventLogin:
     * <ol>
     *     <li>Событие: вход в систему</li>
     *     <li>Синхронный: да</li>
     *     <li>Действие: Скрипт:
     *     <pre>
     *     --------------------------------------------------
     *     api.tx.call {
     *          utils.edit(user,[strAttr: '${expectedLoginValue}']);
     *     }
     *     --------------------------------------------------
     *     Где $expectedLoginValue - ожидаемое значение поля strAttr после успешной авторизации
     *     </pre>
     *     </li>
     * </ol></li>
     * <li>Добавить ДПС eventLogout:
     * <ol>
     *     <li>Событие: выход из системы</li>
     *     <li>Синхронный: да</li>
     *     <li>Действие: Скрипт
     *     <pre>
     *     --------------------------------------------------
     *     api.tx.call {
     *          utils.edit(user,[strAttr: (user.strAttr ? user.strAttr.trim() : '') + '${expectedLogoutValue}']);
     *     }
     *     --------------------------------------------------
     *     Где $expectedLogoutValue - ожидаемое значение поля strAttr после выхода из системы
     *     </pre>
     *     </li>
     * </ol></li>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под учетной записью keycloak</li>
     * <li>Выйти из системы</li>
     * <li>Проверить, что атрибут strAttr у сотрудника employee содержит значение
     * '${expectedLoginValue}${expectedLogoutValue}'</li>
     * </ol>
     */
    @Test
    public void testUserLogoutEventAction()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Attribute strAttr = DAOAttribute.createString(employeeCase);
        DSLAttribute.add(strAttr);

        String expectedLoginValue = ModelUtils.createCode();
        String scriptLoginTemplate = EDIT_USER_ATTRIBUTE_VALUE_SCRIPT_TEMPLATE.formatted(strAttr.getCode(),
                expectedLoginValue);
        ScriptInfo scriptLogin = DAOScriptInfo.createNewScriptInfo(scriptLoginTemplate);
        DSLScriptInfo.addScript(scriptLogin);
        EventAction eventLogin = DAOEventAction.createEventScript(EventType.loginSuccessful, scriptLogin, true,
                TxType.Sync,
                employeeCase);

        String expectedLogoutValue = ModelUtils.createCode();
        String scriptLogoutTemplate = ADD_USER_ATTRIBUTE_VALUE_SUFFIX_SCRIPT_TEMPLATE.formatted(strAttr.getCode(),
                expectedLogoutValue);
        ScriptInfo scriptLogout = DAOScriptInfo.createNewScriptInfo(scriptLogoutTemplate);
        DSLScriptInfo.addScript(scriptLogout);
        EventAction eventLogout = DAOEventAction.createEventScript(EventType.logout, scriptLogout, true, TxType.Sync,
                employeeCase);

        DSLEventAction.add(eventLogin, eventLogout);

        //Действия и проверки
        GUILogonKeycloak
                .login(KeycloakAuthorizationTest.KEYCLOAK_EMAIL, KeycloakAuthorizationTest.KEYCLOAK_PASSWORD, true);
        GUILogon.logout();

        DSLBo.waitAttribute(employee, 10, strAttr.getCode(),
                (expectedLoginValue + expectedLogoutValue)::equals);
    }

    /**
     * Тестирование последовательного выполнения синхронных ДПС "Выход из системы" и "Вход в систему" при авторизации
     * пользователя при наличии у него активной сессии и максимальном количестве сессий равному одному<br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$292146883 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00309 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00307 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Добавить строковый атрибут strAttr в тип сотрудника {@link SharedFixture#employeeCase()}</li>
     * <li>Добавить ДПС eventLogin:
     * <ol>
     *     <li>Событие: вход в систему</li>
     *     <li>Синхронный: да</li>
     *     <li>Действие: Скрипт
     *     <pre>
     *     ____________________________________________________________________
     *     api.tx.call {
     *          utils.edit(user,[strAttr: (user.strAttr ? user.strAttr.trim() : '') + '${expectedLoginValue}']);
     *     }
     *     ____________________________________________________________________
     *     Где expectedLoginValue - ожидаемое значение поля strAttr после успешной авторизации
     *     </pre></li>
     * </ol></li>
     * <li>Добавить ДПС eventLogout:
     * <ol>
     *     <li>Событие: выход из системы</li>
     *     <li>Синхронный: да</li>
     *     <li>Действие: Скрипт
     *     <pre>
     *     ____________________________________________________________________________________
     *     api.tx.call {
     *          utils.edit(user,[strAttr: '${expectedLogoutValue}']);
     *     }
     *     ____________________________________________________________________________________
     *     Где expectedLogoutValue - ожидаемое значение поля strAttr после выхода из системы
     *     </pre></li>
     * </ol>
     * </pre></li>
     * <b>Выполнение действий и проверки</b>
     * <li>Авторизоваться под учетной записью keycloak</li>
     * <li>Авторизоваться под учетной записью keycloak в другом браузере или режиме инкогнито</li>
     * <li>Проверить, что атрибут strAttr у сотрудника employee содержит значение
     * '${expectedLogoutValue}${expectedLoginValue}'</li>
     * </ol>
     */
    @Test
    public void testUserLogoutEventActionAfterLogin()
    {
        //Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Attribute strAttr = DAOAttribute.createString(employeeCase);
        DSLAttribute.add(strAttr);

        String expectedLoginValue = ModelUtils.createCode();
        String scriptLoginTemplate = ADD_USER_ATTRIBUTE_VALUE_SUFFIX_SCRIPT_TEMPLATE.formatted(strAttr.getCode(),
                expectedLoginValue);
        ScriptInfo scriptLogin = DAOScriptInfo.createNewScriptInfo(scriptLoginTemplate);
        DSLScriptInfo.addScript(scriptLogin);
        EventAction eventLogin = DAOEventAction.createEventScript(EventType.loginSuccessful, scriptLogin, true,
                TxType.Sync,
                employeeCase);

        String expectedLogoutValue = ModelUtils.createCode();
        String scriptLogoutTemplate = EDIT_USER_ATTRIBUTE_VALUE_SCRIPT_TEMPLATE.formatted(strAttr.getCode(),
                expectedLogoutValue);
        ScriptInfo scriptLogout = DAOScriptInfo.createNewScriptInfo(scriptLogoutTemplate);
        DSLScriptInfo.addScript(scriptLogout);
        EventAction eventLogout = DAOEventAction.createEventScript(EventType.logout, scriptLogout, true, TxType.Sync,
                employeeCase);

        DSLEventAction.add(eventLogin, eventLogout);

        //Выполнение действий и проверки
        GUILogonKeycloak
                .login(KeycloakAuthorizationTest.KEYCLOAK_EMAIL, KeycloakAuthorizationTest.KEYCLOAK_PASSWORD, true);
        tester.waitAsyncCall();
        GUITester.removeAllCookies();
        GUILogonKeycloak.goToLoginPage();
        GUILogonKeycloak
                .login(KeycloakAuthorizationTest.KEYCLOAK_EMAIL, KeycloakAuthorizationTest.KEYCLOAK_PASSWORD, true);

        DSLBo.waitAttribute(employee, 10, strAttr.getCode(),
                (expectedLogoutValue + expectedLoginValue)::equals);
    }
}