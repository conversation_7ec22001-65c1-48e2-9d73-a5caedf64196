package ru.naumen.selenium.cases.operator.process;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrServicePrs;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.FolderTree;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование параметров запросов Соглашения/Услуга
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
 * <AUTHOR>
 * @since 30.09.2015
 */
public class ScParamsAgrSlm1Test extends AbstractTestCase
{
    private static CatalogItem serviceTimeItem;
    private static MetaClass agreementCase, slmCase, ouCase, scCase1, scCase2, scCase3;
    private static Bo[] agreements, services;
    private static Bo ou;
    private static ScriptInfo filterScriptAgr, filterScriptSc;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Создать элемент справочника Классы обслуживания - serviceTimeItem</li>
     * <li>В классе Соглашение cоздать тип agreementCase</li>
     * <li>В классе Услуга создать тип slmCase</li>
     * <li>В классе Отдел создать тип ouCase</li>
     * <li>В классе Запрос создать типы scCase1, scCase2, scCase3</li>
     * <li>Cоздать 5 соглашений типа agreementCase (agreement1, agreement2, agreement3, agreement4, agreement5) и 
     * 5 услуг типа slmCase (service1, service2, service3, service4, service5)</li>
     * <li>Связать соглашение agreement1 с услугой service1</li>
     * <li>Связать соглашение agreement2 с услугой service2</li>
     * <li>Связать соглашение agreement3 с услугой service3</li>
     * <li>Создать отдел (ou) типа ouCase и связать его 
     * c 5 соглашениями (agreement1, agreement2, agreement3, agreement4, agreement5)</li>
     * <li>Типы scCase1, scCase2 добавить в атрибут "Tипы запросов" service1</li>
     * <li>Тип scCase1 добавить в атрибут "Tипы запросов" service2</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        serviceTimeItem = SharedFixture.serviceTime();
        agreementCase = SharedFixture.agreementCase();
        slmCase = DAOServiceCase.create();
        ouCase = SharedFixture.ouCase();
        scCase1 = DAOScCase.create();
        scCase2 = DAOScCase.create();
        scCase3 = DAOScCase.create();
        DSLMetaClass.add(slmCase, scCase1, scCase2, scCase3);

        agreements = new Bo[5];
        for (int i = 0; i < agreements.length; i++)
        {
            agreements[i] = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        }
        DSLBo.add(agreements);

        services = new Bo[5];
        for (int i = 0; i < services.length; i++)
        {
            services[i] = DAOService.create(slmCase);
        }
        DSLBo.add(services);

        ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        for (int i = 0; i < 3; i++)
        {
            DSLSlmService.addAgreements(services[i], agreements[i]);
        }

        for (int i = 0; i < 5; i++)
        {
            DSLAgreement.addToRecipients(agreements[i], ou);
        }

        DSLSlmService.addScCases(services[0], scCase1, scCase2);
        DSLSlmService.addScCases(services[1], scCase1);

        String agrScript = String.format("return['%s','%s']", agreements[0].getUuid(), agreements[1].getUuid());
        String slmScript = String.format("return['%s']", services[0].getUuid());
        filterScriptAgr = DAOScriptInfo.createNewScriptInfo(agrScript);
        filterScriptSc = DAOScriptInfo.createNewScriptInfo(slmScript);
    }

    /**
     * Тестирование значений Соглашение + Выбор из каталога + Фильтрация соглашений при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение, 
     * установить значение выпадающего списка "Представление для редактирования" = Выбор из каталога,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" скриптом: return[agreement1, agreement2]</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementFoldersTreeFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.FOLDERS_TREE, filterScriptAgr, null);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        BoTree tree = new BoTree(GUIXpath.Id.AGREEMENT_SERVICE_PROPERTY_VALUE, false);
        tree.assertPresentElement(true, agreements[0]);
        tree.assertPresentElement(true, agreements[1]);
    }

    /**
     * Тестирование значений Соглашение + Плоский список + Фильтрация соглашений при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение, 
     * установить значение выпадающего списка "Представление для редактирования" = Плоский список,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" - (return[agreement1, agreement2])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поле "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) согласно фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementListFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, filterScriptAgr, null);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], null);
        GUISc.assertAgreementServicePresent(true, agreements[1], null);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Соглашение или услуга + Плоский список + 
     * Фильтрация соглашений при редактировании + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение или услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Плоский список,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" - (return[agreement1, agreement2]),
     * заполнить поле "Скрипт фильтрации услуг" - (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) и 
     * услуга (service1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementServiceListAgrServiceFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.LIST, filterScriptAgr, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(true, agreements[1], null);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Соглашение или услуга + Плоский список + Фильтрация соглашений при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение или услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Плоский список,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" (return[agreement1, agreement2])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) 
     * и услуги (service1, service2) согласно фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementServiceListFilter()
    {
        //Подготовка:
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.LIST, filterScriptAgr, null);

        //Выполнение действия:
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки:
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(true, agreements[1], services[1]);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Соглашение или услуга + Плоский список + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение или услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Плоский список,
     * выбрать чекбокс "Фильтрация услуг при редактировании",
     * заполнить поле "Скрипт фильтрации услуг" - (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2, agreement3, agreement4, agreement5) 
     * и услуга (service1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementServiceListServiceFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.LIST, null, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(true, agreements[1], null);
        GUISc.assertAgreementServicePresent(true, agreements[2], null);
        GUISc.assertAgreementServicePresent(true, agreements[3], null);
        GUISc.assertAgreementServicePresent(true, agreements[4], null);
    }

    /**
     * Тестирование значений Соглашение или услуга + Иерархический список + 
     * Фильтрация соглашений при редактировании + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение или услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Иерархический список,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" (return[agreement1, agreement2]),
     * выбрать чекбокс "Фильтрация улуг при редактировании",
     * заполнить поле "Скрипт фильтрации услуг (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела</li>
     * <li>Нажимаем кнопку Добавить запрос</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) 
     * и услуга (service1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementServiceTreeListAgrServiceFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.TREE_LIST, filterScriptAgr, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(true, agreements[1], null);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Соглашение или услуга + Иерархический список + Фильтрация соглашений при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Соглашение или услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Иерархический список,
     * выбрать чекбокс "Фильтрация соглашений при редактировании",
     * заполнить поле "Скрипт фильтрации соглашений" (return[agreement1, agreement2])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только соглашения (agreement1, agreement2) 
     * и услуги (service1, service2) согласно фильтрации</li> 
     * </ol>
     */
    @Test
    public void testAgreementServiceTreeListFilter()
    {
        //Подготовка:
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.TREE_LIST, filterScriptAgr, null);

        //Выполнение действия:
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки:
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(true, agreements[1], services[1]);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Услуга + Выбор из каталога + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Выбор из каталога,
     * выбрать чекбокс "Фильтрация услуг при редактировании",
     * заполнить поле "Скрипт фильтрации услуг" (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список "Соглашения/Услуга"</li>
     * <li>В списке отображается только услуга (service1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testServiceFolderTreeFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.FOLDERS_TREE, null, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        String elementId = String.format("%s:%s", agreements[0].getUuid(), services[0].getUuid());
        FolderTree tree = FolderTree.createAgreementServiceTree();
        tree.assertPresentElement(true, elementId);
    }

    /**
     * Тестирование значений Услуга + Плоский список + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Плоский список,
     * выбрать чекбокс "Фильтрация услуг при редактировании",
     * заполнить поле "Скрипт фильтрации услуг" (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только услуга (service1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testServiceListServiceFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.LIST, null, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(false, agreements[1], null);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }

    /**
     * Тестирование значений Услуга + Иерархический список + Фильтрация услуг при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка }</li>
     * <li>В Настройке Бизнес Процессов > Параметры запросов в Поле Соглашение/Услуга,
     * установить значение поля "Соглашение/Услуга" = Услуга, 
     * установить значение выпадающего списка "Представление для редактирования" = Иерархический список,
     * выбрать чекбокс "Фильтрация услуг при редактировании",
     * заполнить поле "Скрипт фильтрации услуг" (return[service1])</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Заходим в карточку отдела (ou)</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Открыть выпадающий список поля "Соглашения/Услуга"</li>
     * <li>В списке отображается только услуга (service1) 
     * и соглашение (agreement1) согласно выбранной фильтрации</li> 
     * </ol>
     */
    @Test
    public void testServiceTreeListServiceFilter()
    {
        //Подготовка
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.TREE_LIST, null, filterScriptSc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIButtonBar.addSC();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        //Проверки
        GUISc.assertAgreementServicePresent(true, agreements[0], services[0]);
        GUISc.assertAgreementServicePresent(false, agreements[1], null);
        GUISc.assertAgreementServicePresent(false, agreements[2], null);
        GUISc.assertAgreementServicePresent(false, agreements[3], null);
        GUISc.assertAgreementServicePresent(false, agreements[4], null);
    }
}
