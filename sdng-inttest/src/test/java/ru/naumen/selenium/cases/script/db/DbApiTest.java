package ru.naumen.selenium.cases.script.db;

import static ru.naumen.selenium.casesutil.messages.ErrorMessages.DATE_FORMAT_NOT_NULL;
import static ru.naumen.selenium.casesutil.messages.ErrorMessages.DATE_NOT_NULL;

import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.GroovyUtils;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;

/**
 * Тесты на скриптовое API hql запросы
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 * <AUTHOR>
 * @since 19.10.2012
 */
public class DbApiTest extends AbstractTestCase
{
    private static final Set<String> POSSIBLE_TYPES = Set.of("POSTGRESQL", "ORACLEDB", "MSSQL");

    /**
     * Тестирование метода api.db.deleteComments(String, String, String, Collection): удаление комментариев запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44549920
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44549925
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать запросы sc1..2 типа scCase</li> 
     * <li>Изменить статус запроса sc2 на Возобновлен</li>
     * <li>Сохранить текущее время в scCreation</li>
     * <li>Создать запрос sc3 типа scCase</li> 
     * <li>Добавить comment1 к sc3, comment2 к sc1, comment3 к sc2</li> 
     * <li>Сохранить текущее время в commentCreation</li>
     * <li>Добавить comment4 к sc1</li>         
     * <b>Выполнение действия и проверки.</b>
     * <li>Выполняем скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.deleteComments($formatter, $scCreationDate, $commentCreation, $states);
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверяем результат:
     *     <pre>
     *     [Параметры]      -  [результат]
     *     ['dd.MM.yyyy HH:mm:ss', scCreation, commentCreation, [registered]]  -  [удален comment2]
     *     (Проверяем возвращенное скриптом значение: количество удаленных комментариев = 1)
     *     ['dd.MM.yyyy HH:mm:ss', scCreation, commentCreation, []]            -  [удален comment3]
     *     ['', scCreation, commentCreation, []]                               -  [ошибка]
     *     [null, scCreation, commentCreation, []]                             -  [ошибка]
     *     ['dd.MM.yyyy HH:mm:ss', '', commentCreation, []]                    -  [ошибка]
     *     ['dd.MM.yyyy HH:mm:ss', null, commentCreation, []]                  -  [ошибка]
     *     ['dd.MM.yyyy HH:mm:ss', scCreation, '', []]                         -  [ошибка]
     *     ['dd.MM.yyyy HH:mm:ss', scCreation, null, []]                       -  [ошибка]
     *     </pre>
     * </li>
     */
    @Test
    public void testDeleteComments()
    {
        //Подготовка
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, SharedFixture.agreement());

        BoStatus registered = DAOBoStatus.createRegistered(SharedFixture.scCase());
        BoStatus resumed = DAOBoStatus.createResumed(SharedFixture.scCase());
        DSLBoStatus.setTransitions(registered, resumed);

        //Создаем запросы
        Bo sc1 = DAOSc.create(SharedFixture.scCase(), ou, SharedFixture.agreement(), SharedFixture.timeZone());
        Bo sc2 = DAOSc.create(SharedFixture.scCase(), ou, SharedFixture.agreement(), SharedFixture.timeZone());
        DSLBo.add(sc1, sc2);

        DSLSc.changeState(sc2, resumed);

        WaitTool.waitMills(1000);
        long scCreation = System.currentTimeMillis();

        Bo sc3 = DAOSc.create(SharedFixture.scCase(), ou, SharedFixture.agreement(), SharedFixture.timeZone());
        DSLBo.add(sc3);

        //Добавляем комментарии
        String commentUuid1 = DSLComment.add(sc3.getUuid(), ModelUtils.createDescription());
        String commentUuid2 = DSLComment.add(sc1.getUuid(), ModelUtils.createDescription());
        String commentUuid3 = DSLComment.add(sc2.getUuid(), ModelUtils.createDescription());

        WaitTool.waitMills(1000);
        long commentCreation = System.currentTimeMillis();

        String commentUuid4 = DSLComment.add(sc1.getUuid(), ModelUtils.createDescription());

        //Подготовка скрипта
        String scriptPattern = "api.db.deleteComments(%s, %s, %s, %s);";
        String dateTimeFormatter = "'dd.MM.yyyy HH:mm:ss'";
        String scCreationDate = "'%s'".formatted(DateTimeUtils.formatTimeddMMyyyyHHmmss(scCreation));
        String commentCreationDate = "'%s'".formatted(DateTimeUtils.formatTimeddMMyyyyHHmmss(commentCreation));

        String result = ScriptRunner.executeScript(scriptPattern, dateTimeFormatter, scCreationDate,
                commentCreationDate, GroovyUtils.collectionToString(Set.of(registered.getCode())));
        Assert.assertEquals("Скрипт вернул не верное количество удаленных комментариев", "1", result);

        //Проверки
        DSLComment.assertPresent(sc1, commentUuid4);
        DSLComment.assertAbsence(sc1, commentUuid2);
        DSLComment.assertPresent(sc2, commentUuid3);
        DSLComment.assertPresent(sc3, commentUuid1);

        ScriptRunner.executeScript(scriptPattern, dateTimeFormatter, scCreationDate, commentCreationDate, "[]");

        //Проверки
        DSLComment.assertPresent(sc1, commentUuid4);
        DSLComment.assertAbsence(sc2, commentUuid3);
        DSLComment.assertPresent(sc3, commentUuid1);

        ScriptRunner.assertError(String.format(scriptPattern, "''", scCreationDate, commentCreationDate, "[]"),
                DATE_FORMAT_NOT_NULL);
        ScriptRunner.assertError(String.format(scriptPattern, null, scCreationDate, commentCreationDate, "[]"),
                DATE_FORMAT_NOT_NULL);

        ScriptRunner.assertError(String.format(scriptPattern, dateTimeFormatter, "''", commentCreationDate, "[]"),
                DATE_NOT_NULL);
        ScriptRunner.assertError(String.format(scriptPattern, dateTimeFormatter, null, commentCreationDate, "[]"),
                DATE_NOT_NULL);
        ScriptRunner.assertError(String.format(scriptPattern, dateTimeFormatter, scCreationDate, "''", "[]"),
                DATE_NOT_NULL);
        ScriptRunner.assertError(String.format(scriptPattern, dateTimeFormatter, scCreationDate, null, "[]"),
                DATE_NOT_NULL);
    }

    /**
     * Тестирование метода api.db.getDBName - возвращает название БД, на которой работает приложение.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44557984
     * <ol>
     * <b>Выполнение действия и проверки.</b>
     * <li>Выполняем скрипт:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.getDBName();
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверяем результат: не пустая строка</li>
     */
    @Test
    public void testGetDBName()
    {
        //Выполнение действий и проверки
        String actual = ScriptRunner.executeScript("api.db.getDBName()");
        Assert.assertFalse("Скрипт вернул пустую строку.", actual.isEmpty());
    }

    /**
     * Тестирование метода api.db.getDBType - возвращает тип БД, на которой работает приложение.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$178877731
     * <ol>
     * <b>Выполнение действия и проверки.</b>
     * <li>Выполняем скрипт:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.getDBType();
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверяем результат: не пустая строка</li>
     */
    @Test
    public void testGetDBType()
    {
        //Выполнение действий и проверки
        String actual = ScriptRunner.executeScript("api.db.getDBType()");
        Assert.assertTrue("Скрипт вернул неожиданное значение - " + actual, POSSIBLE_TYPES.contains(actual));
    }

    /**
     * Тестирование метода api.db.lock: можно изменять объект в транзакции, где взят lock<br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84743483<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <ol>
     * <b>Выполнение действия и проверки.</b>
     * <li>Создаем сотрудника</li>
     * <li>Выполняем скрипт:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     api.db.lock('employee$12345');
     *     return utils.edit('employee$12345', ['email':'<EMAIL>']).email;
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверяем результат: вернулся заданный нами email, а значит редактирование после взятия lock прошло
     * успешно</li>
     * </ol>
     */
    @Test
    public void testObjectChangeAfterDbLock()
    {
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        String email = ModelUtils.createEmail();

        //Выполнение действий и проверки
        Object actual = ScriptRunner.executeScript("""
                def employeeUuid = '%s'
                api.db.lock(employeeUuid)
                return utils.edit(employeeUuid, ['email': '%s']).email;""", employee.getUuid(), email);

        Assert.assertEquals("Не изменился email сотрудника после api.db.lock", email, actual);
    }

    /**
     * Тестирование метода api.db.lock: lock блокирует объект до конца работы транзакции<br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84743483<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <ol>
     * <b>Выполнение действия и проверки.</b>
     * <li>Создаем сотрудника</li>
     * <li>Запускаем на выполнение параллельно 2 раза скрипт:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     String temp = '';
     *     api.db.lock('employee$12345');
     *     temp += System.currentTimeMillis(); // время взятия лока
     *     utils.edit('employee$12345', ['email':temp]);
     *     temp += System.currentTimeMillis(); // ~ время окончания работы скрипта
     *     return temp; // beginTime endTime
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверяем: скрипт взял lock только после завершения скрита, который уже взял lock.<br>
     * Проверяем одну из возможных ситуаций:
     * <ul>
     *     <li>Время окончания работы скрита1 меньше, чем время взятия лока скрита2</li>
     *     <li>Время окончания работы скрита2 меньше, чем время взятия лока скрита1</li>
     * </ul>
     * </li>
     * <li>Проверяем: email остался таким, каким его задал самый последний скрипт</li>
     * </ol>
     */
    @Test
    @SuppressWarnings("resource")
    public void testDbLockInTwoParallelScripts() throws ExecutionException, InterruptedException
    {
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        String script = """
                def temp = ''
                def employeeUuid = '%s'
                api.db.lock(employeeUuid)
                temp += System.currentTimeMillis()
                utils.edit(employeeUuid, ['firstName': temp])
                temp += ' ' + System.currentTimeMillis()
                return temp""";

        ExecutorService executor = Executors.newFixedThreadPool(2);
        Future<String> future1 = executor.submit(() -> ScriptRunner.executeScript(script, employee.getUuid()));
        Future<String> future2 = executor.submit(() -> ScriptRunner.executeScript(script, employee.getUuid()));

        String[] map1 = future1.get().split(" ");
        String[] map2 = future2.get().split(" ");

        long lockTime1 = Long.parseLong(map1[0]);
        long endTime1 = Long.parseLong(map1[1]);
        long lockTime2 = Long.parseLong(map2[0]);
        long endTime2 = Long.parseLong(map2[1]);

        Assert.assertTrue(endTime1 > lockTime2 || endTime2 > lockTime1);

        String email = SdDataUtils.getObjectByUUID(employee).get(SystemAttrEnum.FIRST_NAME.getCode());
        if (lockTime1 > lockTime2)
        {
            Assert.assertEquals(email, String.valueOf(lockTime1));
        }
        else
        {
            Assert.assertEquals(email, String.valueOf(lockTime2));
        }
    }

    /**
     * Тестирование метода api.db.detach, если он вызван после выполнения метода API, создающего в контексте работы с
     * БД объекты, отличные от IUUIDIdentifiable
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$313340982
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Создать сотрудника</li>
     * <li>Выполнить скрипт:
     * <pre>
     *     -------------------------------------------------------------------------------
     *     import org.hibernate.engine.spi.SessionImplementor;
     *     import ru.naumen.core.shared.IUUIDIdentifiable;
     *
     *      // создаём временный файл
     *      api.fileStorage.uploadTemp(new ByteArrayInputStream('some text'.getBytes()), 'file.text', 'text/plain')
     *
     *      // поднимаем объект из БД в кэш Hibernate
     *      def object = utils.get('employeeUuid')
     *
     *      // удаляем объект их кэша
     *      api.db.detach(object)
     *
     *      // проверяем что объекта в кэше больше нет
     *      def session = beanFactory.getBean('sessionFactory').getCurrentSession();
     *      def entities = ((SessionImplementor)session).getPersistenceContext().reentrantSafeEntityEntries();
     *      def objectUuid = object.getUUID();
     *      Arrays.stream(entities).noneMatch(entry -> entry.getKey() instanceof IUUIDIdentifiable
     *      && ((IUUIDIdentifiable)(entry.getKey())).getUUID().equals(objectUuid))
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверить, что скрипт выполнился без ошибок, объекта больше нет в кеше</li>
     * </ol>
     */
    @Test
    public void testDetachObjectWithTempFile()
    {
        // Выполнение действий и проверки
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        String script = """
                import org.hibernate.engine.spi.SessionImplementor;
                import ru.naumen.core.shared.IUUIDIdentifiable;
                
                // создаём временный файл
                api.fileStorage.uploadTemp(new ByteArrayInputStream('some text'.getBytes()), 'file.text', 'text/plain')
                
                // поднимаем объект из БД в кэш Hibernate
                def object = utils.get('%s')
                
                // удаляем объект их кэша
                api.db.detach(object)
                
                // проверяем что объекта в кэше больше нет
                def session = beanFactory.getBean('sessionFactory').getCurrentSession();
                def entities = ((SessionImplementor)session).getPersistenceContext().reentrantSafeEntityEntries();
                def objectUuid = object.getUUID();
                Arrays.stream(entities).noneMatch(entry -> entry.getKey() instanceof IUUIDIdentifiable
                && ((IUUIDIdentifiable)(entry.getKey())).getUUID().equals(objectUuid))""";

        Assert.assertTrue(Boolean.parseBoolean(ScriptRunner.executeScript(script, employee.getUuid())));
    }
}
