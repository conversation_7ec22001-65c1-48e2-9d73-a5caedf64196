package ru.naumen.selenium.cases.script.security;

import static ru.naumen.selenium.cases.script.security.SecurityApiPermissionTest.AttrPresentation.HAS_EDIT_ATTR_PERMISSION;
import static ru.naumen.selenium.cases.script.security.SecurityApiPermissionTest.AttrPresentation.HAS_EDIT_COMMENT_ATTR_PERMISSION;
import static ru.naumen.selenium.cases.script.security.SecurityApiPermissionTest.AttrPresentation.HAS_VIEW_ATTR_PERMISSION;
import static ru.naumen.selenium.cases.script.security.SecurityApiPermissionTest.AttrPresentation.HAS_VIEW_COMMENT_ATTR_PERMISSION;
import static ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights.DELETE;
import static ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights.DELETE_COMMENT;
import static ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights.EDIT_REST_ATTRIBUTES;
import static ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights.EDIT_REST_COMMENT_ATTRIBUTES;

import org.hamcrest.Matchers;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rest.DSLRest;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.security.SecurityMarkerEditCommentAttrs;
import ru.naumen.selenium.security.SecurityMarkerState;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;
import ru.naumen.selenium.security.SecurityMarkerViewCommentAttrs;

/**
 * Тесты на методы работы с правами пользователей в api.security.<br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 *
 * <AUTHOR>
 * @since 10.02.2021
 */
public class SecurityApiPermissionTest extends AbstractTestCase
{
    private final static String CHECK_PERMISSION_MODULE_TEMPLATE = """
            def hasViewAttrPermission(object, attributeCode, loadMode = '') {
                return api.security.hasViewAttrPermission(getObject(object, loadMode), attributeCode)
            }
            
            def hasViewCommentAttrPermission(object, attributeCode, loadMode = '') {
                return api.security.hasViewCommentAttrPermission(getObject(object, loadMode), attributeCode)
            }
            
            def hasEditAttrPermission(object, attributeCode, loadMode = '') {
                return api.security.hasEditAttrPermission(getObject(object, loadMode), attributeCode)
            }
            
            def hasEditCommentAttrPermission(object, attributeCode, loadMode = '') {
                return api.security.hasEditCommentAttrPermission(getObject(object, loadMode), attributeCode)
            }
            
            def hasPermission(object, permission, loadMode = '') {
                return api.security.hasPermission(getObject(object, loadMode), permission)
            }
            
            def hasChangeStatePermission(objectUuid, targetState, objectLoadMode = '', transitionLoadMode = '') {
                def object = getObject(objectUuid, objectLoadMode)
                def transition = getTransition(object, targetState, transitionLoadMode)
                return api.security.hasChangeStatePermission(object, targetState)
            }
            
            def getObject(object, loadMode = '')
            {
               if (loadMode == 'FQN')
               {
                   return api.types.newClassFqn(object)
               }
               if (loadMode == 'OBJECT')
               {
                   return utils.get(object)
               }
               return object;
            }
            
            def getTransition(object, targetState, loadMode = '')
            {
               if (loadMode == 'OBJECT')
               {
                   def loadedObject = (object instanceof String) ? utils.get(object) : object;
                   return api.wf.transitions(loadedObject).find { it.endState == targetState }
               }
               return targetState;
            }
            """;

    private static MetaClass userCase;
    private static ModuleConf module;

    /**
     * <b>Общая часть подготовки</b>
     * <ul>
     *     <li>Добавить в систему скриптовый модуль тестовой обвязкой для вызовов методов api.security</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userCase = DAOUserCase.create(SharedFixture.userClass());
        DSLMetaClass.add(userCase);

        module = DAOModuleConf.create();
        module.setScriptBody(CHECK_PERMISSION_MODULE_TEMPLATE);
        DSLModuleConf.add(module);
    }

    /**
     * Тестирование того, что методы api.security.hasPermission возвращают true, когда текущему пользователю
     * разрешено указанное действие с объектом
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101206565
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать тип userCase</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>Создать объект userBo</li>
     * <li>Создать лицензированного пользователя employee, добавить в эту группу securityGroup</li>
     * <li>Получить accessKey для employee</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Проверить у пользователя отсутствие прав и на удаление и на редактирование атрибутов объекта userBo</li>
     * <li>Проверить у пользователя отсутствие прав и на удаление и на редактирование атрибутов для объектов типа
     * userCase</li>
     * <li>Выдать пользователю права на редактирование атрибутов и на удаление объектов в типе userCase</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибутов и на удаление объекта userBo</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибутов и на удаление для объектов типа
     * userCase</li>
     */
    @Test
    public void testHasPermission()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo, employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);
        String accessKey = DSLRest.getEmployeeAccessKey(employee);

        // Действия и проверки
        assertHasPermission(false, userBo, DELETE, accessKey);
        assertHasPermission(false, userBo, EDIT_REST_ATTRIBUTES, accessKey);

        assertHasPermission(false, userCase, DELETE, accessKey);
        assertHasPermission(false, userCase, EDIT_REST_ATTRIBUTES, accessKey);

        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.setRights(userCase, profile, DELETE, EDIT_REST_ATTRIBUTES);

        assertHasPermission(true, userBo, DELETE, accessKey);
        assertHasPermission(true, userBo, EDIT_REST_ATTRIBUTES, accessKey);

        assertHasPermission(true, userCase, DELETE, accessKey);
        assertHasPermission(true, userCase, EDIT_REST_ATTRIBUTES, accessKey);
    }

    /**
     * Тестирование того, что методы api.security.hasPermission возвращают true, когда текущему пользователю
     * разрешено указанное действие с комментарием
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101206565
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать тип userCase</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>Создать объект userBo</li>
     * <li>Создать лицензированного пользователя employee, добавить в эту группу securityGroup</li>
     * <li>Получить accessKey для employee</li>
     * <li>Создать комментарий comment у объекта userBo</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Проверить у пользователя отсутствие прав на удаление комментария comment и на редактирование его
     * атрибутов</li>
     * <li>Проверить у пользователя отсутствие прав на удаление комментария в объекте типа userCase и на
     * редактирование его атрибутов</li>
     * <li>Выдать пользователю права на удаление комментариев и редактирование их атрибутов в типе userCase</li>
     * <li>Проверить у пользователя наличие прав на удаление комментария comment и на редактирование его атрибутов</li>
     * <li>Проверить у пользователя наличие прав на удаление комментария в объектах типа userCase и на редактирование
     * его атрибутов</li>
     */
    @Test
    public void testHasPermissionForComment()
    {
        // Подготовка
        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        DSLSecurityProfile.add(profile);

        Bo userBo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo, employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);
        String accessKey = DSLRest.getEmployeeAccessKey(employee);

        String commentUuid = DSLComment.add(userBo.getUuid(), ModelUtils.createDescription());

        // Действия и проверки
        assertHasPermission(false, commentUuid, DELETE, accessKey);
        assertHasPermission(false, commentUuid, DELETE_COMMENT, accessKey);
        assertHasPermission(false, commentUuid, EDIT_REST_COMMENT_ATTRIBUTES, accessKey);

        assertHasPermission(false, userCase, DELETE_COMMENT, accessKey);
        assertHasPermission(false, userCase, EDIT_REST_COMMENT_ATTRIBUTES, accessKey);

        DSLSecurityProfile.setRights(userCase, profile, DELETE_COMMENT, EDIT_REST_COMMENT_ATTRIBUTES);

        assertHasPermission(true, commentUuid, DELETE, accessKey);
        assertHasPermission(true, commentUuid, DELETE_COMMENT, accessKey);
        assertHasPermission(true, commentUuid, EDIT_REST_COMMENT_ATTRIBUTES, accessKey);

        assertHasPermission(true, userCase, DELETE_COMMENT, accessKey);
        assertHasPermission(true, userCase, EDIT_REST_COMMENT_ATTRIBUTES, accessKey);
    }

    /**
     * Тестирование того, что метод api.security.hasChangeStatePermission возвращает true, когда текущему
     * пользователю разрешен перевод объекта в указанный статус
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101206565
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать класс userClass и тип userCase</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>Создать объект userBo</li>
     * <li>Создать статус userState в типе userCase</li>
     * <li>Создать лицензированного пользователя employee, добавить в эту группу securityGroup</li>
     * <li>Получить accessKey для employee</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Проверить у пользователя отсутствие прав на перевод объекта userBo на статусы Закрыт и userState</li>
     * <li>Выдать пользователю права на перевод объектов в типе userCase из статуса Зарегистрирован в Закрыт и
     * создать переход из статуса Зарегистрирован в userState, но права на данный переход не выдавать</li>
     * <li>Проверить у пользователя наличие прав на перевод объекта userBo в статус Закрыт,
     * но отсутствие прав на перевод в userState</li>
     */
    @Test
    public void testHasChangeStatePermission()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        DSLSecurityProfile.add(profile);

        Bo userBo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo, employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);
        String accessKey = DSLRest.getEmployeeAccessKey(employee);

        BoStatus registered = DAOBoStatus.createRegistered(userCase);
        BoStatus closed = DAOBoStatus.createClosed(userCase);
        BoStatus userState = DAOBoStatus.createUserStatus(userCase);
        DSLBoStatus.add(userState);

        // Действия и проверки
        assertHasChangeStatePermission(false, userBo, userState, accessKey);
        assertHasChangeStatePermission(false, userBo, closed, accessKey);

        SecurityMarker transitionMarker =
                new SecurityMarkerState(userCase).addTransition(registered, closed).apply();
        DSLSecurityProfile.setRights(userCase, profile, transitionMarker);
        DSLBoStatus.setTransitions(registered, userState);

        assertHasChangeStatePermission(false, userBo, userState, accessKey);
        assertHasChangeStatePermission(true, userBo, closed, accessKey);
    }

    /**
     * Тестирование того, что методы api.security.hasViewAttrPermission и api.security.hasEditAttrPermission
     * возвращают true, когда текущему пользователю разрешен просмотр/редактирование атрибута объект.
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101206565
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать тип userCase</li>
     * <li>Создать строковый атрибут stringAttr в типе userCase</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>Создать объект userBo</li>
     * <li>Создать лицензированного пользователя employee, добавить в эту группу securityGroup</li>
     * <li>Получить accessKey для employee</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Проверить у пользователя отсутствие прав и на просмотр и на редактирование атрибута stringAttr объекта
     * userBo</li>
     * <li>Проверить у пользователя отсутствие прав и на просмотр и на редактирование атрибута stringAttr объекта
     * типа userCase</li>
     * <li>Выдать пользователю права на просмотр атрибута stringAttr в типе userCase</li>
     * <li>Проверить у пользователя наличие прав на просмотр атрибута stringAttr объекта userBo и отсутствие прав на
     * редактирование</li>
     * <li>Проверить у пользователя наличие прав на просмотр атрибута stringAttr объекта типа userCase и отсутствие
     * прав на редактирование</li>
     * <li>Выдать пользователю права на редактирование атрибута stringAttr в типе userCase</li>
     * <li>Проверить у пользователя наличие прав на просмотр и редактирование атрибута stringAttr объекта userBo</li>
     * <li>Проверить у пользователя наличие прав на просмотр и редактирование атрибута stringAttr объекта
     * типа userCase</li>
     * <li>Забрать у пользователя права на просмотр атрибута stringAttr в типе userCase</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибута stringAttr объекта userBo и
     * отсутствие прав на просмотр</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибута stringAttr объекта типа userCase и
     * отсутствие прав на просмотр</li>
     */
    @Test
    public void testHasAttrPermission()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(stringAttr);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        SecurityProfile profile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);

        Bo userBo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo, employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);
        String accessKey = DSLRest.getEmployeeAccessKey(employee);

        // Действия и проверки
        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        SecurityMarker viewAttrMarker =
                new SecurityMarkerViewAttrs(userCase).addAttributes(stringAttr).apply();
        DSLSecurityProfile.setRights(userCase, profile, viewAttrMarker);

        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        SecurityMarker editAttrMarker =
                new SecurityMarkerEditAttrs(userCase).addAttributes(stringAttr).apply();
        DSLSecurityProfile.setRights(userCase, profile, editAttrMarker);

        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        DSLSecurityProfile.removeRights(userCase, profile, viewAttrMarker);

        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, userBo, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
    }

    /**
     * Тестирование того, что методы api.security.hasViewAttrPermission, api.security.hasEditAttrPermission,
     * api.security.hasViewCommentAttrPermission и api.security.hasEditCommentAttrPermission возвращают true,
     * когда текущему пользователю разрешен просмотр/редактирование атрибута комментарий.
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101206565
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$243182399
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать тип userCase</li>
     * <li>Создать строковый атрибут stringAttr в типе userCase</li>
     * <li>Создать группу пользователей securityGroup</li>
     * <li>Создать объект userBo</li>
     * <li>Создать лицензированного пользователя employee, добавить в эту группу securityGroup</li>
     * <li>Получить accessKey для employee</li>
     * <li>Создать комментарий comment у объекта userBo</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Проверить у пользователя отсутствие прав и на просмотр и на редактирование
     * атрибута stringAttr объекта comment</li>
     * <li>Выдать пользователю права на просмотр атрибута комментария stringAttr для типа userCase</li>
     * <li>Проверить у пользователя наличие прав на просмотр атрибута stringAttr комментария comment и
     * отсутствие прав на редактирование, через общий метод и специальный метод для проверки прав на атрибуты
     * комментариев</li>
     * <li>Проверить у пользователя наличие прав на просмотр атрибута stringAttr комментария добавленного в тип
     * userCase и отсутствие прав на редактирование, через специальный метод для проверки прав на атрибуты
     * комментариев</li>
     * <li>Выдать пользователю права на редактирование атрибута комментария stringAttr для типа userCase</li>
     * <li>Проверить у пользователя наличие прав на просмотр и редактирование атрибута stringAttr комментария
     * comment, через общий метод и специальный метод для проверки прав на атрибуты комментариев</li>
     * <li>Проверить у пользователя наличие прав на просмотр и редактирование атрибута stringAttr комментария
     * добавленного в тип userCase, через специальный метод для проверки прав на атрибуты комментариев</li>
     * <li>Забрать у пользователя права на просмотр атрибута комментария stringAttr для типа userCase</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибута stringAttr комментария comment и
     * отсутствие прав на просмотр, через общий метод и специальный метод для проверки прав на атрибуты
     * комментариев</li>
     * <li>Проверить у пользователя наличие прав на редактирование атрибута stringAttr комментария добавленного в тип
     * userCase, через специальный метод для проверки прав на атрибуты комментариев</li>
     */
    @Test
    public void testHasCommentAttrPermission()
    {
        // Подготовка
        MetaClass commentClass = DAOCommentClass.create();

        Attribute stringAttr = DAOAttribute.createString(commentClass);
        DSLAttribute.add(stringAttr);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        Bo userBo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo, employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);
        String accessKey = DSLRest.getEmployeeAccessKey(employee);

        String commentUuid = DSLComment.add(userBo.getUuid(), ModelUtils.createDescription());

        // Действия и проверки
        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_VIEW_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);

        assertHasAttrPermission(false, HAS_VIEW_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        SecurityMarker viewCommentAttrMarker =
                new SecurityMarkerViewCommentAttrs(userCase).addAttributes(stringAttr).apply();
        SecurityProfile profile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.setRights(userCase, profile, viewCommentAttrMarker);

        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_VIEW_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);

        assertHasAttrPermission(true, HAS_VIEW_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_EDIT_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        SecurityMarker editCommentAttrMarker =
                new SecurityMarkerEditCommentAttrs(userCase).addAttributes(stringAttr).apply();
        DSLSecurityProfile.setRights(userCase, profile, editCommentAttrMarker);

        assertHasAttrPermission(true, HAS_VIEW_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_VIEW_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);

        assertHasAttrPermission(true, HAS_VIEW_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);

        DSLSecurityProfile.removeRights(userCase, profile, viewCommentAttrMarker);

        assertHasAttrPermission(false, HAS_VIEW_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(false, HAS_VIEW_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_COMMENT_ATTR_PERMISSION, commentUuid, stringAttr, accessKey);

        assertHasAttrPermission(false, HAS_VIEW_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
        assertHasAttrPermission(true, HAS_EDIT_COMMENT_ATTR_PERMISSION, userCase, stringAttr, accessKey);
    }

    private static void assertHasPermission(boolean expected, Bo object, AbstractBoRights right, String accessKey)
    {
        assertHasPermission(expected, object.getUuid(), right, accessKey);
    }

    private static void assertHasPermission(boolean expected, String objectUuid, AbstractBoRights right,
            String accessKey)
    {
        assertPermission(expected, module, "hasPermission", accessKey, objectUuid, right.getRightCode(), "OBJECT");
        assertPermission(expected, module, "hasPermission", accessKey, objectUuid, right.getRightCode());
    }

    private static void assertHasPermission(boolean expected, MetaClass metaClass, AbstractBoRights right,
            String accessKey)
    {
        String fqn = metaClass.getFqn();
        assertPermission(expected, module, "hasPermission", accessKey, fqn, right.getRightCode(), "FQN");
        assertPermission(expected, module, "hasPermission", accessKey, fqn, right.getRightCode());
    }

    private static void assertHasAttrPermission(boolean expected, AttrPresentation presentation, Bo object,
            Attribute attribute, String accessKey)
    {
        assertHasAttrPermission(expected, presentation, object.getUuid(), attribute, accessKey);
    }

    private static void assertHasAttrPermission(boolean expected, AttrPresentation presentation, String objectUuid,
            Attribute attribute, String accessKey)
    {
        assertPermission(expected, module, presentation.methodCode, accessKey, objectUuid, attribute.getCode(),
                "OBJECT");
        assertPermission(expected, module, presentation.methodCode, accessKey, objectUuid, attribute.getCode());
    }

    private static void assertHasAttrPermission(boolean expected, AttrPresentation presentation, MetaClass metaClass,
            Attribute attribute, String accessKey)
    {
        String fqn = metaClass.getFqn();
        assertPermission(expected, module, presentation.methodCode, accessKey, fqn, attribute.getCode(), "FQN");
        assertPermission(expected, module, presentation.methodCode, accessKey, fqn, attribute.getCode());
    }

    private static void assertHasChangeStatePermission(boolean expected, Bo object, BoStatus status, String accessKey)
    {
        String uuid = object.getUuid();
        String statusCode = status.getCode();
        assertPermission(expected, module, "hasChangeStatePermission", accessKey, uuid, statusCode, "OBJECT", "OBJECT");
        assertPermission(expected, module, "hasChangeStatePermission", accessKey, uuid, statusCode);
    }

    private static void assertPermission(boolean expected, ModuleConf module, String methodName, String accessKey,
            Object... params)
    {
        ValidatableResponse response = DSLRest.execMF(accessKey, module.getCode(), methodName, params);

        response.statusCode(200);
        response.body("[0]", Matchers.is(expected));
    }

    enum AttrPresentation
    {
        HAS_VIEW_ATTR_PERMISSION("hasViewAttrPermission"),
        HAS_EDIT_ATTR_PERMISSION("hasEditAttrPermission"),
        HAS_VIEW_COMMENT_ATTR_PERMISSION("hasViewCommentAttrPermission"),
        HAS_EDIT_COMMENT_ATTR_PERMISSION("hasEditCommentAttrPermission");

        private final String methodCode;

        AttrPresentation(String methodCode)
        {
            this.methodCode = methodCode;
        }
    }
}