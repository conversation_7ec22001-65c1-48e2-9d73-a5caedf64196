package ru.naumen.selenium.cases.admin.sets;

import java.util.List;
import java.util.function.BiFunction;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Span;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOStatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;

/**
 * Тестирование видимости при работе с профилями администрирования
 *
 * <AUTHOR>
 * @since 21.02.25
 */
class TabProfileStatusFolderSetTest extends AbstractTestCaseJ5
{
    private static MetaClass scClass, teamClass;
    private static SettingsSet settingsSet, settingsSet2;
    private static SuperUser superUser;
    private static final String BUTTON_WITH_CODE_NOT_DISPLAYED_MESSAGE = "Кнопка с кодом %s не отображается";
    private static final String BUTTON_WITH_CODE_DISPLAYED_MESSAGE = "Кнопка с кодом %s отображается";
    private static final String BUTTON_EXISTS = "Кнопка найдена на странице";
    private static final String BUTTON_NOT_EXISTS = "Кнопка не найдена на странице";

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * <li>Создать профиль администрирования adminProfile и выдать ему все права в Матрице маркеров доступа</li>
     * <li>Создать комплект setSettings1</li>
     * <li>Создать комплект setSettings2 и связать его с профилем администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser, указать ему профиль администрирования adminProfile</li>
     * <li>Для класса Запрос (serviceCall) добавить тип: scCase1</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);
        DSLAdminProfile.addAllRightsToAdminProfile(adminProfile);

        settingsSet = DAOSettingsSet.createSettingsSet();
        settingsSet2 = DAOSettingsSet.createSettingsSet();
        settingsSet2.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(settingsSet, settingsSet2);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        teamClass = DAOTeamCase.createClass();
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса на вкладках, которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для вкладки Параметры объекта класса Запрос указать комплект setSettings1</li>
     * <li>Для вкладки Параметры объекта класса Команда указать комплект setSettings2</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Карточка объекта класса Запрос</li>
     * <li>Нажать на кнопку Редактировать вкладки</li>
     * <li>Проверить, что на форме Редактирования вкладок у вкладки Параметры объекта отсутствует кнопка
     * Редактировать</li>
     * <li>Нажать на кнопку Закрыть</li>
     * <li>Нажать на кнопку Режим разметки</li>
     * <li>Проверить, что невозможно изменить (подвинуть) линию разметки</li>
     * <li>Открыть вкладку Карточка объекта класса Команда</li>
     * <li>Нажать на кнопку Редактировать вкладки</li>
     * <li>Проверить, что на форме Редактирования вкладок у вкладки Параметры объекта присутствует кнопка
     * Редактировать</li>
     * <li>Нажать на кнопку Закрыть</li>
     * <li>Нажать на кнопку Режим разметки</li>
     * <li>Проверить, что можно изменить положение линии разметки</li>
     * </ol>
     */
    @Test
    void testTabVisibilityBasedOnSettings()
    {
        //Подготовка
        GUILogon.asSuper();
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditMainTabs();
        GUITab.clickEditByIndex(1);
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();

        GUIMetaClass.goToTab(teamClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditMainTabs();
        GUITab.clickEditByIndex(1);
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet2.getCode());
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();

        //Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditMainTabs();

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        GUITester.assertAbsent(GUIXpath.Div.PROPERTY_DIALOG_BOX + GUIXpath.Span.EDIT_ICON,
                "Иконка редактирования присутствует на форме");
        GUITab.closeEditTabsForm();
        GUIContent.clickLayoutMode();

        tester.moveTo(GUIContent.LAYOUT_MODE_SPLITTER);
        GUITester.assertCssValue("Есть возможность изменить (подвинуть) линию разметки",
                GUIContent.LAYOUT_MODE_SPLITTER, "cursor", "auto");
        GUIMetaClass.goToTab(teamClass, MetaclassCardTab.OBJECTCARD);

        GUIContent.clickEditMainTabs();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX_CAPTION);
        GUITester.assertPresent(GUIXpath.Div.PROPERTY_DIALOG_BOX + GUIXpath.Span.EDIT_ICON,
                "Иконка редактирования присутствует на форме");
        GUITab.closeEditTabsForm();

        GUIContent.clickLayoutMode();
        tester.moveTo(GUIContent.LAYOUT_MODE_SPLITTER);
        GUITester.assertCssValue("Есть возможность изменить (подвинуть) линию разметки",
                GUIContent.LAYOUT_MODE_SPLITTER, "cursor", "col-resize");
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса в профилях матрицы прав, которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для класса Запрос добавить профиль profile1: Роли пользователь - Сотрудник, комплект - setSettings1</li>
     * <li>Для класса Запрос добавить профиль profile2: Роли пользователь - Сотрудник, комплект - setSettings2</li>
     * <li>Добавить маркеры прав Просмотр атрибутов объектов/Остальные атрибуты для profile1 и profile2</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть вкладку Права доступа класса Запрос</li>
     * <li>Проверить, что нельзя удалить маркер прав на Просмотр атрибутов для profile1</li>
     * <li>Проверить, что нельзя добавить любой маркер прав для profile1</li>
     * <li>Проверить, что можно удалить маркер прав на Просмотр атрибутов для profile2</li>
     * <li>Проверить, что можно добавить любой маркер прав для profile2</li>
     * <li>Навести курсор мыши на название profile1</li>
     * <li>Проверить, что отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Навести курсор на название profile2</li>
     * <li>Проверить, что присутствуют кнопки Редактировать, Удалить</li>
     * </ol>
     */
    @Test
    void testInterfaceElementAccessByRoleMatrix()
    {
        //Подготовка
        SecurityProfile profile1 = DAOSecurityProfile.create(true, null, List.of(SysRole.employee()), settingsSet);
        SecurityProfile profile2 = DAOSecurityProfile.create(true, null, List.of(SysRole.employee()), settingsSet2);

        DSLSecurityProfile.add(profile1, profile2);
        DSLSecurityProfile.setRights(scClass, profile1, AbstractBoRights.VIEW_REST_ATTRIBUTES);
        DSLSecurityProfile.setRights(scClass, profile2, AbstractBoRights.VIEW_REST_ATTRIBUTES);

        //Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.goToAccessMatrixTab();

        GUIAccessMatrix.assertCheckBoxEnabled(AbstractBoRights.VIEW_REST_ATTRIBUTES.getRightCode(), profile1.getCode(),
                false);
        GUIAccessMatrix.assertCheckBoxEnabled(AbstractBoRights.EDIT_REST_ATTRIBUTES.getRightCode(), profile1.getCode(),
                false);
        GUIAccessMatrix.assertCheckBoxEnabled(AbstractBoRights.VIEW_REST_ATTRIBUTES.getRightCode(), profile2.getCode(),
                true);
        GUIAccessMatrix.assertCheckBoxEnabled(AbstractBoRights.EDIT_REST_ATTRIBUTES.getRightCode(), profile2.getCode(),
                true);

        tester.moveTo(GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile1.getCode()));
        GUITester.assertExists(
                GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile1.getCode()).concat(GUIXpath.Span.EDIT_ICON),
                false, BUTTON_EXISTS);
        GUITester.assertExists(
                GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile1.getCode()).concat(Span.DELETE_ICON), false,
                BUTTON_EXISTS);

        tester.moveTo(GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile2.getCode()));
        GUITester.assertExists(
                GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile2.getCode()).concat(GUIXpath.Span.EDIT_ICON),
                true, BUTTON_NOT_EXISTS);
        GUITester.assertExists(
                GUIAccessMatrix.PROFILE_COLUMN_TITLE.formatted(profile2.getCode()).concat(Span.DELETE_ICON), true,
                BUTTON_NOT_EXISTS);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса в действиях/условиях на вход/выход в/из статуса,
     * которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для класса Запрос для статуса Зарегистрирован добавить Действие при входе в статус:
     *     <ul>
     *         <li>Название - actionEntrance1</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - actionEntrance1</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings1</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Зарегистрирован добавить Действие при выходе из статуса:
     *     <ul>
     *         <li>Название - actionLeaving1</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - actionLeaving1</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings1</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Зарегистрирован добавить Условие при входе в статус:
     *     <ul>
     *         <li>Название - conditionEntrance1</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - conditionEntrance1</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings1</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Зарегистрирован добавить Условие при выходе из статуса:
     *     <ul>
     *         <li>Название - conditionLeaving1</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - conditionLeaving1</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings1</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Разрешен добавить Действие при входе в статус:
     *     <ul>
     *         <li>Название - actionEntrance2</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - actionEntrance2</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings2</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Разрешен добавить Действие при выходе из статуса:
     *     <ul>
     *         <li>Название - actionLeaving2</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - actionLeaving2</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings2</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Разрешен добавить Условие при входе в статус:
     *     <ul>
     *         <li>Название - conditionEntrance2</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - conditionEntrance2</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings2</li>
     *     </ul>
     * </li>
     * <li>Для класса Запрос для статуса Разрешен добавить Условие при выходе из статуса:
     *     <ul>
     *         <li>Название - conditionLeaving2</li>
     *         <li>Скрипт - новый скрипт</li>
     *         <li>Название скрипта - conditionLeaving2</li>
     *         <li>Текст скрипта - произвольный</li>
     *         <li>Комплект действия - setSettings2</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть карточку статуса Зарегистрирован для класса Запрос</li>
     * <li>Для действия при входе в статус actionEntrance1 отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Для действия при выходе из статуса actionLeaving1 отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Для условия при входе в статус conditionEntrance1 отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Для условия при выходе из статуса conditionLeaving1 отсутствуют кнопки Редактировать, Удалить</li>
     * <li>Открыть карточку статуса Разрешен для класса Запрос</li>
     * <li>Для действия при входе в статус actionEntrance2 присутствуют кнопки Редактировать, Удалить</li>
     * <li>Для действия при выходе из статуса actionLeaving2 присутствуют кнопки Редактировать, Удалить</li>
     * <li>Для условия при входе в статус conditionEntrance2 присутствуют кнопки Редактировать, Удалить</li>
     * <li>Для условия при выходе из статуса conditionLeaving2 присутствуют кнопки Редактировать, Удалить</li>
     * </ol>
     */
    @Test
    void testElementDisplayInStatusTransitionConditions()
    {
        //Подготовка
        BoStatus registered = DAOBoStatus.createRegistered(scClass);
        BoStatus resolved = DAOBoStatus.createResolved(scClass);

        StatusAction actionEntrance1 = addScriptActionOrCondition(registered, settingsSet,
                DAOStatusAction::createPreAction);
        StatusAction actionLeaving1 = addScriptActionOrCondition(registered, settingsSet,
                DAOStatusAction::createPostAction);
        StatusAction conditionEntrance1 = addScriptActionOrCondition(registered, settingsSet,
                DAOStatusAction::createPreCondition);
        StatusAction conditionLeaving1 = addScriptActionOrCondition(registered, settingsSet,
                DAOStatusAction::createPostCondition);
        StatusAction actionEntrance2 = addScriptActionOrCondition(resolved, settingsSet2,
                DAOStatusAction::createPreAction);
        StatusAction actionLeaving2 = addScriptActionOrCondition(resolved, settingsSet2,
                DAOStatusAction::createPostAction);
        StatusAction conditionEntrance2 = addScriptActionOrCondition(resolved, settingsSet2,
                DAOStatusAction::createPreCondition);
        StatusAction conditionLeaving2 = addScriptActionOrCondition(resolved, settingsSet2,
                DAOStatusAction::createPostCondition);

        //Действия и проверки
        GUILogon.login(superUser);
        GUIBoStatus.goToStatusCard(registered);
        GUIContent.assertIconsActionExists(actionEntrance1.getCode(), false, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(actionLeaving1.getCode(), false, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(conditionEntrance1.getCode(), false, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(conditionLeaving1.getCode(), false, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIBoStatus.goToStatusCard(resolved);

        GUIContent.assertIconsActionExists(actionEntrance2.getCode(), true, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(actionLeaving2.getCode(), true, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(conditionEntrance2.getCode(), true, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(conditionLeaving2.getCode(), true, GUIButtonBar.BTN_EDIT,
                GUIButtonBar.BTN_DELETE);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для папок в справочнике, которые размечены комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Для системного справочника Категории добавить папку: Название - folder1, код - folder1, Комплект -
     * setSettings1</li>
     * <li>Для системного справочника Категории добавить папку: Название - folder2, код - folder2, Комплект -
     * setSettings2</li>
     * <b>Действия и действия:</b>
     * <li>Войти в систему под пользователем superUser</li>
     * <li>Открыть карточку справочника Категории</li>
     * <li>Проверить, что для папки folder1 отсутствуют кнопки Редактировать, Поместить в архив, Удалить</li>
     * <li>Проверить, что для папки folder2 присутствуют кнопки Редактировать, Поместить в архив, Удалить</li>
     */
    @Test
    void testDisplayAndHideUIElementsForMarkedFolders()
    {
        //Подготовка
        CatalogItem folder1 = DAOCatalogItem.createUserFolder(SystemCatalog.CATEGORY.getCode());
        folder1.setSettingsSet(settingsSet);
        CatalogItem folder2 = DAOCatalogItem.createUserFolder(SystemCatalog.CATEGORY.getCode());
        folder2.setSettingsSet(settingsSet2);
        DSLCatalogItem.add(folder1, folder2);

        //Действия и проверки
        GUILogon.login(superUser);
        GUICatalog.goToCard(DAOCatalog.createSystem(SystemCatalog.CATEGORY));
        GUIContent.assertIconsActionExists(folder1.getUuid(), false, GUIButtonBar.BTN_EDIT, GUIButtonBar.BTN_REMOVE,
                GUIButtonBar.BTN_DELETE);
        GUIContent.assertIconsActionExists(folder2.getUuid(), true, GUIButtonBar.BTN_EDIT, GUIButtonBar.BTN_REMOVE,
                GUIButtonBar.BTN_DEL);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для элемента справочника "Таблица соответствий",
     * который размечен комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В справочник Таблицы соответствий добавить элемент:
     *     <ul>
     *         <li>Название - element1</li>
     *         <li>Код - element1</li>
     *         <li>Объекты - Запрос</li>
     *         <li>Определяемые атрибуты - произвольно</li>
     *         <li>Определяющие атрибуты - произвольно</li>
     *         <li>Комплект - setSettings1</li>
     *     </ul>
     * </li>
     * <li>В справочник Таблицы соответствий добавить элемент:
     *     <ul>
     *         <li>Название - element2</li>
     *         <li>Код - element2</li>
     *         <li>Объекты - Запрос</li>
     *         <li>Определяемые атрибуты - произвольно</li>
     *         <li>Определяющие атрибуты - произвольно</li>
     *         <li>Комплект - setSettings2</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser.</li>
     * <li>Открыть карточку element1 справочника Таблицы соответствий.</li>
     * <li>Проверить, что на карточке элемента element1 отсутствуют кнопки Редактировать, Копировать, Загрузить,
     * Поместить в архив, Удалить, Добавить строку.</li>
     * <li>Открыть вкладку "Правило импорта" для element1.</li>
     * <li>Проверить, что на вкладке "Правило импорта" отсутствуют кнопки Редактировать, Удалить.</li>
     * <li>Открыть вкладку "История" для element1.</li>
     * <li>Проверить, что на вкладке "История" отсутствует кнопка Редактировать.</li>
     * <li>Нажать на кнопку "К справочнику Таблицы соответствий".</li>
     * <li>Нажать на кнопку element2.</li>
     * <li>Проверить, что на карточке элемента element2 присутствуют кнопки Редактировать, Копировать, Загрузить,
     * Выгрузить, Поместить в архив, Удалить, Добавить строку.</li>
     * <li>Открыть вкладку "Правило импорта" для element2.</li>
     * <li>Проверить, что на вкладке "Правило импорта" присутствуют кнопки Редактировать, Удалить.</li>
     * <li>Открыть вкладку "История" для element2.</li>
     * <li>Проверить, что на вкладке "История" присутствует кнопка Редактировать.</li>
     * </ol>
     */
    @Test
    void verifyVisibilityAndHidingOfMatchingTableElements()
    {
        //Подготовка
        CatalogItem rulesSettings = DAOCatalogItem.createRulesSettings(scClass,
                "resolutionTime", "impact");
        CatalogItem rulesSettings2 = DAOCatalogItem.createRulesSettings(scClass,
                "resolutionTime", "impact");
        rulesSettings.setSettingsSet(settingsSet);
        rulesSettings2.setSettingsSet(settingsSet2);
        DSLCatalogItem.add(rulesSettings, rulesSettings2);

        //Действия и проверки
        GUILogon.login(superUser);
        GUICatalogItem.goToCard(rulesSettings);
        GUICatalog.assertCatalogButtonAbsence(GUICatalog.BTN_EDIT, GUICatalog.BTN_COPY, GUICatalog.BTN_DOWNLOAD,
                GUICatalog.BTN_EXPORT_ADVLIST, GUICatalog.BTN_ARCHIVE, GUICatalog.BTN_DEL);
        GUITester.assertAbsent(GUICatalog.X_BUTTON_EXPAND,
                String.format(BUTTON_WITH_CODE_DISPLAYED_MESSAGE, GUICatalog.X_BUTTON_EXPAND));

        GUITester.assertExists(GUIXpath.Any.DEBUG_CONTENT + GUICatalog.BTN_ADD, false,
                BUTTON_NOT_EXISTS);

        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_CONFIG_TAB);
        GUICatalog.assertCatalogButtonAbsence(GUICatalog.BTN_EDIT, GUICatalog.BTN_DEL);
        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_HISTORY_TAB);
        GUIRulesSettings.assertButtonsPresent(false, GUICatalog.BTN_EDIT);

        GUICatalogItem.goToCard(rulesSettings2);
        GUICatalog.assertCatalogButtonPresent(GUICatalog.BTN_EDIT, GUICatalog.BTN_COPY, GUICatalog.BTN_DOWNLOAD,
                GUICatalog.BTN_EXPORT_ADVLIST);
        GUICatalogItem.expandAdditionalButtons();
        GUICatalogItem.assertAdditionalButtonsExists(true, GUICatalog.BTN_ARCHIVE, GUICatalog.BTN_DEL);
        GUITester.assertPresent(GUIAdvListXpath.EDITABLE_TOOL_CONTENT + GUIXpath.Div.ADD,
                String.format(BUTTON_WITH_CODE_NOT_DISPLAYED_MESSAGE, GUIXpath.Div.ADD));

        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_CONFIG_TAB);
        GUIRulesSettings.assertButtonsPresent(true, GUICatalog.BTN_EDIT, GUICatalog.BTN_DEL);

        GUIRulesSettings.goToRulesSettingsCard(GUITab.IMPORT_HISTORY_TAB);
        GUIRulesSettings.assertButtonsPresent(true, GUICatalog.BTN_EDIT);
    }

    /**
     * Тестирование отображения и скрытия элементов интерфейса для элемента справочника "Классы обслуживания",
     * который размечен комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В справочник Классы обслуживания добавить элемент:
     *     <ul>
     *         <li>Название - serviceClass1</li>
     *         <li>Код - serviceClass1</li>
     *         <li>Комплект - setSettings1</li>
     *     </ul>
     * </li>
     * <li>В справочник Классы обслуживания добавить элемент:
     *     <ul>
     *         <li>Название - serviceClass2</li>
     *         <li>Код - serviceClass2</li>
     *         <li>Комплект - setSettings2</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем superUser.</li>
     * <li>Открыть карточку serviceClass1 справочника Классы обслуживания.</li>
     * <li>Проверить, что на карточке элемента serviceClass1 отсутствуют кнопки Редактировать, Копировать, Поместить
     * в архив, Удалить, Добавить исключение.</li>
     * <li>Нажать на кнопку "К справочнику Классы обслуживания".</li>
     * <li>Нажать на serviceClass2.</li>
     * <li>Проверить, что на карточке элемента serviceClass2 присутствуют кнопки Редактировать, Копировать, Поместить
     * в архив, Удалить, Добавить исключение.</li>
     * </ol>
     */
    @Test
    void testServiceClassButtonsVisibilityBasedOnSet()
    {
        //Подготовка
        CatalogItem serviceTime = DAOCatalogItem.createServiceTime();
        serviceTime.setSettingsSet(settingsSet);
        CatalogItem serviceTime2 = DAOCatalogItem.createServiceTime();
        serviceTime2.setSettingsSet(settingsSet2);
        DSLCatalogItem.add(serviceTime, serviceTime2);

        //Действия и проверки
        GUILogon.login(superUser);
        GUICatalogItem.goToCard(serviceTime);
        GUICatalog.assertCatalogButtonAbsence(GUIMetaClass.BTN_EDIT, GUIMetaClass.BTN_COPY, GUIMetaClass.BTN_REMOVE,
                GUICatalog.BTN_DEL);
        GUITester.assertAbsent(GUIXpath.Any.DEBUG_CONTENT + GUIXpath.Any.ANY_CONTAINS,
                String.format(BUTTON_WITH_CODE_DISPLAYED_MESSAGE, GUIXpath.Any.ADD_CONTAINS), GUIButtonBar.BTN_ADD);

        GUICatalogItem.goToCard(serviceTime2);
        GUICatalog.assertCatalogButtonPresent(GUIMetaClass.BTN_EDIT, GUIMetaClass.BTN_COPY, GUIMetaClass.BTN_REMOVE,
                GUICatalog.BTN_DEL);
        GUITester.assertPresent(GUIXpath.Any.DEBUG_CONTENT + GUIXpath.Any.ANY_CONTAINS,
                String.format(BUTTON_WITH_CODE_NOT_DISPLAYED_MESSAGE, GUIXpath.Any.ADD_CONTAINS), GUIButtonBar.BTN_ADD);
    }

    /**
     * Добавляет действие или условие к статусу, используя переданную функцию для создания действия.
     *
     * @param status модель статуса БО
     * @param settingsSet комплект
     * @param operationCreator функция, которая создает действие или условие на основе статуса и кода скрипта
     */
    private static StatusAction addScriptActionOrCondition(BoStatus status, SettingsSet settingsSet,
            BiFunction<BoStatus, String, StatusAction> operationCreator)
    {
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        StatusAction action = operationCreator.apply(status, script.getCode());
        action.setSettingsSet(settingsSet);
        DSLBoStatus.addScriptActionOrCondition(action, script);
        return action;
    }
}
