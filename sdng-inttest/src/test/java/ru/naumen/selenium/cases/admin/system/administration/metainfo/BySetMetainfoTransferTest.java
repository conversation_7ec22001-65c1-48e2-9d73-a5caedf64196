package ru.naumen.selenium.cases.admin.system.administration.metainfo;

import java.io.File;
import java.util.Collections;
import java.util.List;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLBreadCrumb;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOStatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.actions.DAOMobileAction;
import ru.naumen.selenium.casesutil.model.mobile.actions.MobileActionType;
import ru.naumen.selenium.casesutil.model.mobile.actions.MobileObjectAction;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование загрузки и выгрузки метаинформации по комплектам.
 * <AUTHOR>
 * @since Aug 20, 2024
 */
public class BySetMetainfoTransferTest extends AbstractTestCase
{
    private static SettingsSet settingsSet;

    /**
     * Общая подготовка.
     * <ol>
     * <li>Включить использование профилей администрирования на стенде</li>
     * <li>Включить использование комплектов на стенде</li>
     * <li>Создать комплект settingsSet</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        settingsSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet);
    }

    /**
     * Тестирование загрузки и выгрузки классов и атрибутов в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс markedMetaClass (Комплект — settingsSet)</li>
     * <li>Создать классы unmarkedMetaClass и markedAfterExportClass</li>
     * <li>В классе markedMetaClass создать атрибут markedAttribute (Комплект — settingsSet)</li>
     * <li>В классе markedMetaClass создать атрибуты unmarkedAttribute и markedAfterExportAttribute</li>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Разметить класс markedAfterExportClass комплектом settingsSet</li>
     * <li>Изменить названия классов markedMetaClass и unmarkedMetaClass</li>
     * <li>Разметить атрибут markedAfterExportAttribute комплектом settingsSet</li>
     * <li>Изменить названия атрибутов markedAttribute и unmarkedAttribute</li>
     * <br>
     * <b>Действия</b>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверки</b>
     * <li>Класс markedAfterExportClass удален</li>
     * <li>Класс markedMetaClass вернул свое прежнее название</li>
     * <li>Название класса unmarkedMetaClass осталось без изменений</li>
     * <li>Атрибут markedAfterExportAttribute удален</li>
     * <li>Атрибут markedAttribute вернул свое прежнее название</li>
     * <li>Название атрибута unmarkedAttribute осталось без изменений</li>
     * </ol>
     */
    @Test
    public void testTransferMetaClassesAndAttributes()
    {
        // Подготовка
        MetaClass markedMetaClass = DAOUserClass.create();
        String oldMarkedMetaClassTitle = markedMetaClass.getTitle();
        markedMetaClass.setSettingsSet(settingsSet);
        MetaClass unmarkedMetaClass = DAOUserClass.create();
        MetaClass markedAfterExportClass = DAOUserClass.create();

        Attribute markedAttribute = DAOAttribute.createString(markedMetaClass);
        String oldMarkedAttributeTitle = markedAttribute.getTitle();
        markedAttribute.setSettingsSet(settingsSet);
        Attribute unmarkedAttribute = DAOAttribute.createString(markedMetaClass);
        Attribute merkedAfterExportAttribute = DAOAttribute.createString(markedMetaClass);

        DSLMetainfo.add(markedMetaClass, unmarkedMetaClass, markedAfterExportClass, markedAttribute, unmarkedAttribute,
                merkedAfterExportAttribute);

        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        markedAfterExportClass.setSettingsSet(settingsSet);
        markedMetaClass.setTitle(ModelUtils.createTitle());
        unmarkedMetaClass.setTitle(ModelUtils.createTitle());
        DSLMetaClass.edit(markedMetaClass, unmarkedMetaClass, markedAfterExportClass);
        merkedAfterExportAttribute.setSettingsSet(settingsSet);
        markedAttribute.setTitle(ModelUtils.createTitle());
        unmarkedAttribute.setTitle(ModelUtils.createTitle());
        DSLAttribute.edit(markedAttribute, unmarkedAttribute, merkedAfterExportAttribute);

        // Действия
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверки
        DSLMetaClass.assertAbsence(markedAfterExportClass);
        markedAfterExportClass.setExists(false);
        DSLAttribute.assertAbsence(merkedAfterExportAttribute);
        merkedAfterExportAttribute.setExists(false);
        MetaClass markedClassAfterExport = DSLMetaClass.getMetaClass(markedMetaClass.getFqn());
        Assert.assertEquals("Название класса не было заменено после загрузки метаинформации.",
                oldMarkedMetaClassTitle, markedClassAfterExport.getTitle());
        String markedAttributeTitleAfterExport = DSLAttribute.getTitle(markedAttribute);
        Assert.assertEquals("Название атрибута не было заменено после загрузки метаинформации.",
                oldMarkedAttributeTitle, markedAttributeTitleAfterExport);
        MetaClass unmarkedMetaClassAfterExport = DSLMetaClass.getMetaClass(unmarkedMetaClass.getFqn());
        Assert.assertEquals("Название класса было заменено после загрузки метаинформации.",
                unmarkedMetaClass.getTitle(), unmarkedMetaClassAfterExport.getTitle());
        String unmarkedAttributeTitleAfterExport = DSLAttribute.getTitle(unmarkedAttribute);
        Assert.assertEquals("Название атрибута было заменено после загрузки метаинформации.",
                unmarkedAttribute.getTitle(), unmarkedAttributeTitleAfterExport);
    }

    /**
     * Тестирование возникновения ошибки при попытке перенести размеченный комплектом атрибут в несуществующий класс
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass</li>
     * <li>В классе userClass создать атрибут attribute (Комплект — settingsSet)</li>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Удалить класс metaCLass</li>
     * <li>Попытаться загрузить ранее выгруженную метаинформацию</li>
     * <li>Проверить, что при загрузке возникла ошибка:<br>
     * Метаинформация не может быть загружена. Отсутствует родительская настройка с типом
     * 'Класс/тип' и кодом 'userClass' для других настроек в метаинформации</li>
     * </ol>
     */
    @Test
    public void testTransferAttributeToNonExistentClassError()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        Attribute attribute = DAOAttribute.createString(userClass);
        attribute.setSettingsSet(settingsSet);
        DSLMetainfo.add(userClass, attribute);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));

        // Действия и проверки
        DSLMetaClass.delete(userClass);
        DSLMetainfoTransfer.tryImportMetainfo(metainfo, "Метаинформация не может быть загружена. "
                                                        + "Отсутствует родительская настройка с типом 'Класс/тип' и "
                                                        + "кодом '"
                                                        + userClass.getCode()
                                                        + "' для других настроек в метаинформации.");
    }

    /**
     * Тестирование загрузки и выгрузки скрипта в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт script (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить скрипт script</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Скрипт script присутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferScripts()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        script.setSettingsSet(settingsSet);
        DSLScriptInfo.addScript(script);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLScriptInfo.deleteScript(script);
        DSLScriptInfo.assertAbsenceScriptInfo(script.getCode(), true);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверка
        DSLScriptInfo.assertAbsenceScriptInfo(script.getCode(), false);
        script.setExists(true);
    }

    /**
     * Тестирование загрузки и выгрузки формы быстрого добавления и редактирования в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Создать форму быстрого добавления и редактирования quickForm2 (Комплект — settingsSet)</li>
     * <li>Удалить форму quickForm</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Форма quickForm присутствует на стенде</li>
     * <li>Форма quickForm2 отсутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferQuickForms()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        quickForm.setSettingsSet(settingsSet);
        DSLCustomForm.add(quickForm);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));

        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        quickForm2.setSettingsSet(settingsSet);
        DSLCustomForm.add(quickForm2);

        DSLCustomForm.delete(quickForm);
        DSLCustomForm.assertAbsence(quickForm);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверки
        DSLCustomForm.assertPresent(quickForm);
        quickForm.setExists(true);
        DSLCustomForm.assertAbsence(quickForm2);
        quickForm2.setExists(false);
    }

    /**
     * Тестирование загрузки и выгрузки действие на вход в статус в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Создать статус status в типе userCase</li>
     * <li>Создать для статуса status действие на вход в статус statusAction (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить действие на вход в статус statusAction</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Действие на вход в статус statusAction присутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferStatePreActions()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        BoStatus status = DAOBoStatus.createUserStatus(userCase.getFqn());
        DSLBoStatus.add(status);

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        StatusAction statusAction = DAOStatusAction.createPreAction(status, scriptInfo.getCode());
        statusAction.setSettingsSet(settingsSet);
        DSLBoStatus.addScriptActionOrCondition(statusAction, scriptInfo);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLBoStatus.deleteScriptActionOrCondition(statusAction);
        DSLBoStatus.assertAbsenceStatusAction(status, statusAction);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверка
        DSLBoStatus.assertPresentStatusAction(status, statusAction);
        statusAction.setExists(true);
    }

    /**
     * Тестирование загрузки и выгрузки роли в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass</li>
     * <li>В рамках настроек прав доступа класса userClass создать роль role (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить роль role</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Роль role присутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferSecurityRoles()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        ScriptInfo accessScriptInfo = DSLSecurityRole.createDefaultAccessScriptInfo();
        ScriptInfo ownersScriptInfo = DSLSecurityRole.createDefaultOwnersScriptInfo();
        SecurityRole role = DAOSecurityRole.create(userClass, accessScriptInfo, ownersScriptInfo);
        role.setSettingsSet(settingsSet);
        DSLSecurityRole.add(role);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLSecurityRole.delete(role);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверка
        DSLSecurityRole.assertPresence(role);
        role.setExists(true);
    }

    /**
     * Тестирование загрузки и выгрузки настройки «хлебных крошек» в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass</li>
     * <li>Добавить настройку «хлебной крошки» crumb для класса userClass по атрибуту «Автор»
     * (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить настройку crumb</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Настройка «хлебной крошки» crumb присутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferBreadCrumbs()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        Crumb crumb = DAOBreadCrumb.create(userClass, SysAttribute.author(userClass));
        crumb.setSettingsSet(settingsSet);
        DSLBreadCrumb.add(crumb);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLBreadCrumb.delete(crumb);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверка
        DSLBreadCrumb.assertPresent(crumb);
        crumb.setExists(true);
    }

    /**
     * Тестирование загрузки и выгрузки настройки «хлебных крошек» в комплекте
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить лицензию с модулем мобильного клиента</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать карточку объекта класса userClass в настройках мобильного приложения</li>
     * <li>На созданной карточке добавить действия в меню: mobileAction1 (Комплект — settingsSet), mobileAction2 и
     * mobileAction3</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить действия mobileAction1 и mobileAction2 из настроек карточки mobileCard</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Для карточки mobileCard теперь настроены действия mobileAction1 и mobileAction3</li>
     */
    @Test
    public void testTransferMobileObjectActions()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        MetaClass userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        MobileCard mobileCard = DAOMobile.createMobileCard(userClass);
        MobileObjectAction mobileAction1 = DAOMobileAction.createMobileObjectAction(MobileActionType.SYSTEM, null);
        MobileObjectAction mobileAction2 = DAOMobileAction.createMobileObjectAction(MobileActionType.SYSTEM, null);
        MobileObjectAction mobileAction3 = DAOMobileAction.createMobileObjectAction(MobileActionType.SYSTEM, null);
        mobileAction1.setSettingsSet(settingsSet.getCode());
        mobileCard.setObjectActions(mobileAction1, mobileAction2, mobileAction3);
        DSLMobile.add(mobileCard);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        mobileCard.setObjectActions(mobileAction3);
        DSLMobile.edit(mobileCard);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);
        GUIMobileCard.clickEditMenuButton();

        // Проверки
        GUIMobileCard.assertPresentElement(mobileAction1.getCaption(), mobileAction3.getCaption());
        GUIMobileCard.assertAbsenceElement(mobileAction2.getCaption());
    }

    /**
     * Тестирование загрузки и выгрузки профилей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать группы пользователей group1 (Комплект — settingsSet) и group2</li>
     * <li>Создать относительные (в привязке к userClass) роли пользователей role1 (Комплект — settingsSet) и role2</li>
     * <li>Создать профиль profile (Роли — role1, Группы пользователей — group1, Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Изменить профиль profile: Роли — role2, Группы пользователей — group2</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>В профиле profile: Роли — role1, role2; Группы пользователей — group1, group2</li>
     */
    @Test
    public void testTransferAndMergeSecurityProfiles()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        SecurityGroup group1 = DAOSecurityGroup.create();
        group1.setSettingsSet(settingsSet);
        SecurityGroup group2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group1, group2);
        SecurityRole role1 = DAOSecurityRole.create(userClass);
        role1.setSettingsSet(settingsSet);
        SecurityRole role2 = DAOSecurityRole.create(userClass);
        DSLSecurityRole.add(role1, role2);
        SecurityProfile profile = DAOSecurityProfile.create(true, group1, role1);
        profile.setSettingsSet(settingsSet);
        DSLSecurityProfile.add(profile);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        profile.setRoles(Json.listToString(role2.getCode()));
        profile.setGroups(Json.listToString(group2.getCode()));
        DSLSecurityProfile.edit(profile);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверки
        SecurityProfile profileAfterLoad = DSLSecurityProfile.getProfile(profile.getCode());
        List<String> roles = Json.stringToList(profileAfterLoad.getRoles());
        Assert.assertEquals(2, roles.size());
        Assert.assertTrue(roles.contains(role1.getCode()));
        Assert.assertTrue(roles.contains(role2.getCode()));
        List<String> groups = Json.stringToList(profileAfterLoad.getGroups());
        Assert.assertEquals(2, groups.size());
        Assert.assertTrue(groups.contains(group1.getCode()));
        Assert.assertTrue(groups.contains(group2.getCode()));
    }

    /**
     * Тестирование загрузки и выгрузки ДПС
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00252
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$299769206
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт script</li>
     * <li>На основе скрипта script создать ДПС eventAction (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Выгрузить метаинформацию по комплекту settingsSet</li>
     * <li>Удалить ДПС eventAction</li>
     * <li>Загрузить ранее выгруженную метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>ДПС eventAction присутствует на стенде</li>
     * </ol>
     */
    @Test
    public void testTransferAndMergeEventActions()
    {
        // Подготовка
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent,
                script.getCode(), true, DAOUserClass.create());
        eventAction.setSettingsSet(settingsSet);
        DSLEventAction.add(eventAction);

        // Действия
        File metainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLEventAction.delete(eventAction);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        // Проверки
        GUILogon.asSuper();
        GUINavigational.goToEventActions();
        GUIEventActionList.assertActionInList(eventAction);
    }
}
