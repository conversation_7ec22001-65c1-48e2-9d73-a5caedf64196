package ru.naumen.selenium.cases.operator.classes.employee;

import java.util.List;

import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование ссылочной целостности в атрибутах связи, где используется сотрудник
 * <AUTHOR>
 * @since 15.05.2014
 */
public class EmployeeIntegrityRelations2Test extends AbstractTestCase
{
    /**
     * Удаление сотрудника, использующегося в таблицах соответствий эскалаций
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>В классе Запрос scClass добавить атрибут attrBoLink типа Ссылка на БО (класс Сотрудник employeeClass)</li>
     * <li>Добавить схему эскалации escalation (Объекты: Запрос, Счетчик времени: Запас нормативного времени
     * обслуживания)</li>
     * <li>Добавить Таблицу соответствий схем эскалаций rulesSettings (Объекты: Запрос, Определяющий атрибут:
     * attrBoLink)</li>
     * <li>Добавить правило row в таблицу соответствий rulesSettings: escalation-employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под лицензированным сотрудником.</li>
     * <li>Зайти на карточку сотрудника employee. Нажать "Удалить". Подтвердить действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удален, есть сообщение об ошибке:
     * Сотрудник '%empl%' не может быть удален по следующим причинам: 1. Сотрудник используется в качестве значения в
     * таблицах соответствия: %название_тс%.</li>
     * <br>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeInEscalationRulesSettings()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass employeeClass = DAOEmployeeCase.createClass();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        Attribute attrBoLink = DAOAttribute.createObjectLink(scClass, employeeClass, null);
        DSLAttribute.add(attrBoLink);

        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, scClass);
        DSLEscalation.add(escalation);

        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scClass, attrBoLink.getCode());
        DSLCatalogItem.add(rulesSettings);

        RsRow row = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalation.getCode(),
                attrBoLink.getCode(), employee.getUuid());
        DSLRsRows.addRowToRSItem(row);

        // Действие
        GUILogon.asTester();

        // Проверка
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.USED_IN_RS,
                rulesSettings.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);

    }

    /**
     * Удаление сотрудника, используемого в настройке таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Cоздать отдел ou типа ouCase, в нем сотрудника employee типа employeeCase</li>
     * <li>В классе Отдел ouClass добавить атрибут attrBoLink типа Ссылка на БО (на тип сотрудника employeeCase)</li>
     * <li>Создать таблицу соответствий catalogItem для класса Отдел ouClass (определяющий – Тип объекта (metaclass),
     * определяемый - attrBoLink)</li>
     * <li>Добавить правило row в таблицу соответствий: ouCase – employee </li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под лицензированным сотрудником</li>
     * <li>Зайти в карточку employee. Нажать Удалить. Подтвердить действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удален, есть сообщение об ошибке:
     * Сотрудник '%наименование_сотрудника%' не может быть удален по следующим причинам: 1. Сотрудник используется в
     * качестве значения в таблицах соответствия: %название_таблицы_соответствий%.</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeInRulesSettings()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        //Создаем отдел и сотрудника
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        Attribute attrBoLink = DAOAttribute.createObjectLink(ouClass, employeeClass, employeeCase, null);
        DSLAttribute.add(attrBoLink);

        String sourceAttr = SysAttribute.metaClass(ouClass).getCode();
        CatalogItem catalogItem = DAOCatalogItem.createRulesSettings(ouClass, attrBoLink.getCode(), sourceAttr);
        DSLCatalogItem.add(catalogItem);
        RsRow row = DAORsRow.create(catalogItem, attrBoLink.getCode(), employee.getUuid(), sourceAttr, ouCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        // Действие
        GUILogon.asTester();

        // Проверка
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.USED_IN_RS,
                catalogItem.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: ответственный за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee1, employee2 типа employeeCase</li>
     * <li>Создать sc типа scCase</li>
     * <li>Назначить employee1 ответственным за sc</li>
     * <li>Назначить employee2 ответственным за sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть удален.
     * Сотрудник связан с историей изменения ответственного и статуса объектов:
     * %список объектов%"</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeOldResponsibleScRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass scCase = SharedFixture.scCase();

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem resolutionTimeRule = SharedFixture.rsResolutionTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();
        CatalogItem timeZone = SharedFixture.timeZone();

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);
        DSLBo.add(ou, team, agreement);

        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2);

        DSLTeam.addEmployees(team, employee1, employee2);
        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZone);
        DSLBo.add(sc);

        DSLBo.editAttributeValue(sc,
                DAOAttribute.createPseudo("Ответственный (команда)", "responsibleTeam", team.getUuid()),
                DAOAttribute.createPseudo("Ответственный (сотрудник)", "responsibleEmployee", employee1.getUuid()));
        DSLBo.editAttributeValue(sc,
                DAOAttribute.createPseudo("Ответственный (команда)", "responsibleTeam", team.getUuid()),
                DAOAttribute.createPseudo("Ответственный (сотрудник)", "responsibleEmployee", employee2.getUuid()));
        // Выполнение действия и проверки
        GUILogon.asTester();
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.REL_WITH_RESP_HISTORY, DSLEmployee.getFullName(employee1),
                sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee1, expectedMessages);
        DSLBo.assertPresent(employee1);
    }

    /**
     * Тестирование удаления сотрудника со связями: решивший запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать sc типа scCase</li>
     * <li>Разрешить запрос sc</li>
     * <li>Назначить employee решившим запрос sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник связан со следующими объектами: %список объектов%"
     * </li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeResolveScRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass scCase = SharedFixture.scCase();

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem resolutionTimeRule = SharedFixture.rsResolutionTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();
        CatalogItem timeZone = SharedFixture.timeZone();

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);
        DSLBo.add(ou, team, agreement);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        DSLTeam.addEmployees(team, employee);
        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZone);
        DSLBo.add(sc);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUISc.resolve(team, employee);
        // Выполнение действия и проверки
        List<String> expectedMessage = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, DSLEmployee.getFullName(employee), sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: ответственный за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать sc типа scCase</li>
     * <li>Назначить employee ответственным за sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть удален.
     * Сотрудник связан со следующими объектами: %запрос% Сотрудник
     * является/являлся ответственным за объекты: запрос '%s'"</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeResponsibleScRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass scCase = SharedFixture.scCase();

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem resolutionTimeRule = SharedFixture.rsResolutionTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();
        CatalogItem timeZone = SharedFixture.timeZone();

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);
        DSLBo.add(ou, team, agreement);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        DSLTeam.addEmployees(team, employee);
        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZone);
        DSLBo.add(sc);

        DSLBo.editAttributeValue(sc,
                DAOAttribute.createPseudo("Ответственный (команда)", "responsibleTeam", team.getUuid()),
                DAOAttribute.createPseudo("Ответственный (сотрудник)", "responsibleEmployee", employee.getUuid()));
        // Выполнение действия и проверки
        GUILogon.asTester();
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, sc.getTitle(),
                ErrorMessages.SECOND, ErrorMessages.REL_WITH_RESP_HISTORY, sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: куратор услуги
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать service типа slmCase</li>
     * <li>Назначить employee куратором service</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник связан со следующими объектами: %список объектов%"
     * </li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeSlmRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass serviceCase = SharedFixture.slmCase();

        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);

        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        DSLBo.editAttributeValue(service,
                DAOAttribute.createPseudo("Куратор услуги (команда)", "responsibleTeam", team.getUuid()),
                DAOAttribute.createPseudo("Куратор услуги (сотрудник)", "responsibleEmployee", employee.getUuid()));
        // Выполнение действия и проверки
        GUILogon.asTester();
        List<String> expectedMessage = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, DSLEmployee.getFullName(employee), service.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование архивирования сотрудника, ответственного за Пользовательский БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензионного сотрудника employee со связью с командой team</li>
     * <li>Создать пользовательский БО userBo (с назначением ответственных и ЖЦ)</li>
     * <li>Назначить ответственным за объект сотрудника employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заархивировать сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не в архиве, есть сообщение об ошибке Сотрудник
     * '%название сотрудника%' не может быть помещен в архив по следующим
     * причинам: 1. В рамках команды '%название команды%' cотрудник '%название
     * сотрудника%' является ответственным за объекты: %класс объекта%
     * '%название объекта%' 2. Сотрудник является ответственным за открытые
     * запросы: %класс объекта% '%название объекта%'</li>
     * </ol>
     */
    @Test
    public void testTryToArchiveEmployeeWhoIsResponsibleForUserBo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        userClass.setHasWorkflow("true");
        DSLMetaClass.add(userClass);
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee, team, userBo);

        DSLTeam.addEmployees(team, employee);

        DSLSc.setResponsible(userBo, team, employee);

        String expectedMessages = ErrorMessages.EMP + "'" + employee.getTitle() + "' " + ErrorMessages.NOT_BE_REM + "\n"
                                  + ErrorMessages.FIRST
                                  + String.format(ErrorMessages.IS_RESPONSIBLE_WITHIN_TEAM, team.getTitle(),
                employee.getTitle())
                                  + userClass.getTitle() + " '" + userBo.getTitle() + "'\n" + ErrorMessages.SECOND
                                  + ErrorMessages.EMP
                                  + ErrorMessages.IS_RESPONSIBLE_OF_OPEN_SC + ": " + userClass.getTitle().toLowerCase()
                                  + " '"
                                  + userBo.getTitle() + "'";

        // Действие
        GUILogon.asTester();

        // Проверка
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: автор объектов (отдел и
     * пользовательский бо)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee</li>
     * <li>Создать ou типа ouCase, автор- employee</li>
     * <li>Создать obj типа userCase, автор- employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>employee НЕ удален, появилось сообщение об ошибке</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeAuthorOfObjects()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);

        DSLMetaClass.add(ouCase, userClass, userCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        Bo obj = DAOUserBo.create(userCase);
        Bo ou1 = DAOOu.create(ouCase);
        Attribute authObj = SysAttribute.author(userCase);
        authObj.setValue(employee.getUuid());
        Attribute authOu = SysAttribute.author(ouCase);
        authOu.setValue(employee.getUuid());
        DSLBo.add(ou1, obj);
        DSLBo.editAttributeValue(ou1, authOu);
        DSLBo.editAttributeValue(obj, authObj);

        // Выполнение действия
        GUILogon.asTester();
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, employee.getTitle(),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.HAS_REL_OBJS,
                userClass.getTitle().toLowerCase(), obj.getTitle(), ErrorMessages.OU.toLowerCase(), ou1.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование попытки удаления сотрудника со связями: ответственный за
     * закрытый запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем сотрудника employee типа employeeCase</li>
     * <li>Зарегистрировать запрос sc типа scCase, сделать ответственным
     * сотрудника employee</li>
     * <li>Закрыть запрос sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Попытаться удалить сотрудника</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удален</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeClosedScRelation()
    {
        // Создаем типы
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();
        MetaClass teamCase = SharedFixture.teamCase();

        // Создаем элементы справочников
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem closureCode = SharedFixture.closureCode();

        // Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team, ou);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);

        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou, employee);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        DSLSc.setResponsible(sc, null, employee);
        DSLSc.resolve(sc, team, null);
        DSLSc.close(sc, team, null, closureCode);
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        // Выполнение действия
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, employee.getTitle(),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.IS_RESPONSIBLE,
                ErrorMessages.SC.toLowerCase(), sc.getTitle(), ErrorMessages.SECOND, sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
    }

    /**
     * Тестирование удаления сотрудника-контрагента закрытого запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee</li>
     * <li>Создать запрос sc, контрагентом которого является сотрудник employee</li>
     * <li>Закрыть запрос sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удалён, есть сообщение об ошибке Сотрудник
     * '%название сотрудника%' не может быть удален по следующим причинам: 1.
     * Сотрудник связан со следующими объектами: запрос '%название запроса%'.</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeContragentOfClosedRequest()
    {
        // Подготовка
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);

        Bo team = SharedFixture.team();

        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou, employee);

        MetaClass scCase = SharedFixture.scCase();
        Bo sc = DAOSc.create(scCase, employee, agreement, SharedFixture.timeZone());
        DSLBo.add(sc);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, closed);
        DSLSc.close(sc, team, employee, SharedFixture.closureCode());

        String expectedMessages = String.format(ErrorMessages.NOT_DEL_EMPLOYEE, employee.getTitle()) + "\n"
                                  + ErrorMessages.FIRST + ErrorMessages.EMP + ErrorMessages.HAS_REL_OBJS
                                  + ErrorMessages.SC.toLowerCase()
                                  + "'" + sc.getTitle() + "'.";

        // Действие
        GUILogon.asTester();

        // Проверка
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Удаление Сотрудника, выбранного в качестве значения по умолчанию в агрегирующем атрибуте для значения
     * Сотрудник-Отдел
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем отдел ou</li>
     * <li>Создаем в отделе ou сотрудника employee</li>
     * <li>Создаем тип teamCase для класса Команда</li>
     * <li>В типе teamCase создаем доп атрибут: agr тип Агрегирующий атрибут (агрегировать класс - Отдел)</li>
     * <li>В типе teamCase устанавливаем значение по умолчанию для атрибута agr: employee-ou</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Пытаемся удалить сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник НЕ удалился, появилось сообщение об ошибке</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeOuWhichUsedInAggrDefaultValue()
    {
        //Подготовка 

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);

        Attribute agr = DAOAttribute.createAggregate(SharedFixture.teamCase(), AggregatedClasses.OU, SharedFixture.ou(),
                employee);
        DSLAttribute.add(agr);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        String msg = String.format("%s'%s' %s%n%%s", ErrorMessages.EMP, employee.getTitle(), ErrorMessages.NOT_BE_DEL);
        String msgPart = String.format("%s%s\"%s (Сотрудник)\" (тип '%s' класса 'Команда').", ErrorMessages.EMP,
                ErrorMessages.USE_AS_DEF_VALUE, agr.getTitle(), SharedFixture.teamCase().getTitle());
        GUIBo.tryToDeleteWithNumberMessage(msg, msgPart);
    }

    /**
     * Удаление Сотрудника, выбранного в качестве значения по умолчанию в агрегирующем атрибуте для значения
     * Сотрудник-Команда
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем отдел ou</li>
     * <li>Создаем в отделе ou сотрудника employee</li>
     * <li>Создаем тип teamCase для класса Команда</li>
     * <li>Создаем команду team типа teamCase</li>
     * <li>Добавляем в команду team сотрудника employee</li>
     * <li>В типе teamCase создаем доп атрибут: agr тип Агрегирующий атрибут (агрегировать класс - Команда)</li>
     * <li>В типе teamCase устанавливаем значение по умолчанию для атрибута agr: employee-team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Пытаемся удалить сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник НЕ удалился, появилось сообщение об ошибке</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeTeamWhichUsedInAggrDefaultValue()
    {
        //Подготовка 

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);

        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(teamCase);

        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        DSLTeam.addEmployees(team, employee);

        Attribute agr = DAOAttribute.createAggregate(teamCase, AggregatedClasses.TEAM, team, employee);

        DSLAttribute.add(agr);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        String msg = String.format("%s'%s' %s%n%%s", ErrorMessages.EMP, employee.getTitle(), ErrorMessages.NOT_BE_DEL);
        String msgPart = String.format("%s%s\"%s (Сотрудник)\" (тип '%s' класса 'Команда').", ErrorMessages.EMP,
                ErrorMessages.USE_AS_DEF_VALUE, agr.getTitle(), teamCase.getTitle());
        GUIBo.tryToDeleteWithNumberMessage(msg, msgPart);
    }

    /**
     * Тестирование удаления сотрудника, ответственного за Пользовательский БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензионного сотрудника employee со связью с командой team</li>
     * <li>Создать пользовательский БО userBo (с назначением ответственных)</li>
     * <li>Назначить ответственным за объект сотрудника employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удален, есть сообщение об ошибке Сотрудник
     * '%название сотрудника%' не может быть удален по следующим причинам: 1.
     * Сотрудник связан со следующими объектами: %класс объекта% '%название
     * объекта%'. 2. Сотрудник является/являлся ответственным за объекты: %класс
     * объекта% '%название объекта%'. 3. В рамках команды '%название команды%'
     * cотрудник '%название сотрудника%' является ответственным за объекты:
     * %класс объекта% '%название объекта%'</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeWhoIsResponsibleForUserBo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        DSLMetaClass.add(userClass);
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee, team, userBo);

        DSLTeam.addEmployees(team, employee);

        DSLSc.setResponsible(userBo, team, employee);

        String expectedMessage1 = ErrorMessages.EMP + "'" + employee.getTitle() + "' " + ErrorMessages.NOT_BE_DEL;
        String expectedMessage4 = ErrorMessages.EMP + ErrorMessages.HAS_REL_OBJS + userClass.getTitle().toLowerCase()
                                  + " '" + userBo.getTitle() + "'.";
        String expectedMessage2 = ErrorMessages.EMP + ErrorMessages.IS_RESPONSIBLE + userClass.getTitle().toLowerCase()
                                  + " '" + userBo.getTitle() + "'.";
        String expectedMessage5 = String.format(
                ErrorMessages.IS_RESPONSIBLE_WITHIN_TEAM + userClass.getTitle() + " '" + userBo.getTitle() + "'",
                team.getTitle(), employee.getTitle());

        List<String> expectedMessages = Lists.newArrayList(expectedMessage1, expectedMessage4, expectedMessage2,
                expectedMessage5);

        // Действие
        GUILogon.asTester();

        // Проверка
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника, который был ответственным за
     * Пользовательский БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензионных сотрудников employee1 и employee2 со связью с
     * командой team</li>
     * <li>Создать пользовательский БО userBo (с назначением ответственных)</li>
     * <li>Назначить ответственным за объект сотрудника employee1</li>
     * <li>Сменить ответственного за объект на сотрудника employee2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить сотрудника employee1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee1 не удален, есть сообщение об ошибке Сотрудник
     * '%название сотрудника%' не может быть удален по следующим причинам: 1.
     * Сотрудник является/являлся ответственным за объекты: %класс объекта%
     * '%название объекта%'.</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteEmployeeWhoWasResponsibleForUserBo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        DSLMetaClass.add(userClass);
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee1, employee2, team, userBo);

        DSLTeam.addEmployees(team, employee1, employee2);

        DSLSc.setResponsible(userBo, team, employee1);
        DSLSc.setResponsible(userBo, team, employee2);

        String expectedMessages = String.format(ErrorMessages.NOT_DEL_EMPLOYEE, employee1.getTitle()) + "\n"
                                  + ErrorMessages.FIRST + ErrorMessages.EMP
                                  + "является/являлся ответственным за объекты: "
                                  + userClass.getTitle().toLowerCase() + " '" + userBo.getTitle() + "'";

        // Действие
        GUILogon.asTester();

        // Проверка
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee1, expectedMessages);
        DSLBo.assertPresent(employee1);
    }

    /**
     * Тестирование удаления последнего лицензированного сотрудника команды,
     * которая назначена ответственной за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать нелицензированного сотрудника employeeUnLicensed и
     * лицензированного сотрудника employeeLicensed</li>
     * <li>Создать команду team</li>
     * <li>Связать команду team и сотрудников - employeeUnLicensed,
     * employeeLicensed</li>
     * <li>Создать запрос sc</li>
     * <li>Назначить ответственной за запрос sc команду team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить лицензированного сотрудника employeeLicensed</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employeeLicensed не удалён, есть сообщение об ошибке
     * Сотрудник '%название сотрудника%' не может быть удален по следующим
     * причинам: 1. Сотрудник '%название сотрудника%' является единственным
     * исполнителем в команде '%название команды%', ответственной за объекты:
     * запрос '%название запроса%'</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteLastLicensedEmployeeOfTeamResponsableForRequest()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        Bo employeeLicensed = DAOEmployee.create(employeeCase, ou, false, true);
        Bo employeeUnLicensed = DAOEmployee.create(employeeCase, ou, false, false);
        DSLBo.add(employeeLicensed, employeeUnLicensed);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team);
        DSLTeam.addEmployees(team, employeeLicensed, employeeUnLicensed);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team, null);

        String expectedMessages = ErrorMessages.EMP + "'" + employeeLicensed.getTitle() + "' "
                                  + ErrorMessages.NOT_BE_DEL + "\n" + ErrorMessages.FIRST
                                  + String.format(ErrorMessages.IS_ONE_PERFORMER_IN_TEAM, employeeLicensed.getTitle(),
                team.getTitle())
                                  + ErrorMessages.SC + "'" + sc.getTitle() + "'";

        // Действие
        GUILogon.asTester();

        // Проверка
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employeeLicensed, expectedMessages);
        DSLBo.assertPresent(employeeLicensed);
    }

    /**
     * Тестирование разрыва связи с командой, ответсвенной за запрос (сотрудник
     * – единственный лицензированный в команде)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00073 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать нелицензированного сотрудника employeeUnLicensed и
     * лицензированного сотрудника employeeLicensed</li>
     * <li>Создать команду team</li>
     * <li>Связать команду team и сотрудников - employeeUnLicensed,
     * employeeLicensed</li>
     * <li>Создать запрос sc</li>
     * <li>Назначить ответственной за запрос sc команду team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Разорвать связь между лицензированным сотрудником employeeLicensed и
     * командой team</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employeeLicensed остался в команде, есть сообщение об
     * ошибке: Команда '%название команды%' не может быть изменена по следующим
     * причинам: 1. Сотрудник '%название сотрудника%' является единственным
     * исполнителем в команде '%название команды%', ответственной за объекты:
     * запрос '%название запроса%'</li>
     * </ol>
     */
    @Test
    public void testTryToUnlinkEmployeeOfTeamResponsableForRequest()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        Bo employeeLicensed = DAOEmployee.create(employeeCase, ou, false, true);
        Bo employeeUnLicensed = DAOEmployee.create(employeeCase, ou, false, false);
        DSLBo.add(employeeLicensed, employeeUnLicensed);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team);
        DSLTeam.addEmployees(team, employeeLicensed, employeeUnLicensed);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team, null);

        String expectedMessages = ErrorMessages.TEAM + "'" + team.getTitle() + "' " + ErrorMessages.NOT_BE_CHNG_F + "\n"
                                  + ErrorMessages.FIRST
                                  + String.format(ErrorMessages.IS_ONE_PERFORMER_IN_TEAM, employeeLicensed.getTitle(),
                team.getTitle())
                                  + ErrorMessages.SC + "'" + sc.getTitle() + "'";

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(team);

        tester.click(GUIXpath.Div.EDIT_CONTAINS);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        BoTree tree = new BoTree(GUIXpath.Id.MEMBERS_VALUE, false);
        tree.unsetElementInMultiSelectTree(ou, employeeLicensed);

        // Проверка
        GUIForm.applyFormAssertError(expectedMessages);
        DSLEmployee.assertInTeam(employeeLicensed, team);
    }
}
