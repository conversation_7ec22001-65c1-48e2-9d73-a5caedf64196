package ru.naumen.selenium.cases.admin.system.administration;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLAdminLite;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminLite;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.adminprofiles.GUIAdminProfile;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.AdminLiteSettings;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;

/**
 * Тесты для проверки функционала суперпользователя
 * <AUTHOR>
 * @since 10.02.2025
 */
class SuperUser4Test extends AbstractTestCaseJ5
{
    private static AdminProfile adminProfile2;
    private static SuperUser superUser1, superUser2;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Включить функциональность профилей администрирования (customAdminProfiles = true)</li>
     * <li>Добавить профиль администрирования ProfileOne, ProfileTwo</li>
     * <li>Добавить суперпользователя ProfileOne - связанного с профилем ProfileOne, ProfileTwo - связанного с профилем
     * ProfileTwo</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        AdminProfile adminProfile1 = DAOAdminProfile.createAdminProfile();
        adminProfile2 = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile1, adminProfile2);

        superUser1 = DAOSuperUser.create();
        superUser2 = DAOSuperUser.create();
        superUser1.setAdminProfiles(adminProfile1);
        superUser2.setAdminProfiles(adminProfile2);
        DSLSuperUser.add(superUser1, superUser2);
    }

    /**
     * Тестирование отображения интерфейса, если суперпользователь связан с профилем администрирования, у которого
     * нет прав доступа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под суперпользователем ProfileOne (у профиля, связанного с пользователем, нет прав)</li>
     *     <li>Получить сообщение "У Вас нет прав на выполнение данной операции. Для получения доступа необходимо
     *     обратиться к администратору."</li>
     * </ol>
     */
    @Test
    void testInterfaceVisibilityForSuperuserWithoutAccess()
    {
        //Действия и проверки
        GUILogon.login(superUser1);
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование редактирования и удаления профиля администрирования под суперпользователем, профиль
     * администрирования которого позволяет ему это сделать
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01011
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730
     * <br>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <ol>
     * <li>Выдать профилю adminProfile2 права в матрице:
     *     <ul>
     *         <li>Профиль администрирования - все права</li>
     *         <li>Интерфейс администратора - доступ</li>
     *     </ul>
     * <li>Сохранить изменения</li>
     * <li>Создать профиль администрирования adminProfile</li>
     * <li>Выдать профилю adminProfile права в матрице:
     *     <ul>
     *         <li>Интерфейс администратора - доступ</li>
     *     </ul>
     * <li>Связать профиль администрирования adminProfile с superUser2</li>
     * </ol>
     * <br>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под суперпользователем superUser2</li>
     *     <li>Перейти в раздел "Профили администрирования"</li>
     *     <li>Отредактировать профиль adminProfile2, изменив название и описание на произвольные значения</li>
     *     <li>В списке профилей отображается обновлённое название и описание</li>
     *     <li>Удалить профиль adminProfile2</li>
     *     <li>Профиль удаляется, страница обновляется</li>
     *     <li>В интерфейсе отображается ошибка: "У Вас нет прав на выполнение данной операции Для получения
     *      доступа необходимо обратиться к администратору"</li>
     * </ol>
     */
    @Test
    void testCreateEditDeleteAdminProfileBySuperuser()
    {
        //Подготовка
        AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.VIEW,
                AdminProfileAccessMarker.ADMINISTRATION_PROFILES);
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.ALL,
                AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE);
        DSLAdminProfile.edit(adminProfile2);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile2, accessMarkerMatrix);

        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.ALL,
                AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        superUser2.setAdminProfiles(adminProfile2, adminProfile);
        DSLSuperUser.edit(superUser2);

        //Действия и проверки
        GUILogon.login(superUser2);
        GUINavigational.goToAdminProfiles();
        adminProfile2.setTitle(ModelUtils.createTitle());
        adminProfile2.setDescription(ModelUtils.createDescription());
        DSLAdminProfile.edit(adminProfile2);

        GUIAdminProfile.advlist().content().asserts().rowsPresenceByTitle(adminProfile2);
        GUIAdminProfile.advlist().content().asserts()
                .attrValueByColumnNumber(adminProfile2, adminProfile2.getTitle(), 1);

        DSLAdminProfile.delete(adminProfile2);
        Cleaner.afterTest(true, () ->
        {

            DSLAdminProfile.add(adminProfile2);
            // возвращаем ProfileTwo в систему для следующих тестов
            // перезаписываем superUser2 из-за того, что профиль без прав может быть добавлен суперпользователю только
            // при создании
            DSLSuperUser.delete(superUser2);
            DSLSuperUser.add(superUser2);
        });
        tester.refresh();
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
    }

    /**
     * Тестирование просмотра и редактирования облегченного интерфейса настройки под суперпользователем, профиль
     * администрирования которого позволяет ему это сделать
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01011
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Загрузить лицензию adminLite</li>
     *     <li>Выдать профилю ProfileTwo права в матрице:</li>
     *     <li>Облегченный интерфейс настройки - все права</li>
     *     <li>Интерфейс администратора - доступ</li>
     *     <li>Сохранить изменения</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под суперпользователем ProfileTwo</li>
     *     <li>Перейти в раздел "Администрирование -> Облегченный интерфейс настройки"</li>
     *     <li>Произвольно заполнить форму редактирования и сохранить</li>
     *     <li>Форма успешно сохранилась, ошибок при сохранении нет</li>
     *     <li>Настройка облегченного интерфейса отображается, и его можно включить</li>
     * </ol>
     */
    @Test
    void testViewAndEditLightweightInterfaceBySuperuser()
    {
        AdminLiteSettings settingsToRestore = DSLAdminLite.get();
        Cleaner.afterTest(() -> DSLAdminLite.save(settingsToRestore));
        //Подготовка
        AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.ALL,
                AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE);
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.VIEW,
                AdminProfileAccessMarker.ADMIN_LITE_SETTINGS);
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.EDIT,
                AdminProfileAccessMarker.ADMIN_LITE_SETTINGS);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile2, accessMarkerMatrix);
        DSLAdmin.installLicense(DSLAdmin.ADMIN_LITE_LICENSE);
        cleanupAfterTest();

        //Действия и проверки
        GUILogon.login(superUser2);
        GUINavigational.goToAdministration();
        GUIAdminLite.edit();
        GUIAdminLite.selectCatalogs(SystemCatalog.SYSTEM_ICON.getCode());

        GUIForm.applyModalForm();
        GUIError.assertErrorAbsence();
        GUIAdminLite.assertPages(SystemCatalog.SYSTEM_ICON.getTitle());
        GUIAdminLite.assertBtnEnablePresence();
    }

    /**
     * Тестирование доступности списка суперпользователей и технологов под суперпользователем, профиль
     * администрирования которого позволяет ему это сделать
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01011
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730
     * <br>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <ol>
     * <li>Выдать профилю ProfileTwo права в матрице:</li>
     *     <ul>
     *         <li>Список суперпользователей и технологов - доступ</li>
     *         <li>Интерфейс администратора - доступ</li>
     *     </ul>
     * <li>Сохранить изменения</li>
     * </ol>
     * <br>
     * <li>Сохранить изменения</li>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под суперпользователем ProfileTwo</li>
     *     <li>Перейти в раздел "Администрирование"</li>
     *     <li>На странице доступен список "Суперпользователей и технологов"</li>
     * </ol>
     */
    @Test
    void testSuperuserAndTechnologistsListAccessibility()
    {
        //Подготовка
        AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.ALL,
                AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE, AdminProfileAccessMarker.SUPER_USERS);
        adminProfile2.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile2);
        cleanupAfterTest();
        DSLSuperUser.edit(superUser2);

        //Действия и проверки
        GUILogon.login(superUser2);
        GUINavigational.goToAdministration();
        GUIAdmin.assertPresentSuperUserBlock();
    }

    /**
     * Тестирование логики связки суперпользователя с профилем администрирования в зависимости от значения параметра
     * ru.naumen.security.customAdminProfiles.enabled
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742202
     * <br>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выключить функционал профилей администрирования</li>
     * <li>Создать суперпользователя superUser1</li>
     * <li>Включить функционал профилей администрирования</li>
     * <li>Создать суперпользователя superUser2</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Открыть форму редактирования суперпользователя superUser1</li>
     * <li>Проверить, что суперпользователю superUser1 выдан профиль администрирования "Администратор
     * веб-интерфейса"</li>
     * <li>Открыть форму редактирования суперпользователя superUser2</li>
     * <li>Проверить, что суперпользователю superUser2 не выдан профиль администрирования "Администратор
     * веб-интерфейса"</li>
     */
    @Test
    void testAddSuperUserWithDefaultAdminProfile()
    {
        //Подготовка
        String webAdminProfileCode = "webInterface_Administrator";

        DSLConfiguration.setCustomAdminProfilesEnable(false, false);
        SuperUser superUser1 = DAOSuperUser.create();
        DSLSuperUser.add(superUser1);
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        SuperUser superUser2 = DAOSuperUser.create();
        DSLSuperUser.add(superUser2);

        // Выполнение действий и проверок
        GUILogon.asSuper();
        GUINavigational.goToAdministration();
        GUIAdmin.openEditSuperUserForm(superUser1);
        GUISelect.assertDefaultItemPrecense(Any.ADMIN_PROFILES, webAdminProfileCode, true);
        GUIForm.clickCancel();

        GUIAdmin.openEditSuperUserForm(superUser2);
        GUISelect.assertDefaultItemPrecense(Any.ADMIN_PROFILES, webAdminProfileCode, false);
        GUIForm.clickCancel();
    }

    /**
     * Тестирование прав суперпользователя, который обладает профилем администрирования “admin”, в интерфейсе
     * администратора в зависимости от включения функционала комплектов и профилей администрирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742202
     * <br>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под суперпользователем с профилем admin (system)</li>
     * <li>В ИА проверить наличие раздела "Профили администрирования"</li>
     * <li>Проверить, что у пользователя есть доступ в ИО, ошибок не возникает</li>
     * <li>Включить функциональность комплектов на стенде</li>
     * <li>В ИА проверить наличие раздела "Комплекты"</li>
     * <li>Проверить, что у пользователя есть доступ в ИО, ошибок не возникает</li>
     */
    @Test
    void testSystemSuperUserAdminProfilesAndSettingsSetRights()
    {
        //Выполнение действий и проверок
        GUILogon.asSystem();
        GUIAdminNavigationTree.expandItem("systemSettings", "systemSettings:");
        GUIAdminNavigationTree.assertItemPresent("adminProfiles:");
        GUINavigational.goToOperatorUI();

        DSLConfiguration.setSettingSetsEnabled(true, false);

        GUINavigational.goToAdministration();
        GUIAdminNavigationTree.assertItemPresent("sets:");
        GUINavigational.goToOperatorUI();
    }

    /**
     * Возвращаем ProfileTwo в систему для следующих тестов
     */
    static void cleanupAfterTest()
    {
        Cleaner.afterTest(true, () ->
        {
            AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
            accessMarkerMatrix.removeAllPermissions();
            DSLAdminProfile.setAccessMarkerMatrix(adminProfile2, accessMarkerMatrix);
        });
    }
}
