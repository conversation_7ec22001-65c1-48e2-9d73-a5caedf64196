package ru.naumen.selenium.cases.operator.embeddedapplication.events;

import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.OBJECTCARD;
import static ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType.Sync;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.Tool.AppliedToType;
import ru.naumen.selenium.casesutil.model.content.Tool.PresentationType;
import ru.naumen.selenium.casesutil.model.content.UserTool;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование возможности подписки на событие обновления контента через JS API (jsApi.events.onRefreshContent) на
 * карточках и на формах
 *
 * <AUTHOR>
 * @since 24.05.20
 */
class JsApiRefreshContentEventTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static final String ON_REFRESH_CONTENT_TEMPLATE = """
            var count = 0
            jsApi.events.onRefreshContent(event => {
                var source = event.source
                var expectedSource = '%s'
                document.body.innerText = (source == expectedSource)
                    ? ++count
                    : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался '" + expectedSource + "'"
            })
            """;
    private static final String ON_REFRESH_CONTENT = String.format(ON_REFRESH_CONTENT_TEMPLATE, "content");
    private static final String ON_REFRESH_CONTENT_BY_EA = String.format(ON_REFRESH_CONTENT_TEMPLATE, "eventAction");

    private static MetaClass userClass, userCase;
    private static ScriptInfo reloadScript;

    private Bo userBo;

    /**
     * <ol>
     *   <b>Общая подготовка.</b>
     *   <li>Создать пользовательский класс <code>userClass</code> с назначением ответственного и его тип
     *   <code>userCase</code></li>
     *   <li>Создать скрипт <code>reloadScript</code>:
     *   <pre>
     *   --------------------------------------------------
     *     result.reload(true)
     *   --------------------------------------------------
     *   </pre></li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWith().responsible().workFlow().create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        reloadScript = DAOScriptInfo.createNewScriptInfo("result.reload(true)");
        DSLScriptInfo.addScript(reloadScript);
    }

    /**
     * <ol>
     *   <b>Общая подготовка.</b>
     *   <li>{@link #prepareFixture() Общая подготовка для всех тестов}</li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     * </ol>
     */
    @BeforeEach
    public void setUp()
    {
        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано из ПДПС с
     * помощью <code>result.reload(true)</code> на карточке объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая подготовка}</li>
     *   <li>Создать синхронное действие по событию <code>userEvent</code>, со скриптом <code>reloadScript</code></li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний, инициированных из ПДПС, при срабатывании слушателя на событие обновления контента ВП:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'eventAction')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'eventAction'"
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <li>Вывести кнопку вызова ДПС <code>userEvent</code> на карточку для типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Нажать на кнопку пользовательского действия по событию <code>userEvent</code></li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     *   <li>Нажать на кнопку пользовательского действия по событию <code>userEvent</code></li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "2"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventByEventActionOnStandaloneCard()
    {
        // Подготовка
        EventAction userEvent =
                DAOEventAction.createEventScript(EventType.userEvent, reloadScript, true, Sync, userCase);
        DSLEventAction.add(userEvent);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                ON_REFRESH_CONTENT_BY_EA);
        DSLEmbeddedApplication.add(application);

        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        ContentForm card = DAOContentCard.createWindow(userCase);
        String buttonTitle = ModelUtils.createTitle();
        DSLContent.addUserTool(card, buttonTitle, userEvent, AppliedToType.CURRENT_OBJECT);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(userCase, OBJECTCARD));

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIButtonBar.clickButtonByText(buttonTitle);
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        GUIButtonBar.clickButtonByText(buttonTitle);
        Assertions.assertEquals("2", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы Смены статуса, ранее вызванной из встроенного приложения, выведенного на
     * карточку объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний инициированных сохранением формы, при срабатывании слушателя на событие обновления контента ВП,
     *   параллельно выполняя открытие формы Смены статуса:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'content')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'content'"
     *     jsApi.forms.changeState('$uuid', ['closed'])
     *   --------------------------------------------------
     *   Где:
     *     1) $uuid - идентификатор объекта <code>userBo</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventWhenCalledChangeStateFormIsSavedOnStandaloneCard()
    {
        // Подготовка
        BoStatus closed = DAOBoStatus.createClosed(userCase);
        String jsContent = ON_REFRESH_CONTENT
                           + String.format("jsApi.forms.changeState('%s', ['%s'])", userBo.getUuid(), closed.getCode());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.applyModalForm();
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы Смены ответственного, ранее вызванной из встроенного приложения, выведенного на
     * карточку объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний, инициированных сохранением формы, при срабатывании слушателя на событие обновления контента ВП,
     *   параллельно выполняя открытие формы Смены ответственного:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'content')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'content'"
     *     jsApi.forms.changeResponsible('$uuid')
     *   --------------------------------------------------
     *   Где:
     *     1) $uuid - идентификатор объекта <code>userBo</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Установить ответственного и сохранить форму</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventWhenCalledChangeResponsibleFormIsSavedOnStandaloneCard()
    {
        // Подготовка
        String jsContent = ON_REFRESH_CONTENT + String.format("jsApi.forms.changeResponsible('%s')", userBo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIForm.assertDialogCaption("Изменение ответственного");
        GUISc.selectResponsible(SharedFixture.employee(), SharedFixture.team());
        GUIForm.applyModalForm();
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением формы ПДПС, ранее вызванного из встроенного приложения, выведенного на карточку объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Создать синхронное действие по событию <code>userEvent</code>, со скриптом <code>reloadScript</code></li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний инициированных из ПДПС, при срабатывании слушателя на событие обновления контента ВП,
     *   параллельно вызывая выполнение ПДПС:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'eventAction')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'eventAction'"
     *
     *     jsApi.eventActions.getEventActionExecutor('$actionUuid')
     *         .setSubject('$subjectUuid')
     *         .execute()
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <li>Вывести кнопку вызова ДПС <code>userEvent</code> на карточку для типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Назначить ответственного за объект <code>userBo</code> через форму смены ответственного</li>
     *   <li>Проверить, что открылась модальная с параметрами ПДПС <code>userEvent</code><</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventWhenCalledEventActionFormIsSavedOnStandaloneCard()
    {
        // Подготовка
        EventAction userEvent =
                DAOEventAction.createEventScript(EventType.userEvent, reloadScript, true, Sync, userCase);
        DSLEventAction.add(userEvent);

        FormParameter stringParam = DAOFormParameter.createString();
        DSLFormParameter.saveOnEventAction(userEvent, stringParam);

        String jsContent = ON_REFRESH_CONTENT_BY_EA + String.format("""
                jsApi.eventActions.getEventActionExecutor('%s')
                    .setSubject('%s')
                    .execute()
                """, userEvent.getUuid(), userBo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        ContentForm card = DAOContentCard.createWindow(userCase);
        String buttonTitle = ModelUtils.createTitle();
        DSLContent.addUserTool(card, buttonTitle, userEvent);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(userCase, OBJECTCARD));

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());
        GUIForm.assertDialogCaption(userEvent.getTitle());
        GUIForm.applyModalForm();
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование отсутствия вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы, ранее вызванной не из текущего встроенного приложения, выведенного на карточку
     * объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний, при срабатывании слушателя на событие обновления контента ВП:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(_ => document.body.innerText = ++count)
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на карточку объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Установить ответственного и сохранить форму</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     * </ol>
     */
    @Test
    void testNoRefreshContentEventWhenAnyModalFormIsSavedOnStandaloneCard()
    {
        // Подготовка
        String jsContent = """
                var count = 0
                jsApi.events.onRefreshContent(_ => document.body.innerText = ++count)
                """;
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIButtonBar.changeResponsible();
        GUISc.selectResponsible(SharedFixture.employee(), SharedFixture.team());
        GUIForm.applyModalForm();
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы Смены статуса, ранее вызванной из встроенного приложения, выведенного на
     * форму добавления объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний инициированных сохранением формы, при срабатывании слушателя на событие обновления контента ВП,
     *   параллельно выполняя открытие формы Смены статуса:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'content')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'content'"
     *     jsApi.forms.changeState('$uuid', ['closed'])
     *   --------------------------------------------------
     *   Где:
     *     1) $uuid - идентификатор объекта <code>userBo</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на форму добавления объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на форма добавления объектов типа <code>userCase</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены статуса</li>
     *   <li>Сохранить форму</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventWhenCalledChangeStateFormIsSavedOnStandaloneForm()
    {
        // Подготовка
        BoStatus closed = DAOBoStatus.createClosed(userCase);
        String jsContent = ON_REFRESH_CONTENT
                           + String.format("jsApi.forms.changeState('%s', ['%s'])", userBo.getUuid(), closed.getCode());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentAddForm.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass, userCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIForm.assertDialogCaption("Изменение статуса");
        GUIForm.applyModalForm();
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование корректного вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы Смены ответственного, ранее вызванной из встроенного приложения, выведенного на
     * форму добавления объекта.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний инициированных сохранением формы, при срабатывании слушателя на событие обновления контента ВП,
     *   параллельно выполняя открытие формы Смены ответственного:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(event => {
     *     var source = event.source
     *     document.body.innerText = (source == 'content')
     *         ? ++count
     *         : "Некорректное событие! Получено событие с источником - '" + source + "', ожидался 'content'"
     *     jsApi.forms.changeResponsible('$uuid')
     *   --------------------------------------------------
     *   Где:
     *     1) $uuid - идентификатор объекта <code>userBo</code>.
     *   </pre></li>
     *   <li>Добавить контент с ВП <code>application</code> на форму добавления объектов типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на форма добавления объектов типа <code>userCase</code></li>
     *   <li>Проверить, что встроенное приложение загрузилось</li>
     *   <li>Проверить, что в контенте с ВП отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Установить ответственного и сохранить форму</li>
     *   <li>Проверить, что контент с ВП обновился и содержит текст "1"</li>
     * </ol>
     */
    @Test
    void testRefreshContentEventWhenCalledChangeResponsibleFormIsSavedOnStandaloneForm()
    {
        // Подготовка
        String jsContent = ON_REFRESH_CONTENT + String.format("jsApi.forms.changeResponsible('%s')", userBo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentAddForm.createEmbeddedApplication(userCase, application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass, userCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIForm.assertDialogCaption("Изменение ответственного");
        GUISc.selectResponsible(SharedFixture.employee(), SharedFixture.team());
        GUIForm.applyModalForm();
        Assertions.assertEquals("1", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование отсутствия вызова слушателя на событие обновления контента, когда событие инициировано
     * сохранением модальной формы, ранее вызванной не из текущего встроенного приложения, выведенного на
     * родительской форме добавления.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211813847
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #setUp() Общая подготовка}</li>
     *   <li>Добавить встроенное приложение <code>application</code>, которое будет выводить количество своих
     *   срабатываний, при срабатывании слушателя на событие обновления контента ВП:
     *   <pre>
     *   --------------------------------------------------
     *     var count = 0
     *     jsApi.events.onRefreshContent(_ => document.body.innerText = ++count)
     *   --------------------------------------------------
     *   </pre></li>
     *   <li>Добавить встроенное приложение <code>application2</code>, которое будет вызывать смену ответственного
     *   для объекта <code>userBo</code>:
     *   <pre>
     *   --------------------------------------------------
     *     jsApi.forms.changeResponsible('$uuid')
     *   --------------------------------------------------
     *   Где:
     *     1) $uuid - идентификатор объекта <code>userBo</code>.
     *   </pre></li>
     *   <li>Добавить контенты с ВП <code>application</code> и <code>application2</code> на форму добавления объектов
     *   типа <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Зайти под сотрудником</li>
     *   <li>Перейти на форма добавления объектов типа <code>userCase</code></li>
     *   <li>Проверить, что встроенные приложения загрузились</li>
     *   <li>Проверить, что в контенте с ВП <code>application</code> отображается пустая строка</li>
     *   <li>Проверить, что открылась модальная форма смены ответственного</li>
     *   <li>Установить ответственного и сохранить форму</li>
     *   <li>Проверить, что в контенте с ВП <code>application</code> отображается пустая строка</li>
     * </ol>
     */
    @Test
    void testNoRefreshContentEventWhenAnyModalFormIsSavedOnStandaloneForm()
    {
        // Подготовка
        String jsContent = """
                var count = 0
                jsApi.events.onRefreshContent(_ => document.body.innerText = ++count)
                """;
        String jsContent2 = String.format("jsApi.forms.changeResponsible('%s')", userBo.getUuid());
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent2);
        DSLEmbeddedApplication.add(application, application2);
        ContentForm content = DAOContentAddForm.createEmbeddedApplication(userCase, application);
        ContentForm content2 = DAOContentAddForm.createEmbeddedApplication(userCase, application2);
        DSLContent.add(content, content2);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass, userCase);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content2);
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());

        GUIForm.assertDialogCaption("Изменение ответственного");
        GUISc.selectResponsible(SharedFixture.employee(), SharedFixture.team());
        GUIForm.applyModalForm();
        Assertions.assertTrue(GUIEmbeddedApplication.getEmbeddedApplicationContent(content).isEmpty());
    }

    /**
     * Тестирования выполнения обработчиков события refreshContent после вызова пользовательского ДПС по кнопке на
     * панели действий списка объектов <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$233757768 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #setUp() Общая подготовка для каждого теста}</li>
     * <li>Создать действие по событию eventAction:
     *   <ul>
     *     <li>Объекты — userClass;</li>
     *     <li>Событие — [Пользовательское событие];</li>
     *     <li>Действие — Скрипт;</li>
     *     <li>Выполнять синхронно — да;</li>
     *     <li>Скрипт:
     *     <pre>result.reload(true);</pre></li>
     *   </ul>
     * </li>
     * <li>Включить eventAction</li>
     * <li>Создать исполняемое на стороне клиента встроенное приложение application с JS-кодом:
     * <pre>
     * jsApi.events.onRefreshContent((event) => {
     *     if (event.source === 'eventAction') {
     *         document.body.innerText += 'refresh applied!';
     *     }
     * });
     * </pre></li>
     * <li>На карточку объекта класса userClass вывести контент appContent типа «Встроенное приложение»
     * (Приложение — application)</li>
     * <li>На карточку объекта класса userClass вывести список объектов objectList (Класс объектов — userClass,
     * Представление — Сложный список)</li>
     * <li>На панель действий списка objectList вывести пользовательскую кнопку tool1 (Действие — eventAction,
     * Применяется к — текущему объекту)</li>
     * <li>На панель действий списка objectList вывести пользовательскую кнопку tool2 (Действие — eventAction,
     * Применяется к — объектам списка)</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку tool1 на панели действий списка objectList</li>
     * <li>Нажать на кнопку tool2 на панели действий списка objectList</li>
     * <br>
     * <b>Проверка</b>
     * <li>В контенте appContent появился текст:
     * <pre>refresh applied!refresh applied!</pre></li>
     * </ol>
     */
    @Test
    void testRefreshContentEventByUserEventActionInList()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, reloadScript, true, TxType.Sync,
                userClass);
        DSLEventAction.add(eventAction);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                """
                        jsApi.events.onRefreshContent((event) => {
                            if (event.source === 'eventAction') {
                                document.body.innerText += 'refresh applied!';
                            }
                        });
                        """);
        DSLEmbeddedApplication.add(application);

        ContentForm appContent = DAOContentCard.createEmbeddedApplication(userClass, application);
        ContentForm objectList = DAOContentCard.createObjectAdvList(userClass, userClass);
        UserTool tool1 = DAOTool.createUserTool(AppliedToType.CURRENT_OBJECT, PresentationType.DEFAULT,
                eventAction.getUserEventUuid());
        UserTool tool2 = DAOTool.createUserTool(AppliedToType.LIST_OBJECTS, PresentationType.DEFAULT,
                eventAction.getUserEventUuid());
        objectList.setToolPanel(DAOToolPanel.createContentToolPanel(tool1, tool2));
        DSLContent.add(appContent, objectList);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        objectList.advlist().toolPanel().clickUserEventButton(tool1.getTitle());
        objectList.advlist().toolPanel().clickUserEventButton(tool2.getTitle());

        // Проверка
        Assertions.assertEquals("refresh applied!refresh applied!",
                GUIEmbeddedApplication.getEmbeddedApplicationContent(appContent));
    }
}
