package ru.naumen.selenium.cases.admin.classes;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIBoCaption;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISearchSettings;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.DSLTransition;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUITransition;
import ru.naumen.selenium.casesutil.metaclass.GUIWorkflow;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.Transition;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование прав на карточке метакласса в интерфейсе технолога.
 * <AUTHOR>
 * @since Oct 07, 2024
 */
public class MetaClassAdminPermissionTest extends AbstractTestCase
{
    private static SuperUser superUser;
    private static SettingsSet settingsSet;
    private static MetaClass metaClass;
    private static MetaClass metaCase;
    private static Attribute attribute;

    /**
     * Общая подготовка.
     * <ol>
     * <li>Включить использование профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить использование комплектов на стенде</li>
     * <li>Создать комплект settingsSet</li>
     * <li>Создать суперпользователя superUser</li>
     * <li>Создать пользовательский класс metaClass (Комплект — settingsSet) с жизненным циклом</li>
     * <li>Создать тип metaCase класса metaClass</li>
     * <li>В классе metaClass создать строковый атрибут attribute
     * (Комплект — settingsSet)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        settingsSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet);

        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        DSLAdminProfile.addAllRightsToAdminProfile(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(List.of(adminProfile.getCode()));
        DSLSuperUser.add(superUser);

        metaClass = DAOUserClass.createWithWF();
        metaCase = DAOUserCase.create(metaClass);
        metaClass.setSettingsSet(settingsSet);
        DSLMetainfo.add(metaClass, metaCase);

        attribute = DAOAttribute.createString(metaClass);
        attribute.setSettingsSet(settingsSet);
        DSLAttribute.add(attribute);
    }

    /**
     * Тестирование видимости элементов интерфейса на карточках свойств метакласса, если он размечен комплектом <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке класса отсутствуют кнопки «Редактировать», «Удалить», «Копировать», «Поместить в архив»</li>
     * <li>На панели списка атрибутов отсутствует кнопка «Сбросить настройки системных атрибутов»</li>
     * <li>На вкладке «Карточка объекта» отсутствует возможность изменить заголовок карточки и войти в режим
     * управления шаблонами</li>
     * <li>На вкладках «Форма добавления» и «Форма редактирования» отсутствует кнопка переименования формы</li>
     * <li>На вкладках «Статусы и возможные переходы» и «Управление параметрами в статусах и переходах»
     * жизненного цикла отсутствуют кнопки для сброса настроек</li>
     * </ol>
     */
    @Test
    public void testMetaClassPropertiesPermissions()
    {
        // Действия
        GUILogon.login(superUser);
        GUIMetaClass.goToCard(metaClass);

        // Проверки
        GUIMetaClass.assertThatCard(metaClass);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_EDIT);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REMOVE);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_COPY);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_ARCHIVE);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_SYSTEM_REFRESH);

        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.OBJECTCARD);
        GUIBoCaption.assertEditCaptionPresent(false);
        GUIContent.assertTemplateModeAvailable(false);

        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.assertRenameFormButtonPresente(false);

        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.EDITFORM);
        GUIContent.assertRenameFormButtonPresente(false);

        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.LIFECYCLE);
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);

        GUIWorkflow.goToStatesAttrsSettingsTab();
        GUIMetaClass.assertButtonAbsent(GUIMetaClass.BTN_REFRESH);
    }

    /**
     * Тестирование видимости элементов интерфейса в списке атрибутов метакласса,
     * если один из атрибутов размечен комплектом <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке атрибута attribute в списке отсутствуют иконки редактирования и удаления</li>
     * <li>В окне отображения информации об атрибуте attribute отсутствует кнопка редактирования</li>
     * </ol>
     */
    @Test
    public void testAttributePermissions()
    {
        // Действия
        GUILogon.login(superUser);
        GUIMetaClass.goToCard(metaClass);

        // Проверки
        GUIAttribute.assertPresent(attribute);
        GUIAttribute.assertEditButtonPresent(attribute, false);
        GUIAttribute.assertDeleteButtonPresent(attribute, false);
        GUIAttribute.clickShowAttributeInfo(attribute);
        GUIAttribute.assertEditButtonPresentOnInfoDialog(false);
    }

    /**
     * Тестирование видимости элементов интерфейса на вкладке «Группы атрибутов» карточки класса, если одна из групп
     * размечена комплектом<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе metaClass создать группу атрибутов attrGroup (Комплект — settingsSet)
     * и добавить в нее атрибуты «Название» и attribute</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass, на вкладку «Группы атрибутов»</li>
     * <li>Проверить, что на карточке группы attrGroup отсутствует кнопка «Удалить»</li>
     * <li>Проверить, что на карточке группы attrGroup отсутствует кнопка «Редактировать»</li>
     * <li>Перейти на карточку типа metaCase, на вкладку «Группы атрибутов»</li>
     * <li>Проверить, что на карточке группы attrGroup отсутствует кнопка «Сбросить настройки»</li>
     * <li>Проверить, что на карточке группы attrGroup отсутствует кнопка «Редактировать»</li>
     * </ol>
     */
    @Test
    public void testAttributeGroupPermissions()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(metaClass);
        attrGroup.setSettingsSet(settingsSet);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(metaClass), attribute);

        // Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIGroupAttr.assertPresent(attrGroup);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup, GUIButtonBar.BTN_DEL);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup, GUIButtonBar.BTN_EDIT);

        GUIMetaClass.goToTab(metaCase, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIGroupAttr.assertPresent(attrGroup);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup, GUIButtonBar.BTN_REFRESH);
        GUIGroupAttr.assertPresentGroupButton(false, attrGroup, GUIButtonBar.BTN_EDIT);
    }

    /**
     * Тестирование видимости элементов интерфейса на карточках статуса и перехода <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе metaClass создать статус status (Комплект — settingsSet)</li>
     * <li>В классе metaClass создать именованный переход transition из начального статуса в status
     * (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass, на вкладку «Жизненный цикл»</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке статуса status отсутствуют иконки редактирования и удаления</li>
     * <li>На карточке перехода transition отсутствует кнопка «Редактировать»</li>
     * </ol>
     */
    @Test
    public void testStatusAndTransitionPermissions()
    {
        // Подготовка
        BoStatus status = DAOBoStatus.createUserStatus(metaClass);
        status.setSettingsSet(settingsSet);
        DSLBoStatus.add(status);
        BoStatus registered = DAOBoStatus.createRegistered(metaClass);
        DSLBoStatus.setTransitions(registered, status);
        Transition transition = DAOTransition.createTransition(metaClass, registered,
                status, ModelUtils.createTitle(), false, false);
        transition.setSettingsSet(settingsSet);
        DSLTransition.editTransitions(transition);

        // Действия
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.LIFECYCLE);

        // Проверки
        GUIBoStatus.assertEditIconAbsent(status);
        GUIBoStatus.assertDeleteIconAbsent(status);
        GUIBoStatus.goToTransitionCard(registered, status);
        GUITransition.assertSettingEditable(false);
    }

    /**
     * Тестирование видимости элементов редактирования на странице настройки поиска в классе, если сам класс и один из
     * его атрибутов размечены комплектом <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass, на вкладку «Поиск»</li>
     * <br>
     * <b>Проверки</b>
     * <li>В строке атрибута attribute в списке отсутствуют иконка редактирования</li>
     * <li>В строке критерия релевантности поиска отсутствуют иконка редактирования</li>
     * </ol>
     */
    @Test
    public void testSearchSettingsPermissions()
    {
        // Действия
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.SEARCHSETTINGS);

        // Проверки
        GUISearchSettings.assertAttributePresent(attribute);
        GUISearchSettings.assertAttributeSettingEditable(attribute, false);
        GUISearchSettings.assertMSCSettingEditable(false);
    }

    /**
     * Тестирование видимости элементов настройки контента на карточке объекта, если он размечен комплектом <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточку класса metaClass вывести контент fileList типа «Список файлов» (Комплект — settingsSet)</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса metaClass, на вкладку «Карточка объекта»</li>
     * <br>
     * <b>Проверки</b>
     * <li>Для контента fileList недоступна иконка редактирования настроек</li>
     * <li>Для контента fileList недоступна иконка удаления</li>
     * </ol>
     */
    @Test
    public void testContentPermissions()
    {
        // Подготовка
        ContentForm fileList = DAOContentCard.createFileList(metaClass);
        fileList.setSettingsSet(settingsSet);
        DSLContent.add(fileList);

        // Действия
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.OBJECTCARD);

        // Проверки
        GUIContent.assertPresent(fileList);
        GUIContent.assertButtonAbsence(fileList.getXpathId(), GUIContent.EDIT_TOOL);
        GUIContent.assertButtonAbsence(fileList.getXpathId(), GUIContent.DELETE_TOOL);
    }
}
