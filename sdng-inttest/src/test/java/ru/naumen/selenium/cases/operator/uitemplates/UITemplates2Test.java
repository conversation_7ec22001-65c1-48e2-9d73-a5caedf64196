package ru.naumen.selenium.cases.operator.uitemplates;

import java.io.File;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.admin.homepage.DSLHomePage;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.contenttemplate.GUIContentTemplate;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOHomePage;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.HomePage;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.template.UITemplateUtils;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование шаблонов карточек.
 * <AUTHOR>
 * @since Nov 28, 2022
 */
public class UITemplates2Test extends AbstractTestCase
{
    private static MetaClass employeeCase, employeeClass;
    private static String templateCode, templateTitle, tab1Title;
    private static ContentTab tab0, tab1, tab2;
    private static Bo employee;

    /**
     * Общая подготовка
     * <ol>
     * <br>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>На карточку сотрудника типа employeeCase добавить вкладки tab1 и tab2, разместить на каждой из них по
     * одному контенту</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template: оставить включенными только
     * корневые вкладки tab1 и tab2, у tab1 изменить название.</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        employeeClass = DAOEmployeeCase.createClass();
        employeeCase = DAOEmployeeCase.create(employeeClass);
        DSLMetaClass.add(employeeCase);

        tab0 = DSLContent.getFirstTab(employeeCase.getFqn());
        tab1 = DAOContentTab.createTab(employeeCase);
        tab2 = DAOContentTab.createTab(employeeCase);
        DSLContent.addTab(tab1, tab2);
        ContentForm objectAdvList1 = DAOContentCard.createObjectAdvList(employeeCase.getFqn(), employeeClass,
                employeeCase);
        ContentForm objectAdvList2 = DAOContentCard.createObjectAdvList(employeeCase.getFqn(), employeeClass,
                employeeCase);
        DSLContent.add(tab1, objectAdvList1);
        DSLContent.add(tab2, objectAdvList2);

        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContent.clickManageTabs();
        GUITab.clickEdit(tab1);
        tab1Title = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.TITLE_VALUE, tab1Title);
        GUIForm.applyLastModalForm();

        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab2);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        templateTitle = ModelUtils.createTitle();
        templateCode = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(templateTitle, templateCode, false, true);
        Cleaner.afterAllTest(true, () -> UITemplateUtils.removeTemplatesByClass(employeeClass));
    }

    /**
     * Тестирование добавления шаблона карточки в элемент левого меню, если у элемента меню ранее не было выбрано
     * шаблона.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab2, шаблон карточки - [не указано]).</li>
     * <br>
     * <b>Выполнение действий:</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Открыть левое навигационное меню и выбрать пункт item в левом меню</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Отображается карточка employeeCase, открыта вкладка tab2</li>
     * <br>
     * <b>Выполнение действий:</b>
     * <li>Зайти в ИА под суперпользователем</li>
     * <li>Отредактировать элемент item левого навигационного меню: выбрать шаблон карточки - template</li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Заново выбрать пункт item в левом меню</li>
     * <br>
     * <b>Проверка:</b>
     * <li> При добавлении шаблона изменился вид карточки: на карточке стали отображаться только вкладки tab1 и tab2,
     * у tab1 изменено название, выбранная вкладка - tab2.</li>
     * </ol>
     */
    @Test
    public void testAddAndChangeObjectCardTemplate()
    {
        // Подготовка
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, tab2);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(item);

        // Проверка
        GUIMetaClass.assertThatCard(employeeCase);
        GUITab.assertTabSelected(tab2);

        //Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUISelect.selectById(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX, templateCode,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE);
        GUIForm.applyForm();
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(item);

        //Проверка
        GUITab.assertRootTabs(tab1Title, tab2.getTitle());
        GUITab.assertTabSelected(tab2);
    }

    /**
     * Тестирование изменения шаблона карточки в элементе левого меню.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template2: оставить включенной только
     * вкладку tab1</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab1, шаблон карточки - template)</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Войти в систему под сотрудником;</li>
     * <li>Открыть левое навигационное меню и выбрать пункт item в левом меню; </li>
     * <br>
     * <b>Проверки:</b>
     * <li>На карточке отображаются вкладки tab1 и tab2, у tab1 изменено название, выбранная вкладка - tab1;</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Зайти в систему под суперпользователем;</li>
     * <li>Отредактировать элемент item левого навигационного меню: изменить шаблон карточки на template2; </li>
     * <li>Войти в систему как сотрудник;</li>
     * <li>Открыть левое навигационное меню и выбрать пункт item2 в левом меню.</li>
     * <b>Проверки:</b>
     * <li>При изменении шаблона изменился вид карточки: на карточке стала отображаться только вкладка tab1.</li>
     * </ol>
     */
    @Test
    public void testChangeObjectCardTemplateInLeftMenuItem()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        GUIContent.clickManageTabs();
        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        String template2Title = ModelUtils.createTitle();
        String template2Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template2Title, template2Code, false, true);
        GUIContent.clickDisableTemplateMode();

        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, templateCode, tab1);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(item);

        // Проверка
        GUITab.assertRootTabs(tab1Title, tab2.getTitle());
        GUITab.assertTabSelected(tab1);

        // Выполнение действий
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUISelect.selectById(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX, template2Code,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE);
        GUIForm.applyForm();

        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(item);

        // Проверка
        Assert.assertEquals(1, tester.findElements(GUITab.X_ROOT_TAB).size());
        GUITester.assertFindInvisibleElement(String.format(GUIXpath.Any.ID_PATTERN, tab1.getXpathId()));
    }

    /**
     * Тестирование невозможности удалить шаблон карточки, используемый в элементе левого навигационного меню типа
     * "ссылка на карточку".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab2, шаблон карточки - template), включить его.</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>В типе employeeCase в режиме шаблонов удалить шаблон карточки template</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Появляется ошибка "Шаблон 'template' не может быть удалён. Он используется в настройках элементов меню
     * item3'</li>
     * </ol>
     */
    @Test
    public void testImpossibleToDeleteUsedTemplate()
    {
        // Подготовка
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, templateCode, tab2);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(templateCode);
        GUIForm.openDeleteForm();
        GUIForm.clickYes();

        // Проверка
        GUIForm.assertErrorMessageOnForm(String.format("Шаблон '%s' (%s) не может быть удалён. Он используется в "
                                                       + "настройках элементов меню: '%s'.", templateTitle,
                templateCode, item.getTitle()));
    }

    /**
     * Тестирование загрузки частичной метаинформации без шаблона карточки, используемого в элементе левого
     * навигационного меню.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>Выгрузить частичную метаинформацию карточки employeeCase</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template2: оставить включенной только
     * вкладку tab1</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab1, шаблон карточки - template2), включить его</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Загрузить метаинформацию</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Загрузка прошла без ошибок, шаблон не удалился</li>
     * </ol>
     */
    @Test
    public void testImportPartMetaInfoWithoutUsedTemplate()
    {
        // Подготовка
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectObjectCard(exportModel, employeeCase);
        File metaInfo = DSLMetainfoTransfer.exportMetainfo(exportModel);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        GUIContent.clickManageTabs();
        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        String template2Title = ModelUtils.createTitle();
        String template2Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template2Title, template2Code, false, true);
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, template2Code, tab1);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        DSLMetainfoTransfer.importMetainfo(metaInfo);
        tester.refresh();

        // Проверка
        GUIError.assertErrorAbsence();
        GUIContent.clickEnableTemplateMode();
        GUISelect.assertSelected(GUIContentTemplate.TEMPLATE_SELECT_LIST_XPATH, template2Title);
    }

    /**
     * Тестирование удаления элемента левого меню, ссылающегося на шаблон карточки, при загрузке полной
     * метаинформации без шаблона и элемента левого меню.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>Выгрузить полную метаинформацию</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template2: оставить включенной только
     * вкладку tab1</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab1, шаблон карточки - template2), включить его</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Загрузить метаинформацию</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Загрузка прошла без ошибок, шаблон карточки сохранился, элемент левого меню удалился</li>
     * </ol>
     */
    @Test
    public void testDeleteLeftMenuItemAfterMetaInfoImport()
    {
        // Подготовка
        File metaInfo = DSLMetainfoTransfer.exportMetainfo();

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        GUIContent.clickManageTabs();
        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        String template2Title = ModelUtils.createTitle();
        String template2Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template2Title, template2Code, false, true);

        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, template2Code, tab1);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        DSLMetainfoTransfer.importMetainfo(metaInfo);
        tester.refresh();

        // Проверка
        GUIError.assertErrorAbsence();
        GUIContent.clickEnableTemplateMode();
        GUISelect.assertSelected(GUIContentTemplate.TEMPLATE_SELECT_LIST_XPATH, template2Title);

        GUINavSettings.goToCard();
        GUIMenuItem.assertLeftMenuItemAbsent(item);
    }

    /**
     * Тестирование отображения подходящих шаблонов в выпадающем списке на форме добавления и редактирования
     * элемента левого меню, ссылающегося на карточку.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template2: оставить включенной только
     * вкладку tab1</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab2)</li>
     * <br>
     * <b>Проверки:</b>
     * <li>В выпадающем меню шаблонов карточки отображается шаблон template и не отображается шаблон template2</li>
     * <b>Выполнение действия:</b>
     * <li>Выбрать шаблон template, сохранить элемент item</li>
     * <li>На форме редактирования item нажать на выпадающее меню шаблонов карточки</li>
     * <br>
     * <b>Проверки:</b>
     * <li>В выпадающем меню шаблонов карточки не отображается шаблон template2</li>
     * </ol>
     */
    @Test
    public void testDisplayOnlySuitableTemplates()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        GUIContent.clickManageTabs();
        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        String template2Title = ModelUtils.createTitle();
        String template2Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template2Title, template2Code, false, true);

        // Выполненией действий
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, template2Code, tab2);
        DSLLeftMenuItem.add(item);

        // Проверка
        GUINavSettings.goToCard();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUISelect.assertDisplayed(String.format(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE), templateCode);
        GUISelect.assertNotDisplayed(String.format(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE), template2Code);

        // Выполнение действий
        GUISelect.selectById(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX, templateCode,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE);
        GUIForm.applyForm();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUISelect.assertNotDisplayed(String.format(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE), template2Code);
    }

    /**
     * Тестирование сбрасывания шаблона карточки при изменении вкладки карточки в элементе левого меню на вкладку,
     * которой нет в изначально используемом шаблоне.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В типе employeeCase в режиме шаблонов создать шаблон карточки template2: оставить включенной только
     * вкладку tab1</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab1, шаблон карточки - template2), включить его.</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Отредактировать в левом навигационном меню элемент item: изменить вкладку карточки на employeeCase/tab2.</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Шаблон карточки в элементе сбросился, изменился на [не указано]</li>
     * </ol>
     */
    @Test
    public void testResetTemplateIfSwitchedToDisabledTab()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        GUIContent.clickManageTabs();
        GUITab.clickSwitchAllTabs();
        GUITab.clickSwitchTab(tab1);
        GUITab.clickSwitchTab(tab0);
        GUIForm.closeDialog();

        String template2Title = ModelUtils.createTitle();
        String template2Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template2Title, template2Code, false, true);

        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, template2Code, tab1);
        DSLLeftMenuItem.add(item);

        // Выполнение действий
        GUINavSettings.goToCard();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUIMenuItem.selectCardTab(tab2);
        // Проверка
        GUISelect.assertSelected(String.format(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX,
                GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE), "[не указано]");
    }

    /**
     * Тестирование попытки удаления вкладки карточки, которая указана в элементе левого навигационного меню с
     * применением шаблона.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки-
     * employeeCase/tab2, шаблон карточки - template), включить его.</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>В карточке employeeCase удалить вкладку tab2.</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Появляется ошибка "Вкладка employeeCase/tab2 не может быть удалена. Она или вложенные в неё вкладки
     * используются в настройках левого меню: 'item'".</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteObjectCardTabThatIsUsed()
    {
        // Подготовка
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeCase, templateCode, tab2);
        DSLLeftMenuItem.add(item);

        // Выполненией действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUITab.deleteCardTabAssertError(tab2,
                "Вкладка %s/%s не может быть удалена. Она или вложенные в неё вкладки "
                + "используются в настройках левого меню: '%s'", employeeCase.getTitle(), tab2.getTitle(),
                item.getTitle());
    }

    /**
     * Тестирование попытки восстановления наследования типа, шаблон карточки которого используется в элементе левого
     * навигационного меню.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>Создать подтип сотрудника employeeType</li>
     * <li>Разорвать наследование настроек карточки подтипа employeeType</li>
     * <li>В типе employeeType в режиме шаблонов создать шаблон карточки templateType с любыми настройками</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки- employeeType,
     * шаблон карточки - templateType), включить его</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>В типе employeeType восстановить наследование настроек карточки</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Появляется ошибка "Настройки карточки не могут быть сброшены по причинам: 1. Шаблон 'templateType '
     * (templateType ) не может быть удалён. Он используется в настройках элементов меню: 'item'."</li>
     * </ol>
     */
    @Test
    public void testTryResetSettingsWithExistingTemplateLink()
    {
        // Подготовка
        MetaClass employeeType = DAOEmployeeCase.create(employeeCase);
        DSLMetaClass.add(employeeType);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeType, MetaclassCardTab.OBJECTCARD);
        GUIContent.enableEditProperties();

        GUIMetaClass.goToTab(employeeType, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEnableTemplateMode();
        GUIContentTemplate.selectTemplate(GUISelect.EMPTY_SELECTION_ITEM);

        String template3Title = ModelUtils.createTitle();
        String template3Code = ModelUtils.createCode();
        GUIContentTemplate.saveTemplate(template3Title, template3Code, false, true);

        ContentTab firstTabType = DSLContent.getFirstTab(employeeType.getFqn());

        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeType, template3Code, firstTabType);
        DSLLeftMenuItem.add(item);

        // Выполненией действий
        GUIContent.clickDisableTemplateMode();
        GUIContent.resetProperties();

        // Проверка
        GUIError.assertDialogError(String.format("Настройки карточки не могут быть сброшены по причинам:\n"
                                                 + "1. Шаблон '%s' (%s) не может быть удалён. Он используется в "
                                                 + "настройках элементов"
                                                 + " меню: '%s'.", template3Title, template3Code, item.getTitle()));
    }

    /**
     * Тестирование сохранения шаблона карточки типа в элементе левого меню при разрыве наследования типа с его
     * метаклассом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00408
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$127303614
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка};</li>
     * <li>Создать подтип сотрудника employeeType</li>
     * <li>В левом навигационном меню создать элемент item типа "ссылка на карточку" (Вкладка карточки- tab1,
     * шаблон карточки - template), включить его</li>
     * <br>
     * <b>Выполнение действия:</b>
     * <li>Разорвать наследование настроек карточки типа employeeType</li>
     * <br>
     * <b>Проверки:</b>
     * <li>Шаблон карточки template сохранился в элементе левого меню item</li>
     * </ol>
     */
    @Test
    public void testTemplateNotChangedAfterResetSettings()
    {
        // Подготовка
        MetaClass employeeType = DAOEmployeeCase.create(employeeCase);
        DSLMetaClass.add(employeeType);

        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, employeeType, templateCode, tab1);
        DSLLeftMenuItem.add(item);

        // Выоплнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeType, MetaclassCardTab.OBJECTCARD);
        GUIContent.enableEditProperties();

        // Проверка
        GUINavSettings.goToCard();
        GUINavSettings.clickEditLeftMenuItem(item);
        GUISelect.assertSelected(
                String.format(GUIXpath.Div.ANY_VALUE + Input.INPUT_PREFIX, GUIMenuItem.MENU_ITEM_VALUE_TEMPLATE),
                templateTitle);
    }

    /**
     * Тестирование применения шаблона карточки при клике на элемент левого меню, настроенный на этот шаблон без
     * указания вкладки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$197742934
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать корневой элемент левого меню типа «Ссылка на карточку» (Ссылка на карточку — Текущего
     * пользователя, Тип объекта — employeeCase, Вкладка карточки — сама карточка, Шаблон карточки — templateCode)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Открыть левую навигационную панель</li>
     * <li>Перейти по ссылке menuItem в левом меню</li>
     * <br>
     * <b>Проверки</b>
     * <li>Произошел переход на карточку сотрудника</li>
     * <li>На карточке отображаются только две вкладки tab1 и tab2 в соответствии с настройками шаблона с кодом
     * templateCode</li>
     * </ol>
     */
    @Test
    public void testLeftMenuItemReferenceWithTemplateWithoutTab()
    {
        // Подготовка
        LeftMenuItem menuItem = DAOLeftMenuItem.createReference(true, null, employeeCase, templateCode);
        DSLLeftMenuItem.add(menuItem);
        // Выполнение действий
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(menuItem);
        // Проверки
        GUIBo.assertThatBoCard(employee);
        GUITab.assertRootTabs(tab1Title, tab2.getTitle());
    }

    /**
     * Тестирование корректной установки шаблона карточки при переходе на домашнюю страницу с настроенным шаблоном
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00519
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180576632
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для всех тестов}</li>
     * <li>Создать домашнюю страницу referenceToUser:
     *     <ul>
     *         <li>Вид элемента: Ссылка на карточку</li>
     *         <li>Профили: secProfileLic</li>
     *         <li>Ссылка на карточку: Текущего пользователя</li>
     *         <li>Вкладка карточки: tab1</li>
     *         <li>Шаблон карточки: templateCode</li>
     *     </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Проверить, что произошел переход на карточку сотрудника</li>
     * <li>Проверить, что на карточке отображаются только две вкладки tab1 и tab2 в соответствии с настройками
     * шаблона с кодом templateCode</li>
     * </ol>
     */
    @Test
    public void testHomePageWithUITemplate()
    {
        //Подготовка
        HomePage referenceToUser = DAOHomePage.createReferenceToUser(employeeCase, templateCode, tab1);
        referenceToUser.setProfiles(SharedFixture.secProfileLic());
        DSLHomePage.add(referenceToUser);

        //Действия и проверки
        GUILogon.login(employee);
        GUIBo.assertThatBoCard(employee);
        GUITab.assertRootTabs(tab1Title, tab2.getTitle());
    }
}
