package ru.naumen.selenium.cases.cluster;

import static ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication.APPLICATION_WS_SUBSCRIBE;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIStatusPanel;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование основного функционала, который работает на websocket в кластере
 * <AUTHOR>
 * @since 05.03.2025
 */
public class ClusterWebsocketTest extends AbstractClusterSynchronizationTestCase
{
    private static final String SUPER_ADD_COMMENT_TEXT = "Суперпользователь добавил(а) комментарий: %s.";
    private static final String CHANGE_TEXT = "%s редактирует: %s.";
    private static final String TEST_MSG_BODY = "Test message body";
    private static final String EDITED_TEST_MSG_BODY = "Edited test message body";

    private static MetaClass userClass;
    private static MetaClass userCase, userCase2;
    private static Bo employee;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Создать пользовательский класс userClass и унаследованные от него типы userCase, userCase2</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты stringAttr, Название,
     * Статус, Ответственный</li>
     * <li>На карточку объекта класса userClass вывести контент propertyList типа «Параметры объекта» (Группа
     * атрибутов — attrGroup)</li>
     * <li>На форму редактирования объекта класса userClass вывести контент editablePropertyList типа «Параметры на
     * форме» (Группа атрибутов — attrGroup)</li>
     * <li>Создать сотрудника employee</li>
     * <li>Включить отслеживание изменений объектов</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(userClass, userCase, userCase2, stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass.getFqn());
        DSLGroupAttr.add(attrGroup, stringAttr, SysAttribute.title(userClass), SysAttribute.state(userClass),
                SysAttribute.responsible(userClass));
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass, attrGroup);
        ContentForm editablePropertyList = DAOContentEditForm.createEditablePropertyList(userClass.getFqn(), attrGroup);
        DSLContent.add(propertyList, editablePropertyList);
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
    }

    @Before
    public void setUp()
    {
        DSLAdmin.setObjectChangeTrackingEnabled(true);
    }

    /**
     * Тестирование отслеживания события добавления комментария в кластере
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00960
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277796403
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать действие по событию eventAction (Объекты — userClass, Событие — Добавление комментария,
     * Действие — Отслеживание изменений, Использовать текст сообщения по умолчанию — да, Кому — employee,
     * Действие в веб-интерфейсе — Сообщение с кнопкой Обновить, Область обновления — Контенты с изменениями)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку объекта класса userClass вывести контент commentList типа «Список комментариев»</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>На ноде UNIVERSAL1</li>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Добавить комментарий comment к объекту userBo от имени суперпользователя</li>
     * <li>Проверить, что на панели состояния появилось сообщение «Суперпользователь добавил(а) комментарий:
     * %comment%»</li>
     * <li>Нажать на кнопку «Обновить» в панели состояния</li>
     * <li>Проверить, что панель состояния исчезла</li>
     * <li>Проверить, что в списке commentList появился комментарий comment</li>
     */
    @Test
    public void testAddCommentChangeTracking()
    {
        // Подготовка
        EventAction eventAction = DAOEventAction.createChangeTracking(
                userClass, EventType.addComment, employee);
        DSLEventAction.add(eventAction);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        ContentForm commentList = DAOContentCard.createCommentList(userClass);
        DSLContent.add(commentList);

        // Выполнение действий и проверки
        GUILogon.login(employee);
        checkExistMessage(userBo, commentList);
        GUILogon.logout();
    }

    private static void checkExistMessage(Bo userBo, ContentForm commentList)
    {
        GUIBo.goToCard(userBo);
        String text = "Красный";
        String html = "<span style=\"color:red;\">" + text + "</span>";
        String commentUuid = DSLComment.add(userBo.getUuid(), html);
        tester.waitAsyncCall();
        GUIStatusPanel.assertStatusPanelAppear();
        GUIStatusPanel.assertStatusMessage(String.format(SUPER_ADD_COMMENT_TEXT, text));
        GUIStatusPanel.clickMessageAction();
        GUIStatusPanel.assertStatusPanelDisappear();
        GUICommentList.assertCommentText(commentList, commentUuid, text);
    }

    /**
     * Тестирование отслеживания открытия формы массового редактирования в кластере, когда ДПС настроено только на тип
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00960
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277796403
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать атрибут типа строка strClass</li>
     * <li>В классе userClass создать группу атрибутов testGroup</li>
     * <li>Поместить созданные атрибуты в группу testGroup</li>
     * <li>В классе userClass создать форму массового редактирования massEditForm, группа атрибутов testGroup,
     * использовать по умолчанию = true</li>
     * <li>Добавить контент с группой testGroup на карточку класса userClass</li>
     * <li>На карточке класса Компания создать список объектов list класса userClass, представление - сложный
     * список</li>
     * <li>Создать действие по событию eventAction (Объекты — userCase, Событие — Открытие формы редактирования,
     * Действие — Отслеживание изменений, Использовать текст сообщения по умолчанию — да, Кому — employee,
     * Действие в веб-интерфейсе — Сообщение)</li>
     * <li>Создать объекты bo1, bo2 типов userCase и userCase2 соответственно</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>На ноде UNIVERSAL1</li>
     * <li>Залогиниться под пользователем employee</li>
     * <li>Перейти на карточку Компании</li>
     * <li>В соседних вкладках открыть объекты bo1, bo2</li>
     * <li>В списке объектов userClass выделить объекты bo1, bo2 и вызвать форму массового редактирования</li>
     * <li>Открыть вкладку с объектом bo1</li>
     * <li>Проверка: появилось контекстное сообщение с текстом "ВСТАВИТЬ ТЕКСТ"</li>
     * <li>Открыть вкладку с объектом bo2</li>
     * <li>Проверка: не появилось контекстное сообщение</li>
     * </ol>
     */
    @Test
    public void testOpenMassEditFormTracking()
    {
        //Подготовка
        Attribute strClass = DAOAttribute.createString(userClass);
        GroupAttr testGroup = DAOGroupAttr.create(userClass);
        DSLAttribute.add(strClass);
        DSLGroupAttr.add(testGroup, strClass);

        CustomForm massEditForm = DAOCustomForm.createMassEditForm(testGroup,
                CustomForm.CommentOnFormProperty.NOT_FILL, true, userCase);
        DSLCustomForm.add(massEditForm);

        ContentForm testGroupContent = DAOContentCard.createPropertyList(userClass, testGroup);
        DSLContent.add(testGroupContent);

        ContentForm list = DAOContentCard.createObjectAdvList(SharedFixture.employeeCase().getFqn(), userClass);
        DSLContent.add(list);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        EventAction eventAction = DAOEventAction.createChangeTracking(
                userClass, EventType.openEditForm, employee);
        eventAction.setExcludeAuthor(Boolean.FALSE.toString());
        DSLEventAction.add(eventAction);

        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase2);
        DSLBo.add(bo1, bo2);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        list.advlist().mass().selectElements(bo1, bo2);
        list.advlist().mass().clickOperation(MassOperation.MASS_EDIT);
        tester.waitAsyncCall();
        checkMsg(strClass, bo1, bo2);
        GUILogon.logout();
    }

    private static void checkMsg(Attribute strClass, Bo bo1, Bo bo2)
    {
        String expectedMessage = String.format(CHANGE_TEXT, employee.getTitle(), strClass.getTitle());
        GUIBo.goToCard(bo1);
        GUIStatusPanel.assertStatusPanelAppear();
        GUIStatusPanel.assertStatusMessage(expectedMessage);
        GUIBo.goToCard(bo2);
        GUIStatusPanel.assertStatusPanelDisappear();
    }

    /**
     * Тестирование методов jsApi.wsSubscribe(destination, callback), jsApi.wsUnsubscribe(destination, callback)
     * в кластере
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$277796403
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и включить встроенное приложение application, которое подписывается на сообщения, приходящие
     * в dest1 и отписывается, после получения первого сообщения</li>
     * <li>На карточку компании вывести контент "Встроенное приложение" (Приложение - application)</li>
     * <li>Синхронизировать кластер</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти в интерфейс оператора на ноде UNIVERSAL1</li>
     * <li>Проверить, что на ноде UNIVERSAL1 на карточке компании
     * отображается контент "Встроенное приложение"</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Test message body') отправить сообщение в dest1</li>
     * <li>Проверить, что на ноде UNIVERSAL1 в контенте присутствует текст "Test message body"</li>
     * <li>С помощью скрипта api.websocket.sendMessage('dest1', 'Edited test message body') отправить сообщение в
     * dest1</li>
     * <li>Проверить, что на ноде UNIVERSAL1 текст в контенте не изменился</li>
     * </ol>
     */
    @Test
    public void testWsSubscribeUnsubscribe()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                APPLICATION_WS_SUBSCRIBE);
        DSLEmbeddedApplication.add(application);
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIContent.assertPresent(content);
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", TEST_MSG_BODY)).runScript();
        tester.waitAsyncCall();
        Assert.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));

        new ScriptRunner(String.format("api.websocket.sendMessage('dest1', '%s');", EDITED_TEST_MSG_BODY)).runScript();
        tester.waitAsyncCall();
        Assert.assertEquals(TEST_MSG_BODY, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }
}