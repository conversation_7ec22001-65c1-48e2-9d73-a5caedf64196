package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.OBJECTCARD;
import static ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication.TEST_DIV_ID;
import static ru.naumen.selenium.context.CVConsts.BROWSER_TAB_TITLE;
import static ru.naumen.selenium.context.CVConsts.CARD_CAPTION;

import java.io.File;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIBoCaption;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication.FormType;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;

/**
 * Тесты на методы jsApi.utils.*
 *
 * <AUTHOR>
 * @since March 17, 2021
 */
class JsApiUtilsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userClass, userCase;
    private static Bo userBo;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать класс userClass с ответственным и сменой ЖЦ и его подтип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWith().responsible().workFlow().create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
    }

    /**
     * Тестирование создания объекта методом jsApi.utils.create
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$258225631
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать атрибут objectAttr в типе userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.create(userCase, {'title': '%название объекта%', '%код objectAttr%': null})
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = bo.UUID
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить идентификатор объекта из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что в ответ вернулся идентификатор реального объекта</li>
     * <li>Проверить, что объект с полученным UUID имеет пустой атрибут objectAttr и такое название, как мы указали в
     * коде встроенного приложения</li>
     * </ol>
     */
    @Test
    void testCreate()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase);
        DSLAttribute.add(objectAttr);

        String expectedTitle = ModelUtils.createTitle();
        String jsContent = """
                jsApi.utils.create('%s', {'title': '%s', '%s': null})
                    .then(function (bo) {
                        document.getElementById('%s').innerText = bo.UUID
                    })"""
                .formatted(userCase.getFqn(), expectedTitle, objectAttr.getCode(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String uuid = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Bo createdBo = DAOBo.createModelByUuid(uuid);
        DSLBo.assertAttributes(createdBo, objectAttr);
    }

    /**
     * Тестирование получения объектов методом jsApi.utils.create, когда передан список кодов атрибутов для возвращения
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать строковый атрибут stringAttr в типе userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.create(userCase, {'title': '%название объекта%'},
     *             jsApi.utils.buildParams().attrs([%код атрибута stringAttr%])
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = Object.keys(bo).sort()
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить список полученных атрибутов объекта из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что список полученных атрибутов содержит код атрибута stringAttr и системные атрибуты,
     * обязательные для передачи</li>
     * </ol>
     */
    @Test
    void testCreateWithAttributeCodes()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(stringAttr);

        String expectedTitle = ModelUtils.createTitle();
        String jsContent = String.format(
                "jsApi.utils.create('%s', {'title': '%s'}, jsApi.utils.buildParams().attrs(['%s']))" +
                "    .then(function (bo) {\n" +
                "        document.getElementById('%s').innerText = bo.UUID + ' ' + Object.keys(bo).sort()\n" +
                "    })", userCase.getFqn(), expectedTitle, stringAttr.getCode(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String result = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        String[] resultParts = result.split(" ");
        DAOBo.createModelByUuid(resultParts[0]); // проверяем что объект создан

        Assertions.assertEquals(String.join(",",
                stringAttr.getCode(),
                SystemAttrEnum.UUID.getCode(),
                BROWSER_TAB_TITLE,
                CARD_CAPTION,
                SystemAttrEnum.METACLASS.getCode(),
                SystemAttrEnum.REMOVED.getCode(),
                SystemAttrEnum.STATE.getCode(),
                SystemAttrEnum.TITLE.getCode()), resultParts[1]);
    }

    /**
     * Тестирование ошибки при создании объектов без названия методом jsApi.utils.create
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.create(userCase, {}).catch(function (error) {
     *         document.getElementById('testDiv').innerText = error.message
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить текст ошибки из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что был получен текст ошибки "%название userClass% не может быть добавлен
     * по следующим причинам:
     * 1. Не заполнены следующие обязательные атрибуты: Название."</li>
     * </ol>
     */
    @Test
    void testCreateWithTitleNotExistsException()
    {
        // Подготовка
        String jsContent = String.format("jsApi.utils.create('%s', {}).catch(function (error) {\n" +
                                         "    document.getElementById('%s').innerText = error.message\n" +
                                         "})", userCase.getFqn(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String exception = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals(userClass.getTitle() + " не может быть добавлен по следующим причинам:\n"
                                + "1. Не заполнены следующие обязательные атрибуты: Название.", exception);
    }

    /**
     * Тестирование ошибки при создании объектов не существующего типа методом jsApi.utils.create
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.create(%не существующий тип%, {}).catch(function (error) {
     *         document.getElementById('testDiv').innerText = error.message
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить текст ошибки из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что был получен текст ошибки "Метакласс не найден: fqn=%не существующий тип%"</li>
     * </ol>
     */
    @Test
    void testCreateWithFqnNotFoundException()
    {
        // Подготовка
        String fakeFqn = ModelUtils.createFqnFromParent(userClass.getFqn(), ModelUtils.createCode());
        String jsContent = String.format("jsApi.utils.create('%s', {}).catch(function (error) {\n" +
                                         "    document.getElementById('%s').innerText = error.message\n" +
                                         "})", fakeFqn, TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String exception = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals("Метакласс не найден: fqn=" + fakeFqn, exception);
    }

    /**
     * Тестирование изменения объекта методом jsApi.utils.edit
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$258225631
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать атрибут objectAttr в типе userCase</li>
     * <li>Создать объект userBo2 типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.edit(userBo2, {'title': '%новое название объекта%', '%код objectAttr%': null})
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = bo.UUID
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo2</li>
     * <li>Получить идентификатор объекта (содержится в результате работы метода jsApi.utils.edit)
     * из встроенного приложения application с карточки userBo2</li>
     * </ol>
     * <ol>
     * <b>Выполнение проверок:</b>
     * <li>Проверить, что был получен идентификатор объекта userBo2</li>
     * <li>Проверить, что атрибут title объекта userBo2 содержит новое название</li>
     * <li>Проверить, что userBo имеет пустой атрибут objectAttr</li>
     * </ol>
     */
    @Test
    void testEdit()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase);
        DSLAttribute.add(objectAttr);

        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo2);

        String expectedTitle = ModelUtils.createTitle();
        String jsContent = """
                jsApi.utils.edit('%s', {'title': '%s', '%s': null}).then(function (bo) {
                    document.getElementById('%s').innerText = bo.UUID
                })"""
                .formatted(userBo2.getUuid(), expectedTitle, objectAttr.getCode(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo2);
        String uuid = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Выполнение проверок
        Assertions.assertEquals(userBo2.getUuid(), uuid);

        Attribute titleAttr = SysAttribute.title(userCase);
        titleAttr.setValue(expectedTitle);
        DSLBo.assertAttributes(userBo2, titleAttr, objectAttr);
    }

    /**
     * Тестирование изменения ссылочного атрибута объекта методом jsApi.utils.edit.
     * Данный тест также проверяет кейс, когда атрибут не выведен на карточку, но должен вернутся,
     * потому что для Api по умолчанию возвращаются все атрибуты, если набор атрибутов намеренно не ограничить.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создаём ссылочный атрибут objectAttr в типе userCase, ссылающийся на тип userCase</li>
     * <li>Создать объект userBo2 типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.edit(userBo2, {'%код атрибута objectAttr%': userBo})
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = bo.UUID
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий и проверки:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo2</li>
     * <li>Получить идентификатор объекта (содержится в результате работы метода jsApi.utils.edit)
     * из встроенного приложения application с карточки userBo2</li>
     * <li>Проверить, что был получен идентификатор объекта userBo2</li>
     * <li>Проверить, что атрибут objectAttr объекта userBo2 содержит userBo</li>
     * <li>Перезагрузить страницу</li>
     * <li>Проверить, что был получен идентификатор объекта userBo2</li>
     * <li>Проверить, что атрибут objectAttr объекта userBo2 содержит userBo</li>
     * </ol>
     */
    @Test
    void testEditObjectAttribute()
    {
        // Подготовка
        Attribute objectAttr = DAOAttribute.createObjectLink(userCase, userCase, null);
        DSLAttribute.add(objectAttr);

        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo2);

        String jsContent = String.format("jsApi.utils.edit('%s', {'%s': '%s'}).then(function (bo) {\n" +
                                         "    document.getElementById('%s').innerText = bo.UUID\n" +
                                         "})", userBo2.getUuid(), objectAttr.getCode(), userBo.getUuid(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий и проверок
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo2);

        String uuid = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);
        Assertions.assertEquals(userBo2.getUuid(), uuid);
        objectAttr.setValue(userBo.getUuid());
        DSLBo.assertAttributes(userBo2, objectAttr);

        tester.refresh();

        String uuid2 = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);
        Assertions.assertEquals(userBo2.getUuid(), uuid2);
        objectAttr.setValue(userBo.getUuid());
        DSLBo.assertAttributes(userBo2, objectAttr);
    }

    /**
     * Тестирование получения объектов методом jsApi.utils.edit, когда передан список кодов атрибутов для возвращения
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать строковый атрибут stringAttr в типе userCase</li>
     * <li>Создать объект userBo2 типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.edit(userBo2, {'title': '%новое название объекта%'},
     *             jsApi.utils.buildParams().attrs([%код атрибута stringAttr%])
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = Object.keys(bo).sort()
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить список полученных атрибутов объекта из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что список полученных атрибутов содержит код атрибута stringAttr и системные атрибуты,
     * обязательные для передачи</li>
     * </ol>
     */
    @Test
    void testEditWithAttributeCodes()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(stringAttr);

        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo2);

        String expectedTitle = ModelUtils.createTitle();
        String jsContent = String.format(
                "jsApi.utils.edit('%s', {'title': '%s'}, jsApi.utils.buildParams().attrs(['%s']))" +
                "    .then(function (bo) {\n" +
                "        document.getElementById('%s').innerText = Object.keys(bo).sort()\n" +
                "    })", userBo2.getUuid(), expectedTitle, stringAttr.getCode(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String attributeCodes = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals(String.join(",",
                stringAttr.getCode(),
                SystemAttrEnum.UUID.getCode(),
                BROWSER_TAB_TITLE,
                CARD_CAPTION,
                SystemAttrEnum.METACLASS.getCode(),
                SystemAttrEnum.REMOVED.getCode(),
                SystemAttrEnum.STATE.getCode(),
                SystemAttrEnum.TITLE.getCode()), attributeCodes);
    }

    /**
     * Тестирование ошибки при изменении не существующего объекта методом jsApi.utils.edit
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.edit(%не существующий идентификатор%, {'title': '%новое название объекта%'})
     *         .catch(function (error) {
     *             document.getElementById('testDiv').innerText = error.message
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить текст ошибки из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что был получен текст ошибки "Объект не найден: uuid=%не существующий идентификатор%"</li>
     * </ol>
     */
    @Test
    void testEditWithException()
    {
        // Подготовка
        String fakeUuid = ModelUtils.createFqnFromParent(userCase.getFqn(),
                String.valueOf(ThreadLocalRandom.current().nextInt()));
        String jsContent = String.format("jsApi.utils.edit('%s', {'title': '%s'}).catch(function (error) {\n" +
                                         "    document.getElementById('%s').innerText = error.message\n" +
                                         "})", fakeUuid, ModelUtils.createTitle(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String exception = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals("Объект не найден: uuid=" + fakeUuid, exception);
    }

    /**
     * Тестирование удаления объекта методом jsApi.utils.delete
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать объект userBo2 типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.delete(userBo2).then(function (uuid) {
     *         document.getElementById('testDiv').innerText = uuid
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo2</li>
     * <li>Получить идентификатор объекта (результат работы метода jsApi.utils.delete)
     * из встроенного приложения application с карточки userBo2</li>
     * </ol>
     * <ol>
     * <b>Выполнение проверок:</b>
     * <li>Проверить, что был получен идентификатор объекта userBo2</li>
     * <li>Проверить, что объекта userBo2 не существует в системе</li>
     * </ol>
     */
    @Test
    void testDelete()
    {
        // Подготовка
        Bo userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo2);

        String jsContent = String.format("jsApi.utils.delete('%s').then(function (uuid) {\n" +
                                         "    document.getElementById('%s').innerText = uuid\n" +
                                         "})", userBo2.getUuid(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo2);
        String uuid = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Выполнение проверок
        Assertions.assertEquals(userBo2.getUuid(), uuid);
        DSLBo.assertAbsence(userBo2);
    }

    /**
     * Тестирование ошибки при удалении не существующего объекта методом jsApi.utils.delete
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.delete(%не существующий идентификатор%).catch(function (error) {
     *         document.getElementById('testDiv').innerText = error.message
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить текст ошибки из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что был получен текст ошибки "Объект не найден: uuid=%не существующий идентификатор%"</li>
     * </ol>
     */
    @Test
    void testDeleteWithException()
    {
        // Подготовка
        String fakeUuid = ModelUtils.createFqnFromParent(userCase.getFqn(),
                String.valueOf(ThreadLocalRandom.current().nextInt()));
        String jsContent = String.format("jsApi.utils.delete('%s').catch(function (error) {\n" +
                                         "    document.getElementById('%s').innerText = error.message\n" +
                                         "})", fakeUuid, TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String exception = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals("Объект не найден: uuid=" + fakeUuid, exception);
    }

    /**
     * Тестирование получения объекта методом jsApi.utils.get
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.get(userBo).then(function (bo) {
     *         document.getElementById('testDiv').innerText = bo.title
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить название объекта title из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что название объекта userBo совпадает с title</li>
     * </ol>
     */
    @Test
    void testGet()
    {
        // Подготовка
        String jsContent = String.format("jsApi.utils.get('%s').then(function (bo) {\n" +
                                         "    document.getElementById('%s').innerText = bo.title\n" +
                                         "})", userBo.getUuid(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String title = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals(userBo.getTitle(), title);
    }

    /**
     * Тестирование того, что при получении объекта методом jsApi.utils.get
     * страница не обновляется
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$294153922
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Добавить действие по событию <code>eventAction</code>:
     *   <ul>
     *     <li>Тип = "Пользовательское событие"</li>
     *     <li>Скрипт = "return true"</li>
     *     <li>Включен = Да</li>
     *     <li>Синхронный = Да</li>
     *     <li>Объект = <code>userCase</code></li>
     *   </ul></li>
     * <li>Добавить встроенное приложение <code>application</code>:
     *   <pre>
     *   -----------------------------------------------------------------------
     *     jsApi.utils.get(userBo).then(function (bo) {
     *         document.getElementById('testDiv').innerText = bo.title
     *     })
     *   -----------------------------------------------------------------------
     *   </pre></li>
     * <li>Добавить место использования <code>application</code>:
     *   <ul>
     *     <li>Объекты = <code>userClass</code></li>,
     *     <li>Тип формы = "Форма пользовательского действия по событию"/li>,
     *     <li>Доступные пользовательские действия по событию = <code>eventAction</code></li>.
     *   </ul></li>
     * <li>На карточку объектов <code>userCase</code> добавить кнопку пользовательских действий по событию:
     *   <code>eventAction</code></li>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>В соседнем окне отредактировать значение атрибута название newTitle у userBo</li>
     * <li>На карточке userBo нажать кнопку добавленного пользовательского действия</li>
     * <b>Проверки:</b>
     * <li>Появилась форма пользовательского действия</li>
     * <li>Получить название объекта title из встроенного приложения application с этой формы</li>
     * <li>Проверить, что название объекта userBo совпадает с изменённым названием newTitle</li>
     * <li>Проверить, что название объекта userBo на карточке осталось старое oldTitle (карточка не обновилась)</li>
     * </ol>
     */
    @Test
    void testNoRefreshPageWhenGetObject()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(scriptInfo);
        EventAction eventAction =
                DAOEventAction.createEventScript(EventType.userEvent, scriptInfo, true, TxType.Sync, userCase);
        DSLEventAction.add(eventAction);

        String jsContent = String.format("jsApi.utils.get('%s').then(function (bo) {\n" +
                                         "    document.getElementById('%s').innerText = bo.title\n" +
                                         "})", userBo.getUuid(), TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(application);

        UsagePointApplication applicationUsage =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.UserEventActionForm, userClass);
        applicationUsage.setUserEventActions(eventAction);
        DSLEmbeddedApplication.edit(application, null, applicationUsage);

        ContentForm card = DAOContentCard.createWindow(userCase);
        String buttonTitle = ModelUtils.createTitle();
        DSLContent.addUserTool(card, buttonTitle, eventAction);

        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(userCase, OBJECTCARD));

        // Выполнение действий
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);

        Attribute titleAttr = SysAttribute.title(userClass);
        String oldTitle = userBo.getTitle();
        String newTitle = "new" + oldTitle;
        titleAttr.setValue(newTitle);
        DSLBo.editAttributeValue(userBo, titleAttr);

        GUIButtonBar.clickButtonByText(buttonTitle);

        //Проверки
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        String title = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationUsage);
        Assertions.assertEquals(newTitle, title);

        GUIBoCaption.assertCaptionOnBoCard(userCase.getTitle() + " \"" + oldTitle + "\"");
    }

    /**
     * Тестирование получения объектов методом jsApi.utils.get, когда передан список кодов атрибутов для возвращения
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать строковый атрибут stringAttr в типе userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.get('%идентификатор объекта userBo%',
     *             jsApi.utils.buildParams().attrs([%код атрибута stringAttr%])
     *         .then(function (bo) {
     *             document.getElementById('testDiv').innerText = Object.keys(bo).sort()
     *         })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить список полученных атрибутов объекта из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что список полученных атрибутов содержит код атрибута stringAttr и системные атрибуты,
     * обязательные для передачи</li>
     * </ol>
     */
    @Test
    void testGetWithAttributeCodes()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userCase);
        DSLAttribute.add(stringAttr);

        String jsContent = String.format(
                "jsApi.utils.get('%s', jsApi.utils.buildParams().attrs(['%s']))" +
                "    .then(function (bo) {\n" +
                "        document.getElementById('%s').innerText = Object.keys(bo).sort()\n" +
                "    })", userBo.getUuid(), stringAttr.getCode(), TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String attributeCodes = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals(String.join(",",
                stringAttr.getCode(),
                SystemAttrEnum.UUID.getCode(),
                BROWSER_TAB_TITLE,
                CARD_CAPTION,
                SystemAttrEnum.METACLASS.getCode(),
                SystemAttrEnum.REMOVED.getCode(),
                SystemAttrEnum.STATE.getCode(),
                SystemAttrEnum.TITLE.getCode()), attributeCodes);
    }

    /**
     * Тестирование ошибки при получении не существующего объекта методом jsApi.utils.get
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     jsApi.utils.get(%не существующий идентификатор%).catch(function (error) {
     *         document.getElementById('testDiv').innerText = error.message
     *     })
     * </pre></li>
     * <li>Добавляем в систему встроенное приложение application, созданное из файла fileName</li>
     * <li>Создать на карточке userCase контент для встроенного приложения application</li>
     * </ol>
     * <ol>
     * <b>Выполнение действий:</b>
     * <li>Войти под суперпользователем и перейти на карточку userBo</li>
     * <li>Получить текст ошибки из встроенного приложения application с карточки userBo</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Проверить, что был получен текст ошибки "Объект не найден: uuid=%не существующий идентификатор%"</li>
     * </ol>
     */
    @Test
    void testGetWithException()
    {
        // Подготовка
        String fakeUuid = ModelUtils.createCode();
        String jsContent = String.format("jsApi.utils.get('%s').catch(function (error) {\n" +
                                         "    document.getElementById('%s').innerText = error.message\n" +
                                         "})", fakeUuid, TEST_DIV_ID);
        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp, jsContent)
                .getAbsolutePath();

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        ContentForm eaContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(eaContent);

        // Выполнение действий
        GUILogon.asSystem();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userBo);
        String exception = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(eaContent);

        // Проверка
        Assertions.assertEquals("Объект не найден: uuid=" + fakeUuid, exception);
    }
}
