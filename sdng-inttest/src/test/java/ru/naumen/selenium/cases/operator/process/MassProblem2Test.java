package ru.naumen.selenium.cases.operator.process;

import java.util.List;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLWfProfile;
import ru.naumen.selenium.casesutil.admin.GUIWfProfile;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLMassProblem;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Analyzer;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Boost;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUIMassProblem;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLActionCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOWfProfile;
import ru.naumen.selenium.casesutil.model.admin.WfProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование массового инцидента
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495
 * <AUTHOR>
 * @since 05.03.2013
 */
public class MassProblem2Test extends AbstractTestCase
{
    /**
     * Тестирование множественной смены статуса у массового запроса, если дочерние запросы присоединяются в процессе
     * выполнения действия при входе в статус
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$134355775
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе «Запрос» создать атрибут childCallsAttr типа «Набор ссылок на БО» (Класс объектов — Запрос)</li>
     * <li>В классе «Запрос» создать статусы firstState и secondState</li>
     * <li>Настроить переходы из статуса «Зарегистрирован» в firstState и из firstState в secondState</li>
     * <li>Добавить действие на вход в статус firstState со скриптом:
     * <pre>
     * def childCalls = subject?.childCallsAttr
     * if (childCalls) {
     *     def massProblemSlaves = subject.massProblemSlaves
     *     massProblemSlaves = massProblemSlaves + childCalls
     *     utils.edit(subject, ['massProblemSlaves': massProblemSlaves])
     * }</pre></li>
     * <li>Настроить обязательное добавление комментария при входе в статус firstState</li>
     * <li>Создать действие по событию eventAction (Объекты — Запрос, Событие — Добавление комментария, Действие —
     * скрипт, Выполнять синхронно — да) и задать для него скрипт:
     * <pre>utils.edit(currentSubject, ['state': 'secondState'])</pre></li>
     * <li>Для действия по событию eventAction добавить условие выполнения со скриптом:
     * <pre>return subject.state in ['firstState'] ? '' : 'error'</pre></li>
     * <li>Создать запросы mainSc, sc1 и sc2</li>
     * <li>Сделать mainSc массовым и связать его с sc1</li>
     * <li>Изменить mainSc: childCallsAttr = sc2</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку запроса mainSc</li>
     * <li>Нажать на кнопку «Изменить статус»</li>
     * <li>Выбрать статус firstState и заполнить поле ввода комментария</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <br>
     * <b>Проверка</b>
     * <li>Запросы mainSc, sc1 и sc2 находятся в статусе secondState</li>
     * </ol>
     */
    @Test
    public void testMassChangeStateWhenMassScJoinSlaves()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        Attribute childCallsAttr = DAOAttribute.createBoLinks(scClass, scClass);
        DSLAttribute.add(childCallsAttr);
        ScriptInfo stateScript = DAOScriptInfo.createNewScriptInfo(String.format(""
                                                                                 + "def childCalls = subject?.%s%n"
                                                                                 + "if (childCalls) {%n"
                                                                                 + "    def massProblemSlaves = "
                                                                                 + "subject.massProblemSlaves%n"
                                                                                 + "    massProblemSlaves = "
                                                                                 + "massProblemSlaves + childCalls%n"
                                                                                 + "    utils.edit(subject, "
                                                                                 + "['massProblemSlaves': "
                                                                                 + "massProblemSlaves])%n"
                                                                                 + "}", childCallsAttr.getCode()));
        DSLScriptInfo.addScript(stateScript);

        BoStatus firstState = DAOBoStatus.createUserStatus(scClass);
        BoStatus secondState = DAOBoStatus.createUserStatus(scClass);
        DSLBoStatus.add(firstState, secondState);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scClass), firstState);
        DSLBoStatus.setTransitions(firstState, secondState);
        DSLBoStatus.addPreAction(firstState, stateScript);
        DSLBoStatus.setCommentAttrInState(firstState, true, 2, 0);

        ScriptInfo eventActionScript = DAOScriptInfo.createNewScriptInfo(String.format(
                "utils.edit(currentSubject, ['state': '%s'])", secondState.getCode()));
        ScriptInfo conditionScript = DAOScriptInfo.createNewScriptInfo(String.format(
                "return subject.state in ['%s'] ? '' : 'error'", firstState.getCode()));
        DSLScriptInfo.addScript(eventActionScript, conditionScript);

        EventAction eventAction = DAOEventAction.createEventScript(EventType.addComment, eventActionScript.getCode(),
                true, TxType.Sync, scClass);
        DSLEventAction.add(eventAction);
        ActionCondition actionCondition = DAOActionCondition.create(eventAction, conditionScript.getCode());
        DSLActionCondition.add(actionCondition);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        Bo mainSc = DAOSc.create(SharedFixture.scCase(), employee, agreement, SharedFixture.timeZone());
        Bo sc1 = DAOSc.create(SharedFixture.scCase(), employee, agreement, SharedFixture.timeZone());
        Bo sc2 = DAOSc.create(SharedFixture.scCase(), employee, agreement, SharedFixture.timeZone());
        DSLBo.add(mainSc, sc1, sc2);
        DSLMassProblem.setMass(mainSc);
        DSLMassProblem.addSlave(mainSc, sc1);
        childCallsAttr.setValue(Json.listToString(sc2.getUuid()));
        DSLBo.editAttributeValue(mainSc, childCallsAttr);
        // Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(mainSc);
        GUIButtonBar.changeState();
        GUISc.selectStatus(firstState);
        GUIComment.fillCommentOnForm(ModelUtils.createText(20), false);
        GUIForm.applyForm();
        // Проверки
        DSLBo.assertState(mainSc, secondState.getCode());
        DSLBo.assertState(sc1, secondState.getCode());
        DSLBo.assertState(sc2, secondState.getCode());
    }

    /**
     * Тестирование пропадания кнопок Изменить ответственного, изменить статус из панели массовых операций после
     * подчинения запроса массовому (сразу же, без обновления страницы)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * http://sd-jira.naumen.ru/browse/NSDPRD-2229
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase, тип отдела ouCase, тип услуги serviceCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать запросы sc1..2 типа scCase под отделом ou</li>
     * <li>Сделать sc1 массовым</li>
     * <li>Добавить контент Список объектов content типа scCase на карточку компании</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Перейти на карточку ou</li>
     * <li>Выбрать в content запрос sc2</li>
     * <li>Проверить, что в списке массовых операций есть кнопки Изменить ответственного, изменить статус</li>
     * <li>Нажать Работа с массовостью, подчинить запросу sc1, нажать Сохранить</li>
     * <li>Проверить, что в списке массовых операций отсутствуют кнопки Изменить ответственного, изменить статус</li>
     * </ol>
     */
    @Test
    public void testMassOperationsHideAfterChangingMassState()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        DSLMetaClass.add(ouCase, scCase, serviceCase);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(ou, service);
        DSLAgreement.addToRecipients(agreement, ou);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);
        ContentForm content = DAOContentCard.createObjectList(ouCase.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, scCase, DAOGroupAttr.createSystem(), scCase);
        DSLContent.add(content);
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(ou);
        content.advlist().mass().selectElements(sc2);
        content.advlist().mass().asserts().presenceByCode(true, false, MassOperation.CHANGE_STATE,
                MassOperation.CHANGE_RESPONSIBLE);
        content.advlist().mass().clickOperation(MassOperation.MASS_SC_REGULAR);
        GUIMassProblem.clickMass(sc1);
        GUIForm.applyForm();
        content.advlist().mass().selectElements(sc2);
        content.advlist().mass().asserts().absenceByCode(MassOperation.CHANGE_STATE, MassOperation.CHANGE_RESPONSIBLE);
    }

    /**
     * тестирование отсутствия влияния матрицы передачи ответственности на массовость
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * http://sd-jira.naumen.ru/browse/NSDPRD-2104
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать запросы sc1..2 типа scCase</li>
     * <li>Сменить ответственного запроса sc1 на employee1(в team1)</li>
     * <li>Сменить ответственного запроса sc2 на employee2(в team2)</li>
     * <li>Создать профиль связанных ЖЦ, установить в нем наследование ответственного</li>
     * <li>Важно: Матрица передачи ответственности не настроена, то есть запросы из team1 нельзя передавать в
     * ответственность team2</li>
     * <br>
     * <b>Выполнение действия.</b>
     * <li>Сделать sc1 массовым, связать с ним sc2</li>
     * <br>
     * <b>Проверка.</b>
     * <li>Ответственный за sc1..2- team1/employee1</li>
     * </ol>
     */
    @Test
    public void testMassScWithRespMatrix()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(scCase);
        //Создаем все для запроса
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем объекты для ответственных
        Bo team1 = DAOTeam.create(teamCase);
        Bo team2 = DAOTeam.create(teamCase);
        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(team1, team2, employee1, employee2);
        DSLTeam.addEmployees(team1, employee1);
        DSLTeam.addEmployees(team2, employee2);
        //Создаем профиль ЖЦ
        WfProfile wfProfile = DAOWfProfile.create(scCase, scCase, true, DAOBoStatus.createClosed(scCase.getFqn()));
        DSLWfProfile.add(wfProfile);
        DAOWfProfile.addAttrsForInheritanceToModel(wfProfile, SysAttribute.responsible(scCase));
        DSLWfProfile.edit(wfProfile);
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);

        DSLSc.setResponsible(sc1, team1, employee1);
        DSLSc.setResponsible(sc2, team2, employee2);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUIMassProblem.setMass(sc2);
        //Проверки
        DSLSc.assertResponsible(sc1, team1, employee1);
        DSLSc.assertResponsible(sc2, team1, employee1);
    }

    /**
     * Тестирование отмены обязательности совпадения услуг у массового и подчиненного запроса на форме добавления
     * запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На форму добавления запроса добавляем контент "Связанные запросы", указываем сложную форму представления</li>
     * <li>Добавляем запрос sc с услугой service1 типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Переходим на форму добавления запроса sc2</li>
     * <li>Указываем услугу service2 и тип scCase</li>
     * <li>Установливаем чекбокс "Массовый запрос"</li>
     * <li>В поле "Услуга" блока "Запросы для связывания" видим service2</li>
     * <li>Список запросов для связывания пуст</li>
     * <li>В поле "Услуга" блока "Запросы для связывания" выбираем service1</li>
     * <li>В блоке "Запросы для связывания" видим запрос sc1</li>
     * <li>Напротив sc1 нажимаем на ссылку "связать с массовым"</li>
     * <li>Сохраняем</li>
     * <li>Проверяем, что sc2 стал массовым</li>
     * <li>Проверяем, что в списке у подчиненных запросов sc2 появился sc1</li>
     * </ol>
     */
    @Test
    public void testMismatchServicesForMassAndSlaveScOnAdd()
    {
        // Подготовка
        MetaClass scCase = SharedFixture.scCase();
        ContentForm content = DAOContentAddForm.createMassProblems(DAOScCase.createClass(), true, false);
        DSLContent.add(content);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        Bo service1 = DAOService.create(SharedFixture.slmCase());
        Bo service2 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(service1, service2);
        DSLAgreement.addServices(agreement, service1, service2);
        DSLSlmService.addToScCase(scCase, service1, service2);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        Bo sc1 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        DSLBo.add(sc1);

        // Действия и проверки
        GUILogon.asTester();
        Bo sc2 = DAOSc.createWithService(scCase, ou, agreement, service2, timeZoneItem);
        GUIBo.goToScAddForm(sc2);
        GUIBo.fillScMainFields(sc2);
        GUIMassProblem.setMassSc();
        GUIMassProblem.assertServicesSelectList(service2);
        GUIMassProblem.assertMassScForRelationAbsence(sc1);
        GUIMassProblem.selectService(service1, content);
        GUIMassProblem.assertMassScForRelationPresent(sc1);
        GUIMassProblem.clickMass(sc1);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc2);
        DSLMassProblem.assertIsMass(sc2);
        DSLMassProblem.assertSlaves(sc2, sc1);
    }

    /**
     * Тестирование отмены обязательности совпадения услуг у массового и подчиненного запроса на форме работы с
     * массовостью
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавляем два запроса sc1 и sc2 типа scCase с привязкой к услуге service1 и service2 соответственно</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Переходим на карточку sc1</li>
     * <li>Нажимаем кнопку "Работа с массовостью"</li>
     * <li>Установливаем чекбокс "Массовый запрос"</li>
     * <li>В поле "Услуга" блока "Запросы для связывания" видим service1</li>
     * <li>Список запросов для связывания пуст</li>
     * <li>В поле "Услуга" блока "Запросы для связывания" выбираем service2</li>
     * <li>В блоке "Запросы для связывания" видим запрос sc2</li>
     * <li>Напротив sc2 нажимаем на ссылку "связать с массовым"</li>
     * <li>Сохраняем</li>
     * <li>Проверяем, что sc1 стал массовым</li>
     * <li>Проверяем, что в списке у подчиненных запросов sc1 появился sc2</li>
     * </ol>
     */
    @Test
    public void testMismatchServicesForMassAndSlaveScOnEdit()
    {
        // Подготовка
        MetaClass scCase = SharedFixture.scCase();
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        Bo service1 = DAOService.create(SharedFixture.slmCase());
        Bo service2 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(service1, service2);
        DSLAgreement.addServices(agreement, service1, service2);
        DSLSlmService.addToScCase(scCase, service1, service2);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        Bo sc1 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        Bo sc2 = DAOSc.createWithService(scCase, ou, agreement, service2, timeZoneItem);
        DSLBo.add(sc1, sc2);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUIButtonBar.massScRegular();
        GUIMassProblem.setMassSc();
        GUIMassProblem.assertServicesSelectList(service1);
        GUIMassProblem.assertMassScForRelationAbsence(sc2);
        GUIMassProblem.selectService(service2);
        GUIMassProblem.assertMassScForRelationPresent(sc2);
        GUIMassProblem.clickMass(sc2);
        GUIForm.applyForm();
        DSLMassProblem.assertIsMass(sc1);
        DSLMassProblem.assertSlaves(sc1, sc2);
    }

    /**
     * Тестирование разрыва связи между запросами после смены статуса у массового запроса и отсутствия разрыва связи
     * после смены статуса у массового запроса, если в профиле ЖЦ не указан статус разрыва связи.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00494
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать профиль связанных ЖЦ wfProfile (scCase, scCase). Статус для разрыва связи не указываем</li>
     * <li>Создать запросы sc1..2 типа scCase</li>
     * <li>Сделать sc1 массовым</li>
     * <li>Сделать sc2 подчиненным sc1</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>У sc2 в атрибуте Код профиля связанных жизненных циклов - wfProfile</li>
     * <li>Изменить статус sc1 на Возобновлен</li>
     * <li>Проверить, что связь между sc1 и sc2 не разорвана</li>
     * <li>Изменить статус sc1 на Закрыт</li>
     * <li>Проверить, что связь между sc1 и sc2 не разорвана</li>
     * </ol>
     */
    @Test
    public void testRelationsForProfileWithoutStateAfterChangeState()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);
        //Настраиваем переходы между статусами
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, resumed, closed);
        //Создаем профиль ЖЦ
        WfProfile wfProfile = DAOWfProfile.create(scCase, scCase, true);
        DSLWfProfile.add(wfProfile);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Объекты, необходимые для закрытия запроса
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(team, employee);
        DSLTeam.addEmployees(team, employee);
        CatalogItem closureCode = SharedFixture.closureCode();
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);
        //Выполнение действия и проверки
        DSLMassProblem.addSlave(sc1, sc2);
        DSLMassProblem.assertWfProfile(wfProfile, sc2);
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUISc.changeState(resumed);
        DSLMassProblem.assertSlaves(sc1, sc2);
        GUIBo.goToCard(sc2);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_STATE, GUIButtonBar.BTN_CHANGE_RESPONSIBLE,
                GUIButtonBar.BTN_CHANGE_CASE);
        GUIBo.goToCard(sc1);
        GUISc.close(team, employee, closureCode);
        DSLMassProblem.assertSlaves(sc1, sc2);
        GUIBo.goToCard(sc2);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_STATE, GUIButtonBar.BTN_CHANGE_RESPONSIBLE,
                GUIButtonBar.BTN_CHANGE_CASE);
    }

    /**
     * Тестирование добавления связи между подчиненным запросом с массовым запросом, 
     * находящимся в статусе разрыва связи
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495
     * http://sd-jira.naumen.ru/browse/NSDPRD-1568
     * http://sd-jira.naumen.ru/browse/NSDPRD-1914
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать профиль связанных ЖЦ wfProfile для scCase, статус разрыва связи - "Возобновлен"</li>
     * <li>Создать запросы sc1..2 типа scCase, sc1 сделать массовым</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку sc2</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Добавить массовый запрос sc1</li>
     * <li>Скриптом изменить статус sc1 на Возобновлен</li>
     * <li>Нажать "Сохранить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что на форме появилось корректное сообщение об ошибке</li>
     * <li>Нажать "Отмена"</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Проверить, что среди массовых запросов отсутствует запрос sc1</li>
     * <li>Перейти на карточку sc1</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Добавить подчиненный запрос sc2</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Проверить, что на форме появилось корректное сообщение об ошибке</li>
     * </ol>
     */
    @Test
    public void testRelationWithMassProblemInClosedState()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);

        //Настраиваем переходы между статусами
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, resumed);

        //Создаем профиль ЖЦ
        WfProfile wfProfile = DAOWfProfile.create(scCase, scCase, true, resumed);
        DSLWfProfile.add(wfProfile);

        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();

        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);

        //Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc2);
        GUIButtonBar.massScRegular();
        GUIMassProblem.clickMass(sc1);
        DSLSc.changeState(sc1, resumed);

        //Проверки
        GUIForm.applyFormAssertError(
                String.format(ErrorMessages.WF_NOT_RELATED_STATE, sc1.getTitle(), resumed.getTitle()));
        GUIForm.cancelForm();

        GUIButtonBar.massScRegular();
        GUIMassProblem.assertMassScForRelationAbsence(sc1);
        GUIForm.cancelForm();

        GUIBo.goToCard(sc1);
        GUIButtonBar.massScMass();
        GUIMassProblem.clickMass(sc2);
        GUIForm.applyFormAssertError(
                String.format(ErrorMessages.WF_NOT_RELATED_STATE, sc1.getTitle(), resumed.getTitle()));
    }

    /**
     * Тестирование наследования атрибута "Ответственный"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00494
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase создать статус status, стратегия назначения ответственного - team2/employee2</li>
     * <li>Настроить переходы Зарегистрирован --> status --> Зарегистрирован</li>
     * <li>Создать запросы sc1..2 типа scCase</li>
     * <li>Изменить статус sc1 на status</li>
     * <li>У sc1 назначить ответственного team1/employee1, у sc2 - team2/employee2</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Сделать sc1 массовым, sc2 его подчиненным</li>
     * <li>Проверить, что ответственные у sc1..2 - team1/employee1</li>
     * <li>Изменить ответственного у запроса sc1 на team3/employee3</li>
     * <li>Проверить, что ответственные у sc1..2 - team3/employee3</li>
     * </ol>
     */
    @Test
    public void testResponsibleInheritage()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        DSLMetaClass.add(scCase);
        //Создаем все для запроса
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем объекты для ответственных
        Bo team1 = DAOTeam.create(teamCase);
        Bo team2 = DAOTeam.create(teamCase);
        Bo team3 = DAOTeam.create(teamCase);
        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee3 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(team1, team2, team3, employee1, employee2, employee3);
        DSLTeam.addEmployees(team1, employee1);
        DSLTeam.addEmployees(team2, employee2);
        DSLTeam.addEmployees(team3, employee3);
        DSLMetaClass.addAllResponsibleTransfer(scCase, team1, team2, team3);
        //Создаем статус
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                team2.getUuid(), employee2.getUuid());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(registered, status, registered);
        //Создаем профиль ЖЦ
        WfProfile wfProfile = DAOWfProfile.create(scCase, scCase, true, DAOBoStatus.createResumed(scCase.getFqn()));
        DSLWfProfile.add(wfProfile);
        Attribute responsible = SysAttribute.responsible(scCase);
        DAOWfProfile.addAttrsForInheritanceToModel(wfProfile, responsible);
        DSLWfProfile.edit(wfProfile);
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);
        DSLSc.changeState(sc1, status);
        DSLSc.setResponsible(sc1, team1, employee1);
        DSLSc.setResponsible(sc2, team2, employee2);
        //Выполнение действия и проверки
        DSLMassProblem.addSlave(sc1, sc2);
        //Проверяем, что статус изменился
        DSLSc.assertState(sc2, status);
        //Проверяем, что отвественный изменился
        DSLSc.assertResponsible(sc1, team1, employee1);
        DSLSc.assertResponsible(sc2, team1, employee1);
        //Изменяем отвественного
        DSLSc.setResponsible(sc1, team3, employee3);
        //Проверки
        DSLSc.assertResponsible(sc1, team3, employee3);
        DSLSc.assertResponsible(sc2, team3, employee3);
    }

    /**
     * Тестирование массового запроса с привязкой к услуге
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00494
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запросов scCase1..2</li>
     * <li>Создать запросы sc1..2 типа scCase1 с привязкой к услуге service</li>
     * <li>Сделать sc1 массовым, sc2 его подчиненным</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку sc1</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Снять чекбокс "Массовый запрос", сохранить</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Установить чекбокс "Массовый запрос"</li>
     * <li>Установить запрос sc2 в качестве массового</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что отсутствует кнопка "Изменить тип"</li>
     * <li>Нажать "Изменить привязку"</li>
     * <li>Проверить, что в поле новый тип присутствует только тип запроса scCase1</li>
     * <li>В поле "Соглашение/Услуга" выбрать соглашение, нажать сохранить</li>
     * <li>Соглашение и услуга изменились</li>
     * </ol>
     */
    @Test
    public void testScWithSlmAssociation()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass serviceCase = SharedFixture.slmCase();
        DSLMetaClass.add(scCase1, scCase2);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(ou, ou2, service);
        DSLAgreement.addToRecipients(agreement, ou);
        DSLSlmService.addAgreements(service, agreement);
        DSLSlmService.addScCases(service, scCase1);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем запросы
        Bo sc1 = DAOSc.createWithService(scCase1, ou, agreement, service, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.createWithService(scCase1, ou, agreement, service, timeZoneItem);
        DSLBo.add(sc1, sc2);
        DSLMassProblem.addSlave(sc1, sc2);
        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUIButtonBar.massScMass();
        GUIMassProblem.unsetMassSc();
        GUIForm.applyModalForm();
        GUIMassProblem.setMass(sc2);
        //Проверки
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_CASE);
        GUIButtonBar.changeAssociation();
        GUIForm.assertDialogAppear("Окно изменения привязки не появилось");
        GUISelect.assertSelectWithoutEmpty(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, Lists.newArrayList(scCase1
                        .getTitle()),
                true, true);
        GUISelect.select(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agreement.getUuid());
        GUIForm.applyModalForm();
        DSLSc.assertSlmAndAgreement(sc1, agreement, null);
    }

    /**
     * Тестирование поиска запроса для связывания на форме работы с массовостью
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе scCase настраиваем поиск по атрибуту "Название", тип анализатор: Точный</li>
     * <li>Добавляем три запроса sc1, sc2, sc3 типа scCase без привязки к услуге</li>
     * <li>Делаем sc1 массовым</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Переходим на карточку sc1</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Проверяем, что в блоке "запросы для связывания" будут значиться запросы sc2 и sc3</li>
     * <li>В поле поиска вводим название запроса sc2</li>
     * <li>Проверяем, что в блоке "запросы для связывания" будет значиться только запрос sc2</li>
     * </ol>
     */
    @Test
    public void testSearchOnMassForm()
    {
        // Подготовка
        MetaClass scCase = SharedFixture.scCase();
        DSLSearchSettings.editAttributeSearchable(DAOScCase.createClass(), SysAttribute.title(scCase), true, true,
                false, false, "", Boost.AVERAGE, Analyzer.ACCURATE_ANALYZER);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc3 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2, sc3);
        DSLSearch.updateIndex(sc1, sc2, sc3);
        DSLMassProblem.setMass(sc1);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUIButtonBar.massScMass();
        GUIMassProblem.assertMassScForRelationPresent(sc2, sc3);
        GUIMassProblem.setSearchString(sc2.getTitle());
        GUIMassProblem.clickSearch();
        GUIMassProblem.assertMassScForRelationPresent(sc2);
        GUIMassProblem.assertSlaveScForRelationAbsence(sc3);
    }

    /**
     * Тестирование фильтрации параметра Услуга в контенте "Связанные объекты" на форме добавления запроса в
     * зависимости от заполненности полей Соглашение/Услуга и Тип объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выводим контент "Связанные объекты" на форму добавления запроса</li>
     * <li>В рамках соглашения agreement доступны услуги service1..2</li>
     * <li>В рамках услуг service1..2 доступен тип запроса scCase </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Добавляем запрос</li>
     * <li>На форме добавления запроса поля заполнены: Соглашение/Услуга [не указано] и Тип объекта [нет элементов]</li>
     * <li>В контенте "Связанные объекты" в параметре Услуга значится [не указано]</li>
     * <li>В выпадающем списке есть только [не указано]</li>
     * <li>На форме добавления запроса поля заполняем: Соглашение/Услуга [agreement] и Тип объекта [не указано]</li>
     * <li>В контенте "Связанные объекты" в параметре Услуга значится [не указано]</li>
     * <li>В выпадающем списке есть только [не указано]</li>
     * <li>На форме добавления запроса поля заполняем: Соглашение/Услуга [service1] и Тип объекта [не указано]</li>
     * <li>В контенте "Связанные объекты" в параметре Услуга значится [не указано]</li>
     * <li>В выпадающем списке есть только [не указано]</li>
     * <li>На форме добавления запроса поля заполняем: Соглашение/Услуга [agreement] и Тип объекта [scCase]</li>
     * <li>В контенте "Связанные объекты" в параметре Услуга значится [не указано]</li>
     * <li>В выпадающем списке есть: [не указано], [все], service1, service2</li>
     * <li>На форме добавления запроса поля заполняем: Соглашение/Услуга [service1] и Тип объекта [scCase]</li>
     * <li>В контенте "Связанные объекты" в параметре Услуга значится [service1]</li>
     * <li>В выпадающем списке есть: [не указано], [все], service1, service2</li>
     * </ol>
     */
    @Test
    public void testServiceSelectList()
    {
        // Подготовка
        ContentForm content = DAOContentAddForm.createMassProblems(DAOScCase.createClass(), true, false);
        DSLContent.add(content);
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        Bo agreement = SharedFixture.agreement();
        Bo service1 = DAOService.create(SharedFixture.slmCase());
        Bo service2 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(service1, service2);
        DSLAgreement.addServices(agreement, service1, service2);
        DSLSlmService.addToScCase(scCase, service1, service2);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();

        GUISc.assertEmptyAgreement(true);
        GUISc.assertEmptyScCase(false);
        GUIMassProblem.assertEmptyService();
        GUIMassProblem.assertServiceSelectList(true, false);

        GUISc.selectAssociation(agreement, null);
        GUIMassProblem.assertEmptyService();
        GUIMassProblem.assertServiceSelectList(true, false);

        GUISc.selectAssociation(agreement, service1);
        GUIMassProblem.assertEmptyService();
        GUIMassProblem.assertServiceSelectList(true, false);

        GUISc.selectAssociation(agreement, null);
        GUISc.selectScCase(scCase);
        GUIMassProblem.assertEmptyService();
        GUIMassProblem.assertServiceSelectList(true, true, service1, service2);

        GUISc.selectAssociation(agreement, service1);
        GUISc.selectScCase(scCase);
        GUIMassProblem.assertServicesSelectList(service1);
        GUIMassProblem.assertServiceSelectList(true, true, service1, service2);
    }

    /**
     * Тестирование наследования атрибута "Класс обслуживания" 
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$60611986
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00500
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Установить значения по умолчанию для атрибутов priority,resolutionTime типа запроса scCase</li>
     * <li>Создать элементы serviceTimeA, serviceTimeB справочника Класс обслуживания</li>
     * <li>Создать соглашение agreement со временем поддержки serviceTimeA, временем предоставления услуги
     * serviceTimeA</li>
     * <li>Создать сотрудника employee</li>
     * <li>Добавить соглашение agreement пользователю employee</li>
     * <li>Создать запросы sc1, sc2 типа scCase с привязкой к сотруднику employee, соглашению agreement</li>
     * <li>Изменить класс обслуживания запроса sc2 на serviceTimeB с помощью скрипта</li>
     * <br>
     * <b>Действия</b>
     * <li>Зайти под суперпользователем </li>
     * <li>Перейти на карточку системного профиля ЖЦ "Запрос, связанный с массовым"</li>
     * <li>Нажать "Редактировать" на профиле ЖЦ "Запрос, связанный с массовым"</li>
     * <li>Добавить для наследования атрибут Класс обслуживания</li>
     * <li>Перейти на карточку запроса sc1</li>
     * <li>Сделать запрос sc1 массовым и добавить подчиненный запрос sc2</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что значением атрибута "Подчиненные запросы" запроса sc1 является запрос sc2</li>
     * <li>Проверить, что значением атрибута "Массовый запрос" запроса sc2 является запрос sc1</li> 
     * <li>Проверить, что значением атрибута "Класс обслуживания" запроса sc2 является класс обслуживания
     * serviceTimeA</li>
     * </ol>
     */
    @Test
    public void testServiceTimeInheritage()
    {
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(scCase);

        CatalogItem priorityItem = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(priorityItem);
        DSLAttribute.setDefaultValue(scCase, "priority", priorityItem.getUuid());
        DSLAttribute.setDefaultValue(scCase, "resolutionTime", AttributeUtils.prepareTimeInterval("100", "HOUR"));

        CatalogItem serviceTimeA = DAOCatalogItem.createServiceTime();
        CatalogItem serviceTimeB = DAOCatalogItem.createServiceTime();
        DSLCatalogItem.add(serviceTimeA, serviceTimeB);

        Bo agreement = DAOAgreement.create(SharedFixture.agreementCase(), serviceTimeA, serviceTimeA);
        DSLBo.add(agreement);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(agreement, employee);

        Bo sc1 = DAOSc.create(scCase, employee, agreement, SharedFixture.timeZone());
        Bo sc2 = DAOSc.create(scCase, employee, agreement, SharedFixture.timeZone());
        DSLBo.add(sc1, sc2);

        String scriptText = String.format(
                "def serviceCall = utils.get('%s');utils.edit (serviceCall, ['serviceTime' : '%s']);", sc2.getUuid(),
                serviceTimeB.getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();

        Attribute serviceTime = DAOAttribute.createPseudo("Класс обслуживания", "serviceTime", null);
        WfProfile wfProfile = DAOWfProfile.createSystemScProfile();
        Cleaner.afterTest(() ->
        {
            DSLWfProfile.edit(wfProfile);
            DSLAttribute.delete(serviceTime);
        });

        //Действия
        GUILogon.asSuper();
        GUIWfProfile.goToCard(wfProfile);
        GUIWfProfile.clickEditAttributes();
        GUIGroupAttr.addAttributesOnEditform(serviceTime);
        GUIForm.applyModalForm();

        GUIBo.goToCard(sc1);
        GUIMassProblem.setMass(sc2);

        //Проверки
        DSLMassProblem.assertSlaves(sc1, sc2);
        DSLMassProblem.assertMass(sc1, sc2);
        serviceTime.setValue(serviceTimeA.getUuid());
        DSLBo.assertCatalogItemAttr(sc2, serviceTime);
    }

    /**
     * Тестирование отображения на форме работы с массовостью массовых запросов, которые находятся в статусе,
     * отличном от Зарегистрирован
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495
     * http://sd-jira.naumen.ru/browse/NSDPRD-2971
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать профиль связанных ЖЦ wfProfile (scCase, scCase, null)</li>
     * <li>Создать запросы sc1..2 типа scCase</li>
     * <li>Сделать sc1 массовым</li>
     * <li>Сделать sc2 подчиненным sc1</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Изменить статус sc1 на Возобновлен</li>
     * <li>Переходим на карточку sc2</li>
     * <li>Открывает форму для работы с массовостью</li>
     * <li>Проверяем, что в блоке "Массовые запросы для связывания" присутствуют sc1</li>
     * </ol>
     */
    @Test
    public void testShowMassSCsAfterChangeState()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase1, scCase2);
        //Настраиваем переходы между статусами
        BoStatus registered = DAOBoStatus.createRegistered(scCase1.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase1.getFqn());
        DSLBoStatus.setTransitions(registered, resumed);
        //Создаем профиль ЖЦ
        WfProfile wfProfile = DAOWfProfile.create(scCase1, scCase1, true);
        DSLWfProfile.add(wfProfile);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase1, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase1, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);

        //Выполнение действия и проверки.
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUISc.changeState(resumed);

        GUIBo.goToCard(sc2);
        GUIButtonBar.massScRegular();
        GUIMassProblem.assertMassScForRelationPresent(sc1);
    }

    /**
     * Тестирование сортировки на форме работы с массовостью
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
     * http://sd-jira.naumen.ru/browse/NSDPRD-1896
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCasе, правило формирования номера - {YYYY}{N}</li>
     * <li>Создать запрос sc1 типа scCase</li>
     * <li>В scCase изменить правило формирования номера - {N}</li>
     * <li>Создать запрос sc2 типа scCase</li>
     * <li>В scCase изменить правило формирования номера - {YYYY}{N}</li>
     * <li>Создать запросы sc3..4 типа scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку sc4</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Установить чекбокс "Массовый запрос"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в списке Запросы для связывания присутствуют запросы в указанном порядке: sc3, sc1, sc2</li>
     * </ol>
     */
    @Test
    public void testSortingOnMassForm()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass agrCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase, ouCase, agrCase);

        Attribute numberAttr = SysAttribute.number(scCase);
        numberAttr.setGenerationRule("{YYYY}{N}");
        DSLAttribute.edit(numberAttr);

        //Создаем все для запросов
        Bo agreement = DAOAgreement.createWithRules(agrCase, SharedFixture.serviceTime(), SharedFixture.serviceTime(),
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();

        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc3 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc4 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1);

        numberAttr.setGenerationRule("{N}");
        DSLAttribute.edit(numberAttr);
        DSLBo.add(sc2);

        numberAttr.setGenerationRule("{YYYY}{N}");
        DSLAttribute.edit(numberAttr);
        DSLBo.add(sc3, sc4);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(sc4);
        GUIButtonBar.massScRegular();
        GUIMassProblem.setMassSc();

        //Проверки
        List<String> actual = GUIMassProblem.getMassCalls();
        List<String> expected = ImmutableList.of(sc3.getUuid(), sc1.getUuid(), sc2.getUuid());
        Assert.assertEquals("Полученный список запросов не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Тестирование удаления массового запроса, имеющего подчинённые запросы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00199
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать два запроса sc1 и sc2, одинакого типа</li>
     * <li>Запрос sc1 сделать массовым</li>
     * <li>К массовому запросу sc1 привязать подчиненный запрос sc2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить массовый запрос sc1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Запрос sc1 не удалился, есть сообщение об ошибке: 
     * Запрос '%название%' не может быть удален по следующим причинам: 
     * 1. Запрос связан со следующими объектами: запрос '%название подчиненного запроса%'.</li>
     * </ol>
     */
    @Test
    public void testTryToDeleteMassScHavingSubSc()
    {
        // Подготовка
        Bo sc1 = DAOSc.create();
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create();
        DSLBo.add(sc1, sc2);

        DSLMassProblem.addSlave(sc1, sc2);

        String expectedMessage = ErrorMessages.SC + "'" + sc1.getTitle() + "' " + ErrorMessages.NOT_BE_DEL + "\n"
                                 + ErrorMessages.FIRST + ErrorMessages.SC + ErrorMessages.HAS_REL_OBJS
                                 + ErrorMessages.SC.toLowerCase()
                                 + "'" + sc2.getTitle() + "'.";

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(sc1);

        // Проверка
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(sc1, expectedMessage);
        DSLBo.assertPresent(sc1);
    }

    /**
     * Тестирование работы двух профилей ЖЦ для одного типа запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00494
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать профиль связанных ЖЦ wfProfile1 (scCase, scCase, Возобновлен)</li>
     * <li>Создать профиль связанных ЖЦ wfProfile2 (scCase, scCase, Возобновлен)</li>
     * <li>Создать запросы sc1..2 типа scCase</li>
     * <li>Сделать sc1 массовым</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Сделать sc2 подчиненным sc1</li>
     * <br>
     * <b>Проверки</b>
     * <li>У sc2 в атрибуте Код профиля связанных жизненных циклов - systemWfProfile1</li>
     * </ol>
     */
    @Test
    public void testTwoProfilesForSameCase()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);
        //Настраиваем переходы между статусами
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, resumed);
        //Создаем профиль ЖЦ
        WfProfile wfProfile1 = DAOWfProfile.create(scCase, scCase, true, resumed);
        WfProfile wfProfile2 = DAOWfProfile.create(scCase, scCase, true, resumed);
        DSLWfProfile.add(wfProfile1, wfProfile2);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        //Создаем запросы
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);
        //Выполнение действия
        DSLMassProblem.addSlave(sc1, sc2);
        //Проверки
        DSLMassProblem.assertWfProfile(DAOWfProfile.createSystemScProfile(), sc2);
    }

    /**
     * Тестирование валидности списка запросов для связывания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase, тип услуги slmCase</li>
     * <li>Создать 2 услуги service1..2 типа slmCase, связать их с соглашением agreement</li>
     * <li>Создать запросы sc1..4 типа scCase с привязкой к agreement/service1, sc5 с привязкой к
     * agreement/service2</li>
     * <li>Сделать sc1, sc2 массовыми, закрыть sc3, связать sc4 с sc2, как подчиненный запрос</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти в карточку запроса sc1</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке "Подчиненные запросы" - пусто, в списке "Запросы для связывания" - пусто</li>
     * </ol>
     */
    @Test
    public void testValidationScListForRelation()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass slmCase = SharedFixture.slmCase();
        DSLMetaClass.add(scCase);
        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo team = SharedFixture.team();
        Bo service1 = DAOService.create(slmCase);
        Bo service2 = DAOService.create(slmCase);
        DSLBo.add(ou, service1, service2);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);

        DSLAgreement.addServices(agreement, service1, service2);
        DSLSlmService.addScCases(service1, scCase);
        DSLSlmService.addScCases(service2, scCase);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();
        CatalogItem closureCode = SharedFixture.closureCode();
        //Создаем запросы
        Bo sc1 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        sc1.setScMassProblem(String.valueOf(true));
        Bo sc2 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        sc2.setScMassProblem(String.valueOf(true));
        Bo sc3 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        Bo sc4 = DAOSc.createWithService(scCase, ou, agreement, service1, timeZoneItem);
        Bo sc5 = DAOSc.createWithService(scCase, ou, agreement, service2, timeZoneItem);
        DSLBo.add(sc1, sc2, sc3, sc4, sc5);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, closed);
        DSLSc.close(sc3, team, employee, closureCode);
        DSLMassProblem.addSlave(sc2, sc4);
        //Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc1);
        GUIButtonBar.massScMass();

        GUIMassProblem.assertMassCheckbox(true);
        GUIMassProblem.assertSlaveScAbsence(sc1, sc2, sc3, sc4, sc5);
        GUIMassProblem.assertSlaveScForRelationAbsence(sc1, sc2, sc3, sc4, sc5);
    }

    /**
     * Тестирование получения в одной транзакции обновленного списка massProblemSlaves после обнуления атрибута
     * masterMassProblem у подчиненного запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00494
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00502
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$133306292
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase, тип услуги slmCase</li>
     * <li>Создать услугу service типа slmCase, связать ее с соглашением agreement</li>
     * <li>Создать запросы sсSlave, sсMaster типа scCase с привязкой к agreement/service</li>
     * <li>Сделать sсMaster массовым, sсSlave связать c sсMaster, как подчиненный запрос</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт эмулирующий проблему чтения значения атрибута masterMassProblem после его изменения:
     * <p>
     *     Суть воспроизведения проблемы скриптом: в рамках одной транзакции выполнить последовательно:
     *     - переключить режим в "только чтение"
     *     - прочитать все значения поля massProblemSlaves у объекта scMaster
     *     - прочитать одно значение поля massProblemSlaves у объекта scMaster
     *     - переключить режим в "запись"
     *     - присвоить полю masterMassProblem объекта sсSlave значение null
     *     - переключить режим в "только чтение"
     *     - прочитать все значения поля massProblemSlaves у объекта scMaster
     *     - прочитать одно значение поля massProblemSlaves у объекта scMaster
     * </p>
     * <pre>
     *                 import ru.naumen.core.server.jta.TransactionRunner;
     *                 import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
     *                 def commonUtils = beanFactory.getBean('commonUtils');
     *                 def accessorHelper = beanFactory.getBean('accessorHelper');
     *                 def masterSc;
     *                 def slaveSc;
     *                 TransactionRunner.call(TransactionType.READ_ONLY, {
     *                      masterSc = commonUtils.getByUUID('%s');
     *                      slaveSc = commonUtils.getByUUID('%s');
     *                      accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves');
     *                      accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves', 1);
     *                 });
     *                 TransactionRunner.call({
     *                      def props = new ru.naumen.common.shared.utils.MapProperties();
     *                      props.setProperty('masterMassProblem', null);
     *                      commonUtils.edit(slaveSc, props);
     *                 });
     *                 TransactionRunner.call(TransactionType.READ_ONLY, {
     *                      def slaves = accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves');
     *                      def singleSlave = accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves', 1);
     *                      if (singleSlave) slaves<<singleSlave[0];
     *                      return singleSlave;
     *                 });"
     * </pre></li>
     * <li>Проверить, что результат выполнения скрипта - пустой массив</li>
     * </ol>
     */
    @Test
    public void testGetFreshSlavesAfterUpdateMasterInSameTransaction()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass slmCase = SharedFixture.slmCase();
        DSLMetaClass.add(scCase);

        //Создаем все для запросов
        Bo agreement = SharedFixture.agreement();
        Bo ou = DAOOu.create(ouCase);
        Bo team = SharedFixture.team();
        Bo service = DAOService.create(slmCase);
        DSLBo.add(ou, service);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);

        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase);
        DSLAgreement.addToRecipients(agreement, ou);
        CatalogItem timeZoneItem = SharedFixture.timeZone();

        //Создаем массовый запрос и подчиненный
        Bo masterSc = DAOSc.createWithService(scCase, ou, agreement, service, timeZoneItem);
        masterSc.setScMassProblem(String.valueOf(true));
        Bo slaveSc = DAOSc.createWithService(scCase, ou, agreement, service, timeZoneItem);
        DSLBo.add(slaveSc, masterSc);
        DSLMassProblem.addSlave(masterSc, slaveSc);

        //Действия и проверки
        String script = "import ru.naumen.core.server.jta.TransactionRunner;"
                        + "import ru.naumen.core.server.jta.TransactionRunner.TransactionType;"
                        + "def commonUtils = beanFactory.getBean('commonUtils');"
                        + "def accessorHelper = beanFactory.getBean('accessorHelper');"
                        + "def masterSc;"
                        + "def slaveSc;"
                        + "TransactionRunner.call(TransactionType.READ_ONLY, {"
                        + "     masterSc = commonUtils.getByUUID('%s');"
                        + "     slaveSc = commonUtils.getByUUID('%s');"
                        + "     accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves');"
                        + "     accessorHelper.getAttributeValueWithoutPermission(masterSc, 'massProblemSlaves', 1);"
                        + "});"
                        + "TransactionRunner.call({"
                        + "     def props = new ru.naumen.common.shared.utils.MapProperties();"
                        + "     props.setProperty('masterMassProblem', null);"
                        + "     commonUtils.edit(slaveSc, props);"
                        + "});"
                        + "TransactionRunner.call(TransactionType.READ_ONLY, {"
                        + "     def slaves = accessorHelper.getAttributeValueWithoutPermission(masterSc, "
                        + "'massProblemSlaves');"
                        + "     def singleSlave = accessorHelper.getAttributeValueWithoutPermission(masterSc, "
                        + "'massProblemSlaves', 1);"
                        + "     if (singleSlave) slaves<<singleSlave[0];"
                        + "return singleSlave;"
                        + "});";
        String s = new ScriptRunner(String.format(script, masterSc.getUuid(), slaveSc.getUuid())).runScript().get(0);
        Assert.assertEquals("[]", s);
    }

    /**
     * Тестирование доступности формы управления массовостью, если при связывании запросов
     * выбранный запрос не перенесен в подчиненные, и нажата кнопка "Вернуться".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00495 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252231233 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать запросы sc - массовый, sc2</li>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником и перейти на карточку sc</li>
     * <li>Нажать ссылку "Работа с массовостью"</li>
     * <li>В списке запросов "Доступные для связывания" установить чекбокс для запроса sc2</li>
     * <li>Проверить, что появилось предупреждение: “В блоке "Доступные для добавления" отмечены
     * объекты (1), над которыми не произведено действие переноса. Если вы продолжите сохранение,
     * отмеченные объекты останутся в блоке "Доступные для добавления". Для того чтобы перенести объекты,
     * вернитесь на форму, нажмите "перенести к выбранным" и снова нажмите "Сохранить.</li>
     * <li>Нажать кнопку "Вернуться"</li>
     * <li>Проверить, что на форме отсутствует блокирующий экран</li>
     * </ol>
     */
    @Test
    public void testAvailableMassScFormIfSelectedCallNotTransferredToAvailableCallsAndReturnButtonIsPressed()
    {
        // Подготовка
        MetaClass scCase = SharedFixture.scCase();

        Bo sc = DAOSc.create(scCase);
        Bo sc2 = DAOSc.create(scCase);
        sc.setScMassProblem(Boolean.TRUE.toString());
        DSLBo.add(sc, sc2);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.massScMass();
        GUIMassProblem.advListAvailableSC().mass().selectElements(sc2);
        GUIForm.applyFormAndAssertDialog(ConfirmMessages.AVAILABLE_SELECTED_OBJECTS_ARE_NOT_DEPENDENT, 1);
        GUIMassProblem.clickReturnToForm();
        GUIForm.assertFormBlockerAbsence();
    }
}
