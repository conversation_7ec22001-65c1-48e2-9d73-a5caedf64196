package ru.naumen.selenium.cases.operator.classes.employee;

import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.openqa.selenium.WebElement;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование ссылочной целостности в атрибутах связи, где используется сотрудник
 * <AUTHOR>
 * @since 15.05.2014
 */
public class EmployeeIntegrityRelations1Test extends AbstractTestCase
{
    private static Attribute licenseAttr = DAOAttribute.createPseudo("Лицензия", "license", null);

    /**
     * Тестирование помещения в архив и удаления сотрудника со
     * связями:ответственный за состояние
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <ol>
     * <b>Подготовка.</b>
     * <li>employee типа employeeCase</li>
     * <li>Создать статус запроса status типа запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся поместить employee в архив</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть помещен в архив. Сотрудник используется при назначении ответственных
     * для статусов: %список статусов, для каждого в скобках указывается метакласс%"
     * </li>
     * <li>Карточка employee - признак архивирования - "нет"</li>
     * <li>Карточка status - ответственный за состояние employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник назначается ответственным в статусах: %список
     * статусов, для каждого в скобках указывается метакласс%"
     * </li>
     * </ol>
     */
    @Test
    public void testArchiveDeleteEmployeeStatusRelation()
    {
        // Подготовка
        // Создаем метаклассы
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass teamCase = SharedFixture.teamCase();
        DSLMetaClass.add(scCase);
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(employee);
        // Создаем команду
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);
        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);
        // Создаем статус
        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE, team.getUuid(),
                employee.getUuid());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()), status,
                DAOBoStatus.createResolved(scCase.getFqn()));
        // Проверка статуса
        GUILogon.asSuper();
        GUIBoStatus.goToStatusCardWithRefresh(status);
        GUISelect.scrollSelectList();
        List<WebElement> actual = tester.findDisplayedElements(
                "//div[@id='gwt-debug-value' and contains(., '%s / %s')]", DSLEmployee.getFullName(employee),
                team.getTitle());
        Assert.assertEquals("Не найден отвественный сотрудник.", 1, actual.size());
        // Выполнение
        GUILogon.asTester();

        // Проверки
        String expectedMessages = ErrorMessages.EMP
                                  + String.format("'%s' %s\n", DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_REM)
                                  + ErrorMessages.FIRST + String.format(
                ErrorMessages.EMPLOYEE_IS_RESPONSIBLE_FOR_STATE + "%s (%s)",
                status.getTitle(), scCase.getTitle());

        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertNotArchived(employee);

        expectedMessages = String.format(ErrorMessages.EMPLOYEE_IS_RESPONSIBLE_FOR_STATE + "%s (%s)", status.getTitle(),
                scCase.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);

    }

    /**
     * Тестирование помещения в архив сотрудника со связями:поставщик соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать agreement типа agreementCase</li>
     * <li>Назначить employee поставщиком соглашения</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся поместить employee в архив</li>
     * <br>
     * <b>Проверки</b>wa
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть помещен в архив. Сотрудник связан со следующими объектами: %список
     * объектов%"
     * </li>
     * <li>employee не в архиве</li>
     * <li>agreement поставщик - employee</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeAgreementRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agrCase = SharedFixture.agreementCase();
        // Создаем сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        // Добавляем класс обслуживания
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        // Создаем соглашение
        Bo agreement = DAOAgreement.create(agrCase, serviceTimeItem, serviceTimeItem);
        DSLBo.add(agreement);
        // Создаем атрибут
        Attribute supplierAttr = DAOAttribute.createPseudo("Поставщик (сотрудник)", "supplierEmpoyee", null);
        // Связывание
        supplierAttr.setValue(employee.getUuid());
        DSLBo.editAttributeValue(agreement, supplierAttr);
        // Выполнение
        GUILogon.asTester();
        // Проверки
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.HAS_REL_OBJS, agreement.getTitle());

        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);

        DSLBo.assertNotArchived(employee);

        ModelMap map = SdDataUtils.getObject(agreement.getMetaclassFqn(), ModelUuid.UUID, agreement.getUuid());
        String message = String.format("Атрибут БО с uuid-ом '%s' имеет другое значение.", agreement.getUuid());
        String title = Json.GSON.fromJson(map.get("supplierEmpoyee"), ModelMap.class).get("title");
        Assert.assertEquals(message, DSLEmployee.getFullName(employee), title);
    }

    /**
     * Архивирование сотрудника, использующегося в таблицах соответствий эскалаций
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>В классе Запрос scClass добавить атрибут attrBoLink типа Ссылка на БО (класс Сотрудник employeeClass)</li>
     * <li>Добавить схему эскалации escalation (Объекты: Запрос, Счетчик времени: Запас нормативного времени
     * обслуживания)</li>
     * <li>Добавить Таблицу соответствий схем эскалаций rulesSettings (Объекты: Запрос, Определяющий атрибут:
     * attrBoLink)</li>
     * <li>Добавить строку row в таблицу соответствий rulesSettings: escalation-employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под лицензированным сотрудником.</li>
     * <li>Зайти на карточку сотрудника employee. Нажать "Поместить в архив". Подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не в архиве, есть сообщение об ошибке:
     * Сотрудник '%empl%' не может быть помещен в архив по следующим причинам: 1. Сотрудник используется в качестве
     * значения в таблицах соответствия: %название_тс%.</li>
     * <br>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeInEscalationRulesSettings()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass employeeClass = DAOEmployeeCase.createClass();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        Attribute attrBoLink = DAOAttribute.createObjectLink(scClass, employeeClass, null);
        DSLAttribute.add(attrBoLink);

        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, scClass);
        DSLEscalation.add(escalation);

        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scClass, attrBoLink.getCode());
        DSLCatalogItem.add(rulesSettings);

        RsRow row = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalation.getCode(),
                attrBoLink.getCode(), employee.getUuid());
        DSLRsRows.addRowToRSItem(row);

        // Действие
        GUILogon.asTester();

        // Проверка
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_REM, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.USED_IN_RS,
                rulesSettings.getTitle());
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование помещения в архив сотрудника со связями:контрагент запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать запрос sc типа scCase (employee контрагент)</li>
     * <li>Назначить employee ответственным за запрос</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся поместить employee в архив</li>
     * <br>
     * <b>Проверки</b>
     * <li>employee не в архиве</li>
     * <li>Появилось сообщение об ошибке</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeScClientRelation()
    {
        // Подготовка
        // Создаем метаклассы
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();
        // Создаем элементы справочников
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("America/Jujuy");
        DSLCatalogItem.add(timeZoneItem);
        // Создаем отдел и сотрудника
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        // Создаем соглашение
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, employee);
        // Создание запроса
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);
        // Выполнение
        GUILogon.asTester();
        // Проверки
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_REM, ErrorMessages.FIRST, ErrorMessages.HAS_SER_CALL_CL);
        expectedMessages.add("запрос '" + sc.getTitle() + "'");
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование помещения в архив сотрудника со связями:ответственный за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <ol>
     * <b>Подготовка.</b>
     * <li>employee типа employeeCase</li>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Назначить employee ответственным за запрос</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся поместить employee в архив</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть помещен в архив. Сотрудник является ответственным за открытые
     * запросы: %список запросов%"
     * </li>
     * <li>Карточка employee - признак архивирования - "нет"</li>
     * <li>Карточка sc - "Ответственный (сотрудник)" employee</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeScResponsibleRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        // Создаем элементы справочников
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("America/Jujuy");
        DSLCatalogItem.add(timeZoneItem);
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        // Создаем команду
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(employee, team);
        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);
        // Создаем соглашение
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, employee);
        // Создание запроса
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);
        DSLBo.add(sc);
        // Назначаем отвественным
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUISc.setResponsible(team.getUuid() + ":" + employee.getUuid());
        // Выполнение

        // Проверки
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_REM, ErrorMessages.HAS_SER_CALL_CL);
        expectedMessages.add("запрос '" + sc.getTitle() + "'");
        expectedMessages.add(ErrorMessages.SECOND);
        expectedMessages.add(ErrorMessages.IS_RESPONSIBLE_OF_OPEN_SC);
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);

        DSLBo.assertNotArchived(employee);

        ModelMap map = SdDataUtils.getObject(sc.getMetaclassFqn(), ModelUuid.UUID, sc.getUuid());
        String message = String.format("Атрибут БО с uuid-ом '%s' имеет другое значение.", sc.getUuid());
        String title = Json.GSON.fromJson(map.get("responsibleEmployee"), ModelMap.class).get("title");
        Assert.assertEquals(message, DSLEmployee.getFullName(employee), title);
    }

    /**
     * Тестирование помещения в архив сотрудника со связями:куратор услуги
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <ol>
     * <b>Подготовка.</b>
     * <li>employee типа employeeCase</li>
     * <li>Создать service типа slmCase</li>
     * <li>Назначить employee куратором service</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся поместить employee в архив</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть помещен в архив. Сотрудник связан со следующими объектами: %список
     * объектов%"
     * </li>
     * <li>Карточка employee - признак архивирования - "нет"</li>
     * <li>Карточка service - "Куратор услуги" employee</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeServiceRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass serviceCase = SharedFixture.slmCase();
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        // Создаем услугу
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(service);
        // Связывание
        service.setSlmResponsibleEmpl(employee.getUuid());
        DSLSlmService.edit(service);
        // Выполнение
        GUILogon.asTester();

        // Проверки
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_REM, ErrorMessages.HAS_REL_OBJS, service.getTitle());

        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessages);

        DSLBo.assertNotArchived(employee);

        ModelMap map = SdDataUtils.getObject(service.getMetaclassFqn(), ModelUuid.UUID, service.getUuid());
        String message = String.format("Атрибут БО с uuid-ом '%s' имеет другое значение.", service.getUuid());
        String title = Json.GSON.fromJson(map.get("responsibleEmployee"), ModelMap.class).get("title");
        Assert.assertEquals(message, DSLEmployee.getFullName(employee), title);
    }

    /**
     * Тестирование изменений лицензии и архивирования сотрудника со связями:
     * последний исполнитель в ответственной за запрос команде
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358 <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase, тип сотрудника employeeCase, тип команды
     * teamCase, тип запрос scCase</li>
     * <li>В employeeCase создаем группу атрибутов attrGroup, добавляем в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать ou типа ouCase, team типа teamCase</li>
     * <li>Создать employee1(без лицензии), employee2(с лицензией)</li>
     * <li>Связать employee1, employee2 с team</li>
     * <li>Создать sc типа scCase</li>
     * <li>Назначить ответственного за sc команду team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Переходим в карточку employee2</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Тип лицензии не может быть изменен. Сотрудник
     * является единственным исполнителем в команде %Название команды%,
     * ответственной за объекты: %список запросов%"</li>
     * <li>Пытаемся поместить employee2 в архив</li>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть помещен
     * в архив. Сотрудник является единственным исполнителем в команде %Название
     * команды%, ответственной за объекты: %список запросов%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseAndArchiveEmployeeLastPerformerResponsibleTeam()
    {
        // Создаем типы
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();
        // Создаем группу атрибутов
        String caseFqn = employeeCase.getFqn();
        GroupAttr attrGroup = DAOGroupAttr.create(caseFqn);
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(caseFqn, attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем элементы справочников
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("Chile/Continental");
        DSLCatalogItem.add(timeZoneItem);
        // Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);
        Bo employee1 = DAOEmployee.create(employeeCase, ou, false);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2);
        DSLTeam.addEmployees(team, employee1, employee2);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);
        // Назначаем ответственного за запроса
        sc.setScResponsibleTeamUuid(team.getUuid());
        DSLSc.edit(sc);
        // Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee2);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        // Проверки
        // Смена лицензии
        String expected = String
                .format(ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                        + String.format(ErrorMessages.IS_ONE_PERFORMER_IN_TEAM, employee2.getTitle(), team.getTitle())
                        + "%s '%s'", employee2.getTitle(), SystemClass.SERVICECALL.getTitle(), sc.getTitle());

        GUIForm.applyFormAssertError(expected);
        // Архивирование
        GUIBo.goToCard(employee2);
        GUIButtonBar.remove();
        GUIForm.assertQuestionAppear("Форма подтверждения архивирования не появилась.");
        GUIForm.clickYes();
        String expected2 = String
                .format(ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_REM + "\n" + ErrorMessages.FIRST
                        + String.format(ErrorMessages.IS_ONE_PERFORMER_IN_TEAM, employee2.getTitle(), team.getTitle())
                        + "%s '%s'", employee2.getTitle(), SystemClass.SERVICECALL.getTitle(), sc.getTitle());
        GUIForm.assertErrorMessageOnForm(expected2);
    }

    /**
     * Тестирование изменений лицензии сотрудника со связями: последний
     * исполнитель в ответственной за несколько статусов команде
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип сотрудника employeeCase, тип запроса scCase, тип команды
     * teamCase</li>
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать employee типа employeeCase, команду team типа teamCase</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState1 в классе scCase</li>
     * <li>Создать статус testState2 в классе scCase</li>
     * <li>Назначить team ответственной за статусы testState1, testState2 в scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть изменен. Сотрудник
     * является единственным исполнителем в команде %Название команды%, которая
     * назначается ответсвенной в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseEmployeeLastPerformerResponsibleSomeStatus()
    {
        // Создаем типы
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, team);

        DSLTeam.addEmployees(team, employee);
        // Создаем свои статусы
        BoStatus testState1 = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.TEAM, team.getUuid(), null);
        BoStatus testState2 = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.TEAM, team.getUuid(), null);
        DSLBoStatus.add(testState1, testState2);
        // Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        String expected = String.format(
                ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' " + ErrorMessages.NOT_BE_CHNG + "\n"
                + ErrorMessages.FIRST + ErrorMessages.EMPLOYEE_IS_ONE_PERFORMER_IN_TEAM_RESPONSIBLE_FOR_STATE
                + "%s (%s), %s (%s)",
                team.getTitle(), testState1.getTitle(), scCase.getTitle(), testState2.getTitle(), scCase.getTitle());
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование изменений лицензии сотрудника со связями: последний
     * исполнитель в команде, ответственной за несколько статусов с одинаковыми названиями 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип сотрудника employeeCase, тип запроса scCase, тип команды
     * teamCase</li>
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать employee типа employeeCase, команду team типа teamCase</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState1 c названием "state" в классе scCase</li>
     * <li>Создать статус testState2 c названием "state" в классе scCase</li>
     * <li>Назначить team ответственной за статусы testState1, testState2 в scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть изменен. Сотрудник
     * является единственным исполнителем в команде %Название команды%, которая
     * назначается ответсвенной в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseEmployeeLastPerformerResponsibleSomeStatusWithSameName()
    {
        // Создаем типы
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, team);

        DSLTeam.addEmployees(team, employee);
        // Создаем свои статусы
        BoStatus testState1 = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.TEAM, team.getUuid(), null);
        BoStatus testState2 = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.TEAM, team.getUuid(), null);
        testState1.setTitle("state");
        testState2.setTitle("state");
        DSLBoStatus.add(testState1, testState2);
        // Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        String expected = String.format(
                ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' " + ErrorMessages.NOT_BE_CHNG + "\n"
                + ErrorMessages.FIRST + ErrorMessages.EMPLOYEE_IS_ONE_PERFORMER_IN_TEAM_RESPONSIBLE_FOR_STATE
                + "%s (%s), %s (%s)",
                team.getTitle(), testState1.getTitle(), scCase.getTitle(), testState1.getTitle(), scCase.getTitle());
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование изменений лицензии сотрудника со связями: последний
     * исполнитель в ответственной за статус команде
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип сотрудника employeeCase, тип запроса scCase, тип команды
     * teamCase</li>
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать employee типа employeeCase, команду team типа teamCase</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState в классе scCase</li>
     * <li>Назначить team ответственной за статус testState в scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть изменен. Сотрудник
     * является единственным исполнителем в команде %Название команды%, которая
     * назначается ответсвенной в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseEmployeeLastPerformerResponsibleStatus()
    {
        // Создаем типы
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, team);

        DSLTeam.addEmployees(team, employee);
        // Создаем свой статус и настраиваем текущие
        BoStatus testState = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.TEAM, team.getUuid(), null);
        DSLBoStatus.add(testState);

        // Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        String expected = String.format(ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' "
                                        + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                                        + ErrorMessages.EMPLOYEE_IS_ONE_PERFORMER_IN_TEAM_RESPONSIBLE_FOR_STATE
                                        + "%s (%s)",
                team.getTitle(),
                testState.getTitle(), scCase.getTitle());
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование смены лицензии пользователя concurrent → нелиц: единственный исполнитель в команде, ответственной
     * в статусе пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass с назначением ответственного и ЖЦ</li>
     * <li>Создать тип сотрудника employeeCase и тип команды teamCase</li>
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать сотрудника employee с лицензией concurrent (конкурентной)</li>
     * <li>Создать команду team</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState в классе userClass</li>
     * <li>Назначить team ответственным за статус testState</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Получаем сообщение: "Сотрудник %ФИО сотрудника% не может быть изменен по следующим причинам:
     * Сотрудник 'является единственным исполнителем в команде %название команды%, которая назначается ответственной
     * в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * <li>Нажимаем Отмена</li>
     * <li>Выключить testState</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на "Нелицензированный пользователь"</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Получам сообщение: "Сотрудник %ФИО сотрудника% не может быть изменен по следующим причинам:
     * Сотрудник 'является единственным исполнителем в команде %название команды%, которая назначается ответственной
     * в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * </ol>
     **/
    @Test
    public void testChangeLicenseEmployeeLastPerformerResponsibleStatusInUserClass()
    {
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        // Создаем команду
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(employee, team);
        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);
        // Создаем статус
        BoStatus testState = DAOBoStatus.createUserStatus(userClass.getFqn(), ResponsibleType.TEAM, team.getUuid(),
                null);
        DSLBoStatus.add(testState);
        // Проверка статуса
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();

        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");

        String expected = String.format(ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' "
                                        + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                                        + ErrorMessages.EMPLOYEE_IS_ONE_PERFORMER_IN_TEAM_RESPONSIBLE_FOR_STATE
                                        + "%s (%s)",
                team.getTitle(),
                testState.getTitle(), userClass.getTitle());
        GUIForm.applyFormAssertError(expected);
        GUIForm.cancelForm();

        DSLBoStatus.enableBoStatus(false, testState);
        tester.refresh();
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование изменений лицензии сотрудника со связями:
     * последний исполнитель в команде, ответственной за несколько запросов с одинаковым названием 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358 
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase, тип сотрудника employeeCase, тип команды
     * teamCase, тип запрос scCase</li>
     * <li>В employeeCase создаем группу атрибутов attrGroup, добавляем в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать ou типа ouCase, team типа teamCase</li>
     * <li>Создать employee(без лицензии)</li>
     * <li>Связать employee с team</li>
     * <li>Создать sc1 типа scCase с названием "serviceCall"</li>
     * <li>Создать sc2 типа scCase с названием "serviceCall"</li>
     * <li>Назначить ответственного за sc1 и sc2 команду team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Тип лицензии не может быть изменен. Сотрудник
     * является единственным исполнителем в команде %Название команды%,
     * ответственной за объекты: %список запросов%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseEmployeeLastPerformerResponsibleTeamForSomeScSameName()
    {
        // Создаем типы
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();
        // Создаем группу атрибутов
        String caseFqn = employeeCase.getFqn();
        GroupAttr attrGroup = DAOGroupAttr.create(caseFqn);
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(caseFqn, attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем элементы справочников
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("Chile/Continental");
        DSLCatalogItem.add(timeZoneItem);
        // Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);
        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1);
        DSLTeam.addEmployees(team, employee1);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        Bo sc2 = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc1, sc2);

        // Назначаем ответственного за запроса
        sc1.setScResponsibleTeamUuid(team.getUuid());
        sc2.setScResponsibleTeamUuid(team.getUuid());

        sc1.setTitle("serviceCall");
        sc2.setTitle("serviceCall");

        DSLSc.edit(sc1, sc2);
        // Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee1);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        // Проверки
        // Смена лицензии
        String expected = String.format(
                ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                + String.format(ErrorMessages.IS_ONE_PERFORMER_IN_TEAM, employee1.getTitle(), team.getTitle())
                + "%s '%s', %s '%s'",
                employee1.getTitle(), SystemClass.SERVICECALL.getTitle(), sc1.getTitle(),
                SystemClass.SERVICECALL.getTitle(), sc1.getTitle());
        GUIForm.applyFormAssertError(expected);

    }

    /**
     * Тестирование смены лицензии пользователя concurrent → нелиц: ответственный в рамках команды в статусе класса
     * Запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать тип сотрудника employeeCase, тип команды teamCase</li>
     * <li>Созать запрос scCase, </li>
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать сотрудника employee с лицензией concurrent (конкурентной)</li>
     * <li>Создать команду team</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState в классе scCase</li>
     * <li>Назначить employee ответственным за статус testState в рамках команды team</li>
     * <li>Выключить testState</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Получам сообщение: "Сотрудник %ФИО сотрудника% не может быть изменен по следующим причинам: 
     * Сотрудник назначается ответственным в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     *  </ol>
     **/
    @Test
    public void testChangeLicenseEmployeeResponsibleForStateInRequest()
    {
        // Подготовка
        // Создаем метаклассы
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();

        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);

        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        // Создаем команду
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(employee, team);
        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);
        // Создаем статус
        BoStatus testState = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                team.getUuid(), employee.getUuid());
        DSLBoStatus.add(testState);
        DSLBoStatus.enableBoStatus(false, testState);
        // Проверка статуса
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        String expected = String.format(
                ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' " + ErrorMessages.NOT_BE_CHNG + "\n"
                + ErrorMessages.FIRST + ErrorMessages.EMPLOYEE_IS_RESPONSIBLE_FOR_STATE + "%s (%s)",
                testState.getTitle(), scCase.getTitle());
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование смены лицензии пользователя concurrent → нелиц: ответственный в рамках команды в статусе
     * пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * http://sd-jira.naumen.ru/browse/NSDPRD-3112
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass с назначением ответственного и ЖЦ</li>
     * <li>Создать тип сотрудника employeeCase и тип команды teamCase</li> 
     * <li>В employeeCase создать группу атрибутов attrGroup, добавить в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать сотрудника employee с лицензией concurrent (конкурентной)</li>
     * <li>Создать команду team</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Создать статус testState в классе userClass</li>
     * <li>Назначить employee ответственным за статус testState в рамках команды team</li>
     * <li>Выключить status</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполняем вход в систему под сотрудником</li>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Получам сообщение: "Сотрудник %ФИО сотрудника% не может быть изменен по следующим причинам: 
     * Сотрудник назначается ответственным в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * <li>Нажимаем Отмена</li>
     * <li>Выключить testState</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на "Нелицензированный пользователь"</li>
     * <li>Нажимаем Сохранить</li>
     * <li>Получам сообщение: "Сотрудник %ФИО сотрудника% не может быть изменен по следующим причинам: 
     * Сотрудник назначается ответственным в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * </ol>
     **/
    @Test
    public void testChangeLicenseEmployeeResponsibleForStateInUserClass()
    {
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);

        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();
        // Создаем группу атрибутов
        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем отдел и сотрудника
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        // Создаем команду
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(employee, team);
        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);
        // Создаем статус
        BoStatus testState = DAOBoStatus.createUserStatus(userClass.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                team.getUuid(), employee.getUuid());
        DSLBoStatus.add(testState);
        // Проверка статуса
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();

        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        String expected = String.format(
                ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' " + ErrorMessages.NOT_BE_CHNG + "\n"
                + ErrorMessages.FIRST + ErrorMessages.EMPLOYEE_IS_RESPONSIBLE_FOR_STATE + "%s (%s)",
                testState.getTitle(), userClass.getTitle());
        GUIForm.applyFormAssertError(expected);
        GUIForm.cancelForm();

        DSLBoStatus.enableBoStatus(false, testState);
        tester.refresh();
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.select(GUIXpath.Div.FORM_CONTAINS + GUIXpath.InputComplex.ANY_VALUE, DAOBo.NO_LICENSE_STRING,
                "license");
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование изменения лицензии сотрудника со связями: ответственный за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>В employeeCase создаем группу атрибутов attrGroup, добавляем в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Создать sc типа scCase</li>
     * <li>Назначить employee ответственным за sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Тип лицензии не может быть изменен. Сотрудник
     * является ответственным за объекты: %список запросов%"</li>
     * </ol>
     */
    @Test
    public void testChangeLicenseEmployeeResponsibleScRelation()
    {
        // Создаем типы
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();

        // Создаем группу атрибутов
        String caseFqn = employeeCase.getFqn();
        GroupAttr attrGroup = DAOGroupAttr.create(caseFqn);
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(caseFqn, attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем элементы справочников
        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone("Chile/Continental");
        DSLCatalogItem.add(timeZoneItem);
        // Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addToRecipients(agreement, ou);
        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);
        // Назначаем ответственного за запроса
        sc.setScResponsibleEmplUuid(employee.getUuid());
        sc.setScResponsibleTeamUuid(team.getUuid());
        DSLSc.edit(sc);
        // Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        // Проверки
        String expected = String
                .format(ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_CHNG + "\n" + ErrorMessages.FIRST
                        + String.format(ErrorMessages.IS_RESPONSIBLE_WITHIN_TEAM, team.getTitle(), employee.getTitle())
                        + "%s '%s'", employee.getTitle(), SystemClass.SERVICECALL.getTitle(), sc.getTitle());
        GUIForm.applyFormAssertError(expected);
    }

    /**
     * Тестирование изменений лицензии сотрудника со связями: ответственный за
     * состояние
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00358
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип сотрудника employeeCase, тип запроса scCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>В employeeCase создаем группу атрибутов attrGroup, добавляем в нее
     * атрибут Лицензия</li>
     * <li>В employeeCase добавить на форму редактирования контент
     * licenseContent Параметры объекта (attrGroup)</li>
     * <li>Назначить employee ответственным за статус Зарегистрирован в scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Переходим в карточку employee</li>
     * <li>Нажимаем Редактировать</li>
     * <li>Изменяем лицензию на Нелицензированный пользователь</li>
     * <li>Нажимаем Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение "Сотрудник %ФИО сотрудника% не может быть изменен. Сотрудник
     * назначается ответственным в статусах: %список статусов,
     * для каждого в скобках указывается метакласс%"</li>
     * <ol>
     */
    @Test
    public void testChangeLicenseEmployeeResponsibleStatusRelation()
    {
        // Создаем типы
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass scCase = SharedFixture.scCase();
        // Создаем группу атрибутов
        String caseFqn = employeeCase.getFqn();
        GroupAttr attrGroup = DAOGroupAttr.create(caseFqn);
        DSLGroupAttr.add(attrGroup, licenseAttr);
        // Создаем контент
        ContentForm licenseContent = DAOContentEditForm.createEditablePropertyList(caseFqn, attrGroup);
        DSLContent.add(licenseContent);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.EDITFORM));
        // Создаем объекты
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(team);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);
        // Назначаем ответственного за статус
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        registered.setResponsibleType(ResponsibleType.EMPLOYEE_AND_TEAM.getType());
        registered.setResponsibleStrategy(Strategy.EMPLOYEE.getId());
        registered.setTeamUuid(team.getUuid());
        registered.setEmployeeUuid(employee.getUuid());
        DSLBoStatus.edit(registered);
        // Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.edit();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISelect.selectByXpath(GUIXpath.Div.FORM_CONTAINS + String.format(GUIXpath.InputComplex.ANY_VALUE, "license"),
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, "notLicensed"));
        // Проверки
        String expectedMessage = String.format(
                ErrorMessages.EMP + "'" + DSLEmployee.getFullName(employee) + "' " + ErrorMessages.NOT_BE_CHNG + "\n"
                + ErrorMessages.FIRST + ErrorMessages.EMPLOYEE_IS_RESPONSIBLE_FOR_STATE + "%s (%s)",
                registered.getTitle(), scCase.getTitle());
        GUIForm.applyFormAssertError(expectedMessage);
    }

    /**
     * Удаление сотрудника, используемого в настройке архивной таблицы соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00323
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase, в нем сотрудника employee типа employeeCase</li>
     * <li>В классе Отдел ouClass добавить атрибут attrBoLink типа Ссылка на БО (на тип сотрудника employeeCase)</li>
     * <li>Создать таблицу соответствий catalogItem для класса Отдел ouClass (определяющий – Тип объекта (metaclass),
     * определяемый - attrBoLink)</li>
     * <li>Добавить правило row в таблицу соответствий catalogItem: ouCase – employee </li>
     * <li>Заархивировать таблицу соответствий catalogItem</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под лицензированным сотрудником</li>
     * <li>Зайти в карточку employee. Нажать Удалить. Подтвердить действие.</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee не удален, есть сообщение об ошибке:
     * Сотрудник '%empl%' не может быть удален по следующим причинам: 1. Сотрудник используется в качестве значения в
     * таблицах соответствия: %название ТС%.</li>
     * <br>
     * </ol>
     */
    @Test
    public void testDeleteEmplInArchRulesSettings()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        //Создаем отдел и сотрудника
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        Attribute attrBoLink = DAOAttribute.createObjectLink(ouClass, employeeClass, employeeCase, null);
        DSLAttribute.add(attrBoLink);

        String sourceAttr = SysAttribute.metaClass(ouClass).getCode();
        CatalogItem catalogItem = DAOCatalogItem.createRulesSettings(ouClass, attrBoLink.getCode(), sourceAttr);
        DSLCatalogItem.add(catalogItem);
        RsRow row = DAORsRow.create(catalogItem, attrBoLink.getCode(), employee.getUuid(), sourceAttr, ouCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        DSLCatalogItem.archive(catalogItem);

        // Действие
        GUILogon.asTester();

        // Проверка
        List<String> expectedMessages = Lists.newArrayList(ErrorMessages.EMP, DSLEmployee.getFullName(employee),
                ErrorMessages.NOT_BE_DEL, ErrorMessages.FIRST, ErrorMessages.EMP, ErrorMessages.USED_IN_RS,
                catalogItem.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessages);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: поставщик соглашения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать agreement типа agreementCase</li>
     * <li>Назначить employee поставщиком agreement</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник связан со следующими объектами: %список объектов%"
     * </li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeAgreementRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setPassword(ModelUtils.createPassword());
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        Bo agreement = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        DSLBo.add(employee, agreement);
        DSLTeam.addEmployees(SharedFixture.team(), employee);

        DSLBo.editAttributeValue(agreement,
                DAOAttribute.createPseudo("Поставщик (сотрудник)", "supplierEmpoyee", employee.getUuid()));
        // Выполнение действия и проверки
        GUILogon.asTester();
        List<String> expectedMessage = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, DSLEmployee.getFullName(employee),
                agreement.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: контрагент запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать sc типа scCase, контрагент employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник связан со следующими объектами: %список объектов%"
     * </li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeClientScRelation()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass scCase = SharedFixture.scCase();

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem resolutionTimeRule = SharedFixture.rsResolutionTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();
        CatalogItem timeZone = SharedFixture.timeZone();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);
        DSLBo.add(agreement);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, agreement);
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZone);
        DSLBo.add(sc);
        // Выполнение действия и проверки
        GUILogon.asTester();
        List<String> expectedMessage = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, DSLEmployee.getFullName(employee), sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertPresent(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями: закрывший запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee типа employeeCase</li>
     * <li>Создать sc типа scCase</li>
     * <li>Закрыть запрос sc</li>
     * <li>Назначить employee закрывшим запрос sc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Пытаемся удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Получаем сообщение
     * "Сотрудник %ФИО сотрудника% не может быть удален. Сотрудник связан со следующими объектами: %список объектов%"
     * </li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeCloseScRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = SharedFixture.agreementCase();
        MetaClass teamCase = SharedFixture.teamCase();
        MetaClass scCase = SharedFixture.scCase();

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem resolutionTimeRule = SharedFixture.rsResolutionTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();
        CatalogItem timeZone = SharedFixture.timeZone();
        CatalogItem closureCodeItem = SharedFixture.closureCode();

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, resolutionTimeRule,
                priorityRule);
        DSLBo.add(ou, team, agreement);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        DSLTeam.addEmployees(team, employee);
        DSLAgreement.addToRecipients(agreement, ou);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZone);
        DSLBo.add(sc);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUISc.resolve(team, null);
        GUIBo.goToCard(sc);
        GUISc.close(team, employee, closureCodeItem);
        // Выполнение действия и проверки
        List<String> expectedMessage = Lists.newArrayList(ErrorMessages.EMP, ErrorMessages.NOT_BE_DEL,
                ErrorMessages.FIRST, ErrorMessages.HAS_REL_OBJS, DSLEmployee.getFullName(employee), sc.getTitle());
        GUIBo.tryToDeleteBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertPresent(employee);
    }
}
