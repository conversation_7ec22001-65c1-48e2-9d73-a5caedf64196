package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.ObjectType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование прав доступа для нелицензированных пользователей при использовании пользовательского набора прав
 * <p>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387 <br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413 <br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432 <br>
 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
 * </p>
 *
 * <AUTHOR>
 * @since 02.10.2024
 */

public class UnlicCustomPermissionSetTest2 extends AbstractTestCase
{
    private static Bo employee;
    private static SecurityProfile secProfile;
    private static MetaClass scCase, questionaryClass, ouCase, userClass, userCase, questionary1Class, ouClass,
            employeeCase, employeeClass;
    private static Bo ou, root;
    private static ContentForm questionaryList, userClassList;
    private static final String ADD_FORM_TITLE_FORMAT = "%s / Форма добавления";
    private static final String FIELD_NOT_EXIST = "Поле не отображается на контенте";

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Добавить пользовательский класс c кодом questionary</li>
     * <li>Добавить в классе questionary тип questionary1</li>
     * <li>Добавить тип scCase в классе Запрос (serviceCall)</li>
     * <li>Добавить пользовательский класс c кодом userClass и жизненным циклом</li>
     * <li>Добавить в классе userClass тип userCase</li>
     * <li>Добавить в классе Отдел (ou) тип ouCase</li>
     * <li>Создать отдел ou1</li>
     * <li>Загрузить приложенную лицензию (файл ТК0, это стандартная лицензия) с параметром rolesForUnlicensedUsers =
     * employee</li>
     * <li>Создать на карточке компании сложные списки объектов:</li>
     * <li>Создать профиль прав доступа secProfile с параметрами:<br>
     *      "Для нелицензированных пользователей" = "да"<br>
     *      "Роль" = "Сотрудник"</li>
     * <ol>
     *      <li>questionary для класса questionary по группе Системные атрибуты</li>
     *      <li>userClass для класса userClass по группе Системные атрибуты</li>
     *  </ol>
     * <li>Создать сотрудника employee со значением атрибута "Лицензия" = "Нелицензированный пользователь"</li>
     * <li>Добавить соглашение сотруднику</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        questionaryClass = DAOUserClass.createWithCode("questionary");
        questionary1Class = DAOUserCase.create(questionaryClass, "questionary1", "questionary1");
        scCase = DAOScCase.create();

        userClass = DAOUserClass.createWithCode("userClass");
        userClass.setHasWorkflow(Boolean.TRUE.toString());
        userCase = DAOUserCase.create(userClass, "userCase", "userCase");
        employeeClass = DAOEmployeeCase.createClass();
        employeeCase = SharedFixture.employeeCase();
        ouClass = DAOOuCase.createClass();
        ouCase = DAOOuCase.create(ouClass);

        DSLMetaClass.add(questionaryClass, questionary1Class, scCase, userClass, userCase, ouCase);

        ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        secProfile = DAOSecurityProfile.create(
                false, null, SysRole.employee());
        DSLSecurityProfile.add(secProfile);

        questionaryList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(),
                DAOGroupAttr.createSystem(DAORootClass.create()), questionaryClass);
        userClassList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(),
                DAOGroupAttr.createSystem(DAORootClass.create()), userClass);
        DSLContent.add(questionaryList, userClassList);

        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, false);
        DSLBo.add(employee);
        DSLAgreement.addToRecipients(SharedFixture.agreement(), employee);
        root = SharedFixture.root();
    }

    /**
     * Тестирование доступности просмотра части агрегирующего атрибута, если в параметре лицензии указана
     * только часть этого атрибута
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00387 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
     * </p>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип для системного класса "Соглашение" {@code agreementCase}</li>
     * <li>Создать группу атрибутов {@code supplierGroupAttr} с системными атрибутами: "Поставщик" {@code supplier},
     * "Поставщик (сотрудник)" {@code supplierEmpoyee}, "Поставщик (команда)" {@code supplierTeam}</li>
     * <li>Для типа {@code agreementCase} создать контент {@code propertyList} с группой атрибутов
     * {@code supplierGroupAttr}</li>
     * <li>Установить лицензию с пользовательским набором прав:
     * <pre><code>
     *    "permissionsSetForUnlicensedUsers" = custom
     *    "editableAttributesToUnlicensedUser" = agreement: supplierTeam;
     * </code></pre></li>
     * <li>Выдать права в системном классе {@code agreement} для профиля {@code secProfile}<br>
     * - на просмотр карточки объектов {@code agreement}<br>
     * - просмотр и редактирование "остальные атрибуты" класса {@code agreement}</li>
     * <li>Создать объект класса {@code agreementCase}</li>
     * <b>Действия и проверки</b>
     * <li>Проверить, что в контенте {@code propertyList} отсутствует кнопка "Редактировать"</li>
     * <li>Выполнить проверку, на видимость атрибутов:<br>
     * - "Поставщик (команда)" {@code supplierTeam} - присутствует <br>
     * - "Поставщик" {@code supplier} - отсутствует <br>
     * - "Поставщик (сотрудник)" {@code supplierEmpoyee} - отсутствует <br>
     * </li>
     * </ol>
     */
    @Test
    public void testAllowedVisiblePartForAggregateAttribute()
    {
        // Подготовка
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);

        GroupAttr supplierGroupAttr = DAOGroupAttr.create(agreementCase, "Поставщик");
        Attribute supplier = DAOAttribute.createPseudo(SystemAttrEnum.SUPPLIER);
        Attribute supplierEmployee = DAOAttribute.createPseudo(SystemAttrEnum.SUPPLIER_EMPLOYEE);
        supplierEmployee.setType(ObjectType.CODE);
        Attribute supplierTeam = DAOAttribute.createPseudo(SystemAttrEnum.SUPPLIER_TEAM);
        supplierTeam.setType(ObjectType.CODE);

        DSLGroupAttr.add(supplierGroupAttr, supplier, supplierTeam, supplierEmployee);

        ContentForm propertyList = DAOContentCard.createPropertyList(agreementCase, supplierGroupAttr);
        DSLContent.add(propertyList);

        DSLAdmin.installLicense(DSLAdmin.CUSTOM_UNLIC_PERMISSION_SET_LICENSE);
        DSLSecurityProfile.setRights(agreementCase, secProfile,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo agreement = DAOAgreement.create(agreementCase);
        DSLBo.add(agreement);

        //Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(agreement);

        GUIContent.assertContentAttributeCaption(propertyList, supplierTeam);
        GUIContent.assertLinkAbsense(propertyList, GUIContent.LINK_EDIT);
        GUIForm.assertAttrAbsence(supplierEmployee, supplier);
    }

    /**
     * Тестирование возможности добавления нелицензированным пользователем с набором прав custom объектов класса Запрос
     * и невозможности добавления объектов класса Анкета, если эти классы указаны в параметре лицензии
     * forbiddenClassesToUnlicensedUsers, и невозможности добавления объекта пользовательского класса, если он не указан
     * в параметрах лицензии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В Основном классе создать профиль прав secProfile для нелицензированных пользователей, Роль - Сотрудник -
     * и выдать профилю secProfile все доступные права </li>
     * <li>Загрузить на стенд приложенную лицензию (файл ТК1, нужно подписать) с пользовательским набором прав custom
     * с параметрами:</li>
     *     <ul>
     *         <li>permissionsSetForUnlicensedUsers = custom</li>
     *         <li>forbiddenClassesToUnlicensedUsers = questionary, serviceCall</li>
     *     </ul>
     * <li>Выдать профилю secProfile права на Добавление объекта в классе serviceCall (маркер "Добавление запроса
     * для клиента-сотрудника")</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИО под пользователем employee</li>
     * <li>Проверить, что на карточке сотрудника есть кнопка "Добавить запрос"</li>
     * <li>Нажать на кнопку "Добавить запрос"</li>
     * <li>Проверить, что открылась форма добавления запроса</li>
     * <li>Перейти к списку объектов класса questionary</li>
     * <li>Проверить, что на панели действий нет кнопки "Добавить"</li>
     * <li>Перейти к списку объектов класса userClass</li>
     * <li>Проверить, что на панели действий нет кнопки "Добавить"</li>
     * </ol>
     */
    @Test
    public void testAdditionOfServiceCallByUnlicensedUser()
    {
        //Подготовка
        DSLSecurityProfile.grantAllPermissions(secProfile);
        DSLAdmin.installLicense(DSLAdmin.UNLICENSED_CUSTOM_PERMISSIONS_SET_LICENCE);

        //Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_ADD_SC);
        GUIButtonBar.addSC();
        GUIForm.assertFormTitle(String.format(ADD_FORM_TITLE_FORMAT, scCase.getTitle()));
        GUIBo.goToCard(root);
        GUIBo.assertThatBoCard(root);
        GUIContent.assertButtonAbsence(questionaryList.getXpathId(), GUIContent.LINK_ADD);
        GUIContent.assertButtonAbsence(userClassList.getXpathId(), GUIContent.LINK_ADD);
    }

    /**
     * Тестирование возможности добавления нелицензированным пользователем с набором прав custom объектов классов
     * "Анкета" и "Запрос",
     * если они не указаны в параметрах лицензии, и невозможности добавления объекта пользовательского класса, если
     * он указан одновременно в параметрах forbiddenClassesToUnlicensedUsers и allowedClassesToUnlicensedUsers
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В Основном классе создать профиль прав secProfile для нелицензированных пользователей, Роль - Сотрудник -
     * и выдать профилю secProfile все доступные права </li>
     * <li>Загрузить на стенд приложенную лицензию (файл ТК2, нужно подписать) с пользовательским набором прав custom
     * с параметрами:
     * <li>permissionsSetForUnlicensedUsers = custom</li>
     * <li>forbiddenClassesToUnlicensedUsers = userClass</li>
     * <li>allowedClassesToUnlicensedUsers = userClass</li>
     * <li>Выдать профилю {@code unlicProfile} права на "Добавление объекта" в классах {@code questionary} и {@code
     * serviceCall}
     * (маркер "Добавление запроса для клиента-сотрудника")</li>
     * <li>В классе {@code questionary} в атрибуте "Название" указать значение по умолчанию "title"</li>
     * <b>Действия и проверки</b>
     * <li>Войти в ИО под пользователем employee</li>
     * <li>Проверить, что на карточке сотрудника есть кнопка "Добавить запрос"</li>
     * <li>Нажать на кнопку "Добавить запрос"</li>
     * <li>Проверить, что открылась форма добавления запроса</li>
     * <li>Перейти к списку объектов класса {@code questionary}</li>
     * <li>Проверить, что на панели действий есть кнопка "Добавить"</li>
     * <li>Нажать на кнопку "Добавить"</li>
     * <li>Проверить, что открылась форма добавления объекта типа {@code questionary1}</li>
     * <li>Нажать на кнопку "Сохранить" на форме</li>
     * <li>Проверить, что добавился объект с названием "title"</li>
     * <li>Перейти к списку объектов класса {@code userClass}</li>
     * <li>Проверить, что на панели действий нет кнопки "Добавить"</li>
     * </ol>
     */
    @Test
    public void testUnlicensedUserCustomPermissionsForAddingObjects()
    {
        //Подготовка
        DSLSecurityProfile.grantAllPermissions(secProfile);
        DSLAdmin.installLicense(DSLAdmin.UNLICENSED_CUSTOM_PERMISSIONS_SET_LICENCE_2);
        Attribute titleAttr = SysAttribute.title(questionaryClass);
        titleAttr.setDefaultValue(Model.TITLE);
        DSLAttribute.edit(titleAttr);

        // Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_ADD_SC);
        GUIButtonBar.addSC();
        GUIForm.assertFormTitle(String.format(ADD_FORM_TITLE_FORMAT, scCase.getTitle()));
        GUIBo.goToCard(root);
        questionaryList.advlist().toolPanel().asserts().buttonsPresence(GUIButtonBar.BTN_ADD);
        questionaryList.advlist().toolPanel().clickAdd();
        GUIForm.assertFormTitle(String.format(ADD_FORM_TITLE_FORMAT, questionary1Class.getCode()));
        GUIForm.applyForm();
        Bo modelByUuid = DAOBo.createModelByUuid(GUIBo.getUuidByUrl());
        Cleaner.afterTest(true, () -> DSLBo.delete(modelByUuid));

        GUITester.assertTextPresent(GUIXpath.Div.LAYOUT_LEFT_COL + GUIXpath.Any.TITLE_VALUE, Model.TITLE);
        GUIBo.goToCard(root);
        GUIBo.assertThatBoCard(root);
        userClassList.advlist().toolPanel().asserts().buttonsAbsence(GUIButtonBar.BTN_ADD);
    }

    /**
     * Тестирование доступности нелицензированному пользователю с набором прав custom перехода в конечный статус,
     * если для класса и типа объектов в параметре transitionsByStateToUnlicensedUser разрешены переходы в конечные
     * статусы тегом *FINAL_STATE*, и недоступности в случае запрета перехода маркером прав на уровне типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В Основном классе создать профиль прав secProfile для нелицензированных пользователей, Роль - Сотрудник -
     *     и выдать профилю secProfile все доступные права </li>
     *     <li>Загрузить на стенд приложенную лицензию (файл ТК3, нужно подписать) с пользовательским набором прав
     *     custom
     *     с параметрами:</li>
     *     <ul>
     *         <li>permissionsSetForUnlicensedUsers = custom,</li>
     *         <li>transitionsByStateToUnlicensedUser = userClass: *FINAL_STATE*;</li>
     *         <li>userClass$userCase: *FINAL_STATE*;</li>
     *     </ul>
     *     <li>Выдать профилю unlicProfile права на Изменение статуса - Остальные переходы в классе userClass</li>
     *     <li>В классе userClass добавить статус testState и настроить переходы:
     *         <ul>
     *             <li>из Зарегистрирован (registered) в testState,</li>
     *             <li>из testState в Закрыт (closed),</li>
     *             <li>из Закрыт в Зарегистрирован</li>
     *         </ul>
     *     </li>
     *     <li>Создать объект user1 класса userClass</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в ИО под пользователем employee</li>
     *     <li>Перейти на карточку объекта user1</li>
     *     <li>Проверить, что на карточке есть кнопка Изменить статус</li>
     *     <li>Нажать на кнопку Изменить статус</li>
     *     <li>Проверить, что открылась форма смены статуса</li>
     *     <li>Проверить, что на форме в поле Новый статус для выбора доступен только статус Закрыт</li>
     *     <li>Сохранить форму</li>
     *     <li>Проверить, что статус объекта user1 изменился на Закрыт</li>
     *     <li>[скриптом] В типе userCase забрать у профиля unlicProfile права на Изменение статуса - Остальные
     *     переходы</li>
     *     <li>[скриптом] Вернуть объект user1 в статус Зарегистрирован</li>
     *     <li>Обновить страницу</li>
     *     <li>Проверить, что на карточке нет кнопки Изменить статус</li>
     * </ol>
     */
    @Test
    public void testFinalStateTransitionForUnlicensedUser()
    {
        //Подготовка
        DSLSecurityProfile.grantAllPermissions(secProfile);
        DSLAdmin.installLicense(DSLAdmin.UNLICENSED_CUSTOM_PERMISSIONS_SET_LICENCE_3);
        DSLSecurityProfile.setRights(userClass, secProfile, AbstractBoRights.CHANGE_STATE);
        Bo userBo = DAOUserBo.create(userCase);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus testState = DAOBoStatus.createUserStatus(userClass);
        BoStatus closed = DAOBoStatus.createClosed(userClass.getFqn());
        DSLBoStatus.add(testState);
        DSLBoStatus.setTransitions(registered, testState, closed, registered);
        DSLBo.add(userBo);

        //Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_CHANGE_STATE);
        GUIButtonBar.changeState();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        GUISelect.assertNotDisplayed(GUIXpath.Complex.SELECT_STATE, registered.getCode(), testState.getTitle());
        GUISelect.assertSelectWithoutEmpty(GUIXpath.Complex.SELECT_STATE, Lists.newArrayList(closed.getTitle()), false,
                false);
        GUIForm.applyForm();
        DSLBo.assertState(userBo, closed.getCode());

        DSLSecurityProfile.removeRights(userClass, secProfile, AbstractBoRights.CHANGE_STATE);
        DSLSc.changeState(userBo, registered);
        tester.refresh();
        GUIBo.assertThatBoCard(userBo);
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_STATE);
    }

    /**
     * Тестирование доступности нелицензированному пользователю с набором прав custom просмотра и редактирования
     * системных атрибутов, недоступных в других наборах прав, если они указаны в параметрах
     * editableAttributesToUnlicensedUser и visibleAttributesToUnlicensedUser
     https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$242275692
     * </p>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В Основном классе создать профиль прав secProfile для нелицензированных пользователей, Роль - Сотрудник -
     * и выдать профилю secProfile все доступные права </li>
     * <li>Загрузить на стенд приложенную лицензию (файл ТК4, нужно подписать) с пользовательским набором прав custom
     * с параметрами:
     * <pre><code>
     *    "permissionsSetForUnlicensedUsers" = custom,
     *    "visibleAttributesToUnlicensedUser" =
     *           ou: recipientAgreements;
     *           slmService: responsible;
     *           employee: teams;
     *          *ATTRIBUTES_TYPES*: boLinks;
     *    "editableAttributesToUnlicensedUser" =
     *           ou: title, parent, head, recipientAgreements;
     *           root: head;
     *           slmService: stateStartTime, callCases, inventoryNumber, state, agreements;
     *           employee: teams, license, performer, firstName, lastName, parent, password, recipientAgreements;
     *           team: recipientAgreements, members;
     *      *ATTRIBUTES_TYPES*: boLinks, string, dateTime;
     * </code></pre></li>
     * <li>В классе "Отдел" в группу "Системные атрибуты" добавить атрибут: "Соглашения"</li>
     * <li>В классе "Услуга" в группу "Системные атрибуты" добавить атрибуты: "Время входа в статус", "Куратор
     * услуги", "Типы запросов", "Уникальный номер", "Связанные соглашения", "Статус"</li>
     * <li>Добавить в классе "Услуга" тип {@code slmServiceCase}</li>
     * <li>Добавить объект {@code slmService1} класса "Услуга"</li>
     * <li>В классе "Сотрудник" в группу "Системные атрибуты" добавить атрибуты: "Команды", "Исполнитель", "Отдел",
     * "Пароль", "Соглашения"</li>
     * <li>В классе "Команда" в группу "Системные атрибуты" добавить атрибуты: "Соглашения"</li>
     * <li>Добавить в классе "Команда" тип {@code teamCase}</li>
     * <li>Добавить объект {@code team1} класса "Команда"</li>
     * <b>Действия и проверки</b>
     * <li>Войти в ИО под пользователем employee</li>
     * <li>Перейти на карточку "Компании", вкладка "Атрибуты компании"
     * -> Проверить, что в контенте "Системные атрибуты" есть кнопка "Редактировать"</li>
     * <li>Нажать кнопку "Редактировать"
     * -> Проверить, что на форме есть поле для редактирования атрибута "Директор компании"</li>
     * <li>Перейти на карточку объекта {@code ou1}
     * -> Проверить, что в контенте "Системные атрибуты" отображаются атрибуты "Название", "Соглашения",
     * "Руководитель отдела", "Родитель" и есть кнопка "Редактировать"</li>
     * <li>Нажать кнопку "Редактировать"
     * -> Проверить, что на форме есть поля для редактирования атрибутов: "Название", "Соглашения", "Руководитель
     * отдела"</li>
     * <li>Перейти на карточку объекта {@code slmService1}
     * -> Проверить, что в контенте "Основные параметры" отображаются атрибуты "Название", "Время входа в статус",
     * "Куратор услуги", "Типы запросов", "Уникальный номер", "Связанные соглашения", "Статус" и есть кнопка
     * "Редактировать"</li>
     * <li>Нажать кнопку "Редактировать"
     * -> Проверить, что на форме есть поля для редактирования атрибутов: "Типы запросов", "Уникальный номер",
     * "Связанные соглашения"</li>
     * <li>Перейти на карточку объекта employee
     * -> Проверить, что в контенте "Системные атрибуты" отображаются атрибуты "Фамилия", "Имя", "Лицензия",
     * "Команды", "Исполнитель", "Отдел", "Пароль", "Соглашения"
     * -> Проверить, что на панели действий есть кнопка "Редактировать"</li>
     * <li>Нажать кнопку "Редактировать"
     * -> Проверить, что на форме есть поля для редактирования атрибутов: "Фамилия", "Имя", "Лицензия", "Команды",
     * "Соглашения"</li>
     * <li>Перейти на карточку объекта {@code team1}
     * -> Проверить, что в контенте "Системные атрибуты" отображаются атрибуты "Участники команды", "Соглашения" и
     * есть кнопка "Редактировать"</li>
     * <li>Нажать кнопку "Редактировать"
     * -> Проверить, что на форме есть поля для редактирования атрибутов: "Участники команды", "Соглашения"</li>
     * </ol>
     */
    @Test
    public void testCustomPermissionsForUnlicensedUserVisibilityAndEdit()
    {
        //Подготовка
        DSLSecurityProfile.grantAllPermissions(secProfile);
        DSLAdmin.installLicense(DSLAdmin.UNLICENSED_CUSTOM_PERMISSIONS_SET_LICENCE_4);
        Attribute recipientAgreements = SysAttribute.recipientAgreements(ouCase);
        GroupAttr systemOuGroupAttr = DAOGroupAttr.createSystem(ouClass);
        DSLGroupAttr.addToGroup(systemOuGroupAttr, recipientAgreements);

        MetaClass slmCase = SharedFixture.slmCase();
        Attribute stateStartTime = SysAttribute.stateStartTime(slmCase);
        Attribute responsible = SysAttribute.responsible(slmCase);
        Attribute callCases = SysAttribute.callCases(slmCase);
        Attribute inventoryNumber = SysAttribute.inventoryNumber(slmCase);
        Attribute agreements = SysAttribute.agreements(slmCase);
        Attribute state = SysAttribute.state(slmCase);

        GroupAttr systemSlmGroupAttr = DAOGroupAttr.createSystem(slmCase);
        DSLGroupAttr.addToGroup(systemSlmGroupAttr, stateStartTime, responsible, callCases, inventoryNumber, agreements,
                state);

        Bo slmBo = DAOService.create(slmCase);
        Attribute teams = SysAttribute.teams(employeeCase);
        Attribute performer = SysAttribute.performer(employeeCase);
        Attribute parent = SysAttribute.parent(employeeCase);
        Attribute password = SysAttribute.password(employeeCase);
        Attribute license = SysAttribute.license(employeeCase);
        Attribute recipientAgreements1 = SysAttribute.recipientAgreements(employeeCase);

        GroupAttr systemEmployeeGroupAttr = DAOGroupAttr.createSystem(employeeClass);
        DSLGroupAttr.addToGroup(systemEmployeeGroupAttr, teams, performer, parent, password, recipientAgreements1,
                license);
        MetaClass teamCase = SharedFixture.teamCase();
        Attribute recipientAgreementsTeam = SysAttribute.recipientAgreements(teamCase);
        GroupAttr systemTeamGroupAttr = DAOGroupAttr.createSystem(teamCase);
        DSLGroupAttr.addToGroup(systemTeamGroupAttr, recipientAgreementsTeam);

        Bo teamBo = DAOService.create(teamCase);
        DSLBo.add(slmBo, teamBo);

        //Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(root);

        GUIButtonBar.assertPresent(GUIButtonBar.BTN_EDIT);
        GUIButtonBar.edit();
        GUIForm.assertAttrPresent(SysAttribute.companyHead());
        GUIForm.cancelForm();

        GUIBo.goToCard(ou);
        assertAttributesNotPresent(SystemAttrEnum.TITLE, SystemAttrEnum.PARENT, SystemAttrEnum.HEAD,
                SystemAttrEnum.RECIPIENT_AGREEMENTS);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_EDIT);
        GUIButtonBar.edit();
        GUIForm.assertAttrPresent(SysAttribute.title(ouCase), SysAttribute.head(ouCase),
                SysAttribute.recipientAgreements(ouCase));
        GUIForm.cancelForm();

        GUIBo.goToCard(slmBo);
        assertAttributesNotPresent(SystemAttrEnum.TITLE, SystemAttrEnum.STATE_START_TIME, SystemAttrEnum.RESPONSIBLE,
                SystemAttrEnum.CALL_CASES, SystemAttrEnum.INVENTORY_NUMBER, SystemAttrEnum.AGREEMENTS,
                SystemAttrEnum.STATE);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_EDIT);
        GUIButtonBar.edit();
        GUIForm.assertAttrPresent(SysAttribute.title(ouCase), SysAttribute.callCases(ouCase),
                SysAttribute.inventoryNumber(ouCase), SysAttribute.agreements(ouCase));
        GUIForm.cancelForm();

        GUIBo.goToCard(employee);
        assertAttributesNotPresent(SystemAttrEnum.LAST_NAME, SystemAttrEnum.FIRST_NAME,
                SystemAttrEnum.LICENSE, SystemAttrEnum.TEAMS,
                SystemAttrEnum.PERFORMER, SystemAttrEnum.PARENT,
                SystemAttrEnum.PASSWORD, SystemAttrEnum.RECIPIENT_AGREEMENTS);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_EDIT);
        GUIButtonBar.edit();
        GUIForm.assertAttrPresent(SysAttribute.lastName(employeeCase), SysAttribute.firstName(employeeCase),
                SysAttribute.license(employeeCase), SysAttribute.teams(employeeCase),
                SysAttribute.recipientAgreements(employeeCase));
        GUIForm.cancelForm();

        GUIBo.goToCard(teamBo);
        assertAttributesNotPresent(SystemAttrEnum.RECIPIENT_AGREEMENTS, SystemAttrEnum.MEMBERS);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_EDIT);
        GUIButtonBar.edit();
        GUIForm.assertAttrPresent(SysAttribute.recipientAgreements(ouCase), SysAttribute.members(ouCase));
    }

    /**
     * Проверить, что в контенте "Системные атрибуты" отображаются атрибуты
     * @param attributes атрибуты
     */
    private static void assertAttributesNotPresent(SystemAttrEnum... attributes)
    {
        Arrays.stream(attributes)
                .forEach(attr -> GUITester.assertPresent(
                        String.format(Div.OUTER_PANEL + Any.ANY_CAPTION, attr.getCode()), FIELD_NOT_EXIST));
    }

}
