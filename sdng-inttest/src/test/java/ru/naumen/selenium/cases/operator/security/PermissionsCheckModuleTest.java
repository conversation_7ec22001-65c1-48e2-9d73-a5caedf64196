package ru.naumen.selenium.cases.operator.security;

import java.util.Set;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel.AppliedTo;
import ru.naumen.selenium.casesutil.contenttemplate.GUIContentTemplate;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.security.GUIPCModule;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.UserGroup;

/**
 * Тесты на модуль проверки прав
 * <p>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
 *
 * <AUTHOR>
 * @since 21 июня 2016 г.
 */
public class PermissionsCheckModuleTest extends AbstractTestCase
{
    /**
     * Тестирование отсутствия архивных объектов в выпадающем списке пользователей в модуле проверки прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou1 типа ouCase</li>
     * <li>Создать отдел ou2 типа ouCase</li>
     * <li>Поместить ou2 в архив</li>
     * <li>В отделе ou1 создать сотрудника employee1 типа employeeCase</li>
     * <li>В отделе ou1 создать сотрудника employee2 типа employeeCase</li>
     * <li>Поместить employee2 в архив</li
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку отдела ou1</li>
     * <li>Включить модуль проверки прав</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке пользователей не отображается отдел ou2</li>
     * <li>В списке пользователей не отображается сотрудник employee2</li>
     * </ol>
     */
    @Test
    public void testAbsenceArchiveObjectInModule()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase, scCase);

        Bo ou1 = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou1, ou2);
        DSLBo.archive(ou2);

        Bo employee1 = DAOEmployee.create(employeeCase, ou1, false);
        Bo employee2 = DAOEmployee.create(employeeCase, ou1, false);
        DSLBo.add(employee1, employee2);
        DSLBo.archive(employee2);

        //Выполнение действий
        GUILogon.asSuper();
        GUIBo.goToCard(ou1);
        GUIPCModule.clickShowModuleButton();

        //Проверка
        GUIPCModule.assertAbsenceElement(ou2);
        GUIPCModule.assertAbsenceElement(ou1, employee2);
    }

    /**
     * Тестирование отсутствия контента Контрагент при включенном модуле проверке прав под нелицензированным
     * пользователем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип scCase класса Запрос</li>
     * <li>Добавить контент «Выбор контрагента» на форму добавления запроса типа scCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать нелицензированного сотрудника employee типа employeeCase</li>
     * <li>Создать группу пользователей secGroup и добавить в неё employee</li>
     * <li>В типе scCase создаём профиль прав доступа secProfile с параметрами:
     *      <ul>
     *          <li>"Для нелицензированных пользователей" = "да"</li>
     *          <li>"Модель группы пользователя" = "secGroup"</li>
     *          <li>"Роль" = "Сотрудник"</li>
     *      </ul>
     * </li>
     * <li>Выдать профилю secProfile все права</li>
     * <li> Настроить возможность добавления запроса типа scCase через общую кнопку «Добавить»</li>
     * <li>Создать соглашение agreement и назначить сотрудника employee его получателем</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Включить модуль проверки прав</li>
     * <li>Выбрать сотрудника employee</li>
     * <li>Нажать Отобразить</li>
     * <li>Добавить запрос по общей кнопке</li>
     * <li>Выбрать на форме добавления соглашение agreement и тип запроса scCase</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме добавления запроса нет контента Выбор контрагента</li>
     * <li>В блоке роли отображается роли Контрагент запроса, Сотрудник</li>
     * </ol>
     */
    @Test
    public void testAbsenceContentWithEmbeddedModuleUnlicEmployee()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase, scCase);

        ContentForm content = DAOContentAddForm.createSelectClient(scCase);
        DSLContent.add(content);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        SecurityGroup secGroups = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroups);
        DSLSecurityGroup.addUsers(secGroups, employee);

        SecurityProfile secProfile = DAOSecurityProfile.create(false, secGroups, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, scCase);

        MenuItem addButton = DAOMenuItem.createAddButton(true, scCase);
        DSLNavSettings.editVisibilitySettings(true, true);
        DSLMenuItem.add(addButton);

        MetaClass agreeCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreeCase);
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        Bo agreement = DAOAgreement.createWithRules(agreeCase, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        DSLBo.add(agreement);
        DSLAgreement.addRecipients(employee, agreement);

        //Выполнение действий
        GUILogon.asSuper();
        GUIBo.goToCard(ou);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        GUINavSettingsOperator.clickMenuItem(addButton.getCode(), scCase.getFqn());
        GUIBo.selectAgreementService(agreement.getUuid(), agreement.getScSlmServiceUuid());
        GUIBo.selectCase(scCase);

        //Проверка
        GUIContent.assertAbsence(content);
        GUIPCModule.assertRoles(SysRole.scClient(), SysRole.employee());
    }

    /**
     * Тестирование что при включенном модуле проверки прав автором действий остается суперпользователь
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <li>Выдать сотруднику права на просмотр карточки сотрудника</li>
     * <li>Добавить контент "Комментарии к объекту" на карточку сотрудника</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Включить модуль проверки прав</li>
     * <li>Выбрать сотрудника employee</li>
     * <li>Нажать Отобразить</li>
     * <li>Добавить комментарий/li>
     * <br>
     * <b>Проверка</b>
     * <li>Автором комментария является суперпользователь</li>
     * </ol>
     */
    @Test
    public void testActionAuthorWithEmbeddedModule()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        ContentForm content = DAOContentCard.createCommentList(employeeCase.getFqn());
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.asSuper();
        GUIBo.goToCard(employee);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        String messageForComment = ModelUtils.createDescription();

        GUICommentList.assertAddLinkPresent(content);
        GUICommentList.clickAddLink(content);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIRichText.sendKeys(GUIRichText.TEXT, messageForComment);
        GUIForm.applyForm();

        //Проверка
        String commentUUID = GUIComment.getCommentUUIDs(content).iterator().next();
        GUICommentList.assertCommentAuthor(content, commentUUID, "Суперпользователь");
    }

    /**
     * Тестирование работы модуля в режиме "Отображать права выбранного пользователя"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать группы пользователей heads и employees</li>
     * <li>Создать профиль headsProfile на основе абсолютной роли "Сотрудник" и группы пользователей heads</li>
     * <li>Создать профиль employeesProfile на основе абсолютной роли "Сотрудник" и группы пользователей employees</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Выдать профилю employeesProfile все возможные профили в типе employeeCase</li>
     * <li>В классе "Сотрудник" создать группу атрибутов contactInfo с атрибутами:
     *     "Адрес электронной почты" и "Номер внутреннего телефона"</li>
     * <li>На карточку сотрудника вывести контент content типа "Параметры объекта"
     *     (группа атрибутов: contactInfo)</li>
     * <li>Выдать права на просмотр контента content только профилю headsProfile</li>
     * <li>Создать сотрудника employee типа employeeCase с конкурентной лицензией</li>
     * <li>Добавить сотрудника employee в группу пользователей employees</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку сотрудника employee</li>
     * <li>Проверяем что в шапке модуля отображается кнопка открытия панели модуля проверки прав</li>
     * <li>Проверяем иконка кнопки - "Закрытый глаз"</li>
     * <li>Проверяем что при наведении указателя мыши на кнопку появляется подсказка:
     *     "Открыть модуль проверки прав"</li>
     * <li>Кликаем по кнопке открытия панели модуля проверки прав</li>
     * <li>Проверяем что открылась панель для работы с модулем</li>
     * <li>Проверяем что кнопка выключения модуля не отображается</li>
     * <li>Проверяем что блоки с правами пользователя на панели не отображаются</li>
     * <li>В дереве выбора пользователя выбираем сотрудника employee</li>
     * <li>Кликаем по кнопке "Отобразить"</li>
     * <li>Проверяем что кнопка открытия модуля сменила иконку на "Открытый глаз"</li>
     * <li>Проверяем что с карточки сотрудника пропал контент content</li>
     * <li>Проверяем что на панели работы с модулем появилась кнопка выключения модуля</li>
     * <li>Проверяем что на панели работы с модулем появились блоки "Лицензии", "Профили", "Роли" и "Группы
     * пользователей"</li>
     * <li>Проверяем что в блоке "Лицензии" указана лицензия "concurrent"</li>
     * <li>Проверяем что в блоке "Профили" указан профиль employeesProfile</li>
     * <li>Проверяем что в блоке "Роли" указаны роли "Сам сотрудник", "Сотрудник" и "Сотрудник отдела сотрудника"</li>
     * <li>Проверяем что в блоке "Группы пользователей" указана группа employees</li>
     * <li>Кликаем по кнопке "Скрыть"</li>
     * <li>Проверяем что панель скрылась</li>
     * <li>Проверяем что контент content по-прежнему невиден</li>
     * <li>Кликаем по кнопке открытия панели модуля проверки прав</li>
     * <li>Проверяем что панель для работы с модулем раскрылась</li>
     * <li>Проверяем что отображаемые права сотрудника не изменились</li>
     * <li>Кликаем по кнопке "Отключить модуль"</li>
     * <li>Проверяем что панель для работы с модулем скрылась</li>
     * <li>Проверяем что иконка кнопки открытия панели сменилась на "Закрытый глаз"</li>
     * <li>Проверяем что на карточке появился контент content</li>
     * </ol>
     */
    @Test
    public void testShowSelectedEmployeeRights()
    {
        // Подготовка
        SecurityGroup heads = DAOSecurityGroup.create();
        SecurityGroup employees = DAOSecurityGroup.create();
        DSLSecurityGroup.add(heads, employees);

        SecurityProfile headsProfile = DAOSecurityProfile.create(true, heads, SysRole.employee());
        SecurityProfile employeesProfile = DAOSecurityProfile.create(true, employees, SysRole.employee());
        DSLSecurityProfile.add(headsProfile, employeesProfile);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        DSLSecurityProfile.grantAllPermissionsForCase(employeesProfile, employeeCase);

        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Attribute email = SysAttribute.email(employeeClass);
        Attribute phone = SysAttribute.internalPhoneNumber(employeeClass);

        GroupAttr contactInfo = DAOGroupAttr.create(employeeClass);
        DSLGroupAttr.add(contactInfo, email, phone);

        ContentForm content = DAOContentCard.createPropertyList(employeeClass, contactInfo);
        content.setProfiles(headsProfile);
        DSLContent.add(content);

        Bo ou = SharedFixture.ou();
        Bo employee = DAOEmployee.create(employeeCase, ou, false, true);
        DSLBo.add(employee);

        DSLSecurityGroup.addUsers(employees, employee);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(employee);

        GUIPCModule.assertShowModuleButtonPresent(true);
        GUIPCModule.assertModuleEnabled(false);
        GUIPCModule.assertShowButtonHint("Открыть модуль проверки прав");

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.assertModulePanelShowing(true);

        GUIPCModule.assertDisableButtonShowing(false);

        GUIPCModule.assertBlockPresense(GUIPCModule.LICENSES, false);
        GUIPCModule.assertBlockPresense(GUIPCModule.PROFILES, false);
        GUIPCModule.assertBlockPresense(GUIPCModule.ROLES, false);
        GUIPCModule.assertBlockPresense(GUIPCModule.USER_GROUPS, false);

        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        GUIPCModule.assertDisableButtonShowing(true);

        GUIPCModule.assertModuleEnabled(true);

        GUIContent.assertAbsence(content);

        GUITester.assertTextPresent(GUIPCModule.X_BLOCK_CAPTION, "Лицензии", GUIPCModule.LICENSES);
        GUITester.assertTextPresent(GUIPCModule.X_BLOCK_CAPTION, "Профили", GUIPCModule.PROFILES);
        GUITester.assertTextPresent(GUIPCModule.X_BLOCK_CAPTION, "Роли", GUIPCModule.ROLES);
        GUITester.assertTextPresent(GUIPCModule.X_BLOCK_CAPTION, "Группы пользователей", GUIPCModule.USER_GROUPS);

        GUIPCModule.assertLicenses("concurrent");
        GUIPCModule.assertProfiles(employeesProfile);
        GUIPCModule.assertRoles(SysRole.currentUser(), SysRole.employee(), SysRole.userOuEmployee());
        GUIPCModule.assertGroups(employees);

        GUIPCModule.clickHideButton();
        GUIPCModule.assertModulePanelShowing(false);

        GUIContent.assertAbsence(content);

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.assertModulePanelShowing(true);

        GUIPCModule.assertLicenses("concurrent");
        GUIPCModule.assertProfiles(employeesProfile);
        GUIPCModule.assertRoles(SysRole.currentUser(), SysRole.employee(), SysRole.userOuEmployee());
        GUIPCModule.assertGroups(employees);

        GUIPCModule.clickDisableButton();

        GUIPCModule.assertModuleEnabled(false);
        GUIPCModule.assertModulePanelShowing(false);

        GUIContent.assertPresent(content);
    }

    /**
     * Тестирование выключения модуля проверки прав при разлогинивании в приложении
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Включить модуль проверки прав</li>
     * <li>Выбрать сотрудника employee</li>
     * <li>Нажать Отобразить</li>
     * <br>
     * <b>Проверка</b>
     * <li>У сотрудника employee нет прав на просмотр карточки отдела</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Разлогиниться в приложении </li>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку отдела ou</li>
     * <br>
     * <b>Проверка</b>
     * <li>Карточка отобразилась как для суперпользователя</li>
     * </ol>
     */
    @Test
    public void testShutdownModuleAtLogout()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        //Выполнение действий
        GUILogon.asSuper();
        GUIBo.goToCard(ou);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        //Проверка
        GUIBo.assertNotRights();
        GUIPCModule.clickHideButton();

        //Выполнение действий
        GUILogon.logout();
        GUILogon.asSuper();
        GUIBo.goToCard(ou);

        //Проверка
        GUIBo.assertThatBoCard(ou);
    }

    /**
     * Тестирование отображение кнопок и ссылок верхнего меню при включенном модуле проверки прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <li>Создать группу пользователей secGroup и добавить в неё employee</li>
     * <li>В типе scCase создаём профиль прав доступа secProfile с параметрами:
     *      <ul>
     *          <li>"Для нелицензированных пользователей" = "да"</li>
     *          <li>"Модель группы пользователя" = "secGroup"</li>
     *          <li>"Роль" = "Сотрудник"</li>
     *      </ul>
     * </li>
     * <li>Выдать профилю secProfile все права</li>
     * <li>Добавить элемент верхнего меню reference - ссылка на карточку Сотрудник/Карточка сотрудника</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Включить модуль проверки прав</li>
     * <li>Выбрать сотрудника employee</li>
     * <li>Нажать Отобразить</li>
     * <br>
     * <b>Проверка</b>
     * <li>В верхнем меню отображается ссылка</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти по ссылке reference</li>
     * <br>
     * <b>Проверка</b>
     * <li>Открылась карточка сотрудника</li>
     * </ol>
     */
    @Test
    public void testVisibleTopMenuWithEmbeddedModule()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase, scCase);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employee);

        SecurityGroup secGroups = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroups);
        DSLSecurityGroup.addUsers(secGroups, employee);

        SecurityProfile secProfile = DAOSecurityProfile.create(false, secGroups, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, employeeCase);

        //Настройка
        MenuItem chapter = DAOMenuItem.createChapter(true);
        MenuItem reference = DAOMenuItem.createReference(chapter, employeeCase, true);
        DSLMenuItem.add(chapter, reference);

        DSLNavSettings.editVisibilitySettings(true, true);

        //Действия
        GUILogon.asSuper();
        GUIBo.goToCard(ou);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        //Проверка
        GUINavSettingsOperator.assertMenuItemExists(true, chapter);

        //Выполнение действий
        GUINavSettingsOperator.clickMenuItem(chapter, reference);

        //Проверка
        GUIBo.assertThatBoCard(employee);
    }

    /**
     * Тестирование возможности просмотра формы с открытым модулем проверки прав, если у контента на форме
     * настроено условие отображения "Автор - равно атрибуту текущего пользователя - Автор".
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$88401807
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00848
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     *
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <li>Создать группу пользователей secGroup и добавить в неё employee</li>
     * <li>В типе scCase создаём профиль прав доступа secProfile с параметрами:
     *      <ul>
     *          <li>"Для лицензированных пользователей" = "да"</li>
     *          <li>"Модель группы пользователя" = "secGroup"</li>
     *          <li>"Роль" = "Сотрудник"</li>
     *      </ul>
     * </li>
     * <li>Выдать профилю secProfile все права</li>
     * <li>Создать контент типа список объектов objectList на карточке компании</li>
     * <li>Настроить условие отображения контента:
     * "Автор - равно атрибуту текущего пользователя - Автор".</li>
     * <li>Перейти под администратором в интерфейс оператора</li>
     * <li>Открыть модуль проверки прав и выбрать созданного сотрудника</li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить, что контент objectList присутствует на форме</li>
     * <li>Проверить, что не появились ошибки</li>
     * </ol>
     */
    @Test
    public void testFormDisplayedIfContentContainsEqualsCurrentUserCondition()
    {
        final MetaClass ouCase = DAOOuCase.create();
        final MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(ouCase, employeeCase);

        final Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        final Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        final SecurityGroup secGroups = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroups);
        DSLSecurityGroup.addUsers(secGroups, employee);

        final SecurityProfile secProfile = DAOSecurityProfile.create(true, secGroups, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, employeeCase);

        final MetaClass rootClass = DAORootClass.create();
        final MetaClass ouClass = DAOOuCase.createClass();
        final ContentForm objectList = DAOContentCard.createObjectList(rootClass.getFqn(), ouClass);
        FilterBlockOr or = new FilterBlockOr(SysAttribute.author(rootClass),
                FilterCondition.CONTAINS_USER_ATTRIBUTE, false,
                SysAttribute.author(DAOEmployeeCase.createClass()).getFqn());
        FilterBlockAnd and = new FilterBlockAnd(or);
        objectList.setVisibilityCondition(new ListFilter(and));
        DSLContent.add(objectList);

        GUILogon.asSuper();

        GUIBo.goToCard(SharedFixture.root());
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        GUIContent.assertPresent(objectList);
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование просмотра нелицензированным пользователем карточки объекта, с контентом "параметры связанного
     * объекта", выведенный по атрибуту, на просмотр которого у него нет прав и в котором имеется кнопка
     * пользовательского ДПС
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00426
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97133428
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип scCase класса Запрос</li>
     * <li>Создать нелицензированного сотрудника unlicensed со всеми правами</li>
     * <li>Создать ДПС userEvent, которое применяется к объекту класса "Отдел" </li>
     * <li>Создаем группу атрибутов groupAttr (по системным атрибутам отдела)</li>
     * <li>Добавить контент (content) «Параметры связанного объекта» по атрибуту "Контрагент (Отдел)" на карточку
     * запроса типа scCase, выводимый по ранее созданной группе атрибутов - groupAttr</li>
     * <li>Создать запрос типа scCase - sc</li>
     * <li>На панели действий content добавить новую кнопку вызывающую ДПС - userEvent</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти под нелицензированным сотрудником - unlicensed</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Проверить отсутсвтие кнопки вызывающей ДПС - userEvent</li>
     * </ol>
     */
    @Test
    public void testViewingUnlicensedUserObjectCardWithParametersAssociatedObjectWithDPS()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.createClass();
        DSLMetaClass.add(scCase);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);
        Bo unlicensed = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, false);
        DSLBo.add(unlicensed);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);

        EventAction userEvent = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true, ouCase);
        DSLEventAction.add(userEvent);

        GroupAttr groupAttr = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(groupAttr, DSLGroupAttr.getOuSystemAttr());
        ContentForm content = DAOContentCard.createRelObjPropList(scCase, SysAttribute.clientOU(scCase), groupAttr);
        DSLContent.add(content);

        GUILogon.asSuper();
        GUIContent.addUserEventButton(content, userEvent, AppliedTo.RELATED_OBJECT);

        //Действия
        GUILogon.login(unlicensed);
        GUIBo.goToCard(sc);
        GUIContent.assertAbsence(content);
    }

    /**
     * Тестирование недоступности нелицензируемому пользователю кнопки пользовательского действия в контенте "Список
     * файлов", под модулем проверки прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00690
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$114607533
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и тип userCase</li>
     * <li>Создать отдел ou, и в нем нелицензированного сотрудника unlicensed со всеми правами</li>
     * <li>Создать пользовательское ДПС userEvent, которое применяется к объекту класса userClass </li>
     * <li>Добавить контент «Список файлов" на карточку типа userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Войти в систему под суперпользователем</li>
     * <li>На панели действий контента "Список файлов" добавить новую кнопку вызывающую ДПС - userEvent</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить наличие кнопки вызывающей ДПС userEvent в панели действий контента "Список файлов"</li>
     * <li>Включить модуль проверки прав под сотрудником unlicensed</li>
     * <li>Проверить отсутствие кнопки вызывающей ДПС userEvent в панели действий контента "Список файлов"</li>
     * </ol>
     */
    @Test
    public void testUserEventButtonAbsenceForUnlicensedWithPermissionsCheckModule()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass);
        DSLMetaClass.add(userCase);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        Bo unlicensed = DAOEmployee.create(SharedFixture.employeeCase(), ou, true, false);
        DSLBo.add(unlicensed);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return");
        DSLScriptInfo.addScript(script);
        EventAction userEvent = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true, userCase);
        DSLEventAction.add(userEvent);

        ContentForm content = DAOContentCard.createFileList(userCase);
        DSLContent.add(content);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GUILogon.asSuper();
        GUIContent.addUserEventButton(content, userEvent, AppliedTo.CURRENT_OBJECT);

        //Выполнение действий и проверки
        GUIBo.goToCard(userBo);
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_FIRE_USER_EVENT);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, unlicensed);
        GUIPCModule.clickShowButton();
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_FIRE_USER_EVENT);
    }

    /**
     * Тестирование отображения пользователей в выпадающем списке в модуле проверки прав, когда включена защита от
     * подмены параметров запроса на получение данных для выпадающих списков
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$116892147
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou</li>
     * <li>Создать сотрудника employee ходящего в отдел ou </li>
     * <li>Создать суперпользователя superUser, связанного с сотрудником employee, параметр "Учитывать права
     * сотрудника" - true</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Открыть модуль проверки прав</li>
     * <li>Проверить, что в модуле проверки прав, в выпадающем списке присутствуют элементы ou -> employee</li>
     * </ol>
     */
    @Test
    public void testPresenceUserListWhenValidationPossibleValuesIsEnabled()
    {
        //Подготовка
        Bo ou = SharedFixture.ou();
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);

        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser, employee, true);

        //Действия и проверки
        GUILogon.login(employee);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.assertPresentElement(ou, employee);
    }

    /**
     * Тестирование выполнения скрипта вычислимого атрибута при использовании модуля проверки прав и при установленном
     * параметре ru.naumen.core.client.card.computeOnlyVisibleCompAttributes = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00445
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/dbaccess.properties
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$214610825
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить скрипт:
     * <pre>beanFactory.getBean('configurationProperties').setComputeOnlyVisibleCompAttributesOnCards(true)</pre>
     * </li>
     * <li>Создать пользовательский класс userClass и дочерний тип userCase</li>
     * <li>Создать атрибут compAttr типа "Целое число":
     * <pre>
     *     вычислимый: да
     *     скрипт: return 6
     * </pre>
     * </li>
     * <li>Добавить атрибут compAttr в системную группу атрибутов</li>
     * <li>Создать лицензированного пользователя employee</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти в систему под суперпользователем</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что значение атрибута compAttr равно 6</li>
     * <li>Раскрыть модуль проверки прав</li>
     * <li>Выбрать пользователя employee и нажать "Отобразить"</li>
     * <li>Проверить, что значение атрибута compAttr равно 6</li>
     * </ol>
     */
    @Test
    public void testProcessComputableAttrWithPermissionsCheckModule()
    {
        //Подготовка
        DSLConfiguration.setComputeOnlyVisibleCompAttributesOnCards(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 6");
        DSLScriptInfo.addScript(script);
        Attribute compAttr = DAOAttribute.createInteger(userClass);
        compAttr.setComputable(Boolean.TRUE.toString());
        compAttr.setScript(script.getCode());
        DSLAttribute.add(compAttr);
        GroupAttr sysGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysGroup, new Attribute[] { compAttr }, new Attribute[] {});

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверок
        GUILogon.asSuper();
        GUIBo.goToCard(userBo);
        GUIAttribute.assertAttributeIntValue(compAttr, 6);
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(SharedFixture.ou(), employee);
        GUIPCModule.clickShowButton();
        GUIAttribute.assertAttributeIntValue(compAttr, 6);
    }

    /**
     * Тестирование обновления элементов интерфейса карточки объекта, если в модуле проверки прав
     * выбран сотрудник, у которого отсутствуют права на просмотр карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$187747137
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать типы отдела ouCase и сотрудника emplCase</li>
     * <li>Создать отдел ou, и в нем сотрудника employee</li>
     * <li>В типе ouCase забрать право на просмотр карточки у сотрудников типа emplCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку ou</li>
     * <li>Проверить, что на карточке присутствуют элементы: название, кнопки в шапке, панель вкладок</li>
     * <li>Включить модуль проверки прав под сотрудником employee</li>
     * <li>Проверить, что карточка с соответствующими элементами интерфейса отсутствует</li>
     * <li>Нажать кнопку "Отключить модуль"</li>
     * <li>Проверить, что на карточке присутствуют элементы: название, кнопки в шапке, панель вкладок</li>
     * </ol>
     */
    @Test
    public void testUpdateCardViewWithPermissionsCheckModule()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, emplCase);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(emplCase, ou, false, true);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        ru.naumen.selenium.security.SecurityProfile profile = new ru.naumen.selenium.security.SecurityProfile(userGroup,
                new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, emplCase);
        rights.addAllRights(ouCase);
        rights.removeRight(ouCase, AbstractBoRights.VIEW_CARD);
        rights.apply();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(ou);
        String cardTitle = String.format("%s \"%s\"", ouCase.getTitle(), ou.getTitle());
        GUIBo.assertBoCardViewAccess(cardTitle, true);

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);

        GUIPCModule.clickShowButton();
        GUIBo.assertBoCardViewAccess(cardTitle, false);

        GUIPCModule.clickDisableButton();
        GUIBo.assertBoCardViewAccess(cardTitle, true);
    }

    /**
     * Тестирование обновления элементов интерфейса карточки объекта, если в режиме модуля проверки прав
     * добавлен объект, просмотр карточки которого, не доступен сотруднику из модуля проверки прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$187747137
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс отдела userClass</li>
     * <li>Создать типы отдела ouCase и тип сотрудника emplCase</li>
     * <li>Создать отдел ou, и в нем сотрудника employee</li>
     * <li>В типе ouCase забрать право на просмотр карточки у сотрудников типа emplCase</li>
     * <li>На карточке компании добавить контент "Список вложенных объектов" objectList с классом userClass</li>
     * <li>Создать модель данных отдела bo</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку отдела ouNew</li>
     * <li>Включить модуль проверки прав под сотрудником employee</li>
     * <li>На контенте objectList нажать кнопку "Добавить"</li>
     * <li>На форме добавления отдела заполнить заголовок</li>
     * <li>На форме добавления отдела нажать кнопку сохранить</li>
     * <li>Проверить, что нет доступа к созданной карточке отдела ouNew</li>
     * <li>Нажать кнопку "Отключить модуль"</li>
     * <li>Проверить, что на карточке присутствуют элементы: название, кнопки в шапке, панель вкладок</li>
     * </ol>
     */
    @Test
    public void testUpdateCardViewWithPermissionsCheckModuleWhenAddedObject()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();
        MetaClass userClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(userClass);
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, emplCase);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(emplCase, ou, false, true);
        DSLBo.add(employee);

        UserGroup userGroup = new UserGroup(employee);
        ru.naumen.selenium.security.SecurityProfile profile =
                new ru.naumen.selenium.security.SecurityProfile(userGroup, new EmployeeRole(), true);
        RightGroup rights = new RightGroup(profile, emplCase);
        rights.addAllRights(ouCase);
        rights.addAllRights(rootClass);
        rights.removeRight(ouCase, AbstractBoRights.VIEW_CARD);
        rights.apply();

        ContentForm objectList = DAOContentCard.createChildObjectAdvlist(rootClass.getFqn(),
                userClass,
                DAOGroupAttr.createSystem(userClass));
        DSLContent.add(objectList);
        Bo ouNew = DAOOu.create(ouCase);
        String cardTitle = String.format("%s \"%s\"", ouCase.getTitle(), ouNew.getTitle());

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        Set<String> oldUuids = DSLBo.getUuidsByFqn(ouCase.getFqn());
        objectList.advlist().toolPanel().clickAdd();
        GUIForm.fillAttribute(GUIContentTemplate.TITLE_ATTR, ouNew.getTitle());
        GUIForm.applyForm();
        ouNew.setUuid(DSLBo.getCreatedObjectUuid(ouCase.getFqn(), oldUuids));
        Cleaner.afterTest(true, () -> DSLBo.delete(ouNew));

        GUIBo.assertBoCardViewAccess(cardTitle, false);
        GUIPCModule.clickDisableButton();
        GUIBo.assertBoCardViewAccess(cardTitle, true);
    }

    /**
     * Тестирование модуля проверки прав под пользователем, связанным с суперпользователем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00276 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$280536007 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудников employeeUser и employee в отделе ou</li>
     * <li>Создать суперпользователя superUser и связать его с сотрудником employeeUser</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под пользователем employeeUser</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>В модуле проверки прав выбрать сотрудника employee, нажать "Отобразить"</li>
     * <li>Проверить, что у employee нет прав на просмотр карточки отдела ou</li>
     * </ol>
     */
    @Test
    public void testEmployeeUserPermissionCheckModule()
    {
        // Подготовка
        Bo ou = SharedFixture.ou();
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo employeeUser = DAOEmployee.create(employeeCase, ou, false);
        Bo employee = DAOEmployee.create(employeeCase, ou, false);
        DSLBo.add(employeeUser, employee);

        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser, employeeUser);

        // Действия и проверки
        GUILogon.login(employeeUser);
        GUIBo.goToCard(ou);

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        GUIBo.assertNotRights();
    }

    /**
     * Тестирование построения левого меню под модулем проверки прав
     * под различными сотрудниками
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00678
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$300402973
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$307429518
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип для класса сотрудник emplCase</li>
     * <li>Создать сотрудников employee, superEmployee1, superEmployee2 типа emplCase в отделе ou</li>
     * <li>Создать суперпользователя superUser1 связанного с сотрудником superEmployee1 с учётом прав сотрудника</li>
     * <li>Создать суперпользователя superUser2 связанного с сотрудником superEmployee2 без учёта прав сотрудника</li>
     * <li>Создать элемент в левом меню favoritesItem (например "Избранное") и плитку связанную с ним favouritesTile
     * и привязать к ним любой профиль, который не используется для сотрудника employee</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под пользователем суперпользователем и перейти в оператор</li>
     * <li>Проверить присутствие плитки favouritesTile на панели быстрого доступа</li>
     * <li>Открыть левое меню и проверить присутствие элемента favoritesItem</li>
     * <li>В модуле проверки прав выбрать сотрудника employee, нажать "Отобразить"</li>
     * <li>Проверить отсутствие плитки favouritesTile на панели быстрого доступа</li>
     * <li>Открыть левое меню и проверить отсутствие элемента favoritesItem</li>
     * <li>Отключить модуль проверки прав</li>
     * <li>В модуле проверки прав выбрать сотрудника superEmployee1, нажать "Отобразить"</li>
     * <li>Проверить отсутствие плитки favouritesTile на панели быстрого доступа</li>
     * <li>Открыть левое меню и проверить отсутствие элемента favoritesItem</li>
     * <li>Отключить модуль проверки прав</li>
     * <li>В модуле проверки прав выбрать сотрудника superEmployee2, нажать "Отобразить"</li>
     * <li>Проверить присутствие плитки favouritesTile на панели быстрого доступа</li>
     * <li>Открыть левое меню и проверить присутствие элемента favoritesItem</li>
     * <li>Отключить модуль проверки прав</li>
     * </ol>
     */
    @Test
    public void testLeftMenuItemVisible()
    {
        // Подготовка
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);
        Bo ou = SharedFixture.ou();
        Bo employee = DAOEmployee.create(emplCase, ou, false);
        Bo superEmployee1 = DAOEmployee.create(emplCase, ou, false);
        Bo superEmployee2 = DAOEmployee.create(emplCase, ou, false);
        DSLBo.add(employee, superEmployee1, superEmployee2);

        SuperUser superUser1 = DAOSuperUser.create();
        DSLSuperUser.add(superUser1, superEmployee1, true);
        SuperUser superUser2 = DAOSuperUser.create();
        DSLSuperUser.add(superUser2, superEmployee2, false);

        LeftMenuItem favoritesItem = DAOLeftMenuItem.createFavorites();
        favoritesItem.setProfiles(SharedFixture.secProfileLic().getCode());
        DSLLeftMenuItem.add(favoritesItem);

        QuickAccessTile favouritesTile = DAOQuickAccessTile.createQuickAccessTile(favoritesItem, true);
        favouritesTile.setProfiles(SharedFixture.secProfileLic().getCode());
        DSLQuickAccessTile.add(favouritesTile);

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        GUINavSettingsOperator.assertExistsQuickAccessTile(true, favouritesTile);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemPresent(favoritesItem);

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, employee);
        GUIPCModule.clickShowButton();

        GUINavSettingsOperator.assertExistsQuickAccessTile(false, favouritesTile);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(favoritesItem);
        GUIPCModule.clickDisableButton();

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, superEmployee1);
        GUIPCModule.clickShowButton();

        GUINavSettingsOperator.assertExistsQuickAccessTile(false, favouritesTile);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(favoritesItem);
        GUIPCModule.clickDisableButton();

        GUIPCModule.clickShowModuleButton();
        GUIPCModule.selectEmployee(ou, superEmployee2);
        GUIPCModule.clickShowButton();

        GUINavSettingsOperator.assertExistsQuickAccessTile(true, favouritesTile);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemPresent(favoritesItem);
        GUIPCModule.clickDisableButton();
    }
}