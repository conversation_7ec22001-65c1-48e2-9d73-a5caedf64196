package ru.naumen.selenium.cases.admin.sets;

import java.io.File;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLAdminLite;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.admin.AdminLitePage;
import ru.naumen.selenium.casesutil.model.admin.AdminLiteSettings;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;

/**
 * Тестирование разметки настроек метаинформации комплектами в интерфесе администратора
 * <AUTHOR>
 * @since 12.12.2024
 */
class MarkMetainfoSettingsBySettingsSet3Test extends AbstractTestCaseJ5
{
    private static SettingsSet userSet;
    private static MetaClass rootClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * <li>Добавить комплект setCase.</li>
     * <li>Отредактировать класс Компания: выбрать в атрибут Комплект setCase.</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);

        userSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(userSet);
        rootClass = DAORootClass.create();
        rootClass.setSettingsSet(userSet);
        DSLMetaClass.edit(rootClass);
    }

    /**
     * Тестирование загрузки полной метаинформации с настройкой, которая размечена комплектом, которого нет на стенде
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить полную метаинформацию.</li>
     * <li>Удалить комплект setCase</li>
     * <li>Перейти в настройки класса Компания</li>
     * <li>Проверить, что поле Комплект пустое.</li>
     * <li>Загрузить метаинформацию из шага 2</li>
     * <li>Открыть настройки класса Компания</li>
     * <li>Проверить, что поле Комплект заполнено ссылкой на комплект setCase</li>
     * </ol>
     */
    @Test
    void testImportFullMetaInfoAfterDeletingSet()
    {
        // Действия и проверки
        File metainfo = DSLMetainfoTransfer.exportMetainfo();

        DSLSettingsSet.delete(userSet);
        Cleaner.afterTest(() -> userSet.setExists(true));

        GUILogon.asSuper();
        GUIMetaClass.goToCard(rootClass);
        GUISettingsSet.assertSettingsSetOnCards("");
        DSLMetainfoTransfer.importMetainfo(metainfo);
        tester.refresh();
        GUISettingsSet.assertSettingsSetOnCards(userSet.getTitle());
    }

    /**
     * Тестирование наличия ошибки при загрузке частичной метаинформации с настройкой, которая размечена комплектом,
     * которого нет на стенде.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить частичную метаинформацию с настройками класса Компания.</li>
     * <li>Удалить комплект setCase</li>
     * <li>Загрузить метаинформацию из шага 2</li>
     * <li>Проверить, что метаинформация не загрузилась, появилась ошибка "Метаинформация не может быть загружена.
     * Настройки из файла не могут быть загружены. Настройка "Компания" (root) ссылается на комплект "setCase",
     * который отсутствует в системе.".</li>
     * </ol>
     */
    @Test
    void testImportPartialMetaInfoAfterDeletingSet()
    {
        // Действия и проверки
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectClassSettings(exportModel, rootClass);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(exportModel);

        DSLSettingsSet.delete(userSet);
        Cleaner.afterTest(() ->
        {
            DSLSettingsSet.add(userSet);
            DSLMetaClass.edit(rootClass);
        });

        GUILogon.asSuper();
        GUINavigational.goToAdministration();
        GUIAdmin.uploadMetainfoFailed(metainfo.getAbsolutePath(),
                String.format(
                        "Метаинформация не может быть загружена. Настройки из файла не могут быть загружены. Настройка "
                        + "\"Компания\" (root) ссылается на комплект \"%s\", который отсутствует в системе.",
                        userSet.getCode()));
    }

    /**
     * Тестирование загрузки частичной метаинформации с настройкой, которая размечена комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить частичную метаинформацию с настройками класса Компания.</li>
     * <li>Отредактировать класс Компания: в поле Комплект выбрать [не указано]</li>
     * <li>Загрузить метаинформацию из шага 2</li>
     * <li>Проверить, что метаинформация загрузилась успешно.</li>
     * <li>Открыть настройки класса Компания</li>
     * <li>Проверить, что поле Комплект заполнено ссылкой на комплект setCase.</li>
     * </ol>
     */
    @Test
    void testImportPartialMetaInfoAfterRemovingSetFromCompanyParameter()
    {
        // Действия и проверки
        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectClassSettings(exportModel, rootClass);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(exportModel);
        DSLMetaClass.edit(DAORootClass.create());
        DSLMetainfoTransfer.importMetainfo(metainfo);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(rootClass);
        GUISettingsSet.assertSettingsSetOnCards(userSet.getTitle());
    }

    /**
     * Тестирование загрузки полной метаинформации с замещением с настройкой, которая размечена комплектом, которого
     * нет на стенде.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить полную метаинформацию.</li>
     * <li>Удалить комплект setCase</li>
     * <li>Загрузить метаинформацию из ш. 2 с полным замещением.</li>
     * <li>Открыть настройки класса Компания</li>
     * <li>Проверить, что поле Комплект заполнено ссылкой на комплект setCase</li>
     * </ol>
     */
    @Test
    void testImportFullMetaInfoWithFullReloadAfterDeletingSet()
    {
        // Действия и проверки
        File metainfo = DSLMetainfoTransfer.exportMetainfo();

        DSLSettingsSet.delete(userSet);
        Cleaner.afterTest(() -> userSet.setExists(true));

        DSLMetainfoTransfer.importFullReloadMetainfo(metainfo);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(rootClass);
        GUISettingsSet.assertSettingsSetOnCards(userSet.getTitle());
    }

    /**
     * Тестирование загрузки метаинформации без настроек, доступных админ-лайту, с настройкой, которая размечена
     * комплектом, которого нет на стенде.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить на стенд лицензию с модулем admin-lite.</li>
     * <li>Выбрать настройки для облегченного интерфейса: Настройка системы - Администрирование - Интерфейс, и
     * включить его.</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить метаинформацию без настроек, доступных в облегченном интерфейсе.</li>
     * <li>Удалить комплект setCase</li>
     * <li>Загрузить метаинформацию из ш. 2.</li>
     * <li>Открыть настройки класса Компания</li>
     * <li>Проверить, что поле Комплект заполнено ссылкой на комплект setCase</li>
     * </ol>
     */
    @Test
    void testImportPartialMetaInfoWithoutAdminLiteAfterDeletingSet()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.ADMIN_LITE_LICENSE);

        AdminLiteSettings settings = new AdminLiteSettings();
        settings.setSystemSettings(
                Lists.newArrayList(AdminLitePage.EVENT_ACTIONS.getCode(),
                        AdminLitePage.INTERFACE.getCode(), AdminLitePage.SCHEDULER.getCode()));
        settings.setEnabled(true);
        DSLAdminLite.save(settings);

        // Действия и проверки
        File metainfoWithoutAdminLite = DSLMetainfoTransfer.exportMetainfoithoutAdminLite();

        DSLSettingsSet.delete(userSet);
        Cleaner.afterTest(() -> userSet.setExists(true));

        DSLMetainfoTransfer.importMetainfo(metainfoWithoutAdminLite);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(rootClass);
        GUISettingsSet.assertSettingsSetOnCards(userSet.getTitle());
    }

    /**
     * Тестирование загрузки метаинформации без настроек, доступных админ-лайту, с настройкой, которая размечена
     * комплектом, которого нет на стенде.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Выгрузить полную метаинформацию.</li>
     * <li>Отключить функционал комплектов скриптом beanFactory.getBean('settingsSetStorageServiceConfiguration')
     * .setSettingSetsEnabled(false)</li>
     * <li>Загрузить метаинформацию из ш. 2.</li>
     * <li>Проверить, что метаинформация успешно загрузилась.</li>
     * <li>Открыть настройки класса Компания, обновить страницу</li>
     * <li>Проверить, что в настройках класса Компания нет поля Комплект.</li>
     * </ol>
     */
    @Test
    void testImportFullMetaInfoAfterDisablingSets()
    {
        // Действия и проверки
        File metainfo = DSLMetainfoTransfer.exportMetainfo();
        DSLConfiguration.setSettingSetsEnabled(false, true);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(rootClass);
        GUITester.assertAbsent(Any.CODE_PATTERN, "Свойство \"Комплект\" отображается в настройках Компании",
                "Info.settingsSet");
    }
}
