package ru.naumen.selenium.cases.operator.process;

import static org.junit.Assert.assertEquals;
import static ru.naumen.selenium.casesutil.messages.EventListMessages.EC_ESCALATION_CHANGED;
import static ru.naumen.selenium.casesutil.messages.EventListMessages.EC_ESCALATION_TIME;
import static ru.naumen.selenium.casesutil.messages.EventListMessages.ED_ESCALATION_LEVEL;

import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.eventcleaner.EventCleanerConstants.Other;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.EventListMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BackTimerType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.Strategy;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Эскалация в ИО
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00452
 * <AUTHOR>
 * @since 15.04.2013
 *
 */
public class Escalation2Test extends AbstractTestCase
{
    private static final String CHANGE_ESCALATION_TIME_PATTERN = "Изменено время для уровней эскалации '%s':";

    /**
     * Тестирование возможности одновременного редактирования объекта в скрипте в двух транзакциях, редактирование в
     * одной из которых приводит к
     * изменению состояния счетчика времени, что в свою очередь приводит к пересчету схем эскалации
     *
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00376
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00449
     *
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$67780717
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Настроить счётчик времени (timeCnt):
     *  <ol>
     *      <li>Скрипт, условие начала отсчета:
     *          <pre>
     *              return subject.state == 'registered'
     *          </pre>
     *      </li>
     *      <li>Скрипт, условие приостановки отсчета:
     *          <pre>
     *          return (subject.description?.contains('paused') || subject.descriptionInRTF?.contains('paused'))
     *          </pre>
     *      </li>
     *      <li>Объекты: Запрос</li>
     *      <li>Метрика времени: Запас времени обслуживания</li>
     *      <li>Тип условия: По скрипту</li>
     *  </ol>
     * </li>
     * <li>Настроить эскалацию:
     *  <ol>
     *      <li>Действие для эскалации (escEvent), скрипт:
     *          <pre>
     *              utils.event(subject,'Эскалация')
     *          </pre>
     *      </li>
     *      <li>Объекты: Запрос</li>
     *      <li>Счетчик времени: timeCnt</li>
     *      <li>Добавить 1 уровень эскалации escLevel: Условие: По истечении доли времени (%), Значение: 70,
     *      Действие: escEvent, Выполнять действие при изменении схемы: да</li>
     *  </ol>
     * </li>
     * <li>В класс Запрос добавить атрибут типа "Счётчик времени (обратный)" (backTimer), счётчик времени: timeCnt</li>
     * <li>Настроиь таблицу соответствий эскалаций: определяющий атрибут "Часовой пояс запроса", значение: [любое
     * значение]</li>
     * <li>Добавить в ТС эскалации строку: Схема эскалации=escalationScheme, Тип объекта=Запрос</li>
     * <li>Создать группу атрибутов grp. Добавить в нее атрибуты: descriptionInRTF, description, backTimer</li>
     * <li>Создать контент "Параметры объекта". Добавить на карточку объекта Запрос, группа атрибутов grp</li>
     * <li>Создать контент "История событий". Добавить на карточку объекта Запрос</li>
     * <li>Создать запрос sc типа scCase</li>
     * <b>Выполнение действия</b>
     * <li>Выполнить скрипт:
     *  <pre>
     *      def subject = utils.get('sc.getUUID()');
     *      utils.editWithoutEventActions(subject, ['descriptionRTF' : 'paused']);
     *      utils.editWithoutEventActions(subject, ['description' : 't'], true);
     *  </pre>
     * </li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <b>Проверка</b>
     * <li>Проверить, что в контенте "История событий" historyContent отсутствует запись маркера из скрипта действия
     * для эскалации</li>
     * <li>Проверить, что в контенте "История событий" historyContent присутствуют 2 записи об изменении схемы
     * эскалации</li>
     * <li>Проверить, что в контенте "История событий" historyContent присутствует 2 записи об изменении уровней
     * эскалации</li>
     * <li>Проверить, что счетчик backTimer находится в статусе приостановлен (PAUSED)</li>
     * <li>Проверить, что значение атрибута description = "t"</li>
     * <li>Проверить, что значение атрибута descriptionInRTF = "paused");</li>
     * </ol>
     */
    @Test
    public void testEditObjectInScriptInTwoTransactionsWithEscalationSchemeRecalc()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        //1. Настроить счётчик времени (далее - timeCnt)
        ScriptInfo startConditionScript = DAOScriptInfo.createNewScriptInfo("return subject.state == 'registered'");
        ScriptInfo pauseConditionScript = DAOScriptInfo.createNewScriptInfo(
                "return (subject.description?.contains('paused') || subject.descriptionInRTF?.contains('paused'))");

        DSLScriptInfo.addScript(startConditionScript);
        DSLScriptInfo.addScript(pauseConditionScript);

        TimerDefinition timeCnt = DAOTimerDefinition.createFloatTimerByScript(scClass.getFqn(),
                SysAttribute.timeZone(scClass).getCode(), SysAttribute.serviceTime(scClass).getCode(),
                SysAttribute.resolutionTime(scClass).getCode());
        timeCnt.setStartCondition(startConditionScript.getCode());
        timeCnt.setPauseCondition(pauseConditionScript.getCode());
        DSLTimerDefinition.add(timeCnt);

        //2. Настроить эскалацию
        //Создаем действие для эскалации
        String scriptBody = "utils.event(subject,'Эскалация')";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeCnt, true, scClass);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "70", true,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //3. В класс Запрос добавить атрибут типа "Счётчик времени (обратный)", в качестве счётчика времени выбрать
        // timeCnt
        Attribute backTimer = DAOAttribute.createBackTimer(scClass.getFqn(), timeCnt, TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(backTimer);

        //Создаём таблицу соответствий
        CatalogItem escRulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scClass,
                backTimer.getCode());
        DSLCatalogItem.add(escRulesSettings);

        RsRow escRow = DAORsRow.create(escRulesSettings, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), SysAttribute.timeZone(scClass).getCode(), "*");
        DSLRsRows.addRowToRSItem(escRow);

        //Создадим группу атрибутов и контент
        Attribute descriptionInRTF = SysAttribute.descriptionInRTF(scClass);
        Attribute description = SysAttribute.description(scClass);

        GroupAttr grp = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(grp, descriptionInRTF, description, backTimer);
        ContentForm contentForm = DAOContentCard.createPropertyList(scClass, grp);
        DSLContent.add(contentForm);

        //Создаем контент история событий
        ContentForm historyContent = DAOContentCard.createEventList(scClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем объекты
        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        //Действия        
        String scriptTemplate = "def subject = utils.get('%s'); "
                                + "utils.editWithoutEventActions(subject, ['%s' : 'paused']);"
                                + "utils.editWithoutEventActions(subject, ['%s' : 't'], true)";

        new ScriptRunner(String.format(scriptTemplate, sc.getUuid(), descriptionInRTF.getCode(), description.getCode()))
                .runScript();
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        // Проверка
        String changeSchemeMsg = String.format("Изменен набор схем эскалации: [%s]", escalationScheme.getTitle());
        String recalcEscMsg = String.format(CHANGE_ESCALATION_TIME_PATTERN, escalationScheme.getTitle());

        GUIEventList.assertEventCategoryIsAbsence(historyContent, Other.userEvent.getTitle());
        GUIEventList.assertEventsCount(historyContent, 2, EC_ESCALATION_CHANGED, changeSchemeMsg);
        GUIEventList.assertEventsCount(historyContent, 2, EC_ESCALATION_TIME, recalcEscMsg);

        String descriptionXPath = String.format(GUIPropertyList.X_VALUE + "/..", contentForm.getXpathId(),
                description.getCode());

        ModelMap object = SdDataUtils.getObject(sc.getMetaclassFqn(), "UUID", sc.getUuid());
        ModelMap countValue = Json.GSON.fromJson(object.get(backTimer.getCode()), Json.MAP_STRING_STRING_TYPE);
        String actual = countValue.get("status");
        Assert.assertEquals("Счетчик не приостановлен", "PAUSED", actual);

        Assert.assertEquals("t", tester.getText(descriptionXPath));
        GUIPropertyList.clickEditLink(contentForm);
        GUIRichText.assertText(descriptionInRTF, "paused");
    }

    /**
     * Тестирование записи об изменении схемы/уровней эскалации в истории событий, если объект был создан через rest
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00449
     * http://sd-jira.naumen.ru/browse/NSDPRD-4461
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отделов ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип услуг serviceCase</li>
     * <li>Создать тип соглашений agreementCase</li>
     * <li>Создать контент "История событий" для типа запросов scCase на карточке</li>
     * <li>Создаем отдел ou, соглашение agreement, услугу service, связываем их</li>
     * <li>Создаем схему эскалации escalationScheme, 2 уровня эскалации escLevel1 и escLevel1</li>
     * <li>Настроим таблицу соответствий эскалаций: определяющий атрибут "Категории" со значением по умолчанию</li>
     * <li>Перейти по ссылке:</li>
     * <pre>
     *  services/rest/create/{0}/{agreement:{1},service:{2},client:{3},description:'{4}',categories:'{5}', timeZone:{6}}?accessKey={7}
     * </pre>
     * <li>где {0} - fqn типа запроса, {1} - uuid соглашения agreement , {2} - uuid услуги service, {3} - uuid отдела
     * ou,</li>
     * <li> {4} - любая строка, {5} - uuid значения категории, {6} - uuid таймзоны по умолчанию, {7} - accessKey для
     * стандартного сотрудника</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем, что в контенте "История событий" eventList присутствует запись "Изменение схемы эскалации"</li>
     * <li>Проверяем, что в контенте "История событий" eventList присутствует запись "Изменение уровней эскалации"</li>
     * </ol>
     */
    @Test
    public void testEscalationChangedByCreatingServiceCallViaRest()
    {
        //Подготовка
        //Создаем метаклассы
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase, ouCase, serviceCase, agreementCase);

        //Создаем контент-историю событий
        ContentForm eventList = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(eventList);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo agreement = DAOAgreement.create(agreementCase);
        agreement.setAgrResolutionTimeRuleUuid(SharedFixture.rsResolutionTime().getUuid());
        agreement.setAgrPriorityRuleUuid(SharedFixture.rsPriority().getUuid());
        Bo service = DAOService.create(serviceCase);
        DSLBo.add(agreement, service);
        DSLSlmService.addAgreements(service, agreement);
        DSLAgreement.addRecipients(ou, agreement);
        DSLAgreement.addServices(agreement, service);
        DSLSlmService.addScCases(service, scCase);

        Bo sc = DAOSc.createWithService(scCase, ou, agreement, service, SharedFixture.timeZone());
        sc.setExists(true);

        String accessKey = DSLApplication.createAccessKey(SharedFixture.employee().getLogin());
        CatalogItem categoryItem = SharedFixture.category();

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);
        //Создаем уровень эскалации
        EscalationLevel escLevel1 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "30 SECOND", false);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel1);
        EscalationLevel escLevel2 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "60 SECOND", false);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel2);

        String escSourceAttr = "categories";
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, escSourceAttr);
        DSLCatalogItem.add(rulesSettings);
        RsRow escRow = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                escSourceAttr, categoryItem.getUuid());
        DSLRsRows.addRowToRSItem(escRow);

        //Переходим по ссылке
        //обновление от нестабильных тестов
        String link = Config.get().getWebAddress()
                      + "services/rest/create/%s/{agreement:%s,service:%s,client:%s,description:'%s',categories:'%s', "
                      + "timeZone=%s}?accessKey=%s";

        tester.refresh();
        String randomString = RandomStringUtils.random(5);
        tester.goToPage(String.format(link, scCase.getFqn(), agreement.getUuid(), service.getUuid(), ou.getUuid(),
                randomString, categoryItem.getCode(), SharedFixture.timeZone().getUuid(), accessKey));

        ModelMap object = SdDataUtils.getFirstObjectByCase(sc, "description", randomString);
        Assert.assertNotNull("Объект не создан.", object);
        sc.setUuid(object.get("UUID"));
        tester.getBrowserTS().closeWebDriver();

        //Действия
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        //Проверка
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_ESCALATION_CHANGED);
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_ESCALATION_TIME);
    }

    /**
     * Тестирование отсутствия срабатывания уровня эскалации в случае, если его временная отметка осталась в прошлом,
     * и при этом не проставлен признак "Выполнять действие при изменении схемы".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41813384
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отделов ouCase</li>
     * <li>Создать тип запроса scCase1 и scCase2</li>
     * <li>Создать тип соглашения agreementCase</li>
     * <li>Создать элементы справочника Часовые пояса: timezone; Приоритет: priority</li>
     * <li>Создать таблицу соответствий rulesSettings для класса Запрос(Определяющий атрибут: Часовой пояс,
     * Определяемые: Приоритет и Нормативное время)</li>
     * <li>Добавить в ТС строку: ЧП=timezone -> Нормативное время= 20 секунд, Приоритет=priority</li> 
     * <li>Создать контент "История событий" historyContent для типа запросов scCase2 на карточке</li>
     * <li>Создать действие для эскалации escEvent: объекты scCase2, действие - скрипт: utils.event(subject,
     * '!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')</li>
     * <li>Создать схему эскалации escalationScheme: Объекты: scCase2, Счетчик времени: Запас нормативного времени
     * обслуживания</li>
     * <li>Добавить 1 уровень эскалации escLevel: Условие: По истечении доли времени (%), Значение: 50, Действие:
     * escEvent, Выполнять действие при изменении схемы: нет</li>
     * <li>Настроим таблицу соответствий эскалаций: определяющий атрибут "Статус" со значением "Зарегистрирован"</li>
     * <li>Добавить в ТС эскалации строку: Схема эскалации=escalationScheme, Тип объекта=scCase2</li>
     * <li>Создаем отдел ou, соглашение agreement, связываем их</li>
     * <li>Создаём запрос sc типа scCase1</li>
     * <b>Выполнение действия</b>
     * <li>Ждём 20 секунд, для того чтобы статус атрибута Запас нормативного времени обслуживания стал "Кончился
     * запас времени"</li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Меняем тип запроса sc на scCase2</li>
     * <li>Ждём 20 секунд (для уверенности в отстутствии срабатываний действий по эскалации)</li>
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" historyContent присутствуют записи об изменении схемы
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent отсутствуют записи об изменении уровней
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent отсутствуют записи о срабатывании уровня
     * эскалации</li>
     * </ol>
     */
    @Test
    public void testEscalationActionOnTimerExceedIfActionSwitchedOff()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase1, scCase2, agreementCase);

        //Создаём таблицу соответствий
        List<MetaClass> metaclasses = Lists.newArrayList(scCase1, scCase2);
        List<String> targetAttrs = Lists.newArrayList("priority", "resolutionTime");
        List<String> sourceAttrs = Lists.newArrayList("timeZone");
        CatalogItem rulesSettings = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        CatalogItem serviceItem = DAOCatalogItem.createServiceTime();
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Eirunepe");
        CatalogItem priority = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(rulesSettings, serviceItem, priority);
        DSLCatalogItem.add(timezone);

        RsRow row = DAORsRow.createManyTargets(rulesSettings, sourceAttrs.get(0), timezone.getUuid(),
                targetAttrs.get(0), priority.getUuid(), targetAttrs.get(1), "20 SECOND");
        DSLRsRows.addRowToRSItem(row);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase2.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем действие для эскалации
        String scriptBody = "utils.event(subject,'!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase2);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase2);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "50", false,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //Создаем таблицу соответствий эскалации
        String sourceAttr = "state";
        CatalogItem escRulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scCase2, sourceAttr);
        DSLCatalogItem.add(escRulesSettings);

        //Добавляем строки в таблицу соответсвий эскалации
        RsRow escRow = DAORsRow.create(escRulesSettings, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), sourceAttr, "registered");
        DSLRsRows.addRowToRSItem(escRow);

        //Создаем объекты
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceItem, serviceItem, rulesSettings,
                rulesSettings);
        DSLBo.add(agreement);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        Bo sc = DAOSc.create(scCase1, ou, agreement, timezone);
        DSLBo.add(sc);

        //Действия        
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        // Первый уровень эскалации должен сработать через 10 секунд, для надежности ждем 20
        WaitTool.waitMills(20000);
        GUIBo.changeCase(scCase2);

        // Ждем еще 20 секунд, чтобы убериться что за это время не сработало действие по эскалации
        WaitTool.waitMills(20000);

        // Проверка
        String changeSchemeMsg = String.format("Изменен набор схем эскалации: [%s]", escalationScheme.getTitle());

        GUIEventList.assertEventCategoryIsAbsence(historyContent, Other.userEvent.getTitle());
        GUIEventList.assertEventCategoryIsAbsence(historyContent, EC_ESCALATION_TIME);
        GUIEventList.assertEventsCount(historyContent, 2, EC_ESCALATION_CHANGED, changeSchemeMsg);
    }

    /**
     * Тестирование срабатывания уровня эскалации в случае, если его временная отметка осталась в прошлом,
     * и при этом проставлен признак "Выполнять действие при изменении схемы".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41813384
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отделов ouCase</li>
     * <li>Создать тип запроса scCase1 и scCase2</li>
     * <li>Создать тип соглашения agreementCase</li>
     * <li>Создать элементы справочника Часовые пояса: timezone; Приоритет: priority</li>
     * <li>Создать таблицу соответствий rulesSettings для класса Запрос(Определяющий атрибут: Часовой пояс,
     * Определяемые: Приоритет и Нормативное время)</li>
     * <li>Добавить в ТС строку: ЧП=timezone -> Нормативное время= 20 секунд, Приоритет=priority</li> 
     * <li>Создать контент "История событий" historyContent для типа запросов scCase2 на карточке</li>
     * <li>Создать действие для эскалации escEvent: объекты scCase2, действие - скрипт: utils.event(subject,
     * '!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')</li>
     * <li>Создать схему эскалации escalationScheme: Объекты: scCase2, Счетчик времени: Запас нормативного времени
     * обслуживания с возможностью перезапуска при изменении значения промежутка времени</li>
     * <li>Добавить 1 уровень эскалации escLevel: Условие: По истечении доли времени (%), Значение: 50, Действие:
     * escEvent, Выполнять действие при изменении схемы: да</li>
     * <li>Настроим таблицу соответствий эскалаций: определяющий атрибут "Статус" со значением "Зарегистрирован"</li>
     * <li>Добавить в ТС эскалации строку: Схема эскалации=escalationScheme, Тип объекта=scCase2</li>
     * <li>Создаем отдел ou, соглашение agreement, связываем их</li>
     * <li>Создаём запрос sc типа scCase1</li>
     * <b>Выполнение действия</b>
     * <li>Ждём 20 секунд, для того чтобы статус атрибута Запас нормативного времени обслуживания стал "Кончился
     * запас времени"</li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Меняем тип запроса sc на scCase2</li>    
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" historyContent присутствуют 2 записи об изменении уровня
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись о срабатывании уровня
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись маркера из скрипта действия
     * для эскалации</li>
     * </ol>
     */
    @Test
    public void testEscalationActionOnTimerExceedIfActionSwitchedOn()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase1, scCase2, agreementCase);

        //Создаём таблицу соответствий
        List<MetaClass> metaclasses = Lists.newArrayList(scCase1, scCase2);
        List<String> targetAttrs = Lists.newArrayList("priority", "resolutionTime");
        List<String> sourceAttrs = Lists.newArrayList("timeZone");
        CatalogItem rulesSettings = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        CatalogItem serviceItem = DAOCatalogItem.createServiceTime();
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Eirunepe");
        CatalogItem priority = DAOCatalogItem.createPriority(1);
        DSLCatalogItem.add(rulesSettings, serviceItem, priority);
        DSLCatalogItem.add(timezone);

        RsRow row = DAORsRow.createManyTargets(rulesSettings, sourceAttrs.get(0), timezone.getUuid(),
                targetAttrs.get(0), priority.getUuid(), targetAttrs.get(1), "20 SECOND");
        DSLRsRows.addRowToRSItem(row);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase2.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем действие для эскалации
        String scriptBody = "utils.event(subject,'!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase2);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        timeAllowanceTimer.setEnableRecalc(true);
        DSLTimerDefinition.edit(timeAllowanceTimer);
        Cleaner.afterTest(true, () ->
        {
            timeAllowanceTimer.setEnableRecalc(false);
            DSLTimerDefinition.edit(timeAllowanceTimer);
        });
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase2);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "50", true,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //Создаем таблицу соответствий эскалации
        String sourceAttr = "state";
        CatalogItem escRulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scCase2, sourceAttr);
        DSLCatalogItem.add(escRulesSettings);

        //Добавляем строки в таблицу соответсвий эскалации
        RsRow escRow = DAORsRow.create(escRulesSettings, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), sourceAttr, "registered");
        DSLRsRows.addRowToRSItem(escRow);

        //Создаем объекты
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceItem, serviceItem, rulesSettings,
                rulesSettings);
        DSLBo.add(agreement);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        Bo sc = DAOSc.create(scCase1, ou, agreement, timezone);
        DSLBo.add(sc);

        //Действия        
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        // Первый уровень эскалации должен сработать через 10 секунд, для надежности ждем 20
        WaitTool.waitMills(20000);
        GUIBo.changeCase(scCase2);

        // Проверка        
        String escMsg1 = String.format(ED_ESCALATION_LEVEL, 1, escalationScheme.getTitle(), escEvent.getTitle());
        String escMsg2 = "!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!";
        String recalcEscMsg = String.format(CHANGE_ESCALATION_TIME_PATTERN, escalationScheme.getTitle());

        //Прроверка
        GUIEventList.assertEventsCount(historyContent, 2, EC_ESCALATION_TIME, recalcEscMsg);
        GUIEventList.assertEventsCount(historyContent, 1, "plannedEvent", escMsg1);
        GUIEventList.assertEventsCount(historyContent, 1, Other.userEvent.getTitle(), escMsg2);
    }

    /**
     * Тестирование срабатывания уровня эскалации в случае, если его временная отметка осталась в прошлом,
     * и при этом проставлен признак "Выполнять действие при изменении схемы".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     *https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41834089
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отделов ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип соглашения agreementCase</li>
     * <li>Создать элементы справочника Часовые пояса: timezone1, timezone2; Приоритеты: priority1, priority2</li>
     * <li>Создать таблицу соответствий rulesSettings для класса Запрос(Определяющий атрибут: Часовой пояс,
     * Определяемые: Приоритет и Нормативное время)</li>
     * <li>Добавить в ТС строку: ЧП=timezone1 -> Нормативное время=1 час, Приоритет=priority2</li>
     * <li>Добавить в ТС строку: ЧП=timezone2 -> Нормативное время=1 секунда, Приоритет=priority1</li>
     * <li>Создать контент "История событий" historyContent для типа запросов scCase на карточке</li>
     * <li>Создать действие для эскалации escEvent: объекты scCase, действие - скрипт: utils.event(subject,
     * '!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')</li>
     * <li>Создать схему эскалации escalationScheme: Объекты: scCase, Счетчик времени: Запас нормативного времени
     * обслуживания с возможностью перезапуска при изменении значения промежутка времени</li>
     * <li>Добавить 1 уровень эскалации escLevel1: Условие: По истечении доли времени (%), Значение: 100, Действие:
     * escEvent, Выполнять действие при изменении схемы: да</li>
     * <li>Настроим таблицу соответствий эскалаций: определяющий атрибут "Тип объекта" со значением по умолчанию</li>
     * <li>Добавить в ТС эскалации строку: Схема эскалации=escalationScheme, Тип объекта=scCase</li>
     * <li>Создаем отдел ou, соглашение agreement, связываем их</li>
     * <li>Создаём запрос sc c ЧП=timezone1</li>
     * <b>Выполнение действия</b>
     * <li>Ждём 31 секунду, для того чтобы отрезок времени между новым РеглВрЗакр и моментом изменения критичности
     * был больше 30 секунд</li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку редактирования запроса sc</li>
     * <li>Меняем ЧП на timezone2, сохраняем изменения</li>
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" historyContent присутствуют 2 записи об изменении уровня
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись о срабатывании уровня
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись маркера из скрипта действия
     * для эскалации</li>
     * </ol>
     */
    @Test
    public void testEscalationActionOnTimerExceedOnActionExecuted()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(scCase, agreementCase);

        //Создаём таблицу соответствий
        List<MetaClass> metaclasses = Lists.newArrayList(scCase);
        List<String> targetAttrs = Lists.newArrayList("priority", "resolutionTime");
        List<String> sourceAttrs = Lists.newArrayList("timeZone");
        CatalogItem rulesSettings = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        CatalogItem serviceItem = DAOCatalogItem.createServiceTime();
        CatalogItem timezone1 = DAOCatalogItem.createTimeZone("America/Eirunepe");
        CatalogItem timezone2 = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        CatalogItem priority1 = DAOCatalogItem.createPriority(1);
        CatalogItem priority2 = DAOCatalogItem.createPriority(2);
        DSLCatalogItem.add(rulesSettings, serviceItem, priority1, priority2);
        DSLCatalogItem.add(timezone1);
        DSLCatalogItem.add(timezone2);

        RsRow row1 = DAORsRow.createManyTargets(rulesSettings, sourceAttrs.get(0), timezone1.getUuid(),
                targetAttrs.get(0), priority2.getUuid(), targetAttrs.get(1), "1 HOUR");
        RsRow row2 = DAORsRow.createManyTargets(rulesSettings, sourceAttrs.get(0), timezone2.getUuid(),
                targetAttrs.get(0), priority1.getUuid(), targetAttrs.get(1), "1 SECOND");
        DSLRsRows.addRowToRSItem(row1, row2);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем действие для эскалации
        String scriptBody = "utils.event(subject,'!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!')";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        timeAllowanceTimer.setEnableRecalc(true);
        DSLTimerDefinition.edit(timeAllowanceTimer);
        Cleaner.afterTest(true, () ->
        {
            timeAllowanceTimer.setEnableRecalc(false);
            DSLTimerDefinition.edit(timeAllowanceTimer);
        });
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel1 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "100", true,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel1);

        //Создаем таблицу соответствий эскалации
        String sourceAttr = "metaClass";
        CatalogItem escRulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scCase, sourceAttr);
        DSLCatalogItem.add(escRulesSettings);

        //Добавляем строки в таблицу соответсвий эскалации
        RsRow escRow = DAORsRow.create(escRulesSettings, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), sourceAttr, scCase.getFqn());
        DSLRsRows.addRowToRSItem(escRow);

        //Создаем объекты
        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceItem, serviceItem, rulesSettings,
                rulesSettings);
        DSLBo.add(agreement);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        Bo sc = DAOSc.create(scCase, ou, agreement, timezone1);
        DSLBo.add(sc);

        //Действия
        //Ждать 31 секунду, для того чтобы отрезок времени между новым РеглВрЗакр и моментом изменения критичности
        // был больше 30 секунд
        WaitTool.waitMills(31001);
        DSLBo.editAttributeValue(sc, SysAttribute.timeZone(scCase).setValue(timezone2.getUuid()));

        //Проверка
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        String escMsg1 = String.format(ED_ESCALATION_LEVEL, 1, escalationScheme.getTitle(), escEvent.getTitle());
        String recalcEscMsg = String.format(CHANGE_ESCALATION_TIME_PATTERN, escalationScheme.getTitle());
        GUIEventList.assertEventsCount(historyContent, 2, EC_ESCALATION_TIME, recalcEscMsg);
        GUIEventList.assertEventsCount(historyContent, 1, "plannedEvent", escMsg1);
        GUIEventList.assertEventsCount(historyContent, 1, Other.userEvent.getTitle(),
                "!!!!!!!!!!!!!!Эскалация!!!!!!!!!!!");
    }

    /**
     * Тестирование планирования выключенных действий по эскалации
     * (они не должны попадать в таблицу tbl_sys_planned_event)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00450
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00452
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase добавить атрибут timeInt типа "Временной интервал", по умолчанию - 30 секунд</li>
     * <li>В scCase добавить статусы registered, closed и переход между ними</li>
     * <li>Создать счетчик chet (scCase, временная зона timeZone, Запас времени обслуживания, Промежуток времени:
     * timeInt,
     *  Тип условия: По смене статуса, registered)</li>
     * <li>В scCase создать атрибут obrChet типа Счетчик времени (обратный) (счетчик chet)</li>
     * <li>Настроить все для создания запроса</li>
     * <li>Создать элемент справочника "Часовые пояса" timezone</li> 
     * <li>Создать действие для эскалации escEvent (scCase, скрипт: logger.error("!!!!работает эскалация"),
     * выключено)</li>
     * <li>Создать схему эскалации testovayaShema (chet, включена, Объекты - scCase)</li>
     * <li>Создать уровень эскалации escLevel: escalationScheme, По истечении доли времени (%), 100, escEvent,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, Определяющие атрибуты: Тип объекта</li>
     * <li>В добавленной таблице соответствий добавить строку row: testovayaShema, Тип объекта: [Любой]</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase (временная зона timezone)</li>
     * <li>Сменить статус запроса на registered</li> 
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что таблица tbl_sys_planned_event в БД пуста</li>
     * </ol>
     */
    @Test
    public void testEscSysPlannedEvent()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);
        //Создаем атрибут типа "Временной интервал"
        Attribute timeInt = DAOAttribute.createTimeInterval(scCase.getFqn());
        timeInt.setDefaultValue("30 SECOND");
        DSLAttribute.add(timeInt);
        //Создаем статусы и переход между ними
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        DSLBoStatus.setTransitions(registered, closed);
        //Создаем счетчик
        TimerDefinition chet = DAOTimerDefinition.createFloatTimerByStatus(scCase.getFqn(), "timeZone",
                "serviceTime", timeInt.getCode(), registered);
        DSLTimerDefinition.add(chet);
        //Создаем атрибут obrChet
        Attribute obrChet = DAOAttribute.createBackTimer(scCase.getFqn(), chet, BackTimerType.BACKTIMER_DEADLINE_VIEW);
        DSLAttribute.add(obrChet);
        //Настраиваем все для создания запроса
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);
        //Создаем элемент справочника "Часовые пояса" 
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);
        //Создаем действие для эскалации
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("logger.error(\"!!!!работает эскалация\")");
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), false, scCase);
        DSLEventAction.add(escEvent);
        //Создаем схему эскалации
        EscalationScheme testovayaShema = DAOEscalationSheme.create(chet, true, scCase);
        DSLEscalation.add(testovayaShema);
        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "100", false,
                escEvent);
        DSLEscalation.addLevel(testovayaShema.getCode(), escLevel);
        //Создаем таблицу соответствий
        String metaClassAttrCode = SysAttribute.metaClass(scCase).getCode();
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, metaClassAttrCode);
        DSLCatalogItem.add(rulesSettings);
        //Добавляем строку
        RsRow row = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, testovayaShema.getCode(),
                SysAttribute.metaClass(scCase).getCode(), "anyOptionItem");
        DSLRsRows.addRowToRSItem(row);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, ou, agreement, timezone);
        DSLBo.add(sc);
        DSLSc.changeState(sc, registered);

        //Проверки
        String queryScript = String.format(
                "api.db.query(\"SELECT COUNT(1) FROM PlannedEvent WHERE escalationCode = '%s'\")."
                + "list()", testovayaShema.getCode());
        List<String> runResult = new ScriptRunner(queryScript).runScript();
        assertEquals("Таблица tbl_sys_planned_event не пуста.", "[0]", runResult.get(0));
    }

    /**
     * Тестирование того что при переводе объекта в статус, в котором счетчик времени приостанавливается, или
     * становится активным
     * схемы эскалации не переподбираются и выполненное действие эскалации не выполняется вновь.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00307
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41993962
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass (с ЖЦ)</li>
     * <li>Создать тип пользовательского класса userCase</li>
     * <li>В классе userClass добавить статус statusForTimerPaused</li>
     * <li>Настроить переходы registered -> status -> registered</li>
     * <li>В userClass добавить атрибут serviceTime (типа "Элемент справочника", Классы обслуживания, по умолчанию -
     * serviceTime)</li>
     * <li>В userClass добавить атрибут timeZone (типа "Элемент справочника", Часовые пояса, по умолчанию - timeZone)
     * </li>
     * <li>В userClass добавить атрибут resolutionTime (типа "Временной интервал", по умолчанию - 40 минут)</li>
     * <li>Создать счетчик времени counter (класс: userClass, Метрика: Запас времени обслуживания, Тип условия: По
     * смене статуса,
     * Учитывать: registered)</li>
     * <li>В userClass добавить атрибут backTimer (типа "Счетчик времени (обратный)", счетчик counter</li>
     * <li>На карточку userClass добавляем контент eventList типа "История изменений"</li>
     * <li>Добавить action, действие по событию типа Эскалация, действие - скрипт, тип объекта - userClass</li>
     * <li>Создать схему эскалации escScheme (Класс: userClass, Счетчик: counter)</li>
     * <li>В схему эскалации добавить уровень эскалации escLevel (Условие: по истечению времени 2 секунды, Действие:
     * action)</li>
     * <li>Добавить таблицу соответствий эскалации escScheme (Схема по умолчанию: escScheme, Определяющий атрибут:
     * тип объекта)</li>
     * <li>Создать объект bo типа userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку объекта bo</li>
     * <li>Переводим объект в статус statusForTimerPaused</li>
     * <li>Переводим объект в статус registered</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" eventList присутствует единственная запись об изменении схем
     * эскалации</li>
     * <li>Проверяем что в контенте "История событий" eventList присутствует единственная запись о выполнении
     * действия Уровня 1 эскалации</li>
     * <li>Проверяем что в контенте "История событий" eventList присутствует единственная запись об изменении времени
     * для уровней эскалации</li>
     * </ol>
     */
    @Test
    public void testNotReselectEscalationSchemeOnTimerPausedOrActivate()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        BoStatus statusForTimerPaused = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(statusForTimerPaused);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        DSLBoStatus.setTransitions(registered, statusForTimerPaused, registered);

        Catalog seviceTimeCatalog = DAOCatalog.createSystem(SystemCatalog.SERVICETIME);
        Attribute serviceTime = DAOAttribute.createCatalogItem(userClass.getFqn(), seviceTimeCatalog,
                SharedFixture.serviceTime());
        DSLAttribute.add(serviceTime);

        Catalog timeZones = DAOCatalog.createSystem(SystemCatalog.TIMEZONE);
        Attribute timeZone = DAOAttribute.createCatalogItem(userClass.getFqn(), timeZones, SharedFixture.timeZone());
        DSLAttribute.add(timeZone);

        Attribute resolutionTime = DAOAttribute.createTimeInterval(userClass.getFqn());
        resolutionTime.setDefaultValue("40 MINUTE");
        DSLAttribute.add(resolutionTime);

        TimerDefinition counter = DAOTimerDefinition.createFloatTimerByStatus(userClass.getFqn(), timeZone.getCode(),
                serviceTime.getCode(), resolutionTime.getCode(), registered);
        DSLTimerDefinition.add(counter);

        Attribute backTimer = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_DEADLINE_VIEW);
        DSLAttribute.add(backTimer);

        ContentForm eventList = DAOContentCard.createEventList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction action = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, userClass);
        DSLEventAction.add(action);

        EscalationScheme escScheme = DAOEscalationSheme.create(counter, true, userClass);
        DSLEscalation.add(escScheme);

        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "2 SECOND", true,
                action);
        DSLEscalation.addLevel(escScheme.getCode(), escLevel);

        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escScheme, userClass,
                SysAttribute.metaClass(userClass).getCode());
        DSLCatalogItem.add(rulesSettings);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        GUISc.openChangeStateFormAndSetState(statusForTimerPaused.getCode());
        GUIForm.applyModalForm();
        GUISc.openChangeStateFormAndSetState(registered.getCode());
        GUIForm.applyModalForm();

        String eventMessage = "Изменен набор схем эскалации";
        String escMsg1 = String.format(ED_ESCALATION_LEVEL, 1, escScheme.getTitle(), action.getTitle());
        String recalcEscMsg = String.format(CHANGE_ESCALATION_TIME_PATTERN, escScheme.getTitle());

        //Прроверка
        GUIEventList.assertEventsCount(eventList, 1, bo.getUuid(), EC_ESCALATION_CHANGED, eventMessage);
        GUIEventList.assertEventsCount(eventList, 1, "plannedEvent", escMsg1);
        GUIEventList.assertEventsCount(eventList, 1, EC_ESCALATION_TIME, recalcEscMsg);
    }

    /**
     * Тестирование того что при переводе объекта в статус, в котором счетчик времени приостанавливается,
     * схема эскалации не обнуляется.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42015576
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass (с ЖЦ)</li>
     * <li>Создать тип пользовательского класса userCase</li>
     * <li>В классе userClass добавить статус status</li>
     * <li>Настроить переходы registered -> status -> closed</li>
     * <li>В userClass добавить атрибут serviceTime (типа "Элемент справочника", Классы обслуживания, по умолчанию -
     * serviceTime)</li>
     * <li>В userClass добавить атрибут timeZone (типа "Элемент справочника", Часовые пояса, по умолчанию - timeZone)
     * </li>
     * <li>В userClass добавить атрибут resolutionTime (типа "Временной интервал", по умолчанию - 40 минут)</li>
     * <li>Создать счетчик времени counter (класс: userClass, Метрика: Запас времени обслуживания, Тип условия: По
     * смене статуса,
     * Учитывать: registered)</li>
     * <li>В userClass добавить атрибут backTimer (типа "Счетчик времени (обратный)", счетчик counter</li>
     * <li>На карточку userClass добавляем контент eventList типа "История изменений"</li>
     * <li>Добавить action, действие по событию типа Эскалация, действие - скрипт, тип объекта - userClass</li>
     * <li>Создать схему эскалации escScheme (Класс: userClass, Счетчик: counter)</li>
     * <li>В схему эскалации добавить уровень эскалации escLevel (Условие: по истечению 50% времени, Действие:
     * action)</li>
     * <li>Добавить таблицу соответствий эскалации escScheme (Схема по умолчанию: escScheme, Определяющий атрибут:
     * тип объекта)</li>
     * <li>Создать объект bo типа userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку объекта bo</li>
     * <li>Переводим объект в статус status</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" eventList отсутствует запись о том что набор схем эскалации
     * стал пустым</li>
     * </ol>
     */
    @Test
    public void testNotResetingEscalationSchemeOnTimerStop()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(status);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(userClass.getFqn());
        DSLBoStatus.setTransitions(registered, status, closed);

        Catalog seviceTimeCatalog = DAOCatalog.createSystem(SystemCatalog.SERVICETIME);
        Attribute serviceTime = DAOAttribute.createCatalogItem(userClass.getFqn(), seviceTimeCatalog,
                SharedFixture.serviceTime());
        DSLAttribute.add(serviceTime);

        Catalog timeZones = DAOCatalog.createSystem(SystemCatalog.TIMEZONE);
        Attribute timeZone = DAOAttribute.createCatalogItem(userClass.getFqn(), timeZones, SharedFixture.timeZone());
        DSLAttribute.add(timeZone);

        Attribute resolutionTime = DAOAttribute.createTimeInterval(userClass.getFqn());
        resolutionTime.setDefaultValue("40 MINUTE");
        DSLAttribute.add(resolutionTime);

        TimerDefinition counter = DAOTimerDefinition.createFloatTimerByStatus(userClass.getFqn(), timeZone.getCode(),
                serviceTime.getCode(), resolutionTime.getCode(), registered);
        DSLTimerDefinition.add(counter);

        Attribute backTimer = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_DEADLINE_VIEW);
        DSLAttribute.add(backTimer);

        ContentForm eventList = DAOContentCard.createEventList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction action = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, userClass);
        DSLEventAction.add(action);

        EscalationScheme escScheme = DAOEscalationSheme.create(counter, true, userClass);
        DSLEscalation.add(escScheme);

        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME_PART, "50", true,
                action);
        DSLEscalation.addLevel(escScheme.getCode(), escLevel);

        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escScheme, userClass,
                SysAttribute.metaClass(userClass).getCode());
        DSLCatalogItem.add(rulesSettings);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        GUISc.openChangeStateFormAndSetState(status.getCode());
        GUIForm.applyModalForm();

        String eventCategoryName = "Изменение схемы эскалации";
        String eventMessage = "Изменен набор схем эскалации: []";

        //Прроверка
        GUIEventList.assertEventAdvlistAbsence(eventList, bo.getUuid(), eventCategoryName, eventMessage);
    }

    /**
     * Тестирование того что при переводе объекта в статус, в котором счетчик времени приостанавливается, или
     * становится активным
     * схемы происходит пересчет времени выполнения действий уровней эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00307
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41993962
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass (с ЖЦ)</li>
     * <li>Создать тип пользовательского класса userCase</li>
     * <li>В классе userClass добавить статус statusForTimerPaused</li>
     * <li>Настроить переходы registered -> status -> registered</li>
     * <li>В userClass добавить атрибут serviceTime (типа "Элемент справочника", Классы обслуживания, по умолчанию -
     * serviceTime)</li>
     * <li>В userClass добавить атрибут timeZone (типа "Элемент справочника", Часовые пояса, по умолчанию - timeZone)
     * </li>
     * <li>В userClass добавить атрибут resolutionTime (типа "Временной интервал", по умолчанию - 40 минут)</li>
     * <li>Создать счетчик времени counter (класс: userClass, Метрика: Запас времени обслуживания, Тип условия: По
     * смене статуса,
     * Учитывать: registered)</li>
     * <li>В userClass добавить атрибут backTimer (типа "Счетчик времени (обратный)", счетчик counter</li>
     * <li>На карточку userClass добавляем контент eventList типа "История изменений"</li>
     * <li>Добавить action, действие по событию типа Эскалация, действие - скрипт, тип объекта - userClass</li>
     * <li>Создать схему эскалации escScheme (Класс: userClass, Счетчик: counter)</li>
     * <li>В схему эскалации добавить уровень эскалации escLevel (Условие: по истечению времени 20 минут, Действие:
     * action)</li>
     * <li>Добавить таблицу соответствий эскалации escScheme (Схема по умолчанию: escScheme, Определяющий атрибут:
     * тип объекта)</li>
     * <li>Создать объект bo типа userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку объекта bo</li>
     * <li>Переводим объект в статус statusForTimerPaused</li>
     * <li>Переводим объект в статус registered</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в контенте "История событий" eventList присутствует 3 записи об изменении времени для
     * уровней эскалации</li>
     * <li>Проверяем что в контенте "История событий" eventList присутствует 2 записи об изменении времени для уровня
     * 1 эскалации</li>
     * </ol>
     */
    @Test
    public void testRecalculateEscalationActionTimeOnTimerPausedOrActivate()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        BoStatus statusForTimerPaused = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(statusForTimerPaused);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        DSLBoStatus.setTransitions(registered, statusForTimerPaused, registered);

        Catalog seviceTimeCatalog = DAOCatalog.createSystem(SystemCatalog.SERVICETIME);
        Attribute serviceTime = DAOAttribute.createCatalogItem(userClass.getFqn(), seviceTimeCatalog,
                SharedFixture.serviceTime());
        DSLAttribute.add(serviceTime);

        Catalog timeZones = DAOCatalog.createSystem(SystemCatalog.TIMEZONE);
        Attribute timeZone = DAOAttribute.createCatalogItem(userClass.getFqn(), timeZones, SharedFixture.timeZone());
        DSLAttribute.add(timeZone);

        Attribute resolutionTime = DAOAttribute.createTimeInterval(userClass.getFqn());
        resolutionTime.setDefaultValue("40 MINUTE");
        DSLAttribute.add(resolutionTime);

        TimerDefinition counter = DAOTimerDefinition.createFloatTimerByStatus(userClass.getFqn(), timeZone.getCode(),
                serviceTime.getCode(), resolutionTime.getCode(), registered);
        DSLTimerDefinition.add(counter);

        Attribute backTimer = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_DEADLINE_VIEW);
        DSLAttribute.add(backTimer);

        ContentForm eventList = DAOContentCard.createEventList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction action = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, userClass);
        DSLEventAction.add(action);

        EscalationScheme escScheme = DAOEscalationSheme.create(counter, true, userClass);
        DSLEscalation.add(escScheme);

        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "20 MINUTE", true,
                action);
        DSLEscalation.addLevel(escScheme.getCode(), escLevel);

        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escScheme, userClass,
                SysAttribute.metaClass(userClass).getCode());
        DSLCatalogItem.add(rulesSettings);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        GUISc.openChangeStateFormAndSetState(statusForTimerPaused.getCode());
        GUIForm.applyModalForm();
        GUISc.openChangeStateFormAndSetState(registered.getCode());
        GUIForm.applyModalForm();

        String recalcEscMsg = String.format(CHANGE_ESCALATION_TIME_PATTERN, escScheme.getTitle());
        //Прроверка
        GUIEventList.assertEventsCount(eventList, 3, EC_ESCALATION_TIME, recalcEscMsg);
        GUIEventList.assertEventsCount(eventList, 2, EC_ESCALATION_TIME, recalcEscMsg, "Уровень 1:");
    }

    /**
     * Тестирование работы эскалации если в качестве значений определяющего атрибута в объекте выбрано несколько
     * значений
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку scCase добавить контент historyContent История изменений объекта</li>
     * <li>Создать в scCase атрибут boLinks типа Набор ссылок на бо</li>
     * <li>Создать отделы ou, ou2 типа ouCase</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, без значения по умолчанию, boLinks</li>
     * <li>Добавить строку row в rulesSettings: escalationScheme, ou, ou2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase, значение атрибута aggregate - ou, ou2</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись об изменении набора схем
     * эскалации</li>
     * </ol>
     */
    @Test
    public void testRulesSettingsSourceAttrTwoValue()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, employeeCase);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем элементы справочникаов
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou, ou2);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(ou, agreement);
        DSLAgreement.addRecipients(employee, agreement);

        Attribute boLinks = DAOAttribute.createBoLinks(scCase, ouCase, ou, ou2);
        DSLAttribute.add(boLinks);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем таблицу соответствий
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, boLinks.getCode());
        DSLCatalogItem.add(rulesSettings);

        //Добавляем строки
        RsRow escRow = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                boLinks.getCode(), ou.getUuid() + "," + ou2.getUuid());
        DSLRsRows.addRowToRSItem(escRow);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, employee, agreement, timezone);
        DAOBo.addAttributeToModel(sc, boLinks);
        DSLBo.add(sc);

        //Проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIEventList.assertEventAdvlistPresent(historyContent, escalationScheme.getTitle());
    }

    /**
     * Тестирование работы эскалации с определяющим атрибутом типа Агрегирующий атрибут
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>На карточку scCase добавить контент historyContent История изменений объекта</li>
     * <li>Создать в scCase атрибут aggregate типа Агрегирующий атрибут</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, без значения по умолчанию, aggregate</li>
     * <li>Добавить строку row в rulesSettings: escalationScheme, employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase, значение атрибута aggregate - employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись об изменении набора схем
     * эскалации</li>
     * </ol>
     */
    @Test
    public void testRulesSettingsWithAggregateSourceAttr()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, employeeCase);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем элементы справочникаов
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(ou, agreement);
        DSLAgreement.addRecipients(employee, agreement);

        Attribute aggregate = DAOAttribute.createAggregate(scCase, AggregatedClasses.OU, ou, employee);
        DSLAttribute.add(aggregate);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем таблицу соответствий
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, aggregate.getCode());
        DSLCatalogItem.add(rulesSettings);

        //Добавляем строки
        RsRow escRow = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                aggregate.getCode(), employee.getUuid() + "," + ou.getUuid());
        DSLRsRows.addRowToRSItem(escRow);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, employee, agreement, timezone);
        DAOBo.addAttributeToModel(sc, aggregate);
        DSLBo.add(sc);

        //Проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIEventList.assertEventAdvlistPresent(historyContent, escalationScheme.getTitle());
    }

    /**
     * Тестирование работы эскалации с определяющим атрибутом типа Агрегирующий атрибут
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку scCase добавить контент historyContent История изменений объекта</li>
     * <li>Создать в scCase атрибут aggregate типа Агрегирующий атрибут</li>
     * <li>Вывести на карточку scCase атрибут agregate</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, без значения по умолчанию, aggregate</li>
     * <li>Добавить строку row в rulesSettings: escalationScheme, employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что в контенте "История событий" historyContent отсутствует запись об изменении набора схем
     * эскалации</li>
     * <li>На карточке объекта изменяем значение атрибута aggregate</li>
     * <li>Проверяем что в контенте "История событий" historyContent присутствует запись об изменении набора схем
     * эскалации</li>
     * </ol>
     */
    @Test
    public void testRulesSettingsWithAggregateSourceAttr1()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, employeeCase);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем элементы справочникаов
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(ou, agreement);
        DSLAgreement.addRecipients(employee, agreement);

        Attribute aggregate = DAOAttribute.createAggregate(scCase, AggregatedClasses.OU, null, null);
        DSLAttribute.add(aggregate);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем таблицу соответствий
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, aggregate.getCode());
        DSLCatalogItem.add(rulesSettings);

        //Добавляем строки
        RsRow escRow = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                aggregate.getCode(), ou.getUuid());
        DSLRsRows.addRowToRSItem(escRow);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, employee, agreement, timezone);
        DSLBo.add(sc);

        GroupAttr groupAttr = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(groupAttr, aggregate);

        ContentForm content = DAOContentCard.createPropertyList(scCase, groupAttr);
        DSLContent.add(content);

        //Проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIEventList.assertEventAdvlistAbsence(historyContent, EC_ESCALATION_CHANGED,
                String.format(EventListMessages.ED_ESCALATION_CHANGED, "[]"));
        GUIContent.clickEdit(content);
        GUISelect.selectById(GUISelect.SELECT_INPUT, ou.getUuid(), aggregate.getCode());
        GUIForm.applyModalForm();
        GUIEventList.assertEventAdvlistPresent(historyContent, escalationScheme.getTitle());
    }

    /**
     * Тестирование работы эскалации с таблицей соответствий со значением по умолчанию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать элемент справочника Часовые пояса: timezone</li>
     * <li>Создать действие для эскалации escEvent1 (scCase, скрипт:utils.edit(subject, ['clientPhone':'new phone1'])
     * )</li>
     * <li>Создать действие для эскалации escEvent2 (scCase, скрипт:utils.edit(subject, ['clientPhone':'new phone2'])
     * )</li>
     * <li>Включить escEvent</li>
     * <li>Создать схемы эскалации escalationScheme1..2(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme1..2</li>
     * <li>Создать уровень эскалации escLevel1: escalationScheme1, По истечении времени, 1 секунда ,escEvent1,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать уровень эскалации escLevel2: escalationScheme2, По истечении времени, 1 секунда ,escEvent2,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, escalationScheme1 по умолчанию, Часовой пояс
     * запроса</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc1 типа scCase, часовой пояс - timezone</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что через 1 секунду значение атрибута Контактный телефон изменилось на 'new phone1'</li>
     * </ol>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В rulesSettings добавить строку row: timezone - escalationScheme2</li>
     * <li>Создать запрос sc2 типа scCase, часовой пояс - timezone</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что значение атрибута Контактный телефон изменилось на 'new phone2'</li>
     * </ol>
     */
    @Test
    public void testRulesSettingsWithDefaultValue()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);

        //Создаем элементы справочников
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        //Создаем действие для эскалации
        String scriptPattern = "utils.edit(subject, ['clientPhone':'%s'])";

        String clientPhoneValue1 = ModelUtils.createTitle();
        String scriptBody1 = String.format(scriptPattern, clientPhoneValue1);
        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo(scriptBody1);
        DSLScriptInfo.addScript(script1);
        EventAction escEvent1 = DAOEventAction.createEventScript(EventType.escalation, script1.getCode(), true, scCase);

        String clientPhoneValue2 = ModelUtils.createTitle();
        String scriptBody2 = String.format(scriptPattern, clientPhoneValue2);
        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo(scriptBody2);
        DSLScriptInfo.addScript(script2);
        EventAction escEvent2 = DAOEventAction.createEventScript(EventType.escalation, script2.getCode(), true, scCase);

        DSLEventAction.add(escEvent1, escEvent2);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme1 = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        EscalationScheme escalationScheme2 = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme1, escalationScheme2);

        //Создаем уровень эскалации
        String period = "1 SECOND";
        EscalationLevel escLevel1 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, period, false,
                escEvent1);
        DSLEscalation.addLevel(escalationScheme1.getCode(), escLevel1);
        EscalationLevel escLevel2 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, period, false,
                escEvent2);
        DSLEscalation.addLevel(escalationScheme2.getCode(), escLevel2);

        //Создаем таблицу соответствий
        String escSourceAttr = "timeZone";
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme1, scCase, escSourceAttr);
        DSLCatalogItem.add(rulesSettings);

        //Выполнение действия
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timezone);
        DSLBo.add(sc1);

        //Проверки
        Attribute clientPhoneAttr = SysAttribute.clientPhone(scCase);
        clientPhoneAttr.setValue(clientPhoneValue1);
        DSLBo.assertStringAttr(sc1, clientPhoneAttr);

        //Выполнение действия

        //Добавляем строки
        RsRow row = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme2.getCode(),
                escSourceAttr, timezone.getUuid());
        DSLRsRows.addRowToRSItem(row);

        Bo sc2 = DAOSc.create(scCase, ou, agreement, timezone);
        DSLBo.add(sc2);

        //Проверки
        clientPhoneAttr.setValue(clientPhoneValue2);
        DSLBo.assertStringAttr(sc2, clientPhoneAttr);
    }

    /**
     * Тестирования одновременного выполнения 2 двух действий по эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00449
     * http://sd-jira.naumen.ru/browse/NSDPRD-732
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase создать статус status, настроить переходы: Зарегистрирован --> status --> Зарегистрирован</li>
     * <li>В scCase добавить контент historyContent История изменений объекта</li>
     * <li>Создать счетчик backTimer (Запас времени обслуживания, scCase, по смене статуса: status)</li>
     * <li>В scCase создать 2 атрибута attr1, attr2 типа счетчик времени(счетчик backTimer)</li>
     * <li>Создать действие для эскалации escEvent1 (scCase, скрипт:
     * utils.edit(subject, ['clientPhone':'new phone']))</li>
     * <li>Создать действие для эскалации escEvent2 (scCase, скрипт:
     * utils.edit(subject, ['clientName':'client name']))</li>
     * <li>Создать схему эскалации escalationScheme (backTimer, Объекты - scCase)</li>
     * <li>Создать уровень эскалации escLevel1: escalationScheme, По истечении времени, 1 секунда ,escEvent1,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать уровень эскалации escLevel2: escalationScheme, По истечении времени, 1 секунда ,escEvent2,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, без значения по умолчанию, Часовой пояс
     * запроса</li>
     * <li>Добавить строку escRow в rulesSettings: escalationScheme, timezone</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что через 1 секунду значение атрибутов Контактный телефон и Контактное лицо не изменилось</li>
     * <li>Изменить статус запроса sc на status</li>
     * <li>Проверить, что значение атрибута Контактный телефон изменилось на 'new phone',
     * значение атрибута Контактное лицо изменилось на 'client name'</li>
     * <li>Проверить, что в контенте historyContent появились 2 записи о 2-х действиях: escEvent1, escEvent2</li>
     * </ol>
     */
    @Test
    public void testRunEscActionTogether()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);
        //Создаем статус
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn(), ResponsibleType.EMPLOYEE_AND_TEAM,
                Strategy.CURRENT);
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(registered, status, registered);
        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(historyContent);
        //Создаем счетчик
        TimerDefinition backTimer = DAOTimerDefinition.createFloatTimerByStatus(scCase.getFqn(), "timeZone",
                "serviceTime", "resolutionTime", status);
        DSLTimerDefinition.add(backTimer);
        //Создаем атрибуты
        Attribute attr1 = DAOAttribute.createBackTimer(scCase.getFqn(), backTimer, TimerType.EDIT);
        Attribute attr2 = DAOAttribute.createBackTimer(scCase.getFqn(), backTimer, TimerType.EDIT);
        DSLAttribute.add(attr1, attr2);
        //Создаем элемент справочника
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);
        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);
        //Создаем действие для эскалации
        String clientPhone = ModelUtils.createTitle();
        String scriptBody1 = String.format("utils.edit(subject, ['clientPhone':'%s'])", clientPhone);
        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo(scriptBody1);
        DSLScriptInfo.addScript(script1);
        EventAction escEvent1 = DAOEventAction.createEventScript(EventType.escalation, script1.getCode(), true, scCase);
        String clientName = ModelUtils.createTitle();
        String scriptBody2 = String.format("utils.edit(subject, ['clientName':'%s'])", clientName);
        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo(scriptBody2);
        DSLScriptInfo.addScript(script2);
        EventAction escEvent2 = DAOEventAction.createEventScript(EventType.escalation, script2.getCode(), true, scCase);
        DSLEventAction.add(escEvent1, escEvent2);
        //Создаем схему эскалации
        EscalationScheme escalationScheme = DAOEscalationSheme.create(backTimer, true, scCase);
        DSLEscalation.add(escalationScheme);
        //Создаем уровень эскалации
        EscalationLevel escLevel1 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "1 SECOND", false,
                escEvent1);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel1);
        EscalationLevel escLevel2 = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "1 SECOND", false,
                escEvent2);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel2);
        //Создаем таблицу соответствий
        String sourceAttr = "timeZone";
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, sourceAttr);
        DSLCatalogItem.add(rulesSettings);
        //Добавляем строку
        RsRow escRow = DAORsRow.create(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                sourceAttr, timezone.getUuid());
        DSLRsRows.addRowToRSItem(escRow);
        //Выполнение действия
        Bo sc = DAOSc.create(scCase, ou, agreement, timezone);
        DSLBo.add(sc);

        //Проверки
        DSLSc.changeState(sc, status);
        WaitTool.waitMills(1000);
        String message = "Полученное значение атрибута '%s' не совпало с ожидаемым";

        String clientPhoneCode = "clientPhone";
        ModelMap object = SdDataUtils.getObjectByUUID(sc, clientPhoneCode, clientPhone);
        String actual = object.get(clientPhoneCode);
        assertEquals(String.format(message, "Контактный телефон"), clientPhone, actual);

        String clientNameCode = "clientName";
        object = SdDataUtils.getObjectByUUID(sc, clientNameCode, clientName);
        actual = object.get(clientNameCode);
        assertEquals(String.format(message, "Контактное лицо"), clientName, actual);

        //Проверки для истории событий
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        String message1 = String.format(ED_ESCALATION_LEVEL, 1, escalationScheme.getTitle(), escEvent1.getTitle());
        GUIEventList.assertEventPresent(historyContent, sc.getUuid(), message1, "Эскалация");

        String message2 = String.format(ED_ESCALATION_LEVEL, 2, escalationScheme.getTitle(), escEvent2.getTitle());
        GUIEventList.assertEventPresent(historyContent, sc.getUuid(), message2, "Эскалация");
    }

    /**
     * Проверка сохранения текущей авторизации после переподбора схемы эскалации
     * при создании нового объекта через действие по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00310
     * http://sd-jira.naumen.ru/browse/NSDPRD-4953
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать действие для эскалации escEvent (scCase, скрипт: пусто)</li>
     * <li>Включить действие для эскалации escEvent</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить схему эскалации escalationScheme</li>
     * <li>Создать уровень эскалации escLevel: escalationScheme, По истечении времени, 1 секунда, escEvent,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, escalationScheme по умолчанию, Статус
     * запроса</li>
     * <li>Создаем действие по событию eventAction для объекта класса Отдел, событие - создание объекта, действие -
     * скрипт, скрипт:
     * <pre>
     *     utils.create('scCase.FQN', ['description' : 'description', 'agreement' : 'agreement.UUID',
     *      'timeZone' : 'timeZone.UUID', 'clientOU' : 'clientOu.UUID']);
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в систему под тестером</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Проверить, что создался новый запрос sc</li>
     * <li>Перейти на карточку запроса sc (для большей вероятности дождаться окончания работы эскалации)</li>
     * <li>Проверить, что после обновления страницы пользователь остался тестером</li>
     * </ol>
     */
    @Test
    public void testSaveOriginalUserAfterEscalationtRecalcByEventAction()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        //Создаем действие для эскалации
        ScriptInfo escEventScript = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(escEventScript);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, escEventScript.getCode(), true,
                scCase);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "1 SECOND", false,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //Создаем таблицу соответствий
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scCase, "state");
        DSLCatalogItem.add(rulesSettings);

        //Добавляем объекты для создания запроса
        Bo client = SharedFixture.clientOu();
        Bo agreement = SharedFixture.clientAgreement();
        CatalogItem timeZone = SharedFixture.timeZone();

        //Создаем действие по событию
        String scriptPattern = "utils.create('%s', ['description' : 'description', "
                               + "'agreement' : '%s', 'timeZone' : '%s', 'clientOU' : '%s'])";
        String script = String.format(scriptPattern, scCase.getFqn(), agreement.getUuid(), timeZone.getUuid(),
                client.getUuid());
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.add, scriptInfo.getCode(), true, ouCase);
        DSLEventAction.add(eventAction);

        //Выполнение действий и проверок
        GUILogon.asTester();

        Set<String> oldScUuids = DSLBo.getUuidsByFqn(scCase.getFqn());

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo sc = DSLBo.getNewBoModel(oldScUuids, scCase);

        GUIBo.goToCard(sc);
        tester.refresh();
        GUILogon.assertAuthorizedLogin(SharedFixture.employee().getTitle());
    }

    /**
     * Проверка сохранения текущей авторизации после переподбора схемы эскалации при изменении определяющего
     * атрибута скриптом в действии на вход в статус
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00310
     * http://sd-jira.naumen.ru/browse/NSDPRD-4953
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать в типе запроса scCase пользовательский статус userState</li>
     * <li>Настроить переход из статуса resolved в userState</li>
     * <li>Добавить действие на вход в статус resolved. Скрипт: utils.edit(subject, ['state':'userState'])</li>
     * <li>Создать действие для эскалации escEvent (scCase, скрипт: пусто)</li>
     * <li>Включить действие для эскалации escEvent</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить схему эскалации escalationScheme</li>
     * <li>Создать уровень эскалации escLevel: escalationScheme, По истечении времени, 1 секунда, escEvent,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, escalationScheme по умолчанию, Статус
     * запроса</li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти в систему под тестером</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Изменить статус на resolved</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что статус изменился на userState</li>
     * <li>Проверить, что после обновления страницы пользователь остался тестером</li>
     * </ol>
     */
    @Test
    public void testSaveOriginalUserAfterEscalationtRecalcByStateScript()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        //Создаем пользовательский статус и настаиваем в него переход из статуса resolved
        BoStatus resolved = DAOBoStatus.createResolved(scCase);
        BoStatus userState = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(userState);
        DSLBoStatus.setTransitions(resolved, userState);

        //Настаиваем автоматический переход из статуса resolved в userState
        ScriptInfo preActionScript = DAOScriptInfo
                .createNewScriptInfo("utils.edit(subject, ['state':'" + userState.getCode() + "']);");
        DSLBoStatus.addPreAction(resolved, preActionScript);

        //Создаем действие для эскалации
        ScriptInfo escEventScript = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(escEventScript);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, escEventScript.getCode(), true,
                scCase);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "1 SECOND", false,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //Создаем таблицу соответствий
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(escalationScheme, scCase, "state");
        DSLCatalogItem.add(rulesSettings);

        //Создаем объекты
        Bo team = SharedFixture.team();
        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        //Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUISc.resolve(team, null);

        //Проверки
        DSLBo.assertState(sc, userState.getCode());
        tester.refresh();
        GUILogon.assertAuthorizedLogin(SharedFixture.employee().getTitle());
    }

    /**
     * Тестирование изменения схем эскалации, если таблица соответствий настроена на 2 и более подтипов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$50691603
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Создать тип запроса type</li>
     * <li>Создать типы запроса scCase1, scCase2, scCase3, родительским для которых является тип type</li>
     * <li>Создать контент "История событий" на карточке класса "Запрос"</li>
     * <li>Создать схему эскалации escalation (код счетчика времени - timeAllowance) для класса "Запрос", включить</li>
     * <li>Создать элемент справочника "Таблицы соответствий" escalationRule1 для эскалаций, типы scCase1,
     * определяющий атрибут - "Тип объекта"</li>
     * <li>Создать строку rowEsc1 в таблице соответствий escalationRule1: схема эскалации- escalation, тип -
     * "Любой"</li>
     * <li>Создать элемент справочника "Таблицы соответствий" escalationRule2 для эскалаций, типы scCase2, scCase3,
     * определяющий атрибут - "Тип объекта"</li>
     * <li>Создать строку rowEsc2 в таблице соответствий escalationRule2: схема эскалации- escalation, тип -
     * "Любой"</li>
     * <li>Создать запросы sc1 типа scCase1, sc2 типа scCase2, sc3 типа scCase3</li>
     * <b>Выполнение действий 1</b>
     * <li>Зайти под пользователем</li>
     * <li>Перейти на карточку запроса sc1</li>
     * <b>Проверка 1</b>
     * <li>В истории есть запись об изменении схем эскалации</li>
     * <b>Выполнение действий 2</b>
     * <li>Перейти на карточку запроса sc2</li>
     * <b>Проверка 2</b>
     * <li>В истории есть запись об изменении схем эскалации</li>
     * <b>Выполнение действий 3</b>
     * <li>Перейти на карточку запроса sc3</li>
     * <b>Проверка 3</b>
     * <li>В истории есть запись об изменении схем эскалации</li>
     * </ol>
     */
    @Test
    public void testSubTypesEscChanged()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass type = DAOScCase.create();
        MetaClass scCase1 = DAOScCase.create(type);
        MetaClass scCase2 = DAOScCase.create(type);
        MetaClass scCase3 = DAOScCase.create(type);
        DSLMetaClass.add(type, scCase1, scCase2, scCase3);
        ContentForm eventList = DAOContentCard.createEventList(scClass.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(eventList);
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, scClass);
        DSLEscalation.add(escalation);

        CatalogItem escalationRule1 = DAOCatalogItem.createEscalationRule(Lists.newArrayList(scCase1),
                Lists.newArrayList(SysAttribute.metaClass(scClass).getCode()));
        DSLCatalogItem.add(escalationRule1);

        CatalogItem escalationRule2 = DAOCatalogItem.createEscalationRule(Lists.newArrayList(scCase2, scCase3),
                Lists.newArrayList(SysAttribute.metaClass(scClass).getCode()));
        DSLCatalogItem.add(escalationRule2);

        RsRow rowEsc1 = DAORsRow.create(escalationRule1, GUIEscalation.ESCALATION_TARGET_DATA, escalation.getCode(),
                SysAttribute.metaClass(scClass).getCode(), "anyOptionItem");
        DSLRsRows.addRowToRSItem(rowEsc1);

        RsRow rowEsc2 = DAORsRow.create(escalationRule2, GUIEscalation.ESCALATION_TARGET_DATA, escalation.getCode(),
                SysAttribute.metaClass(scClass).getCode(), "anyOptionItem");
        DSLRsRows.addRowToRSItem(rowEsc2);

        Bo sc1 = DAOSc.create(scCase1);
        Bo sc2 = DAOSc.create(scCase2);
        Bo sc3 = DAOSc.create(scCase3);
        DSLBo.add(sc1, sc2, sc3);

        //Выполнение действий 1
        GUILogon.asTester();
        GUIBo.goToCard(sc1);

        //Проверка 1
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_ESCALATION_CHANGED);

        //Выполнение действий 2
        GUIBo.goToCard(sc2);

        //Проверка 2
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_ESCALATION_CHANGED);

        //Выполнение действий 3
        GUIBo.goToCard(sc3);

        //Проверка 3
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_ESCALATION_CHANGED);
    }

    /**
     * Тестирование неприменимости схем эскалации для типа, который не указан в списке типов ТС эскалации, при
     * наличии 2 и более типов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00448
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42790096
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>Создать типы запроса scCase1, scCase2, scCase3</li>
     * <li>Создать контент "История событий" на карточке класса "Запрос"</li>
     * <li>Создать схему эскалации escalation (код счетчика времени - timeAllowance) для класса "Запрос", включить</li>
     * <li>Создать элемент справочника "Таблицы соответствий" escalationRule для эскалаций, типы scCase1, scCase2,
     * определяющий атрибут - "Тип объекта"</li>
     * <li>Создать строку rowEsc в таблице соответствий escalationRule: схема эскалации- escalation, тип - "Любой"</li>
     * <li>Создать запрос sc типа scCase3</li>
     * <b>Выполнение действий</b>
     * <li>Зайти под пользователем</li>
     * <li>Перейти на карточку запроса sc</li>
     * <b>Проверка</b>
     * <li>В истории нет записей об изменении схем эскалации</li>
     * </ol>
     */
    @Test
    public void testThirdTypeObjectNotEscalated()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass scCase3 = DAOScCase.create();
        DSLMetaClass.add(scCase1, scCase2, scCase3);
        ContentForm eventList = DAOContentCard.createEventList(scClass.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(eventList);
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, scClass);
        DSLEscalation.add(escalation);

        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(Lists.newArrayList(scCase1, scCase2),
                Lists.newArrayList(SysAttribute.metaClass(scClass).getCode()));
        DSLCatalogItem.add(escalationRule);

        RsRow rowEsc = DAORsRow.create(escalationRule, GUIEscalation.ESCALATION_TARGET_DATA, escalation.getCode(),
                SysAttribute.metaClass(scClass).getCode(), "anyOptionItem");
        DSLRsRows.addRowToRSItem(rowEsc);

        Bo sc = DAOSc.create(scCase3);
        DSLBo.add(sc);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        //Проверка
        GUIEventList.assertEventCategoryIsAbsence(eventList, EC_ESCALATION_CHANGED);
    }

    /**
     * Тестирование работы эскалации с двумя одинаковыми таблицами соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>На карточку scCase добавить контент historyContent История изменений объекта</li>
     * <li>Создать элемент справочника Часовые пояса: timezone</li>
     * <li>Создать действие для эскалации escEvent (scCase, скрипт:utils.edit(subject, ['clientPhone':'new phone1']))
     * </li>
     * <li>Включить escEvent</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme</li>
     * <li>Создать уровень эскалации escLevel: escalationScheme, По истечении времени, 1 секунда, escEvent,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать 2 таблицы соответствий эскалации rulesSettings1..2: scCase, без значения по умолчанию, Часовой
     * пояс запроса</li>
     * <li>Добавить строки row1..2 в rulesSettings1..2: escalationScheme, timezone</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc типа scCase, часовой пояс - timezone</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что значение атрибута Контактный телефон изменилось на 'new phone1'</li>
     * <li>Проверить, что в контенте historyContent появилась только 1 записи о сработанной эскалации</li>
     * </ol>
     */
    @Test
    public void testTwoSameRulesSettings()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);

        //Создаем контент
        ContentForm historyContent = DAOContentCard.createEventList(scCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        //Создаем элементы справочникаов
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        Bo agreement = SharedFixture.agreement();
        DSLBo.add(ou);
        DSLAgreement.addRecipients(ou, agreement);

        //Создаем действие для эскалации
        String clientPhoneValue = ModelUtils.createTitle();
        String scriptPattern = "utils.edit(subject, ['clientPhone':'%s'])";
        String scriptBody = String.format(scriptPattern, clientPhoneValue);
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase);
        DSLEventAction.add(escEvent);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme);

        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "1 SECOND", false,
                escEvent);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        //Создаем таблицу соответствий
        String escSourceAttr = "timeZone";
        CatalogItem rulesSettings1 = DAOCatalogItem.createEscalationRule(scCase, escSourceAttr);
        CatalogItem rulesSettings2 = DAOCatalogItem.createEscalationRule(scCase, escSourceAttr);
        DSLCatalogItem.add(rulesSettings1, rulesSettings2);

        //Добавляем строки
        RsRow escRow1 = DAORsRow.create(rulesSettings1, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), escSourceAttr, timezone.getUuid());
        RsRow escRow2 = DAORsRow.create(rulesSettings2, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme.getCode(), escSourceAttr, timezone.getUuid());
        DSLRsRows.addRowToRSItem(escRow1, escRow2);

        //Выполнение действия
        Bo sc = DAOSc.create(scCase, ou, agreement, timezone);
        DSLBo.add(sc);

        //Проверки
        Attribute clientPhoneAttr = SysAttribute.clientPhone(scCase);
        clientPhoneAttr.setValue(clientPhoneValue);
        DSLBo.assertStringAttr(sc, clientPhoneAttr);

        GUILogon.asTester();
        GUIBo.goToCard(sc);

        String escMsg = String.format(ED_ESCALATION_LEVEL, 1, escalationScheme.getTitle(), escEvent.getTitle());
        GUIEventList.assertEventAdvlistPresent(historyContent, sc.getUuid(), escMsg, "plannedEvent");
    }

    /**
     * Тест на дефект NSDPRD-1285  Срабатывает эскалация по таблице соответствий, которая помещена в архив
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00449
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase, тип отдела ouCase</li>
     * <li>Создать элемент справочника Часовые пояса: timezone</li>
     * <li>Создать дефолтное соглашение agreement</li>
     * <li>Создать действие для эскалации escEvent1 (scCase, скрипт:
     * utils.edit(subject, ['clientPhone':'new phone1']))</li>
     * <li>Включить escEvent1</li>
     * <li>Создать схему эскалации escalationScheme(системный счетчик запроса, scCase)</li>
     * <li>Включить escalationScheme</li>
     * <li>Создать уровень эскалации escLevel1: escalationScheme1, По истечении времени, 5 секунд ,escEvent1,
     * Выполнять действие при изменении схемы - нет</li>
     * <li>Создать таблицу соответствий эскалации rulesSettings: scCase, без значения по умолчанию, Часовой пояс
     * запроса, Соглашение</li>
     * <li>Добавить строку row1 в rulesSettings: escalationScheme, timezone, соглашения - agreement</li>
     *
     * <br>
     * <b>Выполнение действия</b>
     * <li>Создать запрос sc1 типа scCase, часовой пояс - timezone1, соглашение - agreement1</li>
     * <li>Поместить таблицу соответствий схемы эскалации rulesSettings в архив  </li>
     * <li>Создать запрос sc2 типа scCase, часовой пояс - timezone1, соглашение - agreement1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что у sc1 значение атрибута Контактный телефон изменилось на 'new phone1'</li>
     * <li>Проверить, что у sc2 значение атрибута Контактный телефон не изменилось на 'new phone1'</li>
     * </ol>
     */
    @Test
    public void testWorkWithArchiveRulesSettingsEscScheme()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = SharedFixture.ouCase();
        DSLMetaClass.add(scCase);
        //Создаем элементы справочников
        CatalogItem timezone = DAOCatalogItem.createTimeZone("America/Campo_Grande");
        DSLCatalogItem.add(timezone);

        //Создаем объекты
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addRecipients(ou, agreement);
        //Создаем действие для эскалации
        String valueFromRulesSettings = ModelUtils.createTitle();
        String scriptBody = "utils.edit(subject, ['clientPhone':'" + valueFromRulesSettings + "'])";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(script);
        EventAction escEvent1 = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true, scCase);
        DSLEventAction.add(escEvent1);

        //Создаем схему эскалации
        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalationScheme1 = DAOEscalationSheme.create(timeAllowanceTimer, true, scCase);
        DSLEscalation.add(escalationScheme1);
        //Создаем уровень эскалации
        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "5 SECOND", false,
                escEvent1);
        DSLEscalation.addLevel(escalationScheme1.getCode(), escLevel);

        //Создаем таблицу соответствий
        String escSourceAttr1 = "timeZone";
        String escSourceAttr2 = "agreement";
        CatalogItem rulesSettings = DAOCatalogItem.createEscalationRule(scCase, escSourceAttr1, escSourceAttr2);
        DSLCatalogItem.add(rulesSettings);
        //Добавляем строки
        RsRow escRow1 = DAORsRow.createManySources(rulesSettings, GUIEscalation.ESCALATION_TARGET_DATA,
                escalationScheme1.getCode(), escSourceAttr1, timezone.getUuid(), escSourceAttr2, agreement.getUuid());
        DSLRsRows.addRowToRSItem(escRow1);

        //Выполнение действия
        Attribute clientPhone = SysAttribute.clientPhone(scCase);
        Bo sc1 = DAOSc.create(scCase, ou, agreement, timezone);
        String clientPhoneValue1 = ModelUtils.createTitle();
        clientPhone.setValue(clientPhoneValue1);
        sc1.setScClientPhone(clientPhoneValue1);
        DSLBo.add(sc1);
        //Помещаем схему эскалации в архив
        Attribute removed = DAOAttribute.createPseudo("", "removed", Boolean.TRUE.toString());
        DSLCatalogItem.editAttributes(rulesSettings, removed);

        //Проверяем, что сразу после архивирования эскалация еще не сработала
        DSLBo.assertStringAttr(sc1, clientPhone);

        Bo sc2 = DAOSc.create(scCase, ou, agreement, timezone);
        String clientPhoneValue2 = ModelUtils.createTitle();
        clientPhone.setValue(clientPhoneValue2);
        sc2.setScClientPhone(clientPhoneValue2);
        DSLBo.add(sc2);

        //Проверки
        DSLBo.assertStringAttr(sc1, clientPhone.getCode(), valueFromRulesSettings);

        clientPhone.setValue(clientPhoneValue2);
        DSLBo.assertStringAttr(sc2, clientPhone);
    }
}
