package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.OBJECTCARD;
import static ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication.CONTENTS_GET_PARAMETERS;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.embeddedapplication.JsApiAttributeFormatTester;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication;
import ru.naumen.selenium.casesutil.model.metaclass.UsagePointApplication.FormType;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование формата передачи данных в jsApi
 *
 * <AUTHOR>
 * @since 07.02.2020
 */
class JsApiAttributeFormatsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userClass, userCase;
    private static Attribute[] groupAttributes, generatedAttributes;
    private static Attribute metaClassAttr, stateAttr, responsibleAttr, objectChainAttr, boLinksChainAttr;
    private static ContentForm contentForm;

    /**
     * <ol>
     *   <b>Общая часть подготовки.</b>
     *   <li>Создать пользовательский класс <code>userClass</code> с назначением ответственного</li>
     *   <li>Создать пользовательский тип <code>userCase</code></li>
     *   <li>Создать атрибуты различных типов для тестирования: целое число, вещественное число, логический,
     *   гиперссылка, тип объекта, набор типов класса, строка, текст, текст в формате RTF, текст подсветкой
     *   синтаксиса, статус, дата, дата и время, временной интервал, агрегирующие с разными агрегируемыми классами,
     *   ответственный, ссылка на БО, набор ссылок на БО, обратная ссылка, файл, элемент справочника, набор
     *   элементов справочника и разные комбинации атрибутов связанных объектов (связь через СБО/НБО, ссылается на
     *   строку/агрегирующий/ответственный).</li>
     *   <li>Добавить в системную группу атрибутов ранее созданные атрибуты</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWith().responsible().workFlow().create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userClass);

        contentForm = DSLContent.getDefaultCardContent(userCase);

        Attribute integerAttr = DAOAttribute.createInteger(userClass);
        Attribute doubleAttr = DAOAttribute.createDouble(userClass);
        Attribute booleanAttr = DAOAttribute.createBoolTypeEditCheckbox(userClass.getFqn(), Boolean.FALSE);
        Attribute hyperlinkAttr = DAOAttribute.createHyperlink(userClass.getFqn());
        metaClassAttr = SysAttribute.metaClass(userClass);
        Attribute caseListAttr = DAOAttribute.createCaseList(userClass, SharedFixture.employeeCase());

        Attribute stringAttr = DAOAttribute.createString(userClass);
        Attribute textAttr = DAOAttribute.createText(userClass);
        Attribute textRtfAttr = DAOAttribute.createTextRTF(userClass.getFqn());
        Attribute sourceCodeAttr = DAOAttribute.createSourceCode(userClass.getFqn());
        stateAttr = SysAttribute.state(userClass);
        Attribute dateAttr = DAOAttribute.createDate(userClass);
        Attribute dateTimeAttr = DAOAttribute.createDateTimeWithEditTimeType(userClass.getFqn());
        Attribute dtIntervalAttr = DAOAttribute.createTimeInterval(userClass);

        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null);
        Attribute aggregateAttr2 = DAOAttribute.createAggregate(userClass, AggregatedClasses.TEAM, null, null);
        Attribute aggregateAttr3 = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU_AND_TEAM, null, null);
        responsibleAttr = SysAttribute.responsible(userClass);
        Attribute objectAttr = DAOAttribute.createObjectLink(userClass, SharedFixture.employeeCase(), null);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, SharedFixture.employeeCase());
        Attribute objectAttr2 = DAOAttribute.createObjectLink(userClass, userClass, null);
        Attribute backBOLinksAttr = DAOAttribute.createBackBOLinks(userClass, objectAttr2);
        Attribute fileAttr = DAOAttribute.createFile(userClass);

        Catalog catalog = DAOCatalog.createSystem(SystemCatalog.CLOSURECODE);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(userClass, catalog, null);
        Attribute catalogItemsAttr = DAOAttribute.createCatalogItemSet(userClass, catalog);

        objectChainAttr = DAOAttribute.createObjectLink(userClass, userClass, null);
        boLinksChainAttr = DAOAttribute.createBoLinks(userClass, userClass);
        Attribute stringRelatedAttr =
                DAOAttribute.createAttributeOfRelatedObject(userClass, objectChainAttr, stringAttr);
        Attribute stringRelatedAttr2 =
                DAOAttribute.createAttributeOfRelatedObject(userClass, boLinksChainAttr, stringAttr);
        Attribute aggregateRelatedAttr =
                DAOAttribute.createAttributeOfRelatedObject(userClass, objectChainAttr, aggregateAttr);
        Attribute aggregateRelatedAttr2 =
                DAOAttribute.createAttributeOfRelatedObject(userClass, boLinksChainAttr, aggregateAttr);
        Attribute responsibleRelatedAttr =
                DAOAttribute.createAttributeOfRelatedObject(userClass, objectChainAttr, responsibleAttr);
        Attribute responsibleRelatedAttr2 =
                DAOAttribute.createAttributeOfRelatedObject(userClass, boLinksChainAttr, responsibleAttr);

        DSLAttribute.add(integerAttr, doubleAttr, booleanAttr, hyperlinkAttr, caseListAttr, stringAttr, textAttr,
                textRtfAttr, sourceCodeAttr, dateAttr, dateTimeAttr, dtIntervalAttr, objectAttr, objectAttr2,
                boLinksAttr, backBOLinksAttr, aggregateAttr, aggregateAttr2, aggregateAttr3, fileAttr, catalogItemAttr,
                catalogItemsAttr, objectChainAttr, boLinksChainAttr, stringRelatedAttr, stringRelatedAttr2,
                aggregateRelatedAttr, aggregateRelatedAttr2, responsibleRelatedAttr, responsibleRelatedAttr2);

        generatedAttributes = new Attribute[] { integerAttr, doubleAttr, booleanAttr, hyperlinkAttr,
                metaClassAttr, caseListAttr, stringAttr, textAttr, textRtfAttr, sourceCodeAttr, stateAttr,
                dateAttr, dateTimeAttr, dtIntervalAttr, objectAttr, boLinksAttr, backBOLinksAttr,
                aggregateAttr, aggregateAttr2, aggregateAttr3, responsibleAttr, fileAttr, catalogItemAttr,
                catalogItemsAttr };

        groupAttributes = new Attribute[] { integerAttr, doubleAttr, booleanAttr, hyperlinkAttr, metaClassAttr,
                caseListAttr, stringAttr, textAttr, textRtfAttr, sourceCodeAttr, stateAttr, dateAttr, dateTimeAttr,
                dtIntervalAttr, objectAttr, boLinksAttr, backBOLinksAttr, aggregateAttr, aggregateAttr2, aggregateAttr3,
                responsibleAttr, fileAttr, catalogItemAttr, catalogItemsAttr, objectChainAttr, boLinksChainAttr,
                stringRelatedAttr, stringRelatedAttr2, aggregateRelatedAttr, aggregateRelatedAttr2,
                responsibleRelatedAttr, responsibleRelatedAttr2 };
        DSLGroupAttr.addToGroup(attrsGroup, groupAttributes);
    }

    /**
     * Проверка правильности формата передачи данных методом <code>jsApi.events.addFieldChangeListener</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84322833
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$231528724
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая часть подготовки}</li>
     *   <li>Сгенерировать ожидаемые значения для атрибутов: целое число, вещественное число, логический,
     *   гиперссылка, тип объекта, набор типов класса, строка, текст, текст в формате RTF, текст подсветкой
     *   синтаксиса, статус, дата, дата и время, временной интервал, агрегирующие с разными агрегируемыми классами,
     *   ответственный, ссылка на БО, набор ссылок на БО, обратная ссылка, файл, элемент справочника, набор
     *   элементов справочника</li>
     *   <li>Создать встроенное приложение, которое отображает информацию о формате передачи различных типов
     *   атрибутов</li>
     *   <li>Добавить контент со встроенным приложением на карточку объекта типа <code>userClass</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на форму добавления объекта типа <code>userClass</code></li>
     *   <li>Нажать кнопку "Редактировать" в контенте, в который выведена системная группа атрибутов</li>
     *   <li>Заполнить атрибуты на форме редактирования атрибутов ранее сгенерированными значениями</li>
     *   <li>Заполнить атрибут Название, чтобы гарантированно отработали все события изменения атрибутов</li>
     *   <li>Проверить, что встроенное приложение вывело ожидаемые значения атрибутов</li>
     * </ol>
     */
    @Test
    void testFormatInFieldChangeListener()
    {
        // Подготовка:
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(groupAttributes, generatedAttributes);

        // пропускаем метакласс и статус внутри ВП чтобы не создавать отдельную группу атрибутов
        String jsContent = formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID) +
                           "for (const attribute of attributes) {\n" +
                           "    if (attribute == 'metaClass' || attribute == 'state') {\n" +
                           "        continue\n" +
                           "    }\n" +
                           "    jsApi.events.addFieldChangeListener(attribute, function (result) {\n" +
                           "       logIt(result.attribute, result.newValue);\n" +
                           "    })\n" +
                           "}";
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки:
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        formatTester.fillAttributes();
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle()); //Клик по другому атрибуту

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues(true);
        expectedValues.remove(metaClassAttr);
        expectedValues.remove(stateAttr);
        assertAttributesValuesPresentInEmbeddedApp(applicationContent, expectedValues);
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * Проверка правильности формата передачи данных методом <code>jsApi.events.addSubjectChangeListener</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84322833
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$231528724
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая часть подготовки}</li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Сгенерировать ожидаемые значения для атрибутов: целое число, вещественное число, логический,
     *   гиперссылка, тип объекта, набор типов класса, строка, текст, текст в формате RTF, текст подсветкой
     *   синтаксиса, статус, дата, дата и время, временной интервал, агрегирующие с разными агрегируемыми классами,
     *   ответственный, ссылка на БО, набор ссылок на БО, обратная ссылка, файл, элемент справочника, набор
     *   элементов справочника, атрибут связи СБО для атрибута связанного объекта приравнять объекту
     *   <code>userBo</code>, атрибут связи НБО для атрибута связанного объекта приравнять объекту
     *   <code>userBo</code></li>
     *   <li>Создать встроенное приложение, которое отображает информацию о формате передачи различных типов
     *   атрибутов</li>
     *   <li>Добавить контент со встроенным приложением на карточку объекта типа <code>userClass</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на карточку объекта <code>userBo</code></li>
     *   <li>Нажать кнопку "Редактировать" в контенте, в который выведена системная группа атрибутов</li>
     *   <li>Заполнить атрибуты на форме редактирования атрибутов ранее сгенерированными значениями</li>
     *   <li>Сохранить форму редактирования параметров</li>
     *   <li>Проверить, что встроенное приложение вывело ожидаемые значения атрибутов</li>
     * </ol>
     */
    @Test
    void testFormatInSubjectChangeListener()
    {
        // Подготовка:
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Map<Attribute, Object> attributeValues = JsApiAttributeFormatTester.valuesBuilder()
                .generateValues(generatedAttributes)
                .withValue(objectChainAttr, userBo)
                .withValue(boLinksChainAttr, List.of(userBo))
                .build();
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(groupAttributes, attributeValues);

        String jsContent = formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID) +
                           "for (const attribute of attributes) {\n" +
                           "    jsApi.events.addSubjectChangeListener(attribute, function (result) {\n" +
                           "       logIt(result.attribute, result.value);\n" +
                           "    })\n" +
                           "}\n";
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки:
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.clickEdit(contentForm);
        formatTester.fillAttributes();
        GUIForm.applyForm();
        // очищаем значение атрибута Ответственный, ранее заполненное на форме
        Cleaner.afterTest(true, () ->
        {
            responsibleAttr.setValue(null);
            DSLBo.editAttributeValue(userBo, responsibleAttr);
        });

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues();
        assertAttributesValuesPresentInEmbeddedApp(applicationContent, expectedValues);
        String actualValues = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actualValues);
    }

    /**
     * Проверка правильности формата передачи данных методом API <code>jsApi.contents.getParameters</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84322833
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$231528724
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Сгенерировать ожидаемые значения для параметров контента: целое число, вещественное число, логический,
     *   гиперссылка, набор типов класса, строка, текст, текст в формате RTF, текст подсветкой синтаксиса, дата, дата
     *   и время, временной интервал, агрегирующий, ответственный, ссылка на БО, набор ссылок на БО, файл, элемент
     *   справочника, набор элементов справочника, элемент произвольного справочника, набор элементов произвольного
     *   справочника</li>
     *   <li>Создать встроенное приложение, которое отображает информацию о формате передачи различных типов
     *   параметров контента</li>
     *   <li>Добавить контент со встроенным приложением на карточку объекта типа <code>userClass</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под суперпользователем</li>
     *   <li>Заполнить параметры на форме редактирования контента ВП ранее сгенерированными значениями</li>
     *   <li>Сохранить форму редактирования параметров</li>
     *   <li>Перейти на карточку объекта <code>userBo</code></li>
     *   <li>Проверить, что встроенное приложение вывело ожидаемые значения параметров контента</li>
     * </ol>
     */
    @Test
    void testFormatInContentParameters()
    {
        // Подготовка:
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // именно с такими кодами созданы атрибуты внутри ВП
        Attribute integerAttr = DAOAttribute.createInteger(userClass).withCode("integerAttr");
        Attribute doubleAttr = DAOAttribute.createDouble(userClass).withCode("doubleAttr");
        Attribute booleanAttr = DAOAttribute.createBoolTypeEditCheckbox(userClass.getFqn(), Boolean.FALSE)
                .withCode("booleanAttr");
        Attribute hyperlinkAttr = DAOAttribute.createHyperlink(userClass.getFqn()).withCode("hyperlinkAttr");
        Attribute caseListAttr = DAOAttribute.createCaseList(userClass.getFqn(), SharedFixture.employeeCase())
                .withCode("caseListAttr");

        Attribute stringAttr = DAOAttribute.createString(userClass).withCode("stringAttr");
        Attribute textAttr = DAOAttribute.createText(userClass).withCode("textAttr");
        Attribute textRtfAttr = DAOAttribute.createTextRTF(userClass.getFqn()).withCode("textRtfAttr");
        Attribute sourceCodeAttr = DAOAttribute.createSourceCode(userClass.getFqn()).withCode("sourceCodeAttr");
        Attribute dateAttr = DAOAttribute.createDate(userClass.getFqn()).withCode("dateAttr");
        Attribute dateTimeAttr = DAOAttribute.createDateTime(userClass).withCode("dateTimeAttr");
        Attribute dtIntervalAttr = DAOAttribute.createTimeInterval(userClass.getFqn()).withCode("dtIntervalAttr");

        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null)
                .withCode("aggregateAttr");
        Attribute objectAttr = DAOAttribute.createObjectLink(userClass, SharedFixture.employeeCase(), null)
                .withCode("objectAttr");
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass.getFqn(), SharedFixture.employeeCase())
                .withCode("boLinksAttr");
        Attribute fileAttr = DAOAttribute.createFile(userClass).withCode("fileAttr");

        Catalog catalog = DAOCatalog.createSystem(SystemCatalog.CLOSURECODE);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(userClass, catalog, null)
                .withCode("catalogItemAttr");
        Attribute catalogItemsAttr = DAOAttribute.createCatalogItemSet(userClass, catalog).withCode("catalogItemsAttr");
        Attribute catalogAnyItemAttr = DAOFormParameter.createCatalogAnyItem(null).withCode("catalogAnyItemAttr");
        Attribute catalogAnyItemSetAttr = DAOFormParameter.createCatalogAnyItemSet(null)
                .withCode("catalogAnyItemSetAttr");

        // для набора ссылок задаём явно одно значение, в силу того что несколько значений в параметров конткнта
        // могут возвращаться в случайном порядке
        Map<Attribute, Object> attributeValues = JsApiAttributeFormatTester.valuesBuilder()
                .generateValues(integerAttr, doubleAttr, booleanAttr, hyperlinkAttr, dtIntervalAttr, caseListAttr,
                        stringAttr, textAttr, textRtfAttr, sourceCodeAttr, dateAttr, dateTimeAttr, objectAttr,
                        aggregateAttr, fileAttr, catalogItemAttr, catalogItemsAttr, catalogAnyItemAttr,
                        catalogAnyItemSetAttr)
                .withValue(boLinksAttr, List.of(SharedFixture.employee()))
                .build();

        Attribute[] attributes = new Attribute[] {
                integerAttr, doubleAttr, booleanAttr, hyperlinkAttr, dtIntervalAttr, caseListAttr, stringAttr, textAttr,
                textRtfAttr, sourceCodeAttr, dateAttr, dateTimeAttr, objectAttr, boLinksAttr, aggregateAttr, fileAttr,
                catalogItemAttr, catalogItemsAttr, catalogAnyItemAttr, catalogAnyItemSetAttr
        };
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(attributes, attributeValues);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                CONTENTS_GET_PARAMETERS);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);

        // Действия и проверки:
        GUILogon.asSuper();
        GUIContent.add(applicationContent);
        GUIContent.clickEditContent(applicationContent);
        formatTester.fillAttributes();
        GUIForm.clickApply();

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues();
        assertAttributesValuesPresentInEmbeddedApp(applicationContent, expectedValues);
        String actualValues = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actualValues);
    }

    /**
     * Проверка правильности формата передачи данных методом API <code>jsApi.forms.getValues</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84322833
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$231528724
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая часть подготовки}</li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Сгенерировать ожидаемые значения для атрибутов: целое число, вещественное число, логический,
     *   гиперссылка, тип объекта, набор типов класса, строка, текст, текст в формате RTF, текст подсветкой
     *   синтаксиса, статус, дата, дата и время, временной интервал, агрегирующие с разными агрегируемыми классами,
     *   ответственный, ссылка на БО, набор ссылок на БО, обратная ссылка, файл, элемент справочника, набор
     *   элементов справочника, атрибут связи СБО для атрибута связанного объекта приравнять объекту
     *   <code>userBo</code>, атрибут связи НБО для атрибута связанного объекта приравнять объекту
     *   <code>userBo</code></li>
     *   <li>Создать встроенное приложение, которое отображает информацию о формате передачи различных типов
     *   атрибутов</li>
     *   <li>Добавить контент со встроенным приложением на карточку объекта типа <code>userClass</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под пользователем</li>
     *   <li>Перейти на карточку объекта <code>userBo</code></li>
     *   <li>Нажать кнопку "Редактировать" в контенте, в который выведена системная группа атрибутов</li>
     *   <li>Заполнить атрибуты на форме редактирования атрибутов ранее сгенерированными значениями</li>
     *   <li>Сохранить форму редактирования атрибутов</li>
     *   <li>Проверить, что встроенное приложение вывело ожидаемые значения атрибутов</li>
     * </ol>
     */
    @Test
    void testFormatInFormValues()
    {
        // Подготовка:
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Map<Attribute, Object> attributeValues = JsApiAttributeFormatTester.valuesBuilder()
                .generateValues(generatedAttributes)
                .withValue(objectChainAttr, userBo)
                .withValue(boLinksChainAttr, List.of(userBo))
                .build();
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(groupAttributes, attributeValues);

        String jsContent = formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID) +
                           "jsApi.events.addSubjectChangeListener('title', function (result) {\n" +
                           "    jsApi.forms.getValues().then(function (value) {\n" +
                           "        for (const attribute of attributes) {\n" +
                           "            logIt(attribute, value[attribute])\n" +
                           "        }\n" +
                           "    });\n" +
                           "});\n";
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(userCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки:
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.clickEdit(contentForm);
        formatTester.fillAttributes();
        GUIForm.applyForm();
        // очищаем значение атрибута Ответственный, ранее заполненное на форме
        Cleaner.afterTest(true, () ->
        {
            responsibleAttr.setValue(null);
            DSLBo.editAttributeValue(userBo, responsibleAttr);
        });

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues();
        assertAttributesValuesPresentInEmbeddedApp(applicationContent, expectedValues);
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * Проверка правильности формата передачи особенных атрибутов класса Сотрудник методом API
     * <code>jsApi.forms.getValues</code>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$109856401
     * <br>
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Создать тип <code>employeeCase</code> класса Сотрудник</li>
     *   <li>Создать сотрудника <code>employee</code> типа <code>employeeCase</code></li>
     *   <li>Создать атрибут "Группы пользователей сотрудника" редактируемым на уровне типа
     *   <code>employeeCase</code></li>
     *   <li>Сгенерировать ожидаемые значения для атрибутов: группы пользователей сотрудника, лицензия</li>
     *   <li>Создать встроенное приложение, которое отображает информацию о формате передачи различных типов
     *   атрибутов</li>
     *   <li>Добавить контент со встроенным приложением на карточку объекта типа <code>employeeCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под пользователем <code>employee</code> и оказаться на его карточке</li>
     *   <li>Нажать кнопку "Редактировать" в контенте, в который выведена системная группа атрибутов</li>
     *   <li>Заполнить атрибуты на форме редактирования атрибутов ранее сгенерированными значениями</li>
     *   <li>Сохранить форму редактирования атрибутов</li>
     *   <li>Проверить, что встроенное приложение вывело ожидаемые значения атрибутов</li>
     * </ol>
     */
    @Test
    void testFormatForEmployee()
    {
        // Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        Attribute employeeSecGroupsAttr = SysAttribute.employeeSecGroups(employeeCase);
        employeeSecGroupsAttr.setEditable(Boolean.TRUE.toString());
        DSLAttribute.edit(employeeSecGroupsAttr);

        Attribute licenseAttr = SysAttribute.license(employeeCase);

        Attribute[] generatedAttributes2 = new Attribute[] { employeeSecGroupsAttr, licenseAttr };
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(generatedAttributes2);

        GroupAttr attrsGroup2 = DAOGroupAttr.createSystem(employeeCase);
        DSLGroupAttr.addToGroup(attrsGroup2, generatedAttributes2);

        ContentForm contentForm2 = DAOContentCard.createPropertyList(employeeCase, attrsGroup2);
        DSLContent.add(contentForm2);

        String jsContent = formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID) +
                           "jsApi.events.addSubjectChangeListener('firstName', function (result) {\n" +
                           "    jsApi.forms.getValues().then(function (value) {\n" +
                           "        for (const attribute of attributes) {\n" +
                           "            logIt(attribute, value[attribute])\n" +
                           "        }\n" +
                           "    });\n" +
                           "});\n";
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(employeeCase, application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.login(employee);
        GUIContent.clickEdit(contentForm2);
        formatTester.fillAttributes();
        GUIForm.applyForm();

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues();
        assertAttributesValuesPresentInEmbeddedApp(applicationContent, expectedValues);
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(applicationContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * Тестирование передачи метакласса в контекст ВП на форме добавления запроса для метода API
     * <code>jsApi.forms.getValues()</code>
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00127
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180576615
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>{@link #prepareFixture() Общая часть подготовки}</li>
     *   <li>Создать тип <code>scCase</code> класса "Запрос"</li>
     *   <li>Добавить встроенное приложение <code>application</code>, получающее значение атрибутов с формы:
     *   <pre>
     *   -----------------------------------------------------------------------
     *     jsApi.forms.getValues().then((result) => {
     *         var testDiv = document.getElementById('test_div')
     *         testDiv.innerText += JSON.stringify(result)
     *     })
     *   -----------------------------------------------------------------------
     *   </pre>
     *   </li>
     *   <li>Добавить встроенное приложение <code>application</code> на форму добавления запроса</li>
     *   <li>Получить ссылку на форму добавления запроса типа <code>scCase</code>, выполнив скрипт:
     *   <pre>
     *   -----------------------------------------------------------------------
     *     return api.web.add('$fqn');
     *   -----------------------------------------------------------------------
     *   Где:
     *     1) $fqn - FQN типа <code>scCase</code>.
     *   </pre>
     *   </li>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под сотрудником</li>
     *   <li>Перейти на форму добавления запроса по полученной ссылке</li>
     *   <li>Проверить, что в ВП <code>applicationContent</code> присутствует FQN типа <code>scCase</code></li>
     * </ol>
     */
    @Test
    void testFillMetaClassFqnForEmbeddedApplicationOnAddServiceCall()
    {
        // Подготовка:
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        String jsContent = String.format("jsApi.forms.getValues().then((result) => {\n"
                                         + "   var testDiv = document.getElementById('%s')\n"
                                         + "   testDiv.innerText += JSON.stringify(result)\n"
                                         + "})", GUIEmbeddedApplication.TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);

        ContentForm applicationContent = DAOContentAddForm.createEmbeddedApplication(scClass.getFqn(), application);
        DSLContent.add(applicationContent);

        String url = ScriptRunner.executeScript(String.format("api.web.add('%s')", scCase.getFqn()));

        // Действия и проверки:
        GUILogon.asTester();
        tester.goToPage(url);

        String result = GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationContent);
        Assertions.assertTrue(result.contains(String.format("\"metaClass\":\"%s\"", scCase.getFqn())));
    }

    /**
     * Тестирование работоспособности метода API <code>jsApi.forms.getValues()</code> на форме пользовательского
     * действия по событию
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00657
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$169294853
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$194669499
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Добавить действие по событию <code>eventAction</code>:
     *   <ul>
     *     <li>Тип = "Пользовательское событие"</li>,
     *     <li>Скрипт = "return true"</li>,
     *     <li>Включен = Да</li>,
     *     <li>Синхронный = Да</li>,
     *     <li>Объект = <code>userCase</code></li>.
     *   </ul></li>
     *   <li>Добавить строковый параметр <code>stringParam</code> для действия по событию <code>eventAction</code></li>
     *   <li>Добавить встроенное приложение <code>application</code>, получающее значение атрибутов с формы:
     *   <pre>
     *   -----------------------------------------------------------------------
     *     jsApi.forms.getValues().then((result) => {
     *         var testDiv = document.getElementById('test_div')
     *         testDiv.innerText += JSON.stringify(Object.keys(result).sort())
     *     })
     *   -----------------------------------------------------------------------
     *   </pre></li>
     *   <li>Добавить место использования <code>application</code>:
     *   <ul>
     *     <li>Объекты = <code>userClass</code></li>,
     *     <li>Тип формы = "Форма пользовательского действия по событию"/li>,
     *     <li>Доступные пользовательские действия по событию = <code>eventAction</code></li>.
     *   </ul></li>
     *   <li>На карточку объектов <code>userCase</code> добавить кнопку пользовательских действий по событию:
     *   <code>eventAction</code></li>
     *   <li>Добавить объект <code>userBo</code> пользовательского класса <code>userCase</code></li>
     *   <br>
     *   <b>Действия и проверки.</b>
     *   <li>Войти под сотрудником</li>
     *   <li>Открыть карточку объекта <code>userBo</code></li>
     *   <li>Нажать на кнопку пользовательского действия по событию <code>eventAction</code></li>
     *   <li>Проверить, что в контенте с ВП <code>application</code> содержится текст: ["stringAttribute"]</li>
     * </ol>
     */
    @Test
    void testGetValuesOnUserEventActionForm()
    {
        // Подготовка:
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(scriptInfo);

        EventAction eventAction =
                DAOEventAction.createEventScript(EventType.userEvent, scriptInfo, true, TxType.Sync, userCase);
        DSLEventAction.add(eventAction);

        FormParameter stringParam = DAOFormParameter.createString();
        DSLFormParameter.saveOnEventAction(eventAction, stringParam);

        String jsContent = String.format("jsApi.forms.getValues().then((result) => {\n" +
                                         "   var testDiv = document.getElementById('%s')\n" +
                                         "   testDiv.innerText += JSON.stringify(Object.keys(result))\n" +
                                         "})", GUIEmbeddedApplication.TEST_DIV_ID);
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                jsContent);
        DSLEmbeddedApplication.add(application);
        DSLEmbeddedApplication.setApplicationsAvailableOnModalForm(application);

        UsagePointApplication applicationUsage =
                DAOEmbeddedApplication.createUsagePointApplication(FormType.UserEventActionForm, userClass);
        applicationUsage.setUserEventActions(eventAction);
        DSLEmbeddedApplication.edit(application, null, applicationUsage);

        ContentForm card = DAOContentCard.createWindow(userCase);
        String buttonTitle = ModelUtils.createTitle();
        DSLContent.addUserTool(card, buttonTitle, eventAction);

        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(userCase, OBJECTCARD));

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Действия и проверки:
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.clickButtonByText(buttonTitle);

        Assertions.assertEquals(String.format("[\"%s\"]", stringParam.getCode()),
                GUIEmbeddedApplication.getEmbeddedApplicationContent(applicationUsage),
                "Содержимое встроенного приложения не соответствует ожиданиям!");
    }

    /**
     * Проверить, что в ВП прогрузились коды всех искомых атрибутов
     * @param applicationContent контент, в котором находится ВП
     * @param expectedValues мапа Атрибут-Значение
     */
    private void assertAttributesValuesPresentInEmbeddedApp(ContentForm applicationContent,
            Map<Attribute, String> expectedValues)
    {
        GUIFrame.switchToFrame(String.format(Div.IFRAME_PATTERN, applicationContent.getXpathId()));
        for (Map.Entry<Attribute, String> item : expectedValues.entrySet())
        {
            Attribute attribute = item.getKey();
            String attributeCode = attribute.getCode();
            GUITester.assertTextContainsWithMsg(GUIXpath.Div.ID_PATTERN, attributeCode,
                    String.format("Атрибут с кодом %s не отобразился во встроенном приложении", attributeCode),
                    GUIEmbeddedApplication.TEST_DIV_ID);
        }
        GUIFrame.switchToTopWindow();
    }
}