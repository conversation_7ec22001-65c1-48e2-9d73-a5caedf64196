package ru.naumen.selenium.cases.admin.adminprofiles;

import static ru.naumen.selenium.casesutil.admin.GUIAdmin.EDIT_SUPER_USER_BUTTON;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.ADMINISTRATION_PROFILES;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.ADMINISTRATION_PROFILES_MANAGEMENT;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.OPERATOR_INTERFACE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.SUPER_USERS;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.ALL;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.VIEW;
import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.io.File;
import java.util.Map;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.InputComplex;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.DSLInterface;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.adminprofiles.GUIAdminProfile;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.admin.log.Constants.CategoryCode;
import ru.naumen.selenium.casesutil.model.admin.log.Constants.CategoryTitle;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование профилей администрирования в интерфейсе администратора
 * <AUTHOR>
 * @since 05.05.2024
 */
public class AdminProfilesTest extends AbstractTestCase
{
    private static AdminProfile adminProfile;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить использование профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность профилей администрирования на стенде (setSetsEnabled - true)</li>
     * <li>Добавить в систему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);
        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        AdminProfileAccessMarkerMatrix matrix = new AdminProfileAccessMarkerMatrix();
        matrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, matrix);
    }

    /**
     * Тестирование добавления нового профиля администрирования в интерфейсе технолога
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить профиль администрирования existingAdminProfile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на страницу "Профили администрирования" в разделе "Настройка системы"</li>
     * <li>Нажать на кнопку "Добавить профиль администрирования"</li>
     * <li>Задать Название = Название профиля администрирования</li>
     * <li>Перейти в поле "Код"</li>
     * <li>Проверить что поле "Код" автоматически заполнилось значением "NazvanieKomplekta"</li>
     * <li>Задать код "кириллица", и нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что над полем "Код" появилось сообщение от валидатора:<br>
     * "Поле "Код" должно содержать не менее одного символа, но не более 255,
     * состоять только из символов латинского алфавита и цифр."</li>
     * <li>Задать код special%chars, и нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что над полем "Код" появилось сообщение от валидатора:<br>
     * "Поле "Код" должно содержать не менее одного символа, но не более 255,
     * состоять только из символов латинского алфавита и цифр."</li>
     * <li>Задать код, совпадающий с кодом профиля администрирования existingAdminProfile, и нажать на кнопку
     * "Сохранить"</li>
     * <li>Проверить, на форме появилось сообщение об ошибке:<br>
     * "Профиль администрирования с кодом "%код existingAdminProfile%" не может быть добавлен.
     * Код профиля администрирования должен быть уникальным."</li>
     * <li>Заполнить форму добавление корректными значениями и нажать на кнопку "Сохранить"</li>
     * <li>Перейти на карточку созданного профиля администрирования</li>
     * <li>Проверить, что значения полей на карточке профиля администрирования соответствуют заполненным при
     * создании</li>
     * </ol>
     */
    @Test
    public void testAddAdminProfile()
    {
        // Подготовка
        AdminProfile existingAdminProfile = DAOAdminProfile.createAdminProfile();
        AdminProfile newAdminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(existingAdminProfile);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().toolPanel().clickAdd();
        GUIAdminProfile.setTitle("Название профиля администрирования");
        GUIAdminProfile.clickCodeField();
        GUIAdminProfile.assertCodeOnForm("NazvanieProfilyaAdministrirovaniya");
        GUIAdminProfile.setCode("кириллица");
        GUIForm.clickApply();
        GUIValidation.assertValidation("code", ErrorMessages.CODE_ERROR_NEW);
        GUIAdminProfile.setCode("special%chars");
        GUIForm.clickApply();
        GUIValidation.assertValidation("code", ErrorMessages.CODE_ERROR_NEW);
        GUIAdminProfile.setCode(existingAdminProfile.getCode());
        GUIForm.applyFormAssertError(
                String.format(
                        "Профиль администрирования с кодом \"%s\" не может быть добавлен. Код профиля "
                        + "администрирования должен быть уникальным.",
                        existingAdminProfile.getCode()));

        GUIAdminProfile.fillAddForm(newAdminProfile);
        GUIForm.applyModalForm();
        newAdminProfile.setExists(true);
        GUIAdminProfile.goToAdminProfileCard(newAdminProfile);
        GUIAdminProfile.assertThatCard(newAdminProfile);
    }

    /**
     * Тестирование редактирования профиля администрирования в интерфейсе технолога
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить профиль администрирования newAdminProfiles</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на страницу "Профили администрирования" в разделе "Настройка системы"</li>
     * <li>В списке профилей администрирования нажать кнопку "Редактировать" в строке с профилем администрирования
     * newAdminProfiles</li>
     * <li>Задать Название = "Название профиля администрирования"</li>
     * <li>Нажать на кнопку "Сохранить"</li>
     * <li>Перейти на карточку созданного профиля администрирования</li>
     * <li>Проверить, что наименование профиля администрирования изменилось на "Название профиля администрирования"</li>
     * </ol>
     */
    @Test
    public void testEditAdminProfiles()
    {
        // Подготовка
        String newTitle = "Название профиля администрирования";
        AdminProfile newAdminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(newAdminProfile);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().clickPict(newAdminProfile, "editAdminProfile");
        GUIAdminProfile.setTitle(newTitle);
        GUIForm.applyForm();
        GUIAdminProfile.goToAdminProfileCard(newAdminProfile);
        GUIAdminProfile.assertTitleOnAdminProfileCard(newTitle);
    }

    /**
     * Тестирование отображения настроек администрирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить комплект settingsSet и связать его с adminProfile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку профиля администрирования adminProfile</li>
     * <li>Проверить, что в матрице настроек администрирования отображается комплект settingsSet</li>
     * <li>Перейти на карточку комплекта settingsSet</li>
     * <li>Проверить, что профиль администрирования adminProfile присутствует на карточке</li>
     * <li>Отредактировать settingsSet убрав adminProfile из "Профили администрирования"</li>
     * <li>Проверить, что профиль администрирования adminProfile отсутствует на карточке</li>
     * <li>Перейти на карточку профиля администрирования adminProfile</li>
     * <li>Проверить, что в матрице настроек администрирования не отображается комплект settingsSet</li>
     * </ol>
     */
    @Test
    public void testAdminSettings()
    {
        SettingsSet settingsSet = DAOSettingsSet.createSettingsSet();
        settingsSet.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(settingsSet);
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(true, settingsSet);
        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfile.getCode());
        GUISettingsSet.editFromCard();
        GUISelect.selectById(InputComplex.ANY_VALUE, adminProfile.getCode(),
                GUISettingsSet.ADMIN_PROFILES_ATTRIBUTE.getCode());
        GUIForm.applyForm();
        GUISettingsSet.assertAbsenceAdminProfile(adminProfile.getCode());
        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(false, settingsSet);
    }

    /**
     * Тестирование загрузки профиля администрирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить комплект settingsSet с профилем администрирования adminProfile</li>
     * <li>Добавить комплект settingsSetForDelete с профилем администрирования adminProfile</li>
     * <li>Добавить профиль администрирования adminProfileNew</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Выгрузить частичную матинформацию metaInfo по профилю adminProfile</li>
     * <li>В комплекте settingsSet добавить профиль администрирования adminProfileNew</li>
     * <li>Удалить комплект settingsSetForDelete</li>
     * <li>Удалить профиль администрирования adminProfile</li>
     * <li>Проверить, что на карточке комплекта settingsSet отсутствует профиль администрирования adminProfile</li>
     * <li>Проверить, что на карточке комплекта settingsSet присутствует профиль администрирования adminProfileNew</li>
     * <li>Проверить, что adminProfile отсутсвтует в списке профилей администрирования</li>
     * <li>Загрузить частичную метаифнормацию metaInfo</li>
     * <li>Перейти на карточку профиля администрирования adminProfile</li>
     * <li>Проверить, что на карточке профиля администрирования присутствует комлпект settingsSet</li>
     * <li>Проверить, что на карточке профиля администрирования отсутствует комлпект settingsSetForDelete</li>
     * <li>Перейти на карточку комплекта settingsSet</li>
     * <li>Провреить, что профили администрирования adminProfile и adminProfileNew присутствуют</li>
     * </ol>
     */
    @Test
    public void testImportAdminProfile()
    {
        // Подготовка
        SettingsSet settingsSet = DAOSettingsSet.createSettingsSet();
        SettingsSet settingsSetForDelete = DAOSettingsSet.createSettingsSet();
        settingsSetForDelete.setAdminProfiles(adminProfile);
        settingsSet.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(settingsSet, settingsSetForDelete);
        AdminProfile adminProfileNew = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfileNew);
        Cleaner.afterTest(() -> DSLAdminProfile.delete(adminProfileNew));

        // Выполнение действий и проверки
        File metaInfo = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.ALL_SETTINGS,
                        MetainfoElementIdBuilder.SYSTEM_SETTINGS,
                        MetainfoElementIdBuilder.ADMIN_PROFILES,
                        MetainfoElementIdBuilder.ADMIN_PROFILES + "$" + adminProfile.getCode() }));
        settingsSet.setAdminProfiles(adminProfileNew);
        DSLSettingsSet.edit(settingsSet);
        DSLSettingsSet.delete(settingsSetForDelete);
        DSLAdminProfile.delete(adminProfile);
        adminProfile.setExists(true);

        GUILogon.asSuper();
        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUISettingsSet.assertAbsenceAdminProfile(adminProfile.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfileNew.getCode());
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().rowsAbsence(adminProfile);

        DSLMetainfoTransfer.importMetainfo(metaInfo);

        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(true, settingsSet);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(false, settingsSetForDelete);
        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfile.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfileNew.getCode());
    }

    /**
     * Тестирование загрузки профиля администрирования связанного с комплектом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить комплект settingsSet с профилем администрирования adminProfile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Выгрузить полную матинформацию metaInfo</li>
     * <li>Добавить комплект settingsSetBreak с профилем администрирования adminProfile</li>
     * <li>Добавить профиль администрирования adminProfileBreak</li>
     * <li>Отредактировать комплект settingsSet, поменять профиль администрирования на adminProfileBreak</li>
     * <li>Проверить на карточке объекта settingsSet наличие adminProfileBreak и отсутствие adminProfile</li>
     * <li>Проверить на карточке объекта settingsSetBreak наличие adminProfile</li>
     * <li>Проверить на карточке объекта adminProfile наличие settingsSetBreak и отсутствие settingsSet</li>
     * <li>Проверить на карточке объекта adminProfileBreak наличие settingsSet</li>
     * <li>Загрузить метаинформацию metaInfo</li>
     * <li>Проверить на карточке объекта settingsSet наличие adminProfile и отсутствие adminProfileBreak</li>
     * <li>Проверить на карточке объекта settingsSetBreak отсутствие adminProfile</li>
     * <li>Проверить на карточке объекта adminProfile наличие settingsSet и отсутствие settingsSetBreak</li>
     * <li>Проверить на карточке объекта adminProfileBreak отсутствие settingsSet</li>
     * </ol>
     */
    @Test
    public void testImportAdminProfileWithSettingSet()
    {
        // Подготовка
        SettingsSet settingsSet = DAOSettingsSet.createSettingsSet();
        settingsSet.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(settingsSet);

        // Выполнение действий и проверки
        File metaInfo = DSLMetainfoTransfer.exportMetainfo();

        SettingsSet settingsSetBreak = DAOSettingsSet.createSettingsSet();
        settingsSetBreak.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(settingsSetBreak);
        AdminProfile adminProfileBreak = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfileBreak);
        settingsSet.setAdminProfiles(adminProfileBreak);
        DSLSettingsSet.edit(settingsSet);

        GUILogon.asSuper();

        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUISettingsSet.assertAbsenceAdminProfile(adminProfile.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfileBreak.getCode());

        GUISettingsSet.goToSetCard(settingsSetBreak.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfile.getCode());

        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(false, settingsSet);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(true, settingsSetBreak);

        GUIAdminProfile.goToAdminProfileCard(adminProfileBreak);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(true, settingsSet);

        DSLMetainfoTransfer.importMetainfo(metaInfo);

        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUISettingsSet.assertPresentAdminProfile(adminProfile.getCode());
        GUISettingsSet.assertAbsenceAdminProfile(adminProfileBreak.getCode());

        GUISettingsSet.goToSetCard(settingsSetBreak.getCode());
        GUISettingsSet.assertAbsenceAdminProfile(adminProfile.getCode());

        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(true, settingsSet);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(false, settingsSetBreak);

        GUIAdminProfile.goToAdminProfileCard(adminProfileBreak);
        GUIAdminProfile.assertExistSetInMatrixAdminSetting(false, settingsSet);
    }

    /**
     * Тестирование добавления записи в лог действий технолога при создании профиля администрирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Добавить профиль с названием и кодом = adminProfile1</li>
     * <b>Проверить,</b> что в логе действий технолога первым указано действие Изменение настроек профилей
     * администрирования с описанием "Добавлен профиль администрирования 'adminProfile1' (adminProfile1)."
     * </ol>
     */
    @Test
    public void testLogAddAdminProfile()
    {
        // Выполнение действий и проверок
        AdminProfile adminProfile1 = DAOAdminProfile.createAdminProfile("adminProfile1", "adminProfile1");
        DSLAdminProfile.add(adminProfile1);

        LogEntry logEntry = DSLAdminLog.getLastLogEntry();
        Assert.assertEquals(CategoryTitle.CHANGE_ADMIN_PROFILE, logEntry.getActionType());
        Assert.assertEquals(String.format("Добавлен профиль администрирования '%s' (%s).", adminProfile1.getTitle(),
                adminProfile1.getCode()), logEntry.getDescription());
    }

    /**
     * Тестирование добавления записи в лог действий технолога при изменении профиля администрирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Изменить название профиля adminProfile на editedAdminProfile</li>
     * <b>Проверить,</b> что в логе действий технолога первым указано действие Изменение настроек профилей
     * администрирования с описанием "Изменен профиль администрирования 'editedAdminProfile' (adminProfile):<br>
     * Название: 'adminProfile' -> 'editedAdminProfile'."
     * </ol>
     */
    @Test
    public void testLogEditAdminProfile()
    {
        // Выполнение действий и проверок
        String oldTitle = adminProfile.getTitle();
        adminProfile.setTitle("editedAdminProfile");
        DSLAdminProfile.edit(adminProfile);
        Cleaner.afterTest(true, () ->
        {
            adminProfile.setTitle(oldTitle);
            DSLAdminProfile.edit(adminProfile);
        });

        LogEntry logEntry = DSLAdminLog.getLastLogEntry();
        Assert.assertEquals(CategoryTitle.CHANGE_ADMIN_PROFILE, logEntry.getActionType());
        Assert.assertEquals(String.format("Изменен профиль администрирования '%s' (%s):\nНазвание: '%s' -> '%s'.",
                        adminProfile.getTitle(), adminProfile.getCode(), oldTitle, adminProfile.getTitle()),
                logEntry.getDescription());
    }

    /**
     * Тестирование добавления записи в лог действий технолога при изменении матрицы настроек администрирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под суперпользователем naumen.</li>
     * <li>Создать комплект set с выбранным профилем adminProfile (Настройка системы - Комплекты)</li>
     * <li>Перейти на карточку профиля adminProfile</li>
     * <li>В матрице настроек администрирования профиля adminProfile изменить значения параметра Редактирование для
     * комплекта set: true</li>
     * <b>Проверить,</b> что в логе действий технолога первым указано действие Изменение настроек профилей
     * администрирования с описанием "Изменен профиль администрирования 'editedAdminProfile' (adminProfile):<br>
     * В профиле администрирования 'editedAdminProfile' (adminProfile) изменена настройка прав."
     * </ol>
     */
    @Test
    public void testLogChangeMatrixSettingsAdminProfile()
    {
        // Выполнение действий и проверок
        SettingsSet set = DAOSettingsSet.createSettingsSet();
        set.setAdminProfiles(adminProfile);
        DSLSettingsSet.add(set);

        GUILogon.asSuper();
        GUIAdminProfile.goToAdminProfileCard(adminProfile);
        GUIAdminProfile.clickEditingInMatrixSettings(set);
        GUIAdminProfile.assertEditingInMatrixSettings(true, set);
        GUIAdminProfile.clickSaveMatrixSettings();

        // Добавлено для стабильности, после клика нужно время для добавления записи в лог
        boolean isContinue;
        long startTime = System.currentTimeMillis();
        LogEntry logEntry;
        String expectedDescription = String.format("Изменен профиль администрирования '%s' (%s):\nВ профиле "
                                                   + "администрирования '%s' (%s) изменена настройка прав.",
                adminProfile.getTitle(), adminProfile.getCode(), adminProfile.getTitle(), adminProfile.getCode());
        do
        {
            logEntry = DSLAdminLog.getLastByCategoryCode(CategoryCode.CHANGE_ADMIN_PROFILE);
            isContinue = !logEntry.getDescription().equals(expectedDescription);
        }
        while (isContinue && System.currentTimeMillis() - startTime < WAIT_TIME * 1000);

        Assert.assertEquals(CategoryTitle.CHANGE_ADMIN_PROFILE, logEntry.getActionType());
        Assert.assertEquals(expectedDescription, logEntry.getDescription());
    }

    /**
     * Тестирование добавления записи в лог действий технолога при удалении профиля администрирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Удалить профиль adminProfile</li>
     * <b>Проверить,</b> что в логе действий технолога первым указано действие Изменение настроек профилей
     * администрирования с описанием "Изменен профиль администрирования 'editedAdminProfile' (adminProfile):<br>
     * Название: 'adminProfile' -> 'editedAdminProfile'."
     * </ol>
     */
    @Test
    public void testLogDeleteAdminProfile()
    {
        // Выполнение действий и проверок
        DSLAdminProfile.delete(adminProfile);
        Cleaner.afterTest(true, () -> DSLAdminProfile.add(adminProfile));

        LogEntry logEntry = DSLAdminLog.getLastLogEntry();
        Assert.assertEquals(CategoryTitle.CHANGE_ADMIN_PROFILE, logEntry.getActionType());
        Assert.assertEquals(String.format("Удален профиль администрирования '%s' (%s).",
                adminProfile.getTitle(), adminProfile.getCode()), logEntry.getDescription());
    }

    /**
     * Тестирование локализации форм действий с профилями администрирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$241648512
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выбрать пользователю naumen язык интерфейса - English</li>
     * <li>Создать суперпользователя superUser, связать его с профилем администрирования adminProfile</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под суперпользователем naumen</li>
     * <li>Открыть список профилей администрирования (System settings - Administration profiles)</li>
     * <li>Нажать на кнопку Add a profile</li>
     * <b>Проверить,</b> что в шапке формы написано Adding an administration profile, названия полей формы (сверху
     * вниз) - Title, Code, Description
     * <li>Нажать Cancel</li>
     * <li>У профиля adminProfile в строке списка нажать на иконку карандаша (Edit)</li>
     * <b>Проверить,</b> что в шапке формы написано Editing the administration profile
     * <li>Нажать Cancel</li>
     * <li>У комплекта adminProfile в строке списка нажать на иконку крестика (Delete)</li>
     * <b>Проверить,</b> что в шапке появившегося сообщения написано Confirm delete, в теле сообщения написано Do you
     * really want to delete the administration profile?
     * <li>Нажать No</li>
     * <li>Выделить чекбокс массового выбора объектов</li>
     * <li>Нажать на "delete" на панели массовых операций</li>
     * <b>Проверить,</b> что в шапке появившегося сообщения написано Confirm delete, в теле сообщения написано Do you
     * really want to delete the selected administration profiles?
     * <li>Нажать Yes</li>
     * <b>Проверить,</b> что появилась ошибка An error occurred! Unable to delete administration profile with code
     * "adminProfile", as it is the only administration profile linked to the superuser(s): superUser.
     * </ol>
     */
    @Test
    public void testLocalisationFormAdminProfile()
    {
        // Подготовка
        DSLInterface.editSystemLanguage(DSLInterface.EN_LANGUAGE);
        SuperUser superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        // Выполнение действий и проверок
        GUILogon.asSuper();
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().toolPanel().clickAdd();
        GUIForm.assertDialogCaption("Adding an administration profile");
        GUIForm.assertAttributeCaption(DAOAttribute.createPseudo("Title", "title"),
                DAOAttribute.createPseudo("Code", "code"), DAOAttribute.createPseudo("Description", "description"));
        GUIForm.cancelDialog();

        GUIAdminProfile.advlist().content().clickPict(adminProfile, "editAdminProfile");
        GUIForm.assertDialogCaption("Editing the administration profile");
        GUIForm.cancelDialog();

        GUIAdminProfile.advlist().content().clickPict(adminProfile, "deleteAdminProfile");
        GUIForm.assertQuestionDialogTitle("Confirm delete");
        GUIForm.assertQuestion("Do you really want to delete the administration profile?");
        GUIForm.clickNo();

        GUIAdminProfile.advlist().mass().selectElements(adminProfile);
        GUIAdminProfile.advlist().mass().clickOperation(MassOperation.DELETE);
        GUIForm.assertQuestionDialogTitle("Confirm delete");
        GUIForm.assertQuestion("Do you really want to delete the selected administration profiles?");
        GUIForm.clickYes();
        Assert.assertEquals("Заголовок сообщения об ошибке не совпал с ожидаемым.", "An error occurred!",
                tester.findDisplayedWithoutErrorCheck(GUIXpath.Div.DIALOG_WIDGET_HEAD).getText());
        GUIError.assertDialogError(String.format("Unable to delete the administration profile "
                                                 + "with code \"%s\" as it is the only administration profile linked "
                                                 + "to the superuser(s): %s.",
                adminProfile.getCode(), superUser.getLogin()));
    }

    /**
     * Тестирование присутствия на стенде дефолтного профиля администрирования "Администратор веб-интерфейса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305663622
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИА под суперпользователем naumen</li>
     * <li>Открыть список профилей администрирования (System settings - Administration profiles)</li>
     * <li>Проверить, что в списке присутствует профиль "Администратор веб-интерфейса"</li>
     * <li>Перейти на карточку профиля "Администратор веб-интерфейса"</li>
     * <li>Проверить, что код и название профиля соответствуют ожидаемым</li>
     * </ol>
     */
    @Test
    public void testWebInterfaceAdminProfilePresence()
    {
        AdminProfile webInterfaceAdminProfile = ModelFactory.create(AdminProfile.class);
        webInterfaceAdminProfile.setCode("webInterface_Administrator");
        webInterfaceAdminProfile.setTitle("Администратор веб-интерфейса");

        //Выполнение действий и проверок
        GUILogon.asSuper();
        GUINavigational.goToAdminProfiles();
        GUIAdminProfile.advlist().content().asserts().rowsPresence(webInterfaceAdminProfile);
        GUIAdminProfile.goToAdminProfileCard(webInterfaceAdminProfile);
        GUIAdminProfile.assertCodeOnAdminProfileCard(webInterfaceAdminProfile.getCode());
        GUIAdminProfile.assertTitleOnAdminProfileCard(webInterfaceAdminProfile.getTitle());
    }

    /**
     * Тестирование редактирования суперпользователя другим суперпользователем ассоциированным с сотрудником
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01012
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01014
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$305742203
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профиль администрирования adminProfile1 со следующими правами: "Интерфейс
     * администратора" - "Доступ", "Список суперпользователей" - "Доступ", "Интерфейс оператора" - "Доступ",
     * "Управление привязкой профилей администрирования" - "Доступ", "Профили администрирования" - "Доступ"</li>
     * <li>Создать профиль администрирования adminProfile2 с правом доступа в Интерфейс оператора</li>
     * <li>Создать сотрудника employee</li>
     * <li>Создать суперпользователя superUser1, связать его с сотрудником employee, выдать ему профиль
     * администрирования adminProfile1</li>
     * <li>Создать суперпользователя superUser2 с доступом на полное замещение настроек при загрузке метаинформации</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником employee, связанным с суперпользрвателем superUser1</li>
     * <li>Перейти в интерфейс администратора (вкладка аминистрирование)</li>
     * <li>Проверить, что у суперпользователя superUser1 отсутствует кнопка редактирования, он не может
     * отредактировать сам себя</li>
     * <li>Проверить, что у суперпользователя superUser2 присутствует кнопка редактирования</li>
     * <li>Открыть форму редактирования суперпользователя superUser2</li>
     * <li>В выпадающем списке выбрать профиль администрирования adminProfile2</li>
     * <li>Сохранить форму, проверить, что ошибок не возникло</li>
     * <li>Открыть форму редактирования суперпользователя superUser2</li>
     * <li>Проверить, что поле "Профили аднимистрирования" заполнено значением adminProfile2</li>
     * <li>Проверить, что у суперпользователя superUser2 сохранилось право на полное замещение настроек при загрузке
     * метаинформации</li>
     * </ol>
     */
    @Test
    public void testEditSuperUserByAnotherSuperUserAssociatedWithEmployee()
    {
        // Подготовка
        AdminProfile adminProfile1 = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile1);

        AdminProfileAccessMarkerMatrix accessMarkerMatrix1 = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix1.addAdministrationInterfacePermission();
        accessMarkerMatrix1.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        accessMarkerMatrix1.addAccessMarkerPermission(SUPER_USERS, ALL);
        accessMarkerMatrix1.addAccessMarkerPermission(OPERATOR_INTERFACE, ALL);
        accessMarkerMatrix1.addAccessMarkerPermission(ADMINISTRATION_PROFILES_MANAGEMENT, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile1, accessMarkerMatrix1);

        AdminProfile adminProfile2 = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile2);
        AdminProfileAccessMarkerMatrix accessMarkerMatrix2 = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix2.addAccessMarkerPermission(OPERATOR_INTERFACE, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile2, accessMarkerMatrix2);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
        SuperUser superUser1 = DAOSuperUser.create();
        superUser1.setEmployee(employee);
        superUser1.setAdminProfiles(adminProfile1);

        SuperUser superUser2 = DAOSuperUser.create();
        superUser2.setFullReloadMetaInfoEnabled(true);
        DSLSuperUser.add(superUser1, superUser2);

        // Выполнение действий и проверок
        GUILogon.login(employee);
        GUINavigational.goToAdministration();

        String editSuperUser1ButtonXpath = EDIT_SUPER_USER_BUTTON.formatted(superUser1.getUuid());
        String editSuperUser2ButtonXpath = EDIT_SUPER_USER_BUTTON.formatted(superUser2.getUuid());

        GUIAdmin.assertAbsenceButtonOnXpath(editSuperUser1ButtonXpath,
                "Кнопка редактирования суперпользователя присутствует в списке");
        GUIAdmin.assertPresentButtonOnXpath(editSuperUser2ButtonXpath,
                "Кнопка редактирования суперпользователя отсутствует в списке");

        GUIAdmin.openEditSuperUserForm(superUser2);
        GUISelect.selectByTitle(Any.ADMIN_PROFILES, adminProfile2.getTitle());
        GUIForm.applyForm();

        GUIAdmin.openEditSuperUserForm(superUser2);
        GUISelect.assertDefaultItemPrecense(Any.ADMIN_PROFILES, adminProfile2.getCode(), true);

        Map<String, String> actualSuperUser = DSLSuperUser.getSuperUserByLoginWithFullReloadMetaInfoProperty(
                superUser2.getLogin());
        Assert.assertTrue(Boolean.parseBoolean(actualSuperUser.get("fullReloadMetaInfoEnabled")));
    }
}