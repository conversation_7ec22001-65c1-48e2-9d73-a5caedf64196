package ru.naumen.selenium.cases.admin.sets;

import static ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab.PERMISSIONSETTINGS;

import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLBreadCrumb;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.admin.DSLWfProfile;
import ru.naumen.selenium.casesutil.admin.GUIFolder;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.admin.GUIWfProfile;
import ru.naumen.selenium.casesutil.admin.homepage.DSLHomePage;
import ru.naumen.selenium.casesutil.admin.homepage.GUINavHomePage;
import ru.naumen.selenium.casesutil.advimport.DSLAdvimport;
import ru.naumen.selenium.casesutil.advimport.GUIAdvimportList;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUICustomForm;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.contenttemplate.DSLContentTemplate;
import ru.naumen.selenium.casesutil.contenttemplate.GUIContentTemplate;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.jmsqueue.DAOJMSQueue;
import ru.naumen.selenium.casesutil.jmsqueue.GUIJMSQueueList;
import ru.naumen.selenium.casesutil.listtemplate.DSLListTemplate;
import ru.naumen.selenium.casesutil.listtemplate.GUIListTemplate;
import ru.naumen.selenium.casesutil.metaclass.DSLActionCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.DSLTransition;
import ru.naumen.selenium.casesutil.metaclass.GUIActionCondition;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationForm;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIStatusAction;
import ru.naumen.selenium.casesutil.metaclass.GUITransition;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileAddForm;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.mobile.GUIMobileEditForm;
import ru.naumen.selenium.casesutil.mobile.GUIMobileList;
import ru.naumen.selenium.casesutil.mobile.contents.GUIMobileContents;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.DAOHomePage;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.DAOWfProfile;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.admin.HomePage;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.WfProfile;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConfig;
import ru.naumen.selenium.casesutil.model.advimport.DAOAdvimport;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm.CommentOnFormProperty;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentTools;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.ToolBar;
import ru.naumen.selenium.casesutil.model.content.ToolPanel;
import ru.naumen.selenium.casesutil.model.contenttemplate.ContentTemplate;
import ru.naumen.selenium.casesutil.model.contenttemplate.DAOContentTemplate;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.jmsqueue.DSLJmsQueue;
import ru.naumen.selenium.casesutil.model.jmsqueue.JMSQueue;
import ru.naumen.selenium.casesutil.model.listtemplate.DAOListTemplate;
import ru.naumen.selenium.casesutil.model.listtemplate.ListTemplate;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOStatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.ActionType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.MobileEditForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileList;
import ru.naumen.selenium.casesutil.model.mobile.MobilePropertiesList;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.report.DAOReportTemplate;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection.MailProtocol;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.Periods;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.Strategy;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.styletemplate.DAOStyleTemplate;
import ru.naumen.selenium.casesutil.model.styletemplate.StyleTemplate;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition.TimeMetric;
import ru.naumen.selenium.casesutil.report.DSLReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportTemplate;
import ru.naumen.selenium.casesutil.role.GUISecurityRoleList;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.schedulertask.GUISchedulerTaskList;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.script.GUIScriptModule;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.secgroup.GUISecurityGroupList;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.casesutil.styletemplate.DSLStyleTemplate;
import ru.naumen.selenium.casesutil.styletemplate.GUIStyleTemplate;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.tag.GUITag;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.timer.GUITimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.Transition;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.MetaInfoXml;

/**
 * Тестирование комплектов
 * <AUTHOR>
 * @since 23.12.2024
 */
class Sets1Test extends AbstractTestCaseJ5
{
    private static SettingsSet settingsSet;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить функционал профилей администрирования (customAdminProfiles = true)</li>
     * <li>Выполнить скрипт включения функционала комплектов
     * <br>beanFactory.getBean('settingsSetStorageServiceConfiguration').setSettingSetsEnabled(true)</li>
     * <li>Добавить комплект setCase</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);
        settingsSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet);
    }

    /**
     * Тестирование удаления комплекта из настройки метакласса и вложенных для метакласса настроек
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass с ЖЦ и разметить комплектом setCase.</li>
     * <li>В классе userClass создать атрибут boolAttr типа логический, разметить комплектом setCase.</li>
     * <li>В классе userClass разметить комплектом setCase группу Системных атрибутов.</li>
     * <li>В классе userClass на вкладке Жизненный цикл добавить статус userState, разметить комплектом setCase.</li>
     * <li>Перейти на карточку статуса userState добавить действие при входе в статус:
     * <ul>
     *     <li>Название: userAction1</li>
     *     <li>Скрипт: [новый скрипт]
     *     <ul>
     *         <li>Название скрипта: userActionScript</li>
     *         <li>Скрипт: return true</li>
     *     </ul>
     *     </li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить действие при выходе из статуса:
     * <ul>
     *     <li>Название: userAction2</li>
     *     <li>Скрипт: userActionScript</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить условие при входе из статус:
     * <ul>
     *     <li>Название: userCondition1</li>
     *     <li>Скрипт: [новый скрипт]
     *     <ul>
     *         <li>Название скрипта: userConditionScript</li>
     *         <li>Скрипт: return true</li>
     *     </ul>
     *     </li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить условие при выходе из статуса:
     * <ul>
     *     <li>Название: userCondition2</li>
     *     <li>Скрипт: userConditionScript</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Вернуться на вкладку Жизненный цикл класса userClass</li>
     * <li>Настроить переход из Зарегистрирован в userState:
     * <ul>
     *     <li>Название кнопки перехода: userButton</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Сохранить матрицу переходов между статусами.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку класса userClass.</li>
     * <li>Проверить, что у класса userClass в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования атрибута boolAttr.</li>
     * <li>Проверить, что у атрибута boolAttr в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на вкладку Группы атрибутов и открыть форму редактирования Системной группы атрибутов.</li>
     * <li>Проверить, что у Системной группы атрибутов в свойстве "Комплект" комплект с названием setCase отсутствует
     * .</li>
     * <li>Перейти на вкладку Жизненный цикл и открыть карточку статуса userState.</li>
     * <li>Проверить, что у статуса userState в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку перехода userButton.</li>
     * <li>Проверить, что у перехода userButton в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку статуса userState и открыть форму редактирования Действия при входе в статус
     * userAction1.</li>
     * <li>Проверить, что у действия userAction1 в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования Действия при выходе в статуса userAction2.</li>
     * <li>Проверить, что у действия userAction2 в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования Условия при входе в статус userCondition1.</li>
     * <li>Проверить, что у действия userCondition1 в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования Условия при выходе в статуса userCondition2</li>
     * <li>Проверить, что у действия userCondition2 в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию с настройками класса userClass.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromMetaClassSettings()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setSettingsSet(settingsSet);
        DSLMetaClass.add(userClass);

        Attribute boolAttr = DAOAttribute.createBool(userClass);
        boolAttr.setSettingsSet(settingsSet);
        DSLAttribute.add(boolAttr);

        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        groupAttr.setSettingsSet(settingsSet);
        DSLGroupAttr.edit(groupAttr, new Attribute[] {}, new Attribute[] {});

        BoStatus userState = DAOBoStatus.createUserStatus(userClass);
        userState.setSettingsSet(settingsSet);
        DSLBoStatus.add(userState);

        ScriptInfo userScript1 = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(userScript1);

        StatusAction userAction1 = DAOStatusAction.createPreAction(userState, userScript1.getCode(), settingsSet);
        DSLBoStatus.addScriptActionOrCondition(userAction1, userScript1);

        StatusAction userAction2 = DAOStatusAction.createPostAction(userState, userScript1.getCode(), settingsSet);
        DSLBoStatus.addScriptActionOrCondition(userAction2, userScript1);

        ScriptInfo userScript2 = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(userScript2);

        StatusAction userCondition1 = DAOStatusAction.createPreCondition(userState, userScript2.getCode(), settingsSet);
        DSLBoStatus.addScriptActionOrCondition(userCondition1, userScript2);

        StatusAction userCondition2 = DAOStatusAction.createPostCondition(userState, userScript2.getCode(),
                settingsSet);
        DSLBoStatus.addScriptActionOrCondition(userCondition2, userScript2);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        DSLBoStatus.setTransitions(registered, userState);
        Transition transition = DAOTransition.createTransition(userClass, registered, userState, "userButton", false,
                false);
        transition.setSettingsSet(settingsSet);
        DSLTransition.editTransitions(transition);

        // Действия и проверки:
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIMetaClass.goToCard(userClass);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTES);
        GUIAttribute.clickEdit(boolAttr);
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.clickCancel();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIGroupAttr.openEditForm(groupAttr);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.goToStatusCardWithRefresh(userState);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUITransition.goToCard(transition);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIBoStatus.goToStatusCardWithRefresh(userState);
        GUIStatusAction.clickEditIcon(userAction1);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        GUIBoStatus.goToStatusCardWithRefresh(userState);
        GUIStatusAction.clickEditIcon(userAction2);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        GUIBoStatus.goToStatusCardWithRefresh(userState);
        GUIStatusAction.clickEditIcon(userCondition1);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        GUIBoStatus.goToStatusCardWithRefresh(userState);
        GUIStatusAction.clickEditIcon(userCondition2);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectMetaClasses(exportModel, userClass);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настройки карточки объекта, настроек вложенных для карточки объекта и
     * настройки маркера прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass и тип userCase.</li>
     * <li>В классе userClass перейти на Карточку объекта и добавить вкладку userTab, разметить комплектом setCase.</li>
     * <li>На вкладке userTab добавить контент Список объектов objectList для класса userClass, разметить комплектом
     * setCase.</li>
     * <li>Открыть форму настройки действий в контенте Список объектов.</li>
     * <li>Снять чек-бокс Использовать системную логику формирования панели действий (отображаются только системные
     * кнопки).</li>
     * <li>Открыть форму редактирования элемента Добавить, разметить комплектом setCase.</li>
     * <li>В классе userClass на вкладке Другие формы добавить форму смены типа userChangeTypeForm, разметить
     * комплектом setCase.</li>
     * <li>В классе userClass на вкладке Другие формы добавить форму быстрого добавления и редактирования
     * userQuickForm, разметить комплектом setCase.</li>
     * <li>В классе userClass на вкладке Другие формы добавить форму массового редактирования userMassEditForm,
     * разметить комплектом setCase.</li>
     * <li>В классе userClass перейти на вкладку Права доступа.</li>
     * <li>В матрице прав добавить маркер прав userMarker, разметить комплектом setCase.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку класса userClass.</li>
     * <li>Перейти на Карточку объекта класса userClass и открыть форму редактирования вкладки userTab.</li>
     * <li>Проверить, что у вкладки userTab в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>На вкладке userTab открыть форму редактирования контента objectList.</li>
     * <li>Проверить, что у контента objectList в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования элемента Добавить.</li>
     * <li>Проверить, что у элемента Добавить в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на вкладку Другие формы и открыть форму редактирования смены типа userChangeTypeForm.</li>
     * <li>Проверить, что у формы userChangeTypeForm в свойстве "Комплект" комплект с названием setCase отсутствует
     * .</li>
     * <li>Открыть форму редактирования быстрого добавления и редактирования userQuickForm.</li>
     * <li>Проверить, что у формы userQuickForm в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования массового редактирования userMassEditForm.</li>
     * <li>Проверить, что у формы userMassEditForm в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на вкладку Права доступа класса userClass и открыть форму редактирования маркера прав userMarker
     * .</li>
     * <li>Проверить, что у маркера прав userMarker в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: userClass.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromObjectCardAndRightsSettings()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        userClass.setSettingsSet(settingsSet);
        DSLMetaClass.add(userClass, userCase);

        ContentTab contentTab = DAOContentTab.createTab(userClass, "userTab");
        DSLContent.addTab(contentTab);

        ContentForm objectList = DAOContentCard.createObjectList(userClass.getFqn(), userClass);
        ToolPanel toolPanel = DAOToolPanel.createContentToolPanel();
        toolPanel.setUseSystemSettings(false);

        ToolBar toolBar = new ToolBar();
        toolBar.addTools(ContentTools.ADD);
        toolPanel.addToolBars(toolBar);

        objectList.setToolPanel(toolPanel);
        objectList.setSettingsSet(settingsSet);
        DSLContent.add(contentTab, objectList);

        CustomForm changeTypeForm = DAOCustomForm.createChangeCaseForm(DAOGroupAttr.createSystem(userClass),
                CommentOnFormProperty.NOT_FILL, userCase);
        changeTypeForm.setSettingsSet(settingsSet);
        DSLCustomForm.add(changeTypeForm);

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        quickForm.setSettingsSet(settingsSet);
        DSLCustomForm.add(quickForm);

        CustomForm massForm = DAOCustomForm.createMassEditForm(DAOGroupAttr.createSystem(userClass), userCase);
        massForm.setSettingsSet(settingsSet);
        DSLCustomForm.add(massForm);

        SecurityMarker marker = new SecurityMarkerViewAttrs(userClass).addAttributes().apply();

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.editTab();
        GUITab.clickEdit(contentTab);
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.clickApply();
        GUIForm.closeDialog();

        GUIMetaClass.goToTab(userClass, PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.clickApply();

        GUIContent.goToContent(objectList);
        objectList.advlist().toolPanel().clickEditToolPanel();
        objectList.advlist().editableToolPanel().rightClickTool(GUIButtonBar.BTN_ADD);
        objectList.advlist().editableToolPanel().clickEditContextMenuOption();
        GUISettingsSet.fillSettingsSetOnForm(settingsSet.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.clickApply();

        // Действия и проверки
        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));
        tester.refresh();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.editTab();
        GUITab.clickEdit(contentTab);
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickApply();
        GUIForm.closeDialog();

        GUIContent.goToContent(objectList);
        objectList.advlist().toolPanel().clickEditToolPanel();
        objectList.advlist().editableToolPanel().rightClickTool(GUIButtonBar.BTN_ADD);
        objectList.advlist().editableToolPanel().clickEditContextMenuOption();
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.applyLastModalForm();
        GUIForm.clickApply();

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);
        GUICustomForm.editCustomFormByUuid(quickForm);
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickCancel();

        GUICustomForm.editCustomFormByUuid(changeTypeForm);
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickCancel();

        GUICustomForm.editCustomFormByUuid(massForm);
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickCancel();

        GUIMetaClass.goToTab(userClass, PERMISSIONSETTINGS);
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        MetainfoExportModel exportModel = DAOMetainfoExport.create();
        DAOMetainfoExport.selectMetaClasses(exportModel, userClass);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(exportModel);

        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек групп пользователя, роли и профиля
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass.</li>
     * <li>Создать группу пользователей userGroup, разметить комплектом setCase.</li>
     * <li>Добавить роль userRole, разметить комплектом setCase:
     * <ul>
     *     <li>Определить права доступа пользователя к объекту: true</li>
     *     <li>Скрипт: [новый скрипт]
     *     <ul>
     *         <li>Название скрипта: userRoleScript</li>
     *         <li>Текст: return true</li>
     *     </ul>
     *     </li>
     * </ul>
     * </li>
     * <li>В классе rClass перейти на вкладку Права доступа и в матрице прав добавить профиль userProfile, разметить
     * комплектом setCase.</li>
     * <li>Сохранить матрицу прав.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти в раздел Группы пользователей и роли на карточку userGroup.</li>
     * <li>Проверить, что у группы userGroup в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>В разделе Группы пользователей и роли перейти на карточку userRole на вкладке Роли.</li>
     * <li>Проверить, что у роли userRole в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на вкладку Права доступа класса userClass и открыть форму редактирования профиля userProfile.</li>
     * <li>Проверить, что у профиля userProfile в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: Группы пользователей, роли и профили.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromUserGroupAndRolesAndProfilesSettings()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        userGroup.setSettingsSet(settingsSet);
        DSLSecurityGroup.add(userGroup);

        ScriptInfo userScript = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(userScript);

        SecurityRole userRole = DAOSecurityRole.create(userClass, userScript, null, null);
        userRole.setSettingsSet(settingsSet);
        DSLSecurityRole.add(userRole);

        SecurityProfile userProfile = DAOSecurityProfile.create(false, null, SysRole.employee());
        userProfile.setSettingsSet(settingsSet);
        DSLSecurityProfile.add(userProfile);

        // Действия и проверки
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUISecurityGroupList.goToCard(userGroup);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUISecurityRoleList.goToCard(userRole);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIMetaClass.goToTab(userClass, PERMISSIONSETTINGS);
        GUIAccessMatrix.callEditForm(userProfile);
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.GROUPS_ROLES_PROFILES }));
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек справочника и элемента справочника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать справочник userCatalog, разметить комплектом setCase.</li>
     * <li>Создать группу пользователей userGroup, разметить комплектом setCase.</li>
     * <li>Добавить в справочник элемент userElem, разметить комплектом setCase.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку справочника userCatalog.</li>
     * <li>Проверить, что у справочника userCatalog в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку элемента справочника userElem.</li>
     * <li>Проверить, что у элемента справочника userElem в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: userCatalog.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromCatalogAndCatalogItemsSettings()
    {
        // Подготовка
        Catalog userCatalog = DAOCatalog.createUser(true, false);
        userCatalog.setSettingsSet(settingsSet);
        DSLCatalog.add(userCatalog);

        CatalogItem userItem = DAOCatalogItem.createUser(userCatalog);
        userItem.setSettingsSet(settingsSet);
        DSLCatalogItem.add(userItem);

        // Действия и проверки
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUICatalog.goToCard(userCatalog);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUICatalogItem.goToCard(userItem);
        GUISettingsSet.assertSettingsSetOnCards("");

        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { userCatalog.getCode() }));
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек профиля связанных ЖЦ, каталога и метки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать класс userClass.</li>
     * <li>Создать профиль связанных жизненных циклов wfProfile:
     * <ul>
     *     <li>Ведущий: Запрос</li>
     *     <li>Ведомый: Запрос</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Перейти в раздел Каталоги и добавить папку userCatalogFolder:
     * <ul>
     *     <li>Ведущий: userCatalogFolder</li>
     *     <li>Родитель: userClass</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Перейти в раздел Метки и добавить метку userMark, разметить комплектом setCase.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку профиля связанных жизненных циклов wfProfile.</li>
     * <li>Проверить, что у профиля wfProfile в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку каталога userCatalogFolder.</li>
     * <li>Проверить, что у каталога userCatalogFolder в свойстве "Комплект" комплект с названием setCase отсутствует
     * .</li>
     * <li>Перейти на карточку метки userMark.</li>
     * <li>Проверить, что у метки userMark в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: Профили связанных жизненных циклов, Каталоги, Метки.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromWFProfilesAndFoldersAndMarksSettings()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        MetaClass scClass = DAOScCase.createClass();
        WfProfile userWfProfile = DAOWfProfile.create(scClass, scClass, true);
        userWfProfile.setSettingsSet(settingsSet);
        DSLWfProfile.add(userWfProfile);

        Folder userFolder = DAOFolder.create(userClass);
        userFolder.setSettingsSet(settingsSet);
        DSLFolder.add(userFolder);

        Tag userTag = DAOTag.createTag();
        userTag.setSettingsSet(settingsSet);
        DSLTag.add(userTag);

        // Действия и проверки
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIWfProfile.goToCard(userWfProfile);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIFolder.goToCardWithRefresh(userFolder);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUITag.goToTagCard(userTag.getCode());
        GUISettingsSet.assertSettingsSetOnCards("");

        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.TAGS }, new String[] { MetainfoElementIdBuilder.FOLDERS },
                new String[] { MetainfoElementIdBuilder.WF_PROFILES }));
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек счетчика времени и эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать счетчик времени:
     * <ul>
     *     <li>Название: userCounter</li>
     *     <li>Объекты: Запрос</li>
     *     <li>Метрика времени: Запас времени обслуживания</li>
     *     <li>Тип условия: По скрипту</li>
     *     <li>Условие начала отсчета: [новый скрипт]
     *     <ul>
     *         <li>Название скрипта: startCondition</li>
     *         <li>Скрипт: return true</li>
     *         <li>Комплект: setCase</li>
     *     </ul>
     *     </li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить схему эскалации:
     * <ul>
     *     <li>Название: userEscalationScheme</li>
     *     <li>Объекты: Запрос</li>
     *     <li>Счетчик времени: userCounter</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить уровень эскалации:
     * <ul>
     *     <li>Условие: По истечении времени</li>
     *     <li>Значение: 10 секунд</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить элемент таблицы соответствий:
     * <ul>
     *     <li>Название: elemEscalation</li>
     *     <li>Объекты: Запрос</li>
     *     <li>Определяющий атрибуты: Категории запроса</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить строку таблицы соответствий:
     * <ul>
     *     <li>Схемы эскалации: userEscalationScheme</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку счетчика времени userCounter.</li>
     * <li>Проверить, что у счетчика userCounter в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку скрипта startCondition.</li>
     * <li>Проверить, что у скрипта startCondition в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку схемы эскалации userEscalationScheme.</li>
     * <li>Проверить, что у схемы эскалации userEscalationScheme в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Открыть форму редактирования уровня эскалации.</li>
     * <li>Проверить, что у уровня эскалации в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на элемент схемы соответствий эскалации elemEscalation.</li>
     * <li>Проверить, что у элемента elemEscalation в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования строки таблицы соответствий userEscalationScheme.</li>
     * <li>Проверить, что у строки userEscalationScheme в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: userCounter, Эскалация.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetDeletingFromTimersAndEscalationsSettings()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();

        ScriptInfo userScript = DAOScriptInfo.createNewScriptInfo("return true");
        userScript.setSettingsSet(settingsSet);
        DSLScriptInfo.addScript(userScript);

        TimerDefinition userTimer = DAOTimerDefinition.createScriptTimerForSc(TimeMetric.FLOAT);
        userTimer.setStartCondition(userScript.getCode());
        userTimer.setSettingsSet(settingsSet);
        DSLTimerDefinition.add(userTimer);

        EscalationScheme escalationScheme = DAOEscalationSheme.create(userTimer, false, scClass);
        escalationScheme.setSettingsSet(settingsSet);
        DSLEscalation.add(escalationScheme);

        EscalationLevel escLevel = DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "10 SECOND", true);
        escLevel.setSettingsSet(settingsSet);
        DSLEscalation.addLevel(escalationScheme.getCode(), escLevel);

        Attribute categoriesAttr = SysAttribute.categories(scClass);
        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(scClass, categoriesAttr.getCode());
        escalationRule.setSettingsSet(settingsSet);
        DSLCatalogItem.add(escalationRule);

        RsRow rowEsc = DAORsRow.create(escalationRule, GUIEscalation.ESCALATION_TARGET_DATA, escalationScheme.getCode(),
                SysAttribute.metaClass(scClass).getCode(), scClass.getFqn());
        DSLRsRows.addRowToRSItem(rowEsc);

        // Действия и проверки
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUITimerDefinition.goToCardWithRefresh(userTimer);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIScript.goToCard(userScript);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIEscalation.goToSchemeWithRefresh(escalationScheme);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIEscalation.clickEditButton();
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.clickCancel();

        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIRulesSettings.clickRSEditRow();
        GUISettingsSet.assertSettingsSetPropOnForm(null);

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.ESCALATION });
        DAOMetainfoExport.selectTimers(metainfoExportModel, userTimer);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек ДПС и очереди
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Перейти в Настройки системы -> Действия по событиями и добавить ДПС:
     * <ul>
     *     <li>Название: userEventAction</li>
     *     <li>Объекты: Компания</li>
     *     <li>Комплект: setCase</li>
     *     <li>Событие: Пользовательское событие</li>
     *     <li>Действие: Скрипт
     *     <ul>
     *         <li>Название скрипта: userEventActionScript</li>
     *         <li>Текст: return true</li>
     *         <li>Комплект: setCase</li>
     *     </ul>
     *     </li>
     * </ul>
     * </li>
     * <li>Добавить параметр:
     * <ul>
     *     <li>Название: boolAttr</li>
     *     <li>Код: boolAttr</li>
     *     <li>Тип значения: Логический</li>
     *     <li>Комплект: setCase</li>
     *     </li>
     * </ul>
     * </li>
     * <li>Добавить условие выполнения действия:
     * <ul>
     *     <li>Название: userCondition</li>
     *     <ul>
     *         <li>Название скрипта: conditionScript</li>
     *         <li>Текст: return true</li>
     *     </ul>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Перейти на вкладку Очереди в разделе Действия по событиям и добавить очередь:
     * <ul>
     *     <li>Название: userQueue</li>
     *     <li>Тип обрабатываемых действий: Скрипт</li>
     *     <li>Комплект: setCase</li>
     *     </li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку ДПС userEventAction.</li>
     * <li>Проверить, что у ДПС userEventAction в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку скрипта userEventActionScript.</li>
     * <li>Проверить, что у скрипта userEventActionScript в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку ДПС userEventAction и открыть форму редактирования параметра.</li>
     * <li>Проверить, что у параметра boolAttr в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть карточку условия выполнения действия userCondition.</li>
     * <li>Проверить, что у условия userCondition в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточки очереди userQueue во вкладке Очереди в разделе Действия по событиям.</li>
     * <li>Проверить, что у очереди userQueue в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: userEventAction, userQueue.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetsDeletingFromEventActionAndQuery()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();

        ScriptInfo userScript = DAOScriptInfo.createNewScriptInfo("return true");
        userScript.setSettingsSet(settingsSet);
        DSLScriptInfo.addScript(userScript);
        EventAction userEventAction = DAOEventAction.createEventScript(EventType.userEvent, userScript, true,
                rootClass);
        userEventAction.setSettingsSet(settingsSet);
        DSLEventAction.add(userEventAction);

        FormParameter boolParam = DAOFormParameter.createBool();
        boolParam.setEventAction(userEventAction.getUuid());
        boolParam.setSettingsSet(settingsSet);
        DSLFormParameter.save(boolParam);

        ScriptInfo userScript2 = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(userScript2);

        ActionCondition userCondition = DAOActionCondition.create(userEventAction, userScript2.getCode());
        userCondition.setSettingsSet(settingsSet);
        DSLActionCondition.add(userCondition);

        JMSQueue jmsQueue = DAOJMSQueue.create(ActionType.ScriptEventAction);
        DSLJmsQueue.addJMSQueue(jmsQueue);

        GUILogon.asSuper();
        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUIJMSQueueList.clickEdit();
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.applyForm();

        // Действия и проверки
        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIEventAction.goToCardWithRefresh(userEventAction);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIScript.goToCard(userScript);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIEventAction.goToCardWithRefresh(userEventAction);
        GUIEventAction.clickEditParameter(boolParam);
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.clickCancel();

        GUIActionCondition.goToCard(userCondition);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUISettingsSet.assertSettingsSetOnCards("");

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { String.format(MetainfoElementIdBuilder.USER_JMS_QUEUES_ID, jmsQueue.getCode()) });
        DAOMetainfoExport.selectActionSettings(metainfoExportModel, userEventAction);
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Компания на вкладке Права доступа создать профиль userProfile.</li>
     * <li>Перейти в Настройки системы -> Интерфейс и навигация -> вкладка Навигация.</li>
     * <li>Добавить элемент верхнего меню:
     * <ul>
     *     <li>Вид элемента: Раздел меню</li>
     *     <li>Название: topElem</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить элемент левого меню:
     * <ul>
     *     <li>Вид элемента: Раздел меню</li>
     *     <li>Название: leftElem</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить плитку панели быстрого доступа:
     * <ul>
     *     <li>Элемент левого меню: leftElem</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * <li>Добавить элемент навигационной цепочки:
     * <ul>
     *     <li>Объекты: Сотрудник</li>
     *     <li>Атрибуты связи: Отдел</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить элемент домашней страницы:
     * <ul>
     *     <li>Вид домашней страницы: Ссылка на карточку</li>
     *     <li>Название: userHomePage</li>
     *     <li>Профили: userProfile</li>
     *     <li>Вкладка карточки: Сотрудник (employee)</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку элемента topElem верхнего меню. </li>
     * <li>Проверить, что у элемента topElem в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку элемента leftElem левого меню. </li>
     * <li>Проверить, что у элемента leftElem в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования плитки leftElem. </li>
     * <li>Проверить, что у плитки leftElem в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования элемента навигационной цепочки.</li>
     * <li>Проверить, что у элемента навигационной цепочки в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку элемента userHomePage домашней страницы.</li>
     * <li>Проверить, что у элемента userHomePage в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: Навигация.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetsDeletingFromNavigationSettings()
    {
        // Подготовка
        SecurityProfile userProfile = DAOSecurityProfile.create(false, null, SysRole.employee());
        DSLSecurityProfile.add(userProfile);

        // Верхний элемент добавляется через GUI, из-за того, что при добавлении через DSL тест не проходит.
        // Подробнее в задаче NSDAT-32155 https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$294171255
        MenuItem topElem = DAOMenuItem.createChapter(false);
        GUILogon.asSuper();
        GUINavigational.goToNavigationSettings();
        List<String> oldIds = GUINavSettings.getElementsId();
        GUINavSettings.clickAddTopMenuElement();
        GUIMenuItem.selectMenuItemType(topElem.getType());
        GUIMenuItem.fillElementTitleField(topElem.getTitle());
        GUISettingsSet.fillSettingsSetOnForm(settingsSet.getCode());
        GUIForm.applyForm();
        List<String> newIds = GUINavSettings.getNewElementsId(oldIds);
        topElem.setCode(newIds.get(0));
        topElem.setExists(true);

        LeftMenuItem leftElem = DAOLeftMenuItem.createChapter(true, null);
        leftElem.setSettingsSet(settingsSet);
        DSLLeftMenuItem.add(leftElem);

        QuickAccessTile userTile = DAOQuickAccessTile.createQuickAccessTile(leftElem, true);
        userTile.setSettingsSet(settingsSet);
        DSLQuickAccessTile.add(userTile);

        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Crumb crumb = DAOBreadCrumb.create(employeeClass, SysAttribute.parent(employeeClass));
        crumb.setSettingsSet(settingsSet);
        DSLBreadCrumb.add(crumb);

        HomePage userHomePage = DAOHomePage.createReferenceToUser(employeeClass, null).setProfiles(userProfile);
        userHomePage.setSettingsSet(settingsSet);
        DSLHomePage.add(userHomePage);

        // Действия и проверки:
        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIMenuItem.goToTopMenuItemCard(topElem);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUINavSettings.goToCard();
        GUIMenuItem.goToLeftMenuItemCard(leftElem);
        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.clickCancel();

        GUINavSettings.goToCard();
        GUINavSettings.goToCrumb(crumb);
        GUIForm.openEditForm();
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickCancel();

        GUINavSettings.goToCard();
        GUIMenuItem.clickEditMenuItemQuickTile(leftElem);
        GUISettingsSet.assertSettingsSetOnForm(null);
        GUIForm.clickCancel();

        GUINavHomePage.goToHomePageCard(userHomePage);
        GUISettingsSet.assertSettingsSetOnCards("");

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.NAVIGATION });
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек модуля, планировщика задач, правила обработки почты, встроенного
     * приложения, конфигурации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить модуль (Настройки системы -> Каталог скриптов и модулей -> Каталог модулей):
     * <ul>
     *     <li>Код: userModule</li>
     *     <li>Текст модуля: def test() { return 5 }</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить задачу (Настройки системы ->Планировщик задач):
     * <ul>
     *     <li>Тип задачи: Скрипт</li>
     *     <li>Название: userScheduler</li>
     *     <ul>
     *         <li>Название скрипта: userSchedulerScript</li>
     *         <li>Текст: return true</li>
     *     </ul>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить правило к задаче userScheduler:
     * <ul>
     *     <li>Тип правила: Периодическое выполнение</li>
     *     <li>Период: ежедневно</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить правило (Настройки системы -> Почта -> Правила обработки):
     * <ul>
     *     <li>Название: userMailRule</li>
     *     <li>Скрипт: [новый скрипт]</li>
     *     <ul>
     *         <li>Название скрипта: userMailRuleScript</li>
     *         <li>Текст: return true</li>
     *     </ul>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить встроенное приложение (Настройки системы -> Приложения):
     * <ul>
     *     <li>Название: userApplication</li>
     *     <li>Тип приложения: Приложение, исполняемое на стороне клиента</li>
     *     <li>Файл приложения: Index.zip</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить конфигурацию (Настройки системы -> Синхронизация):
     * <ul>
     *     <li>Название: advConfig</li>
     *     <li>Заполнить необходимые поля на форме добавления</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Перейти на карточку комплекта setCase.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку модуля userModule.</li>
     * <li>Проверить, что у модуля userModule в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку задачи userScheduler.</li>
     * <li>Проверить, что у задачи userScheduler в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку правила задачи userScheduler.</li>
     * <li>Проверить, что у правила в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку правила обработки почты userMailRule.</li>
     * <li>Проверить, что у правила userMailRule в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку встроенного приложения userApplication.</li>
     * <li>Проверить, что у приложения userApplication в свойстве "Комплект" комплект с названием setCase отсутствует
     * .</li>
     * <li>Перейти на карточку конфигурации advConfig.</li>
     * <li>Проверить, что у конфигурации advConfig в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: userModule, userScheduler, userMailRule, userApplication, advConfig.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetsDeletingFromModuleSettingsAndSchedulersAndEmbeddedApplicationAndAdvimportConfig()
    {
        // Подготовка
        ModuleConf userModule = DAOModuleConf.createWithBody("def test() { return 5 }");
        userModule.setSettingsSet(settingsSet);
        DSLModuleConf.add(userModule);

        ScriptInfo userScript = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(userScript);
        SchedulerTaskScript userScheduler = DAOSchedulerTask.createScriptRule(userScript);
        userScheduler.setSettingsSet(settingsSet);
        DSLSchedulerTask.addTask(userScheduler);

        GregorianCalendar c = new GregorianCalendar();
        c.setTime(new Date());
        c.add(Calendar.HOUR_OF_DAY, -1);
        String pastDate = DateTimeUtils.formatTimeddMMyyyyHHmm(c.getTimeInMillis());
        Trigger trigger = DAOSchedulerTask.createPeriodTrigger(userScheduler, pastDate, Periods.DAILY,
                Strategy.FROM_START);
        trigger.setSettingsSet(settingsSet);
        DSLSchedulerTask.addTrigger(trigger);

        InboundMailConnection connection = DAOSchedulerTask.createInboundMailConnection(MailProtocol.POP3, false);
        DSLSchedulerTask.addInboundMailConnection(connection);
        ScriptInfo userScript2 = DAOScriptInfo.createNewScriptInfo("return true");
        SchedulerTask userMailRule = DAOSchedulerTask.createReceiveMailTask(true, connection, userScript2);
        DSLSchedulerTask.addReceiveMailTask(userMailRule, userScript2);
        DSLSchedulerTask.addTask(userMailRule);

        EmbeddedApplication userApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.INDEX_ZIP);
        userApplication.setSettingsSet(settingsSet);
        DSLEmbeddedApplication.add(userApplication);

        AdvimportConfig advConfig = DAOAdvimport.createConfig(FileUtils.readAll(DSLAdvimport.CONFIG_5));
        advConfig.setSettingsSet(settingsSet);
        DSLAdvimport.addConfig(advConfig);

        GUILogon.asSuper();
        GUISchedulerTaskList.goToTaskCardWithRefresh(userMailRule);
        GUIForm.openEditForm();
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.clickApply();

        // Действия и проверки
        // Удаление выполнено через GUI, из-за того, что при удалении через DSL тест не проходит.
        // Подробнее в задаче NSDAT-32155 https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$294171255
        GUISettingsSet.goToSetCard(settingsSet.getCode());
        GUIForm.openDeleteForm();
        GUIForm.clickYes();

        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIScriptModule.goToCard(userModule);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUISchedulerTaskList.goToTaskCardWithRefresh(userScheduler);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUISchedulerTaskList.goToTaskCardWithRefresh(userMailRule);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationForm.goToCard(userApplication);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIAdvimportList.goToCardWithRefresh(advConfig);
        GUISettingsSet.assertSettingsSetOnCards("");

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { String.format(MetainfoElementIdBuilder.TASK_TMP, userMailRule.getCode()) },
                new String[] { String.format(MetainfoElementIdBuilder.SCRIPT_TMP, userScheduler.getCode()) },
                new String[] { String.format(MetainfoElementIdBuilder.ADVIMPORT_CONFIGURATION_ID, advConfig.getCode()) }
        );
        DAOMetainfoExport.selectScriptModules(metainfoExportModel, userModule);

        DAOMetainfoExport.selectEmbeddedApplication(metainfoExportModel, userApplication);

        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек структур, упоминаний, шаблонов списков/стилей/контентов/отчетов и
     * печатных форм
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить структуру (Настройки системы -> Структуры):
     * <ul>
     *     <li>Название: userStructure</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить упоминание (Настройки системы -> Упоминание объектов):
     * <ul>
     *     <li>Название: userFastLinkSetting</li>
     *     <li>Объекты: Сотрудник</li>
     *     <li>Префикс для упоминания объекта: @</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить шаблон списка (Настройки системы -> Шаблоны -> Шаблоны списков):
     * <ul>
     *     <li>Название: listTemplate</li>
     *     <li>Класс объектов списка: Запрос</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>Добавить шаблон стиля (Настройки системы -> Шаблоны -> Шаблоны стилей):
     * <li>
     * <ul>
     *     <li>Название: styleTemplate</li>
     *     <li>Текст шаблона: styleTemplate</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>
     * <ul>Добавить шаблон контента (Настройки системы -> Шаблоны -> Шаблоны контентов):
     *     <li>Название: contentTemplate</li>
     *     <li>Тип контента: Иерархическое дерево</li>
     *     <li>Структура: userStructure</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить шаблон отчета (Настройки системы -> Шаблоны -> Шаблоны отчетов и печатных форм):
     * <ul>
     *     <li>Название: reportTemplate</li>
     *     <li>Файл: object.prpt</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на карточку структуры userStructure.</li>
     * <li>Проверить, что у структуры userStructure в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку упоминания userFastLinkSetting.</li>
     * <li>Проверить, что у упоминания userFastLinkSetting в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку шаблона списка listTemplate.</li>
     * <li>Проверить, что у шаблона списка listTemplate в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку шаблона стиля styleTemplate.</li>
     * <li>Проверить, что у шаблона стиля styleTemplate в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку шаблона контента contentTemplate.</li>
     * <li>Проверить, что у шаблона контента contentTemplate в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Перейти на карточку шаблона отчета reportTemplate.</li>
     * <li>Проверить, что у шаблона отчета reportTemplate в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: Структуры, Упоминания объектов, Шаблоны.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetsDeletingFromStructureSettingsAndTagsAndTemplates()
    {
        // Подготовка
        StructuredObjectsView userStructure = DAOStructuredObjectsView.create();
        userStructure.setSettingsSet(settingsSet);
        DSLStructuredObjectsView.add(userStructure);

        MetaClass employeeClass = DAOEmployeeCase.createClass();
        FastLinkSetting userFastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", GUIFastLinkSetting.UUID,
                employeeClass);
        userFastLinkSetting.setSettingsSet(settingsSet);
        DSLFastLinkSetting.add(userFastLinkSetting);

        MetaClass scClass = DAOScCase.createClass();
        ListTemplate listTemplate = DAOListTemplate.createTemplate(scClass);
        listTemplate.setSettingsSet(settingsSet);
        DSLListTemplate.add(listTemplate);

        StyleTemplate styleTemplate = DAOStyleTemplate.createTemplate("styleTemplate");
        styleTemplate.setSettingsSet(settingsSet);
        DSLStyleTemplate.add(styleTemplate);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(DAOEmployeeCase.createClass(), true,
                PositionContent.FULL, userStructure, false, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);
        ContentTemplate gridTemplate = DAOContentTemplate.createTemplate(hierarchyGrid);
        gridTemplate.setSettingsSet(settingsSet);
        DSLContentTemplate.add(gridTemplate);

        ReportTemplate template = DAOReportTemplate.createReportTemplate(DAOReportTemplate.OBJECT);
        template.setSettingsSet(settingsSet);
        DSLReportTemplate.add(template);

        // Действия и проверки
        GUILogon.asSuper();

        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUIStructuredObjectsView.goToCard(userStructure);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIFastLinkSetting.goToFastSettingCard(userFastLinkSetting);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIListTemplate.goToCard(listTemplate);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIStyleTemplate.goToCard(styleTemplate.getCode());
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIContentTemplate.goToContentTemplate(gridTemplate.getCode());
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIReportTemplate.goToCard(template);
        GUISettingsSet.assertSettingsSetOnCards("");

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.STRUCTURE_SETTINGS },
                new String[] { MetainfoElementIdBuilder.TEMPLATES },
                new String[] { MetainfoElementIdBuilder.MENTION_OBJECTS_SETTINGS });
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }

    /**
     * Тестирование удаления комплекта из настроек списка, карточки объекта, форм добавления/редактирования МК
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00993
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$269093328
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Залить на стенд лицензию с модулем мобильного приложения.</li>
     * <li>Создать класс userClass.</li>
     * <li>Перейти в Настройки системы -> Мобильное приложение.</li>
     * <li>Добавить список объектов::
     * <ul>
     *     <li>Название: userList</li>
     *     <li>Класс: userClass</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Добавить карточку объекта:
     * <ul>
     *     <li>Название: userCard</li>
     *     <li>Класс: userClass</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Нажать на кнопку Редактировать в блоке Элементы управления, выводимые в меню объекта.</li>
     * <li>Снять чек-бокс Использовать системную настройку меню.</li>
     * <li>Добавить элемент:
     * <ul>
     *     <li>Название: userElem</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>Добавить контент в блоке Настройка интерфейса карточки:
     * <li>
     * <ul>
     *     <li>Тип контента: Параметры объекта</li>
     *     <li>Название: propertyList</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>
     * <ul>Создать форму добавления:
     *     <li>Название: userAddForm</li>
     *     <li>Класс: userClass</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <li>Создать форму редактирования:
     * <ul>
     *     <li>Название: userEditForm</li>
     *     <li>Класс: userClass</li>
     *     <li>Комплект: setCase</li>
     * </ul>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в ИА под naumen.</li>
     * <li>Удалить комплект setCase.</li>
     * <li>Перейти на список userList. </li>
     * <li>Проверить, что у списка userList в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Перейти на карточку объекта userCard. </li>
     * <li>Проверить, что у карточки объекта userCard в свойстве "Комплект" комплект с названием setCase отсутствует
     * .</li>
     * <li>Нажать на кнопку Редактировать в блоке Элементы управления, выводимые в меню объекта.</li>
     * <li>Открыть форму редактирования элемента userElem.</li>
     * <li>Проверить, что у элемента userElem в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму редактирования свойства контента propertyList.</li>
     * <li>Проверить, что у контента propertyList в свойстве "Комплект" комплект с названием setCase отсутствует.</li>
     * <li>Открыть форму добавления userAddForm.</li>
     * <li>Проверить, что у формы добавления userAddForm в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Открыть форму редактирования userEditForm.</li>
     * <li>Проверить, что у формы редактирования userEditForm в свойстве "Комплект" комплект с названием setCase
     * отсутствует.</li>
     * <li>Выгрузить частичную метаинформацию: Мобильное приложение.</li>
     * <li>Проверить, что в выгруженной метаинформации отсутствуют узлы <set>.</li>
     * </ol>
     */
    @Test
    void testSetsDeletingFromListSettingsAndObjectCardAndMobileFormsOfAddingAndDeleting()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        MobileList userList = DAOMobile.createMobileList(userClass);
        DSLMobile.add(userList);

        MobileCard userCard = DAOMobile.createMobileCard(userClass);
        DSLMobile.add(userCard);

        MobilePropertiesList content = DAOMobile.createMobilePropertiesList();
        DSLMobile.addContents(userCard, content);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        DSLMobile.add(addForm);

        MobileEditForm editForm = DAOMobile.createMobileEditForm(userClass);
        DSLMobile.add(editForm);

        GUILogon.asSuper();

        GUIMobileCard.goToCard(userCard);
        GUIMobileCard.clickEditMenuButton();
        GUIMobileCard.setUseSystemSettings(false);
        GUIMobileCard.clickAddElement();
        GUIMobileAddForm.fillTitle(ModelUtils.createTitle());
        GUISettingsSet.fillSettingsSetOnForm(settingsSet.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        GUINavigational.goToMobileSettings();
        GUIMobileList.clickOnTableRow(userList);
        GUIMobileList.clickEditButton();
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.applyForm();

        GUIMobileCard.goToCard(userCard);
        GUIMobileContents.clickEditContent(content);
        GUISettingsSet.fillSettingsSetPropOnForm(settingsSet.getCode());
        GUIForm.applyForm();

        // Действия и проверки
        DSLSettingsSet.delete(settingsSet);
        Cleaner.afterTest(() -> DSLSettingsSet.add(settingsSet));

        GUINavigational.goToMobileSettings();
        GUIMobileList.clickOnTableRow(userList);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIMobileCard.goToCard(userCard);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIMobileContents.clickEditContent(content);
        GUISettingsSet.assertSettingsSetPropOnForm(null);
        GUIForm.clickCancel();

        GUIMobileAddForm.goToAddForm(addForm);
        GUISettingsSet.assertSettingsSetOnCards("");

        GUIMobileEditForm.goToEditForm(editForm);
        GUISettingsSet.assertSettingsSetOnCards("");

        MetainfoExportModel metainfoExportModel = DAOMetainfoExport.createExportModelByExportPaths(
                new String[] { MetainfoElementIdBuilder.MOBILE_SETTINGS });
        File metainfoFile = DSLMetainfoTransfer.exportMetainfo(metainfoExportModel);
        MetaInfoXml metaInfoXml = new MetaInfoXml(metainfoFile);
        metaInfoXml.assertAbsenceElement("set");
    }
}