package ru.naumen.selenium.cases.script.security;

import static io.restassured.RestAssured.given;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.function.BiFunction;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityMarker;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.RightGroup;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.security.UserGroup;

/**
 * Тесты на скриптовое API (API для работы с правами из скриптов)
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 * <AUTHOR>
 * @since 18.10.2012
 */
public class SecurityApiTest extends AbstractTestCase
{
    /**
     * Вызывать метод api.security.showNewPermissionsForUnlicensedUser(String...)
     * с указанными параметрами с сравнить вернувшееся значение с ожидаемым
     */
    private static void assertShowNewPermissionsForUnlicensedUser(String params, String expected)
    {
        String scriptPattern = "return api.security.showNewPermissionsForUnlicensedUser("
                               + params + ")";
        //Выполнение действий и проверки
        ScriptRunner script = new ScriptRunner(scriptPattern);
        Assert.assertEquals(expected, script.runScript().get(0).trim());
    }

    /**
     * Тестирование, что метод api.security.hasEditPermission(object, attributeCode) возвращает false, когда текущему
     * пользователю запрещено редактирование атрибута<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>
     *   Добавить в систему скриптовый модуль с кодом testModule с текстом
     *   <pre>
     *     def hasEditPermissionWrapper(objectUuid, attributeCode) {
     *       return api.security.hasEditPermission(utils.get(objectUuid), attributeCode)
     *     }
     *   </pre>
     * </li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать атрибут attrString в ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Убрать право на "Редактирование атрибутов"</li>
     *
     * <br>
     * <b>Действие:</b>
     * <li>Получаем accessKey для employee
     * <li>Выполняем REST запрос /services/rest/exec?accessKey=${accessKey}&func=modules.testModule
     * .hasEditPermissionWrapper&params='${ou.uuid}','${attrString.code}'</li>
     * <br>
     * <b>Проверки</b>
     * <li>Результат REST вызова равен false</li>
     */
    @Test
    public void testHasEditPermissionReturnsFalseWhenItReallyHasNot()
    {
        testHasEditPermissionMethod("false",
                (ouCase, attrString) -> new SecurityMarkerEditAttrs(ouCase).addAttributes(attrString).apply());
    }

    /**
     * Тестирование, что метод api.security.hasEditPermission(object, attributeCode) возвращает true, когда текущему
     * пользователю разрешено редактирование атрибута<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>
     *   Добавить в систему скриптовый модуль с кодом testModule с текстом
     *   <pre>
     *     def hasEditPermissionWrapper(objectUuid, attributeCode) {
     *       return api.security.hasEditPermission(utils.get(objectUuid), attributeCode)
     *     }
     *   </pre>
     * </li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать атрибут attrString в ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Убрать право на "Редактирование атрибутов"</li>
     *
     * <br>
     * <b>Действие:</b>
     * <li>Получаем accessKey для employee
     * <li>Выполняем REST запрос /services/rest/exec?accessKey=${accessKey}&func=modules.testModule
     * .hasEditPermissionWrapper&params='${ou.uuid}','${attrString.code}'</li>
     * <br>
     * <b>Проверки</b>
     * <li>Результат REST вызова равен false</li>
     */
    @Test
    public void testHasEditPermissionReturnsTrueWhenItReallyHas()
    {
        testHasEditPermissionMethod("true", (ouCase, attrString) -> null);
    }

    /**
     * Тестирование метода api.security.hasProfile проверяет наличия профиля для указанного метакласса или объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44552037
     * <br>
     * <b>Подготовка.</b>
     * <li>Создаем тип сотрудника employeeCase и сотрудника employee</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Выполняем скрипт:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *    return api.security.hasProfile('ou$AtesthIW5NFzp', 'Atest permission set');
     *
     *    и скрипт
     *
     *    def obj = utils.get('ou$901');
     *    return api.security.hasProfile(obj, 'Atest permission set');
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверяем результат выполнения скрипта true и true
     * <pre>
     * </pre>
     * </li>
     */
    @Test
    public void testHasProfile()
    {
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(employee);

        String profile = "Atest permission set";
        String message = "Ожидаемый результат выполнения скрипта не совпал с полученным.";

        String scriptPattern1 = "return api.security.hasProfile('" + employeeCase.getFqn() + "', '" + profile + "');";
        //Выполнение действий и проверки
        ScriptRunner script1 = new ScriptRunner(scriptPattern1);
        Assert.assertEquals(message, "true", script1.runScript().get(0).trim());

        String scriptPattern2 = "def obj = utils.get('" + employee.getUuid() + "');"
                                + "return api.security.hasProfile(obj, '" + profile + "');";
        //Выполнение действий и проверки
        ScriptRunner script2 = new ScriptRunner(scriptPattern2);
        Assert.assertEquals(message, "true", script2.runScript().get(0).trim());
    }

    /**
     * Тестирование метода api.security.hasProfile (наличие роли в системе или у сотрудника для указанного объекта) в
     * случае отрицательного результата проверок
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44555754
     * <br>
     * <b>Подготовка</b>
     * <br>
     * <li>Добавить тип класса Отдел ouCase</li>
     * <li>Создать пользовательский профиль userProfile без указания группы пользователей</li>
     * <li>Добавить в ouCase пользовательский атрибут attr типа "Логический" со скриптом:
     *  <pre>
     *     -------------------------------------------------------------------------------
     *     return api.security.hasProfile('%Uuid сотрудника%','%название userProfile%')
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Создать группу атрибутов attrGroup в ouCase, добавить в нее attr</li>
     * <li>Добавить на карточку ouCase контент типа "Параметры объекта" с группой атрибутов attrGroup</li>
     * <li>Добавить отдел ou типа ouCase</li>
     * <br>
     * <b>Действия</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку ou</li>
     * <br>
     * <b>Проверки</b>
     * <li>Значение attr в content = "нет"</li>
     */
    @Test
    public void testHasProfileReturnsFalse()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createBool(ouCase);
        attr.setViewPresentation(BooleanType.VIEW_YES_NO);
        SecurityProfile userProfile = DAOSecurityProfile.create(true, null);
        DSLSecurityProfile.add(userProfile);
        String script = "def obj = utils.get('" + SharedFixture.employee().getUuid() + "');"
                        + "return api.security.hasProfile(obj, '" + userProfile.getTitle() + "');";
        DAOAttribute.changeToComputable(attr, script);
        DSLAttribute.add(attr);
        GroupAttr attrGroup = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(attrGroup, attr);

        ContentForm content = DAOContentCard.createPropertyList(ouCase, attrGroup);
        DSLContent.add(content);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Действия
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        //Проверки
        attr.setValue("нет");
        GUIPropertyList.assertPropertyListAttribute(content, attr);
    }

    /**
     * Тестирование метода api.security.hasRole(IUUIDIdentifiable, String) проверяет наличие роли в системе или у
     * сотрудника
     * для указанного объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44552037
     * <br>
     * <b>Подготовка.</b>
     * <li>Создаем тип сотрудника employeeCase и сотрудника employee</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Создаем вычислимый атрибут типа строка со скриптом
     *     <pre>
     *     -------------------------------------------------------------------------------
     *    def obj = utils.get('ou$901');
     *    return api.security.hasRole(obj, 'employee');
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверяем результат выполнения скрипта true
     * <pre>
     * </pre>
     * </li>
     */
    @Test
    public void testHasRole()
    {
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        Attribute stringAttr = DAOAttribute.createString(ouCase.getFqn());
        DAOAttribute.changeToComputable(stringAttr,
                "def obj = utils.get('" + ou.getUuid() + "');" + "return api.security.hasRole(obj, 'employee');");
        DSLAttribute.add(stringAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(attrGroup, stringAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, attrGroup);
        DSLContent.add(propertyList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        //Выполнение действия
        stringAttr.setValue("true");
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIPropertyList.assertPropertyListAttribute(propertyList, stringAttr);
    }

    /**
     * Тестирование метода api.security.hasRole (IUUIDIdentifiable, String) (наличие роли в системе или у сотрудника для
     * указанного объекта) в случае отрицательного результата проверок
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$44555754
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$41991541
     * <br>
     * <b>Подготовка</b>
     * <br>
     * <li>Добавить пользовательскую роль userRole для класса Запрос со скриптом доступа "return false"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт profileScript:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     def obj = utils.get('%UUID отдела%'); return api.security.hasRole(obj, '%код userRole%');
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить, что результат выполнения скрипта -  "false"</li>
     * <li>Установить для userRole скрипт доступа "return true"</li>
     * <li>Выполнить скрипт profileScript</li>
     * <li>Проверить, что результат выполнения скрипта -  "true"</li>
     */
    @Test
    public void testHasRoleReturnsFalse()
    {
        //Подготовка
        String accessScript = "return false";
        ScriptInfo accessScriptInfo = DAOScriptInfo.createNewScriptInfo(accessScript);
        DSLScriptInfo.addScript(accessScriptInfo);

        String accessScript2 = "return true";
        ScriptInfo accessScriptInfo2 = DAOScriptInfo.createNewScriptInfo(accessScript2);
        DSLScriptInfo.addScript(accessScriptInfo2);

        ScriptInfo ownersScriptInfo = DSLSecurityRole.createDefaultOwnersScriptInfo();
        SecurityRole userRole = DAOSecurityRole.create(DAOScCase.createClass(), accessScriptInfo, ownersScriptInfo);
        DSLSecurityRole.add(userRole);

        //Действия и проверки
        String profileScript = String.format("def obj = utils.get('%s'); return api.security.hasRole(obj, '%s');",
                SharedFixture.ou().getUuid(), userRole.getCode());
        ScriptRunner script = new ScriptRunner(profileScript);
        String result = script.runScript().get(0).trim();
        Assert.assertTrue("false".equals(result));

        userRole.setAccessScript(accessScriptInfo2.getCode());
        DSLSecurityRole.edit(userRole);
        result = script.runScript().get(0).trim();
        Assert.assertTrue("true".equals(result));
    }

    /**
     * Тестирование метода api.security.showNewPermissionsForUnlicensedUser(String...)<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76349145
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс {@code userClass}</li>
     * <li>Создать в классе {@code userClass} тип {@code userType1}</li>
     * <li>Создать в классе {@code userClass} тип {@code userType2}</li>
     * <li>Создать в классе {@code userClass} тип {@code userType3}</li>
     * <li>В классе {@code userClass} создать пользовательский атрибут
     *     {@code doubleAttr} типа "Вещественное число"</li>
     * <li>В типе {@code userType1} создать пользовательский атрибут
     *     {@code textRTF} типа "Текст в формате RTF"</li>
     * <li>В типе {@code userType2} создать пользовательский атрибут
     *     {@code file} типа "Файл"</li>
     * <li>В матрице прав класса {@code userClass} добавить профиль {@code secProfile}:
     *     Для нелицензированных пользователей: Да;
     *     Роли пользователей: Сотрудник.
     * </li>
     * <li>В матрице прав класса {@code userClass} добавить маркер прав {@code userClassMarker}
     *     на редактирование атрибута {@code doubleAttr}</li>
     * <li>В матрице прав типа {@code userType1} изменяем маркер прав {@code userClassMarker}
     *     добавляя атрибут {@code textRTF}</li>
     * <li>В матрице прав типа {@code userType2} добавить маркер прав {@code userType2Marker}
     *     на редактирование атрибута {@code file}</li>
     * <li>Для профиля {@code secProfile} в классе {@code userClass} выставляем права:
     *     просмотр карточки объекта, просмотр атрибутов: Остальные атрибуты,
     *     редактирование атрибутов: Остальные атрибуты, маркер {@code userClassMarker}</li>
     * <li>Для профиля {@code secProfile} в типе {@code userType2} выставляем права:
     *     маркер {@code userType2Marker}</li>
     * <li>Для профиля {@code secProfile} в типе {@code userType2} убираем права:
     *     маркер {@code userClassMarker}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт для класса {@code userClass} с параметрами 'system','full'</li>
     * <li>Проверить, что для класса {@code userClass} отобразился блок "Просмотр атрибутов"
     *     с маркером "Остальные атрибуты" с системными атрибутами:
     *     "Автор", "Дата архивирования", "Дата изменения", "Дата создания", "Папки"
     *     и блок "Редактирование атрибутов" с маркером {@code userClassMarker}
     *     с атрибутом {@code doubleAttr}</li>
     * <li>Проверить, что для типа {@code userType1} отобразился блок "Редактирование атрибутов"
     *     с маркером {@code userType1Marker}
     *     с атрибутами {@code doubleAttr}, {@code textRTF}</li>
     * <li>Проверить, что для типа {@code userType2} отобразился блок "Просмотр атрибутов"
     *     с маркером "Остальные атрибуты" с системными атрибутами:
     *     "Автор", "Дата архивирования", "Дата изменения", "Дата создания", "Папки"
     *     и блок "Редактирование атрибутов" с маркером {@code userType2Marker}
     *     с атрибутом {@code file}</li>
     * <li>Проверить, что для типа {@code userType3} не отобразилось ничего</li>
     * </ol>
     */
    @Test
    public void testShowNewPermissionsForUnlicensedUser()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        // Определим названия чтобы порядок типов был однозначен
        MetaClass userType1 = DAOUserCase.create(userClass, "userType1", "userType1");
        MetaClass userType2 = DAOUserCase.create(userClass, "userType2", "userType2");
        MetaClass userType3 = DAOUserCase.create(userClass, "userType3", "userType3");
        DSLMetaClass.add(userClass, userType1, userType2, userType3);

        Attribute doubleAttr = DAOAttribute.createDouble(userClass);
        Attribute textRTF = DAOAttribute.createTextRTF(userType1.getFqn());
        Attribute file = DAOAttribute.createFile(userType2.getFqn());
        DSLAttribute.add(doubleAttr, textRTF, file);

        SecurityProfile secProfile = SharedFixture.secProfileUnlic();

        SecurityMarker userClassMarker = new SecurityMarkerEditAttrs(userClass)
                .addAttributes(doubleAttr).apply();

        SecurityMarkerEditAttrs userType1Marker =
                (SecurityMarkerEditAttrs)DSLSecurityMarker.getSecurityMarker(
                        userType1, userClassMarker.getRightCode());
        assertNotNull("Маркер не был наследован в тип", userType1Marker);
        userType1Marker.addAttributes(textRTF);
        DSLSecurityMarker.edit(userType1, userType1Marker);

        SecurityMarker userType2Marker = new SecurityMarkerEditAttrs(userType2)
                .addAttributes(file).apply();

        DSLSecurityProfile.setRights(userClass, secProfile,
                AbstractBoRights.VIEW_CARD, AbstractBoRights.VIEW_REST_ATTRIBUTES,
                AbstractBoRights.EDIT_REST_ATTRIBUTES, userClassMarker);
        DSLSecurityProfile.setRights(userType2, secProfile, userType2Marker);
        DSLSecurityProfile.removeRights(userType2, secProfile, userClassMarker);

        // Действия и проверки
        String scriptPattern = "import ru.naumen.core.server.script.api.utils.ChangesInPermissionSetUnlicUsers\n"
                               + "import ru.naumen.core.server.license.conf.PermissionSetUnlicUsers\n"
                               + "return beanFactory.getBean(ChangesInPermissionSetUnlicUsers.class)"
                               + ".asHTML(PermissionSetUnlicUsers.SYSTEM, PermissionSetUnlicUsers.FULL, '%s')";
        //Выполнение действий и проверки
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, userClass.getFqn()));
        String expected = "<html><h3>Класс \"" + userClass.getTitle() + "\"</h3>\n"
                          + "Просмотр атрибутов<br>\n"
                          + "<ul style=\"list-style-type: disc;\">\n"
                          + "\t<li>Маркер \"<u>Остальные атрибуты</u>\": \"Автор\", \"Дата архивирования\","
                          + " \"Дата изменения\", \"Дата создания\", \"Папки\"</li>\n"
                          + "</ul>\n"
                          + "Редактирование атрибутов<br>\n"
                          + "<ul style=\"list-style-type: disc;\">\n"
                          + "\t<li>Маркер \"<u>" + userClassMarker.getTitle() + "</u>\": \""
                          + doubleAttr.getTitle() + "\"</li>\n"
                          + "</ul>\n"
                          + "\n"
                          + "<h4>Тип \"" + userType1.getTitle() + "\" (класс \"" + userClass.getTitle() + "\")</h4>\n"
                          + "Редактирование атрибутов<br>\n"
                          + "<ul style=\"list-style-type: disc;\">\n"
                          + "\t<li>Маркер \"<u>" + userType1Marker.getTitle() + "</u>\": \""
                          + doubleAttr.getTitle() + "\", \"" + textRTF.getTitle() + "\"</li>\n"
                          + "</ul>\n"
                          + "\n"
                          + "<h4>Тип \"" + userType2.getTitle() + "\" (класс \"" + userClass.getTitle() + "\")</h4>\n"
                          + "Просмотр атрибутов<br>\n"
                          + "<ul style=\"list-style-type: disc;\">\n"
                          + "\t<li>Маркер \"<u>Остальные атрибуты</u>\": \"Автор\", \"Дата архивирования\", \"Дата "
                          + "изменения\","
                          + " \"Дата создания\", \"Папки\"</li>\n"
                          + "</ul>\n"
                          + "Редактирование атрибутов<br>\n"
                          + "<ul style=\"list-style-type: disc;\">\n"
                          + "\t<li>Маркер \"<u>" + userType2Marker.getTitle() + "</u>\": \""
                          + file.getTitle() + "\"</li>\n"
                          + "</ul>\n"
                          + "\n"
                          + "</html>";
        Assert.assertEquals(expected, script.runScript().get(0).trim());
    }

    /**
     * Тестирование метода api.security.showNewPermissionsForUnlicensedUser(String...)
     * когда результат должен быть обязательно пуст (сообщение об отсутствии результата)<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76349145
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:<br>
     *   <code>return api.security.showNewPermissionsForUnlicensedUser('full', 'full')</code>
     * </li>
     * <li>Проверить, что результат выполнения
     * "<html><h4>Отсутствуют изменения прав доступа для нелицензированных пользователей. Перенастройка прав не
     * требуется.</h4></html>"
     * </li>
     * <li>Выполнить скрипт:<br>
     *   <code>return api.security.showNewPermissionsForUnlicensedUser('system', 'system')</code>
     * </li>
     * <li>Проверить, что результат выполнения
     * "<html><h4>Отсутствуют изменения прав доступа для нелицензированных пользователей. Перенастройка прав не
     * требуется.</h4></html>"
     * </li>
     * <li>Выполнить скрипт:<br>
     *   <code>return api.security.showNewPermissionsForUnlicensedUser('full', 'system')</code>
     * </li>
     * <li>Проверить, что результат выполнения
     * "<html><h4>Отсутствуют изменения прав доступа для нелицензированных пользователей. Перенастройка прав не
     * требуется.</h4></html>"
     * </li>
     * </ol>
     */
    @Test
    public void testShowNewPermissionsForUnlicensedUserEmptyResult()
    {
        String expected = "<html><h4>Отсутствуют изменения прав доступа для нелицензированных пользователей. "
                          + "Перенастройка прав не требуется.</h4></html>";
        assertShowNewPermissionsForUnlicensedUser("'full', 'full'", expected);
        assertShowNewPermissionsForUnlicensedUser("'system', 'system'", expected);
        assertShowNewPermissionsForUnlicensedUser("'full', 'system'", expected);
    }

    /**
     * Тестирование метода api.security.showNewPermissionsForUnlicensedUser(String...)
     * при вводе некорректных параметров<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76349145
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт:<br>
     *   <code>return api.security.showNewPermissionsForUnlicensedUser('sistem', 'fill')</code>
     * </li>
     * <li>Проверить, что результат выполнения "Ошибка! Недопустимые параметры: 'sistem', 'fill'"</li>
     * <li>Выполнить скрипт:<br>
     *   <code>return api.security.showNewPermissionsForUnlicensedUser('extendedWorkflow', 'test')</code>
     * </li>
     * <li>Проверить, что результат выполнения "Ошибка! Недопустимые параметры: 'test'"</li>
     * </ol>
     */
    @Test
    public void testShowNewPermissionsForUnlicensedUserErrorParams()
    {
        String params = "'sistem', 'fill'";
        String errMsg = "Ошибка! Недопустимые параметры: ";
        assertShowNewPermissionsForUnlicensedUser(params, errMsg + params);
        assertShowNewPermissionsForUnlicensedUser(
                "'extendedWorkflow', 'test'", errMsg + "'test'");
    }

    private String getEmployeeAccessKey(Bo employee)
    {
        String scriptPattern = "return api.auth.getAccessKey('%s').uuid;";
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, employee.getLogin()));
        return script.runScript().get(0).trim();
    }

    private void testHasEditPermissionMethod(String expectedResult,
            BiFunction<MetaClass, Attribute, SecurityMarker> markerProducer)
    {
        //Подготовка
        ModuleConf module = DAOModuleConf.create("testModule");
        module.setScriptBody(
                "def hasEditPermissionWrapper(uuid, attrCode) { return api.security.hasEditPermission(utils.get(uuid)"
                + ", attrCode) }");
        DSLModuleConf.add(module);

        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase, employeeCase);
        Attribute attrString = DAOAttribute.createString(ouCase.getFqn());
        DSLAttribute.add(attrString);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        //Убираем право на редактирование атрибута attrString
        UserGroup userGroup = new UserGroup(employee);
        ru.naumen.selenium.security.SecurityProfile profile = new ru.naumen.selenium.security.SecurityProfile(userGroup,
                new EmployeeRole(), true);
        RightGroup rightGroup = new RightGroup(profile, ouCase).addAllRights(ouCase);
        SecurityMarker marker = markerProducer.apply(ouCase, attrString);
        if (marker != null)
        {
            rightGroup.removeRight(ouCase, marker);
        }
        rightGroup.apply();

        //Выполнение действия

        String accessKey = getEmployeeAccessKey(employee);
        String link = Config.get().getWebAddress()
                      + "services/rest/exec?accessKey=%s&func=modules.testModule"
                      + ".hasEditPermissionWrapper&params='%s','%s'";
        //Переходим по ссылке и проверяем, что метод модуля выполнился успешно 
        String result = given().when().get(String.format(link, accessKey, ou.getUuid(), attrString.getCode()))
                .asString();

        //Проверка
        assertEquals("Полученное сообщение об ошибке не совпало с ожидаемым.", expectedResult, result);
    }

    /**
     * Тестирование метода api.security.hasAddServiceCallPermission(fqns, client, employee)
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$89481078
     * https://naupp.naumen.ru/sd/operator/#uuid:domesticsup$89394632
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <b>Подготовка.</b>
     * <li>Создать подтип запроса scCase</li>
     * <li>Создать команду team, отдел ou и сотрудника employee</li>
     * <li>Создать группу пользователей secGroup и добавить в неё team, ou и employee</li>
     * <li>Создать профиль emplProfile с группой пользователей secGroup для роли Сотрудник</li>
     * <br>
     * <b>Выполнение действий и проверок.</b>
     * <li>Выполнить скрипт с типом scCase, контрагентом и пользователем - employee
     * <pre>
     * ------------------------------------------------------------------------------------------------
     * api.security.hasAddServiceCallPermission(['%подтип запроса%'], '%контрагент%', '%пользователь%')
     * ------------------------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Проверяем результат выполнения скрипта - пустой массив</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом ou и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - пустой массив</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом team и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - пустой массив</li>
     * <li>Выдать права в метаклассе scCase для профиля emplProfile</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом и пользователем - employee</li>
     * <li>Проверяем результат выполнения скрипта - список с переданным типом</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом ou и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - список с переданным типом</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом team и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - список с переданным типом</li>
     * <li>Выдать права в метаклассе scCase для профиля emplProfile установив скрипт уточнения права
     * (если клиент команда - права есть, иначе их нет)
     * <pre>
     * ---------------------------------------
     * initialValues?.client?.contains('team')
     * ---------------------------------------
     * </pre>
     * </li>
     * <li>Выполнить скрипт с типом scCase, контрагентом и пользователем - employee</li>
     * <li>Проверяем результат выполнения скрипта - пустой массив</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом ou и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - пустой массив</li>
     * <li>Выполнить скрипт с типом scCase, контрагентом team и пользователем employee</li>
     * <li>Проверяем результат выполнения скрипта - список с переданным типом</li>
     */
    @Test
    public void testHasAddServiceCallPermission()
    {
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(team, employee);

        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee, ou, team);

        SecurityProfile emplProfile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);

        hasAddServiceCallPermission(scCase, employee, employee, false);
        hasAddServiceCallPermission(scCase, ou, employee, false);
        hasAddServiceCallPermission(scCase, team, employee, false);

        DSLSecurityProfile.setRights(scCase, emplProfile, ScRights.ADD_TO_EMPLOYEE,
                ScRights.ADD_TO_OU, ScRights.ADD_TO_TEAM);

        hasAddServiceCallPermission(scCase, employee, employee, true);
        hasAddServiceCallPermission(scCase, ou, employee, true);
        hasAddServiceCallPermission(scCase, team, employee, true);

        String scriptTemplate = "initialValues?.client?.contains('team')";
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(scriptTemplate);
        Cleaner.afterTest(true, () ->
        {
            DSLSecurityProfile.delete(emplProfile);
        });
        DSLScriptInfo.addScript(scriptInfo);

        DSLSecurityProfile.setScriptRight(scCase, emplProfile, ScRights.ADD_TO_EMPLOYEE, scriptInfo.getCode());
        DSLSecurityProfile.setScriptRight(scCase, emplProfile, ScRights.ADD_TO_OU, scriptInfo.getCode());
        DSLSecurityProfile.setScriptRight(scCase, emplProfile, ScRights.ADD_TO_TEAM, scriptInfo.getCode());

        hasAddServiceCallPermission(scCase, employee, employee, false);
        hasAddServiceCallPermission(scCase, ou, employee, false);
        hasAddServiceCallPermission(scCase, team, employee, true);
    }

    private static void hasAddServiceCallPermission(MetaClass metaClass, Bo client, Bo user, Boolean expected)
    {
        String scriptPattern = "api.security.hasAddServiceCallPermission(['%s'], '%s', '%s')";
        String script = String.format(scriptPattern, metaClass.getFqn(), client.getUuid(), user.getUuid());
        Assert.assertEquals(expected ? "[" + metaClass.getFqn() + "]" : "[]",
                new ScriptRunner(script).runScript().get(0).trim());
    }

    /**
     * Тестирование того, что метод api.security.hasEditPermission(object, attributeCode) возвращает true, когда
     * текущему пользователю с системной ролью разрешено редактирование атрибута<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120294
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>
     *   Добавить в систему скриптовый модуль с кодом testModule с текстом
     *   <pre>
     *   def hasEditPermissionWrapper(objectUuid, attributeCode) {
     *     return api.security.hasEditPermission(utils.get(objectUuid), attributeCode)
     *   }</pre></li>
     * <li>Создать тип класса "Запрос" scCase</li>
     * <li>Создать строковый атрибут stringAttr в классе "Запрос" scClass</li>
     * <li>Создать в классе scClass "Запрос" в настройке прав на редактирование атрибутов отдельный маркер markerEdit с
     * атрибутом stringAttr</li>
     * <li>Создать лицензированного пользователя employee</li>
     * <li>Создать группу пользователей securityGroup, добавить в эту группу пользователя employee</li>
     * <li>В классе "Запрос"  scClass, на вкладке "Права доступа", создать профиль securityProfile
     * (Для нелицензированного пользователя: нет, Роли пользователей: "Сотрудник, ответственный за объект",
     * Группы пользователей: securityGroup)
     * для данного профиля дать права на редактирование атрибута stringAttr (в markerEdit) </li>
     * <li>Создать запрос scBo в интерфейсе оператора</li>
     * <li>Установить ответственным у запроса scBo сотрудника employee</li>
     * <br>
     * <b>Действия:</b>
     * <li>Получаем accessKey для employee
     * <li>Выполняем REST запрос /services/rest/exec?accessKey=${accessKey}&func=modules.testModule
     * .hasEditPermissionWrapper&params='${ou.uuid}','${attrString.code}'</li>
     * <br>
     * <b>Проверка:</b>
     * <li>Результат REST вызова равен "true"</li>
     */
    @Test
    public void testHasEditPermissionMethodIfSystemRole()
    {
        //Подготовка
        ModuleConf module = DAOModuleConf.create("testModule");
        module.setScriptBody(
                "def hasEditPermissionWrapper(uuid, attrCode) { return api.security.hasEditPermission(utils.get(uuid)"
                + ", attrCode) }");
        DSLModuleConf.add(module);

        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        Attribute stringAttr = DAOAttribute.createString(scClass);
        DSLAttribute.add(stringAttr);

        SecurityMarker markerEdit = new SecurityMarkerEditAttrs(scClass).addAttributes(stringAttr).apply();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        DSLSecurityGroup.addUsers(securityGroup, employee);

        SecurityRole securityRole = SysRole.respEmp(scCase);

        SecurityProfile securityProfile = DAOSecurityProfile.create(true, securityGroup, securityRole);
        DSLSecurityProfile.add(securityProfile);
        DSLSecurityProfile.grantAllPermissions(securityProfile);
        DSLSecurityProfile.setRights(scClass, securityProfile, markerEdit.getRightsBlock());

        Bo scBo = DAOSc.create(scCase);
        DSLBo.add(scBo);

        DSLSc.setResponsible(scBo, null, employee);

        //Действия
        String accessKey = getEmployeeAccessKey(employee);
        String link = Config.get().getWebAddress()
                      + "services/rest/exec?accessKey=%s&func=modules.testModule"
                      + ".hasEditPermissionWrapper&params='%s','%s'";
        //Переходим по ссылке и проверяем, что метод модуля выполнился успешно
        String formatted = String.format(link, accessKey, scBo.getUuid(), stringAttr.getCode());
        String result = given().when().get(formatted).asString();

        //Проверка
        assertEquals("Полученное сообщение об ошибке не совпало с ожидаемым.", "true", result);
    }
}
