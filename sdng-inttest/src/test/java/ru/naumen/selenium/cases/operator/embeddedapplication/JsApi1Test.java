package ru.naumen.selenium.cases.operator.embeddedapplication;

import java.io.File;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLInterface;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на JS API для встроенных приложений (кроме команд)
 *
 * <AUTHOR> Biktashev
 * @since 26 дек. 2017 г.
 */
class JsApi1Test extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static final long JS_MAX_SAFE_INTEGER = 9007199254740991L;

    private static MetaClass userClass, rootClass;

    /**
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создаем пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создаем модель компании</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        rootClass = DAORootClass.create();
    }

    private static String join(String... strings)
    {
        return String.join("\n", strings);
    }

    /**
     * Тестирование метода jsApi.addFieldChangeListener
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382241
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавляем в систему атрибут attr для класса userClass</li>
     * <li>Добавляем файл applicationFile встроенного приложения, 
     * в котором добавляем обработчик события изменения значения поля title: 
     *      <pre>jsApi.addFieldChangeListener('title', function(result) {
     var testDiv = document.getElementById('test_div')
     testDiv.innerText = result.newValue
     })</pre>
     * Данный обработчик изменит текст элемента с идентификатором test_div приложения на значение атрибута title, 
     * если он изменится</li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На форму добавления объектов типа userClass добавляем встроенное приложение app</li> 
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Заходим под Naumen</li>
     * <li>Переходим на форму добавления объекта класса userClass</li>
     * <li>Заполняем интересующий нас атрибут title текстом "TEST"</li>
     * <li>Кликаем по атрибуту attr - это необходимо для того, чтобы сработало событие FieldChangedEvent, а далее
     * сработал наш обработчик из п.2</li>
     * <li>Проверяем, что текст элемента с идентификатором test_div 
     * приложения стал равен "TEST" - значению атрибута title на форме</li>
     * </ol>
     */
    @Test
    void testAddFieldChangeListener()
    {
        //Подготовка
        Attribute attr = DAOAttribute.createString(userClass);
        DSLAttribute.add(attr);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(attrsGroup, new Attribute[] { attr }, new Attribute[] {});

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp.getAbsoluteFile(), String.format(join(
                    "jsApi.addFieldChangeListener('%s', function(result) {",
                    "       var testDiv = document.getElementById('%s')",
                    "       testDiv.innerText = result.newValue",
                    "})"
                    
                ), 
                Bo.TITLE, GUIEmbeddedApplication.TEST_DIV_ID));
        //@formatter:on

        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm app = DAOContentAddForm.createEmbeddedApplication(userClass.getFqn(), model);
        DSLContent.add(app);

        //Выполнение действий и проверка
        GUILogon.asNaumen();
        GUIBo.goToAddForm(userClass);

        final String attrValue = "TEST";
        GUIForm.fillAttribute(Bo.TITLE, attrValue);
        GUIForm.fillAttribute(attr, ""); //Клик по другому атрибуту

        Assertions.assertEquals(attrValue, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(app));
    }

    /**
     * <p>Тестирование получения UUID объекта, на карточке которого находимся
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить тип ouCase
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.extractSubjectUuid())</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку ouCase
     *   <li>Создать объект ou типа ouCase</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку объекта ou</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен UUID объекта ou
     * </ol>   
     */
    @Test
    void testExtractSubjectUuid()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();

        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "document.write(jsApi.extractSubjectUuid())");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(ouCase.getFqn(), model);
        DSLContent.add(content);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));
        Bo ou = DAOUserBo.create(ouCase);
        DSLBo.add(ou);

        GUILogon.asNaumen();
        GUIBo.goToCard(ou);

        GUIContent.assertPresent(content);
        Assertions.assertEquals(ou.getUuid(), GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование метода jsApi.addFieldChangeListener: тестирование того, что изменение атрибута, на изменение
     * которого нет
     * обработчика не вызывает существующие обработчики
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58382241 
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавляем в систему атрибут attr для класса userClass</li>
     * <li>Добавляем файл applicationFile встроенного приложения, 
     * в котором добавляем обработчик события изменения значения поля title: 
     *      <pre>jsApi.addFieldChangeListener('title', function(result) {
     var testDiv = document.getElementById('test_div')
     testDiv.innerText = result.newValue
     })</pre>
     * Данный обработчик изменит текст элемента с идентификатором test_div приложения на значение атрибута title, 
     * если он изменится</li>
     * <li>Добавляем в систему и включаем встроенное приложение app из файла applicationFile</li>
     * <li>На форму добавления объектов типа userClass добавляем встроенное приложение app</li> 
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Заходим под Naumen</li>
     * <li>Переходим на форму добавления объекта класса userClass</li>
     * <li>Заполняем интересующий нас атрибут attr текстом "TEST"</li>
     * <li>Кликаем по атрибуту title</li>
     * <li>Проверяем, что текст элемента с идентификатором test_div 
     * приложения не стал равен "TEST" => обработчик не сработал</li>
     * </ol>
     */
    @Test
    void testFieldChangeListenerDoesntTriggerOnUnlistenedFields()
    {
        //Подготовка
        Attribute attr = DAOAttribute.createString(userClass);
        DSLAttribute.add(attr);

        GroupAttr attrsGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(attrsGroup, new Attribute[] { attr }, new Attribute[] {});

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(join(
                    "jsApi.addFieldChangeListener('%s', function(result) {",
                    "       var testDiv = document.getElementById('%s')",
                    "       testDiv.innerText = result.newValue",
                    "})"
                    
                ), 
                Bo.TITLE, GUIEmbeddedApplication.TEST_DIV_ID));
        //@formatter:on

        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm app = DAOContentAddForm.createEmbeddedApplication(userClass.getFqn(), model);
        DSLContent.add(app);

        //Выполнение действий и проверка
        GUILogon.asNaumen();
        GUIBo.goToAddForm(userClass);

        final String attrValue = "TEST";
        GUIForm.fillAttribute(attr, attrValue);
        GUIForm.fillAttribute(Bo.TITLE, "");

        Assertions.assertNotEquals(attrValue, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(app));
    }

    /**
     * <p>Тестирование получения base url приложения
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.getAppBaseUrl())</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен корректный base url приложения
     * </ol>
     */
    @Test
    void testGetAppBaseUrl()
    {
        testSimpleText(Config.get().getWebAddress(), "jsApi.getAppBaseUrl()");
    }

    /**
     * <p>Тестирование получения base url rest сервиса приложения
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.getAppRestBaseUrl())</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен корректный rest base url приложения
     * </ol>
     */
    @Test
    void testGetAppRestBaseUrl()
    {
        testSimpleText(Config.get().getWebAddress() + "services/earest", "jsApi.getAppRestBaseUrl()");
    }

    /**
     * <p>Тестирование получения идентификатора из объекта текущего пользователя
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.getCurrentUser().uuid)</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен "superUser$naumen"
     * </ol>
     */
    @Test
    void testGetCurrentUserUuid()
    {
        testSimpleText("superUser$naumen", "jsApi.getCurrentUser().uuid");
    }

    /**
     * <p>Тестирование получения локали из объекта текущего пользователя
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>document.write(jsApi.getCurrentLocale())</pre>
     *
     *   <li>{@link #testSimpleText(String, String) Действия}</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен "ru"
     * </ol>
     */
    @Test
    void testGetCurrentLocale()
    {
        testSimpleText(DSLInterface.RU_LANGUAGE, "jsApi.getCurrentLocale()");
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение по коду контента
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *      <pre>def someCode() {
     *  '{"value": "RANDOM_TEXT_HERE"}'
     *}</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byContentCode('test')
     *  .then(function (result) {
     *    document.body.innerHTML = result.value
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'RANDOM_TEXT_HERE'
     * </ol>   
     */
    @Test
    void testGettingConfigurationByContentCode()
    {
        ModuleConf module = DAOModuleConf.create("test");
        String expectedText = ModelUtils.createText(20);
        //@formatter:off
        module.setScriptBody(String.format(join(
            "def someCode() {",
            "  '{\"value\": \"%s\"}'",
            "}"
        ), expectedText));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "jsApi.configuration.byContentCode('test').then(function (result) { "
                + "document.body.innerHTML = result.value })");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение по коду контента с дополнительными аргументами
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *     <pre>
     *     import com.google.gson.GsonBuilder
     *     def someCode(num, str, nullValue, jsonObject, bool) {
     *       def gson = new GsonBuilder().create()
     *       try {
     *         assert num == RANDOM_NUMBER_HERE
     *         assert bool == RANDOM_BOOLEAN_HERE
     *         assert str == 'RANDOM_TEXT_HERE'
     *         assert jsonObject.property == 'RANDOM_TEXT_HERE'
     *         assert nullValue == null
     *         return new GsonBuilder().serializeNulls().create().toJson('Success!')
     *       } catch (Throwable e) {
     *         return gson.toJson(e.message)
     *       }
     *     }</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byContentCode('test', RANDOM_NUMBER_HERE, 'RANDOM_TEXT_HERE', null, {property: 'RANDOM_TEXT_HERE'}, RANDOM_BOOLEAN_HERE)
     *  .then(function (result) {
     *    document.body.innerHTML = result
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'Success!'
     * </ol>   
     */
    @Test
    void testGettingConfigurationByContentCodeWithArguments()
    {
        ModuleConf module = DAOModuleConf.create("test");
        Random random = new Random();
        String expectedNumber = String.valueOf(
                ThreadLocalRandom.current().nextLong(((long)Integer.MAX_VALUE) + 1, JS_MAX_SAFE_INTEGER));
        String expectedBoolean = String.valueOf(random.nextBoolean());
        String expectedText = ModelUtils.createText(20);
        String expectedObject = String.format("{property: '%s'}", expectedText);
        //@formatter:off
        module.setScriptBody(String.format(join(
            "import com.google.gson.GsonBuilder",
            "def someCode(num, str, nullValue, jsonObject, bool) {",
            "  def gson = new GsonBuilder().create()",
            "  try {",
            "    assert num == %s",
            "    assert bool == %s",
            "    assert str == '%s'",
            "    assert jsonObject.property == '%s'",
            "    assert nullValue == null",
            "    return new GsonBuilder().serializeNulls().create().toJson('Success!')",
            "  } catch (Throwable e) {",
            "    return gson.toJson(e.message)",
            "  }",
            "}"
        ), expectedNumber, expectedBoolean, expectedText, expectedText));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(
                "jsApi.configuration.byContentCode('test', %s, '%s', null, %s, %s).then(function (result) { "
                + "document.body.innerHTML = result })",
                expectedNumber, expectedText, expectedObject, expectedBoolean));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals("Success!", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение по коду контента с аргументом: строкой,
     * содержащей $
     * (т.к. $ - спец. символ в Groovy, важно, чтобы строки передавались в модуль в одинарных кавычках)
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *      <pre>import com.google.gson.GsonBuilder
     *def someCode(def param) {
     *  new GsonBuilder().create().toJson([value: param])
     *}</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byContentCode('test', 'asd$123')
     *  .then(function (result) {
     *    document.body.innerHTML = result.value
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'asd$123'
     * </ol>   
     */
    @Test
    void testGettingConfigurationByContentCodeWithDollarSignAsArgumentPart()
    {
        ModuleConf module = DAOModuleConf.create("test");
        String expectedText = "asd$123";
        //@formatter:off
        module.setScriptBody(join(
                "import com.google.gson.GsonBuilder",
                "def someCode(def param) {",
                "  new GsonBuilder().create().toJson([value: param])",
                "}"
            ));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(
                "jsApi.configuration.byContentCode('test', '%s').then(function (result) { "
                + "document.body.innerHTML = result.value })",
                expectedText));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение без привязки к коду контента
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *      <pre>def someCode() {
     *  '{"value": "RANDOM_TEXT_HERE"}'
     *}</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byDefault('test')
     *  .then(function (result) {
     *    document.body.innerHTML = result.value
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'RANDOM_TEXT_HERE'
     * </ol>
     */
    @Test
    void testGettingConfigurationByDefault()
    {
        ModuleConf module = DAOModuleConf.create("test");
        String expectedText = ModelUtils.createText(20);
        //@formatter:off
        module.setScriptBody(String.format(join(
            "def getConfiguration() {",
            "  '{\"value\": \"%s\"}'",
            "}"
        ), expectedText));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                "jsApi.configuration.byDefault('test').then(function (result) { "
                + "document.body.innerHTML = result.value })");
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение по коду контента с дополнительными аргументами
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *     <pre>
     *     import com.google.gson.GsonBuilder
     *     def getConfiguration(num, str, nullValue, jsonObject, bool) {
     *       def gson = new GsonBuilder().create()
     *       try {
     *         assert num == RANDOM_NUMBER_HERE
     *         assert bool == RANDOM_BOOLEAN_HERE
     *         assert str == 'RANDOM_TEXT_HERE'
     *         assert jsonObject.property == 'RANDOM_TEXT_HERE'
     *         assert nullValue == null
     *         return new GsonBuilder().serializeNulls().create().toJson('Success!')
     *       } catch (Throwable e) {
     *         return gson.toJson(e.message)
     *       }
     *     }</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byDefault('test', RANDOM_NUMBER_HERE, 'RANDOM_TEXT_HERE', null, {property: 'RANDOM_TEXT_HERE'}, RANDOM_BOOLEAN_HERE)
     *  .then(function (result) {
     *    document.body.innerHTML = result
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'Success!'
     * </ol>
     */
    @Test
    void testGettingConfigurationByDefaultWithArguments()
    {
        ModuleConf module = DAOModuleConf.create("test");
        Random random = new Random();
        String expectedNumber = String.valueOf(
                ThreadLocalRandom.current().nextLong(((long)Integer.MAX_VALUE) + 1, JS_MAX_SAFE_INTEGER));
        String expectedBoolean = String.valueOf(random.nextBoolean());
        String expectedText = ModelUtils.createText(20);
        String expectedObject = String.format("{property: '%s'}", expectedText);
        //@formatter:off
        module.setScriptBody(String.format(join(
            "import com.google.gson.GsonBuilder",
            "def getConfiguration(num, str, nullValue, jsonObject, bool) {",
            "  def gson = new GsonBuilder().create()",
            "  try {",
            "    assert num == %s",
            "    assert bool == %s",
            "    assert str == '%s'",
            "    assert jsonObject.property == '%s'",
            "    assert nullValue == null",
            "    return new GsonBuilder().serializeNulls().create().toJson('Success!')",
            "  } catch (Throwable e) {",
            "    return gson.toJson(e.message)",
            "  }",
            "}"
        ), expectedNumber, expectedBoolean, expectedText, expectedText));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(
                "jsApi.configuration.byDefault('test', %s, '%s', null, %s, %s).then(function (result) { "
                + "document.body.innerHTML = result })",
                expectedNumber, expectedText, expectedObject, expectedBoolean));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals("Success!", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование получения конфигурации встроенного приложение по коду контента с аргументом: строкой,
     * содержащей $
     * (т.к. $ - спец. символ в Groovy, важно, чтобы строки передавались в модуль в одинарных кавычках)
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом 'test' и содержимым:
     *      <pre>import com.google.gson.GsonBuilder
     *def getConfiguration(def param) {
     *  new GsonBuilder().create().toJson([value: param])
     *}</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>jsApi.configuration
     *  .byDefault('test', 'asd$123')
     *  .then(function (result) {
     *    document.body.innerHTML = result.value
     *  })</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *   Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен текст 'asd$123'
     * </ol>
     */
    @Test
    void testGettingConfigurationByDefaultWithDollarSignAsArgumentPart()
    {
        ModuleConf module = DAOModuleConf.create("test");
        String expectedText = "asd$123";
        //@formatter:off
        module.setScriptBody(join(
            "import com.google.gson.GsonBuilder",
            "def getConfiguration(def param) {",
            "  new GsonBuilder().create().toJson([value: param])",
            "}"
        ));
        //@formatter:on
        DSLModuleConf.add(module);

        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp, String.format(
                "jsApi.configuration.byDefault('test', '%s').then(function (result) { "
                + "document.body.innerHTML = result.value })",
                expectedText));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        content.setCode("someCode");
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(expectedText, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * <p>Тестирование отправки запроса к SMP REST API
     * <p>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00692
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$53454882
     * <p>
     * <ol>
     *   <b>Подготовка</b>
     *   <li>Добавить скриптовый модуль с кодом <code>test</code> текстом:
     *      <pre>def five() {
     *  5
     *}</pre>
     *   <li>Добавить встроенное приложение, исполняемое на клиенте, с js кодом:
     *      <pre>"jsApi.makeRequestToSmpRest('exec?func=modules.test.five&params=')
     *  .then(result => document.body.innerHTML = result)"</pre>
     *   <li>Включить добавленное встроенное приложение
     *   <li>Добавить контент типа "Встроенное приложение" c добавленным ранее приложением на карточку Компании</li><br>
     *
     *   <b>Действия</b>
     *   <li>Войти под naumen
     *   <li>Перейти на карточку Компании</li><br>
     *
     *   <b>Проверка</b>
     *   <li>Проверить, что во встроенном приложении выведен "5"
     * </ol>
     */
    @Test
    void testMakeRequestToSmpRest()
    {
        //Подготовка
        ModuleConf module = DAOModuleConf.create("test");
        //@formatter:off
        module.setScriptBody(join(
            "def five() {",
            "  5",
            "}"
        ));
        //@formatter:on
        DSLModuleConf.add(module);

        //@formatter:off
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                join(
                    "jsApi.restCall('exec?func=modules.test.five&params=')",
                    "  .then(result => document.body.innerHTML = result)"
                )
        );
        //@formatter:on
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals("5", GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }

    /**
     * Тестирование метода JsApi <code>jsApi.getBrowserType()</code>, возвращающий информацию о месте запуска
     * встроенного приложения: "webView", если это мобильный клиент и "browser", если это браузер.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$102631562
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать модель встроенного приложения application, вызывающее метод <code>jsApi.getBrowserType()</code> и
     * выводящее его ответ</li>
     * <li>{@link #testSimpleText(String, String) Действия и проверки}</li>
     * <li>Проверить, что во встроенном приложении выведен текст - "browser", поскольку встроенное приложение
     * запущено в браузере</li>
     * </ol>
     */
    @Test
    void testGetBrowserType()
    {
        testSimpleText("browser", "jsApi.getBrowserType()");
    }

    /**
     * Тестирование метода <code>jsApi.getWebViewType()</code>, возвращающий тип WebView, в котором запущено встроенное
     * приложение
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$102631562
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать модель встроенного приложения application, вызывающее метод <code>jsApi.getWebViewType()</code> и
     * выводящее его ответ</li>
     * <li>{@link #testSimpleText(String, String) Действия и проверки}</li>
     * <li>Проверить, что во встроенном приложении выведен текст - "null", поскольку встроенное приложение
     * запущено не в мобильном клиенте, а в браузере</li>
     * </ol>
     */
    @Test
    void testGetWebViewType()
    {
        testSimpleText("null", "jsApi.getWebViewType()");
    }

    /**
     *  <li>Включить добавленное встроенное приложение
     *  <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку класса
     *  Компании</li><br>
     *
     *  <b>Действия</b>
     *  <li>Войти под naumen
     *  <li>Перейти на карточку Компании</li><br>
     */
    private void testSimpleText(String expected, String js)
    {
        //Подготовка
        File applicationFile = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format("document.write(%s)", js));
        EmbeddedApplication model = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                applicationFile.getAbsolutePath());
        DSLEmbeddedApplication.add(model);

        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), model);
        DSLContent.add(content);

        GUILogon.asNaumen();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(expected, GUIEmbeddedApplication.getEmbeddedApplicationContent(content));
    }
}
