package ru.naumen.selenium.cases.operator.session;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.content.GUISimpleList;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на время сессии
 *
 * <AUTHOR>
 * @since 21.10.2015
 */
public class SessionTimeTest extends AbstractTestCase
{
    @AfterClass
    public static void postTestAction()
    {
        tester.enableWaitAsyncCall();
    }

    @BeforeClass
    public static void prepareFixture()
    {
        tester.disableWaitAsyncCall();
    }

    /**
     * Тестирование записи события выхода пользователя из системы в историю изменений сотрудника
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00307
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$74153444
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензрованного сотрудника employee</li>
     * <li>На карточку сотрудника вывести контент eventList типа "История изменений объекта"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Выйти из системы</li>
     * <li>Войти в систему под другим сотрудником</li>
     * <li>Перейти на карточку сотружника employee</li>
     * <br>
     * <b>Проверка</b>
     * <li>В истории изменений eventList последнее событие - "Пользователь 'employee' вышел из системы"</li>
     * </ol>
     */
    @Test
    public void testLogSimpleUserLogout()
    {
        // Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
        ContentForm eventList = DAOContentCard.createEventList(SharedFixture.employeeCase().getFqn(),
                PresentationContent.ADVLIST);
        DSLContent.add(eventList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));
        // Выполнение действий
        GUILogon.login(employee);
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        // Проверка
        GUIEventList.assertPartOfEventMessages(eventList, 0, 1, "Выход из системы",
                String.format("Пользователь '%s' вышел из системы", employee.getLogin()));
    }

    /**
     * Тестирование времени сессии для конкурентной лицензии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * http://sd-jira.naumen.ru/browse/NSDPRD-4434
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Загрузить файл лицензии, в котором время сессии для конкурентной лицензии 1 минута, для именной 5 минут,
     * для нелицензированных пользователей 1 минута</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать контент типа "Список вложенных объектов" contentChildObjectLis в карточке компании для ouCase</li>
     * <li>Создать тип сотрудника emloyeeCase</li>
     * <li>Создать контент eventList типа "История изменений объекта" на карточке сотрудника типа employeeCase</li>
     * <li>Создать сотрудников empl1 с конкурентной лицензией типа employeeCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логин в систему под empl1</li>
     * <li>Перейти на карточку на компании, в контенте contentChildObjectLis нажать на "Добавить"</li>
     * <li>На форме заполнить поле Название</li>
     * <li>Ждать 1 минуту</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * <li>Перейти на карточку сотрудника empl1</li>
     * <li>Проверить, что в истории изменений eventList последнее событие - "Пользователь 'empl1' вышел из системы"</li>
     * </ol>
     */
    @Test
    public void testTimeSessionsConcurrentLicenseGroup()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE_TIME_SESSION_PATH);
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo empl1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        empl1.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(empl1);

        ContentForm contentChildObjectList = DAOContentCard.createChildObjectList(DAORootClass.create().getCode(),
                DAOOuCase.createClass(), ouCase);
        ContentForm eventList = DAOContentCard.createEventList(employeeCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(contentChildObjectList, eventList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        //Выполнение действия
        GUILogon.login(empl1);
        Bo ou = DAOOu.create(ouCase);
        GUINavigational.goToOperatorUI();
        GUISimpleList.clickAdd(contentChildObjectList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIBo.fillOuMainFields(ou);
        WaitTool.waitMills(60000);
        GUIForm.applyFormAndAssertDialog("Сессия завершена. Обычно это происходит, если вы длительное время не "
                                         + "совершали действий в системе или зашли с другого устройства.");
        GUIForm.confirmByYes();

        GUILogon.asSuper();
        GUIBo.goToCard(empl1);
        GUIEventList.assertPartOfEventMessages(eventList, 0, 1, "Выход из системы",
                String.format("Пользователь '%s' вышел из системы", empl1.getLogin()));
    }

    /**
     * Тестирование времени сессии для разных типов лицензий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * http://sd-jira.naumen.ru/browse/NSDPRD-4434
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Загрузить файл лицензии, в котором время сессии для конкурентной лицензии 1 минута, для именной 5 минут,
     * для нелицензированных пользователей 1 минута</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать контент типа "Список вложенных объектов" contentChildObjectLis в карточке компании для ouCase</li>
     * <li>Создать тип сотрудника emloyeeCase</li>
     * <li>Создать контент eventList типа "История изменений объекта" на карточке сотрудника типа employeeCase</li>
     * <li>Создать сотрудников empl1 с конкурентной лицензией типа employeeCase</li>
     * <li>Создать сотрудников empl2 с именной лицензией типа employeeCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логин в систему под нелицензированным пользователем</li>
     * <li>Перейти на карточку на компании</li>
     * <li>Ждать 1 минуту</li>
     * <li>Обновить страницу</li>
     * <li>Проверить что произожел редирект на страницу авторизации</li>
     * <li>Логин в систему под empl2</li>
     * <li>Перейти на карточку на компании, в контенте contentChildObjectLis нажать на "Добавить"</li>
     * <li>На форме заполняем поле Название</li>
     * <li>Ждать 1 минуту</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить, что форма закрылась</li>
     * </ol>
     */
    @Test
    public void testTimeSessionsDifferentLicenseGroup()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE_TIME_SESSION_PATH);
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo empl1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        empl1.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        Bo empl2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        empl2.setLicenseCode(DAOBo.NAMED_LICENSE_SET);
        DSLBo.add(empl1, empl2);

        ContentForm contentChildObjectList = DAOContentCard.createChildObjectList(DAORootClass.create().getCode(),
                DAOOuCase.createClass(), ouCase);
        ContentForm eventList = DAOContentCard.createEventList(employeeCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(contentChildObjectList, eventList);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        //Выполнение действия
        GUILogon.asUnlicensed();
        GUINavigational.goToOperatorUI();
        WaitTool.waitMills(60000);
        tester.refresh();
        WaitTool.waitMills(3000);
        GUILogon.assertLoginPage();

        GUILogon.login(empl2);
        GUISimpleList.clickAdd(contentChildObjectList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        Bo ou = DAOOu.create(ouCase);
        GUIBo.fillOuMainFields(ou);
        WaitTool.waitMills(60000);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(ou);
    }

    /**
     * Тестирование времени сессии, если вход выполнен через "Администрирование -> Вход под сотрудником"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * http://sd-jira.naumen.ru/browse/NSDPRD-4434
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Загрузить файл лицензии, в котором время сессии для конкурентной лицензии 1 минута</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать контент типа "Список вложенных объектов" contentChildObjectLis в карточке компании для ouCase</li>
     * <li>Создать тип сотрудника emloyeeCase</li>
     * <li>Создать сотрудников empl1 с конкурентной лицензией типа employeeCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Логин в систему под суперпользователем</li>
     * <li>Перейти на карточку "Администрирование"</li>
     * <li>Открыть форму "Вход под сотрудником"</li>
     * <li>Выбрать сотрудника empl1</li>
     * <li>Нажать на сохранить</li>
     * <li>Перейти на карточку на компании, в контенте contentChildObjectLis нажать на "Добавить"</li>
     * <li>На форме заполнить поле Название</li>
     * <li>Ждать 1 минуту</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить, что появилось всплывающее сообщение, оповещающее о не возможности выполнения операции</li>
     * </ol>
     */
    @Test
    public void testTimeSessionsLoginFromAdmin()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE_TIME_SESSION_PATH);
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Bo empl1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        empl1.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(empl1);

        ContentForm contentChildObjectLis = DAOContentCard.createChildObjectList(DAORootClass.create().getCode(),
                DAOOuCase.createClass(), ouCase);
        DSLContent.add(contentChildObjectLis);

        //Выполнение действия
        GUILogon.asSuper();
        GUIAdmin.goToCard();
        GUIAdmin.openLoginForm();
        GUIAdmin.selectUserInSelect(SharedFixture.ou(), empl1);
        GUIForm.applyForm();

        GUINavigational.goToOperatorUI();
        Bo ou = DAOOu.create(ouCase);
        GUISimpleList.clickAdd(contentChildObjectLis);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIBo.fillOuMainFields(ou);
        WaitTool.waitMills(60000);
        GUIForm.clickApply();
        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.confirmByYes();
    }
}
