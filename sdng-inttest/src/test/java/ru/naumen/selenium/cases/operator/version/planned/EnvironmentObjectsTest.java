package ru.naumen.selenium.cases.operator.version.planned;

import java.util.Arrays;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLBranch;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBranchCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование операций с объектами окружения
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
 *
 * <AUTHOR>
 * @since 22.07.2020
 */
public class EnvironmentObjectsTest extends AbstractTestCase
{
    private static MetaClass userClass;
    private static MetaClass userCase1;
    private static Attribute userClassLinkAttr, objectLinkAttr;
    private static ContentForm groupAttrContent;
    private static Bo branch;
    private static Bo boMain;
    private static Bo boRel;
    private static GroupAttr groupAttr;
    private static String boRelVerUUID, archBoRelVerUUID;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Установить лицензию с модулем Планового версионирования</li>
     * <li>Создать пользовательский класс userClass с возможностью назначения ответственного
     *     и возможностью создания плановых версий</li>
     * <li>Создать 2 типа userCase1 и userCase2 класса userClass</li>
     * <li>Создать тип branchCase класса Ветка</li>
     * <li>Создать объект branch типа branchCase</li>
     * <li>Создать атрибут userClassLinkAttr типа НБО на объект класса userClass</li>
     * <li>Создать атрибут objectLinkAttr типа СБО на объект класса userClass</li>
     * <li>Создать группу атрибутов groupAttr с атрибутом userClassLinkAttr</li>
     * <li>Создать Контент-Параметры объекта с группой атрибутов groupAttr</li>
     * <li>Создать объекты boRel, boMain (userClassLinkAttr ссылается на boRel и objectLinkAttr ссылается на boRel)
     * типа userCase2</li>
     * <li>Добавить boMain в ветку, т.е. создать версию boMainVer, при этом будет создан
     *     объект окружения boRelVer</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.PLANNED_VERSION_LICENSE);

        userClass = DAOUserClass.createWith()
                .responsible().plannedVersions().inSelf().create();
        userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass branchClass = DAOBranchCase.createClass();
        MetaClass branchCase = DAOBranchCase.create(branchClass);
        DSLMetaClass.add(userClass, userCase1, userCase2, branchCase);

        branch = DAOBranch.create(branchCase);
        DSLBo.add(branch);

        userClassLinkAttr = DAOAttribute.createBoLinks(userClass, userClass);
        userClassLinkAttr.setComplexRelation(Boolean.TRUE.toString());
        objectLinkAttr = DAOAttribute.createObjectLink(userClass, userClass, null);
        DSLAttribute.add(userClassLinkAttr, objectLinkAttr);
        groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, userClassLinkAttr);
        groupAttrContent = DAOContentCard.createPropertyList(userClass, groupAttr);
        DSLContent.add(groupAttrContent);

        boRel = DAOUserBo.create(userCase2);
        Bo archBoRel = DAOUserBo.create(userCase2);
        DSLBo.add(boRel, archBoRel);
        DSLBo.archive(archBoRel);
        boMain = DAOUserBo.create(userCase2);
        userClassLinkAttr.setValue(Json.GSON.toJson(Arrays.asList(boRel.getUuid(), archBoRel.getUuid())));
        objectLinkAttr.setValue(boRel.getUuid());
        DAOBo.addAttributeToModel(boMain, userClassLinkAttr, objectLinkAttr);
        DSLBo.add(boMain);
        //noinspection
        DSLBranch.addToBranch(branch, boMain).get(0);
        boRelVerUUID = DSLBranch.getObjVers(branch, boRel);
        archBoRelVerUUID = DSLBranch.getObjVers(branch, archBoRel);
    }

    /**
     * Тестирование запрета редактирования объектов окружения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать контент Список связанных объектов по атрибуту userClassLinkAttr</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти в режим планирования изменений в ветку branch</li>
     * <li>Выполнить скрипт:<pre>utils.edit('boRelVer', ['title':'новое название'])</pre>
     *     и проверить, что появилась ошибка
     *     "userClass '%название%' не может быть изменен по следующим причинам:
     *     1. Объект окружения недоступен для редактирования в режиме планирования изменений.
     *     Для возможности редактирования добавьте объект в ветку."</li>
     * <li>Перейти на форму редактирования boRelVer</li>
     * <li>Отредактировать поле title и проверить, что появилась ошибка
     *     "userClass '%название%' не может быть изменен по следующим причинам:
     *     1. Объект окружения недоступен для редактирования в режиме планирования изменений.
     *     Для возможности редактирования добавьте объект в ветку."</li>
     * </ol>
     */
    @Test
    public void testCantEditEnvironmentObjects()
    {
        // Подготовка
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(
                userClass.getFqn(), true, PositionContent.FULL, PresentationContent.ADVLIST, String.format("%s@%s",
                        userClass.getFqn(), userClassLinkAttr.getCode()), DAOGroupAttr.createSystem());
        DSLContent.add(relatedObjectList);

        String errorMsgTmpl = userClass.getTitle() + " '%s' не может быть изменен по следующим причинам:\n"
                              + " 1. Объект окружения недоступен для редактирования в режиме планирования изменений. "
                              + "Для возможности редактирования добавьте объект в ветку.";

        // Действия и проверки
        String newTitle = ModelUtils.createTitle();
        new ScriptRunner(
                String.format("api.branch.with('%s', {\n"
                              + "    utils.edit('%s', ['title':'%s'])\n"
                              + "})",
                        branch.getUuid(), boRelVerUUID, newTitle))
                // В сообщении скрипта таб вместо пробела
                .assertScriptError(String.format(errorMsgTmpl.replace(" 1.", "\t1."), newTitle));

        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);

        // Проверка изменения системного атрибута
        GUIBo.goToEditForm(boRelVerUUID);
        GUIForm.fillTitle(newTitle);
        GUIForm.applyFormAssertError(String.format(errorMsgTmpl, newTitle));
        GUIForm.cancelForm();
    }

    /**
     * Тестирование возможности перемещения объекта окружения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95936560
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В группу атрибутов groupAttr добавить системный атрибут parent</li>
     * <li>Создать объект otherBo типа userCase1</li>
     * <li>Добавить объект otherBo в ветку branch, получим otherBoVer</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти в режим планирования изменений</li>
     * <li>Перейти на карточку объекта окружения boRelVer</li>
     * <li>Переместить объект в otherBoVer</li>
     * <li>Проверить, что атрибут parent изменился</li>
     * </ol>
     */
    @Test
    public void testCanMoveEnvironmentObject()
    {
        // Подготовка
        Attribute parentAttr = SysAttribute.parent(userClass);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { parentAttr }, new Attribute[] {});

        Bo otherBo = DAOUserBo.create(userCase1);
        DSLBo.add(otherBo);

        Bo otherBoVer = DSLBranch.addToBranch(branch, otherBo).get(0);

        // Действия и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);

        GUIBo.goToCard(boRelVerUUID);

        // Проверить, что объект окружения можно переместить
        GUIButtonBar.move();
        new BoTree(GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_PROPERTY,
                true, GUIForm.PARENT_FIELD)
                .setElementInSelectTree(otherBoVer);
        GUIForm.applyModalForm();

        GUIPropertyList.assertPropertyListAttributeValue(
                groupAttrContent, true, parentAttr, otherBo.getTitle());
    }

    /**
     * Тестирование возможности на карточке версии изменить значение обратной ссылки
     * на объект окружения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95936560
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать атрибут backLinkAttr типа Обратная ссылка для атрибута userClassLinkAttr
     *     и добавить его в группу атрибутов groupAttr</li>
     * <li>Создать объект otherBo типа userCase1</li>
     * <li>Добавить объект otherBo в ветку branch, получим otherBoVer</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти на карточку otherBoVer с подтверждением перехода в ветку</li>
     * <li>Добавить значение boRelVer в обратную ссылку backLinkAttr</li>
     * <li>Проверить, что значение добавилось</li>
     * <li>Перейти на карточку объекта окружения boRelVer</li>
     * <li>Проверить, что значение ссылки userClassLinkAttr это boMainVer</li>
     * </ol>
     */
    @Test
    public void testCanEditBackLinkToEnvironmentObject()
    {
        // Подготовка
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userClass, userClassLinkAttr);
        DSLAttribute.add(backLinkAttr);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { backLinkAttr }, new Attribute[] {});

        Bo otherBo = DAOUserBo.create(userCase1);
        DSLBo.add(otherBo);

        Bo otherBoVer = DSLBranch.addToBranch(branch, otherBo).get(0);

        // Действия и проверки
        GUILogon.asTester();

        // Проверить, что можно изменить значение обратной ссылки с карточки версии
        GUIBo.goToCardWithContinueDialog(otherBoVer);
        GUIForm.applyInfoDialog();
        GUIContent.clickEdit(groupAttrContent);
        new BoTree(
                GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_PROPERTY,
                false, backLinkAttr.getCode())
                .setElementInSelectTree(boRelVerUUID);
        GUIForm.applyForm();
        GUIPropertyList.assertPropertyListAttributeValue(
                groupAttrContent, true, backLinkAttr, boRel.getTitle());
        GUIBo.goToCard(boRelVerUUID);
        GUIPropertyList.assertPropertyListAttributeValue(
                groupAttrContent, true, userClassLinkAttr, otherBo.getTitle());
    }

    /**
     * Тестирование отсутствия элементов редактирования для контента параметры связанного объекта,
     * сли этот объект является объектом окружения
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98352323
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать контент relObjPropList "Параметры связанного объектов" по атрибуту objectLinkAttr</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти на карточку объекта boMain</li>
     * <li>Проверить отсутствие кнопки редактирования в контенте relObjPropList</li>
     * </ol>
     */
    @Test
    public void testAbsentEditLinkInRelObjPropListIfEnvironmentObject()
    {
        // Подготовка
        ContentForm relObjPropList = DAOContentCard.createRelObjPropList(userClass, objectLinkAttr, groupAttr);
        DSLContent.add(relObjPropList);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(boMain);
        GUIContent.assertPresent(relObjPropList);

        // Проверка отсутствия кнопки "Редактировать"
        GUIPropertyList.assertEditLinkAbsence(relObjPropList);
    }

    /**
     * Тестирование отсутствия элементов редактирования для объектов окружения
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98352323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать контент relatedObjectList Список связанных объектов по атрибуту userClassLinkAttr</li>
     * <li>Перейти в интерфейс администратора</li>
     * <li>Перейти в настройку действий списка relatedObjectList (иконка шестеренки). Снять галку системной логики и
     * переместить кнопку “Добавить/удалить связи” на панель действий</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти в режим планирования изменений в ветку branch</li>
     * <li>Перейти на карточку boRelVer</li>
     * <li>Проверить отсутствие кнопки редактирования ссылочного атрибута userClassLinkAttr</li>
     * <li>Проверить отсутствие кнопок "Изменить тип" и "Изменить ответственного" на карточке</li>
     * <li>Проверить отсутствие кнопки "Поместить в архив" на карточке</li>
     * <li>Проверить отсутствие кнопки "Добавить связь"  с объектом boMainVer</li>
     * <li>Проверить отсутствие кнопки "Добавить/удалить связи"</li>
     * </ol>
     */
    @Test
    public void testAbsentElementsForEditEnvironmentObjects()
    {
        // Подготовка
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(
                userClass.getFqn(), true, PositionContent.FULL, PresentationContent.ADVLIST, String.format("%s@%s",
                        userClass.getFqn(), userClassLinkAttr.getCode()), DAOGroupAttr.createSystem());
        DSLContent.add(relatedObjectList);

        GUIAdvListEditableToolPanel panel = relatedObjectList.advlist().editableToolPanel();
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(relatedObjectList);
        panel.setUseSystemSettings(false);
        panel.rightClickTool(GUIButtonBar.BTN_ADD_DELETE_OBJS);
        panel.clickAddContextMenuOption();
        GUIForm.applyForm();
        GUILogon.logout();

        //Действия и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);
        GUIBo.goToCard(boRelVerUUID);

        // Проверка отсутствия кнопки изменения ссылочного атрибута
        GUIPropertyList.assertEditLinkAbsence(groupAttrContent);

        // Проверка отсутствия кнопок изменения типа и ответственного
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_CHANGE_CASE, GUIButtonBar.BTN_CHANGE_RESPONSIBLE);

        // Проверка отсутствия кнопки поместить в архив
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_REMOVE);

        // Проверка отсутствия кнопки добавления связи
        relatedObjectList.advlist().toolPanel().asserts().buttonsAbsence(GUIContent.LINK_ADD_LINK);

        // Проверка отсутствия кнопки добавления/удаления связи
        relatedObjectList.advlist().toolPanel().asserts().buttonsAbsence(GUIContent.LINK_ADD_DEL_OBJ);
    }

    /**
     * Тестирование отсутствия кнопки "Восстановить из архива" для объекта окружения находящегося в архиве
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98352323
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти в режим планирования изменений в ветку branch</li>
     * <li>Перейти на карточку archBoRelVerUUID</li>
     * <li>Проверить отсутствие кнопки "Восстановить из архива" на карточке</li>
     * </ol>
     */
    @Test
    public void testAbsentRestoreButtonForEnvironmentObject()
    {
        //Действия и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);
        GUIBo.goToCard(archBoRelVerUUID);

        // Проверка отсутствия кнопки восстановить из архива
        GUIButtonBar.assertButtonsAbsence(GUIButtonBar.BTN_RESTORE);
    }
}
