package ru.naumen.selenium.cases.operator.security;

import java.util.Arrays;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на права пользователя на добавление БО различных классов
 * По мотивам дефекта http://sd-jira.naumen.ru/browse/NSDPRD-3291
 *
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
 *
 * <AUTHOR>
 * @since 31 июля 2015 г.
 */
public class AddObjectSecurityTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase1, userCase11, userCase2, userCase12;
    private static SecurityProfile profile;
    private static SecurityGroup group;
    private static Bo employee;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать тип пользовательского класса userClass userCase1</li>
     * <li>Создать тип пользовательского класса userClass userCase2</li>
     * <li>В типе userCase1 создать подтипы userCase11, userCase12</li>
     * <li>Создать группу пользователей group</li>
     * <li>Создать профиль profile (Роль: "Сотрудник", Группа: group)</li>
     * <li>Выдать профилю profile все права в типе userCase1</li>
     * <li>Выдать профилю profile все права в компании</li>  
     * <li>Создать лицензированного сотрудника employee</li>
     * <li>Добавить сотрудника employee в группу пользователей group</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        userCase1 = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase1, userCase2);

        userCase11 = DAOUserCase.create(userCase1);
        userCase12 = DAOUserCase.create(userCase1);
        DSLMetaClass.add(userCase11, userCase12);

        group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);

        profile = DAOSecurityProfile.create(true, group, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userCase1);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, DAORootClass.create());

        MetaClass emplCase = SharedFixture.employeeCase();
        employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        DSLSecurityGroup.addUsers(group, employee);
    }

    /**
     * Тестирование отсутствия ссылки "Добавить" в случае если для выбора на форме добавления
     * вложенного объекта недоступен ни один тип
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс nestedClass вложенный в класс userClass</li>
     * <li>В классе nestedClass создать типы nestedCase1 и nestedCase2</li>
     * <li>Выдать профилю profile все права в типе nestedCase1</li>
     * <li>В типе nestedCase1 ограничить значение системного атрибута "Родитель" типом userCase2</li>
     * <li>На карточку типа userCase1 вывести контент childObjList - список вложенных объектов класса nestedClass</li>
     * <li>Создать объект userBo типа userCase1</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку объекта userBo</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке вложенных объектов childObjList отсутствует кнопка "Добавить"</li>
     * </ol>
     */
    @Test
    public void testAbsenseOfAddLinkInChildList()
    {
        //Подготовка
        MetaClass nestedClass = DAOUserClass.create(userClass.getFqn());
        DSLMetaClass.add(nestedClass);

        MetaClass nestedCase1 = DAOUserCase.create(nestedClass);
        MetaClass nestedCase2 = DAOUserCase.create(nestedClass);
        DSLMetaClass.add(nestedCase1, nestedCase2);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, nestedCase1);

        Attribute parent = SysAttribute.parent(nestedCase1);
        DSLAttribute.editPermittedLinks(parent, userCase2);

        ContentForm childObjList = DAOContentCard.createChildObjectList(userCase1, nestedClass);
        DSLContent.add(childObjList);

        Bo userBo = DAOUserBo.create(userCase1);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        //Проверка
        GUIContent.assertLinkAbsense(childObjList, GUIContent.LINK_ADD);
    }

    /**
     * Тестирование отсутствия ссылки "Добавить" в случае если для выбора на форме добавления
     * недоступен ни один тип
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточку компании вывести список объектов objectList класса userClass, отображаемый тип userCase2</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку компании</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке объектов objectList отсутствует кнопка "Добавить"</li>
     * </ol>
     */
    @Test
    public void testAbsenseOfAddLinkInObjectList()
    {
        MetaClass rootClass = DAORootClass.create();
        ContentForm objectList = DAOContentCard.createObjectList(rootClass.getFqn(), userClass, userCase2);
        DSLContent.add(objectList);

        GUILogon.login(employee);
        GUIBo.goToCard(SharedFixture.root());

        GUIContent.assertLinkAbsense(objectList, GUIContent.LINK_ADD);
    }

    /**
     * Тестирование отсутствия ссылки "Добавить" в случае если для выбора на форме добавления
     * связанного объекта недоступен ни один тип
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Выдать профилю profile все права в типе отдела ouCase</li>
     * <li>В пользовательском классе добавить атрибут directLink типа ссылка на отдел</li>
     * <li>В типе отдела создать обратную ссылку backLink (прямая ссылка: directLink, 
     *     возможные типы: userCase2, userCase11)</li>
     * <li>На карточку отдела типа ouCase вывести контент relObjectList типа "Список связанных объектов" по атрибуту
     * backLink
     *     Тип отображаемых объектов: userCase2</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке связанных объектов relObjectList отсутствует кнопка "Добавить"</li>
     * </ol>
     */
    @Test
    public void testAbsenseOfAddLinkInRelObjectList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, ouCase);

        Attribute directLink = DAOAttribute.createObjectLink(userClass, DAOOuCase.createClass(), null);
        DSLAttribute.add(directLink);

        Attribute backLink = DAOAttribute.createBackBOLinks(ouCase.getFqn(), directLink, userCase2, userCase11);
        DSLAttribute.add(backLink);

        ContentForm relObjectList = DAOContentCard.createRelatedObjectList(ouCase.getFqn(), ouCase.getFqn() + "@"
                                                                                            + backLink.getCode(),
                userCase2);
        DSLContent.add(relObjectList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.login(employee);

        GUIBo.goToCard(ou);

        //Проверка
        GUIContent.assertLinkAbsense(relObjectList, GUIContent.LINK_ADD);
    }

    /**
     * Тестирование добавления объекта в случае если у пользователя отсутствуют права на добавление 
     * в классе, но присутствуют права на добаление в типе 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточку компании вывести списов объектов класса userClass list</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке объектов list нажимаем кнопку "Добавить"</li>
     * <li>В открывшейся форме добавления выбираем тип userCase11</li>
     * <li>Заполняем атрибут "Название"</li>
     * <li>Нажимаем "Сохранить"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Форма добавления успешно закрылась</li>
     * </ol>
     */
    @Test
    public void testAddBoWithNoRightsInClass()
    {
        //Подготовка
        ContentForm list = DAOContentCard.createObjectList(DAORootClass.create().getFqn(), userClass);
        DSLContent.add(list);

        //Выполнение действий
        GUILogon.login(employee);

        GUIBo.goToCard(SharedFixture.root());

        GUIContent.clickAdd(list);

        GUIBo.selectCaseOnAddForm(userCase11.getFqn());

        Bo userBo = DAOUserBo.create(userCase11);
        GUIBo.fillMainFields(userBo);

        //Проверка
        GUIForm.applyForm();

        GUIBo.setUuidByUrl(userBo);
    }

    /**
     * Тестирование неотображения формы добавления, настроенной в классе в случае если 
     * у пользователя отсутствуют права на добавление объекта в классе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В типе userCase1 создать строковый атрибут attr</li>
     * <li>Добавить атрибут attr в группу "Системные атрибуты" типа userCase1</li>
     * <li>На карточку типа userCase1 вывести контент propertyList типа "Параметры объекта" 
     *     (группа атрибутов: "Системные атрибуты")</li>
     * <li>На форму добавления типа userCase1 вывести контент editablePropertyList типа "Параметры объекта на форме"
     *     (группа атрибутов: "Системные атрибуты")</li>
     * <li>На форму добавления типа userCase11 вывести список файлов fileList</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на форму добаления объекта пользовательского класса userClass
     *     при этом в качестве доступных для выбора типов в url передаем userCase1, userCase11 и userCase2</li>
     * <li>Проверяем что на форме добавления для выбора доступны только типы userCase1 и userCase11</li>
     * <li>Проверяем что на форме добавления присутствует контент editablePropertyList</li>
     * <li>Проверяем что на форме добавления отсутствует контент fileList</li>
     * <li>В списке выбора типа выбираем тип userCase11</li>
     * <li>Проверяем что на форме добавления присутствуют контенты editablePropertyList и fileList</li>
     * <li>В списке выбора типа выбираем [не указано]</li>
     * <li>Проверяем что на форме добавления присутствует контент editablePropertyList</li>
     * <li>Проверяем что на форме добавления отсутствует контент fileList</li>
     * <li>В списке выбора типа выбираем тип userCase1</li>
     * <li>Заполняем атрибут "Название"</li>
     * <li>Заполняем атрибут attr</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что на открывшейся карточке объекта значение атрибута attr совпадает с введенным на форме</li>
     * </ol>
     */
    @Test
    public void testAddFormWithoutRightsInClass()
    {
        //Подготовка
        Attribute attr = DAOAttribute.createString(userCase1);
        DSLAttribute.add(attr);

        GroupAttr sysGroup = DAOGroupAttr.createSystem(userCase1);
        DSLGroupAttr.edit(sysGroup, new Attribute[] { attr }, new Attribute[0]);

        ContentForm propertyList = DAOContentCard.createPropertyList(userCase1, sysGroup);
        DSLContent.add(propertyList);

        ContentForm editablePropertyList = DAOContentAddForm.createEditablePropertyList(userCase1, sysGroup);
        DSLContent.add(editablePropertyList);

        ContentForm fileList = DAOContentAddForm.createFileList(userCase11.getFqn());
        DSLContent.add(fileList);

        //Выполнение действий и проверки
        GUILogon.login(employee);

        Bo userBo = DAOUserBo.create(userCase1);
        GUIBo.goToAddForm(userBo, userCase1.getFqn(), userCase11.getFqn(), userCase2.getFqn());

        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(userCase1.getTitle(), userCase11.getTitle()), false, true, true);

        GUIContent.assertPresent(editablePropertyList);
        GUIContent.assertAbsence(fileList);

        GUIBo.selectCaseOnAddForm(userCase11.getFqn());

        GUIContent.assertPresent(editablePropertyList);
        GUIContent.assertPresent(fileList);

        GUIBo.selectCaseOnAddForm(null);

        GUIContent.assertPresent(editablePropertyList);
        GUIContent.assertAbsence(fileList);

        GUIBo.fillMainFields(userBo);

        GUIBo.selectCaseOnAddForm(userCase1.getFqn());
        GUIBo.fillMainFields(userBo);

        attr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(attr, attr.getValue());

        GUIForm.applyForm();

        GUIBo.setUuidByUrl(userBo);

        GUIPropertyList.assertPropertyListAttribute(propertyList, attr);
    }

    /**
     * Тестирование добавления запроса при отсутствии прав на добавления запроса для отдела
     * в классе "Запрос"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип запроса scCase1 и в нет подтип scCase2</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Сделать отдел ou получателем соглашения agreement</li> 
     * <li>Выдать профилю profile все права в типах ouCase, scCase1 и scCase2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>Нажимаем кнопку "Добавить запрос"</li>
     * <li>На форме добавления запроса выбираем согдашение agreement</li>
     * <li>Проверяем что в списке выбора типа запроса присутствуют только типы scCase1 и scCase2</li>
     * <li>В списке выбора типа выбираем тип userCase2</li>
     * <li>Заполняем остальные обязательные поля на форме</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что форма добавления успешно закрылась</li>
     * </ol>
     */
    @Test
    public void testAddScWithNoRightsInClass()
    {
        //Подготовка
        MetaClass scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase1);

        MetaClass scCase2 = DAOScCase.create(scCase1);
        DSLMetaClass.add(scCase2);

        MetaClass ouCase = SharedFixture.ouCase();
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addRecipients(ou, agreement);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, ouCase);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, scCase1, scCase2);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(ou);

        Bo sc = DAOSc.create(scCase2, ou, agreement, SharedFixture.timeZone());

        GUIButtonBar.addSC();
        GUISc.selectAssociation(agreement, null);
        GUISc.assertScCasesPresent(scCase1, scCase2);

        GUISc.selectScCase(scCase2);

        GUIBo.fillScMainFields(sc);

        GUIForm.applyForm();

        GUIBo.setUuidByUrl(sc);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов на форме добавления,
     * вызванной по общей кнопке "Добавить"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В типе userCase1 создать подтип userCase13</li>
     * <li>Забрать у профиля profile право на добавления объектов типа userCase13</li>
     * <li>Настроить в верхнем меню общую кнопку добавления объектов типов userCase1, userCase11, userCase13,
     * userCase2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Кликаем по кнопке верхнего меню addUserBo</li>
     * <li>Проверяем, что в открывшемся подменю отсутствует пункт добавления объектов типа userCase2</li>
     * <li>Кликаем по кнопке добавления объектов типа userCase1</li>
     * <li>Проверяем что в списке доступных типов на форме добавления присутствуют только типы userCase1 и
     * userCase11</li>
     * </ol>
     */
    @Test
    public void testPossibleCasesOnFastCreationForm()
    {
        //Подготовка
        MetaClass userCase13 = DAOUserCase.create(userCase11);
        DSLMetaClass.add(userCase13);

        DSLSecurityProfile.removeRights(userCase13, profile, AbstractBoRights.ADD);

        MenuItem addUserBo = DAOMenuItem.createAddButton(true, userCase1, userCase11, userCase13, userCase2);
        DSLMenuItem.add(addUserBo);

        DSLNavSettings.editVisibilitySettings(true, true);

        //Выполнение действий и проверки
        GUILogon.login(employee);

        GUINavSettingsOperator.clickMenuItem(addUserBo.getCode());
        GUINavSettingsOperator.assertMenuItemExists(false, userCase2.getFqn());
        GUINavSettingsOperator.clickMenuItem(userCase1.getFqn());

        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(userCase1.getTitle(), userCase11.getTitle()), false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов множеством разрешенных
     * типов атрибута "Родитель" , по которому формируется контент "Список вложенных объектов"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <br>
     * <ol>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс nestedClass вложенный в класс userClass</li>
     * <li>В пользовательском классе nestedClass создать типы userCase1..4</li>
     * <li>Выдать профилю profile все права, в типах nestedCase1..3</li>
     * <li>В типе nestedCase3 ограничить значение системного атрибута "Родитель" типом userCase2</li>
     * <li>На карточку типа userCase1 вывести контент childObjList - список вложенных объектов класса nestedClass</li>
     * <li>Создать объект userBo типа userCase1</li>
     * <br>
     * <b>Выполнение действий </b>
     * <ol>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку объекта userBo</li>
     * <li>В списке вложенных объектов childObjList нажимаем кнопку "Добавить"</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке типов, доступных для выбора на форме добавления, отображаются только типы nestedCase1 и
     * nestedCase2</li>
     * </ol>
     */
    @Test
    public void testPossibleCasesRestictedByParentAtribute()
    {
        //Подготовка
        MetaClass nestedClass = DAOUserClass.create(userClass.getFqn());
        DSLMetaClass.add(nestedClass);

        MetaClass[] nestedCases = new MetaClass[4];
        for (int i = 0; i < 4; ++i)
        {
            nestedCases[i] = DAOUserCase.create(nestedClass);
        }

        DSLMetaClass.add(nestedCases);

        for (int i = 0; i < 3; ++i)
        {
            DSLSecurityProfile.grantAllPermissionsForCase(profile, nestedCases[i]);
        }

        Attribute parent = SysAttribute.parent(nestedCases[2]);
        DSLAttribute.editPermittedLinks(parent, userCase2);

        ContentForm childObjList = DAOContentCard.createChildObjectList(userCase1, nestedClass);
        DSLContent.add(childObjList);

        Bo userBo = DAOUserBo.create(userCase1);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        GUIContent.clickAdd(childObjList);

        //Проверки
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(nestedCases[0].getTitle(), nestedCases[1].getTitle()), false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов множеством типов объектов,
     * отображаемых в списке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточку компании вывести список объектов list пользовательского класса userClass. 
     *     Типы объектов: userCase1, userCase11, userCase2</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке объектов list нажать кнопку "Добавить"</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке типов, доступных для выбора на форме добавления, отображаются только типы userCase1 и
     * userCase11</li>
     * </ol>
     */
    @Test
    public void testPossibleCasesRestrictedByObjectList()
    {
        //Подготовка
        ContentForm list = DAOContentCard.createObjectList(DAORootClass.create().getFqn(), userClass, userCase1,
                userCase11, userCase2);
        DSLContent.add(list);

        //Выполнение действий
        GUILogon.login(employee);

        GUIBo.goToCard(SharedFixture.root());

        GUIContent.clickAdd(list);

        //Проверка
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(userCase1.getTitle(), userCase11.getTitle()), false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов разрешенными типами
     * в прямой и обратной ссылке при добавлении объектов через список связанных объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В типе userCase1 создать подтип userCase13</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Выдать профилю profile все права в отделе ouCase</li>
     * <li>В пользовательском классе userClass создать ссылку на отдел direcLink</li>
     * <li>В типе отдела ouCase создать обратную ссылку backLink (прямая ссылка: directLink)
     *     ограниченную по типам: userCase11, userCase12, userCase13, userCase2</li>
     * <li>В атрибуте directLink в типе userCase13 запретить все типы</li>
     * <li>На карточку типа ouCase вывести контент relObjectList типа "Список связанных объектов"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке связанных объектов relObjectList нажимаем кнопку "Добавить"</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке типов, доступных для выбора на форме добавления, отображаются только типы userCase11 и
     * userCase12</li>
     * </ol>
     */
    @Test
    public void testPossibleCasesRestrictedByRelAttribute()
    {
        //Подготовка
        MetaClass userCase13 = DAOUserCase.create(userCase1);
        DSLMetaClass.add(userCase13);

        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, ouCase);

        Attribute directLink = DAOAttribute.createObjectLink(userClass, DAOOuCase.createClass(), null);
        DSLAttribute.add(directLink);

        Attribute backLink = DAOAttribute.createBackBOLinks(ouCase.getFqn(), directLink, userCase11, userCase12,
                userCase13, userCase2);
        DSLAttribute.add(backLink);

        DSLAttribute
                .editPermittedLinks(DAOAttribute.createPseudo(null, directLink.getCode(), userCase13.getFqn(), null));

        ContentForm relObjectList = DAOContentCard.createRelatedObjectList(ouCase.getFqn(), ouCase.getFqn() + "@"
                                                                                            + backLink.getCode());
        DSLContent.add(relObjectList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.login(employee);

        GUIBo.goToCard(ou);
        GUIContent.clickAdd(relObjectList);

        //Проверка
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(userCase11.getTitle(), userCase12.getTitle()), false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов множеством типов объектов,
     * отображаемых в списке связанных объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Выдать профилю profile все права в типе отдела ouCase</li>
     * <li>В пользовательском классе userClass создать атрибут directLink - ссылка на отдел</li>
     * <li>В типе отдела ouCase создать атрибут backLink - отратная ссылка на объекты класса userClass 
     *     (прямая ссылка: directLink)</li>
     * <li>На карточку типа отдела ouCase вывести список связанных объектов relObjectList
     *     (атрибут связи: backLink, типы объектов: userCase1, userCase11, userCase2)</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке связанных объектов relObjectList нажать кнопку "Добавить"</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке типов, доступных для выбора на форме добавления, отображаются только типы userCase1 и
     * userCase11</li>
     * </ol>
     */
    @Test
    public void testPossibleCasesRestrictedByRelObjectList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, ouCase);

        Attribute directLink = DAOAttribute.createObjectLink(userClass, DAOOuCase.createClass(), null);
        DSLAttribute.add(directLink);

        Attribute backLink = DAOAttribute.createBackBOLinks(ouCase.getFqn(), directLink);
        DSLAttribute.add(backLink);

        ContentForm relObjectList = DAOContentCard.createRelatedObjectList(ouCase.getFqn(), ouCase.getFqn() + "@"
                                                                                            + backLink.getCode(),
                userCase1, userCase11, userCase2);
        DSLContent.add(relObjectList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Выполнение действий
        GUILogon.login(employee);

        GUIBo.goToCard(ou);
        GUIContent.clickAdd(relObjectList);

        //Проверка
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(userCase1.getTitle(), userCase11.getTitle()), false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов при переходе по 
     * ссылке на форму быстрого выбора
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На форму добавления запроса добавить контент selectClient типа "Выбор контрагента"</li>
     * <li>Создать тип запроса scCase1 и в нем два подтипа scCase11 и scCase12</li>
     * <li>Выдать профилю profile все права в типе запроса scCase1</li>
     * <li>В типе запроса scCase12 забрать у профиля profile право на добавление для отдела</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Сделать отдел ou получателем соглашения agreement</li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на форму быстрого добавления запроса по ссылке. 
     *     При этом в качестве доступных типов указаны scCase1, scCase11 и scCase12</li>
     * <li>В качестве контрагента выбрать отдел ou</li>
     * <li>Выбрать соглашение agreement</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке доступных типов отображаются только типы scCase1 и scCase11</li>
     * </ol>
     */
    @Test
    public void testPossibleScCasesOnFastCreationForm()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        ContentForm selectClient = DAOContentAddForm.createSelectClient(scClass);
        DSLContent.add(selectClient);

        MetaClass scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase1);

        MetaClass scCase11 = DAOScCase.create(scCase1);
        MetaClass scCase12 = DAOScCase.create(scCase1);
        DSLMetaClass.add(scCase11, scCase12);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, scCase1);
        DSLSecurityProfile.removeRights(scCase12, profile, ScRights.ADD_TO_OU);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addRecipients(ou, agreement);

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToFastAddForm(scClass.getFqn(), scCase1.getFqn(), scCase11.getFqn(), scCase12.getFqn());

        GUISc.selectClient(ou);

        GUISc.selectAssociation(agreement, null);

        //Проверка
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(scCase1.getTitle(), scCase11.getTitle()),
                false, true, true);
    }

    /**
     * Тестирование ограничения списка доступных для выбора типов при переходе по 
     * ссылке на форму быстрого выбора в случае, если права определяются скриптом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00197
     * http://sd-jira.naumen.ru/browse/NSDPRD-3291
     * <ol>
     * <br>
     * <b>Настройка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На форму добавления запроса добавить контент selectClient типа "Выбор контрагента"</li>
     * <li>Создать тип запроса scCase1 и в нем два подтипа scCase11 и scCase12</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Сделать отдел ou получателем соглашения agreement</li>
     * <li>Выдать профилю profile все права в типе запроса scCase1</li>
     * <li>В профиле profile в типе запроса scCase11 установить скрипт определения права 
     * на добавление запроса для отдела:
     * <pre>
     * --------------------------------------------------------------------------------------------------
     *     def ACCEPTED_TYPES = ['ouCase'];
     *     if(!sourceObject)
     *          return true;
     *     return ACCEPTED_TYPES.contains(sourceObject.metaClass.toString());
     *  --------------------------------------------------------------------------------------------------
     * </pre> 
     * </li>
     * <li>В профиле profile в типе запроса scCase12 установить скрипт определения права 
     * на добавление запроса для отдела:
     * <pre>
     * --------------------------------------------------------------------------------------------------
     *     def ACCEPTED_TYPES = ['ouCase2'];
     *     if(!sourceObject)
     *          return true;
     *     return ACCEPTED_TYPES.contains(sourceObject.metaClass.toString());
     *  --------------------------------------------------------------------------------------------------
     * </pre> 
     * </li>
     * <br>
     * <b>Выполнение действий </b>
     * <li>Заходим под сотрудником employee</li>
     * <li>Переходим на форму быстрого добавления запроса по ссылке. 
     *     При этом в качестве доступных типов указаны scCase1, scCase11 и scCase12</li>
     * <li>В качестве контрагента выбрать отдел ou</li>
     * <li>Выбрать соглашение agreement</li>
     * <br>
     * <b>Проверка</b>
     * <li>В списке доступных типов отображаются только типы scCase1 и scCase11</li>
     * </ol>
     */
    @Test
    public void testPossibleScCasesOnFastCreationFormWithScript()
    {
        //Подготовка
        //@formatter:off
        String scriptTemplate = "def ACCEPTED_TYPES = ['%s']; \n"
                + "if(!sourceObject) \n"
                + "     return true; \n"
                + "return ACCEPTED_TYPES.contains(sourceObject.metaClass.toString());";
        //@formatter:on

        MetaClass ouCase = SharedFixture.ouCase();
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        String script1 = String.format(scriptTemplate, ouCase.getFqn());
        ScriptInfo scriptInfo1 = DAOScriptInfo.createNewScriptInfo(script1);
        DSLScriptInfo.addScript(scriptInfo1);

        String script2 = String.format(scriptTemplate, SharedFixture.scCase2().getFqn());
        ScriptInfo scriptInfo2 = DAOScriptInfo.createNewScriptInfo(script2);
        DSLScriptInfo.addScript(scriptInfo2);

        MetaClass scClass = DAOScCase.createClass();
        ContentForm selectClient = DAOContentAddForm.createSelectClient(scClass);
        DSLContent.add(selectClient);

        MetaClass scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase1);

        MetaClass scCase11 = DAOScCase.create(scCase1);
        MetaClass scCase12 = DAOScCase.create(scCase1);
        DSLMetaClass.add(scCase11, scCase12);

        Bo agreement = SharedFixture.agreement();
        DSLAgreement.addRecipients(ou, agreement);

        DSLSecurityProfile.grantAllPermissionsForCase(profile, scCase1);

        DSLSecurityProfile.setScriptRight(scCase11, profile, ScRights.ADD_TO_OU, scriptInfo1.getCode());
        DSLSecurityProfile.setScriptRight(scCase12, profile, ScRights.ADD_TO_OU, scriptInfo2.getCode());

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToFastAddForm(scClass.getFqn(), scCase1.getFqn(), scCase11.getFqn(), scCase12.getFqn());

        GUISc.selectClient(ou);

        GUISc.selectAssociation(agreement, null);

        //Проверка
        GUISelect.assertSelect(GUIXpath.SpecificComplex.METACLASS_SELECT_VALUE,
                Arrays.asList(scCase1.getTitle(), scCase11.getTitle()),
                false, true, true);
    }
}
