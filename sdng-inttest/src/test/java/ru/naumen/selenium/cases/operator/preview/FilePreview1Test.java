package ru.naumen.selenium.cases.operator.preview;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFilePreview;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.FileType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.RichTextType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOFileClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.preview.DSLFilePreviewSettings;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.ActionToActiveElement;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тесты на интерфейс предварительного просмотра файлов
 *
 * <AUTHOR>
 * @since 16 сент. 2015 г.
 */
public class FilePreview1Test extends AbstractTestCase
{
    /**
     * Тестирование предварительного просмотра .wav файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attr типа файл с представлением "Изображение"</li>
     * <li>Вывести атрибут attr на карточку компании</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>Добавляем в атрибут attr аудио файл audioFile в формате .wav</li>
     * <li>В поле значения атрибута attr кликаем по ссылке с названием файла audioFile</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр аудио файла audionFile</li>
     * </ol>
     */
    @Test
    public void testAudioFilePreview()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        Attribute attr = DAOAttribute.createFile(rootClass.getFqn());
        attr.setViewPresentation(FileType.VIEW_IMAGE);
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(rootClass);
        DSLGroupAttr.add(group, attr);

        ContentForm propertyList = DAOContentCard.createPropertyList(rootClass, group);
        DSLContent.add(propertyList);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        Set<String> oldUuids = DSLBo.getUuidsByFqn(DAOFileClass.create().getFqn());

        GUIPropertyList.clickEditLink(propertyList);
        GUIFileOperator.uploadFile(GUIXpath.Any.ANY_VALUE, DSLFile.WAV_FILE, attr.getCode());
        Assert.assertTrue("Файл не был загружен", tester.waitAppear(GUIXpath.Any.CLEAR_BUTTON_CONTAINS));
        GUIForm.applyModalForm();

        SdFile audioFile = DSLFile.getNewFile(oldUuids);

        GUIFileOperator.clickOnNotImageFileLink(propertyList, attr, audioFile);

        //Проверка
        GUITester.assertExists(GUIFilePreview.PREVIEW_XPATH + "//audio", true);
        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование просмотра оригинального размера cжатых изображений в атрибуте типа "Текст в формате RTF"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$78643398
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем изображение размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <li>Проверяем, что открылось окно предварительного просмотра изображения</li>
     * <li>Проверяем, что изображение вписано в рабочую область</li>
     * <li>Проверяем, что в верхней панели присутствуют кнопки "Закрыть", "Скачать файл" и "Увеличить масштаб"
     *     с подсказкой "Увеличить масштаб"</li>
     * <li>Проверяем, что на верхней панели отсутствует кнопка "Дополнительная информация о файле"</li>
     * <li>Кликнуть по кнопке "Увеличить масштаб"</li>
     * <li>Проверить, что вместо нее появилась кнопка "Уменьшить масштаб" с подсказкой "Уменьшить масштаб"</li>
     * <li>Проверить, что изображение отображается в натуральном размере</li>
     * <li>Кликнуть по кнопке "Уменьшить масштаб"</li>
     * <li>Проверить, что вместо нее появилась кнопка "Увеличить масштаб"</li>
     * <li>Проверяем, что изображение вписано в рабочую область</li>
     * <li>Кликнуть по кнопке "Закрыть"</li>
     * <li>Проверить, что окно предварительного просмотра закрылось</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-12140", ignoreBrowser = { WebBrowserType.FIREFOX })
    @Test
    public void testBrowsingOriginalImage()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);
        String oldValue = DSLFilePreviewSettings.getImageCompressionStateSettings();
        DSLFilePreviewSettings.setImageCompressionState(Boolean.FALSE);
        Cleaner.afterTest(() -> DSLFilePreviewSettings.setImageCompressionState(Boolean.parseBoolean(oldValue)));

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);

        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE4);
        GUIRichText.pasteAndWait(attribute);

        GUIForm.applyForm();

        GUIRichText.clickImage(content, attribute);

        GUIFilePreview.assertImageCanvasPresent();
        GUIFilePreview.assertImageFitsIn(1920, 1200);

        GUIFilePreview.assertCloseButtonPresent();
        GUIFilePreview.assertDownloadButtonPresent();

        GUIFilePreview.assertMaximizeButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.MAXIMIZE_BUTTON_XPATH, "Увеличить масштаб");

        GUIFilePreview.assertShowInfoButtonAbsent();

        GUIFilePreview.clickMaximizeButton();
        GUIFilePreview.assertMinimizeButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.MINIMIZE_BUTTON_XPATH, "Уменьшить масштаб");

        GUIFilePreview.assertImageSize(1920, 1200);

        GUIFilePreview.clickMinimizeButton();
        GUIFilePreview.assertMaximizeButtonPresent();
        GUIFilePreview.assertImageFitsIn(1920, 1200);

        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование ошибки при попытке открыть окно предварительного просмотра,
     * кликнув по уже удаленному файлу в тексте rtf
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <li>Добавить изображение в значение атрибута attribute в компании</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>Параллельно удаляем файл добавленного изображения</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что появилось сообщение об ошибке вида; "Файл недоступен для просмотра, так как был удален"</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testClickOnRemovedImage()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        String fileClassFqn = DAOFileClass.create().getFqn();
        Set<String> oldUuids = DSLBo.getUuidsByFqn(fileClassFqn);

        String imgTemplate = "<img src=\"data:image/png;base64,%s\" />";
        attribute.setValue(String.format(imgTemplate, DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD)));

        Bo root = SharedFixture.root();
        DSLBo.editAttributeValue(root, attribute);

        Bo file = DAOBo.createModelByUuid(DSLBo.getCreatedObjectUuid(fileClassFqn, oldUuids));

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(root);

        DSLBo.delete(file);

        GUIRichText.clickImage(content, attribute);

        //Проверка
        GUIError.assertDialogError("Файл недоступен для просмотра, так как был удален");
    }

    /**
     * Тестирование наличия элементов управления в интерфейсе предварительного просмотра,
     * открытым по клику по строке в списке файлов, и подсказок над ними
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела типа ouCase вывести список файлов fileList (представление: "Список")</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>к отделу ou прикрепить pdf-файл</li>
     * <li>к отделу ou прикрепить png-файл pngFile</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке fileList кликаем по строке файла pngFile</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что на верхней панели присутствует кнопка "Дополнительная информация о файле" 
     *     с посказкой "Дополнительная информация о файле (F11)"</li>
     * <li>Проверяем что на верхней панели отсутствует кнопка "Увеличить масштаб"</li>
     * <li>Проверяем что на верхней панели присутствует кнопка "Скачать файл" 
     *     с посказкой "Скачать файл"</li>
     * <li>Проверяем что на верхней панели присутствует кнопка "Закрыть окно" 
     *     с посказкой "Закрыть окно (Esc)"</li>
     * <li>Проверяем что в окне присутствует кнопка "Следующий файл" 
     *     с посказкой "Следующий файл"</li>
     * <li>Проверяем что в окне присутствует кнопка "Предыдущий файл" 
     *     с посказкой "Предыдущий файл"</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testControllHints()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        DSLFile.add(ou, DSLFile.FILE_FOR_SEARCH_PDF);
        SdFile pngFile = DSLFile.add(ou, DSLFile.BIG_IMG_FOR_UPLOAD);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileOperator.clickOnFileInList(fileList, pngFile);

        //Проверки
        GUIFilePreview.assertShowInfoButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.SHOW_INFO_BUTTON_XPATH,
                "Дополнительная информация о файле (F11)");

        GUIFilePreview.assertMaximizeButtonAbsence();

        GUIFilePreview.assertDownloadButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.DOWNLOAD_BUTTON_XPATH, "Скачать файл");

        GUIFilePreview.assertCloseButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.CLOSE_BUTTON_XPATH, "Закрыть окно (Esc)");

        GUIFilePreview.assertNextButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.NEXT_BUTTON_XPATH, "Следующий файл");

        GUIFilePreview.assertPrevButtonPresent();
        GUIFilePreview.assertButtonHint(GUIFilePreview.PREV_BUTTON_XPATH, "Предыдущий файл");

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование предварительного просмотра .csv файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку компании вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к компании файл file в формате .csv</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testCsvFilePreview()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        ContentForm fileList = DAOContentCard.createFileList(rootClass.getFqn());
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();

        SdFile file = DSLFile.add(root, DSLFile.CSV_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_DIV_XPATH, "This;is;a;csv;file");

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование просмотра изображений из текста RTF при их параллельном удалении
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <li>Добавить два изображения в значение атрибута attribute в компании</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В поле атрибута attribute кликнуть по первому добавленному изображению</li>
     * <li>Параллельно удаляем файл одного из добавленых изображений</li>
     * <li>Кликаем по кнопке "Следующий файл"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что количество просматриваемых файлов по-прежнему равно двум</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testDeteteFileWhilePreviewIsOpen()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        String fileClassFqn = DAOFileClass.create().getFqn();
        Set<String> oldUuids = DSLBo.getUuidsByFqn(fileClassFqn);

        String img = String.format("<img src=\"data:image/png;base64,%s\" />",
                DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD));
        attribute.setValue(img + img);

        Bo root = SharedFixture.root();
        DSLBo.editAttributeValue(root, attribute);

        Bo file = DAOBo.createModelByUuid(
                DSLBo.getCreatedObjectsUuid(fileClassFqn, oldUuids, DSLBo.WAIT_BO_CREATED, 2).iterator().next());

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(root);

        GUIRichText.clickImage(content, attribute);

        DSLBo.delete(file);

        GUIFilePreview.clickNextButton();

        //Проверка
        GUIFilePreview.assertCounter(2, 2);

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование предварительного просмотра .doc файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку компании вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к компании файл file в формате .doc</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testDocFilePreview()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        ContentForm fileList = DAOContentCard.createFileList(rootClass.getFqn());
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();

        SdFile file = DSLFile.add(root, DSLFile.DOC_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//span", "This is a doc file.");

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование предварительного просмотра .docx файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку компании вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к компании файл file в формате .docx</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testDocxFilePreview()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        ContentForm fileList = DAOContentCard.createFileList(rootClass.getFqn());
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();

        SdFile file = DSLFile.add(root, DSLFile.DOCX_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextContains(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//span", "This is a docx file.");

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование скачивания текущего просматриваемого файла по кнопке "Скачать файл"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <li>Добавить изображение в значение атрибута attribute в компании</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В поле атрибута attribute кликнуть по первому добавленному изображению</li>
     * <li>Кликаем по кнопке "Скачать файл"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что файл изображения скачался</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testDownloadFile()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        String imgTemplate = "<img src=\"data:image/png;base64,%s\" />";
        attribute.setValue(String.format(imgTemplate, DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD)));

        Bo root = SharedFixture.root();
        DSLBo.editAttributeValue(root, attribute);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(root);

        GUIRichText.clickImage(content, attribute);

        //Проверка
        //Падает!
        GUIFilePreview.download();

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование открытия окна предварительного просмотра при клике на картинку в тексте RTF 
     * с представлением для отображения "Текст в формате RTF, небезопасный"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"
     *     для отображения "Текст в формате RTF, небезопасный"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем изображение размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылось окно предварительного просмотра с изображением</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testImagePreviewWithUnsafeRTFView()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        attribute.setViewPresentation(RichTextType.UNSAFE_VIEW);
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);

        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE);
        GUIRichText.pasteAndWait(attribute);

        GUIForm.applyForm();

        GUIRichText.clickImageUnsafe(content, attribute);

        //Проверка
        GUIFilePreview.assertImageCanvasPresent();

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование открытия окна предварительного просмотра при клике на картинку в тексте RTF 
     * с представлением для отображения "Текст в формате RTF (по всей ширине)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"
     *     для отображения "Текст в формате RTF (по всей ширине)"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем изображение размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылось окно предварительного просмотра с изображением</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testImagePreviewWithWideRTFView()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        attribute.setViewPresentation(RichTextType.WIDE_VIEW);
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);

        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE);
        GUIRichText.pasteAndWait(attribute);

        GUIForm.applyForm();

        GUIRichText.clickImage(content, attribute);

        //Проверка
        GUIFilePreview.assertImageCanvasPresent();

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование открытия окна предварительного просмотра при клике на картинку в тексте RTF 
     * с представлением для отображения "Текст в формате RTF, небезопасный (по всей ширине)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"
     *     для отображения "Текст в формате RTF, небезопасный (по всей ширине)"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем изображение размера 1920x1200 из буфера обмена</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылось окно предварительного просмотра с изображением</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testImagePreviewWithWideUnsafeRTFView()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        attribute.setViewPresentation(RichTextType.UNSAFE_WIDE_VIEW);
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);

        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE);
        GUIRichText.pasteAndWait(attribute);

        GUIForm.applyForm();

        GUIRichText.clickImageUnsafe(content, attribute);

        //Проверка
        GUIFilePreview.assertImageCanvasPresent();

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование вывода на левую информационную панель виджета предварительного просмотра файлов, 
     * открытого по клику на изображение в атрибуте типа "Файл", группы атрибутов Системные атрибуты.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В компанию добавить атрибут attr типа "Файл" с представлением "Изображение"</li>
     * <li>Вывести атрибут attr на карточку компании</li>
     * <li>Добавить атриубут attr файл с изображением file</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В поле со значением атрибута attr кликаем по изображению из файла file</li>
     * <li>В открывшемся окне предварительного просмотра нажимаем кнопку "Дополнительная информация о файле"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что на появившейся информационной панели присутствуют только атрибуты 
     *     "Имя файла", "Размер",  "Дата создания", "Автор" и "Описание"</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testInfoPanelAttrsFromFileAttr()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        Attribute attr = DAOAttribute.createFile(rootClass.getFqn());
        attr.setViewPresentation(FileType.VIEW_IMAGE);
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(rootClass);
        DSLGroupAttr.add(group, attr);

        ContentForm propertyList = DAOContentCard.createPropertyList(rootClass, group);
        DSLContent.add(propertyList);

        Bo root = SharedFixture.root();
        SdFile file = DSLFile.addFileToAttribute(root, attr, DSLFile.IMG_FOR_UPLOAD, ModelUtils.createDescription());

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(root);

        GUIFileOperator.clickOnImage(propertyList, attr, file);
        GUIFilePreview.clickShowInfo();

        //Проверка
        MetaClass fileClass = DAOFileClass.create();
        Attribute title = SysAttribute.title(fileClass);
        Attribute size = SysAttribute.size(fileClass);
        Attribute creationDate = SysAttribute.creationDate(fileClass);
        Attribute author = SysAttribute.author(fileClass);
        Attribute description = SysAttribute.description(fileClass);

        GUIFilePreview.assertAttributesPresent(title, size, creationDate, author, description);

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование вывода на левую информационную панель виджета предварительного просмотра файлов, 
     * открытого из контента Список файлов с представлением Иконки, группы атрибутов Системные атрибуты.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку компании вывести список файлов fileList с представлением "Иконки"</li>
     * <li>Прикрепить к компании файл file</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <li>В открывшемся окне предварительного просмотра нажимаем кнопку "Дополнительная информация о файле"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что на появившейся информационной панели присутствуют только атрибуты 
     *     "Имя файла", "Размер",  "Дата создания", "Автор" и "Описание"</li>
     * <li>Закрываем явно preview для декремента счетчиков активных preview<li>
     * </ol>
     */
    @Test
    public void testInfoPanelAttrsFromFileIconList()
    {
        //Подготовка
        ContentForm fileList = DAOContentCard.createFileList(DAORootClass.create().getFqn());
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();
        SdFile file = DSLFile.add(root, DSLFile.DOC_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(root);

        GUIFileOperator.clickOnIcon(fileList, file);

        GUIFilePreview.clickShowInfo();

        //Проверка
        MetaClass fileClass = DAOFileClass.create();
        Attribute title = SysAttribute.title(fileClass);
        Attribute size = SysAttribute.size(fileClass);
        Attribute creationDate = SysAttribute.creationDate(fileClass);
        Attribute author = SysAttribute.author(fileClass);
        Attribute description = SysAttribute.description(fileClass);

        GUIFilePreview.assertAttributesPresent(title, size, creationDate, author, description);

        //Закрываем явно preview для декремента счетчиков активных preview
        GUIFilePreview.closePreview();
    }

    /**
     * Тестирование вывода на левую информационную панель виджета предварительного просмотра файлов, 
     * открытого с контента Список файлов с представлением Список, пользовательской группы атрибутов, 
     * содержащей пользовательский ссылочный атрибут.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудников empl1 и empl2 типа emplCase</li>
     * <li>В системный класс "Файл" добавить набор ссылок на сотрудников boLinks со значением по умолчанию empl1 и
     * empl2</li>
     * <li>В классе "Файл" создать группу атрибутов group с атрибутами "Имя файла", "Тип объекта",  "Дата создания" и
     * boLinks</li>
     * <li>На карточку компании вывести список файлов fileList с представлением "Список" и группой атрибутов group</li>
     * <li>Прикрепить к компании файл file</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по строке файла file</li>
     * <li>В открывшемся окне предварительного просмотра нажимаем кнопку "Дополнительная информация о файле"</li>
     * <li>Проверяем что на появившейся информационной панели присутствуют только атрибуты "Имя файла", "Тип
     * объекта",  "Дата создания" и boLinks</li>
     * <li>Проверяем что в значении атрибута boLonks указаны ссылки на сотрудников empl1 и empl2</li>
     * <li>Кликаем по ссылке на сортудника empl2</li>
     * <li>Проверяем что осуществлен переход на карточку сотрудника empl2</li>
     * </ol>
     */
    @Test
    public void testInfoPanelAttrsFromFileListWithUserGroup()
    {
        //Подготовка
        Bo ou = SharedFixture.ou();
        MetaClass emplCase = SharedFixture.employeeCase();

        Bo empl1 = DAOEmployee.create(emplCase, ou, true);
        Bo empl2 = DAOEmployee.create(emplCase, ou, true);
        DSLBo.add(empl1, empl2);

        MetaClass fileClass = DAOFileClass.create();
        Attribute boLinks = DAOAttribute.createBoLinks(fileClass.getFqn(), DAOEmployeeCase.createClass(), empl1, empl2);
        DSLAttribute.add(boLinks);

        GroupAttr group = DAOGroupAttr.create(fileClass);
        Attribute title = SysAttribute.title(fileClass);
        Attribute metaClass = SysAttribute.metaClass(fileClass);
        Attribute creationDate = SysAttribute.creationDate(fileClass);
        DSLGroupAttr.add(group, title, metaClass, creationDate, boLinks);

        MetaClass rootClass = DAORootClass.create();
        ContentForm fileList = DAOContentCard.createFileAdvList(rootClass.getFqn(), group);
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();
        SdFile file = DSLFile.add(root, DSLFile.DOC_FILE);
        file.setExists(true);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(root);

        GUIFileOperator.clickOnFileInList(fileList, file);

        GUIFilePreview.clickShowInfo();
        GUIFilePreview.assertAttributesPresent(title, metaClass, creationDate, boLinks);

        List<String> titles = ModelUtils.getTitles(empl1, empl2);
        Collections.sort(titles);
        String value = StringUtils.join(titles, ", ");

        GUIFilePreview.assertAttrValue(boLinks, value);

        tester.click(GUIFilePreview.FILE_ATTR_VALUE_XPATH + GUIXpath.A.TEXT_PATTERN, boLinks.getCode(),
                empl2.getTitle());
        GUIBo.assertThatBoCard(empl2);
    }

    /**
     * Тестирование открытия и закрытия информационной панели
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела типа ouCase вывести список файлов fileList (представление: "Список")</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>к отделу ou прикрепить pdf-файл pdfFile</li>
     * <li>к отделу ou прикрепить png-файл pngFile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке fileList кликаем по строке файла pngFile</li>
     * <li>Проверяем что информационная панель скрыта</li>
     * <li>Кликаем по кнопке "Дополнительная информация о файле"</li>
     * <li>Проверяем что информационная панель открылась</li>
     * <li>Проверяем что кнопка "Дополнительная информация о файле" отображается оранжевым цветом</li>
     * <li>Проверяем что значением атрибута "Название файла" является название файла pngFile</li>
     * <li>Нажимаем кнопку "Следующий файл"</li>
     * <li>Проверяем что информационная панель не скрылась при смене файла</li>
     * <li>Проверяем что значением атрибута "Название файла" является название файла pdfFile</li>
     * <li>Кликаем по кнопке "Дополнительная информация о файле"</li>
     * <li>Проверяем что информационная панель скрылась</li>
     * <li>Проверяем что кнопка "Дополнительная информация о файле" приняла обычный вид</li>
     * </ol>
     */
    @Test
    public void testInfoPanelOpeningAndClosing()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        SdFile pdfFile = DSLFile.add(ou, DSLFile.FILE_FOR_SEARCH_PDF);
        SdFile pngFile = DSLFile.add(ou, DSLFile.BIG_IMG_FOR_UPLOAD);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileOperator.clickOnFileInList(fileList, pngFile);

        GUIFilePreview.assertInfoPanelHidden();
        GUIFilePreview.clickShowInfo();

        GUIFilePreview.assertInfoPanelShown();
        GUIFilePreview.assertShowInfoButtonAbsent();
        GUIFilePreview.assertHideInfoButtonPresent();

        Attribute title = SysAttribute.title(DAOFileClass.create());
        GUIFilePreview.assertAttrValue(title, pngFile.getTitle());

        GUIFilePreview.clickNextButton();

        GUIFilePreview.assertInfoPanelShown();
        GUIFilePreview.assertAttrValue(title, pdfFile.getTitle());

        GUIFilePreview.clickHideInfoPanel();
        GUIFilePreview.assertInfoPanelHidden();
        GUIFilePreview.assertShowInfoButtonPresent();
        GUIFilePreview.assertHideInfoButtonAbsent();
    }

    /**
     * Тестирование открытия и закрытия информационной панели по нажатию на клавишу F11
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела типа ouCase вывести список файлов fileList (представление: "Список")</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>к отделу ou прикрепить pdf-файл pdfFile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке fileList кликаем по строке файла pngFile</li>
     * <li>Проверяем что информационная панель скрыта</li>
     * <li>Нажимаем клавишу F11 на клавиатуре</li>
     * <li>Проверяем что информационная панель открылась</li>
     * <li>Проверяем что кнопка "Дополнительная информация о файле" отображается оранжевым цветом</li>
     * <li>Нажимаем клавишу F11 на клавиатуре</li>
     * <li>Проверяем что информационная панель скрылась</li>
     * <li>Проверяем что кнопка "Дополнительная информация о файле" приняла обычный вид</li>
     * </ol>
     */
    @Test
    public void testInfoPanelOpeningAndClosingOnKeyboard()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        SdFile pngFile = DSLFile.add(ou, DSLFile.BIG_IMG_FOR_UPLOAD);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileOperator.clickOnFileInList(fileList, pngFile);

        GUIFilePreview.assertInfoPanelHidden();
        GUIFilePreview.assertHideInfoButtonAbsent();

        tester.actives().pressingFullScreenKey();

        GUIFilePreview.assertInfoPanelShown();
        GUIFilePreview.assertHideInfoButtonPresent();
        GUIFilePreview.assertShowInfoButtonAbsent();

        tester.actives().pressingFullScreenKey();

        GUIFilePreview.assertInfoPanelHidden();
        GUIFilePreview.assertHideInfoButtonAbsent();
        GUIFilePreview.assertShowInfoButtonPresent();
    }

    /**
     * Тестирование отсутствия кнопки "Увеличить масштаб" в случае если размеры изображения меньше размеров
     * рабочей области
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>В классе "Компания" создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку компании вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем изображение размера 340x250 из буфера обмена</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по картинке отображаемой в значении атрибута attribute</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в открывшемся окне предварительного просмотра на верхней панели отсутствует кнопка
     * "Увеличить масштаб"</li>
     * </ol>
     */
    @Test
    public void testMaximizeButtonAbsenceIfImageFitsScreen()
    {
        //Подготовка
        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        String imgPattern = "<img src=\"data:image/jpg;base64,%s\" />";
        String img = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD));

        GUIPropertyList.clickEditLink(content);
        GUIRichText.clickHTML(attribute.getCode());
        ActionToActiveElement.copyToClipboard(img);
        GUIRichText.paste(attribute);
        GUIForm.applyForm();

        GUIRichText.clickImage(content,
                attribute);// //div[@id='gwt-debug-PropertyList.ATestB5xP5RDK']//*[@id='gwt-debug-ATestMT16dKoc-value
        // ']//iframe
        //Проверка
        GUIFilePreview.assertMaximizeButtonAbsence();
    }

    /**
     * Тестирование переключения между файлами в окне предварительного просмотра открытого
     * по клику по строке в списке файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку отдела типа ouCase вывести список файлов fileList (представление: "Список")</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>к отделу ou прикрепить pdf-файл pdfFile</li>
     * <li>к отделу ou прикрепить png-файл pngFile</li>
     * <li>к отделу ou прикрепить odt-файл odtFile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке fileList кликаем по строке файла pngFile</li>
     * <li>Проверяем что в окне предварительного просмотра отображается содержимое файла pngFile</li>
     * <li>Проверяем что в счетчике файлов указано "Файл 2 из 3"</li>
     * <li>Проверяем что в верхней панели отображается название файла pngFile</li>
     * <li>Кликаем по кнопке "Следующий файл"</li>
     * <li>Проверяем что в окне предварительного просмотра отображается содержимое файла pdfFile</li>
     * <li>Проверяем что в счетчике файлов указано "Файл 3 из 3"</li>
     * <li>Проверяем что в верхней панели отображается название файла pdfFile</li>
     * <li>Кликаем по кнопке "Предыдущий файл"</li>
     * <li>Проверяем что в счетчике файлов указано "Файл 2 из 3"</li>
     * <li>Проверяем что в верхней панели отображается название файла pngFile</li>
     * <li>Кликаем по кнопке "Предыдущий файл"</li>
     * <li>Проверяем что в счетчике файлов указано "Файл 1 из 3"</li>
     * <li>Проверяем что в верхней панели отображается название файла odtFile</li>
     * <li>Проверяем что в окне предварительного просмотра отображается сообщение:
     *     "Данный файл недоступен для предварительного просмотра. Вы можете скачать его по этой ссылке 
     *     или по нажатию на иконку 'Скачать', расположенную в верхнем правом углу окна"</li>
     * </ol>
     */
    @Test
    public void testNavigationBetweenFilesInFileList()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        SdFile pdfFile = DSLFile.add(ou, DSLFile.FILE_FOR_SEARCH_PDF);
        SdFile pngFile = DSLFile.add(ou, DSLFile.BIG_IMG_FOR_UPLOAD);
        SdFile odtFile = DSLFile.add(ou, DSLFile.FILE_FOR_SEARCH_ODT);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileOperator.clickOnFileInList(fileList, pngFile);
        GUIFilePreview.assertImageCanvasPresent();
        GUIFilePreview.assertCounter(2, 3);
        GUIFilePreview.assertFileTitle(pngFile.getTitle());

        GUIFilePreview.clickNextButton();
        GUIFilePreview.assertPdfCanvasPresent();
        GUIFilePreview.assertCounter(3, 3);
        GUIFilePreview.assertFileTitle(pdfFile.getTitle());

        GUIFilePreview.clickPrevButton();
        GUIFilePreview.assertCounter(2, 3);
        GUIFilePreview.assertFileTitle(pngFile.getTitle());

        GUIFilePreview.clickPrevButton();
        GUIFilePreview.assertCounter(1, 3);
        GUIFilePreview.assertFileTitle(odtFile.getTitle());
        GUIFilePreview.assertPreviewNotAvailible();
    }

    /**
     * Тестирование открытия окна предварительного просмотра
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase cоздать атрибут attr типа "Файл" с представлением для отображения "Изображение"</li>
     * <li>Создать группу атрибутов group c атрибутом attr</li>
     * <li>На карточку типа ouCase вывести контент propertyList типа "Параметры объекта" (группа атрибутов: group)</li>
     * <li>На карточку типа ouCase вывести контент fileList типа "Список файлов" (представление: список)</li>
     * <li>На карточку типа ouCase вывести контент fileIconList типа "Список файлов" (представление: иконки)</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Прикрепить к отделу ou pdf-файл pdfFile1</li>
     * <li>В значение атрибута attr отдела ou добавить pdf-файл pdfFile2</li>
     * <li>В значение атрибута attr отдела ou добавить png-файл pngFile</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В списке файлов fileList кликаем по файлу pdfFile1</li>
     * <li>Проверяем что открылось окно предварительного просмотра файла pdfFile1</li>
     * <li>Закрываем окно предварительного просмотра</li>
     * <li>В списке файлов fileIconList кликаем по иконке файла pdfFile1</li>
     * <li>Проверяем что открылось окно предварительного просмотра файла pdfFile1</li>
     * <li>Закрываем окно предварительного просмотра</li>
     * <li>В значении атрибута attr в контенте propertyList кликаем по изображению pngFile</li>
     * <li>Проверяем что открылось окно предварительного просмотра файла pngFile</li>
     * <li>Закрываем окно предварительного просмотра</li>
     * <li>В значении атрибута attr в контенте propertyList кликаем по ссылке файла pdfFile2</li>
     * <li>Проверяем что открылось окно предварительного просмотра файла pdfFile2</li>
     * <li>Закрываем окно предварительного просмотра</li>
     * </ol>
     */
    @Test
    public void testOpenFilePreview()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attr = DAOAttribute.createFile(ouCase.getFqn());
        attr.setViewPresentation(FileType.VIEW_IMAGE);
        DSLAttribute.add(attr);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        ContentForm fileList = DAOContentCard.createFileList(ouCase.getFqn(), PresentationContent.ADVLIST);
        ContentForm fileIconList = DAOContentCard.createFileList(ouCase.getFqn());
        DSLContent.add(propertyList, fileList, fileIconList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        SdFile pdfFile1 = DSLFile.add(ou, DSLFile.FILE_FOR_SEARCH_PDF);
        SdFile pdfFile2 = DSLFile.addFileToAttribute(ou, attr, DSLFile.FILE_FOR_SEARCH_PDF,
                ModelUtils.createDescription());
        SdFile pngFile = DSLFile.addFileToAttribute(ou, attr, DSLFile.BIG_IMG_FOR_UPLOAD,
                ModelUtils.createDescription());

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIFileOperator.clickOnFileInList(fileList, pdfFile1);
        GUIFilePreview.assertPdfCanvasPresent();
        GUIFilePreview.closePreview();

        GUIFileOperator.clickOnIcon(fileIconList, pdfFile1);
        GUIFilePreview.assertPdfCanvasPresent();
        GUIFilePreview.closePreview();

        GUIFileOperator.clickOnImage(propertyList, attr, pngFile);
        GUIFilePreview.assertImageCanvasPresent();
        GUIFilePreview.closePreview();

        GUIFileOperator.clickOnNotImageFileLink(propertyList, attr, pdfFile2);
        GUIFilePreview.assertPdfCanvasPresent();
        GUIFilePreview.closePreview();
    }
}
