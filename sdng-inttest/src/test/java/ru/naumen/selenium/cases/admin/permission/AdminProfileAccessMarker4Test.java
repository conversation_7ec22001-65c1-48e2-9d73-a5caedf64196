package ru.naumen.selenium.cases.admin.permission;

import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.*;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.admin.interfaze.GUIInterface;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.adminprofiles.GUIAdminProfile;
import ru.naumen.selenium.casesutil.advimport.DSLAdvimport;
import ru.naumen.selenium.casesutil.advimport.GUIAdvimportList;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.inbmailconnection.GUIInboundMailServerConnection;
import ru.naumen.selenium.casesutil.jmsqueue.DAOJMSQueue;
import ru.naumen.selenium.casesutil.jmsqueue.GUIJMSQueueList;
import ru.naumen.selenium.casesutil.mail.DSLSmtpServer;
import ru.naumen.selenium.casesutil.mail.GUIOutgoingServerConfigCard;
import ru.naumen.selenium.casesutil.mail.GUIOutgoingServerConfigsList;
import ru.naumen.selenium.casesutil.mail.rules.DSLMailProcessorRule;
import ru.naumen.selenium.casesutil.mail.rules.GUIMailProcessorRule;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationCard;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationForm;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationList;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConnection;
import ru.naumen.selenium.casesutil.model.advimport.DAOAdvimport;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.jmsqueue.DSLJmsQueue;
import ru.naumen.selenium.casesutil.model.jmsqueue.JMSQueue;
import ru.naumen.selenium.casesutil.model.mail.DAOSmtpServer;
import ru.naumen.selenium.casesutil.model.mail.SmtpServer;
import ru.naumen.selenium.casesutil.model.mail.rules.DAOMailProcessorRule;
import ru.naumen.selenium.casesutil.model.mail.rules.MailProcessorRule;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection.MailProtocol;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.schedulertask.GUISchedulerTaskList;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;

/**
 * Тестирование прав на действие в интерфейсе администратора по маркерам доступа в профиле администрирования.
 *
 * <AUTHOR>
 * @since 20.12.2025
 */
public class AdminProfileAccessMarker4Test extends AbstractTestCase
{
    private static final String LOGIN_AS_USER_SCRIPT = """
                def authImpl = beanFactory.getBean("authorizationRunnerServiceImpl")
                authImpl.callAsSuperUser('%s', {
                	def dispatch = beanFactory.getBean("dispatch")
                	def action = new ru.naumen.sec.shared.LoginAsUserAction('%s')
                	dispatch.execute(action)
                })
            """;
    private SuperUser superUser;
    private AdminProfile adminProfile;
    private AdminProfileAccessMarkerMatrix accessMarkerMatrix;
    private MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareAllFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
    }

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Добавить в систему профиль администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser и назначить ему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Почта". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать подключение к серверу входящей почты inboundMailConnection</li>
     * <li>Создать подключение к серверу исходящей почты smtpServer</li>
     * <li>Создать правило обработки почты mailProcessorRule со скриптом script</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Почта"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку подключения к серверу входящей почты inboundMailConnection</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что подключение к серверу входящей почты inboundMailConnection выключено</li>
     * <li>Открыть форму редактирования и в поле "Сервер" установить значение inboundMailConnectionNewServer,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что сервер подключения к серверу входящей почты inboundMailConnection не изменился</li>
     * <li>Перейти на карточку подключения к серверу исходящей почты smtpServer</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что подключение к серверу исходящей почты smtpServer выключено</li>
     * <li>Открыть форму редактирования и в поле "Сервер" установить значение smtpServerNewServer,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что сервер подключения к серверу исходящей почты smtpServer не изменился</li>
     * <li>Перейти на карточку правила обработки почты mailProcessorRule</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что правило обработки почты mailProcessorRule выключено</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение mailProcessorRuleNewTitle,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название правила обработки почты mailProcessorRule не изменилось</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Почта"</li>
     * <li>Перейти на карточку подключения к серверу входящей почты inboundMailConnection</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что подключение к серверу входящей почты inboundMailConnection включено</li>
     * <li>Открыть форму редактирования и в поле "Сервер" установить значение inboundMailConnectionNewServer,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что сервер подключения к серверу входящей почты
     * inboundMailConnection - inboundMailConnectionNewServer</li>
     * <li>Перейти на карточку подключения к серверу исходящей почты smtpServer</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что подключение к серверу исходящей почты smtpServer включено</li>
     * <li>Открыть форму редактирования и в поле "Сервер" установить значение smtpServerNewServer,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что сервер подключения к серверу исходящей почты smtpServer - smtpServerNewServer</li>
     * <li>Перейти на карточку правила обработки почты mailProcessorRule</li>
     * <li>Нажать на кнопку "Включить"</li>
     * <li>Проверить, что правило обработки почты mailProcessorRule включено</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение mailProcessorRuleNewTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название правила обработки почты mailProcessorRule - mailProcessorRuleNewTitle</li>
     * </ol>
     */
    @Test
    public void testEditPermissionMailAccessMarker()
    {
        InboundMailConnection inboundMailConnection = DAOSchedulerTask.createInboundMailConnection(
                MailProtocol.IMAP4, false);
        DSLSchedulerTask.addInboundMailConnection(inboundMailConnection);

        SmtpServer smtpServer = DAOSmtpServer.create();
        DSLSmtpServer.add(smtpServer);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        MailProcessorRule mailProcessorRule = DAOMailProcessorRule.createMailProcessorRule();
        DSLMailProcessorRule.addReceiveMailRule(mailProcessorRule, script);

        String inboundMailConnectionNewServer = ModelUtils.createTitle();
        String smtpServerNewServer = ModelUtils.createTitle();
        String mailProcessorRuleNewTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(MAIL, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIInboundMailServerConnection.goToConnectionCard(inboundMailConnection);
        GUIInboundMailServerConnection.clickSwitch();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIInboundMailServerConnection.assertEnabledConnectionIconOnCard(true);
        clickEditInboundMailConnectionAndFillServerAndPassword(inboundMailConnectionNewServer, inboundMailConnection);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIInboundMailServerConnection.assertServerOnCard(inboundMailConnection.getServer());

        GUIOutgoingServerConfigCard.goToOutgoingMailServerCard(smtpServer);
        GUIOutgoingServerConfigCard.clickEnable();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIOutgoingServerConfigCard.assertConnectionEnabled(smtpServer, false);
        clickEditOutgoingMailConnectionAndFillServerAndPassword(smtpServerNewServer, smtpServer);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIOutgoingServerConfigCard.assertServerOnCard(smtpServer.getServer());

        GUIMailProcessorRule.goToMailRuleCard(mailProcessorRule);
        GUIMailProcessorRule.clickSwitch();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMailProcessorRule.assertMailProcessorRuleEnabled(true);
        clickEditMailProcessorRuleAndFillTitle(mailProcessorRuleNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMailProcessorRule.assertTitleOnCard(mailProcessorRule.getTitle());

        accessMarkerMatrix.addAccessMarkerPermission(MAIL, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIInboundMailServerConnection.goToConnectionCard(inboundMailConnection);
        GUIInboundMailServerConnection.clickSwitch();
        GUIInboundMailServerConnection.assertEnabledConnectionIconOnCard(false);
        clickEditInboundMailConnectionAndFillServerAndPassword(inboundMailConnectionNewServer, inboundMailConnection);
        GUIForm.applyForm();
        GUIInboundMailServerConnection.assertServerOnCard(inboundMailConnectionNewServer);

        GUIOutgoingServerConfigCard.goToOutgoingMailServerCard(smtpServer);
        GUIOutgoingServerConfigCard.clickEnable();
        GUIOutgoingServerConfigCard.assertConnectionEnabled(smtpServer, true);
        clickEditOutgoingMailConnectionAndFillServerAndPassword(smtpServerNewServer, smtpServer);
        GUIForm.applyForm();
        GUIOutgoingServerConfigCard.assertServerOnCard(smtpServerNewServer);

        GUIMailProcessorRule.goToMailRuleCard(mailProcessorRule);
        GUIMailProcessorRule.clickSwitch();
        GUIMailProcessorRule.assertMailProcessorRuleEnabled(false);
        clickEditMailProcessorRuleAndFillTitle(mailProcessorRuleNewTitle);
        GUIForm.applyForm();
        GUIMailProcessorRule.assertTitleOnCard(mailProcessorRuleNewTitle);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Почта". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать подключение к серверу входящей почты inboundMailConnection</li>
     * <li>Создать подключение к серверу исходящей почты smtpServer</li>
     * <li>Создать правило обработки почты mailProcessorRule со скриптом script</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Почта"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Параметры подключения" вкладка "Входящая почта"</li>
     * <li>Нажать на пиктограмму "Удалить" у подключения inboundMailConnection в списке подключений,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что подключение inboundMailConnection присутствует в списке подключений</li>
     * <li>Перейти на вкладку "Исходящая почта"</li>
     * <li>Нажать на пиктограмму "Удалить" у подключения smtpServer в списке подключений,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что подключение smtpServer присутствует в списке подключений</li>
     * <li>Перейти в раздел "Правила обработки"</li>
     * <li>Нажать на пиктограмму "Удалить" у правила обработки mailProcessorRule в списке правил обработки,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что правило обработки mailProcessorRule присутствует в списке правил обработки</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Почта"</li>
     * <li>Нажать на пиктограмму "Удалить" у правила обработки mailProcessorRule в списке правил обработки,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что правило обработки mailProcessorRule отсутствует в списке правил обработки</li>
     * <li>Перейти в раздел "Параметры подключения" вкладка "Входящая почта"</li>
     * <li>Нажать на пиктограмму "Удалить" у подключения inboundMailConnection в списке подключений,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что подключение inboundMailConnection отсутствует в списке подключений</li>
     * <li>Перейти на вкладку "Исходящая почта"</li>
     * <li>Нажать на пиктограмму "Удалить" у подключения smtpServer в списке подключений,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что подключение smtpServer отсутствует в списке подключений</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionMailAccessMarker()
    {
        InboundMailConnection inboundMailConnection = DAOSchedulerTask.createInboundMailConnection(MailProtocol.IMAP4,
                false);
        DSLSchedulerTask.addInboundMailConnection(inboundMailConnection);

        SmtpServer smtpServer = DAOSmtpServer.create();
        DSLSmtpServer.add(smtpServer);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        MailProcessorRule mailProcessorRule = DAOMailProcessorRule.createMailProcessorRule();
        DSLMailProcessorRule.addReceiveMailRule(mailProcessorRule, script);

        accessMarkerMatrix.addAccessMarkerPermission(MAIL, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToInboundMailConnectionParameters();
        GUIInboundMailServerConnection.clickPictList(inboundMailConnection,
                GUIInboundMailServerConnection.DELETE_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIInboundMailServerConnection.assertConnectionExists(inboundMailConnection, true);

        GUIOutgoingServerConfigsList.goToOutgoingMailServersTab();
        GUIOutgoingServerConfigsList.clickPictList(smtpServer, GUIOutgoingServerConfigsList.DELETE_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIOutgoingServerConfigsList.assertConnectionExists(smtpServer);

        GUINavigational.goToMailProcessors();
        GUIMailProcessorRule.clickPictList(mailProcessorRule, GUIMailProcessorRule.DELETE_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMailProcessorRule.assertPresentMailRule(mailProcessorRule);

        accessMarkerMatrix.addAccessMarkerPermission(MAIL, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIMailProcessorRule.clickPictList(mailProcessorRule, GUIMailProcessorRule.DELETE_ACTION);
        GUIForm.confirmByYes();
        GUIMailProcessorRule.assertAbsenceMailRule(mailProcessorRule);
        mailProcessorRule.setExists(false);

        GUINavigational.goToInboundMailConnectionParameters();
        GUIInboundMailServerConnection.clickPictList(inboundMailConnection,
                GUIInboundMailServerConnection.DELETE_ACTION);
        GUIForm.confirmByYes();
        GUIInboundMailServerConnection.assertConnectionExists(inboundMailConnection, false);
        inboundMailConnection.setExists(false);

        GUIOutgoingServerConfigsList.goToOutgoingMailServersTab();
        GUIOutgoingServerConfigsList.clickPictList(smtpServer, GUIOutgoingServerConfigsList.DELETE_ACTION);
        GUIForm.confirmByYes();
        GUIOutgoingServerConfigsList.assertConnectionAbsence(smtpServer);
        smtpServer.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Очереди". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать очередь queue</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям" вкладка "Очереди"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Очереди"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Действия по событиям"</li>
     * <li>Перейти в карточку очереди queue</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствуют блоки с контентами "Свойства", "Количество сообщений в очереди"
     * и "Действия по событиям, обрабатываемые в очереди"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Очереди"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствуют блоки с контентами "Свойства", "Количество сообщений в очереди"
     * и "Действия по событиям, обрабатываемые в очереди"</li>
     * <li>Перейти в раздел "Действия по событиям" вкладка "Очереди"</li>
     * <li>Проверить, что присутствует вкладка "Очереди"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Действия по событиям"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionQueuesAccessMarker()
    {
        // Подготовка
        JMSQueue queue = DAOJMSQueue.create();
        DSLJmsQueue.addJMSQueue(queue);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToJMSQueues();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Очереди\"", tester.waitDisappear(Div.JMS_QUEUES_TAB));

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("eventactions:");

        GUIJMSQueueList.gotoJMSQueueCard(queue.getCode());
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIJMSQueueList.assertPresentJmsQueueCardInfoBlock(false);
        GUIJMSQueueList.assertPresentJmsQueueCardCountMessagesBlock(false);
        GUIJMSQueueList.assertPresentJmsQueueCardEventActionsInQueueBlock(false);

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);
        tester.refresh();

        GUIJMSQueueList.assertPresentJmsQueueCardInfoBlock(true);
        GUIJMSQueueList.assertPresentJmsQueueCardCountMessagesBlock(true);
        GUIJMSQueueList.assertPresentJmsQueueCardEventActionsInQueueBlock(true);

        GUINavigational.goToJMSQueues();
        Assert.assertTrue("Отсутствует вкладка \"Очереди\"", tester.find(Div.JMS_QUEUES_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("eventactions:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Очереди". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Очереди"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям" вкладка "Очереди"</li>
     * <li>Нажать кнопку "Добавить очередь" и заполнить поля на форме добавления очереди,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая очередь queue отсутствует в списке очередей.</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Очереди"</li>
     * <li>Нажать кнопку "Добавить очередь" и заполнить поля на форме добавления очереди,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Перейти в раздел "Действия по событиям" вкладка "Очереди"</li>
     * <li>Проверить, что создаваемая очередь queue присутствует в списке очередей</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionQueuesAccessMarker()
    {
        // Подготовка
        JMSQueue queue = DAOJMSQueue.create();

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToJMSQueues();
        clickAddJmsQueueAndFillForm(queue);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIJMSQueueList.advlist().content().asserts().rowsAbsence(queue);

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddJmsQueueAndFillForm(queue);
        GUIForm.applyForm();
        GUINavigational.goToJMSQueues();
        GUIJMSQueueList.advlist().content().asserts().rowsPresence(queue);
        queue.setExists(true);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Очереди". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в карточку очереди queue</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение queueNewTitle,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название очереди queue не изменилось</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Очереди"</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение queueNewTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название очереди queue - queueNewTitle</li>
     * </ol>
     */
    @Test
    public void testEditPermissionQueuesAccessMarker()
    {
        // Подготовка
        JMSQueue queue = DAOJMSQueue.create();
        DSLJmsQueue.addJMSQueue(queue);

        String queueNewTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIJMSQueueList.gotoJMSQueueCard(queue.getCode());
        GUIJMSQueueList.clickEdit();
        GUIJMSQueueList.fillTitle(queueNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIJMSQueueList.assertTitleOnCard(queue.getTitle());

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIJMSQueueList.clickEdit();
        GUIJMSQueueList.fillTitle(queueNewTitle);
        GUIForm.applyForm();
        GUIJMSQueueList.assertTitleOnCard(queueNewTitle);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Очереди". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать очередь queue</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Действия по событиям" вкладка "Очереди"</li>
     * <li>Нажать на пиктограмму "Удалить" у очереди queue в списке очередей,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что очередь queue присутствует в списке очередей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Очереди"</li>
     * <li>Нажать на пиктограмму "Удалить" у очереди queue в списке очередей,
     * затем нажать кнопку "Да" на форме подтверждения и проверить, что форма исчезла</li>
     * <li>Проверить, что очередь queue отсутствует в списке очередей</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionQueuesAccessMarker()
    {
        // Подготовка
        JMSQueue queue = DAOJMSQueue.create();
        DSLJmsQueue.addJMSQueue(queue);

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToJMSQueues();
        GUIJMSQueueList.advlist().content().clickPict(queue, GUIJMSQueueList.DELETE_ACTION);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIJMSQueueList.advlist().content().asserts().rowsPresence(queue);

        accessMarkerMatrix.addAccessMarkerPermission(QUEUES, DELETE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        GUIJMSQueueList.advlist().content().clickPict(queue, GUIJMSQueueList.DELETE_ACTION);
        GUIForm.confirmByYes();
        GUIJMSQueueList.advlist().content().asserts().rowsAbsence(queue);
        queue.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Подключения и конфигурации". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать конфигурацию импорта advimportConfig</li>
     * <li>Создать подключение конфигурации импорта advimportConnection</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Синхронизация"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствуют вкладки "Конфигурации", "Подключения"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Синхронизация"</li>
     * <li>Перейти на карточку подключения конфигурации импорта advimportConnection</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок с контентом "Свойства"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует блок с контентом "Свойства"</li>
     * <li>Перейти в раздел "Синхронизация"</li>
     * <li>Проверить, что присутствуют вкладки "Конфигурации", "Подключения"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы" и раздел "Синхронизация"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionConnectionsAndConfigurationsAccessMarker()
    {
        AdvimportConnection advimportConnection = DAOAdvimport.createConnection();
        DSLAdvimport.addConnection(advimportConnection);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdvimport();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Конфигурации\"", tester.waitDisappear(Div.CONFIG_LIST_TAB));
        Assert.assertTrue("Присутствует вкладка \"Подключения\"", tester.waitDisappear(Div.CONNECTION_LIST_TAB));

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("advimport:");

        GUIAdvimportList.goToCard(advimportConnection);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdvimportList.assertPresentAdvImportConnectionCardInfoBlock(false);

        accessMarkerMatrix.addAccessMarkerPermission(CONNECTIONS_AND_CONFIGURATIONS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdvimportList.assertPresentAdvImportConnectionCardInfoBlock(true);

        GUINavigational.goToAdvimport();
        Assert.assertTrue("Отсутствует вкладка \"Конфигурации\"",
                tester.find(Div.CONFIG_LIST_TAB).isDisplayed());
        Assert.assertTrue("Отсутствует вкладка \"Подключения\"",
                tester.find(Div.CONNECTION_LIST_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("advimport:");
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Работа со встроенными приложениями". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение embeddedApplication</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Приложения"</li>
     * <li>Перейти на карточку встроенного приложения embeddedApplication</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что на карточке встроенного приложения embeddedApplication отсутствует блок "Свойства"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что на карточке встроенного приложения embeddedApplication присутствует блок "Свойства"</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Приложения"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionEmbeddedApplicationsAccessMarker()
    {
        // Подготовка
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createExternalApplication(null);
        DSLEmbeddedApplication.add(embeddedApplication);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToEmbeddedApplications();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("applications:");

        GUIEmbeddedApplication.goToCard(embeddedApplication);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEmbeddedApplication.assertAbsenceInfoBlock();

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        tester.refresh();
        GUIEmbeddedApplication.assertPresentInfoBlock();

        GUINavigational.goToEmbeddedApplications();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("applications:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Работа со встроенными приложениями". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Нажать кнопку "Добавить приложение", заполнить форму добавления встроенного приложения,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемое ВП embeddedApplication отсутствует в списке встроенных приложений</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <li>Нажать кнопку "Добавить приложение", заполнить форму добавления встроенного приложения,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемое ВП embeddedApplication присутствует в списке встроенных приложений</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionEmbeddedApplicationsAccessMarker()
    {
        // Подготовка
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createExternalApplication(
                ModelUtils.createText(10));

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToEmbeddedApplications();
        clickAddEmbeddedApplicationAndFillForm(embeddedApplication);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEmbeddedApplicationList.assertAbsenceEmbeddedApplication(embeddedApplication);

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, CREATE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        clickAddEmbeddedApplicationAndFillForm(embeddedApplication);
        GUIForm.applyForm();
        GUIEmbeddedApplicationList.assertPresenceEmbeddedApplication(embeddedApplication);
        embeddedApplication.setExists(true);

    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Работа со встроенными приложениями". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение embeddedApplication</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Нажать на иконку "Включить" напротив встроенного приложения embeddedApplication</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что приложение embeddedApplication выключено</li>
     * <li>Перейти в карточку приложения embeddedApplication</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение newEmbeddedApplicationTitle,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название приложения embeddedApplication не изменилось</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <li>Открыть форму редактирования и в поле "Название" установить значение newEmbeddedApplicationTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название приложения embeddedApplication - newEmbeddedApplicationTitle</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Нажать на иконку "Включить" напротив встроенного приложения embeddedApplication</li>
     * <li>Проверить, что приложение embeddedApplication включено</li>
     * </ol>
     */
    @Test
    public void testEditPermissionEmbeddedApplicationsAccessMarker()
    {
        // Подготовка
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createExternalApplication(
                ModelUtils.createText(10));
        embeddedApplication.setEnable(false);
        DSLEmbeddedApplication.add(embeddedApplication);

        String newEmbeddedApplicationTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.clickEnable(embeddedApplication);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEmbeddedApplicationList.assertEnabled(embeddedApplication, false);

        GUIEmbeddedApplication.goToCard(embeddedApplication);
        GUIEmbeddedApplicationForm.clickEdit();
        GUIEmbeddedApplicationForm.setTitle(embeddedApplication);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEmbeddedApplicationCard.assertTitle(embeddedApplication.getTitle());

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIEmbeddedApplicationForm.clickEdit();
        GUIEmbeddedApplicationForm.setTitle(newEmbeddedApplicationTitle);
        GUIForm.clickApply();
        GUIEmbeddedApplicationCard.assertTitle(newEmbeddedApplicationTitle);

        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.clickEnable(embeddedApplication);
        GUIEmbeddedApplicationList.assertEnabled(embeddedApplication, true);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Работа со встроенными приложениями"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать встроенное приложение embeddedApplication</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Нажать на иконку "Удалить" напротив встроенного приложения embeddedApplication</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в списке приложений содержится встроенное приложение embeddedApplication</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Работа со встроенными приложениями"</li>
     * <li>Нажать на иконку "Удалить" напротив встроенного приложения embeddedApplication,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что в списке приложений отсутствует встроенное приложение embeddedApplication</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionEmbeddedApplicationsAccessMarker()
    {
        // Подготовка
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createExternalApplication(null);
        DSLEmbeddedApplication.add(embeddedApplication);

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.clickDelete(embeddedApplication);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIEmbeddedApplicationList.assertPresenceEmbeddedApplication(embeddedApplication);

        accessMarkerMatrix.addAccessMarkerPermission(EMBEDDED_APPLICATIONS, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIEmbeddedApplicationList.clickDelete(embeddedApplication);
        GUIForm.confirmByYes();
        GUIEmbeddedApplicationList.assertAbsenceEmbeddedApplication(embeddedApplication);
        embeddedApplication.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Профили администрирования". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать профиль администратора profile</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Профили администрирования"</li>
     * <li>Перейти на карточку профиля администрирования profile</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Профили администрирования"</li>
     * <li>Обновить страницу</li>
     * <li>Перейти в раздел "Профили администрирования"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Профили администрирования"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionAdministrationProfilesAccessMarker()
    {
        AdminProfile profile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(profile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToAdminProfiles();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("adminProfiles:");

        GUIAdminProfile.goToAdminProfileCard(profile);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(ADMINISTRATION_PROFILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUINavigational.goToAdminProfiles();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("adminProfiles:");
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Планировщик". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo</li>
     * <li>Создать задачу планировщика schedulerTaskScript типа "Скрипт" со скриптом scriptInfo</li>
     * <li>Создать правило trigger задачи планировщика schedulerTaskScript</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Планировщик задач"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Планировщик задач"</li>
     * <li>Перейти на карточку задачи планировщика schedulerTaskScript</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку правила trigger</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Планировщик"</li>
     * <li>Обновить страницу</li>
     * <li>Перейти в раздел "Планировщик задач"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Планировщик задач"</li>
     * <li>Перейти на карточку задачи планировщика schedulerTaskScript</li>
     * </ol>
     */
    @Test
    public void testViewPermissionSchedulerAccessMarker()
    {
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        SchedulerTaskScript schedulerTaskScript = DAOSchedulerTask.createScriptRule(scriptInfo);
        DSLSchedulerTask.addTask(schedulerTaskScript);

        Trigger trigger = DAOSchedulerTask.createConcreteTrigger(schedulerTaskScript,
                DateTimeUtils.getRandomDateTimeddMMyyyyHHmm());
        DSLSchedulerTask.addTrigger(trigger);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScheduler();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("scheduler:");

        GUISchedulerTaskList.goToTaskCard(schedulerTaskScript);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUISchedulerTaskList.goToTriggerCard(trigger);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(SCHEDULER, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUINavigational.goToScheduler();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("scheduler:");

        GUISchedulerTaskList.goToTaskCard(schedulerTaskScript);
        GUISchedulerTaskList.goToTriggerCard(trigger);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Планировщик"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo</li>
     * <li>Создать задачу планировщика schedulerTaskScript типа "Скрипт" со скриптом scriptInfo</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Планировщик"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Планировщик задач"</li>
     * <li>Нажать на пиктограмму "Удалить" у задачи планировщика schedulerTaskScript, затем нажать кнопку "Да" на
     * форме подтверждения</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что задача планировщика schedulerTaskScript присутствует в списке задач планировщика</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Планировщик"</li>
     * <li>Нажать на пиктограмму "Удалить" у задачи планировщика schedulerTaskScript,
     * затем нажать кнопку "Да" на форме подтверждения и проверить, что форма исчезла</li>
     * <li>Проверить, что задача планировщика schedulerTaskScript отсутствует в списке задач планировщика</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionSchedulerAccessMarker()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        SchedulerTaskScript schedulerTaskScript = DAOSchedulerTask.createScriptRule(scriptInfo);
        DSLSchedulerTask.addTask(schedulerTaskScript);

        accessMarkerMatrix.addAccessMarkerPermission(SCHEDULER, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScheduler();
        GUISchedulerTaskList.advlist().content().clickPict(schedulerTaskScript, GUISchedulerTaskList.PICT_DELETE);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUISchedulerTaskList.advlist().content().asserts().rowsPresence(schedulerTaskScript);

        accessMarkerMatrix.addAccessMarkerPermission(SCHEDULER, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUISchedulerTaskList.advlist().content().clickPict(schedulerTaskScript, GUISchedulerTaskList.PICT_DELETE);
        GUIForm.confirmByYes();
        GUISchedulerTaskList.advlist().content().asserts().rowsAbsence(schedulerTaskScript);
        schedulerTaskScript.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Структуры". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать структуру structuredObjectsView</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Структуры"</li>
     * <li>Перейти на карточку структуры structuredObjectsView</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствуют блоки "Свойства", "Элементы структуры", "Места использования"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Структуры"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствуют блоки "Свойства", "Элементы структуры", "Места использования"</li>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Структуры"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionStructuresAccessMarker()
    {
        // Подготовка
        StructuredObjectsView structuredObjectsView = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(structuredObjectsView);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToStructuredObjectsViews();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("structureSettings:");

        GUIStructuredObjectsView.goToCard(structuredObjectsView);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIStructuredObjectsView.assertAbsenceInfoBlock();
        GUIStructuredObjectsView.assertAbsenceItemsBlock();
        GUIStructuredObjectsView.assertPresentUsageBlock(false);

        accessMarkerMatrix.addAccessMarkerPermission(STRUCTURES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIStructuredObjectsView.assertPresentInfoBlock();
        GUIStructuredObjectsView.assertPresentItemsBlock();
        GUIStructuredObjectsView.assertPresentUsageBlock(true);

        GUINavigational.goToStructuredObjectsViews();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("structureSettings:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Структуры". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Нажать кнопку "Добавить структуру" и заполнить форму добавления структуры,
     * затем нажать кнопку Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая структура structuredObjectsView отсутствует в списке структур</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Структура"</li>
     * <li>Нажать кнопку "Добавить структуру" и заполнить форму добавления структуры,
     * затем нажать кнопку Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемая структура structuredObjectsView присутствует в списке структур</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionStructuresAccessMarker()
    {
        // Подготовка
        StructuredObjectsView structuredObjectsView = DAOStructuredObjectsView.create();

        accessMarkerMatrix.addAccessMarkerPermission(STRUCTURES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToStructuredObjectsViews();
        clickAddStructureAndFillForm(structuredObjectsView);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIStructuredObjectsView.advlist().content().asserts().rowsAbsence(structuredObjectsView);

        accessMarkerMatrix.addAccessMarkerPermission(STRUCTURES, CREATE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUINavigational.goToStructuredObjectsViews();
        clickAddStructureAndFillForm(structuredObjectsView);
        GUIForm.applyForm();
        GUIStructuredObjectsView.assertThatCard(structuredObjectsView);
        structuredObjectsView.setExists(true);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Структуры"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать структуру structuredObjectsView</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Структуры"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Нажать на пиктограмму "Удалить" у структуры structuredObjectsView в списке структур,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что структура structuredObjectsView присутствует в списке структур</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Структуры"</li>
     * <li>Нажать на пиктограмму "Удалить" у структуры structuredObjectsView в списке структур,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что структура structuredObjectsView отсутствует в списке структур</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionStructuresAccessMarker()
    {
        // Подготовка
        StructuredObjectsView structuredObjectsView = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(structuredObjectsView);

        accessMarkerMatrix.addAccessMarkerPermission(STRUCTURES, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);
        GUINavigational.goToStructuredObjectsViews();
        GUIStructuredObjectsView.deleteStructure(structuredObjectsView);
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIStructuredObjectsView.advlist().content().asserts().rowsPresence(structuredObjectsView);

        accessMarkerMatrix.addAccessMarkerPermission(STRUCTURES, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIStructuredObjectsView.deleteStructure(structuredObjectsView);
        GUIForm.confirmByYes();
        GUIStructuredObjectsView.advlist().content().asserts().rowsAbsence(structuredObjectsView);
        structuredObjectsView.setExists(false);
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Интерфейс администратора". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Удалить все права из матрицы маркеров доступа accessMarkerMatrix</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует кнопка смены интерфейса</li>
     * <li>Проверить, что отсутствует панель поиска</li>
     * <li>Проверить, что присутствует кнопка "Выйти"</li>
     * <li>Проверить, что отсутствует навигационное дерево левого меню</li>
     * <li>Проверить, что отсутствует системный логотип</li>
     * <li>Добавить в матрицу маркеров доступа accessMarkerMatrix право на доступ
     * к маркеру доступа "Интерфейс администратора"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует панель поиска</li>
     * <li>Проверить, что присутствует навигационное дерево левого меню</li>
     * <li>Проверить, что присутствует системный логотип</li>
     * </ol>
     */
    @Test
    public void testAllPermissionAdministrationInterfaceAccessMarker()
    {
        accessMarkerMatrix.removeAllPermissions();
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        GUILogon.login(superUser);
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUINavigational.assertSwitchInterfaceAbsence();
        GUISearch.assertAbsentSearchPanel();
        GUILogon.assertLogoutButtonPresent();
        GUIAdminNavigationTree.assertAbsentLeftMenuNavigationTree();
        GUIInterface.assertAbsentSystemLogo();

        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUISearch.assertPresentSearchPanel();
        GUIAdminNavigationTree.assertPresentLeftMenuNavigationTree();
        GUIInterface.assertPresentSystemLogo();
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Вход под сотрудником". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать отдел ou типа ouCase и в нем сотрудника employee</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Список активных пользователей"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Попытаться авторизоваться под сотрудником выполнив скрипт:<pre>
     *      def authImpl = beanFactory.getBean("authorizationRunnerServiceImpl")
     *      authImpl.callAsSuperUser(superUser, {
     *          	def dispatch = beanFactory.getBean("dispatch")
     *             	def action = new ru.naumen.sec.shared.LoginAsUserAction(employee)
     *             	dispatch.execute(action)
     *      })
     *     </pre>
     * </li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что кнопка "Войти под сотрудником" отсутствует</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Вход под сотрудником"</li>
     * <li>Обновить страницу</li>
     * <li>Открыть форму входа под сотрудником, выбрать сотрудника employee, затем нажать кнопку "Сохранить"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionLoginAsEmployeeAccessMarker()
    {
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        DSLBo.add(employee);

        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(ACTIVE_USERS, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        ScriptRunner.assertError(LOGIN_AS_USER_SCRIPT.formatted(superUser.getUuid(), employee.getUuid()),
                ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUILogon.login(superUser);
        GUINavigational.goToAdministration();

        GUIAdmin.assertLoginAsUserIsPresent(false);

        accessMarkerMatrix.addAccessMarkerPermission(LOGIN_AS_EMPLOYEE, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        tester.refresh();

        GUIAdmin.openLoginForm();
        GUIAdmin.selectUserInSelect(ou, employee);
        GUIForm.clickApply();
    }

    /**
     * Тестирование действия "Доступ" маркера доступа "Переиндексация". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Нажать кнопку "Переиндексировать"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на доступ
     * по маркеру доступа "Переиндексация"</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Переиндексировать"</li>
     * </ol>
     */
    @Test
    public void testAllPermissionReindexingAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        GUIMetaClass.expandInfoPanelAndClickRefresh();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(REINDEXING, ALL);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIMetaClass.refresh();
    }

    private static void clickEditInboundMailConnectionAndFillServerAndPassword(String inboundMailConnectionNewServer,
            InboundMailConnection inboundMailConnection)
    {
        GUIInboundMailServerConnection.clickEdit();
        GUISchedulerTaskList.fillServerInboundMailConnection(inboundMailConnectionNewServer);
        GUISchedulerTaskList.fillPasswordMailInboundConnection(inboundMailConnection);
    }

    private static void clickEditOutgoingMailConnectionAndFillServerAndPassword(String smtpServerNewServer,
            SmtpServer smtpServer)
    {
        GUIOutgoingServerConfigCard.clickEdit();
        GUIOutgoingServerConfigCard.fillServer(smtpServerNewServer);
        GUIOutgoingServerConfigCard.fillServerPassword(smtpServer.getPassword());
    }

    private static void clickEditMailProcessorRuleAndFillTitle(String mailProcessorRuleNewTitle)
    {
        GUIMailProcessorRule.clickEditRule();
        GUIMailProcessorRule.fillTitle(mailProcessorRuleNewTitle);
    }

    private static void clickAddJmsQueueAndFillForm(JMSQueue queue)
    {
        GUIJMSQueueList.advlist().toolPanel().clickAdd();
        GUIJMSQueueList.fillAddForm(queue);
    }

    private static void clickAddEmbeddedApplicationAndFillForm(EmbeddedApplication embeddedApplication)
    {
        GUIEmbeddedApplicationList.clickAdd();
        GUIEmbeddedApplicationForm.fillAdd(embeddedApplication);
    }

    private static void clickAddStructureAndFillForm(StructuredObjectsView structuredObjectsView)
    {
        GUIStructuredObjectsView.clickAddStructure();
        GUIStructuredObjectsView.fillAddForm(structuredObjectsView);
    }
}