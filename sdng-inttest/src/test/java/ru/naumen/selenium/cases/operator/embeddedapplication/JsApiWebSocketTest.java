package ru.naumen.selenium.cases.operator.embeddedapplication;

import java.io.File;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.websocket.DSLWebSocket;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование работы с WebSocket-каналом через JS API
 *
 * <AUTHOR>
 * @since 13.07.2018
 */
class JsApiWebSocketTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    /**
     * Тестирование закрытия WebSocket сессии клиента JS API при
     * инвалидации HTTP сессии по таймауту, указанному в лицензии.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157542403
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить файл лицензии, в котором время сессии для конкурентной лицензии 1 минута</li>
     * <li>Создать сотрудника employee и выдать ему конкурентную лицензию</li>
     * <li>Создать и загрузить встроенное приложение application, которое через JS API
     * подключается к WebSocket каналу и проверяет подключение</li>
     * <li>Создать и загрузить встроенное приложение applicationAfterTimeout, которое через JS API
     * подключается к WebSocket каналу и проверяет подключение через 60 секунд</li>
     * <li>Вывести на карточку компании приложение application</li>
     * <li>Вывести на карточку компании приложение applicationAfterTimeout</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться в систему под employee</li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить, что WebSocket клиенту, который используется в JS API, удалось
     * подключится к WebSocket каналу</li>
     * <li>Подождать 1 минуту, пока истечет HTTP сессия по таймауту</li>
     * <li>Проверить, что клиент, который используется в JS API, отключен от WebSocket канала и
     * не пытается переподключиться к нему</li>
     * </ol>
     */
    @Test
    void testCloseWebSocketSessionAfterHttpSessionIsExpired()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.LICENSE_TIME_SESSION_PATH);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(employee);

        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                "jsApi.ws.connect()");
        DSLEmbeddedApplication.add(application);
        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(SharedFixture.root());
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(content);
        DSLWebSocket.waitWebSocketClientConnected(tester.getWebDriver(), content.getXpathId());

        // ждем инвалидации сессии по таймауту
        WaitTool.waitMills(60_000);
        DSLWebSocket.waitWebSocketClientDisconnected(tester.getWebDriver(), content.getXpathId());
    }

    /**
     * Тестирование того, что JS API не переподключается к WebSocket каналу после
     * инвалидации HTTP сессии и последующего вызова метода любого метода
     * JS API для работы с WebSocket каналом.
     * <br>
     * <b>P.S.</b> Вообще, это не принципиально, на какое событие отправлять сообщение в WebSocket.
     * В тесте отправляется при раскрытии приложения на полный экран, только потому что это
     * поведение легко автоматизировать.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157542403
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и загрузить файл встроенного приложения application, которое при загрузке
     * подключается к WebSocket каналу, а при раскрытии контента приложения на полный экран отправляет
     * сообщение в WebSocket канал.
     * <pre>
     *   -----------------------------------------------------------------------
     *   jsApi.ws.connect()
     *   jsApi.events.onFullscreenEnabled(_ => {
     *       return jsApi.ws.send('dest', 'msg'))
     *   })
     *   -----------------------------------------------------------------------
     * </pre>
     * <li>Создать и загрузить встроенное приложение applicationAfterTimeout, которое через JS API
     * подключается к WebSocket каналу и проверяет подключение через 60 секунд</li>
     * </li>
     * <li>Добавить встроенное приложение application на карточку компании</li>
     * <li>Добавить встроенное приложение applicationAfterTimeout на карточку компании</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником {@link SharedFixture#employee() employee}</li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить, что JS API подключилось к WebSocket каналу</li>
     * <li>Инвалидировать сессию пользователя {@link SharedFixture#employee() employee}</li>
     * <li>Проверить, что WebSocket клиент в JS API был отключен от WebSocket канала</li>
     * </ol>
     */
    @Test
    void testWebSocketDoesntReconnectAfterSessionIsExpired()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(temp,
                "jsApi.ws.connect()\n" +
                "jsApi.events.onFullscreenEnabled(_ => jsApi.ws.send('dest', 'msg'))");
        application.setFullscreenAllowed(true);
        DSLEmbeddedApplication.add(application);
        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(DAORootClass.create(), application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        Bo employee = SharedFixture.employee();
        GUILogon.login(employee);
        GUIBo.goToCard(SharedFixture.root());
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        DSLWebSocket.waitWebSocketClientConnected(tester.getWebDriver(), applicationContent.getXpathId());

        DSLSession.disconectSessionByUuid(employee.getUuid());
        WaitTool.waitMills(60_000);
        DSLWebSocket.waitWebSocketClientDisconnected(tester.getWebDriver(), applicationContent.getXpathId());
    }

    /**
     * Тестирование того, что после нажатия на кнопку "Выйти" на одной вкладке, JS API корректно
     * отключается от WebSocket канала и не пытается к нему переподключится на другой вкладке.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00876
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157542403
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать и загрузить файл встроенного приложения application, которое при загрузке
     * подключается к WebSocket каналу. Исходный код ВП application:
     * <pre>
     *   -----------------------------------------------------------------------
     *   jsApi.ws.connect()
     *   -----------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Создать и загрузить встроенное приложение applicationAfterTimeout, которое через JS API
     * подключается к WebSocket каналу и проверяет подключение через 60 секунд</li>
     * <li>Добавить встроенное приложение application на карточку компании</li>
     * <li>Добавить встроенное приложение applicationAfterTimeout на карточку компании</li>
     * <b>Действия и проверки</b>
     * <li>Залогиниться под сотрудником </li>
     * <li>Перейти на карточку компании</li>
     * <li>Проверить, что JS API подключилось к WebSocket каналу</li>
     * <li>Продублировать текущую вкладку и перейти на новую вкладку</li>
     * <li>Разлогиниться из системы (нажать на кнопку "Выйти")</li>
     * <li>Перейти на предыдущую вкладку</li>
     * <li>Проверить, что WebSocket клиент в JS API на текущей вкладке
     * отключен от WebSocket канала и не пытается переподключиться к нему</li>
     * </ol>
     */
    @Test
    void testWebSocketDoesntReconnectAfterLogout()
    {
        // Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplication(
                temp, "jsApi.ws.connect()");
        DSLEmbeddedApplication.add(application);
        ContentForm applicationContent = DAOContentCard.createEmbeddedApplication(
                SharedFixture.root().getMetaclassFqn(), application);
        DSLContent.add(applicationContent);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        GUIEmbeddedApplication.waitForEmbeddedApplicationLoading(applicationContent);
        DSLWebSocket.waitWebSocketClientConnected(tester.getWebDriver(), applicationContent.getXpathId());

        tester.doInNewTab(() ->
        {
            // Ждем пока на новой вкладке прогрузиться кнопка "Выйти".
            // Это необходимо, т.к. метод WebTester#doInNewTab не дожидается пока
            // содержимое вкладки полностью загрузиться, а как это фиксить в самом методе - не очевидно.
            GUILogon.assertLogoutButtonPresent();
            GUILogon.logout();
            return null;
        });

        WaitTool.waitMills(60_000);
        DSLWebSocket.waitWebSocketClientDisconnected(tester.getWebDriver(), applicationContent.getXpathId());
    }
}