package ru.naumen.selenium.cases.operator.embeddedapplication;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.embeddedapplication.JsApiAttributeFormatTester;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BackTimerType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на формат данных при получении объектов через jsApi.utils.*
 *
 * <AUTHOR>
 * @since March 17, 2021
 */
class JsApiUtilsFormatsTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private final static String UTILS_CALL_TEMPLATE = "%s\n" +
                                                      "var subjectUuid = jsApi.extractSubjectUuid()\n" +
                                                      "var metaClass = subjectUuid.split('$')[0]\n" +
                                                      "var title = (!metaClass.includes('employee')) ? 'title' : "
                                                      + "'firstName'\n"
                                                      +
                                                      "var called = false\n" +
                                                      "jsApi.events.addSubjectChangeListener(title, function (result)"
                                                      + " {\n"
                                                      +
                                                      "    if (called) {\n" +
                                                      "        return\n" +
                                                      "    }\n" +
                                                      "    jsApi.utils.%s.then((result) => {\n" +
                                                      "        for (const attribute of attributes) {\n" +
                                                      "            logIt(attribute, result[attribute])\n" +
                                                      "        }\n" +
                                                      "    })\n" +
                                                      "    called = true\n" +
                                                      "})";

    private static MetaClass userClass, userCase, employeeCase;
    private static Attribute[] commonAttributes;
    private static ContentForm commonContent, employeeContent;
    private static JsApiAttributeFormatTester commonFormatTester, employeeFormatTester;
    private static Attribute fileAttr;

    private Bo employee, userBo;

    /**
     * <b>Общая подготовка для всех тестов</b>
     * <ol>
     *     <li>Создать класс userClass с ответственным и сменой ЖЦ и его подтип userCase,
     *     подтип сотрудника employeeCase</li>
     *     <li>Запланировать удаление объектов типов userCase и employeeCase</li>
     *     <li>Создать в типе userCase группу атрибутов с {@link #initCommonAttributes() общими атрибутами},
     *     добавить её на карточку типа userCase</li>
     *     <li>Создать в типе employeeCase группу атрибутов с
     *     {@link #initEmployeeAttributes() атрибутами, которые не тестируемы в типе userClass},
     *     добавить её на карточку типа employeeCase</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWith().responsible().workFlow().create();
        userCase = DAOUserCase.create(userClass);
        employeeCase = DAOEmployeeCase.create(DAOEmployeeCase.createClass());
        DSLMetaClass.add(userClass, userCase, employeeCase);

        Cleaner.afterAllTest(true, () ->
        {
            DSLBo.deleteAllObjectsOfMetaclass(employeeCase);
            DSLBo.deleteAllObjectsOfMetaclass(userCase);
        });

        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        commonAttributes = initCommonAttributes();
        DSLGroupAttr.addToGroup(groupAttr, commonAttributes);

        commonFormatTester = new JsApiAttributeFormatTester(commonAttributes);

        GroupAttr groupAttr2 = DAOGroupAttr.createSystem(employeeCase);
        Attribute[] attributes2 = initEmployeeAttributes();
        DSLGroupAttr.addToGroup(groupAttr2, attributes2);

        employeeFormatTester = new JsApiAttributeFormatTester(attributes2);

        commonContent = DSLContent.getDefaultCardContent(userCase);
        employeeContent = DAOContentCard.createPropertyList(employeeCase, groupAttr2);
        DSLContent.add(employeeContent);
    }

    /**
     * <b>Общая подготовка</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая часть подготовки для всех тестов}</li>
     *     <li>Создать объект userBo типа userCase и сотрудника employee типа employeeCase</li>
     * </ol>
     */
    @BeforeEach
    public void setUp()
    {
        userBo = DAOUserBo.create(userCase);
        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee, userBo);
    }

    /**
     * <b>Создание атрибутов в пользовательском типе для тестирования jsApi</b>
     */
    private static Attribute[] initCommonAttributes()
    {
        // создание обычных атрибутов
        Attribute integerAttr = DAOAttribute.createInteger(userClass);
        Attribute doubleAttr = DAOAttribute.createDouble(userClass);
        Attribute booleanAttr = DAOAttribute.createBoolTypeEditCheckbox(userClass.getFqn(), Boolean.FALSE);

        // создание атрибутов, связанных с метаклассом
        Attribute metaClassAttr = SysAttribute.metaClass(userClass);
        Attribute caseListAttr = DAOAttribute.createCaseList(userClass, DAOEmployeeCase.createClass());

        // создание строковых атрибутов
        Attribute stringAttr = DAOAttribute.createString(userClass);
        Attribute textAttr = DAOAttribute.createText(userClass);
        Attribute textRtfAttr = DAOAttribute.createTextRTF(userClass.getFqn());
        Attribute sourceCodeAttr = DAOAttribute.createSourceCode(userClass.getFqn());
        Attribute hyperlinkAttr = DAOAttribute.createHyperlink(userClass.getFqn());
        Attribute stateAttr = SysAttribute.state(userClass);

        // создание дат
        Attribute dateAttr = DAOAttribute.createDate(userClass);
        Attribute dateTimeAttr = DAOAttribute.createDateTimeWithEditTimeType(userClass.getFqn());
        Attribute dtIntervalAttr = DAOAttribute.createTimeInterval(userClass);

        // создание ссылочных атрибутов
        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null);
        Attribute aggregateAttr2 = DAOAttribute.createAggregate(userClass, AggregatedClasses.TEAM, null, null);
        Attribute aggregateAttr3 = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU_AND_TEAM, null, null);
        Attribute responsibleAttr = SysAttribute.responsible(userClass);
        Attribute objectAttr = DAOAttribute.createObjectLink(userClass, DAOEmployeeCase.createClass(), null);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, DAOEmployeeCase.createClass());
        Attribute objectAttr2 = DAOAttribute.createObjectLink(userClass, userClass, null);
        Attribute backBOLinksAttr = DAOAttribute.createBackBOLinks(userClass, objectAttr2);
        fileAttr = DAOAttribute.createFile(userClass);

        // Создание справочников
        Catalog catalog = DAOCatalog.createSystem(SystemCatalog.CLOSURECODE);
        Attribute catalogItemAttr = DAOAttribute.createCatalogItem(userClass, catalog, null);
        Attribute catalogItemsAttr = DAOAttribute.createCatalogItemSet(userClass, catalog);

        DSLAttribute.add(integerAttr, doubleAttr, booleanAttr, caseListAttr, stringAttr, textAttr, textRtfAttr,
                sourceCodeAttr, hyperlinkAttr, dateAttr, dateTimeAttr, dtIntervalAttr, aggregateAttr, aggregateAttr2,
                aggregateAttr3, objectAttr, boLinksAttr, objectAttr2, backBOLinksAttr, fileAttr, catalogItemAttr,
                catalogItemsAttr);

        // создание таймеров
        Attribute timeZoneAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), null);
        DSLAttribute.add(timeZoneAttr);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        TimerDefinition counter = DAOTimerDefinition.createAstroTimerByStatus(userClass.getFqn(),
                timeZoneAttr.getCode(), registered);
        DSLTimerDefinition.add(counter);

        Attribute timerAttr = DAOAttribute.createTimer(userClass.getFqn(), counter, TimerType.TIMER_STATUS_VIEW);
        Attribute timerAttr2 = DAOAttribute.createTimer(userClass.getFqn(), counter, TimerType.TIMER_ELAPSED_VIEW);
        Attribute backTimerAttr = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_ALLOWANCE_VIEW);
        Attribute backTimerAttr2 = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_DEADLINE_VIEW);
        Attribute backTimerAttr3 = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_STATUS_VIEW);
        Attribute backTimerAttr4 = DAOAttribute.createBackTimer(userClass.getFqn(), counter,
                BackTimerType.BACKTIMER_YES_NO);
        DSLAttribute.add(timerAttr, timerAttr2, backTimerAttr, backTimerAttr2, backTimerAttr3, backTimerAttr4);

        return new Attribute[] { integerAttr, doubleAttr, booleanAttr, metaClassAttr, caseListAttr, stringAttr,
                textAttr, textRtfAttr, sourceCodeAttr, hyperlinkAttr, stateAttr, dateAttr, dateTimeAttr, dtIntervalAttr,
                aggregateAttr, aggregateAttr2, aggregateAttr3, responsibleAttr, objectAttr, boLinksAttr,
                backBOLinksAttr, fileAttr, catalogItemAttr, catalogItemsAttr, timerAttr, timerAttr2, backTimerAttr,
                backTimerAttr2, backTimerAttr3, backTimerAttr4 };
    }

    /**
     * <b>Создание атрибутов в типе Сотрудник для тестирования jsApi</b>
     */
    private static Attribute[] initEmployeeAttributes()
    {
        Attribute secGroupsAttr = SysAttribute.employeeSecGroups(employeeCase);
        Attribute licenseAttr = SysAttribute.license(employeeCase);

        return new Attribute[] { secGroupsAttr, licenseAttr };
    }

    /**
     * Проверка правильности формата передачи и ответа для обычных атрибутов для метода jsApi.utils.get
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createGetApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.get с обычными атрибутами}</li>
     *     <li>Создать на карточке userClass контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта userBo и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика получения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testGetWithCommonAttributes()
    {
        testWithCommonAttributes(createGetApplication(commonFormatTester));
    }

    /**
     * Проверка правильности формата передачи и ответа для особых атрибутов сотрудника для метода jsApi.utils.get
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createGetApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.get c особыми атрибутами сотрудника}</li>
     *     <li>Создать на карточке employeeCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта employee и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика получения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testGetWithEmployeeAttributes()
    {
        testWithEmployeeAttributes(createGetApplication(employeeFormatTester));
    }

    /**
     * <b>Создать встроенное приложение utilsApplication для тестирования метода jsApi.utils.get</b>
     * <ol>
     *     <li>Создать файл встроенного приложения utilsFileName для тестирования метода jsApi.utils.findFirst,
     *     в качестве остальных значений атрибутов типа использовать
     *     {@link JsApiAttributeFormatTester#getAttributeValues()} сгенерированные значения для атрибутов}
     *     <pre>
     *         %объявление функции logIt для логирования атрибутов в нужном формате%
     *         var subjectUuid = jsApi.extractSubjectUuid()
     *         var metaClass = subjectUuid.split('$')[0]
     *         var title = (!metaClass.includes('employee')) ? 'title' : 'firstName'
     *         var called = false
     *         jsApi.events.addSubjectChangeListener(title, function (result) {
     *             if (called) {
     *                 return
     *             }
     *             jsApi.utils.get(metaClass, {uuid: subjectUuid}).then((result) => {
     *                 for (const attribute of attributes) {
     *                     logIt(attribute, result[attribute])
     *                 }
     *             })
     *             called = true
     *         })
     *     </pre></li>
     *     <li>Добавляем в систему встроенное приложение utilsApplication, созданное из файла utilsFileName</li>
     * </ol>
     */
    private EmbeddedApplication createGetApplication(JsApiAttributeFormatTester formatTester)
    {
        String utilsJsContent = String.format(UTILS_CALL_TEMPLATE,
                formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID), "get(subjectUuid)");
        String utilsFileName = DAOEmbeddedApplication.createApplicationWithJs(temp, utilsJsContent)
                .getAbsolutePath();

        EmbeddedApplication utilsApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                utilsFileName);
        DSLEmbeddedApplication.add(utilsApplication);

        return utilsApplication;
    }

    /**
     * Проверка правильности формата передачи и ответа для обычных атрибутов для метода jsApi.utils.findFirst
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createFindApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.findFirst с обычными атрибутами}</li>
     *     <li>Создать на карточке userClass контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта userBo и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика поиска объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testFindWithCommonAttributes()
    {
        testWithCommonAttributes(createFindApplication(commonFormatTester));
    }

    /**
     * Проверка правильности формата передачи и ответа для особых атрибутов сотрудника для метода jsApi.utils.find
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createFindApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.find c особыми атрибутами сотрудника}</li>
     *     <li>Создать на карточке employeeCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта employee и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика поиска объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testFindWithEmployeeAttributes()
    {
        testWithEmployeeAttributes(createFindApplication(employeeFormatTester));
    }

    /**
     * <b>Создать встроенное приложение utilsApplication для тестирования метода jsApi.utils.findFirst</b>
     * <ol>
     *     <li>Создать файл встроенного приложения utilsFileName для тестирования метода jsApi.utils.findFirst,
     *     в качестве остальных значений атрибутов типа использовать
     *     {@link JsApiAttributeFormatTester#getAttributeValues()} сгенерированные значения для атрибутов}
     *     <pre>
     *         %объявление функции logIt для логирования атрибутов в нужном формате%
     *         var subjectUuid = jsApi.extractSubjectUuid()
     *         var metaClass = subjectUuid.split('$')[0]
     *         var title = (!metaClass.includes('employee')) ? 'title' : 'firstName'
     *         var called = false
     *         jsApi.events.addSubjectChangeListener(title, function (result) {
     *             if (called) {
     *                 return
     *             }
     *             jsApi.utils.findFirst(metaClass, {uuid: subjectUuid}).then((result) => {
     *                 for (const attribute of attributes) {
     *                     logIt(attribute, result[attribute])
     *                 }
     *             })
     *             called = true
     *         })
     *     </pre></li>
     *     <li>Добавляем в систему встроенное приложение utilsApplication, созданное из файла utilsFileName</li>
     * </ol>
     */
    private EmbeddedApplication createFindApplication(JsApiAttributeFormatTester formatTester)
    {
        String utilsJsContent = String.format(UTILS_CALL_TEMPLATE,
                formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID),
                "findFirst(metaClass, {uuid: subjectUuid})");
        String utilsFileName = DAOEmbeddedApplication.createApplicationWithJs(temp, utilsJsContent)
                .getAbsolutePath();

        EmbeddedApplication utilsApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                utilsFileName);
        DSLEmbeddedApplication.add(utilsApplication);

        return utilsApplication;
    }

    /**
     * Проверка правильности формата передачи и ответа для обычных атрибутов для метода jsApi.utils.edit
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createEditApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.edit с обычными атрибутами}</li>
     *     <li>Создать на карточке userClass контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта userBo и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика редактирования и возвращения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testEditWithCommonAttributes()
    {
        testWithCommonAttributes(createEditApplication(commonFormatTester));
    }

    /**
     * Проверка правильности формата передачи и ответа для особых атрибутов сотрудника для метода jsApi.utils.edit
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>{@link #createEditApplication(JsApiAttributeFormatTester) Создать встроенное приложение utilsApplication
     *     для тестирования метода jsApi.utils.edit c особыми атрибутами сотрудника}</li>
     *     <li>Создать на карточке employeeCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта employee и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика редактирования и возвращения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testEditWithEmployeeAttributes()
    {
        testWithEmployeeAttributes(createEditApplication(employeeFormatTester));
    }

    /**
     * <b>Создать встроенное приложение utilsApplication для тестирования метода jsApi.utils.edit</b>
     * <ol>
     *     <li>Создать файл встроенного приложения utilsFileName для тестирования метода jsApi.utils.edit, в качестве
     *     остальных значений атрибутов типа использовать
     *     {@link JsApiAttributeFormatTester#getAttributeValues()} сгенерированные значения для атрибутов}
     *     <pre>
     *         %объявление функции logIt для логирования атрибутов в нужном формате%
     *         var subjectUuid = jsApi.extractSubjectUuid()
     *         var metaClass = subjectUuid.split('$')[0]
     *         var title = (!metaClass.includes('employee')) ? 'title' : 'firstName'
     *         var called = false
     *         jsApi.events.addSubjectChangeListener(title, function (result) {
     *             if (called) {
     *                 return
     *             }
     *             jsApi.utils.edit(subjectUuid, {title: subjectUuid}).then((result) => {
     *                 for (const attribute of attributes) {
     *                     logIt(attribute, result[attribute])
     *                 }
     *             })
     *             called = true
     *         })
     *     </pre></li>
     *     <li>Добавляем в систему встроенное приложение utilsApplication, созданное из файла utilsFileName</li>
     * </ol>
     */
    private EmbeddedApplication createEditApplication(JsApiAttributeFormatTester formatTester)
    {
        String utilsJsContent = String.format(UTILS_CALL_TEMPLATE,
                formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID),
                "edit(subjectUuid, {[title]: subjectUuid})");
        String utilsFileName = DAOEmbeddedApplication.createApplicationWithJs(temp, utilsJsContent)
                .getAbsolutePath();

        EmbeddedApplication utilsApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                utilsFileName);
        DSLEmbeddedApplication.add(utilsApplication);

        return utilsApplication;
    }

    /**
     * Проверка правильности формата передачи и ответа для обычных атрибутов для метода jsApi.utils.create
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>Создать файл встроенного приложения utilsFileName для тестирования метода jsApi.utils.create, в качестве
     *     остальных значений атрибутов userCase использовать
     *     {@link JsApiAttributeFormatTester#getAttributeValues()} сгенерированные значения для атрибутов}
     *     <pre>
     *         %объявление функции logIt для логирования атрибутов в нужном формате%
     *         var subjectUuid = jsApi.extractSubjectUuid()
     *         var metaClass = subjectUuid.split('$')[0]
     *         var title = (!metaClass.includes('employee')) ? 'title' : 'firstName'
     *         var called = false
     *         jsApi.events.addSubjectChangeListener(title, function (result) {
     *             if (called) {
     *                 return
     *             }
     *             jsApi.utils.create(userCase, {title: '%название объекта%', %остальные атрибуты userCase%})
     *                 .then((result) => {
     *                     for (const attribute of attributes) {
     *                         logIt(attribute, result[attribute])
     *                     }
     *                 })
     *             called = true
     *         })
     *     </pre></li>
     *     <li>Добавляем в систему встроенное приложение utilsApplication, созданное из файла utilsFileName</li>
     *     <li>Создать на карточке userCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта userBo и вызвать окно редактирования атрибутов</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика создания и возвращения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testCreationWithCommonAttributes()
    {
        String fileUuid = DSLFile.addTempFile(DSLFile.IMG_FOR_UPLOAD);

        // Подготовка
        Map<Attribute, Object> attributeValues = JsApiAttributeFormatTester.valuesBuilder()
                .generateValues(commonAttributes)
                .withValue(SysAttribute.title(userClass), ModelUtils.createTitle())
                .withValue(fileAttr, List.of(fileUuid))
                .build();
        JsApiAttributeFormatTester formatTester = new JsApiAttributeFormatTester(commonAttributes, attributeValues);

        Map<String, Object> creationValues = formatTester.getAttributeValues();
        String createJsPart = String.format("create('%s', %s)", userCase.getFqn(), Json.GSON.toJson(creationValues));

        String utilsJsContent = String.format(UTILS_CALL_TEMPLATE,
                formatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID), createJsPart);
        String utilsFileName = DAOEmbeddedApplication.createApplicationWithJs(temp, utilsJsContent)
                .getAbsolutePath();

        EmbeddedApplication utilsApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                utilsFileName);
        DSLEmbeddedApplication.add(utilsApplication);

        ContentForm utilsContent = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), utilsApplication);
        DSLContent.add(utilsContent);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.clickEdit(commonContent);
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());
        GUIForm.applyForm();

        // Проверка
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(utilsContent);
        Cleaner.afterTest(true, formatTester::cleanRelations);

        Map<Attribute, String> expectedValues = formatTester.getExpectedAttributeValues();
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * Проверка правильности формата передачи и ответа для особых атрибутов сотрудника для метода jsApi.utils.create
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>{@link #setUp() Общая часть настройки}</li>
     *     <li>Создать файл встроенного приложения utilsFileName для тестирования метода jsApi.utils.create, в качестве
     *     остальных значений атрибутов employeeCase использовать
     *     {@link JsApiAttributeFormatTester#getAttributeValues()} сгенерированные значения для атрибутов}
     *     <pre>
     *         %объявление функции logIt для логирования атрибутов в нужном формате%
     *         var subjectUuid = jsApi.extractSubjectUuid()
     *         var metaClass = subjectUuid.split('$')[0]
     *         var title = (!metaClass.includes('employee')) ? 'title' : 'firstName'
     *         var called = false
     *         jsApi.events.addSubjectChangeListener(title, function (result) {
     *             if (called) {
     *                 return
     *             }
     *             jsApi.utils.create(employeeCase, {
     *                 lastName: '%название объекта%',
     *                 parent: '%идентификатор отдела%',
     *                 %остальные значения атрибутов employeeCase%
     *             })
     *                 .then((result) => {
     *                     for (const attribute of attributes) {
     *                         logIt(attribute, result[attribute])
     *                     }
     *                 })
     *             called = true
     *         })
     *     </pre></li>
     *     <li>Добавляем в систему встроенное приложение utilsApplication, созданное из файла utilsFileName</li>
     *     <li>Создать на карточке employeeCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта employee и вызвать окно редактирования атрибутов</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика создания и возвращения объекта</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    @Test
    void testCreationWithEmployeeAttributes()
    {
        // Подготовка
        Map<String, Object> attributeValues = employeeFormatTester.getAttributeValues();
        attributeValues.put(SysAttribute.lastName(employeeCase).getCode(), ModelUtils.createTitle());
        attributeValues.put(SysAttribute.parent(employeeCase).getCode(), SharedFixture.ou().getUuid());
        String createJsPart = String.format("create('%s', %s)", employeeCase.getFqn(),
                Json.GSON.toJson(attributeValues));

        String utilsJsContent = String.format(UTILS_CALL_TEMPLATE,
                employeeFormatTester.buildLogFunction(GUIEmbeddedApplication.TEST_DIV_ID), createJsPart);
        String utilsFileName = DAOEmbeddedApplication.createApplicationWithJs(temp, utilsJsContent)
                .getAbsolutePath();

        EmbeddedApplication utilsApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                utilsFileName);
        DSLEmbeddedApplication.add(utilsApplication);

        ContentForm utilsContent = DAOContentCard.createEmbeddedApplication(employeeCase.getFqn(), utilsApplication);
        DSLContent.add(utilsContent);

        // Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIContent.clickEdit(employeeContent);
        GUIForm.fillAttribute(SysAttribute.firstName(employeeCase), ModelUtils.createTitle());
        GUIForm.applyForm();

        Map<Attribute, String> expectedValues = employeeFormatTester.getExpectedAttributeValues();
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(utilsContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * <b>Проверка правильности формата передачи и ответа для обычных атрибутов для методов jsApi.utils.*</b>
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>Создать на карточке userClass контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта userBo и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика формирования актуальных значений</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    private void testWithCommonAttributes(EmbeddedApplication utilsApplication)
    {
        // Подготовка
        ContentForm utilsContent = DAOContentCard.createEmbeddedApplication(userClass.getFqn(), utilsApplication);
        DSLContent.add(utilsContent);

        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIContent.clickEdit(commonContent);
        commonFormatTester.fillAttributes();
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());
        GUIForm.applyForm();

        Map<Attribute, String> expectedValues = commonFormatTester.getExpectedAttributeValues();

        // Проверка
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(utilsContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }

    /**
     * <b>Проверка правильности формата передачи и ответа для особых атрибутов сотрудника для методов jsApi.utils.*</b>
     * <br>
     * <ol>
     *     <b>Подготовка:</b>
     *     <li>Создать на карточке employeeCase контент для встроенного приложения utilsApplication</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки:</b>
     *     <li>Войти под суперпользователем</li>
     *     <li>Перейти на карточку объекта employee и вызвать окно редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#fillAttributes() Заполнить все атрибуты на форме}</li>
     *     <li>Заполняем атрибут Имя, чтобы сработала логика формирования актуальных значений</li>
     *     <li>Сохранить форму редактирования атрибутов</li>
     *     <li>{@link JsApiAttributeFormatTester#getExpectedAttributeValues() Получаем ожидаемые значения для
     *     атрибутов}</li>
     *     <li>Получить параметры объекта actual из встроенного приложения utilsApplication</li>
     *     <li>{@link JsApiAttributeFormatTester#assertAttributeValues(Map, String) Проверяем, что встроенное
     *     приложение вывело ожидаемые данные}</li>
     * </ol>
     */
    private void testWithEmployeeAttributes(EmbeddedApplication utilsApplication)
    {
        // Подготовка
        ContentForm utilsContent = DAOContentCard.createEmbeddedApplication(employeeCase.getFqn(), utilsApplication);
        DSLContent.add(utilsContent);

        // Выполнение действий и проверка
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIContent.clickEdit(employeeContent);
        employeeFormatTester.fillAttributes();
        GUIForm.fillAttribute(SysAttribute.firstName(employeeCase), ModelUtils.createTitle());
        GUIForm.applyForm();

        // Проверка
        Map<Attribute, String> expectedValues = employeeFormatTester.getExpectedAttributeValues();
        String actual = GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(utilsContent);
        JsApiAttributeFormatTester.assertAttributeValues(expectedValues, actual);
    }
}
