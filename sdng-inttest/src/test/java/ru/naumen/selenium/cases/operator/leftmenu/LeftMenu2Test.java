package ru.naumen.selenium.cases.operator.leftmenu;

import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuLinkToContentItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование левого меню в ИО
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
 * <AUTHOR>
 * @since 08.02.2021
 */
public class LeftMenu2Test extends AbstractTestCase
{
    private static final String LEFT_MENU_ROOT_LINK = "//div[contains(@id, 'gwt-debug-menuItem.uuid:root') and "
                                                      + "(@class)]//a";

    /**
     * Тестирование сохранения состояния шторы и разделов в левом меню при повторном входе пользователя в систему
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню chapter:
     * <ul>
     *      <li>вид элемента - Раздел меню</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink:
     * <ul>
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вложен в раздел — chapter</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент Link</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Развернуть раздел chapter в шторе</li>
     * <li>Выйти из системы</li>
     * <li>Войти в систему под тем же сотрудником</li>
     * <li>Элемент chapter присутствует</li>
     * </ol>
     */
    @Test
    public void testSaveLeftMenuRepeatLogin()
    {
        //Подготовка
        LeftMenuItem chapter = DAOLeftMenuItem.createChapter(true, null);
        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(true, chapter, rootClass, contentTab);
        DSLLeftMenuItem.add(chapter, rootLink);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.expandMenuItem(chapter);
        GUILogon.asTester();
        GUINavSettingsOperator.assertMenuItemPresent(chapter);
    }

    /**
     *  Тестирование сохранения состояния левого меню при повторном входе пользователя в систему,
     *  если им был открыт раздел Избранное по плитке из панели быстрого доступа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню favorites:
     * <ul>
     *      <li>вид элемента - Избранное</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - favorites</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент favorites в левом меню</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Кликнуть по плитке tile в панели быстрого доступа левого меню</li>
     * <li>Выйти из системы</li>
     * <li>Войти в систему под тем же сотрудником</li>
     * <li>В шторе отображается заголовок favorites</li>
     * </ol>
     */
    @Test
    public void testSaveLeftMenuRepeatLoginWithOpenFavorite()
    {
        //Подготовка
        LeftMenuItem favorites = DAOLeftMenuItem.createFavorites(true, null);
        DSLLeftMenuItem.add(favorites);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(favorites, "FV", true);
        DSLQuickAccessTile.add(tile);

        //Действия и проверки
        GUILogon.asTester();
        GUINavSettingsOperator.clickTile(tile);
        GUILogon.asTester();
        GUITester.assertPresent(String.format(GUIXpath.Div.TEXT_ANY, favorites.getTitle()), "Элемент отсутствует'");
    }

    /**
     * Тестирование включения родительских элементов левого меню при включении вложенного элемента,
     * а также включения их плиток в панели быстрого доступа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню chapter:
     * <ul>
     *      <li>вид элемента - Раздел меню</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню chapter2:
     * <ul>
     *      <li>вид элемента - Раздел меню</li>
     *      <li>вложен в раздел — chapter</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink:
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вложен в раздел — chapter2</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - chapter</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile2::
     * <ul>
     *      <li>элемент левого меню - chapter2</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile3:
     * <ul>
     *      <li>элемент левого меню - rootLink</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент rootLink</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Развернуть элемент chapter</li>
     * <li>Развернуть элемент chapter2</li>
     * <li> В шторе левого меню отображаются три элемента: chapter, chapter2, rootLink</li>
     * <li>В панели быстрого доступа левого меню расположены 3 плитки: tile, tile2, tile3</li>
     * </ol>
     */
    @Test
    public void testTurnOnParentMenuItem()
    {
        //Подготовка
        LeftMenuItem chapter = DAOLeftMenuItem.createChapter(false, null);
        LeftMenuItem chapter2 = DAOLeftMenuItem.createChapter(false, chapter);
        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(false, chapter2, rootClass, contentTab);
        DSLLeftMenuItem.add(chapter, chapter2, rootLink);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(chapter, "AA", false);
        QuickAccessTile tile2 = DAOQuickAccessTile.createQuickAccessTile(chapter2, "BB", false);
        QuickAccessTile tile3 = DAOQuickAccessTile.createQuickAccessTile(rootLink, "CC", false);
        DSLQuickAccessTile.add(tile, tile2, tile3);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUIMenuItem.clickSwitchOnLeftMenuItem(rootLink);

        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.expandMenuItem(chapter);
        GUINavSettingsOperator.expandMenuItem(chapter2);
        GUINavSettingsOperator.assertMenuItemPresent(rootLink);
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, tile);
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, tile2);
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, tile3);
    }

    /**
     * Тестирование удаления вложенного элемента левого меню и его плитки при удалении родительского элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню chapter:
     * <ul>
     *      <li>вид элемента - Раздел меню</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink:
     * <ul>
     *      <li>вид элемента -  ссылка на карточку</li>
     *      <li>вложен в раздел — chapter</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - rootLink</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент rootLink</li>
     * <li>Удалить элемент chapter</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Элементы в шторе отсутствуют</li>
     * <li>На панели быстрого доступа отсутствуют плитки кроме одной - «Показать все элементы» </li>
     * </ol>
     */
    @Test
    public void testDeleteChildLeftMenuItem()
    {
        //Подготовка
        LeftMenuItem chapter = DAOLeftMenuItem.createChapter(true, null);
        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(true, chapter, rootClass, contentTab);
        DSLLeftMenuItem.add(chapter, rootLink);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(rootLink, "AA", true);
        DSLQuickAccessTile.add(tile);
        DSLLeftMenuItem.delete(chapter);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(chapter);
        GUINavSettingsOperator.assertMenuItemAbsent(rootLink);
    }

    /**
     * Тестирование отображения элементов левого меню в ИО в зависимости от установленных в них профилей прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать профили прав profile и profile2</li>
     * <li>Создать сотрудника employee, обладающего профилем profile и не обладающего профилем profile2</li>
     * <li>Добавить элемент левого меню rootLink:
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>профили - profile</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink2:
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>профили - profile2</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - rootLink</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile2::
     * <ul>
     *      <li>элемент левого меню - rootLink2</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элементы tile и tile2</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>В шторе отображается элемент rootLink</li>
     * <li>В шторе отсутствует элемент rootLink2</li>
     * <li>В панели быстрого доступа отображается плитка tile</li>
     * <li>В панели быстрого доступа отсутствует плитка tile2</li>
     * </ol>
     */
    @Test
    public void testProfileLeftMenu()
    {
        //Подготовка
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);
        MetaClass ouCase = SharedFixture.ouCase();

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        SecurityGroup userGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup, userGroup2);

        Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);
        DSLSecurityGroup.addUsers(userGroup, employee);

        SecurityProfile profile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        SecurityProfile profile2 = DAOSecurityProfile.create(true, userGroup2, SysRole.employee());
        DSLSecurityProfile.add(profile, profile2);

        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab);
        rootLink.setProfiles(profile.getCode());
        LeftMenuItem rootLink2 = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab);
        rootLink2.setProfiles(profile2.getCode());
        DSLLeftMenuItem.add(rootLink, rootLink2);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(rootLink, "AA", true);
        QuickAccessTile tile2 = DAOQuickAccessTile.createQuickAccessTile(rootLink2, "BB", true);
        DSLQuickAccessTile.add(tile, tile2);

        //Действия и проверки
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, tile);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, tile2);
        GUINavSettingsOperator.assertMenuItemPresent(rootLink);
        GUINavSettingsOperator.assertMenuItemAbsent(rootLink2);
    }

    /**
     * Тестирование невозможности перехода по связанной с пользователем ссылке из левого меню под суперпользователем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню ouLink:
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вкладка карточки -  Отдел (ou)</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - ouLink</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент ouLink</li>
     * <b>Действия и поверки</b>
     * <li>Войти под naumen в ИО</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Кликнуть на элемент ouLink в шторе</li>
     * <li>Проверить что переход не произошел, открылось диалоговое окно с ошибкой «Данный переход невозможно
     * осуществить для суперпользователя»</li>
     * <li>Закрыть диалоговое окно кликом по «Ок»</li>
     * <li>Кликнуть на плитку tile в панели быстрого доступа</li>
     * <li>Проверить что переход не произошел, открылось диалоговое окно с ошибкой «Данный переход невозможно
     * осуществить для суперпользователя»</li>
     * </ol>
     */
    @Test
    public void testTransitOuLinkOnSuper()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        ContentTab contentTab1 = DSLContent.getFirstTab(ouCase.getFqn());
        LeftMenuItem ouLink = DAOLeftMenuItem.createReference(true, null, ouCase, contentTab1);
        DSLLeftMenuItem.add(ouLink);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(ouLink, "AA", true);
        DSLQuickAccessTile.add(tile);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(ouLink);
        String msg = "Данный переход невозможно осуществить для суперпользователя";
        GUIError.assertDialogError(msg);
        GUINavSettingsOperator.clickTileLink(tile);
        GUIError.assertDialogError(msg);
    }

    /**
     * Тестирование отсутствия элемента Компания в левом меню для нелицинзированного пользователя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню company:
     * <ul>
     *      <li>вид элемента - Компания</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - company</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент company</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под нелицензированным пользователем</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>В шторе левого меню отсутствует элемент company</li>
     * <li>В панели быстрого доступа левого меню отсутствует плитка tile</li>
     * </ol>
     */
    @Test
    public void testRootLinkOnUnlicensedUser()
    {
        //Подготовка
        LeftMenuItem company = DAOLeftMenuItem.createCompany(false, null);
        DSLLeftMenuItem.add(company);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(company, "AA", true);
        DSLQuickAccessTile.add(tile);

        //Действия и проверки
        GUILogon.asUnlicensed();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(company);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, tile);
    }

    /**
     * Тестирование изменения порядка элементов в левом меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню rootLink:
     * <ul>
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink2:
     * <ul>
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вкладка карточки - Компания / Оргструктура</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент rootLink2</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под naumen в «Настройка системы» - «Интерфейс и навигация» - вкладка «Навигация»</li>
     * <li>В блоке «Левое меню» в строке элемента rootLink2 кликнуть на стрелку перемещения элемента вверх</li>
     * <li>Перейти в ИО</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Элементы в шторе меню расположены в порядке: rootLink2, rootLink.</li>
     * </ol>
     */
    @Test
    public void testChangeOfOrderLeftMenu()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab);
        ContentTab contentTab2 = DSLContent.getTab(rootClass.getFqn(), 1);
        LeftMenuItem rootLink2 = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab2);
        DSLLeftMenuItem.add(rootLink, rootLink2);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUINavigational.showNavPanelOperator();
        GUITester.assertFindElements(LEFT_MENU_ROOT_LINK, Lists.newArrayList(rootLink.getTitle(), rootLink2.getTitle()),
                true, true);

        GUINavSettings.goToCard();
        GUINavSettings.clickMoveLeftMenuItem(rootLink2, true);
        GUINavigational.goToOperatorUI();
        GUINavigational.showNavPanelOperator();
        GUITester.assertFindElements(LEFT_MENU_ROOT_LINK, Lists.newArrayList(rootLink2.getTitle(), rootLink.getTitle()),
                true, true);
    }

    /**
     * Тестирование изменения порядка плиток в панели быстрого доступа левого меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00204
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$*********
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню rootLink:
     * <ul>
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить элемент левого меню rootLink2:
     * <ul>
     *      <li>вид элемента - ссылка на карточку</li>
     *      <li>вкладка карточки - Компания (root)</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile::
     * <ul>
     *      <li>элемент левого меню - rootLink</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа tile2::
     * <ul>
     *      <li>элемент левого меню - rootLink2</li>
     *      <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент rootLink b rootLink2</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под naumen в «Настройка системы» - «Интерфейс и навигация» - вкладка «Навигация»</li>
     * <li>В блоке «Панель быстрого доступа» в строке плитки rootLink2 кликнуть на стрелку перемещения элемента
     * вверх</li>
     * <li>Перейти в ИО</li>
     * <li>Плитки на панели быстрого доступа меню расположены в порядке: бургер(без текста), tile2, tile</li>
     * </ol>
     */
    @Test
    public void testChangeOfOrderQuickAccessMenu()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab = DSLContent.getFirstTab(rootClass.getFqn());
        LeftMenuItem rootLink = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab);
        rootLink.setAbbreviation("aaa");
        ContentTab contentTab2 = DSLContent.getTab(rootClass.getFqn(), 1);
        LeftMenuItem rootLink2 = DAOLeftMenuItem.createReference(true, null, rootClass, contentTab2);
        rootLink2.setAbbreviation("bbb");
        DSLLeftMenuItem.add(rootLink, rootLink2);

        QuickAccessTile tile = DAOQuickAccessTile.createQuickAccessTile(rootLink, "AA", true);
        QuickAccessTile tile2 = DAOQuickAccessTile.createQuickAccessTile(rootLink2, "BB", true);
        DSLQuickAccessTile.add(tile, tile2);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavSettings.goToCard();
        GUIMenuItem.clickMoveLeftMenuItemQuickTile(rootLink2, true);

        GUINavigational.goToOperatorUI();
        GUITester.assertFindElements(GUINavSettingsOperator.QUICK_ACCESS_MENU_TILES, Lists.newArrayList("",
                tile2.getAbbreviation(), tile.getAbbreviation()), true, true);
    }

    /**
     * Тестирование отображения в левом меню ссылки на отдел определенного типа, в зависимости от того входит ли
     * текущий сотрудник в отдел этого типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$111186599
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе Отдел создать 2 типа ouCase1 и ouCase2</li>
     * <li>Создать 2 отдела ou1 типа ouCase1 и ou2 типа ouCase2</li>
     * <li>В отделе ou1 создать лицензированного сотрудника employee со всеми правами</li>
     * <li>Создать в левом меню элемент ouLink1:
     * <ul>
     *     <li>название — Ou1</li>
     *     <li>вид элемента — ссылка на карточку</li>
     *     <li>вкладка карточки - ouCase1 (ouCase1)</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку ouTile1 на панель быстрого доступа:
     * <ul>
     *     <li>элемент левого меню — ouLink1</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент ouLink1</li>
     * <li>В настройках Навигации добавить элемент левого меню ouLink2:
     * <ul>
     *     <li>название — Ou2</li>
     *     <li>вид элемента — ссылка на карточку</li>
     *     <li>вкладка карточки - ouCase2 (ouCase2)</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку ouTile2 на панель быстрого доступа:
     * <ul>
     *     <li>элемент левого меню — ouLink2</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент ouLink2</li>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <b>Проверки</b>
     * <li>В шторе левого меню есть ссылка ouLink1</li>
     * <li>В шторе левого меню отсутствует ссылка ouLink2</li>
     * <li>В панели быстрого доступа левого меню есть плитка ouTile1</li>
     * <li>В панели быстрого доступа левого меню отсутствует плитка ouTile2</li>
     * </ol>
     */
    @Test
    public void testVisibilityOfLeftMenuItem()
    {
        // Подготовка
        MetaClass ouCase1 = DAOOuCase.create();
        MetaClass ouCase2 = DAOOuCase.create();
        DSLMetainfo.add(ouCase1, ouCase2);

        Bo ou1 = DAOOu.create(ouCase1);
        Bo ou2 = DAOOu.create(ouCase2);
        DSLBo.add(ou1, ou2);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou1, true, true);
        DSLBo.add(employee);

        LeftMenuItem ouLink1 = DAOLeftMenuItem.createReference(true, null, ouCase1);
        ouLink1.setTitle("Ou1");
        LeftMenuItem ouLink2 = DAOLeftMenuItem.createReference(true, null, ouCase2);
        ouLink2.setTitle("Ou2");
        DSLLeftMenuItem.add(ouLink1, ouLink2);

        QuickAccessTile ouTile1 = DAOQuickAccessTile.createQuickAccessTile(ouLink1, "OL1", true);
        QuickAccessTile ouTile2 = DAOQuickAccessTile.createQuickAccessTile(ouLink2, "OL2", true);
        DSLQuickAccessTile.add(ouTile1, ouTile2);

        // Действия
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();

        // Проверки
        GUINavSettingsOperator.assertMenuItemPresent(ouLink1);
        GUINavSettingsOperator.assertMenuItemAbsent(ouLink2);
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, ouTile1);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, ouTile2);
    }

    /**
     * Тестирование отсутствия в левом меню ссылки на пустую вкладку карточки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$111186599
     * <ol>
     * <b>Подготовка</b>
     * <li>На карточку компании добавить вкладку tab</li>
     * <li>Создать в левом меню элемент tabLink
     * <ul>
     *     <li>название — tab</li>
     *     <li>вид элемента — ссылка на карточку</li>
     *     <li>вкладка карточки — Компания / tab</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку tabTile на панель быстрого доступа:
     * <ul>
     *     <li>элемент левого меню — tabLink</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент tabLink</li>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником </li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <b>Проверки</b>
     * <li>В шторе левого меню отсутствует ссылка tabLink</li>
     * <li>В панели быстрого доступа левого меню отсутствует плитка tabTile</li>
     * </ol>
     */
    @Test
    public void testAbsenceLinkToEmptyTab()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();
        ContentTab tab = DAOContentTab.createTab(rootClass, "tab");
        DSLContent.addTab(tab);

        LeftMenuItem tabLink = DAOLeftMenuItem.createReference(true, null, rootClass, tab);
        DSLLeftMenuItem.add(tabLink);

        QuickAccessTile tabTile = DAOQuickAccessTile.createQuickAccessTile(tabLink, "TT", true);
        DSLQuickAccessTile.add(tabTile);

        // Действия
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();

        // Проверки
        GUINavSettingsOperator.assertMenuItemAbsent(tabLink);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, tabTile);
    }

    /**
     * Тестирование отсутствия в левом меню ссылки на вкладку карточки, если у пользователя нет прав на просмотр этой
     * вкладки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$111186599
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать профили прав profile1 и profile2</li>
     * <li>Создать сотрудника employee, обладающего профилем прав profile1 и НЕ обладающего профилем profile2</li>
     * <li>На карточку компании добавить вкладку tab с настройкой:
     * <ul>
     *     <li>профили — profile2</li>
     * </ul></li>
     * <li>Добавить на вкладку tab контент «Параметры объекта» по группе атрибутов «Системные атрибуты»</li>
     * <li>Создать в левом меню элемент
     * <ul>
     *     <li>название — tab</li>
     *     <li>вид элемента — ссылка на карточку</li>
     *     <li>вкладка карточки — Компания / tab</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку на панель быстрого доступа:
     * <ul>
     *     <li>элемент левого меню — tab</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент tab</li>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <b>Проверки</b>
     * <li>В шторе левого меню отсутствует ссылка tab</li>
     * <li>В панели быстрого доступа левого меню отсутствует плитка tab</li>
     * </ol>
     */
    @Test
    public void testAbsenceLinkIfNoRights()
    {
        // Подготовка
        SecurityGroup userGroup1 = DAOSecurityGroup.create();
        SecurityGroup userGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup1, userGroup2);

        SecurityProfile profile1 = DAOSecurityProfile.create(true, userGroup1, SysRole.employee());
        SecurityProfile profile2 = DAOSecurityProfile.create(true, userGroup2, SysRole.employee());
        DSLSecurityProfile.add(profile1, profile2);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true, true);
        DSLBo.add(employee);
        DSLSecurityGroup.addUsers(userGroup1, employee);

        MetaClass rootClass = DAORootClass.create();
        ContentTab tab = DAOContentTab.createTab(rootClass, "tab");
        tab.setProfiles(profile2);
        DSLContent.addTab(tab);

        ContentForm propertyList = DAOContentCard.createPropertyList(rootClass);
        DSLContent.add(tab, propertyList);

        LeftMenuItem tabLink = DAOLeftMenuItem.createReference(true, null, rootClass, tab);
        DSLLeftMenuItem.add(tabLink);

        QuickAccessTile tabTile = DAOQuickAccessTile.createQuickAccessTile(tabLink, "TT", true);
        DSLQuickAccessTile.add(tabTile);

        // Действия
        GUILogon.login(employee);
        GUINavigational.showNavPanelOperator();

        // Проверки
        GUINavSettingsOperator.assertMenuItemAbsent(tabLink);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, tabTile);
    }

    /**
     * Тестирование отображения в левом меню ссылки на вкладку, для которой настроено условие отображения, в
     * зависимости от выполнения этого условия
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$111186599
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou и в нем лицензированного сотрудника employee со всеми правами</li>
     * <li>На карточку класса Отдел добавить вкладку conditionTab с условием отображения:
     * <ul>
     *     <li>Руководитель отдела — содержит — employee</li>
     * </ul></li>
     * <li>Добавить на вкладку conditionTab контент «Параметры объекта» по группе атрибутов «Системные атрибуты»</li>
     * <li>Создать в левом меню элемент conditionLink
     * <ul>
     *     <li>название — Condition</li>
     *     <li>вид элемента — ссылка на карточку</li>
     *     <li>вкладка карточки — Отдел / conditionTab</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Добавить плитку conditionTile на панель быстрого доступа:
     * <ul>
     *     <li>элемент левого меню — conditionLink</li>
     *     <li>остальные параметры — по умолчанию</li>
     * </ul></li>
     * <li>Включить элемент conditionLink</li>
     * <b>Действия и поверки</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти в отдел ou</li>
     * <li>Развернуть штору левого меню кликом по плитке «Показать все меню»</li>
     * <li>Проверить, что в шторе левого меню отсутствует ссылка conditionLink</li>
     * <li>Проверить, что в панели быстрого доступа левого меню отсутствует плитка conditionTab</li>
     * <li>Открыть модальную форму редактирования параметров в контенте Параметры объекта и изменить значение
     * атрибута «Руководитель отдела» на «employee», сохранить.</li>
     * <li>Проверить, что в шторе левого меню отобразилась ссылка conditionLink</li>
     * <li>Проверить, что в панели быстрого доступа левого меню отобразилась плитка conditionTab</li>
     * </ol>
     */
    @Test
    public void testVisibilityOfLeftMenuItemWithFilterOnTab()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetainfo.add(ouCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);

        FilterBlockOr orTab = new FilterBlockOr(SysAttribute.head(ouCase), FilterCondition.CONTAINS, false,
                employee.getUuid());
        FilterBlockAnd andTab = new FilterBlockAnd(orTab);
        ListFilter filterTab = new ListFilter(andTab);

        ContentTab conditionTab = DAOContentTab.createTab(ouCase, "Condition");
        conditionTab.setVisibilityCondition(filterTab);
        DSLContent.addTab(conditionTab);

        ContentForm propertyListOnConditionTab = DAOContentCard.createPropertyList(ouCase);
        DSLContent.add(conditionTab, propertyListOnConditionTab);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase);
        DSLContent.add(propertyList);

        LeftMenuItem conditionLink = DAOLeftMenuItem.createReference(true, null, ouCase, conditionTab);
        DSLLeftMenuItem.add(conditionLink);

        QuickAccessTile conditionTile = DAOQuickAccessTile.createQuickAccessTile(conditionLink, "CN", true);
        DSLQuickAccessTile.add(conditionTile);

        // Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(ou);
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(conditionLink);
        GUINavSettingsOperator.assertExistsQuickAccessTile(false, conditionTile);

        GUIContent.clickEdit(propertyList);
        BoTree tree = new BoTree("gwt-debug-head-value", false);
        tree.setElementInSelectTree(ou, employee);
        // очищаем кэш левого меню перед изменением объекта, чтобы
        // отрисовались все элементы (см. NSDPRD-28207)
        DSLNavSettings.clearLeftMenuCache();
        GUIForm.applyForm();

        GUINavSettingsOperator.assertMenuItemPresent(conditionLink);
        GUINavSettingsOperator.assertExistsQuickAccessTile(true, conditionTile);
    }

    /**
     * Тестирование перехода из левого меню по ссылке на контент типа Иерархический список (без шаблонов)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00125
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211391015
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить элемент левого меню leftMenuItem:
     * <ul>
     *      <li>вид элемента - ссылка на контент</li>
     *      <li>контент - любое иерархическое дерево без шаблонов content</li>
     *      <li>остальные параметры - по умолчанию</li>
     * </ul>
     * <li>Включить элемент leftMenuItem</li>
     * <b>Действия и поверки</b>
     * <li>Войти в ИО под любым сотрудником, в левом меню кликнуть по leftMenuItem</li>
     * <li>Убедиться, что переход карточку с content произошел без ошибок</li>
     * </ol>
     */
    @Test
    public void testLinkToHierarchyWithoutTemplate()
    {
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        StructuredObjectsView content = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem ouViewItem = DAOStructuredObjectsView.createItem(null,
                ouClass, SysAttribute.parent(ouClass),
                DAOGroupAttr.createSystem(ouClass.getFqn()), true);
        content.setItems(ouViewItem);
        DSLStructuredObjectsView.add(content);

        LeftMenuLinkToContentItem leftMenuItem = DAOLeftMenuItem.createLinkToHierarchyGrid(content.getCode(), true,
                null);
        DSLLeftMenuItem.add(leftMenuItem);

        // Действия
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.clickRefMenuItem(leftMenuItem);

        // Проверки
        GUITester.assertPresent(Div.HEADER_TITLE_TEXT, "Не перешли на карточку иерархического списка.",
                leftMenuItem.getListPageTitle());
    }
}