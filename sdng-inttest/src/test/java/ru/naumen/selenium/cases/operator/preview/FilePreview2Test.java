package ru.naumen.selenium.cases.operator.preview;

import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFilePreview;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichTextGwt;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.file.DAOSdFile;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.mail.DAOEmail;
import ru.naumen.selenium.casesutil.model.mail.Email;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection;
import ru.naumen.selenium.casesutil.model.schedulertask.InboundMailConnection.MailProtocol;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTask;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.ActionToActiveElement;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.UrlUtils;

/**
 * Тесты на интерфейс предварительного просмотра файлов
 *
 * <AUTHOR>
 * @since 16 сент. 2015 г.
 */
public class FilePreview2Test extends AbstractTestCase
{
    /**
     * Тестирования принудительного сжатия изображения в значении атрибута типа "Текст в формате RTF"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Вывести атрибут attribute на карточку типа отдела ouCase</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела ou</li>
     * <li>В качестве значения атрибута attribute устанавливаем html содержащий изображение размера
     *     1920x1200 без атрибутов width и height</li>
     * <li>Проверяем что вставленное изображение на карточке отдела имеет размер 700x438</li>
     * <li>Выполняем скрипт:
     * <pre>
     * -------------------------------------------------------
     * def ou = utils.load('%uuid отдела ou%')
     * api.preview.shrinkImages(ou, '%код атрибута attribute%')
     * -------------------------------------------------------
     * </pre>
     * </li>
     * <li>Обновляем страницу</li>
     * <li>Проверяем что вставленное изображение на карточке отдела имеет размер 700x438</li>
     * <li>Кликаем по изображению</li>
     * <li>Проверяем что открылся интерфейс предварительного просмотра с изображением</li>
     * <li>Кликаем по кнопке "Увеличить масштаб"</li>
     * <li>Проверяем что изображаение приняло размер 1920x1200</li>
     * </ol>
     */
    @Test
    public void testCreatePreviewsInExistingValue()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attribute = DAOAttribute.createTextRTF(ouCase.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attribute);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(propertyList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        String imgPattern = "<img src=\"data:image/jpg;base64,%s\"/>";
        String html = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_PICTURE4));

        GUIPropertyList.clickEditLink(propertyList);
        GUIRichText.clickHTML(attribute.getCode());
        ActionToActiveElement.copyToClipboard(html);
        GUIRichText.paste(attribute);
        GUIForm.applyForm();

        GUIRichText.assertImageSize(propertyList, attribute, 700, 438);

        //@formatter:off
        String scriptTemplate = "def ou = utils.load('%s') \n"
                              + "api.preview.shrinkImages(ou, '%s')";
        //@formatter:on

        new ScriptRunner(String.format(scriptTemplate, ou.getUuid(), attribute.getCode())).runScript();

        tester.refresh();

        GUIRichText.assertImageSize(propertyList, attribute, 700, 438);

        GUIRichText.clickImage(propertyList, attribute);

        GUIFilePreview.assertImageCanvasPresent();

        GUIFilePreview.clickMaximizeButton();
        GUIFilePreview.assertImageSize(1920, 1200);
    }

    /**
     * Тестирования принудительного сжатия изображения в теле письма
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Установить максимальный размер изображения в тексте RTF - 300px</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Вывести атрибут attribute на карточку типа отдела ouCase</li>
     * <li>Создать задачу планировщика task типа "Обработка входящей почты" с правилом обработки:
     * <pre>
     * -------------------------------------------------------
     * api.preview.shrinkImages(message)
     * utils.create('%s', ['title' : 'testOu', '%код атрибута attribute%' : message.htmlBody])
     * --------
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Обрабатываем письмо, содержащее изображение размера 340x250</li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела, созданного в результате обработки письма</li>
     * <li>Проверяем что изображение в атрибуте attribute на карточке отдела имеет размер 300x221</li>
     * <li>Кликаем по изображению в атрибуте attribute</li>
     * <li>Проверяем что открылся интерфейс предварительного просмотра с изображением</li>
     * <li>Проверяем что изображаение в режиме предварительного просмотра имеет размер 340x250</li>
     * </ol>
     */
    @Test
    public void testCreatePreviewsProcessingMail()
    {
        // Подготовка
        DSLConfiguration.setMaxRtfImageSize(300);

        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attribute = DAOAttribute.createTextRTF(ouCase.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attribute);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(propertyList);

        InboundMailConnection connection = DAOSchedulerTask.createInboundMailConnection(MailProtocol.POP3, false);
        DSLSchedulerTask.addInboundMailConnection(connection);

        //@formatter:off
        String scriptTemplate = "api.preview.shrinkImages(message) \n"
                              + "utils.create('%s', ['title' : 'testOu', '%s' : message.htmlBody])";
        //@formatter:on
        ScriptInfo script = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, ouCase.getFqn(), attribute.getCode()));

        SchedulerTask task = DAOSchedulerTask.createReceiveMailTask(false, connection, script);
        DSLSchedulerTask.addReceiveMailTask(task, script);
        DSLSchedulerTask.addTask(task);

        Email email = DAOEmail.createEmailForSend();
        String imgPattern = "<img src=\"data:image/jpg;base64,%s\"/>";
        String body = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD));
        email.setBody(body);

        Set<String> oldUuids = DSLBo.getUuidsByFqn(ouCase.getFqn());

        // Выполнение действий и проверки
        DSLSchedulerTask.processInboundMail(task, email);

        String uuid = DSLBo.getCreatedObjectUuid(ouCase.getFqn(), oldUuids);
        Bo ou = DAOBo.createModelByUuid(uuid);

        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIRichText.assertImageSize(propertyList, attribute, 300, 221);

        GUIRichText.clickImage(propertyList, attribute);

        GUIFilePreview.assertImageSize(340, 250);
    }

    /**
     * Тестирования принудительного сжатия изображения в теле письма,
     * если для картинки задан стиль с размером
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Установить максимальный размер изображения в тексте RTF - 300px</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе отдела ouCase создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Вывести атрибут attribute на карточку типа отдела ouCase</li>
     * <li>Создать задачу планировщика task типа "Обработка входящей почты" с правилом обработки:
     * <pre>
     * -------------------------------------------------------
     * api.preview.shrinkImages(message)
     * utils.create('%s', ['title' : 'testOu', '%код атрибута attribute%' : message.htmlBody])
     * --------
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Обрабатываем письмо, содержащее изображение размера 340x250 с заданным 
     * в стиле размером style="width:  340.5px;height:250.5px"</li>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку отдела, созданного в результате обработки письма</li>
     * <li>Проверяем что изображение в атрибуте attribute на карточке отдела имеет размер 300x221</li>
     * <li>Кликаем по изображению в атрибуте attribute</li>
     * <li>Проверяем что открылся интерфейс предварительного просмотра с изображением</li>
     * <li>Проверяем что изображение в режиме предварительного просмотра имеет размер 340x250</li>
     * </ol>
     */
    @Test
    public void testCreatePreviewsProcessingMailIfHaveStyleSize()
    {
        // Подготовка
        DSLConfiguration.setMaxRtfImageSize(300);

        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute attribute = DAOAttribute.createTextRTF(ouCase.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attribute);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(propertyList);

        InboundMailConnection connection = DAOSchedulerTask.createInboundMailConnection(MailProtocol.POP3, false);
        DSLSchedulerTask.addInboundMailConnection(connection);

        //@formatter:off
        String scriptTemplate = "api.preview.shrinkImages(message) \n"
                              + "utils.create('%s', ['title' : 'testOu', '%s' : message.htmlBody])";
        //@formatter:on
        ScriptInfo script = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, ouCase.getFqn(), attribute.getCode()));

        SchedulerTask task = DAOSchedulerTask.createReceiveMailTask(false, connection, script);
        DSLSchedulerTask.addReceiveMailTask(task, script);
        DSLSchedulerTask.addTask(task);

        Email email = DAOEmail.createEmailForSend();
        String imgPattern = "<img src=\"data:image/jpg;base64,%s\" style=\"width:  340.5px;height:250.5px\"/>";
        String body = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_IMG_FOR_UPLOAD));
        email.setBody(body);

        Set<String> oldUuids = DSLBo.getUuidsByFqn(ouCase.getFqn());

        // Выполнение действий и проверки
        DSLSchedulerTask.processInboundMail(task, email);

        String uuid = DSLBo.getCreatedObjectUuid(ouCase.getFqn(), oldUuids);
        Bo ou = DAOBo.createModelByUuid(uuid);

        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIRichText.assertImageSize(propertyList, attribute, 300, 221);

        GUIRichText.clickImage(propertyList, attribute);

        GUIFilePreview.assertImageSize(340, 250);
    }

    /**
     * Тестирование предварительного просмотра .doc файлов на форме добавления объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * http://sd-jira.naumen.ru/browse/NSDPRD-4806
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На форму добавления отдела типа ouCase вывести список файлов fileList</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на форму добавления отдела типа ouCase/li>
     * <li>На форме добавления в список файлов fileList добавляем .doc файл file</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testDocFilePreviewOnAddForm()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        ContentForm fileList = DAOContentAddForm.createFileList(ouCase.getFqn());
        DSLContent.add(fileList);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToAddForm(DAOOuCase.createClass().getFqn(), SharedFixture.root().getUuid(), ouCase.getFqn());

        SdFile file = DAOSdFile.create(DSLFile.DOC_FILE);
        GUIFileList.addFileFromAddForm(fileList, file);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//span", "This is a doc file.");
    }

    /**
     * Тестирование ссылки для копирования на кнопке "Скачать файл"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * http://sd-jira.naumen.ru/browse/NSDPRD-4989
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к userBo файлы file1(картинка больше чем 128x64) и file2(файл, не отображающийся в окне просмотра)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке fileList кликаем по иконке файла file1</li>
     * <li>Проверяем, что в окне просмотра на кнопке "Скачать файл" есть ссылка для копирования на скачивание file1</li>
     * <li>Нажимаем кнопку "Следующий файл"</li>
     * <li>Проверяем, что в окне просмотра на кнопке "Скачать файл" есть ссылка для копирования на скачивание file2</li> 
     * </ol>
     */
    @Test
    public void testDownloadHref()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file1 = DSLFile.add(userBo, DSLFile.BIG_IMG_FOR_UPLOAD);
        SdFile file2 = DSLFile.add(userBo, DSLFile.FAKE_IMAGE_FILE);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        String anchor = UrlUtils.reencodeUriComponent(tester.getWebDriver().getCurrentUrl().split("#")[1]);
        String urlPostfix = "&checkContent=true&back=" + anchor;
        GUIFileOperator.clickOnIcon(fileList, file1);
        GUIFilePreview.assertDownloadHref(file1, urlPostfix);

        GUIFilePreview.clickNextButton();
        GUIFilePreview.assertDownloadHref(file2, urlPostfix);
    }

    /**
     * Тестирование предварительного просмотра .ppt файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>Прикрепляем к userBo файл file в формате .ppt</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testPptFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        SdFile file = DAOSdFile.create(DSLFile.PPT_FILE);
        GUIFileList.addFileFromCard(fileList, file);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertCountAllElements(GUIFilePreview.PREVIEW_CANVAS_IMG_XPATH, 2);
    }

    /**
     * Тестирование предварительного просмотра .pptx файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>Прикрепляем к userBo файл file в формате .pptx</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testPptxFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        SdFile file = DAOSdFile.create(DSLFile.PPTX_FILE);
        GUIFileList.addFileFromCard(fileList, file);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertCountAllElements(GUIFilePreview.PREVIEW_CANVAS_IMG_XPATH, 2);
    }

    /**
     * Тестирование появления ошибки в случае попытки предварительного просмотра документа
     * который был удален
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Список"</li>
     * <li>Прикрепить к userBo файлы docFile и docxFile форматов .doc и .docx соответственно</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке fileList кликаем по строке файла docFile</li>
     * <li>В соседней вкладке удаляем файл docxFile</li>
     * <li>Нажимаем кнопку "Следующий файл"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в окне предварительного просмотра появилось сообщение: "Данный файл недоступен для предварительного просмотра."</li>
     * </ol>
     */
    @Test
    public void testPreviewOfDeletedFile()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        SdFile docFile = DSLFile.add(userBo, DSLFile.DOC_FILE);
        docFile.setExists(true);
        SdFile docxFile = DSLFile.add(userBo, DSLFile.DOCX_FILE);
        docxFile.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnFileInList(fileList, docFile);

        DSLFile.deleteFile(docxFile.getUuid());

        GUIFilePreview.clickNextButton();

        //Проверка
        GUIFilePreview.assertErrorMessage();
    }

    /**
     * Тестирование появления ошибки в случае попытки предварительного просмотра pdf-файла
     * который был удален
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$159613247
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Список"</li>
     * <li>Прикрепить к userBo два файла: imageFile1 и pdfFile2</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке fileList кликаем по строке файла imageFile1</li>
     * <li>В соседней вкладке удаляем файл pdfFile2</li>
     * <li>Нажимаем кнопку "Следующий файл"</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что в окне предварительного просмотра появилось сообщение: "Данный файл недоступен для предварительного просмотра."</li>
     * </ol>
     */
    @Test
    public void testPreviewOfDeletedPdf()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile imageFile1 = DSLFile.add(userBo, DSLFile.BIG_IMG_FOR_UPLOAD);
        imageFile1.setExists(true);
        SdFile pdfFile2 = DSLFile.add(userBo, DSLFile.FILE_FOR_SEARCH_PDF);
        pdfFile2.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnFileInList(fileList, imageFile1);

        DSLFile.deleteFile(pdfFile2.getUuid());

        GUIFilePreview.clickNextButton();

        //Проверка
        GUIFilePreview.assertErrorMessage();
    }

    /**
     * Тестирования сбрасывания режима просмотра изображения в натуральную величину при переходе между изображениями
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В классе userClass создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку userClass вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем два изображение размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по первой картинке отображаемой в значении атрибута attribute</li>
     * <li>В открывшемся окне предварительного просмотра включаем режим просмотра в натуральную величину</li>
     * <li>Кликаем по кнопке "Следующий файл"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем что появилась кнопка "Увеличить масштаб"</li>
     * <li>Проверяем что следующее изображение вписано в рабочую область</li>
     * </ol>
     */
    @Test
    public void testResetFullSizeOnSwitchImages()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute attribute = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(userClass, group);
        DSLContent.add(content);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        String imgPattern = "<img src=\"data:image/jpg;base64,%s\" width=\"700\" height=\"438\"/>";
        String firstImg = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_PICTURE4));
        String secondImg = String.format(imgPattern, DSLFile.getFileContentAsBase64(DSLFile.BIG_PICTURE5));

        GUIPropertyList.clickEditLink(content);
        GUIRichText.clickHTML(attribute.getCode());
        ActionToActiveElement.copyToClipboard(firstImg + secondImg);
        GUIRichText.paste(attribute);
        GUIForm.applyForm();

        GUIRichText.clickImage(content, attribute);
        GUIFilePreview.clickMaximizeButton();

        GUIFilePreview.clickNextButton();

        //Проверки
        GUIFilePreview.assertMaximizeButtonPresent();
        GUIFilePreview.assertImageFitsIn(1920, 1200);
    }

    /**
     * Тестирования предварительного просмотра изображений в атрибуте типа "Текст в формате RTF",
     * содержащем несколько изображений указывающих на один и тот же файл
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В классе userClass создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку userClass вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <li>Прикрепляем к userBo файл file, содержащий изображение</li>
     * <li>Добавляем в значение атрибута attribute userClasss два тэга <img> со ссылкой на файл file</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>Кликаем по первой картинке в атрибуте attribute</li>
     * <li>Проверяем что в счетчике файлов в открывшемся окне предварительного просмотра указан только один файл</li>
     * <li>Проверяем что кнопки "Следующий файл" и "Предыдущий файл" отсутствуют</li>
     * <li>Закрываем окно предварительного просмотра</li>
     * <li>Кликаем по второй картинке в атрибуте attribute</li>
     * <li>Проверяем что в счетчике файлов в открывшемся окне предварительного просмотра указан только один файл</li>
     * <li>Проверяем что кнопки "Следующий файл" и "Предыдущий файл" отсутствуют</li>
     * </ol>
     */
    @Test
    public void testSameImageFileInRichText()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute attribute = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(userClass, group);
        DSLContent.add(content);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        SdFile file = DSLFile.add(userBo, DSLFile.BIG_IMG_FOR_UPLOAD);
        file.setExists(true);

        String img = String.format("<img src=\"./download?uuid=%s\"/>", file.getUuid());
        attribute.setValue(img + img);
        DSLBo.editAttributeValue(userBo, attribute);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIRichText.clickImage(content, attribute);

        GUIFilePreview.assertCounter(1, 1);
        GUIFilePreview.assertPrevButtonAbsence();
        GUIFilePreview.assertNextButtonAbsence();

        GUIFilePreview.closePreview();

        GUIRichText.clickImage(content, attribute, 2);

        GUIFilePreview.assertCounter(1, 1);
        GUIFilePreview.assertPrevButtonAbsence();
        GUIFilePreview.assertNextButtonAbsence();
    }

    /**
     * Тестирование кнопок "Предыдущий файл" и "Следующий файл"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В классе userClass создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку userClass вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем два изображения размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по первой картинке отображаемой в значении атрибута attribute</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Кликаем по кнопке "Следующий файл"</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 2 из 2"</li>
     * <li>Проверяем что отображаемое название текущего файла сменилось</li>
     * <li>Кликаем по кнопке "Предыдущий файл"</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Проверяем что отображается название первого файла</li>
     * <li>Кликаем по кнопке "Предыдущий файл"</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 2 из 2"</li>
     * <li>Проверяем что отображается название второго файла</li>
     * <li>Кликаем по кнопке "Следующий файл"</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Проверяем что отображается название первого файла</li>
     * </ol>
     */
    @Test
    public void testSwitchImagesButtons()
    {
        //Подготовка
        DSLAdmin.setUseFroalaRtfEditor(false);

        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);
        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE);
        GUIRichTextGwt.pasteAndWait(attribute);
        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE2);
        GUIRichTextGwt.pasteAndWait(attribute);
        GUIForm.applyForm();

        GUIRichTextGwt.clickImage(content, attribute);

        GUIFilePreview.assertCounter(1, 2);
        String firstFileTitle = GUIFilePreview.getFileTitle();

        GUIFilePreview.clickNextButton();

        GUIFilePreview.assertCounter(2, 2);
        String secondFileTitle = GUIFilePreview.getFileTitle();

        Assert.assertNotEquals("Отображаемое название не сменилось", firstFileTitle, secondFileTitle);

        GUIFilePreview.clickPrevButton();
        GUIFilePreview.assertCounter(1, 2);
        GUIFilePreview.assertFileTitle(firstFileTitle);

        GUIFilePreview.clickPrevButton();
        GUIFilePreview.assertCounter(2, 2);
        GUIFilePreview.assertFileTitle(secondFileTitle);

        GUIFilePreview.clickNextButton();
        GUIFilePreview.assertCounter(1, 2);
        GUIFilePreview.assertFileTitle(firstFileTitle);
    }

    /**
     * Тестирование смены текущего изображаения нажатием клавиш "Стрелка вправо" 
     * и "Стрелка влево" на клавиатуре
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00647
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>В классе userClass создать атрибут attribute типа "Текст в формате RTF"</li>
     * <li>Создать группу атрибутов group с единственным атрибутом attribute</li>
     * <li>На карточку userClass вывести контент content типа "Параметры объекта" (группа атрибутов: group)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В контенте content нажимаем ссылку "Редактировать"</li>
     * <li>В поле для редактирования текста RTF вставляем два изображения размера 1920x1200</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Кликаем по первой картинке отображаемой в значении атрибута attribute</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Нажимаем кноку "Стрелка вправо" на клавиатуре</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 2 из 2"</li>
     * <li>Проверяем что отображаемое название текущего файла сменилось</li>
     * <li>Нажимаем кноку "Стрелка влево" на клавиатуре</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Проверяем что отображается название первого файла</li>
     * <li>Нажимаем кноку "Стрелка влево" на клавиатуре</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 2 из 2"</li>
     * <li>Проверяем что отображается название второго файла</li>
     * <li>Нажимаем кноку "Стрелка вправо" на клавиатуре</li>
     * <li>Проверяем что в счетчике фалов указано "Файл 1 из 2"</li>
     * <li>Проверяем что отображается название первого файла</li>
     * </ol>
     */
    @Test
    public void testSwitchImagesWithArrowKeys()
    {
        //Подготовка
        DSLAdmin.setUseFroalaRtfEditor(false);

        MetaClass rootMetaClass = DAORootClass.create();

        Attribute attribute = DAOAttribute.createTextRTF(rootMetaClass.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr group = DAOGroupAttr.create(rootMetaClass);
        DSLGroupAttr.add(group, attribute);

        ContentForm content = DAOContentCard.createPropertyList(rootMetaClass, group);
        DSLContent.add(content);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIPropertyList.clickEditLink(content);

        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE);
        GUIRichTextGwt.pasteAndWait(attribute);
        ActionToActiveElement.copyImageToClipboard(DSLFile.BIG_PICTURE2);
        GUIRichTextGwt.pasteAndWait(attribute);

        GUIForm.applyForm();

        GUIRichTextGwt.clickImage(content, attribute);

        GUIFilePreview.assertCounter(1, 2);
        String firstFileTitle = GUIFilePreview.getFileTitle();

        tester.actives().pressingRightKey();

        GUIFilePreview.assertCounter(2, 2);
        String secondFileTitle = GUIFilePreview.getFileTitle();

        Assert.assertNotEquals("Отображаемое название не сменилось", firstFileTitle, secondFileTitle);

        tester.actives().pressingLeftKey();
        GUIFilePreview.assertCounter(1, 2);
        GUIFilePreview.assertFileTitle(firstFileTitle);

        tester.actives().pressingLeftKey();
        GUIFilePreview.assertCounter(2, 2);
        GUIFilePreview.assertFileTitle(secondFileTitle);

        tester.actives().pressingRightKey();
        GUIFilePreview.assertCounter(1, 2);
        GUIFilePreview.assertFileTitle(firstFileTitle);
    }

    /**
     * Тестирование предварительного просмотра .txt файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить userBo файл file в формате .txt</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testTxtFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file = DSLFile.add(userBo, DSLFile.TXT_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_DIV_XPATH, "This is a txt file.");
    }

    /**
     * Тестирование предварительного просмотра .txt файлов с кодировкой win-1251
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00210
     * http://sd-jira.naumen.ru/browse/NSDPRD-5156
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>На карточку компании вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к компании файл file в формате .txt с кодировкой win-1251</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку компании</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file с контентом:</li>
     * <pre>
     *      Доступ открыт с адресов: 
     *      N​aumen – 109.2​34.14.28 
     *      SQL – 109.2​34.14.27 
     *      Л​огин/Пасс: naumen | N@umen 
     * </pre>
     * </ol>
     */
    @Test
    public void testTxtFilePreviewWithWin1251()
    {
        //Подготовка
        MetaClass rootClass = DAORootClass.create();

        ContentForm fileList = DAOContentCard.createFileList(rootClass.getFqn());
        DSLContent.add(fileList);

        Bo root = SharedFixture.root();

        SdFile file = DSLFile.add(root, DSLFile.TXT_FILE_WIN1251);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_DIV_XPATH, "Доступ открыт с адресов:\n"
                                                                             + "Naumen – 109.234.14.28\nSQL – 109.234"
                                                                             + ".14.27\n\nЛогин/Пасс: naumen | N@umen");
    }

    /**
     * Тестирование предварительного просмотра .webm файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Список"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>Прикрепляем видео файл videoFile в формате .webm</li>
     * <li>В списке файлов fileList кликаем по строке файла videoFile</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла videoFile</li>
     * </ol>
     */
    @Test
    public void testVideoFilePreview()
    {
        //Подготовка 
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        SdFile videoFile = DAOSdFile.create(DSLFile.WEBM_FILE);
        GUIFileList.addFileFromCard(fileList, videoFile);

        GUIFileOperator.clickOnFileInList(fileList, videoFile);

        //Проверка
        GUITester.assertExists(GUIFilePreview.PREVIEW_XPATH + "//video", true);
    }

    /**
     * Тестирование предварительного просмотра .xls файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к userBo файл file в формате .xls</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testXlsFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file = DSLFile.add(userBo, DSLFile.XLS_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//td[2]", "This is a xls file.");
    }

    /**
     * Тестирование предварительного просмотра .xlsx файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к userBo файл file в формате .xlsx</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testXlsxFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file = DSLFile.add(userBo, DSLFile.XLSX_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//td[2]", "This is a xlsx file.");
    }

    /**
     * Тестирование предварительного просмотра .xlsx файлов c ячейкой формата "числовой"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42791625
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к userBo файл file в формате .xlsx</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * <li>Текст в ячейке равен "2 498,19"</li>
     * </ol>
     */
    @Test
    public void testXlsxNumericFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file = DSLFile.add(userBo, DSLFile.XLSX_FILE_NUMERIC);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextPresent(GUIFilePreview.PREVIEW_CANVAS_XPATH + "//td[2]", "2 498,19");
    }

    /**
     * Тестирование предварительного просмотра .xml файлов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00643
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, в нем тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>На карточку userClass вывести контент fileList типа "Список файлов" с представлением "Иконки"</li>
     * <li>Прикрепить к userBo файл file в формате .xml</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку userBo</li>
     * <li>В списке файлов fileList кликаем по иконке файла file</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверяем что открылся предварительный просмотр файла file</li>
     * </ol>
     */
    @Test
    public void testXmlFilePreview()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm fileList = DAOContentCard.createFileList(userClass.getFqn());
        DSLContent.add(fileList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        SdFile file = DSLFile.add(userBo, DSLFile.XML_FILE);
        file.setExists(true);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIFileOperator.clickOnIcon(fileList, file);

        //Проверка
        GUITester.assertTextContains(GUIFilePreview.PREVIEW_CANVAS_DIV_XPATH,
                "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>"
                + "<content>This is an xml file.</content>");
    }
}
