package ru.naumen.selenium.cases.operator.classes.content;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестировние фильтрации в контентах
 * <AUTHOR>
 * @since 28.01.2025
 */
class ContentFilter2Test extends AbstractTestCaseJ5
{

    private static MetaClass employeeCase, userClass2, rootClass;
    private static Attribute objectLinkAttr, objectLinkAttrEmployee, boLinksAttr, objectLinkAttrUserClass1,
            boLinksAttrUserClass;
    private static Bo employee, userBo1, userBo2, userBoUserClass1, userBoUserClass11, userBoUserClass12;
    private static ContentForm childObjectList, rootList, hierarchyGrid;
    private static GroupAttr employeeSystemGroup;
    private static StructuredObjectsView structure;
    private static StructuredObjectsViewItem userClassItem;

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>В ИА создать пользовательский класс:</li>
     * <ul>
     *     <li>Название / код - UserClass1</li>
     *     <li>Объекты вложены в - В объекты своего класса</li>
     * </ul>
     * <li>В UserClass1 создать тип userCase1</li>
     * <li>В классе UserClass1 создать атрибут типа “Ссылка на БО”:</li>
     * <ul>
     *     <li>Название / код - objectLinkAttrUserClass1</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>Добавить атрибут objectLinkAttrUserClass1 в Системную группу атрибутов</li>
     * <li>В классе UserClass1 на карточку объекта добавить контент:</li>
     * <ul>
     *     <li>Тип - список вложенных объектов</li>
     *     <li>Название / код - childObjectList</li>
     *     <li>Класс объектов - UserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Создать класс UserClass2 и в нем тип userCase</li>
     * <li>В классе UserClass2 создать атрибут типа “Набор ссылок на БО”:</li>
     * <ul>
     *     <li>Название / код - boLinksAttr</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>В классе UserClass2 создать атрибут типа “Ссылка на БО”:</li>
     * <ul>
     *     <li>Название / код - objectLinkAttr</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>Добавить атрибуты boLinksAttr и objectLinkAttr в Системную группу атрибутов</li>
     * <li>Вывести на карточку компании список вложенных объектов:</li>
     * <ul>
     *     <li>Название / код - UserClass2</li>
     *     <li>Класс объектов - UserClass2</li>
     *     <li>Группа атрибутов - системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Вывести на карточку компании список вложенных объектов:</li>
     * <ul>
     *     <li>Название / код - UserClass1</li>
     *     <li>Класс объектов - UserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Создать лицензированного сотрудника со всеми правами - employee</li>
     * <li>Создать в классе Сотрудник атрибут:</li>
     * <ul>
     *     <li>Тип - Ссылка на бизнес объект</li>
     *     <li>Название / код - objectLinkAttrEmployee</li>
     *     <li>Класс объектов - UserClass1</li>
     *     <li>Редактируемый в списках = true</li>
     * </ul>
     * <li>В классе Сотрудник создать атрибут типа “Набор ссылок на БО”:</li>
     * <ul>
     *     <li>Название / код - boLinksAttrUserClass</li>
     *     <li>Класс объекта - UserClass2</li>
     * </ul>
     * <li>Добавить атрибуты boLinksAttrUserClass и objectLinkAttrEmployee в Системную группу атрибутов</li>
     * <li>В ИО создать 1 объект класса UserClass1:</li>
     * <ul>
     *     <li>userBoUserClass1</li>
     * </ul>
     * <li>Создать 2 объекта класса UserClass1, вложенные в объект userBoUserClass1:</li>
     * <ul>
     *     <li>userBoUserClass1.1</li>
     *     <li>userBoUserClass1.2</li>
     * </ul>
     * <li>Создать 2 объекта класса UserClass2:</li>
     * <ul>
     *     <li>userBo1</li>
     *     <li>userBo2</li>
     * </ul>
     * <li>В ИО для объектов класса UserClass2 заполнить атрибут boLinksAttr:</li>
     * <ul>
     *     <li>Объект: userBo1</li>
     *     <li>boLinksAttr / objectLinkAttr: userBoUserClass1</li>
     *     <li>Объект: userBo2</li>
     *     <li>boLinksAttr / objectLinkAttr: userBoUserClass1.1</li>
     * </ul>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        MetaClass userClass1 = DAOUserClass.createInSelf();
        MetaClass userCase1 = DAOUserCase.create(userClass1);

        userClass2 = DAOUserClass.createInSelf();
        MetaClass userCase2 = DAOUserCase.create(userClass2);
        DSLMetaClass.add(userClass1, userCase1, userClass2, userCase2);

        objectLinkAttrUserClass1 = DAOAttribute.createObjectLink(userClass1.getFqn(), userClass1, null);
        GroupAttr systemGroup1 = DAOGroupAttr.createSystem(userClass1);

        DSLAttribute.add(objectLinkAttrUserClass1);
        DSLGroupAttr.edit(systemGroup1, new Attribute[] { objectLinkAttrUserClass1 }, new Attribute[] {});

        childObjectList = DAOContentCard.createChildObjectList(userClass1.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, userClass1, systemGroup1);
        DSLContent.add(childObjectList);

        boLinksAttr = DAOAttribute.createBoLinks(userClass2.getFqn(), userClass1);

        objectLinkAttr = DAOAttribute.createObjectLink(userClass2.getFqn(), userClass1, null);
        GroupAttr systemGroup2 = DAOGroupAttr.createSystem(userClass2);
        DSLAttribute.add(boLinksAttr, objectLinkAttr);
        DSLGroupAttr.edit(systemGroup2, new Attribute[] { boLinksAttr, objectLinkAttr }, new Attribute[] {});

        rootClass = DAORootClass.create();
        rootList = DAOContentCard.createChildObjectList(rootClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass2, systemGroup2);
        ContentForm rootList2 = DAOContentCard.createChildObjectList(rootClass.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass1, systemGroup1);
        DSLContent.add(rootList, rootList2);

        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);
        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true, true);
        DSLBo.add(employee);

        objectLinkAttrEmployee = DAOAttribute.createObjectLink(employeeCase, userClass1);
        objectLinkAttrEmployee.setEditableInLists(true);

        boLinksAttrUserClass = DAOAttribute.createBoLinks(employeeCase, userClass2);
        boLinksAttrUserClass.setEditableInLists(true);

        DSLAttribute.add(objectLinkAttrEmployee, boLinksAttrUserClass);

        employeeSystemGroup = DAOGroupAttr.createSystem(employeeCase);

        DSLGroupAttr.edit(employeeSystemGroup, new Attribute[] { boLinksAttrUserClass, objectLinkAttrEmployee },
                new Attribute[] {});

        userBoUserClass1 = DAOUserBo.create(userCase1);
        DSLBo.add(userBoUserClass1);

        userBoUserClass11 = DAOUserBo.create(userCase1);
        userBoUserClass11.setParentUuid(userBoUserClass1.getUuid());
        userBoUserClass12 = DAOUserBo.create(userCase1);
        userBoUserClass12.setParentUuid(userBoUserClass1.getUuid());
        DSLBo.add(userBoUserClass11, userBoUserClass12);

        userBo1 = DAOUserBo.create(userCase2);
        userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2);

        boLinksAttr.setBoValue(userBoUserClass1);
        objectLinkAttr.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(userBo1, boLinksAttr, objectLinkAttr);

        boLinksAttr.setBoValue(userBoUserClass11);
        objectLinkAttr.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(userBo2, boLinksAttr, objectLinkAttr);
    }

    /**
     * Возврат настроек для объектов, созданных в общей подготовке
     */
    @AfterEach
    void resetSettings()
    {
        boLinksAttr.setBoValue(userBoUserClass1);
        objectLinkAttr.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(userBo1, boLinksAttr, objectLinkAttr);

        boLinksAttr.setBoValue(userBoUserClass11);
        objectLinkAttr.setBoValue(userBoUserClass11);
        objectLinkAttrEmployee.setValue(null);
        objectLinkAttrUserClass1.setValue(null);
        boLinksAttrUserClass.setValue(null);

        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttr, objectLinkAttr);
        DSLBo.editAttributeValue(userBoUserClass11, objectLinkAttrUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee, boLinksAttrUserClass);

    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка со значением параметра “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Сотрудник вывести контент:</li>
     * <ul>
     *     <li>Тип - список объектов</li>
     *     <li>Название / код - objectList</li>
     *     <li>Класс объектов списка - UserClass</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму настройки ограничения при фильтрации:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttr</li>
     *     <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя(включая вложенные)”</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     <li>“Игнорировать, если атрибут пуст” = true</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <b>Действия и проверки</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>На контенте objectList отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>на контенте objectList отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass11, сохранить</li>
     *     <li>На контенте objectList отображается один объект userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass12, сохранить</li>
     *     <li>Контент objectList пуст</li>
     * </ol>
     * </ol>
     */
    @Test
    void testFilterByUserAttributeIgnoreEmpty()
    {
        //Подготовка
        ContentForm objectList = DAOContentCard.createObjectAdvList(employeeCase.getFqn(),
                DAOGroupAttr.createSystem(employeeCase), userClass2);

        objectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr, true,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.add(objectList);

        //Действия и проверки
        GUILogon.login(employee);
        objectList.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        objectList.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        objectList.advlist().content().asserts().rowsPresence(userBo2);
        objectList.advlist().content().asserts().rowsAbsence(userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        objectList.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для условия отображения
     * контента типа “Список вложенных объектов”
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА открыть карточку класса UserClass1</li>
     *     <li>На контенте childObjectList открыть форму “Изменения контента”</li>
     *     <li>Открыть форму “Настройка условия отображения контента”:
     *         <ul>
     *             <li>В первом поле выбрать атрибут - objectLinkAttrUserClass1</li>
     *             <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя(включая вложенные)”</li>
     *             <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *         </ul>
     *     </li>
     *     <li>Сохранить настройки</li>
     * </ol>
     * <b>Действия:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1, сохранить.</li>
     *     <li>Открыть карточку объекта userBoUserClass1</li>
     *     <li>Контент childObjectList не отображается</li>
     *     <li>Открыть форму редактирования контента “Основные параметры” у объекта userBoUserClass1,
     *         выставить атрибуту objectLinkAttrUserClass1 значение - userBoUserClass1.1</li>
     *     <li>Контент childObjectList отображается</li>
     *     <li>Открыть карточку сотрудника employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1.1, сохранить.</li>
     *     <li>Открыть карточку объекта userBoUserClass1</li>
     *     <li>Контент childObjectList отображается</li>
     *     <li>Открыть карточку сотрудника employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1.2, сохранить.</li>
     *     <li>Открыть карточку объекта userBoUserClass1</li>
     *     <li>Контент childObjectList не отображается</li>
     * </ol>
     */
    @Test
    void testCurrentUserAttributeNestedListDisplay()
    {
        //Подготовка
        childObjectList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(
                new FilterBlockOr(objectLinkAttrUserClass1, FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED,
                        false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.edit(childObjectList);

        //Действия и проверки
        GUILogon.login(employee);
        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        GUIBo.goToCard(userBoUserClass1);
        GUIBo.assertThatBoCard(userBoUserClass1);
        childObjectList.advlist().asserts().absence();

        objectLinkAttrUserClass1.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        childObjectList.advlist().asserts().presence();

        GUIBo.goToCard(employee);
        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        GUIBo.goToCard(userBoUserClass1);
        GUIContent.assertPresent(childObjectList);

        GUIBo.goToCard(employee);
        GUIBo.assertThatBoCard(employee);
        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        GUIBo.goToCard(userBoUserClass1);
        GUIBo.assertThatBoCard(userBoUserClass1);
        childObjectList.advlist().asserts().absence();
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка и условия отображения контента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>На карточку класса Сотрудник вывести контент:</li>
     *     <ul>
     *         <li>Тип - Список связанных объектов</li>
     *         <li>Название / код - relatedObjectList</li>
     *         <li>Атрибут - boLinksAttrUserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *         <li>Представление - Сложный список</li>
     *     </ul>
     *     <li>Открыть форму настройки ограничения при фильтрации:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - objectLinkAttr</li>
     *         <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     *     <li>Открыть форму “Изменения контента”</li>
     *     <li>Открыть форму “Настройка условия отображения контента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttrUserClass / objectLinkAttr</li>
     *         <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту boLinksAttrUserClass
     *     значение - userBo1 и userBo2, сохранить</li>
     *     <li>Контент relatedObjectList не отображается на карточке объекта employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте objectList отображаются два объекта userBo1 и userBo1</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте objectList отображается один объект userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент objectList пуст</li>
     * </ol>
     */
    @Test
    void testCurrentUserAttributeEquality()
    {
        // Подготовка
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(employeeCase.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST,
                String.format("%s@%s", employeeCase.getFqn(), boLinksAttrUserClass.getCode()),
                employeeSystemGroup);
        relatedObjectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        relatedObjectList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(
                new FilterBlockOr(Lists.newArrayList(boLinksAttrUserClass, objectLinkAttr),
                        FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED,
                        false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.add(relatedObjectList);
        // Действия и проверки
        GUILogon.login(employee);

        boLinksAttrUserClass.setBoValue(userBo1, userBo2);
        DSLBo.editAttributeValue(employee, boLinksAttrUserClass);
        tester.refresh();
        GUIBo.assertThatBoCard(employee);
        relatedObjectList.advlist().asserts().absence();

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo2);
        relatedObjectList.advlist().content().asserts().rowsAbsence(userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIBo.assertThatBoCard(employee);
        relatedObjectList.advlist().asserts().absence();
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка иерархического дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *         <li>Построить структуру объектов (вниз), начиная с текущего объекта = true</li>
     *     </ul>
     *     <li>Открыть форму “Настройки ограничения содержимого списка”</li>
     *     <li>Открыть форму ограничения элемента UserClass:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - objectLinkAttr</li>
     *         <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Контент HierarchicalTree пуст</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testHierarchicalTreeContentByUserAttribute()
    {
        //Подготовка
        createHierarchyGridForUserClass(true);
        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(objectLinkAttr, 1, 1,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED);
        GUIHierarchyGrid.objectFilterSettingsForm().setAttributeTree(1, 1, objectLinkAttrEmployee);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        //Действия и проверки
        verifyHierarchyGridForEmployee();
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для ограничения содержимого
     * элемента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>Открыть форму “Ограничение содержимого элемента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - objectLinkAttr</li>
     *         <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *         <li>Построить структуру объектов (вниз), начиная с текущего объекта = true</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Контент HierarchicalTree пуст</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testContentRestrictionEqualsCurrentUserAttribute()
    {
        //Подготовка
        createHierarchyGridForUserClass(true);
        userClassItem.setDefaultFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLStructuredObjectsView.edit(structure);

        //Действия и проверки
        verifyHierarchyGridForEmployee();
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего пользователя (включая вложенные)” для ограничения содержимого
     * элемента со значением параметра “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>Открыть форму “Ограничение содержимого элемента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - objectLinkAttr</li>
     *         <li>Во втором критерий фильтрации - “Равно атрибуту текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *         <li>Установить параметр “Игнорировать, если атрибут пуст” = true</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *         <li>Построить структуру объектов (вниз), начиная с текущего объекта = true</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testContentFilterByCurrentUserAttributeWithNestedAndIgnoreEmpty()
    {
        //Подготовка
        createHierarchyGridForUserClass(true);
        userClassItem.setDefaultFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr, true,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLStructuredObjectsView.edit(structure);

        //Действия и проверки
        GUILogon.login(employee);

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIBo.assertThatBoCard(employee);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo2);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для условия отображения
     * контента типа “Список объектов”
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА, в классе Сотрудник вывести контент:</li>
     *     <ul>
     *         <li>Тип - Список объектов</li>
     *         <li>Название / код - objectList</li>
     *         <li>Класс объектов - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *         <li>Представление - Сложный список</li>
     *     </ul>
     *     <li>Открыть форму “Изменение контента”</li>
     *     <li>Открыть форму “Настройка условия отображения контента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttrUserClass / boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут boLinksAttrUserClass
     *     значением - userBo1 и userBo2</li>
     *     <li>Контент objectList не отображается на карточке сотрудника employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1</li>
     *     <li>Контент objectList отображается на карточке сотрудника employee</li>
     *     <li>В списке objectList открыть форму редактирования объекта userBo1, изменить значение атрибута boLinksAttr
     *     на [не указано]</li>
     *     <li>Контент objectList отображается на карточке сотрудника employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1.1</li>
     *     <li>Контент objectList отображается на карточке сотрудника employee</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1.2</li>
     *     <li>Контент objectList не отображается на карточке сотрудника employee</li>
     * </ol>
     */
    @Test
    void testContentVisibilityForCurrentUserAttributeWithNestedCriteria()
    {
        //Подготовка
        ContentForm objectList = DAOContentCard.createObjectAdvList(employeeCase.getFqn(),
                DAOGroupAttr.createSystem(employeeCase), userClass2);

        objectList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(
                new FilterBlockOr(Lists.newArrayList(boLinksAttrUserClass, objectLinkAttr),
                        FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED,
                        false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.add(objectList);

        //Действия и проверки
        GUILogon.login(employee);
        boLinksAttrUserClass.setBoValue(userBo1, userBo2);
        DSLBo.editAttributeValue(employee, boLinksAttrUserClass);
        tester.refresh();
        objectList.advlist().asserts().absence();

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIContent.assertPresent(objectList);

        GUIBo.goToCard(userBo1);
        boLinksAttr.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(userBo1, boLinksAttr);
        tester.refresh();
        GUIBo.goToCard(employee);
        GUIContent.assertPresent(objectList);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIContent.assertPresent(objectList);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIBo.assertThatBoCard(employee);
        objectList.advlist().asserts().absence();
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка вложенных объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА перейти на карточку компании</li>
     *     <li>В контенте UserClass открыть форму “Настройка ограничений при фильтрации”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     *     <li>На карточку класса Компания вывести контент:</li>
     *     <ul>
     *         <li>Тип - Список объектов</li>
     *         <li>Название / код - objectListEmployee</li>
     *         <li>Класс объектов - Сотрудник</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *         <li>Представление - Сложный список</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Перейти на карточку Компании</li>
     *     <li>Контент UserClass пуст</li>
     *     <li>В списке objectListEmployee атрибуту objectLinkAttrEmployee, выставить
     *     значение - userBoUserClass1</li>
     *     <li>На контенте UserClass отображается два объекта - userBo1 и userBo2</li>
     *     <li>В списке objectListEmployee атрибуту objectLinkAttrEmployee, выставить
     *     значение - userBoUserClass1.1</li>
     *     <li>На контенте UserClass отображается один объект - userBo2</li>
     *     <li>В списке objectListEmployee атрибуту objectLinkAttrEmployee, выставить
     *     значение - userBoUserClass1.2</li>
     *     <li>Контент UserClass пуст</li>
     * </ol>
     */
    @Test
    void testContainsCurrentUserAttributeIncludingNested()
    {
        //Подготовка
        rootList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttr,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.edit(rootList);
        ContentForm objectListEmployee = DAOContentCard.createObjectAdvList(rootClass.getFqn(),
                DAOGroupAttr.createSystem(employeeCase), DAOEmployeeCase.createClass());

        DSLContent.add(objectListEmployee);

        // Действия и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(SharedFixture.root());
        rootList.advlist().content().asserts().rowsNumberOnCurrentPage(0);
        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        rootList.advlist().content().asserts().rowsPresence(userBo1, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        rootList.advlist().content().asserts().rowsPresence(userBo2);
        rootList.advlist().content().asserts().rowsAbsence(userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        rootList.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка связанных объектов со значением параметра “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * Заполнить атрибут boLinksAttrUserClass у employee значением userBo1, userBo2.
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В классе Сотрудник вывести контент:</li>
     *     <ul>
     *         <li>Тип - Список связанных объектов</li>
     *         <li>Название / код - relatedObjectList</li>
     *         <li>Атрибут - boLinksAttrUserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *         <li>Представление - Сложный список</li>
     *     </ul>
     *     <li>Открыть форму настройки ограничения при фильтрации контента relatedObjectList:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *         <li>Установить параметр “Игнорировать, если атрибут пуст” = true</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>На контенте relatedObjectList отображается два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1</li>
     *     <li>На контенте relatedObjectList отображается два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1.1</li>
     *     <li>На контенте relatedObjectList отображается один объект - userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrEmployee
     *     значением - userBoUserClass1.2</li>
     *     <li>Контент relatedObjectList пуст</li>
     * </ol>
     */
    @Test
    void testFilterRelatedObjectsByUserAttributeWithIgnoreEmpty()
    {
        //Подготовка
        boLinksAttrUserClass.setBoValue(userBo2, userBo1);
        DSLBo.editAttributeValue(employee, boLinksAttrUserClass);
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(employeeCase.getFqn(), true,
                PositionContent.FULL, PresentationContent.ADVLIST,
                String.format("%s@%s", employeeCase.getFqn(), boLinksAttrUserClass.getCode()),
                employeeSystemGroup);
        relatedObjectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr, true,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLContent.add(relatedObjectList);

        //Действия и проверки
        GUILogon.login(employee);
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo1, userBo2);
        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo1, userBo2);
        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo2);
        relatedObjectList.advlist().content().asserts().rowsAbsence(userBo1);
        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для ограничения содержимого
     * списка иерархического дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *     </ul>
     *     <li>Открыть форму “Настройки ограничения содержимого списка”</li>
     *     <li>Открыть форму ограничения элемента UserClass:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     </ul>
     *     <li>Сохранить настройки</li>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Контент HierarchicalTree пуст</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект - userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testFilterHierarchicalTreeByUserAttribute()
    {
        //Подготовка
        createHierarchyGridForUserClass(false);
        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(boLinksAttr, 1, 1,
                FilterCondition.EQUALS_USER_ATTRIBUTE_WITH_NESTED);
        GUIHierarchyGrid.objectFilterSettingsForm().setAttributeTree(1, 1, objectLinkAttrEmployee);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        //Действия и проверки
        verifyHierarchyGridForEmployee();
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для ограничения содержимого
     * элемента иерархического дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>Открыть форму “Ограничение содержимого элемента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *         <li>Сохранить настройки</li>
     *     </ul>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>Контент HierarchicalTree пуст</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект - userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”,
     *         выставить атрибуту objectLinkAttrEmployee значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testHierarchicalTreeContentFilteringByUserAttribute()
    {
        //Подготовка
        createHierarchyGridForUserClass(false);
        userClassItem.setDefaultFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttr,
                FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrEmployee.getFqn()))));
        DSLStructuredObjectsView.edit(structure);

        //Действия и проверки
        GUILogon.login(employee);
        hierarchyGrid.advlist().content().asserts().rowsNumberOnCurrentPage(0);

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        hierarchyGrid.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего пользователя (включая вложенные)” для ограничения содержимого
     * элемента со значением параметра “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <b>Подготовка:</b>
     * <ol>
     *     <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>В ИА в настройках системы открыть класс Структуры</li>
     *     <li>Добавить структуру:</li>
     *     <ul>
     *         <li>Название / код - Structure</li>
     *     </ul>
     *     <li>Добавить элемент структуры:</li>
     *     <ul>
     *         <li>Название / код - UserClass</li>
     *         <li>Объекты - UserClass</li>
     *         <li>Группа атрибутов - Системные атрибуты</li>
     *     </ul>
     *     <li>Открыть форму “Ограничение содержимого элемента”:</li>
     *     <ul>
     *         <li>В первом поле выбрать атрибут - boLinksAttr</li>
     *         <li>Во втором критерий фильтрации - “Содержит атрибут текущего пользователя (включая вложенные)”</li>
     *         <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *         <li>Установить “Игнорировать, если атрибут пуст” = true</li>
     *         <li>Сохранить настройки</li>
     *     </ul>
     *     <li>В классе Сотрудник вывести контент типа “Иерархическое дерево”:</li>
     *     <ul>
     *         <li>Название / код - HierarchicalTree</li>
     *         <li>Структура - Structure</li>
     *     </ul>
     * </ol>
     * <b>Действия и проверки:</b>
     * <ol>
     *     <li>Войти в систему под сотрудником employee</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображаются два объекта - userBo1 и userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.1, сохранить</li>
     *     <li>На контенте HierarchicalTree отображается один объект - userBo2</li>
     *     <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrEmployee
     *     значение - userBoUserClass1.2, сохранить</li>
     *     <li>Контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testHierarchicalTreeContentFilteringWithIgnoreEmptyAttribute()
    {
        //Подготовка
        createHierarchyGridForUserClass(false);
        GUILogon.asSuper();
        GUIStructuredObjectsView.goToCard(structure);
        GUIStructuredObjectsView.editItem(userClassItem);
        GUIStructuredObjectsView.objectFilter().clickChange();
        GUIStructuredObjectsView.objectFilter().addAttr(boLinksAttr, 1, 1,
                FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIStructuredObjectsView.objectFilter().setAttributeTree(1, 1, objectLinkAttrEmployee);
        GUIStructuredObjectsView.objectFilter().setIgnoreIfEmpty(1, 1, true);
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();

        //Действия и проверки
        GUILogon.login(employee);

        GUIHierarchyGrid.assertPresenceElements(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertPresenceElements(hierarchyGrid, userClassItem, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        hierarchyGrid.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Выполняет проверку и настройку контента для заданного работника
     */
    private static void verifyHierarchyGridForEmployee()
    {
        GUILogon.login(employee);
        hierarchyGrid.advlist().content().asserts().rowsNumberOnCurrentPage(0);

        objectLinkAttrEmployee.setBoValue(userBoUserClass1);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo1);
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);

        objectLinkAttrEmployee.setBoValue(userBoUserClass11);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        GUIHierarchyGrid.assertPresenceElement(hierarchyGrid, userClassItem, userBo2);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo1);

        objectLinkAttrEmployee.setBoValue(userBoUserClass12);
        DSLBo.editAttributeValue(employee, objectLinkAttrEmployee);
        tester.refresh();
        hierarchyGrid.advlist().content().asserts().rowsNumberOnCurrentPage(0);
    }

    /**
     * Создаёт и добавляет иерархическое дерево для указанного класса пользователя
     * @param buildHierarchy построить структуру объектов (вниз), начиная с текущего объекта
     */
    private static void createHierarchyGridForUserClass(boolean buildHierarchy)
    {
        structure = DAOStructuredObjectsView.create();
        userClassItem = DAOStructuredObjectsView.createItem(null, userClass2,
                null, DAOGroupAttr.createSystem(userClass2), false, false);
        structure.setItems(userClassItem);
        DSLStructuredObjectsView.add(structure);
        hierarchyGrid = DAOContentCard.createHierarchyGrid(employeeCase, true, PositionContent.FULL,
                structure, buildHierarchy, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);
    }
}