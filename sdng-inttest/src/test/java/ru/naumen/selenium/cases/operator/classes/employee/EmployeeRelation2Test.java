package ru.naumen.selenium.cases.operator.classes.employee;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.GUISecurityGroupList;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на архивирование и удаление сотрудника со связями
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
 *
 * <AUTHOR>
 * @since 31.08.2012
 *
 */
public class EmployeeRelation2Test extends AbstractTestCase
{
    /**
     * Тестирование архивирования сотрудника, ответственного за закрытый
     * Пользовательский БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензионного сотрудника employee со связью с командой team</li>
     * <li>Создать пользовательский БО userBo (с назначением ответственных)</li>
     * <li>Назначить ответственным за объект сотрудника employee</li>
     * <li>Закрыть Пользовательский БО userBo</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заархивировать сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeWhoIsResponsibleForClosedUserBo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        userClass.setHasWorkflow("true");
        DSLMetaClass.add(userClass);
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee, team, userBo);

        DSLTeam.addEmployees(team, employee);

        DSLSc.setResponsible(userBo, team, employee);
        DSLSc.changeState(userBo, DAOBoStatus.createClosed(userCase.getFqn()));

        // Действие
        DSLBo.archive(employee);

        // Проверка
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование архивирования сотрудника, решившего запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензированного сотрудника employee</li>
     * <li>Создать запрос sc</li>
     * <li>Решить запрос sc (Кем решен – сотрудник employee)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заархивировать сотрудника employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeWhoResolveRequest()
    {
        // Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.resolve(sc, SharedFixture.team(), employee);

        // Действие
        DSLBo.archive(employee);

        // Проверка
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование архивирования сотрудника, который был отвественным за запрос
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензированных сотрудников employee1 и employee2</li>
     * <li>Создать запрос sc</li>
     * <li>Назначить сотрудника employee1 ответсвенным за запрос sc</li>
     * <li>Сменить ответственного у запроса sc на сотрудника employee2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заархивировать сотрудника employee1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee1 в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeWhoWasResponsableForRequest()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();

        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        DSLSc.setResponsible(sc, null, employee1);
        DSLSc.setResponsible(sc, null, employee2);

        // Действие
        DSLBo.archive(employee1);

        // Проверка
        DSLBo.assertArchived(employee1);
    }

    /**
     * Тестирование архивирования сотрудника, который был ответственным за
     * Пользовательский БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензионных сотрудников employee1 и employee2 со связью с
     * командой team</li>
     * <li>Создать пользовательский БО userBo (с назначением ответственных)</li>
     * <li>Назначить ответственным за объект сотрудника employee1</li>
     * <li>Сменить ответственного за объект на сотрудника employee2</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заархивировать сотрудника employee1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee1 в архиве, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testArchiveEmployeeWhoWasResponsibleForUserBo()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setHasResponsible("true");
        DSLMetaClass.add(userClass);
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee1, employee2, team, userBo);

        DSLTeam.addEmployees(team, employee1, employee2);

        DSLSc.setResponsible(userBo, team, employee1);
        DSLSc.setResponsible(userBo, team, employee2);

        // Действие
        DSLBo.archive(employee1);

        // Проверка
        DSLBo.assertArchived(employee1);
    }

    /**
     * Тестирование архивирования сотрудника ответственного за архивный запрос в статусе отличном от "Закрыт"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$51488175
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензированного сотрудника employee</b>
     * <li>Создать запрос sc</li>
     * <li>Сделать employee ответственным за запрос sc</li>
     * <li>Заархивировать запрос sc</li>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Поместить employee в архив</li>
     * <li>Проверить, что сотрудник employee в архиве, ошибок нет</li>
     * </ol> 
     */
    @Test
    public void testArchiveEmployeeWithArchivedAndOpenSc()
    {
        //Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, null, employee);
        DSLBo.archive(sc);

        //Действия и проверки
        DSLBo.archive(employee);
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование архивирования сотрудника, который является ответственным за архивный объект
     * в статусе, отличном от "Закрыт".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$51488175
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Добавить пользовательский класс userClass с возможностью назначения ответственного, с ЖЦ)</li>
     * <li>Добавить тип userCase в класс userClass</li>
     * <li>Добавить команду team, сотрудника employee и объект userObj типа userCase</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на карточку объекта userObj</li>
     * <li>Назначить сотрудника employee ответственным за объект userObj</li>
     * <li>Поместить объект userObj в архив</li>
     * <li>Проверить, что объект userObj находится в архиве</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Поместить сотрудника employee в архив</li>
     * <li>Проверить, что сотрудник employee заархивировался</li>
     * </ol>
     */
    @Test
    public void testArchiveResponsibleEmployeeIfLinkedObjIsArchivedAndNotClosedState()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        Bo userObj = DAOUserBo.create(userCase);
        DSLBo.add(team, employee, userObj);
        DSLTeam.addEmployees(team, employee);

        //Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(userObj);
        GUIButtonBar.changeResponsible();
        GUISc.selectResponsible(employee, team);
        GUIForm.applyForm();
        GUIButtonBar.remove();
        GUIForm.confirmByYes();
        DSLBo.assertArchived(userObj);
        GUIBo.goToCard(employee);
        GUIButtonBar.remove();
        GUIForm.confirmByYes();
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями:получатель соглашения,
     * участник команды
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать team типа teamCase</li>
     * <li>Создать agreement типа agreementCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Назначить сотрудника получателем agreement и участником team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>employee удален</li>
     * <li>team - "Участники" null</li>
     * <li>agreement - "получатели" null</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeAgreementTeamRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(teamCase, agreementCase);

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        Bo agreement = DAOAgreement.create(agreementCase, serviceTimeItem, serviceTimeItem);
        DSLBo.add(team, ou, agreement);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        DSLTeam.addEmployees(team, employee);

        Attribute recipientAgreements = DAOAttribute.createPseudo(null, "recipientAgreements",
                Json.GSON.toJson(Lists.newArrayList(agreement.getUuid())));
        DSLBo.editAttributeValue(employee, recipientAgreements);
        // Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIBo.delete(employee);

        // Проверки
        DSLBo.assertAbsence(employee);
        String message = "В команде не должно быть участников.";
        Assert.assertTrue(message, SdDataUtils
                .getMapsArrayValue(SdDataUtils.getFirstObjectByMetaclassFqn(teamCase.getFqn()), "members").isEmpty());
        message = "У соглашения не должно быть получателей.";
        Assert.assertTrue(message, SdDataUtils
                .getMapsArrayValue(SdDataUtils.getFirstObjectByMetaclassFqn(agreementCase.getFqn()), "recipients")
                .isEmpty());
    }

    /**
     * Удаление сотрудника, указанного получателем оповещения в эскалациях
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00450
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>В эскалациях escalation Добавить действие notification:</li>
     * <li>Объекты – любые, Действие - Оповещение заполнить любым значением
     * поля: Название, Тема, Текст оповещения поле Кому->Сотрудники выбрать
     * сотрудника employee</li>
     * <li>Включить действие notification</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить сотрудника</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee удален, ошибок нет</li>
     * <li>В настройках действия notification в поле Кому пусто</li>
     * <br>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeInEscalationEventAction()
    {
        // Подготовка
        MetaClass employeeClass = DAOEmployeeCase.createClass();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        TimerDefinition timeAllowanceTimer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme escalation = DAOEscalationSheme.create(timeAllowanceTimer, true, employeeClass);
        DSLEscalation.add(escalation);

        EventAction notification = DAOEventAction.createNotificationByMetaClass(employeeClass);
        notification.addRecipients(employee);
        notification.setEventType(EventType.escalation.name());
        DSLEventAction.add(notification);

        // Действие
        DSLBo.delete(employee);

        // Проверка
        DSLBo.assertAbsence(employee);

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(notification);
        String xPath = String.format(GUIXpath.Any.ANY_VALUE + "/..", "to");
        Assert.assertEquals("Сотрудник присутствует в поле \"Кому\"", tester.getText(xPath), "");
    }

    /**
     * Удаление сотрудника, указанного получателем оповещения в действии на событие
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Добавить действие по событию notification:</li>
     * <li>1. Заполнить обязательные атрибуты: Название, Тема, Текст оповещения любым значением; (Объект и Событие –
     * любые, Действие - Оповещение)</li>
     * <li>2. Указать в оповещении в поле Кому->Сотрудники: сотрудник employee</li>
     * <li>Включить добавленное Действие по событию notification</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить сотрудника</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник удален, ошибок нет.</li>
     * <li>В параметрах Действия по событию notification в поле Кому пусто</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeInEventAction()
    {
        // Подготовка
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        EventAction notification = DAOEventAction.createNotificationByMetaClass(employeeClass);
        notification.addRecipients(employee);
        DSLEventAction.add(notification);

        // Действие
        DSLBo.delete(employee);

        // Проверка
        DSLBo.assertAbsence(employee);

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(notification);
        String xPath = String.format(GUIXpath.Any.ANY_VALUE, "to");
        GUITester.assertTextContainsWithMsg(xPath, "", "Сотрудник присутствует в поле \"Кому\"");
    }

    /**
     * Удаление сотрудника, входящего в группу пользователей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать сотрудника employee типа employeeCase</li>
     * <li>Создать группу пользователей userGroup, добавить в нее сотрудника employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под лицензированным сотрудником.</li>
     * <li>Зайти в карточку employee. Нажать Удалить. Подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Сотрудник employee удалён без ошибок</li>
     * <li>В списке сотрудников в группе userGroup удаленный сотрудник employee не отображается</li>
     * <br>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeInUserGroup()
    {
        // Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIBo.delete(employee);

        // Проверка
        DSLBo.assertAbsence(employee);
        //DSLSecurityGroup.assertUsers(userGroup, employee);
        //TODO NSDPRD-2663 в securityServiceBean не удаляются уиды удаленных объектов
        GUILogon.asSuper();
        GUISecurityGroupList.goToCard(userGroup);
        GUISecurityGroupList.employeeAdvlist().content().asserts().rowsAbsence(employee);
    }

    /**
     * Тестирование удаления сотрудника со связями:руководитель отдела, лидер
     * команды, директор
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать team типа teamCase</li>
     * <li>Создать employee типа employeeCase</li>
     * <li>Назначить сотрудника дирекотом, руководителем ou, лидером team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Удалить employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>employee удален</li>
     * <li>Карточка ou - "Руководитель" - null</li>
     * <li>Карточка team - "Лидер" null</li>
     * <li>Карточка company - "Директор" null</li>
     * </ol>
     */
    @Test
    public void testDeleteEmployeeOuCompanyTeamLeadRelation()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = SharedFixture.teamCase();

        Bo company = SharedFixture.root();
        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);

        // Добавление сотрудника в команду
        DSLTeam.addEmployees(team, employee);

        Attribute companyHead = DAOAttribute.createPseudo(null, "head", employee.getUuid());
        DSLBo.editAttributeValue(company, companyHead);
        Attribute ouHead = DAOAttribute.createPseudo(null, "head", employee.getUuid());
        DSLBo.editAttributeValue(ou, ouHead);
        Attribute leader = DAOAttribute.createPseudo(null, "leader", employee.getUuid());
        DSLBo.editAttributeValue(team, leader);
        // Выполнение действия
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIBo.delete(employee);

        // Проверки
        DSLBo.assertAbsence(employee);
        String message = "У компании присутствует директор.";
        Assert.assertNull(message, SdDataUtils.getFirstObjectByMetaclassFqn("root").get("head"));
        message = "У отдела присутствует руководитель.";
        Assert.assertNull(message, SdDataUtils.getFirstObjectByMetaclassFqn(ouCase.getFqn()).get("head"));
        message = "У команды присутствует лидер.";
        Assert.assertNull(message, SdDataUtils.getFirstObjectByMetaclassFqn(teamCase.getFqn()).get("leader"));
    }

    /**
     * Тестирование архивирования Сотрудника, выбранного в качестве значения по умолчанию в агрегирующем атрибуте для 
     * значения Сотрудник-Отдел
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип команды teamCase</li>
     * <li>Создать отдел ou</li>
     * <li>Создать в отделе ou сотрудника employee</li>
     * <li>В типе команды teamCase создать дополнительный атрибут: aggrAttr типа Агрегирующий атрибут (агрегировать 
     * класс — Отдел, значение по умолчанию - "employee-ou")</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Нажать кнопку "Поместить в архив"</li>
     * <li>Подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение об ошибке: "Сотрудник 'employee' не может быть помещен в архив по следующим причинам: 
     * 1. Сотрудник используется в качестве значения по умолчанию атрибутов: 'aggrAttr' (тип 'teamCase' класса 
     * 'Команда')."</li>
     * <li>Сотрудник employee не в архиве</li>
     * </ol>
     */
    @Test
    public void testTryArchiveEmplAsDefaultValueOfEmplOuAgrAttr()
    {
        // Подготовка
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(teamCase);

        Bo ou = SharedFixture.ou();

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        DSLBo.add(employee);

        Attribute aggrAttr = DAOAttribute.createAggregate(teamCase, AggregatedClasses.OU, ou, employee);
        DSLAttribute.add(aggrAttr);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        // Проверка
        String expectedMessage = String.format(
                ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_REM + "\n" + ErrorMessages.FIRST + ErrorMessages.EMP
                + ErrorMessages.USE_AS_DEF_VALUE + ErrorMessages.DEF_ATTR_PATTERN,
                employee.getTitle(), aggrAttr.getTitle() + " (Сотрудник)", teamCase.getTitle(), "Команда");
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(employee, expectedMessage);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование архивирования Сотрудника, выбранного в качестве значения по умолчанию в агрегирующем атрибуте для 
     * значения Сотрудник-Команда
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00059
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать типы команд teamCase1, teamCase2</li>
     * <li>Создать отдел ou</li>
     * <li>Создать в отделе ou сотрудника employee</li>
     * <li>Создать команду team1 типа teamCase1</li>
     * <li>Добавить в команду team1 сотрудника employee</li>
     * <li>В типе команды teamCase2 создать дополнительный атрибут: aggrAttr типа Агрегирующий атрибут (агрегировать 
     * класс - Команда, значение по умолчанию - "employee-team1")</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку сотдруника employee</li>
     * <li>Нажать кнопку "Поместить в архив"</li>
     * <li>Подтвердить действие</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появилось сообщение об ошибке: "Сотрудник 'employee' не может быть помещен в архив по следующим причинам: 
     * 1. Сотрудник используется в качестве значения по умолчанию атрибутов: 'aggrAttr' (тип 'teamCase2' класса 
     * 'Команда')."</li>
     * <li>Сотрудник employee не в архиве</li>
     * </ol>
     */
    @Test
    public void testTryArchiveEmplAsDefaultValueOfEmplTeamAgrAttr()
    {
        // Подготовка
        MetaClass teamCase1 = DAOTeamCase.create();
        MetaClass teamCase2 = DAOTeamCase.create();
        DSLMetaClass.add(teamCase1, teamCase2);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        Bo team1 = DAOTeam.create(teamCase1);
        DSLBo.add(employee, team1);

        DSLTeam.addEmployees(team1, employee);

        Attribute aggrAttr = DAOAttribute.createAggregate(teamCase2, AggregatedClasses.TEAM, team1, employee);
        DSLAttribute.add(aggrAttr);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        // Проверка
        String expectedMessage = String.format(
                ErrorMessages.EMP + "'%s' " + ErrorMessages.NOT_BE_REM + "\n" + ErrorMessages.FIRST + ErrorMessages.EMP
                + ErrorMessages.USE_AS_DEF_VALUE + ErrorMessages.DEF_ATTR_PATTERN,
                employee.getTitle(), aggrAttr.getTitle() + " (Сотрудник)", teamCase2.getTitle(), "Команда");
        GUIBo.tryToArchiveBoAndCheckMessagesExistence(ou, expectedMessage);
        DSLBo.assertNotArchived(employee);
    }

    /**
     * Тестирование архивирования сотрудника со связями: автор объектов (отдел и
     * пользовательский бо)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00057
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать employee</li>
     * <li>Создать ou типа ouCase, автор- employee</li>
     * <li>Создать obj типа userCase, автор- employee</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Архивируем employee</li>
     * <br>
     * <b>Проверки</b>
     * <li>employee в архиве</li>
     * </ol>
     */
    @Test
    public void testTryToArchiveEmployeeAuthorOfObjects()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);

        DSLMetaClass.add(ouCase, userClass, userCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        Bo obj = DAOUserBo.create(userCase);
        Bo ou1 = DAOOu.create(ouCase);
        Attribute authObj = SysAttribute.author(userCase);
        authObj.setValue(employee.getUuid());
        Attribute authOu = SysAttribute.author(ouCase);
        authOu.setValue(employee.getUuid());
        DSLBo.add(ou1, obj);
        DSLBo.editAttributeValue(ou1, authOu);
        DSLBo.editAttributeValue(obj, authObj);

        // Выполнение действия
        DSLBo.archive(employee);

        // Проверки
        DSLBo.assertArchived(employee);
    }

    /**
     * Тестирование разрыва связи сотрудника с командой, если эта связь используется в качестве
     * значения агрегирующего атрибута в таблице соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00073
     * http://sd-jira.naumen.ru/browse/NSDPRD-4582
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В классе userClass создать агрегирующий атрибут aggregateAttr
     * (Агрегируемые классы - Сотрудник, Команда)</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>Создать таблицу соответствий item (Объекты - userClass, Определяемые атрибуты - aggregateAttr,
     * Определяющие атрибуты - stringAttr)</li>
     * <li>Создать команду team</li>
     * <li>Создать сотрудника employee и поместить его в команду team</li>
     * <li>Создать строку row в таблице соответствий item (aggregateAttr = [team, employee],
     * stringAttr задать произвольное значение)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму редактирования команды team</li>
     * <li>Убрать сотрудника employee из поля "Участники команды" и нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что на экране появилось сообщение об ошибке:<br>
     * "Команда '%team%' не может быть изменена по следующим причинам:<br>
     * 1. Дочерний объект сотрудник '%employee%' не может быть исключен из команды.
     * Это отношение используется в таблице соответствий '%item%'."</li>
     * <li>Проверить, что сотрудник остался в команде team</li>
     * </ol>
     */
    @Test
    public void testUnlinkEmployeeWithTeamRelationUsedInValueMap()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.TEAM, null, null);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(aggregateAttr, stringAttr);

        CatalogItem item = DAOCatalogItem.createRulesSettings(userClass, aggregateAttr, stringAttr);
        DSLCatalogItem.add(item);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(team, employee);
        DSLTeam.addEmployees(team, employee);
        RsRow row = DAORsRow.create(item, aggregateAttr, AttributeUtils.prepareAggregateForRuleSettings(team, employee),
                stringAttr, ModelUtils.createTitle());
        DSLRsRows.addRowToRSItem(row);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(team);
        BoTree tree = new BoTree(GUIXpath.Id.MEMBERS_VALUE, false);
        tree.unsetElementInMultiSelectTree(SharedFixture.ou(), employee);
        GUIForm.applyFormAssertError(String.format(ErrorMessages.TEAM_EDIT_RELATION_IN_USE, team.getTitle(),
                employee.getTitle(), item.getTitle()));
        DSLEmployee.assertInTeam(employee, team);
    }

    /**
     * Тестирование разрыва связи сотрудника командой, ответственной за запрос
     * (сотрудник – не единственный исполнитель)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00073 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать лицензированных сотрудников employee1 и employee2</li>
     * <li>Создать команду team</li>
     * <li>Создать запрос sc</li>
     * <li>Назначить отвественной за запрос sc команду team</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Разорвать связь между командой team и сотрудником employee1</li>
     * <br>
     * <b>Проверки</b>
     * <li>Связь разорвана, команды team нет в списке команд сотрудника
     * employee1, ошибок нет</li>
     * </ol>
     */
    @Test
    public void testUnlinkEmployeeWithTeamResponsableForRequest()
    {
        // Подготовка
        MetaClass employeeCase = SharedFixture.employeeCase();
        Bo ou = SharedFixture.ou();
        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee1, employee2);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(team);
        DSLTeam.addEmployees(team, employee1, employee2);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team, null);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(team);

        tester.click(GUIXpath.Div.EDIT_CONTAINS);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        BoTree tree = new BoTree(GUIXpath.Id.MEMBERS_VALUE, false);
        tree.unsetElementInMultiSelectTree(ou, employee1);
        GUIForm.applyForm();

        // Проверка
        DSLEmployee.assertNotInTeam(employee1, team);
    }
}
