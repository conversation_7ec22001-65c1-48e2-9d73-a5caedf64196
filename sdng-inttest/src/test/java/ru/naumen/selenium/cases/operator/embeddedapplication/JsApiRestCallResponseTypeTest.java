package ru.naumen.selenium.cases.operator.embeddedapplication;

import static ru.naumen.selenium.casesutil.embeddedapplication.EmbeddedApplicationTemplateFactory.withBlobCreation;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.hamcrest.MatcherAssert;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование управления ожидаемым responseType при запросах через jsApi.restCall
 *
 * <AUTHOR>
 * @since 26.12.2022
 */
class JsApiRestCallResponseTypeTest extends AbstractTestCaseJ5
{
    @TempDir
    public File temp;

    private static MetaClass userCase;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать тип scCase класса Запрос</li>
     * <li>Создать класс userClass и его тип userCase</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
    }

    /**
     * Тестирование работы метода jsApi.restCall с responseType = "" или "text" и без него
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125410324
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение application с телом:
     * <pre>
     *     jsApi.restCall('exec?func=modules.testRestCallWithText.test&params=')
     *         .then(str => document.getElementById('test_div').innerHTML = str)
     * </pre>
     * и скриптовым модулем с кодом 'testRestCallToLicensedModule' и содержимым:
     * <pre>
     *     def str() {
     *         '$randomString'
     *     }
     * </pre>
     * </li>
     * <li>Добавить контент content типа "Встроенное приложение" c приложением application
     * на карточку класса Компании</li>
     * <li>Добавить встроенное приложение application2 с телом:
     * <pre>
     *     jsApi.restCall('exec?func=modules.testRestCallWithText.test&params=', {
     *         responseType: ''
     *     })
     *         .then(str => document.getElementById('test_div').innerHTML = str)
     * </pre>
     * и лицензированным скриптовым модулем с кодом 'testRestCallWithText' и содержимым:
     * <pre>
     *     def str() {
     *         '$randomString'
     *     }
     * </pre>
     * </li>
     * <li>Добавить контент content2 типа "Встроенное приложение" c приложением application2
     * на карточку класса Компании</li>
     * <li>Добавить встроенное приложение application3 с телом:
     * <pre>
     *     jsApi.restCall('exec?func=modules.testRestCallWithText.test&params=', {
     *         responseType: 'text'
     *     })
     *         .then(str => document.getElementById('test_div').innerHTML = str)
     * </pre>
     * и лицензированным скриптовым модулем с кодом 'testRestCallWithText' и содержимым:
     * <pre>
     *     def str() {
     *         '$randomString'
     *     }
     * </pre>
     * </li>
     * <li>Добавить контент content3 типа "Встроенное приложение" c приложением application3
     * на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении application внутри test_div выведен текст $randomString</li>
     * <li>Проверить, что во встроенном приложении application2 внутри test_div выведен текст $randomString</li>
     * <li>Проверить, что во встроенном приложении application3 внутри test_div выведен текст $randomString</li>
     * <ol>
     */
    @Test
    void testRestCallWithTextResponseType()
    {
        // Подготовка
        String randomString = ModelUtils.createDescription();

        String moduleCode = "testRestCallWithText";
        ModuleConf moduleConf = DAOModuleConf.create(moduleCode, null,
                String.format("def test() {\n"
                              + "   '%s'\n"
                              + "}\n", randomString));

        String template = "%s.then(text => document.getElementById('%s').innerHTML = text)";

        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template, "jsApi.restCall('exec?func=modules." + moduleCode + ".test&params=')",
                        GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf).getAbsolutePath();
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        String fileName2 = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template,
                        "jsApi.restCall('exec?func=modules." + moduleCode + ".test&params=', {\n" +
                        "    responseType: ''\n" +
                        "})", GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf).getAbsolutePath();
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName2);
        DSLEmbeddedApplication.add(application2);

        String fileName3 = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template,
                        "jsApi.restCall('exec?func=modules." + moduleCode + ".test&params=', {\n" +
                        "    responseType: 'text'\n" +
                        "})", GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf).getAbsolutePath();
        EmbeddedApplication application3 = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName3);
        DSLEmbeddedApplication.add(application3);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application2);
        ContentForm content3 = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application3);
        DSLContent.add(content, content2, content3);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));

        GUIContent.assertPresent(content2);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content2));

        GUIContent.assertPresent(content3);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content3));
    }

    /**
     * Тестирование работы метода jsApi.restCall с responseType = "json" и метода jsApi.restCallAsJson
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125410324
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение application с телом:
     * <pre>
     *     jsApi.restCall('exec?func=modules.testRestCallWithJson.test&params=', {
     *         responseType: 'json'
     *     }).then(json => document.getElementById('test_div').innerHTML = json)
     * </pre>
     * и лицензированным скриптовым модулем с кодом 'testRestCallWithJson' и содержимым:
     * <pre>
     *     def test() {
     *         '"$randomString"'
     *     }
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c приложением application
     * на карточку класса Компании</li>
     * <li>Добавить встроенное приложение application2 с телом:
     * <pre>
     *     jsApi.restCallAsJson('exec?func=modules.testRestCallWithJson.test&params=')"
     *         .then(json => document.getElementById('test_div').innerHTML = json)
     * </pre>
     * и лицензированным скриптовым модулем с кодом 'testRestCallWithJson' и содержимым:
     * <pre>
     *     def test() {
     *         '"$randomString"'
     *     }
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c приложением application2
     * на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении application внутри test_div выведен текст "$randomString"</li>
     * <li>Проверить, что во встроенном приложении application2 внутри test_div выведен текст "$randomString"</li>
     * <ol>
     */
    @Test
    void testRestCallWithJsonResponseType()
    {
        // Подготовка
        String randomString = ModelUtils.createDescription();

        String moduleCode = "testRestCallWithJson";
        ModuleConf moduleConf = DAOModuleConf.create(moduleCode, null,
                String.format("def test() {\n"
                              + "   '\"%s\"'\n"
                              + "}\n", randomString));

        String template = "%s.then(json => document.getElementById('%s').innerHTML = json)";

        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template,
                        "jsApi.restCall('exec?func=modules." + moduleCode + ".test&params=', {\n" +
                        "    responseType: 'json'\n" +
                        "})", GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf).getAbsolutePath();
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        String fileName2 = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template, "jsApi.restCallAsJson('exec?func=modules." + moduleCode + ".test&params=')",
                        GUIEmbeddedApplication.TEST_DIV_ID),
                moduleConf).getAbsolutePath();
        EmbeddedApplication application2 = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName2);
        DSLEmbeddedApplication.add(application2);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        ContentForm content2 = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application2);
        DSLContent.add(content, content2);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));

        GUIContent.assertPresent(content2);
        Assertions.assertEquals(randomString, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content2));
    }

    /**
     * Тестирование работы метода jsApi.restCall с responseType = "blob"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125410324
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение application с телом:
     * <pre>
     *     function convertBlobToDataURL(blob) {
     *         return new Promise((resolve, reject) => {
     *             const reader = new FileReader();
     *             reader.onerror = reject;
     *             reader.onload = () => {
     *                 resolve(reader.result);
     *             };
     *
     *             reader.readAsDataURL(blob);
     *         })
     *     }
     *
     *     jsApi.restCall('get-file/%идентификатор файла%', {
     *         responseType: 'blob'
     *     })
     *         .then(blob => convertBlobToDataURL(blob))
     *         .then(str => document.getElementById('test_div').innerHTML = str)
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c приложением application
     * на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении application внутри test_div выведен файл в формате dateUrl</li>
     * <ol>
     */
    @Test
    void testRestCallWithBlobResponseType()
    {
        // Подготовка
        SdFile file = DSLFile.add(SharedFixture.root(), DSLFile.IMG_FOR_UPLOAD);

        String dateUrl = "data:image/png;base64," + DSLFile.getFileContentAsBase64(DSLFile.IMG_FOR_UPLOAD);

        String template = "function convertBlobToDataURL(blob) {\n" +
                          "    return new Promise((resolve, reject) => {\n" +
                          "        const reader = new FileReader();\n" +
                          "        reader.onerror = reject;\n" +
                          "        reader.onload = () => {\n" +
                          "            resolve(reader.result);\n" +
                          "        };\n" +
                          "        \n" +
                          "        reader.readAsDataURL(blob);\n" +
                          "    })\n" +
                          "}" +
                          "\n" +
                          "jsApi.restCall('get-file/%s', {\n" +
                          "    responseType: 'blob'\n" +
                          "})" +
                          "    .then(blob => convertBlobToDataURL(blob))\n" +
                          "    .then(dataURL => document.getElementById('%s').innerHTML = dataURL)";

        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template, file.getUuid(), GUIEmbeddedApplication.TEST_DIV_ID)).getAbsolutePath();
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(dateUrl, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));
    }

    /**
     * Тестирование работы метода jsApi.restCall с responseType = "arrayBuffer"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125410324
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Добавить встроенное приложение с телом:
     * <pre>
     *     function convertBlobToDataURL(blob) {
     *         return new Promise((resolve, reject) => {
     *             const reader = new FileReader();
     *             reader.onerror = reject;
     *             reader.onload = () => {
     *                 resolve(reader.result);
     *             };
     *
     *             reader.readAsDataURL(blob);
     *         })
     *     }
     *
     *     jsApi.restCall('get-file/%идентификатор файла%', {
     *         responseType: 'arraybuffer'
     *     }).then(buffer => new Blob([buffer], {
     *         type: 'image/png'
     *     }))
     *         .then(blob => convertBlobToDataURL(blob))
     *         .then(str => document.getElementById('test_div').innerHTML = str)
     * </pre>
     * <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее
     * приложением на карточку класса Компании</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Проверить, что во встроенном приложении внутри test_div выведен файл в формате dateUrl</li>
     * <ol>
     */
    @Test
    void testRestCallWithArrayBufferResponseType()
    {
        // Подготовка
        SdFile file = DSLFile.add(SharedFixture.root(), DSLFile.IMG_FOR_UPLOAD);

        String dateUrl = "data:image/png;base64," + DSLFile.getFileContentAsBase64(DSLFile.IMG_FOR_UPLOAD);

        String template = "function convertBlobToDataURL(blob) {\n" +
                          "    return new Promise((resolve, reject) => {\n" +
                          "        const reader = new FileReader();\n" +
                          "        reader.onerror = reject;\n" +
                          "        reader.onload = () => {\n" +
                          "            resolve(reader.result);\n" +
                          "        };\n" +
                          "        \n" +
                          "        reader.readAsDataURL(blob);\n" +
                          "    })\n" +
                          "}\n" +
                          "\n" +
                          "jsApi.restCall('get-file/%s', {\n" +
                          "    responseType: 'arraybuffer'\n" +
                          "}).then(buffer => new Blob([buffer], {\n" +
                          "    type: 'image/png'\n" +
                          "}))\n" +
                          "    .then(blob => convertBlobToDataURL(blob))\n" +
                          "    .then(str => document.getElementById('%s').innerHTML = str)";

        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp,
                String.format(template, file.getUuid(), GUIEmbeddedApplication.TEST_DIV_ID)).getAbsolutePath();
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);

        MetaClass rootClass = DAORootClass.create();
        ContentForm content = DAOContentCard.createEmbeddedApplication(rootClass.getFqn(), application);
        DSLContent.add(content);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIContent.assertPresent(content);
        Assertions.assertEquals(dateUrl, GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content));
    }

    /**
     * Тестирование корректности загрузки нескольких файлов на сервер через jsApi
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108427850
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать объект userBo2 типа userCase</li>
     * <li>Создать файл встроенного приложения fileName
     * <pre>
     *     var canvas = document.createElement('canvas')
     *     var ctx = canvas.getContext('2d');
     *
     *     ctx.beginPath();
     *     ctx.arc(75, 75, 50,0,Math.PI*2,true);
     *     ctx.moveTo(110, 75);
     *     ctx.arc(75, 75, 35, 0, Math.PI, false);
     *     ctx.moveTo(65, 65);
     *     ctx.arc(60, 65, 5, 0, Math.PI*2, true);
     *     ctx.moveTo(95, 65);
     *     ctx.arc(90, 65, 5, 0, Math.PI*2, true);
     *     ctx.stroke();
     *
     *     canvas.toBlob((blob) => {
     *         var url = 'add-file/%идентификатор userBo2%'
     *         var formData = new FormData()
     *         formData.append('file1', blob)
     *         formData.append('file2', blob, 'canvas')
     *         jsApi.restCall(url, {
     *             method: 'POST',
     *             body: formData
     *         }).then(() => {
     *             document.getElementById('testDiv').innerText = 'loaded'
     *         })
     *     })
     * </pre>
     * для тестирования загрузки файлов на сервер</li>
     * <li>Добавить контент content типа "Встроенное приложение" c добавленным ранее приложением на карточку
     * типа userCase</li>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под сотрудником (автотестером)</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что к объекту userBo прикреплено 2 файла с названиями 'blob' и 'canvas'</li>
     * </ol>
     */
    @Test
    void testRestCallWithFormDataUpload()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        String jsContent = String.format(
                "var url = 'add-file/%s'\n" +
                "var formData = new FormData()\n" +
                "formData.append('file1', blob)\n" +
                "formData.append('file2', blob, 'canvas')\n" +
                "jsApi.restCall(url, {\n" +
                "    method: 'POST',\n" +
                "    body: formData\n" +
                "}).then(() => {\n" +
                "    document.getElementById('%s').innerText = 'loaded'\n" +
                "})", userBo.getUuid(), GUIEmbeddedApplication.TEST_DIV_ID);

        String fileName = DAOEmbeddedApplication.createApplicationWithJs(temp,
                        withBlobCreation(jsContent))
                .getAbsolutePath();
        EmbeddedApplication application = DAOEmbeddedApplication.createClientSideApplicationFromFile(fileName);
        DSLEmbeddedApplication.add(application);
        ContentForm content = DAOContentCard.createEmbeddedApplication(userCase.getFqn(), application);
        DSLContent.add(content);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIContent.assertPresent(content);
        // Ожидаем загрузки на сервер всех файлов
        GUIEmbeddedApplication.getEmbeddedApplicationTestDivContent(content);

        Set<Map<String, String>> files = DSLFile.getFiles(userBo);
        Assertions.assertEquals(2, files.size(), "Количество файлов должно ровняться 2");

        List<String> actualTitles = files.stream().map(f -> f.get(SdFile.TITLE)).collect(Collectors.toList());
        String[] expectedTitles = new String[] { "blob", "canvas" };
        MatcherAssert.assertThat("Названия последних двух файлов должны быть blob и canvas", actualTitles,
                Matchers.containsInAnyOrder(expectedTitles));
    }
}