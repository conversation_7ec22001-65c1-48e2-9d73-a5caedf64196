package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.AGGREGATED_TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.ResponsibleType.TREE_EDIT;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.ResponsibleType.TREE_EDIT_WITH_FOLDER;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.BaseTreePossibleValueValidator;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута Ответственный на формах через мобильное API.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestResponsiblePossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userCase, userCaseTree, userCaseTreeFolders;
    private static Attribute responsibleAttr;
    private static Folder teamFolder;
    private static Bo team, team2, employee, employee2;
    private static Bo[] teams;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтипы: userCase, userCaseTree, userCaseTreeFolders</li>
     * <li>Создать папку teamFolder в отделе</li>
     * <li>Создать команды team, team2 и добавить команду team в папку teamFolder</li>
     * <li>Создать сотрудников employee, employee2, employee3</li>
     * <li>Добавить в команду team сотрудников employee и employee2, в команду team2 - employee3</li>
     * <li>Создать дополнительно ещё 20 команд teams для тестирования пагинации и добавить сотрудника employee3 в
     * каждую из них</li>
     * <li>Изменить представление атрибута Ответственный в типе userCase на "Список со сдвигом"</li>
     * <li>Изменить представление атрибута Ответственный в типе userCaseTree на "Поле выбора"</li>
     * <li>Изменить представление атрибута Ответственный в типе userCaseTreeFolders на "Поле выбора с папками"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут Ответственный</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createWithResp();
        userCase = DAOUserCase.create(userClass);
        userCaseTree = DAOUserCase.create(userClass);
        userCaseTreeFolders = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase, userCaseTree, userCaseTreeFolders);

        teamFolder = DAOFolder.create(DAOTeamCase.createClass());
        DSLFolder.add(teamFolder);

        Attribute folderAttr = SysAttribute.folders(userClass);
        folderAttr.setItemValue(teamFolder);

        team = DAOTeam.create(SharedFixture.teamCase());
        team.setUserAttribute(folderAttr);
        team2 = DAOTeam.create(SharedFixture.teamCase());
        DAOBo.appendTitlePrefixes(0, 22, team, team2);
        DSLBo.add(team, team2);
        DSLSearch.updateIndex(team, team2);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo employee3 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DAOBo.appendTitlePrefixes(employee, employee2, employee3);
        DSLBo.add(employee, employee2, employee3);
        DSLSearch.updateIndex(employee, employee2, employee3);
        DSLTeam.addEmployees(team, employee, employee2);
        DSLTeam.addEmployees(team2, employee3);

        int expectedCount = 20;
        teams = new Bo[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            teams[i] = DAOTeam.create(SharedFixture.teamCase());
        }
        DAOBo.appendTitlePrefixes(2, 22, teams);
        DSLBo.add(teams);
        DSLSearch.updateIndex(teams);
        DSLEmployee.addToTeams(employee3, teams);

        String teamsPart = Arrays.stream(teams).map(Bo::getUuid).collect(Collectors.joining("','", "'", "'"));
        ScriptInfo responsibleScriptInfo = DAOScriptInfo.createNewScriptInfo("""
                        if (!subject) return []
                        def team1 = '%s'
                        def team2 = '%s'
                        return [team1, team2, [(team1): ['%s', '%s'], (team2): ['%s']], %s]""",
                team.getUuid(), team2.getUuid(), employee.getUuid(), employee2.getUuid(), employee3.getUuid(),
                teamsPart);
        DSLScriptInfo.addScript(responsibleScriptInfo);

        responsibleAttr = SysAttribute.responsible(userClass);
        DSLAttribute.setEditFilter(responsibleAttr, responsibleScriptInfo);

        Attribute responsibleTreeAttr = SysAttribute.responsible(userCaseTree);
        DAOAttribute.changeToEditFilter(responsibleTreeAttr, responsibleScriptInfo);
        responsibleTreeAttr.setEditPresentation(TREE_EDIT);
        Attribute responsibleTreeFoldersAttr = SysAttribute.responsible(userCaseTreeFolders);
        DAOAttribute.changeToEditFilter(responsibleTreeFoldersAttr, responsibleScriptInfo);
        responsibleTreeFoldersAttr.setEditPresentation(TREE_EDIT_WITH_FOLDER);
        DSLAttribute.edit(responsibleTreeAttr, responsibleTreeFoldersAttr);

        addForm = DAOMobile.createMobileAddForm(userClass);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, responsibleAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    @AfterClass
    public static void cleanUp()
    {
        responsibleAttr.setFilteredByScript(Boolean.FALSE.toString());
        DSLAttribute.edit(responsibleAttr);

        Attribute responsibleTreeAttr = SysAttribute.responsible(userCaseTree);
        responsibleTreeAttr.setFilteredByScript(Boolean.FALSE.toString());
        DSLAttribute.edit(responsibleTreeAttr);

        Attribute responsibleTreeFoldersAttr = SysAttribute.responsible(userCaseTreeFolders);
        responsibleTreeFoldersAttr.setFilteredByScript(Boolean.FALSE.toString());
        DSLAttribute.edit(responsibleTreeFoldersAttr);
    }

    /**
     * Тестирование получения возможных значений атрибута Ответственный.
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Ответственный",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из: пустого элемента,
     * команд team, team2 и первых 17 команд из teams, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее только из команд team,
     * team2 и первых 18 команд из teams, уровень дерева возвращён не полностью = true</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Ответственный",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = team,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из сотрудников employee,
     * employee2</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах атрибут "Ответственный" имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах атрибут "Ответственный" имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testResponsible()
    {
        for (MetaClass metaClass : List.of(userCase, userCaseTree, userCaseTreeFolders))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(metaClass)
                    .setAttribute(responsibleAttr, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm);
            // получаем дерево для старой версии: должен вернуться пустой элемент
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, responsibleAttr)
                    .assertValues(
                            tree(
                                    emptyTreeElement(),
                                    treeElement(team).leaf(false),
                                    treeElement(team2).leaf(false)
                            ).with(
                                    Arrays.stream(teams)
                                            .limit(17)
                                            .map(team3 -> treeElement(team3).leaf(false))
                                            .toArray(BaseTreePossibleValueValidator[]::new)
                            ).hasMore(true)
                    );

            // получаем дерево для v15: пустой элемент должен отсутствовать, должна вернуться папка,
            // а также цепочка идентификаторов, однозначно определяющая выбираемое значение
            ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

            pvResponse2.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse2, responsibleAttr, V15)
                    .assertValues(
                            tree(
                                    aggregatedFolderElement(teamFolder),
                                    aggregatedTreeElement(team2).leaf(false)
                            ).with(
                                    Arrays.stream(teams)
                                            .limit(18)
                                            .map(team3 -> aggregatedTreeElement(team3).leaf(false))
                                            .toArray(BaseTreePossibleValueValidator[]::new)
                            ).hasMore(true)
                    );

            MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm)
                    .setParent(teamFolder);

            // получаем дочерние элементы для папки для v15: должна возвращаться цепочка идентификаторов,
            // однозначно определяющая выбираемое значение
            ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

            pvResponse3.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse3, responsibleAttr, V15)
                    .assertValues(
                            aggregatedTreeElement(team).leaf(false)
                    );

            MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm)
                    .setParent(team);

            // получаемые дочерние элементы должно быть идентичны
            for (MobileVersion version : List.of(V13_1, V15))
            {
                ValidatableResponse pvResponse4 =
                        DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, version);

                pvResponse4.statusCode(HttpStatus.SC_OK);
                DSLMobilePossibleValues.assertForAttribute(pvResponse4, responsibleAttr, version)
                        .assertValues(
                                aggregatedTreeElement(team, employee),
                                aggregatedTreeElement(team, employee2)
                        );
            }

            ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

            formResponse.statusCode(HttpStatus.SC_OK);
            DSLMobileForms.assertForForm(formResponse, false)
                    .assertForAttribute(responsibleAttr)
                    .hasEditPresentationSelectType(null);

            ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

            formResponse2.statusCode(HttpStatus.SC_OK);
            DSLMobileForms.assertForForm(formResponse2, false)
                    .assertForAttribute(responsibleAttr)
                    .hasEditPresentationSelectType(AGGREGATED_TREE);
        }
    }

    /**
     * Тестирование поиска среди возможных значений атрибута Ответственный.
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Ответственный"</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из: пустого элемента и сотрудника employee2, вложенного
     * в команду team</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее только из сотрудника employee2, вложенного в команду
     * team</li>
     * </ol>
     */
    @Test
    public void testResponsibleWhenPerformsSearch()
    {
        for (MetaClass metaClass : List.of(userCase, userCaseTree, userCaseTreeFolders))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(metaClass)
                    .setAttribute(responsibleAttr, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm)
                    .setSearchString(employee2.getTitle());
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, responsibleAttr)
                    .assertValues(
                            treeSearch(
                                    treeElement(team).children(
                                            treeElement(employee2)
                                    )
                            ).foundAmount(1)
                    );

            ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

            pvResponse2.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse2, responsibleAttr, V15)
                    .assertValues(
                            treeSearch(
                                    aggregatedFolderElement(teamFolder).children(
                                            aggregatedTreeElement(team).children(
                                                    aggregatedTreeElement(team, employee2)
                                            )
                                    )
                            ).foundAmount(1)
                    );
        }
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для атрибута Ответственный.
     * Пустой элемент должен возвращаться до версии 15 мобильного API.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = "Ответственный",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из последних 13
     * команд teams</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из последних 12
     * команд teams</li>
     * </ol>
     */
    @Test
    public void testResponsibleWhenOffsetMoreThenZero()
    {
        for (MetaClass metaClass : List.of(userCase, userCaseTree, userCaseTreeFolders))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(metaClass)
                    .setAttribute(responsibleAttr, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm)
                    .setOffset(10);
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, responsibleAttr)
                    .assertValues(
                            Arrays.stream(teams)
                                    .skip(7)
                                    .map(team3 -> treeElement(team3).leaf(false))
                                    .toArray(BaseTreePossibleValueValidator[]::new)
                    );

            ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

            pvResponse2.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse2, responsibleAttr, V15)
                    .assertValues(
                            Arrays.stream(teams)
                                    .skip(8)
                                    .map(team3 -> aggregatedTreeElement(team3).leaf(false))
                                    .toArray(BaseTreePossibleValueValidator[]::new)
                    );
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута Ответственный. Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = "Ответственный",</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название employee2,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase или userCaseTree или userCaseTreeFolders,</li>
     *         <li>"Ответственный" = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testResponsibleWhenPerformsSearchAndOffsetMoreThenZero()
    {
        for (MetaClass metaClass : List.of(userCase, userCaseTree, userCaseTreeFolders))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(metaClass)
                    .setAttribute(responsibleAttr, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(responsibleAttr, objectTemplate)
                    .setForm(addForm)
                    .setOffset(1)
                    .setSearchString(employee2.getTitle());
            for (MobileVersion version : List.of(V13_1, V15))
            {
                ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
                pvResponse.statusCode(HttpStatus.SC_OK);
                DSLMobilePossibleValues.assertForAttribute(pvResponse, responsibleAttr, version)
                        .assertValues(treeSearch());
            }
        }
    }
}
