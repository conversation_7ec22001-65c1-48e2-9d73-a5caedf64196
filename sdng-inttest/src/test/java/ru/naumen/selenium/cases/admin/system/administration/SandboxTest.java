package ru.naumen.selenium.cases.admin.system.administration;

import org.junit.Test;

import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тесты параметров groovy.enableSandbox и groovy.enableSandboxForScripts
 *
 * <AUTHOR>
 * @since 04 апр. 2025
 */
public class SandboxTest extends AbstractTestCase
{

    /**
     * Тестирование запрета использования класса groovy.lang.Script и методов GroovyScriptEngine#run,
     * GroovyScriptEngineImpl#eval в скриптах
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$298619917
     * <br>
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипты
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     import groovy.lang.Binding;
     *     import groovy.util.GroovyScriptEngine;
     *     String[] paths = { "" };
     *     GroovyScriptEngine gse = new GroovyScriptEngine(paths);
     *     Binding binding = new Binding();
     *     String result = (String) gse.run("path", binding);
     *     -------------------------------------------------------------------------------
     *     </pre>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     import org.codehaus.groovy.jsr223.GroovyScriptEngineFactory;
     *     def gsh = new GroovyScriptEngineFactory().getScriptEngine();
     *     gsh.eval("return true");
     *     -------------------------------------------------------------------------------
     *     </pre>
     *     <pre>
     *     -------------------------------------------------------------------------------
     *      import groovy.lang.Script;
     *      public class MyScript extends Script
     *      {
     *          @Override
     *          public Object run()
     *          {
     *              return true;
     *          }
     *       }
     *      def d = new MyScript();
     *      d.run();
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Проверить, что при выполнении каждого скрипта возникло исключение SandboxSecurityException</li>
     * </ol>
     */
    @Test
    public void testSandboxForScriptsParamEnable()
    {
        String expectedException = "SandboxSecurityException";
        String script1 = """
                import groovy.lang.Binding;
                import groovy.util.GroovyScriptEngine;
                String[] paths = { "" };
                GroovyScriptEngine gse = new GroovyScriptEngine(paths);
                Binding binding = new Binding();
                String result = (String) gse.run("path", binding);
                """;
        new ScriptRunner(script1).assertScriptError(expectedException);

        String script2 = """
                import org.codehaus.groovy.jsr223.GroovyScriptEngineFactory;
                def gsh = new GroovyScriptEngineFactory().getScriptEngine();
                gsh.eval("return true");
                """;
        new ScriptRunner(script2).assertScriptError(expectedException);

        String script3 = """
                import groovy.lang.Script;
                public class MyScript extends Script
                {
                    @Override
                    public Object run()
                    {
                        return true;
                    }
                }
                def d = new MyScript();
                d.run();
                """;
        new ScriptRunner(script3).assertScriptError(expectedException);
    }
}