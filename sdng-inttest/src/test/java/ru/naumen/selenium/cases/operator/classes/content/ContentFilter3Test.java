package ru.naumen.selenium.cases.operator.classes.content;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование фильтрации в контентах
 * <AUTHOR>
 * @since 07.03.2025
 */
class ContentFilter3Test extends AbstractTestCaseJ5
{
    private static MetaClass employeeClass, employeeCase;
    private static Attribute boLinksAttrUserClass, boLinksAttrEmployee, objectLinkAttrEmployee;
    private static ContentForm objectListEmployee;
    private static Bo userBo1, userBo2, employee1, employee2, employee3;

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>В ИА создать класс UserClass и в нем тип userCase</li>
     * <li>В классе UserClass создать атрибут:</li>
     * <ul>
     *     <li>Тип - Набор ссылок на БО</li>
     *     <li>Название / код - boLinksAttrUserClass</li>
     *     <li>Класс объекта - UserClass</li>
     * </ul>
     * <li>Добавить атрибут boLinksAttrUserClass в Системную группу атрибутов</li>
     * <li>В классе Сотрудник создать атрибут:</li>
     * <ul>
     *     <li>Тип - Набор ссылок на БО</li>
     *     <li>Название / код - boLinksAttrEmployee</li>
     *     <li>Класс объекта - UserClass</li>
     * </ul>
     * <li>В классе Сотрудник создать атрибут:</li>
     * <ul>
     *     <li>Тип - Ссылка на БО</li>
     *     <li>Название / код - objectLinkAttrEmployee</li>
     *     <li>Класс объекта - UserClass</li>
     * </ul>
     * <li>Добавить атрибуты objectLinkAttrEmployee и boLinksAttrEmployee в Системную группу атрибутов</li>
     * <li>Вывести на карточку класса Компания контент:</li>
     * <ul>
     *     <li>Тип - Список объектов</li>
     *     <li>Название / код - objectListUserClass</li>
     *     <li>Класс объектов - UserClass</li>
     *     <li>Группа атрибутов - Системная группа</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Вывести на карточку класса UserClass контент:</li>
     * <ul>
     *     <li>Тип - Список объектов</li>
     *     <li>Название / код - objectListEmployee</li>
     *     <li>Класс объектов - Сотрудник</li>
     *     <li>Группа атрибутов - Системная группа</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Создать два объекта класса UserClass:</li>
     * <ul>
     *     <li>userBo1</li>
     *     <li>userBo2</li>
     * </ul>
     * <li>Создать трех лицензированных сотрудников со всеми правами, заполнить атрибуты objectLinkAttrEmployee и
     * boLinksAttrEmployee:</li>
     * <ul>
     *     <li>objectLinkAttrEmployee:</li>
     *     <ul>
     *         <li>Employee1 - userBo1</li>
     *         <li>Employee2 - userBo2</li>
     *         <li>Employee3 - -</li>
     *     </ul>
     *     <li>boLinksAttrEmployee:</li>
     *     <ul>
     *         <li>Employee1 - userBo1, userBo2</li>
     *         <li>Employee2 - userBo2</li>
     *         <li>Employee3 - -</li>
     *     </ul>
     * </ul>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        employeeClass = DAOEmployeeCase.createClass();
        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(userClass, userCase, employeeCase);

        boLinksAttrUserClass = DAOAttribute.createBoLinks(userClass.getFqn(), userClass);
        GroupAttr systemGroupUserClass = DAOGroupAttr.createSystem(userClass);
        DSLAttribute.add(boLinksAttrUserClass);
        DSLGroupAttr.addToGroup(systemGroupUserClass, boLinksAttrUserClass);

        boLinksAttrEmployee = DAOAttribute.createBoLinks(employeeCase, userClass);
        objectLinkAttrEmployee = DAOAttribute.createObjectLink(employeeCase, userClass);

        GroupAttr employeeSystemGroup = DAOGroupAttr.createSystem(employeeCase);
        DSLAttribute.add(boLinksAttrEmployee, objectLinkAttrEmployee);
        DSLGroupAttr.addToGroup(employeeSystemGroup, boLinksAttrEmployee, objectLinkAttrEmployee);

        MetaClass rootClass = DAORootClass.create();
        ContentForm objectListUserClass = DAOContentCard.createObjectAdvList(rootClass.getFqn(), userClass);
        objectListEmployee = DAOContentCard.createObjectAdvList(userClass.getFqn(), employeeClass);
        DSLContent.add(objectListUserClass, objectListEmployee);

        userBo1 = DAOUserBo.create(userCase);
        userBo2 = DAOUserBo.create(userCase);
        DSLBo.add(userBo1, userBo2);

        Bo ou = SharedFixture.ou();
        employee1 = DAOEmployee.create(employeeCase, ou, true, true);
        employee2 = DAOEmployee.create(employeeCase, ou, true, true);
        employee3 = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee1, employee2, employee3);

        objectLinkAttrEmployee.setBoValue(userBo1);
        boLinksAttrEmployee.setBoValue(userBo1, userBo2);
        DSLBo.editAttributeValue(employee1, objectLinkAttrEmployee, boLinksAttrEmployee);
        objectLinkAttrEmployee.setBoValue(userBo2);
        boLinksAttrEmployee.setBoValue(userBo2);
        DSLBo.editAttributeValue(employee2, objectLinkAttrEmployee, boLinksAttrEmployee);
    }

    /**
     * Тестирование фильтрации списка по критерию "Содержит атрибут текущего объекта" с параметром "Игнорировать,
     * если атрибут пуст" = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass у контента objectListEmployee открыть форму "Настройка ограничения содержимого
     * списка":</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttrEmployee</li>
     *     <li>Во втором поле выбрать критерий фильтрации - "Содержит атрибут текущего объекта"</li>
     *     <li>В третьем поле выбрать атрибут - boLinksAttrUserClass</li>
     *     <li>Установить параметр "Игнорировать, если атрибут пуст" = true</li>
     * </ul>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBo1</li>
     * <li>Проверить, что на контенте objectListEmployee отображается три объекта - Employee1, Employee2 и
     * Employee3</li>
     * <li>Открыть форму редактирования контента "Основные параметры", выставить атрибуту boLinksAttrUserClass
     * значение - userBo2</li>
     * <li>Сохранить настройки</li>
     * <li>Проверить, что на контенте objectListEmployee отображается два объекта - Employee1 и Employee2</li>
     * </ol>
     */
    @Test
    void testFilterListByContainsAttrIgnoreEmpty()
    {
        //Подготовка
        objectListEmployee.setObjectListFilter(
                new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttrEmployee, true,
                        FilterCondition.CONTAINS_SUBJECT_ATTRIBUTE, false, boLinksAttrUserClass.getFqn()))));
        DSLContent.edit(objectListEmployee);

        //Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBo1);
        objectListEmployee.advlist().content().asserts().rowsPresence(employee1, employee2, employee3);
        boLinksAttrUserClass.setBoValue(userBo2);
        DSLBo.editAttributeValue(userBo1, boLinksAttrUserClass);
        Cleaner.afterTest(true, () ->
        {
            boLinksAttrUserClass.setBoValue();
            DSLBo.editAttributeValue(userBo1, boLinksAttrUserClass);
        });
        tester.refresh();
        objectListEmployee.advlist().content().asserts().rowsPresence(employee1, employee2);
        objectListEmployee.advlist().content().asserts().rowsAbsence(employee3);
    }

    /**
     * Тестирование фильтрации списка по критерию "Равно атрибуту текущего объекта" с параметром "Игнорировать, если
     * атрибут пуст" = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass у контента objectListEmployee открыть форму "Настройка ограничения содержимого
     * списка":</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     <li>Во втором поле выбрать критерий фильтрации - "Равно атрибуту текущего объекта"</li>
     *     <li>В третьем поле выбрать атрибут - boLinksAttrUserClass</li>
     *     <li>Установить параметр "Игнорировать, если атрибут пуст" = true</li>
     * </ul>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBo1</li>
     * <li>Проверить, что на контенте objectListEmployee отображается три объекта - Employee1, Employee2 и
     * Employee3</li>
     * <li>Открыть форму редактирования контента "Основные параметры", выставить атрибуту boLinksAttrUserClass
     * значение - userBo2</li>
     * <li>Сохранить настройки</li>
     * <li>Проверить, что на контенте objectListEmployee отображается один объект - Employee2</li>
     * </ol>
     */
    @Test
    void testFilterByEqualsAttrIgnoreEmpty()
    {
        //Подготовка
        objectListEmployee.setObjectListFilter(
                new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttrEmployee, true,
                        FilterCondition.EQUALS_SUBJECT_ATTRIBUTE, false, boLinksAttrUserClass.getFqn()))));
        DSLContent.edit(objectListEmployee);

        //Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBo1);
        objectListEmployee.advlist().content().asserts().rowsPresence(employee1, employee2, employee3);
        boLinksAttrUserClass.setBoValue(userBo2);
        DSLBo.editAttributeValue(userBo1, boLinksAttrUserClass);

        Cleaner.afterTest(true, () ->
        {
            boLinksAttrUserClass.setBoValue();
            DSLBo.editAttributeValue(userBo1, boLinksAttrUserClass);
        });

        tester.refresh();
        GUIBo.assertThatBoCard(userBo1);
        objectListEmployee.advlist().content().asserts().rowsPresence(employee2);
        objectListEmployee.advlist().content().asserts().rowsAbsence(employee1, employee3);
    }

    /**
     * Тестирование настройки фильтрации в структуре по критерию “Равно атрибуту текущего пользователя” с параметром
     * "Игнорировать, если атрибут пуст" = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - Employee</li>
     *     <li>Объекты - Сотрудник</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>Открыть форму "Настройки ограничения содержимого элемента":</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     <li>Во втором поле выбрать критерий фильтрации - "Равно атрибуту текущего пользователя"</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     <li>Установить параметр "Игнорировать, если атрибут пуст" = true</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <li>Вывести на карточку класса Сотрудник контент:</li>
     * <ul>
     *     <li>Тип - Иерархическое дерево</li>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     * </ul>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под сотрудником Employee3</li>
     * <li>Проверить, что на контенте HierarchicalTree отображается три объекта - Employee1, Employee2 и Employee3</li>
     * <li>Войти в систему под сотрудником Employee1</li>
     * <li>Проверить, что на контенте HierarchicalTree отображается один объект - Employee1</li>
     * </ol>
     */
    @Test
    void testStructureFilterUserAttrEqualsSkipEmpty()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem employeeItem = DAOStructuredObjectsView.createItem(null, employeeCase,
                null, DAOGroupAttr.createSystem(employeeCase), false, false);
        employeeItem.setDefaultFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttrEmployee, true,
                FilterCondition.EQUALS_USER_ATTRIBUTE, false, objectLinkAttrEmployee.getFqn()))));
        structure.setItems(employeeItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchicalTree = DAOContentCard.createHierarchyGrid(employeeCase, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchicalTree);

        // Действия и проверки
        GUILogon.login(employee3);
        GUIHierarchyGrid.assertRows(hierarchicalTree, employeeItem, Lists.newArrayList(employee1, employee2, employee3),
                false, true);

        GUILogon.login(employee1);
        GUIHierarchyGrid.assertRows(hierarchicalTree, employeeItem, Lists.newArrayList(employee1), false, true);
    }

    /**
     * Тестирование настройки фильтрации в структуре по критерию "Содержит атрибут текущего пользователя" с
     * параметром "Игнорировать, если атрибут пуст" = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - Employee</li>
     *     <li>Объекты - Сотрудник</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>Открыть форму "Настройки ограничения содержимого элемента":</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttrEmployee</li>
     *     <li>Во втором поле выбрать критерий фильтрации - "Содержит атрибут текущего пользователя"</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrEmployee</li>
     *     <li>Установить параметр "Игнорировать, если атрибут пуст" = true</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <li>Вывести на карточку класса Сотрудник контент:</li>
     * <ul>
     *     <li>Тип - Иерархическое дерево</li>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     * </ul>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти в систему под сотрудником Employee1</li>
     * <li>Проверить, что на контенте HierarchicalTree отображается один объект - Employee1</li>
     * <li>Войти в систему под сотрудником Employee2</li>
     * <li>Проверить, что на контенте HierarchicalTree отображается два объекта - Employee1 и Employee2</li>
     * <li>Войти в систему под сотрудником Employee3</li>
     * <li>Проверить, что на контенте HierarchicalTree отображается три объекта - Employee1, Employee2 и Employee3</li>
     * </ol>
     */
    @Test
    void testUserAttrFilterEqualsIgnoreEmpty()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem employeeItem = DAOStructuredObjectsView.createItem(null, employeeCase,
                null, DAOGroupAttr.createSystem(employeeCase), false, false);
        employeeItem.setDefaultFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttrEmployee, true,
                FilterCondition.CONTAINS_USER_ATTRIBUTE, false, objectLinkAttrEmployee.getFqn()))));
        structure.setItems(employeeItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchicalTree = DAOContentCard.createHierarchyGrid(employeeCase, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchicalTree);

        // Действия и проверки
        GUILogon.login(employee1);
        GUIHierarchyGrid.assertRows(hierarchicalTree, employeeItem, Lists.newArrayList(employee1), false, true);

        GUILogon.login(employee2);
        GUIHierarchyGrid.assertRows(hierarchicalTree, employeeItem, Lists.newArrayList(employee1, employee2), false,
                true);

        GUILogon.login(employee3);
        GUIHierarchyGrid.assertRows(hierarchicalTree, employeeItem, Lists.newArrayList(employee1, employee2, employee3),
                false, true);
    }
}
