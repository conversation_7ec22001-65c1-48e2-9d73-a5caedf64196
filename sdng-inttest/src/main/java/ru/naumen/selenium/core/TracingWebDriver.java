package ru.naumen.selenium.core;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.openqa.selenium.Alert;
import org.openqa.selenium.By;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.Point;
import org.openqa.selenium.Rectangle;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Coordinates;
import org.openqa.selenium.interactions.HasInputDevices;
import org.openqa.selenium.interactions.HasTouchScreen;
import org.openqa.selenium.interactions.Keyboard;
import org.openqa.selenium.interactions.Locatable;
import org.openqa.selenium.interactions.Mouse;
import org.openqa.selenium.interactions.TouchScreen;
import org.openqa.selenium.internal.BuildInfo;
import org.openqa.selenium.internal.WrapsDriver;
import org.openqa.selenium.internal.WrapsElement;
import org.openqa.selenium.logging.LogEntry;
import org.openqa.selenium.logging.Logs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.util.MetricUtils;

/**
 * Обертка для {@link WebDriver} с логированием действий, совершенных WebDriver-ом
 * <AUTHOR>
 * @since 16.12.2013
 */
@SuppressFBWarnings("CRLF_INJECTION_LOGS")
public class TracingWebDriver
        implements WebDriver, JavascriptExecutor, TakesScreenshot, WrapsDriver, HasInputDevices, HasTouchScreen
{

    public static class TracingKeyboard implements Keyboard
    {

        private final WebDriver driver;
        private final Keyboard keyboard;

        public TracingKeyboard(WebDriver driver)
        {
            this.driver = driver;
            final Keyboard kb = ((HasInputDevices)this.driver).getKeyboard();
            this.keyboard = (Keyboard)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(),
                    extractInterfaces(kb), (Object proxy, Method method, Object[] args) ->
                    {
                        long startTime = System.currentTimeMillis();

                        String m = method.getName();

                        if (args == null || args.length == 0)
                        {
                            LOG.debug("keyboard:" + m + "()");
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug("keyboard:" + m + "({})", args[0]);
                            }
                            else
                            {
                                LOG.debug("keyboard:" + m + "({}, ...)", args[0]);
                            }
                        }
                        try
                        {
                            Object result = method.invoke(kb, args);
                            LOG.debug("keyboard:" + m + "(...): OK", result); // NOPMD
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            return result;
                        }
                        catch (InvocationTargetException e)
                        {
                            LOG.debug("keyboard:" + m + "(...)", e.getTargetException());
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            throw e.getTargetException();
                        }
                    });
        }

        @Override
        public void pressKey(CharSequence keyToPress)
        {
            keyboard.pressKey(keyToPress);
        }

        @Override
        public void releaseKey(CharSequence keyToRelease)
        {
            keyboard.releaseKey(keyToRelease);
        }

        @Override
        public void sendKeys(CharSequence... keysToSend)
        {
            keyboard.sendKeys(keysToSend);
        }
    }

    //
    public static class TracingMouse implements Mouse
    {
        private final WebDriver driver;
        private final Mouse mouse;

        public TracingMouse(WebDriver driver)
        {
            this.driver = driver;
            final Mouse ms = ((HasInputDevices)this.driver).getMouse();
            this.mouse = (Mouse)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(), extractInterfaces(ms),
                    (Object proxy, Method method, Object[] args) ->
                    {
                        long startTime = System.currentTimeMillis();

                        String m = method.getName();

                        if (args == null || args.length == 0)
                        {
                            LOG.debug("mouse:" + m + "()");
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug("mouse:" + m + "({})", args[0]);
                            }
                            else
                            {
                                LOG.debug("mouse:" + m + "({}, ...)", args[0]);
                            }
                        }
                        try
                        {
                            Object result = method.invoke(ms, args);
                            LOG.debug("mouse:" + m + "(...): OK", result); // NOPMD
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            return result;
                        }
                        catch (InvocationTargetException e)
                        {
                            LOG.debug("mouse:" + m + "(...)", e.getTargetException());
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            throw e.getTargetException();
                        }
                    });
        }

        @Override
        public void click(Coordinates where)
        {
            mouse.click(where);
        }

        @Override
        public void contextClick(Coordinates where)
        {
            mouse.contextClick(where);
        }

        @Override
        public void doubleClick(Coordinates where)
        {
            mouse.doubleClick(where);
        }

        @Override
        public void mouseDown(Coordinates where)
        {
            mouse.mouseDown(where);
        }

        @Override
        public void mouseMove(Coordinates where)
        {
            mouse.mouseMove(where);
        }

        @Override
        public void mouseMove(Coordinates where, long xOffset, long yOffset)
        {
            mouse.mouseMove(where, xOffset, yOffset);
        }

        @Override
        public void mouseUp(Coordinates where)
        {
            mouse.mouseUp(where);
        }
    }

    public static class TracingTouch implements TouchScreen
    {

        private final WebDriver driver;
        private final TouchScreen touchScreen;

        @SuppressFBWarnings("BC_UNCONFIRMED_CAST")
        public TracingTouch(WebDriver driver)
        {
            this.driver = driver;
            final TouchScreen ts = ((HasTouchScreen)this.driver).getTouch();
            this.touchScreen = (TouchScreen)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(),
                    extractInterfaces(ts), (Object proxy, Method method, Object[] args) ->
                    {
                        long startTime = System.currentTimeMillis();

                        String m = method.getName();

                        if (args == null || args.length == 0)
                        {
                            LOG.debug("touch:" + m + "()");
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug("touch:" + m + "({})", args[0]);
                            }
                            else
                            {
                                LOG.debug("touch:" + m + "({}, ...)", args[0]);
                            }
                        }
                        try
                        {
                            Object result = method.invoke(ts, args);
                            LOG.debug("touch:" + m + "(...): OK", result); // NOPMD
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            return result;
                        }
                        catch (InvocationTargetException e)
                        {
                            LOG.debug("touch:" + m + "(...)", e.getTargetException());
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            throw e.getTargetException();
                        }
                    });
        }

        @Override
        public void doubleTap(Coordinates where)
        {
            touchScreen.doubleTap(where);
        }

        @Override
        public void down(int x, int y)
        {
            touchScreen.down(x, y);
        }

        @Override
        public void flick(int xSpeed, int ySpeed)
        {
            touchScreen.flick(xSpeed, ySpeed);
        }

        @Override
        public void flick(Coordinates where, int xOffset, int yOffset, int speed)
        {
            touchScreen.flick(where, xOffset, yOffset, speed);
        }

        @Override
        public void longPress(Coordinates where)
        {
            touchScreen.longPress(where);
        }

        @Override
        public void move(int x, int y)
        {
            touchScreen.move(x, y);
        }

        @Override
        public void scroll(int xOffset, int yOffset)
        {
            touchScreen.scroll(xOffset, yOffset);
        }

        @Override
        public void scroll(Coordinates where, int xOffset, int yOffset)
        {
            touchScreen.scroll(where, xOffset, yOffset);
        }

        @Override
        public void singleTap(Coordinates where)
        {
            touchScreen.singleTap(where);
        }

        @Override
        public void up(int x, int y)
        {
            touchScreen.up(x, y);
        }
    }

    private static class TracingNavigation implements Navigation
    {

        private final WebDriver.Navigation navigation;

        TracingNavigation(Navigation navigation)
        {
            this.navigation = navigation;
        }

        @Override
        public void back()
        {
            long startTime = System.currentTimeMillis();
            navigation.back();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void forward()
        {
            long startTime = System.currentTimeMillis();
            navigation.forward();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void refresh()
        {
            long startTime = System.currentTimeMillis();
            navigation.refresh();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void to(String url)
        {
            long startTime = System.currentTimeMillis();
            navigation.to(url);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void to(URL url)
        {
            to(String.valueOf(url));
        }
    }

    private static class TracingOptions implements Options
    {

        private Options options;

        private TracingOptions(Options options)
        {
            this.options = options;
        }

        @Override
        public void addCookie(Cookie cookie)
        {
            long startTime = System.currentTimeMillis();
            options.addCookie(cookie);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void deleteAllCookies()
        {
            long startTime = System.currentTimeMillis();
            options.deleteAllCookies();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void deleteCookie(Cookie cookie)
        {
            long startTime = System.currentTimeMillis();
            options.deleteCookie(cookie);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public void deleteCookieNamed(String name)
        {
            long startTime = System.currentTimeMillis();
            options.deleteCookieNamed(name);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
        }

        @Override
        public Cookie getCookieNamed(String name)
        {
            long startTime = System.currentTimeMillis();
            Cookie result = options.getCookieNamed(name);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return result;
        }

        @Override
        public Set<Cookie> getCookies()
        {
            long startTime = System.currentTimeMillis();
            Set<Cookie> result = options.getCookies();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return result;
        }

        @Override
        public ImeHandler ime()
        {
            throw new UnsupportedOperationException("Driver does not support IME interactions");
        }

        @Override
        public Logs logs()
        {
            long startTime = System.currentTimeMillis();
            Logs result = options.logs();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return result;
        }

        @Override
        public Timeouts timeouts()
        {
            return new TracingTimeouts(options.timeouts());
        }

        @Override
        public Window window()
        {
            long startTime = System.currentTimeMillis();
            Window result = options.window();
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }

    private class TracingTargetLocator implements TargetLocator
    {

        private final TargetLocator targetLocator;

        private TracingTargetLocator(final TargetLocator targetLocator)
        {
            this.targetLocator = (TargetLocator)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(),
                    extractInterfaces(targetLocator), (proxy, method, args) ->
                    {
                        long startTime = System.currentTimeMillis();

                        String m = method.getName();

                        if (args == null || args.length == 0)
                        {
                            LOG.debug("switchTo()." + m + "()");
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug("switchTo()." + m + "({})", args[0]);
                            }
                            else
                            {
                                LOG.debug("switchTo()." + m + "({}, ...)", args[0]);
                            }
                        }
                        try
                        {
                            Object result = method.invoke(targetLocator, args);
                            LOG.debug("switchTo()." + m + "(...): OK", result); // NOPMD
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            return result;
                        }
                        catch (InvocationTargetException e)
                        {
                            LOG.debug("switchTo()." + m + "(...)", e.getTargetException());
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            throw e.getTargetException();
                        }
                    });
        }

        @Override
        public WebElement activeElement()
        {
            return new TracingWebElement(targetLocator.activeElement());
        }

        @Override
        public Alert alert()
        {
            return targetLocator.alert();
        }

        @Override
        public WebDriver defaultContent()
        {
            return targetLocator.defaultContent();
        }

        @Override
        public WebDriver frame(int frameIndex)
        {
            return targetLocator.frame(frameIndex);
        }

        @Override
        public WebDriver frame(String frameName)
        {
            return targetLocator.frame(frameName);
        }

        @Override
        public WebDriver frame(WebElement frameElement)
        {
            return targetLocator.frame(frameElement);
        }

        @Override
        public WebDriver parentFrame()
        {
            return targetLocator.parentFrame();
        }

        @Override
        public WebDriver window(String windowName)
        {
            return targetLocator.window(windowName);
        }
    }

    private static class TracingTimeouts implements Timeouts
    {

        private final Timeouts timeouts;

        TracingTimeouts(Timeouts timeouts)
        {
            this.timeouts = timeouts;
        }

        @Override
        public Timeouts implicitlyWait(long time, TimeUnit unit)
        {
            long startTime = System.currentTimeMillis();
            timeouts.implicitlyWait(time, unit);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return this;
        }

        @Override
        public Timeouts pageLoadTimeout(long time, TimeUnit unit)
        {
            long startTime = System.currentTimeMillis();
            timeouts.pageLoadTimeout(time, unit);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return null;
        }

        @Override
        public Timeouts setScriptTimeout(long time, TimeUnit unit)
        {
            long startTime = System.currentTimeMillis();
            timeouts.setScriptTimeout(time, unit);
            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
            return this;
        }
    }

    private class TracingWebElement implements WebElement, WrapsElement, WrapsDriver, Locatable
    {
        private final WebElement element;
        private final WebElement underlyingElement;

        private TracingWebElement(final WebElement element)
        {
            this.element = (WebElement)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(),
                    extractInterfaces(element), (proxy, method, args) ->
                    {
                        long startTime = System.currentTimeMillis();

                        String m = method.getName();
                        if ("getWrappedElement".equals(m))
                        {
                            return element;
                        }

                        if (args == null || args.length == 0)
                        {
                            LOG.debug("[{}]." + m + "()", element);
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug("[{}]." + m + "({})", element, args[0]);
                            }
                            else
                            {
                                LOG.debug("[{}]." + m + "({}, ...)", element, args[0]);
                            }
                        }
                        //TODOAT geckodriver не поддерживает logging API, поскольку это пока не входит в спецификацию
                        // w3c
                        //"UnsupportedCommandException: POST /session/<sessionid>/log"
                        //https://github.com/SeleniumHQ/selenium/issues/2910
                        //https://github.com/w3c/webdriver/issues/406
                        //https://bugzilla.mozilla.org/show_bug.cgi?id=1453962
                        if (Config.isChrome())
                        {
                            writeToBrowserLog(driver);
                        }
                        try
                        {
                            Object result = method.invoke(element, args);
                            LOG.debug("[{}]." + m + "(...) = {}", element, result);
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            return result;
                        }
                        catch (InvocationTargetException e)
                        {
                            LOG.debug("[{}]." + m + "(...)", element, e.getTargetException());
                            MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                            throw e.getTargetException();
                        }
                    });
            this.underlyingElement = element;
        }

        @Override
        public void clear()
        {
            element.clear();
        }

        @Override
        public void click()
        {
            element.click();
        }

        @Override
        public boolean equals(Object obj)
        {
            if (!(obj instanceof WebElement))
            {
                return false;
            }

            WebElement other = (WebElement)obj;
            if (other instanceof WrapsElement)
            {
                other = ((WrapsElement)other).getWrappedElement();
            }

            return underlyingElement.equals(other);
        }

        @SuppressWarnings("unchecked")
        @Override
        public WebElement findElement(By by)
        {
            return createWebElement(element.findElement(by));
        }

        @Override
        public List<WebElement> findElements(By by)
        {
            List<WebElement> temp = element.findElements(by);
            List<WebElement> result = new ArrayList<>(temp.size());
            for (WebElement element : temp)
            {
                result.add(createWebElement(element));
            }
            return result;
        }

        @Override
        public String getAttribute(String name)
        {
            return element.getAttribute(name);
        }

        @Override
        public Coordinates getCoordinates()
        {
            return ((Locatable)element).getCoordinates();
        }

        @Override
        public String getCssValue(String propertyName)
        {
            return element.getCssValue(propertyName);
        }

        @Override
        public Point getLocation()
        {
            return element.getLocation();
        }

        @Override
        public Rectangle getRect()
        {
            return null;
        }

        @Override
        public <X> X getScreenshotAs(OutputType<X> arg0) throws WebDriverException
        {
            return null;
        }

        @Override
        public Dimension getSize()
        {
            return element.getSize();
        }

        @Override
        public String getTagName()
        {
            return element.getTagName();
        }

        @Override
        public String getText()
        {
            return element.getText();
        }

        @Override
        public WebDriver getWrappedDriver()
        {
            return driver;
        }

        @Override
        public WebElement getWrappedElement()
        {
            return underlyingElement;
        }

        @Override
        public int hashCode()
        {
            return underlyingElement.hashCode();
        }

        @Override
        public boolean isDisplayed()
        {
            return element.isDisplayed();
        }

        @Override
        public boolean isEnabled()
        {
            return element.isEnabled();
        }

        @Override
        public boolean isSelected()
        {
            return element.isSelected();
        }

        @Override
        public void sendKeys(CharSequence... keysToSend)
        {
            element.sendKeys(keysToSend);
        }

        @Override
        public void submit()
        {
            element.submit();
        }
    }

    private static Logger BROWSER_LOG = LoggerFactory.getLogger("Browser");
    private static Logger LOG = LoggerFactory.getLogger(TracingWebDriver.class);

    private static Class<?>[] extractInterfaces(Object object)
    {
        Set<Class<?>> allInterfaces = new HashSet<>();
        allInterfaces.add(WrapsDriver.class);
        if (object instanceof WebElement)
        {
            allInterfaces.add(WrapsElement.class);
        }
        extractInterfaces(allInterfaces, object.getClass());

        return allInterfaces.toArray(new Class<?>[allInterfaces.size()]);
    }

    private static void extractInterfaces(Set<Class<?>> addTo, Class<?> clazz)
    {
        if (Object.class.equals(clazz))
        {
            return; // Done
        }

        Class<?>[] classes = clazz.getInterfaces();
        addTo.addAll(Arrays.asList(classes));
        extractInterfaces(addTo, clazz.getSuperclass());
    }

    private static void writeToBrowserLog(WebDriver driver)
    {
        if (Config.get().isBrowserLogOn())
        {
            for (LogEntry logEntry : driver.manage().logs().get("browser").getAll())
            {
                BROWSER_LOG.debug("" + logEntry);
            }
        }
    }

    private final WebDriver driver;

    public TracingWebDriver(final WebDriver driver)
    {
        LOG.info("Init tracer for {}, driver {}", new BuildInfo(), driver.getClass().getName());
        Class<?>[] allInterfaces = extractInterfaces(driver);

        this.driver = (WebDriver)Proxy.newProxyInstance(TracingWebDriver.class.getClassLoader(), allInterfaces,
                (Object proxy, Method method, Object[] args) ->
                {
                    long startTime = System.currentTimeMillis();

                    String m = method.getName();
                    if ("getWrappedDriver".equals(m))
                    {
                        return driver;
                    }

                    if (!("manage".equals(m) || "switchTo".equals(m)))
                    {
                        if (args == null || args.length == 0)
                        {
                            LOG.debug(m + "()");
                        }
                        else
                        {
                            if (args.length == 1)
                            {
                                LOG.debug(m + "({})", args[0]);
                            }
                            else
                            {
                                LOG.debug(m + "({}, ...)", args[0]);
                            }
                        }
                    }
                    //TODOAT geckodriver не поддерживает logging API, поскольку это пока не входит в спецификацию w3c
                    //"UnsupportedCommandException: POST /session/<sessionid>/log"
                    //https://github.com/SeleniumHQ/selenium/issues/2910
                    //https://github.com/w3c/webdriver/issues/406
                    //https://bugzilla.mozilla.org/show_bug.cgi?id=1453962
                    if (Config.isChrome())
                    {
                        writeToBrowserLog(driver);
                    }
                    try
                    {
                        Object result = method.invoke(driver, args);
                        if ("getScreenshotAs".equals(m))
                        {
                            LOG.debug(m + "(...) = Screenshot taken");
                        }
                        else if (!("manage".equals(m)))
                        {
                            LOG.debug(m + "(...) = {}", result);
                        }
                        MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                        return result;
                    }
                    catch (InvocationTargetException e)
                    {
                        LOG.debug(m + "(...)", e.getTargetException().getMessage());
                        MetricUtils.addBrowserTime(System.currentTimeMillis() - startTime);
                        throw e.getTargetException();
                    }
                });
    }

    @Override
    public void close()
    {
        driver.close();
    }

    @Override
    public Object executeAsyncScript(String script, Object... args)
    {
        if (driver instanceof JavascriptExecutor)
        {
            Object[] usedArgs = unpackWrappedArgs(args);
            return ((JavascriptExecutor)driver).executeAsyncScript(script, usedArgs);
        }

        throw new UnsupportedOperationException("Underlying driver instance does not support executing javascript");
    }

    @Override
    public Object executeScript(String script, Object... args)
    {
        if (driver instanceof JavascriptExecutor)
        {
            Object[] usedArgs = unpackWrappedArgs(args);
            return ((JavascriptExecutor)driver).executeScript(script, usedArgs);
        }

        throw new UnsupportedOperationException("Underlying driver instance does not support executing javascript");
    }

    @SuppressWarnings("unchecked")
    @Override
    public WebElement findElement(By by)
    {
        return createWebElement(driver.findElement(by));
    }

    @Override
    public List<WebElement> findElements(By by)
    {
        List<WebElement> temp = driver.findElements(by);
        List<WebElement> result = new ArrayList<>(temp.size());
        for (WebElement element : temp)
        {
            result.add(createWebElement(element));
        }
        return result;
    }

    @Override
    public void get(String url)
    {
        driver.get(url);
    }

    @Override
    public String getCurrentUrl()
    {
        return driver.getCurrentUrl();
    }

    @Override
    public Keyboard getKeyboard()
    {
        if (driver instanceof HasInputDevices)
        {
            return new TracingKeyboard(driver);
        }
        else
        {
            throw new UnsupportedOperationException(
                    "Underlying driver does not implement advanced" + " user interactions yet.");
        }
    }

    @Override
    public Mouse getMouse()
    {
        if (driver instanceof HasInputDevices)
        {
            return new TracingMouse(driver);
        }
        else
        {
            throw new UnsupportedOperationException(
                    "Underlying driver does not implement advanced" + " user interactions yet.");
        }
    }

    @Override
    public String getPageSource()
    {
        return driver.getPageSource();
    }

    @Override
    public <X> X getScreenshotAs(OutputType<X> target) throws WebDriverException
    {
        if (driver instanceof TakesScreenshot)
        {
            return ((TakesScreenshot)driver).getScreenshotAs(target);
        }

        throw new UnsupportedOperationException("Underlying driver instance does not support taking screenshots");
    }

    @Override
    public String getTitle()
    {
        return driver.getTitle();
    }

    @Override
    public TouchScreen getTouch()
    {
        if (driver instanceof HasTouchScreen)
        {
            return new TracingTouch(driver);
        }
        else
        {
            throw new UnsupportedOperationException(
                    "Underlying driver does not implement advanced" + " user interactions yet.");
        }
    }

    @Override
    public String getWindowHandle()
    {
        return driver.getWindowHandle();
    }

    @Override
    public Set<String> getWindowHandles()
    {
        return driver.getWindowHandles();
    }

    @Override
    public WebDriver getWrappedDriver()
    {
        return driver;
    }

    @Override
    public Options manage()
    {
        return new TracingOptions(driver.manage());
    }

    @Override
    public Navigation navigate()
    {
        return new TracingNavigation(driver.navigate());
    }

    @Override
    public void quit()
    {
        driver.quit();
    }

    @Override
    public TargetLocator switchTo()
    {
        return new TracingTargetLocator(driver.switchTo());
    }

    private WebElement createWebElement(WebElement from)
    {
        return new TracingWebElement(from);
    }

    private Object[] unpackWrappedArgs(Object... args)
    {
        // Walk the args: the various drivers expect unpacked versions of the
        // elements
        Object[] usedArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++)
        {
            usedArgs[i] = unpackWrappedElement(args[i]);
        }
        return usedArgs;
    }

    private Object unpackWrappedElement(Object arg)
    {
        if (arg instanceof List<?>)
        {
            @SuppressWarnings("unchecked")
            List<Object> aList = (List<Object>)arg;
            List<Object> toReturn = new ArrayList<>();
            for (int j = 0; j < aList.size(); j++)
            {
                toReturn.add(unpackWrappedElement(aList.get(j)));
            }
            return toReturn;
        }
        else if (arg instanceof Map<?, ?>)
        {
            @SuppressWarnings("unchecked")
            Map<Object, Object> aMap = (Map<Object, Object>)arg;
            Map<Object, Object> toReturn = new HashMap<>();
            for (Entry<Object, Object> entry : aMap.entrySet())
            {
                toReturn.put(entry.getKey(), unpackWrappedElement(entry.getValue()));
            }
            return toReturn;
        }
        else if (arg instanceof TracingWebElement)
        {
            return ((TracingWebElement)arg).getWrappedElement();
        }
        else
        {
            return arg;
        }
    }
}