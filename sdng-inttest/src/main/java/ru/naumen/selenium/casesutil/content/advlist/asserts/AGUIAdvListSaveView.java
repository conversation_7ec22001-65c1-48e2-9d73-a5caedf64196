package ru.naumen.selenium.casesutil.content.advlist.asserts;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.model.content.presentation.DAOPresentation.PresentationType;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInCodeException;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

/**
 * Утилитарные методы для проверок, связанных с формой сохранения представлений advList-а, через интерфейс
 * <AUTHOR>
 * @since 26.05.2015
 */
public class AGUIAdvListSaveView extends GUIAdvlist
{
    public AGUIAdvListSaveView(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверяет значение чекбокса "открывать по умолчанию (для текущего пользователя)"
     * @param value ожидаемое значение чекбокса
     */
    public void defaultValue(boolean value)
    {
        String msg = "Полученное значение чекбокса 'открывать по умолчанию (для текущего пользователя)' не совпало с "
                     + "ожидаемым";
        Assert.assertTrue(msg, value == tester.isSelected(SAVE_VIEW_DEFAULT_CHECKBOX));
    }

    /**
     * Проверить доступность выбора типа представления (новый/текущий)
     */
    public void enablePrsType()
    {
        Assert.assertTrue("Радиобаттон 'сохранить текущий вид' не доступен для выбора",
                tester.isEnabled(X_SAVE_VIEW_CURRENT_RADIO_INPUT));
        Assert.assertTrue("Радиобаттон 'сохранить новый вид' не доступен для выбора",
                tester.isEnabled(X_SAVE_VIEW_NEW_RADIO_INPUT));
    }

    /**
     * Проверяет, что открытое сохранение представления первое, т.е. не из другого предстваления
     */
    public void fromEmptyPrs()
    {
        Assert.assertFalse("Радиобаттон 'сохранить текущий вид' доступен для выбора",
                tester.isEnabled(X_SAVE_VIEW_CURRENT_RADIO_INPUT));
        Assert.assertFalse("Радиобаттон 'сохранить новый вид' доступен для выбора",
                tester.isEnabled(X_SAVE_VIEW_NEW_RADIO_INPUT));
        prsTypeRadiobutton(PresentationType.NEW);
        title("");
    }

    /**
     * Проверить присутствие поля "Разделить вид с..."
     */
    public void ownersTreePresence()
    {
        String input = String.format(GUIXpath.Div.ID_PATTERN, SAVE_VIEW_PUBLIC_TREE_ID) + "//input";
        GUITester.assertExists(input, true, "На форме сохранения вида отсутствует поле 'Разделить вид с...'");
    }

    /**
     * Проверить выбранный тип (новый/текущий) на форме сохранения вида
     * @param type {@link PresentationType}
     */
    public void prsTypeRadiobutton(PresentationType type)
    {
        switch (type)
        {
            case CURRENT:
                GUITester.assertRadioButton(X_SAVE_VIEW_CURRENT_RADIO_INPUT, true);
                GUITester.assertRadioButton(X_SAVE_VIEW_NEW_RADIO_INPUT, false);
                break;
            case NEW:
                GUITester.assertRadioButton(X_SAVE_VIEW_CURRENT_RADIO_INPUT, false);
                GUITester.assertRadioButton(X_SAVE_VIEW_NEW_RADIO_INPUT, true);
                break;
            default:
                throw new ErrorInCodeException("Указан не верный тип представления: " + type);
        }
    }

    /**
     * Проверяет отсутствие чекбокса "общий вид"
     */
    public void publicCheckboxAbsence()
    {
        WaitTool.waitMills(1000);
        Assert.assertFalse("На форме присутствует отсутствует чекбокс 'общий вид'",
                tester.isPresence(SAVE_VIEW_PUBLIC_CHECKBOX));
    }

    /**
     * Проверяет присутствие чекбокса "общий вид"
     */
    public void publicCheckboxPresence()
    {
        WaitTool.waitMills(1000);
        Assert.assertTrue("На форме отсутствует чекбокс 'общий вид'", tester.isPresence(SAVE_VIEW_PUBLIC_CHECKBOX));
    }

    /**
     * Проверяет состояние чекбокса "общий вид" - не доступен для редактирования (не активен)
     */
    public void publicDisabled()
    {
        String msg = "Чекбокса 'общий вид' доступен для редактирования (активен).";
        Assert.assertFalse(msg, tester.isEnabled(SAVE_VIEW_PUBLIC_CHECKBOX));
    }

    /**
     * Проверяет значение чекбокса "общий вид"
     * @param value ожидаемое значение чекбокса
     */
    public void publicValue(boolean value)
    {
        String msg = "Полученное значение чекбокса 'общий вид' не совпало с ожидаемым";
        Assert.assertTrue(msg, value == tester.isSelected(SAVE_VIEW_PUBLIC_CHECKBOX));
    }

    /**
     * Проверяет название представления на форме
     * @param expected ожидаемое название
     */
    public void title(String expected)
    {
        GUITester.assertValue(SAVE_VIEW_TITLE, expected);
    }
}
