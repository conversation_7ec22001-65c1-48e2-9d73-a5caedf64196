package ru.naumen.selenium.casesutil.rights.matrix;

import ru.naumen.selenium.security.rights.IRight;

/**
 * Набор прав для абстрактного БО
 * http://gitsd.naumen.ru/sd40/tree/develop/sdng/src/main/resources/ru/naumen/core/server/bo/abstractBO.domain.xml
 * <AUTHOR>
 * @since 16.01.2013
 */
public enum AbstractBoRights implements IRight
{
    /**Добавление объекта*/
    ADD("AddObject"),
    /**Добавление комментариев*/
    ADD_COMMENT("CommentAdd"),
    /**Добавление файлов*/
    ADD_FILE("FileAdd"),
    /**Добавление приватных комментариев*/
    ADD_PRIVATE_COMMENT("PrivateCommentAdd"),
    /**Помещение объекта в архив*/
    ARCHIVE("RemoveObject"),
    /**Изменение типа объекта*/
    CHANGE_CASE("ChangeObjectType"),
    /**Изменение статуса объекта*/
    CHANGE_STATE("editState"),
    /**Копирование объекта*/
    COPY("CopyObject"),

    /**Удаление объекта*/
    DELETE("DeleteObject"),
    /**Удаление комментариев*/
    DELETE_COMMENT("CommentDel"),
    /**Удаление файлов*/
    DELETE_FILE("FileDel"),
    /**Редактирование комментариев*/
    EDIT_COMMENT("CommentEdit"),
    /**Редактрирование атрибутов комментариев: Остальные атрибуты*/
    EDIT_REST_COMMENT_ATTRIBUTES("editRestCommentAttributes"),
    /**Редактирование атрибутов: Остальные атрибуты*/
    EDIT_REST_ATTRIBUTES("editRestAttributes"),
    /**Другие права: Выгрузка объектов из списка*/
    EXPORT_ADVLIST("exportAdvlist"),

    /**Просмотр файлов*/
    LIST_FILE("FileView"),
    /**Перемещение объекта*/
    MOVE("MoveObject"),
    /**Показывать в результатах поиска*/
    SHOW_IN_SEARCH_RESULTS("ShowInSearchResults"),

    /**Восстановление объекта из архива*/
    UNARCHIVE("UnRemoveObject"),

    /**Пользовательские события: Остальные события*/
    USER_EVENTS("otherEvents"),

    /**Просмотр карточки объекта*/
    VIEW_CARD("ViewObjectCard"),
    /**Просмотр комментариев*/
    VIEW_COMMENT("CommentView"),
    /**Просмотр автора комментария*/
    VIEW_AUTHOR("CommentViewAuthor"),

    /**Просмотр истории событий*/
    VIEW_EVENT("EventView"),

    /**Просмотр приватных комментариев*/
    VIEW_PRIVATE_COMMENT("PrivateCommentView"),

    /**Просмотр атрибутов: Остальные атрибуты*/
    VIEW_REST_ATTRIBUTES("viewRestAttributes"),

    /** Просмотр атрибутов комментариев: Остальные атрибуты*/
    VIEW_REST_COMMENTS_ATTRIBUTES("viewRestCommentAttributes"),

    /** Просмотр списка объектов на отдельной странице*/
    VIEW_LIST_ON_SEPARATE_PAGE("viewListOnSeparatePage"),

    /** Переключение в режим версионирования*/
    SWITCH_TO_PLANNING_MODE("switchToPlanningMode"),

    /**
     * Показывать при упоминании
     */
    SHOW_IN_FAST_LINKS("ShowInFastLinks");

    /**
     * @return все права
     */
    public static IRight[] allRightWithoutChangeState()
    {
        return new IRight[] { UNARCHIVE, ADD, CHANGE_CASE, COPY, MOVE, ARCHIVE, VIEW_CARD, DELETE, ADD_COMMENT,
                ADD_PRIVATE_COMMENT, VIEW_COMMENT, VIEW_AUTHOR, VIEW_PRIVATE_COMMENT, EDIT_COMMENT, DELETE_COMMENT,
                ADD_FILE, LIST_FILE, DELETE_FILE, VIEW_EVENT, VIEW_REST_ATTRIBUTES, EDIT_REST_ATTRIBUTES,
                SHOW_IN_SEARCH_RESULTS };
    }

    /**
     * @return все права
     */
    public static IRight[] allDefaultRight()
    {
        return new IRight[] { UNARCHIVE, ADD, CHANGE_CASE, COPY, MOVE, ARCHIVE, VIEW_CARD, DELETE, ADD_COMMENT,
                ADD_PRIVATE_COMMENT, VIEW_COMMENT, VIEW_AUTHOR, VIEW_PRIVATE_COMMENT, EDIT_COMMENT, DELETE_COMMENT,
                ADD_FILE, LIST_FILE, DELETE_FILE, VIEW_EVENT, VIEW_REST_ATTRIBUTES, EDIT_REST_ATTRIBUTES,
                EDIT_REST_COMMENT_ATTRIBUTES, VIEW_REST_COMMENTS_ATTRIBUTES, EXPORT_ADVLIST, USER_EVENTS,
                VIEW_LIST_ON_SEPARATE_PAGE };
    }

    /**
     * @return все права блока "Комментарии"
     */
    public static IRight[] comments()
    {
        return new IRight[] { ADD_COMMENT, ADD_PRIVATE_COMMENT, VIEW_COMMENT, VIEW_AUTHOR, VIEW_PRIVATE_COMMENT,
                EDIT_COMMENT, DELETE_COMMENT };
    }

    /**
     * @return все права блока "Файлы"
     */
    public static IRight[] files()
    {
        return new IRight[] { ADD_FILE, LIST_FILE, DELETE_FILE };
    }

    /**
     * @return все права блока "Файлы"
     */
    public static IRight[] objects()
    {
        return new IRight[] { UNARCHIVE, ADD, CHANGE_CASE, COPY, MOVE, ARCHIVE, VIEW_CARD, DELETE };
    }

    /**
     * @return все права для Остальных атрибутов
     */
    public static IRight[] rest()
    {
        return new IRight[] { VIEW_REST_ATTRIBUTES, EDIT_REST_ATTRIBUTES };
    }

    /**Код права*/
    private String rightCode;

    private AbstractBoRights(String rightCode)
    {
        this.rightCode = rightCode;
    }

    @Override
    public String getRightCode()
    {
        return rightCode;
    }

    @Override
    public IRight[] getRightsBlock()
    {
        return AbstractBoRights.values();
    }
}
