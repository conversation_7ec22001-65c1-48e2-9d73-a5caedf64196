package ru.naumen.selenium.casesutil.content;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm.CustomFormType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

/**
 * Утилитарные методы для работы со специальными формами через интерфейс
 * <AUTHOR>
 * @since 15.06.2016
 */
public class GUICustomForm extends CoreTester
{
    private static final String FORM_NOT_FOUND_MSG = "Соответствующая форма не найдена";

    private static volatile GUIAdvListUtil advList;

    private static volatile BoTree transitionCasesTree;

    public static final String CUSTOM_FORM_TYPE = String.format(GUIXpath.Any.ANY_CAPTION, "customFormType")
            .concat("//span");

    public static final String CUSTOM_FORM_TITLE = String.format(GUIXpath.Any.ANY_VALUE, "title");
    public static final String CUSTOM_FORM_CODE = String.format(GUIXpath.Any.ANY_VALUE, "code");

    public static final String CUSTOM_FORM_TYPE_VALUE = String.format(GUIXpath.Any.ANY_VALUE, "customFormType")
            .concat("//input");

    public static final String TRANSITION_CASES = String.format(GUIXpath.Any.ANY_VALUE, "transitionCasesProperty");

    public static final String ATTR_GROUP = String.format(GUIXpath.Any.ANY_VALUE, "selectAttributeGroup")
            .concat("//input");

    public static final String COMMENT_ATTR_GROUP =
            "//div[@id='gwt-debug-commentAttributeGroup-value']" + Input.INPUT_PREFIX;

    /*Форма смены типа*/
    public static final String CHANGE_CASE_FORM = "//tr[contains(@__did,'ChangeCaseForm')]";
    /*Форма смены ответственного*/
    public static final String CHANGE_RESPONSIBLE_CASE_FORM = "//tr[contains(@__did,'ChangeResponsibleForm')]";
    /*Форма быстрого добавления и редактирования*/
    public static final String QUICK_ADDEDIT_FORM = "//tr[contains(@__did,'quickAddAndEditForm')]";
    /*Форма массового редактирования*/
    public static final String MASS_EDIT_FORM = "//tr[contains(@__did,'massEditForm')]";

    /*Пиктограмма удаления в адвлисте "Другие формы"*/
    public static final String DELETE_CUSTOM_FORM_CODE = "deleteCustomForm";
    /*Пиктограмма редактирования в адвлисте "Другие формы"*/
    public static final String EDIT_CUSTOM_FORM_CODE = "editCustomForm";

    public static final String EDIT_CUSTOM_FORM = "//span[contains(@id,'" + EDIT_CUSTOM_FORM_CODE + "')]"; // fontIcon

    public static final String DELETE_CUSTOM_FORM =
            "//span[contains(@id,'" + DELETE_CUSTOM_FORM_CODE + "')]"; // fontIcon

    public static final String USE_AS_DEFAULT = "//input[@id='gwt-debug-useAsDefaultProperty-value-input']";

    public static final String USE_STANDARD_ATTRIBUTES_SET = "//input[@id='gwt-debug-useStandardAttributesSetProperty"
                                                             + "-value-input']";

    /*Системная группа атрибутов*/
    public static final String SYSTEM_GROUP = "system";
    /*Системные атрибуты*/
    public static final String SYSTEM_ATTR = "Системные атрибуты";
    /*Значение параметра "Комментарий на форме"*/
    public static final String NOT_FILL = "Не заполнять";

    public static final String NDAP_MAIN_INFO = "Основная информация";
    public static final String NDAP_TASK_PLANNER_CONNECTION_PARAMS = "Параметры соединения";
    public static final String NDAP_TASK_PLANNER_CALCULATION_PARAMS = "Параметры вычисления";
    public static final String SCHEDULE = "Расписание";

    public static final String NDAP_TASK_PLANNER_MAIN_INFO = "ndap_taskPlanner_main_info";
    public static final String NDAP_TASK_PLANNER_CONNECTION = "ndap_taskPlanner_connection";
    public static final String NDAP_TASK_PLANNER_CALCULATION = "ndap_taskPlanner_calculation";

    /**
     * Проверить отсутствие группы атрибутов в списке выбора
     * @param groupAttrCode код группы атрибутов
     */
    public static void assertAbsentsAttrGroup(String groupAttrCode)
    {
        GUISelect.assertNotDisplayed(ATTR_GROUP, groupAttrCode);
    }

    /**
     * Проверить отсутствие группы атрибутов в списке выбора для блока добавления комментария
     * @param groupAttr группа атрибутов
     */
    public static void assertAbsentsCommentAttrGroup(GroupAttr groupAttr)
    {
        GUISelect.assertNotDisplayed(COMMENT_ATTR_GROUP, groupAttr.getCode());
    }

    /**
     * Проверить отсутствие типа в выбранных.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param transitionCase - тип для перехода
     */
    public static void assertAbsentsSelectCase(MetaClass transitionCase)
    {
        String msg = transitionCase.getTitle() + " присутстсвует в выбранных";
        GUITester.assertExists(String.format(TRANSITION_CASES.concat(GUIXpath.Any.ANY), transitionCase.getFqn()),
                Boolean.FALSE, msg);
    }

    /**
     * Проверить отсутствие формы смены типа
     * Должна быть вкладка "Другие формы" метакласса
     * @param cases - типы
     */
    public static void assertCustomChangeCaseFormAbsence(MetaClass... cases)
    {
        String xpath = getFormsXpath(CHANGE_CASE_FORM);
        if (tester.waitDisappear(xpath))
        {
            return;
        }

        List<WebElement> webElements = tester.findElements(xpath);
        for (MetaClass transitionCase : cases)
        {
            webElements = selectWebElementByTransitionCase(webElements, transitionCase);
        }
        Assert.assertTrue("Соответствующая форма смены типа найдена", webElements.isEmpty());
    }

    /**
     * Проверить правильность заполнения параметров для формы смены типа
     * Должна быть вкладка "Другие формы" метакласса
     * @param groupAttrCode код группы атрибутов
     * @param commentOnForm комментарий на форме (Не заполнять/Заполнять/Обязательно заполнять)
     * @param transitionCase - тип для перехода
     */
    public static void assertCustomFormChangeCase(String groupAttribute, String commentOnForm,
            MetaClass... transitionCases)
    {
        WebElement customFormWebElement = getWebElementByTransitionCases(GUICustomForm.CHANGE_CASE_FORM,
                CustomFormType.ChangeCaseForm, transitionCases);
        Assert.assertTrue("Соответствующая форма не найдена", customFormWebElement != null);
        assertCustomFormType(customFormWebElement, CustomForm.CustomFormType.ChangeCaseForm);
        assertCustomFormTransitionCase(customFormWebElement, transitionCases);
        assertCustomFormGroupAttr(customFormWebElement, groupAttribute);
        assertCustomFormCommentOnForm(customFormWebElement, commentOnForm);
    }

    /**
     * Проверить правильность заполнения параметров для формы смены ответственного
     * Должна быть вкладка "Другие формы" метакласса
     * @param groupAttrCode код группы атрибутов
     * @param commentOnForm комментарий на форме (Не заполнять/Заполнять/Обязательно заполнять)
     * @param transitionCase - тип для перехода
     */
    public static void assertCustomFormChangeResponsible(String groupAttribute, String commentOnForm,
            MetaClass... transitionCases)
    {
        WebElement customFormWebElement = getWebElementByTransitionCases(GUICustomForm.CHANGE_RESPONSIBLE_CASE_FORM,
                CustomFormType.ChangeResponsibleForm, transitionCases);
        Assert.assertTrue("Соответствующая форма не найдена", customFormWebElement != null);
        assertCustomFormType(customFormWebElement, CustomForm.CustomFormType.ChangeResponsibleForm);
        assertCustomFormTransitionCase(customFormWebElement, transitionCases);
        assertCustomFormGroupAttr(customFormWebElement, groupAttribute);
        assertCustomFormCommentOnForm(customFormWebElement, commentOnForm);
    }

    /**
     * Проверить правильность заполнения параметров для формы массового редактирования
     * Должна быть вкладка "Другие формы" метакласса
     * @param groupAttrCode код группы атрибутов
     * @param commentOnForm комментарий на форме (Не заполнять/Заполнять/Обязательно заполнять)
     * @param transitionCase - тип для перехода
     */
    public static void assertCustomFormMassEdit(String groupAttribute, String commentOnForm,
            MetaClass... transitionCases)
    {
        WebElement customFormWebElement = getWebElementByTransitionCases(GUICustomForm.MASS_EDIT_FORM,
                CustomFormType.MassEditForm, transitionCases);
        Assert.assertTrue("Соответствующая форма не найдена", customFormWebElement != null);
        assertCustomFormType(customFormWebElement, CustomForm.CustomFormType.MassEditForm);
        assertCustomFormTransitionCase(customFormWebElement, transitionCases);
        assertCustomFormGroupAttr(customFormWebElement, groupAttribute);
        assertCustomFormCommentOnForm(customFormWebElement, commentOnForm);
    }

    /**
     * Проверить правильность заполнения параметров для формы быстрого добавления и редактирвоания
     * Должна быть вкладка "Другие формы" метакласса
     * @param groupAttrCode код группы атрибутов
     * @param transitionCase - тип для перехода
     */
    public static void assertCustomFormQuickAddEdit(String groupAttribute, MetaClass... transitionCases)
    {
        WebElement customFormWebElement = getWebElementByTransitionCases(GUICustomForm.QUICK_ADDEDIT_FORM,
                CustomFormType.QuickForm, transitionCases);
        Assert.assertTrue("Соответствующая форма не найдена", customFormWebElement != null);
        assertCustomFormType(customFormWebElement, CustomForm.CustomFormType.QuickForm);
        assertCustomFormTransitionCase(customFormWebElement, transitionCases);
        assertCustomFormGroupAttr(customFormWebElement, groupAttribute);
    }

    /**
     * Проверить заголовок блока списка специальных форм
     * @param expected - ожидаемый заголовок
     */
    public static void assertCustomFormsTitle(String expected)
    {
        String current = getTitle();
        String message = String.format("Заголовок формы не соответствует ожидаемому, ожидался %s, получен %s", expected,
                current);
        Assert.assertEquals(message, expected, current);
    }

    /**
     * Проверить тип формы на форме добавления/редактирования специальных форм.
     * Должна быть открыта форма добавления/редактирования специальных форм.
     * @param expected название ожидаемого типа формы
     */
    public static void assertCustomFormType(String expected)
    {
        GUISelect.assertSelected(CUSTOM_FORM_TYPE_VALUE, expected);
    }

    /**
     * Проверяет наличие указанного типа пользовательской формы в списке выбора на форме добавления.
     * @param formType тип формы
     * @param selectable true, если тип должен присутствовать в списке, иначе false
     */
    public static void assertCustomFormTypeSelectable(CustomFormType formType, boolean selectable)
    {
        if (selectable)
        {
            GUISelect.assertDisplayed(CUSTOM_FORM_TYPE_VALUE, formType.getFormTypeId());
        }
        else
        {
            GUISelect.assertNotDisplayed(CUSTOM_FORM_TYPE_VALUE, formType.getFormTypeId());
        }
    }

    /**
     * Проверить наличие группы атрибутов в выпадающем списке.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param groupAttrCode код группы атрибутов
     */
    public static void assertPresentAttrGroup(String groupAttrCode)
    {
        GUISelect.assertDisplayed(ATTR_GROUP, groupAttrCode);
    }

    /**
     * Проверить наличие группы атрибутов в выпадающем списке для блока добавления комментария.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param groupAttr группа атрибутов
     */
    public static void assertPresentCommentAttrGroup(GroupAttr groupAttr)
    {
        GUISelect.assertDisplayed(COMMENT_ATTR_GROUP, groupAttr.getCode());
    }

    /**
     * Проверить присутствие формы смены типа
     * Должна быть вкладка "Другие формы" метакласса
     * @param expected true/false присутствует или нет форма
     * @param transitionCases - типы для перехода
     */
    public static void assertPresentChangeCaseForm(boolean expected, MetaClass... transitionCases)
    {
        String message = String.format("Типы перехода %s", expected ? "не найдены" : "найдены");
        WebElement webElement = getWebElementByTransitionCases(GUICustomForm.CHANGE_CASE_FORM,
                CustomFormType.ChangeCaseForm, transitionCases);
        Assert.assertTrue(message, expected && webElement != null || !expected && webElement == null);
    }

    /**
     * Проверить присутствие типа в выбранных,
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param transitionCase - тип для перехода
     */
    public static void assertPresentSelectCase(MetaClass transitionCase)
    {
        String msg = transitionCase.getTitle() + " отсутстсвует в выбранных";
        GUITester.assertExists(String.format(TRANSITION_CASES.concat(GUIXpath.Any.ANY), transitionCase.getFqn()),
                Boolean.TRUE, msg);
    }

    /**
     * Проверить наличие типов для перехода.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param expected true - элемент присутствует, false - элемент отсутствует
     * @param transitionCases - проверяемые типы
     */
    public static void assertPresentTransitionCases(boolean expected, MetaClass... transitionCases)
    {
        getTransitionCasesTree().assertPresentElement(expected, MetaTree.toFqns(transitionCases));
    }

    /**
     * Нажать кнопку "Добавить форму", Необходимо находиться на вкладке "Другие формы"
     */
    public static void clickButtonAddCustomForm()
    {
        getAdvList().toolPanel().clickButton(GUIAdvListXpath.BTN_ADD_CUSTOM_FORM);
    }

    /**
     * Открыть форму редактирования формы массового редактирования
     */
    public static void clickEditMassEditForm()
    {
        tester.click(getFormsXpath(MASS_EDIT_FORM) + "//span[contains(@id,'%s')]", "editCustomForm");
    }

    /**
     * Удалить форму смены типа
     * Необходимо находиться на вкладке "Другие формы"
     * @param cases - тип перехода
     */
    public static void deleteChangeCaseForm(MetaClass... cases)
    {
        for (MetaClass type : cases)
        {
            WebElement webElement = getWebElementByTransitionCases(GUICustomForm.CHANGE_CASE_FORM,
                    CustomFormType.ChangeCaseForm, type);
            //В firefox сначала необходимо навести на элемент мышкой,
            //а потом сделать click. Иначе, если попытаться сразу кликнуть,
            //то сбивается событие клика событием наведения на элемент.
            tester.moveMouse(DELETE_CUSTOM_FORM, 0, 0);
            webElement.findElement(By.xpath(DELETE_CUSTOM_FORM)).click();
            GUIForm.confirmByYes();
        }
    }

    /**
     * Удалить форму массового редактирования
     * Необходимо находиться на вкладке "Другие формы"
     * @param cases - типы перехода
     */
    public static void deleteMassEditCustomForm(MetaClass... cases)
    {
        for (MetaClass type : cases)
        {
            WebElement webElement = getWebElementByTransitionCases(GUICustomForm.MASS_EDIT_FORM,
                    CustomFormType.MassEditForm, type);
            //В firefox сначала необходимо навести на элемент мышкой,
            //а потом сделать click. Иначе, если попытаться сразу кликнуть,
            //то сбивается событие клика событием наведения на элемент.
            tester.moveMouse(DELETE_CUSTOM_FORM, 0, 0);
            webElement.findElement(By.xpath(DELETE_CUSTOM_FORM)).click();
            GUIForm.confirmByYes();
        }
    }

    /**
     * Открыть форму редактирования формы смены типа,
     * Необходимо находиться на вкладке "Другие формы"
     * @param transitionCase - типы перехода
     */
    public static void editChangeCaseForm(MetaClass... transitionCases)
    {

        WebElement webElement = getWebElementByTransitionCases(GUICustomForm.CHANGE_CASE_FORM,
                CustomFormType.ChangeCaseForm, transitionCases);
        //В firefox сначала необходимо навести на элемент мышкой,
        //а потом сделать click. Иначе, если попытаться сразу кликнуть,
        //то сбивается событие клика событием наведения на элемент.
        tester.moveMouse(EDIT_CUSTOM_FORM, 0, 0);
        webElement.findElement(By.xpath(EDIT_CUSTOM_FORM)).click();
    }

    /**
     * Нажимает на иконку редактирования пользовательской формы, имеющей UUID.
     * @param customForm модель формы
     */
    public static void editCustomFormByUuid(CustomForm customForm)
    {
        getAdvList().content().clickPict(customForm, "editCustomForm");
    }

    /**
     * Получить GUIAdvListUtil, относящийся к вкладке "Другие формы".
     * Необходимо находиться на вкладке "Другие формы"
     */
    public static GUIAdvListUtil getAdvList()
    {
        if (advList == null)
        {
            advList = new GUIAdvListUtil(GUIXpath.divGwtDebugId("customForms"));
        }
        return advList;
    }

    /**
     * Получить значение поля "Тип формы".
     * Должна быть открыта форма добавления/редактирования специальных форм
     */
    public static String getCustomFormType()
    {
        return tester.getText(CUSTOM_FORM_TYPE);
    }

    /**
     * Получить заголовок блока списка специальных форм
     */
    public static String getTitle()
    {
        return tester.getText(String.format(GUIXpath.Div.X_BLOCK_TITLE, "customForms"));
    }

    /**
     * Получить BoTree списка "Для перехода в типы".
     * Должна быть открыта форма добавления/редактирования специальных форм
     */
    public static BoTree getTransitionCasesTree()
    {
        if (transitionCasesTree == null)
        {
            transitionCasesTree = new BoTree(TRANSITION_CASES, false);
        }
        return transitionCasesTree;
    }

    /**
     * Снять выбор с указанного типа на форме добавления/редактирования пользовательской формы.
     * @param transitionCase тип, с которого необходимо снять выбор
     */
    public static void resetSelectCase(MetaClass transitionCase)
    {
        tester.click(TRANSITION_CASES.concat(GUIXpath.Any.ANY + GUIXpath.Span.CLOSE2), transitionCase.getFqn());
    }

    /**
     * Выбирает типы, для которых настраивается форма, в дереве "быстрого" выбора.
     * @param value true, если флажоу нужно установить, иначе false
     * @param transitionCases путь к типу, флажок для которого настраивается
     */
    public static void selectTransitionCase(boolean value, MetaClass... transitionCases)
    {
        MetaTree metaClassTree = new MetaTree(TRANSITION_CASES);
        String[] nodes = Arrays.stream(transitionCases).map(MetaClass::getFqn).toArray(n -> new String[n]);
        metaClassTree.openTreeToNode(nodes);
        metaClassTree.setCheckbox(value, nodes);
        metaClassTree.hideSelect();
    }

    /**
     * Установить группу атрибутов.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param groupAttrCode код группы атрибутов
     */
    public static void setAttrGroup(String groupAttrCode)
    {
        GUISelect.expand(ATTR_GROUP);
        GUISelect.select(ATTR_GROUP, groupAttrCode);
    }

    /**
     * Установить группу атрибутов для блока добавления комментария.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param groupAttr группа атрибутов
     */
    public static void setCommentAttrGroup(GroupAttr groupAttr)
    {
        GUISelect.expand(COMMENT_ATTR_GROUP);
        GUISelect.select(COMMENT_ATTR_GROUP, groupAttr.getCode());
    }

    /**
     * Установить атрибут "комментарий на форме" для специальной формы.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param commentOnFormPropCode код необходимого значения атрибута
     */
    public static void setCommentOnForm(String commentOnFormPropCode)
    {
        String xpath = String.format("//label[contains(@id,'%s')]", commentOnFormPropCode);
        CoreTester.tester.click(xpath);
    }

    /**
     * Заполнить название специальной формы указанным значением.
     * @param title название формы
     */
    public static void setFormTitle(String title)
    {
        tester.sendKeys(CUSTOM_FORM_TITLE, title);
    }

    /**
     * Заполнить код специальной формы указанным значением.
     * @param code код формы
     */
    public static void setFormCode(String code)
    {
        tester.sendKeys(CUSTOM_FORM_CODE, code);
    }

    /**
     * Установить тип формы.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param formTypeCode код типа формы
     */
    public static void setFormType(String formTypeCode)
    {
        GUISelect.expand(CUSTOM_FORM_TYPE_VALUE);
        GUISelect.select(CUSTOM_FORM_TYPE_VALUE, formTypeCode);
    }

    /**
     * Установить тип для перехода.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param value значение в которое необходимо выставить чекбокс
     * @param transitionCase - тип
     */
    public static void setPresentTransitionCase(boolean value, MetaClass transitionCase)
    {
        getTransitionCasesTree().showTree();
        getTransitionCasesTree().setCheckbox(value, MetaTree.toFqns(transitionCase));
        getTransitionCasesTree().hideSelect();
    }

    /**
     * Установить типы для перехода.
     * Должна быть открыта форма добавления/редактирования специальных форм
     * @param value значение в которое необходимо выставить чекбокс
     * @param transitionCase - тип
     */
    public static void setPresentTransitionCases(boolean value, MetaClass... transitionCases)
    {
        getTransitionCasesTree().showTree();
        for (MetaClass transitionCase : transitionCases)
        {
            getTransitionCasesTree().setCheckbox(value, MetaTree.toFqns(transitionCase));
        }
        getTransitionCasesTree().hideSelect();
    }

    /**
     * Установить значение чекбокса использовать по умолчанию
     * @param useAsDefault
     */
    public static void setUseAsDefault(boolean useAsDefault)
    {
        tester.setCheckbox(USE_AS_DEFAULT, useAsDefault);
    }

    /**
     * Установить значение чекбокса: использовать системный набор атрибутов
     * @param useSystem
     */
    public static void setUseSystemAttributes(boolean useSystem)
    {
        tester.setCheckbox(USE_STANDARD_ATTRIBUTES_SET, useSystem);
    }

    /**
     * Проверить поле "Комментарий на форме"
     * Необходимо находиться на вкладке "Другие формы"
     * @param customFormWebElement - webElement формы
     * @param expected - ожидаемое значение
     */
    private static void assertCustomFormCommentOnForm(WebElement customFormWebElement, String expected)
    {
        Assert.assertTrue(FORM_NOT_FOUND_MSG, customFormWebElement != null);
        String current = customFormWebElement.findElement(By.xpath(".//td[6]")).getText();
        String message = String.format(
                "Ожидаемое значение поля \"Комментарий на форме\" не совпало с полученым, ожидалось %s, получен %s",
                expected, current);
        Assert.assertEquals(message, expected, current);
    }

    /**
     * Проверить группу атрибутов.
     * Необходимо находиться на вкладке "Другие формы"
     * @param customFormWebElement - webElement формы
     * @param expected - ожидаемая группа атрибутов
     */
    private static void assertCustomFormGroupAttr(WebElement customFormWebElement, String expected)
    {
        Assert.assertTrue(FORM_NOT_FOUND_MSG, customFormWebElement != null);
        String current = customFormWebElement.findElement(By.xpath(".//td[5]")).getText();
        String message = String.format("Ожидаемая группа атрибутов не совпала с полученым, ожидался %s, получен %s",
                expected, current);
        Assert.assertEquals(message, expected, current);
    }

    /**
     * Проверить типы перехода.
     * Необходимо находиться на вкладке "Другие формы"
     * @param customFormWebElement - webElement формы
     * @param expected - ожидаемые типы переходов
     */
    private static void assertCustomFormTransitionCase(WebElement customFormWebElement, MetaClass... expected)
    {
        Assert.assertTrue(FORM_NOT_FOUND_MSG, customFormWebElement != null);
        String current = customFormWebElement.findElement(By.xpath(".//td[4]")).getText();
        for (MetaClass expect : expected)
        {
            String message = String.format("Ожидаемый тип перехода отсутствует %s", expect.getTitle());
            Assert.assertTrue(message, current.contains(expect.getTitle()));
        }
    }

    /**
     * Проверить тип формы.
     * Необходимо находиться на вкладке "Другие формы"
     * @param customFormWebElement - webElement формы
     * @param expected - ожидаемый тип формы
     */
    private static void assertCustomFormType(WebElement customFormWebElement, CustomFormType expected)
    {
        Assert.assertTrue(FORM_NOT_FOUND_MSG, customFormWebElement != null);
        String current = customFormWebElement.findElement(By.xpath(".//td[2]")).getText();
        String message = String.format("Ожидаемый тип формы не совпал с полученым, ожидался %s, получен %s",
                expected.getDescription(), current);
        Assert.assertEquals(message, expected.getDescription(), current);
    }

    /**
     * Получить xpath для всех форм
     */
    private static String getFormsXpath(String xpath)
    {
        return getAdvList().content().getContentXpath().concat(xpath);
    }

    /**
     * Получить WebElement - строку Advlist формы
     * Необходимо находиться на вкладке "Другие формы"
     * @param transitionCases - типы для перехода
     */
    private static WebElement getWebElementByTransitionCases(String xpath, CustomFormType customFormType,
            MetaClass... transitionCases)
    {
        String xPath = getFormsXpath(xpath);
        Assert.assertTrue("Форма не найдена", tester.waitAppear(xPath));
        List<WebElement> webElements = tester.findElements(xPath);

        if (customFormType == CustomFormType.ChangeCaseForm || customFormType == CustomFormType.ChangeResponsibleForm)
        {
            for (MetaClass transitionCase : transitionCases)
            {
                webElements = selectWebElementByTransitionCase(webElements, transitionCase);
            }
        }
        return !webElements.isEmpty() ? webElements.get(0) : null;
    }

    /**
     * Выбрать WebElement из списка WebElement-ов в зависимости от типов
     * @param transitionCase - типы для перехода
     */
    private static List<WebElement> selectWebElementByTransitionCase(List<WebElement> changeCaseWebElements,
            MetaClass transitionCase)
    {
        return changeCaseWebElements.stream().filter(i -> i.getAttribute("__did").contains(transitionCase.getFqn()))
                .collect(Collectors.toList());
    }

}
