package ru.naumen.selenium.casesutil.content;

import static ru.naumen.selenium.casesutil.content.GUIContent.CONTENT_PATTERN;
import static ru.naumen.selenium.casesutil.model.content.ContentForm.*;
import static ru.naumen.selenium.casesutil.model.content.ContentTab.FORM_CODE;
import static ru.naumen.selenium.casesutil.model.content.DAOContentTab.TAB_ID_PREFIX;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.bo.GUIBoCaption;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.IRemoveOperation;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.ContentTabBar;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.Tool;
import ru.naumen.selenium.casesutil.model.content.Tool.AppliedToType;
import ru.naumen.selenium.casesutil.model.content.Tool.PresentationType;
import ru.naumen.selenium.casesutil.model.content.ToolBar;
import ru.naumen.selenium.casesutil.model.content.ToolPanel;
import ru.naumen.selenium.casesutil.model.content.UserTool;
import ru.naumen.selenium.casesutil.model.content.advlist.AdvlistSettings;
import ru.naumen.selenium.casesutil.model.content.advlist.DefaultAdvlistColumnSetting;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.content.advlist.ListSort;
import ru.naumen.selenium.casesutil.model.content.gantt.GanttContent;
import ru.naumen.selenium.casesutil.model.content.gantt.ResourceParams;
import ru.naumen.selenium.casesutil.model.content.gantt.WorkParams;
import ru.naumen.selenium.casesutil.model.content.hierarchygrid.HierarchyGridContent;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.scripts.element.SEContent;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.modules.IModuleContent;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с контентами
 * <AUTHOR>
 * @since 12.04.2012
 */
public class DSLContent
{
    /**
     * Получить модуль для работы с контентами
     */
    public static IModuleContent getContentModule()
    {
        return ScriptModules.getModuleContent();
    }

    /**
     * Вкладка Карточка объекта в метаклассе
     */
    private static final String OBJECTCARD = MetaclassCardTab.OBJECTCARD.get();
    private static final String SHOW_TITLE = "showTitle";

    private static final String PRESENTATION = "presentation";

    private static final String ATTR_GROUP = "attributeGroup";

    private static final String PAGING_POSITION = "pagingPosition";

    public static final String SHOW_ATTRIBUTE_ATTACHED_FILES = "showAttributeAttachedFiles";

    private static final String SHOW_ATTR_DESCRIPTION = "showAttrDescription";

    private static final String ALLOW_EDIT = "allowEdit";

    private static final String SUBJECT_TYPE = "subjectType";

    private static final String RELATION_TYPE = "relationType";

    private static final String RELATION_ATTR_CODE = "relationAttrCode";

    public static final String META_CLASS = "metaClass";

    /**
     * Добавить контенты
     * @param models набор добавляемых контентов
     */
    public static void add(ContentForm... models)
    {
        for (ContentForm model : models)
        {
            getAddContentProperties(null, model);
            model.setExists(true);
            model.setXpathId(String.format(CONTENT_PATTERN, model.getType(), model.getCode()));
        }
    }

    /**
     * Добавить контенты на определенную вкладку
     * @param tab вкладка, на которую добавляется контент
     * @param models набор добавляемых контентов
     */
    public static void add(ContentTab tab, ContentForm... models)
    {
        for (ContentForm model : models)
        {
            getAddContentProperties(tab, model);
            model.setExists(true);
            model.setXpathId(String.format(CONTENT_PATTERN, model.getType(), model.getCode()));
            setParent(model, tab);
        }
    }

    /**
     * Добавить вкладки в контент типа "Панель вкладок"
     * @param content модель контента
     * @param models набор добавляемых вкладок
     */
    public static void addTab(ContentForm content, ContentTab... models)
    {
        List<String> elements = new ArrayList<>();
        for (ContentTab model : models)
        {
            elements.add(addTab(model, content));
        }

        int index = 0;
        List<String> parents = null;
        if (content.getParentTabs() != null)
        {
            parents = Lists.newArrayList(content.getParentTabs().split(","));
            parents.add(content.getXpathId());
        }

        for (String code : elements)
        {
            models[index].setExists(true);
            models[index].setXpathId(TAB_ID_PREFIX + code.trim());
            models[index].setCode(code.trim());
            if (parents != null)
            {
                models[index].setParentTabs(StringUtils.join(parents, ","));
            }
            models[index].setTabBarId(content.getXpathId());
            index++;
        }
    }

    /**
     * Добавить вкладки
     * @param models набор добавляемых вкладок
     */
    public static void addTab(ContentTab... models)
    {
        for (ContentTab model : models)
        {
            addTab(model, null);
            model.setExists(true);
            model.setXpathId(TAB_ID_PREFIX + model.getCode());
        }
    }

    /**
     * Добавить вкладку в контент
     * @param model модель вкладки
     * @param content модель контента, в который добавляется вкладка, может быть null, тогда вкладка
     * добавляется в корневой контент
     * @return uuid вкладки
     */
    public static String addTab(ContentTab model, ContentForm content)
    {
        Map<String, Object> dataForScript = new HashMap<>();
        dataForScript.put("fqn", model.getParentFqn());
        dataForScript.put("code", model.getCode());
        //Если указан контент, в который добавляется вкладка, то форму получаем из него,
        //инчаче форма должна быть указана в модели вкладки
        dataForScript.put(FORM_CODE, content == null ? model.getFormCode() : content.getTab());
        dataForScript.put(Model.TITLE, model.getTitle());
        dataForScript.put("parent",
                content == null ? null : StringUtils.substringAfter(content.getXpathId(), "gwt-debug-TabBar."));
        dataForScript.put("profiles", Json.GSON.toJson(model.getProfileCodes()));
        dataForScript.put("versProfiles", Json.GSON.toJson(model.getVersProfileCodes()));
        dataForScript.put("tags", Json.GSON.toJson(model.getTagCodes()));
        dataForScript.put("countObjectsContentUuids", Json.GSON.toJson(model.getCountObjectContentUuids()));
        dataForScript.put("internalScrolling", model.getUseInternalScrollSetting());
        dataForScript.put("considerListFilter", model.getConsiderListFilter());
        dataForScript.put("panel", Json.GSON.toJson(model.getToolPanel()));
        dataForScript.put(ContentTab.VISIBILITY_CONDITION, model.getVisibilityCondition());

        return getContentModule().addContent_AddTab(dataForScript);
    }

    /**
     * Добавить в контент на карточке объекта пользовательский инструмент (кнопку-скрипт)
     * @param content - контент
     * @param tab - вкладка карточки объекта, на которой распололжен контент
     * @param title - заголовок кнопки
     * @param toolBarNumber - номер тулбара, в который будет добавлен инструмент ( начиная от 1 )
     * @param toolNumber - номер расположения нового инструмента в тулбаре ( начиная от 1 )
     * @param eventAction - действие по событию для выполнения в кнопке-скрипт
     */
    public static void addUserTool(ContentForm content, @Nullable ContentTab tab, String title, int toolBarNumber,
            int toolNumber, EventAction eventAction)
    {
        UserTool tool = new UserTool(title);
        tool.setEventUuid(eventAction.getUserEventUuid());
        ToolPanel toolPanel = Json.GSON.fromJson(content.getToolPanel(), ToolPanel.class);
        toolPanel.setUseSystemSettings(false);
        List<ToolBar> toolBars = toolPanel.getToolBars();
        if (toolBars.isEmpty())
        {
            toolBars.add(new ToolBar());
        }
        ToolBar toolBar = toolPanel.getToolBars().get(toolBarNumber - 1);
        toolBar.getTools().add(toolNumber - 1, tool);
        content.setToolPanel(toolPanel);
        if (tab == null)
        {
            edit(content);
            return;
        }
        edit(tab, content);
    }

    /**
     * Добавить в контент пользовательский инструмент (кнопку-скрипт)
     * @param content - контент
     * @param title - заголовок кнопки
     * @param eventAction - действие по событию для выполнения в кнопке-скрипт
     */
    public static void addUserTool(ContentForm content, String title, EventAction eventAction)
    {
        Tool tool = DAOTool.createUserTool(AppliedToType.LIST_OBJECTS, PresentationType.DEFAULT_TEXT_ONLY,
                eventAction.getUserEventUuid());
        tool.setTitle(title);

        addUserTool(content, tool);
    }

    /**
     * Добавить в контент пользовательский инструмент (кнопку-скрипт)
     * @param content - контент
     * @param title - заголовок кнопки
     * @param eventAction - действие по событию для выполнения в кнопке-скрипт
     * @param appliedTo - тип сущности, к которой применяется действие
     */
    public static void addUserTool(ContentForm content, String title, EventAction eventAction, AppliedToType appliedTo)
    {
        Tool tool = DAOTool.createUserTool(appliedTo, PresentationType.DEFAULT_TEXT_ONLY,
                eventAction.getUserEventUuid());
        tool.setTitle(title);

        addUserTool(content, tool);
    }

    /**
     * Добавить в контент пользовательский инструмент (кнопку-скрипт)
     * @param content - контент
     * @param title - заголовок кнопки
     * @param toolBarNumber - номер тулбара, в который будет добавлен инструмент ( начиная от 1 )
     * @param toolNumber - номер расположения нового инструмента в тулбаре ( начиная от 1 )
     * @param eventAction - действие по событию для выполнения в кнопке-скрипт
     */
    public static void addUserTool(ContentForm content, String title, int toolBarNumber, int toolNumber,
            EventAction eventAction)
    {
        addUserTool(content, null, title, toolBarNumber, toolNumber, eventAction);
    }

    /**
     * Добавить в контент пользовательский инструмент (кнопку-скрипт)
     * @param content - контент
     * @param tool - кнопка
     */
    public static void addUserTool(ContentForm content, Tool tool)
    {
        ToolPanel toolPanel = Json.GSON.fromJson(content.getToolPanel(), ToolPanel.class);
        toolPanel.setUseSystemSettings(false);
        toolPanel.getToolBars().get(0).addTools(tool);
        content.setToolPanel(toolPanel);
        edit(content);

        Cleaner.afterTest(true, () ->
        {
            toolPanel.getToolBars().get(0).getTools().remove(tool);
            content.setToolPanel(toolPanel);
            edit(content);
        });
    }

    /**
     * Добавить в контент пользовательские инструменты (кнопку-скрипт)
     * @param content - контент
     * @param toolBarNumber - номер тулбара, в который будет добавлен инструмент ( начиная от 1 )
     * @param toolNumber - номер расположения нового инструмента в тулбаре ( начиная от 1 )
     * @param userTools - массив кнопок, которые надо добавить
     */
    public static void addUserTools(ContentForm content, int toolBarNumber, int toolNumber, UserTool... userTools)
    {
        ToolPanel toolPanel = Json.GSON.fromJson(content.getToolPanel(), ToolPanel.class);
        toolPanel.setUseSystemSettings(false);
        ToolBar toolBar = toolPanel.getToolBars().get(toolBarNumber - 1);
        toolBar.getTools().addAll(toolNumber - 1, Arrays.asList(userTools));
        content.setToolPanel(toolPanel);
        edit(content);
    }

    /**
     * Добавить в контент сортировку по умолчанию
     * @param content - контент
     * @param setting - настройка сортировки
     */
    public static void addListSort(ContentForm content, ListSort setting)
    {
        content.setDefaultListSort(setting);
        edit(content);
    }

    /**
     * Добавить в контент фильтрацию по умолчанию
     * @param content - контент
     * @param setting - настройка фильтрации
     */
    public static void addListFilter(ContentForm content, ListFilter setting)
    {
        content.setDefaultListFilter(setting);
        edit(content);
    }

    /**
     * Сохраняет UUID из ссылки в качестве кода контента.
     * @param contentType тип контента
     * @param link ссылка на контент на отдельной странице
     * @return модель контента с UUID, соответствующим ссылке
     */
    public static ContentForm createContentFromLink(ContentType contentType, String link)
    {
        int index = link.indexOf("encoded_text$");
        ContentForm copy = ModelFactory.create(ContentForm.class);
        String uuid = link.substring(index);
        copy.setCode(uuid);
        copy.setType(contentType.getType());
        copy.setXpathId("gwt-debug-" + contentType.getType() + "." + uuid);
        copy.setExists(false);
        return copy;
    }

    /**
     * Превращает системную панель действий контента в пользовательскую.
     * @param content контент, содержащий панель действий
     */
    public static void resetSystemToolPanel(ContentForm content)
    {
        ToolPanel toolPanel = Json.GSON.fromJson(content.getToolPanel(), ToolPanel.class);
        toolPanel.setUseSystemSettings(false);
        content.setToolPanel(toolPanel);
        edit(content);
    }

    /**
     * Сохранить вид
     * @param model модель вида
     */
    public static void saveAdvlistSettings(AdvlistSettings model)
    {
        Map<String, Object> properties = new HashMap<>();
        properties.put("fqn", model.getFqn());
        properties.put("cardFqn", model.getCardFqn());
        properties.put("listFilter", model.getListFilter());
        properties.put("listSort", model.getListSort());
        properties.put("columnList", model.getColumnList());
        properties.put("pageSize", model.getPageSize());
        properties.put("title", model.getTitle());
        properties.put("contentUuid", model.getContentUuid());
        getContentModule().saveAdvlistSettings(properties);
        Cleaner.afterTest(() -> deleteAdvlistSettings(model.getFqn()));
    }

    /**
     * Добавить в контент Количество объектов на странице по умолчанию
     * @param content - контент
     * @param size - количество объектов на странице
     */
    public static void setPageSize(ContentForm content, Integer size)
    {
        content.setPageSize(size);
        edit(content);
    }

    /**
     * Изменить атрибуты в настройку Атрибуты, выводимые в контент по умолчанию
     * @param content - контент
     * @param setting - настройка колонок для адвлиста
     */
    public static void setAdvlistColumns(ContentForm content, DefaultAdvlistColumnSetting setting)
    {
        content.setInheritAdvlistColumns(false);
        content.setAdvlistColumns(setting);
        edit(content);
    }

    /**
     * Удалить контент
     * @param models модели контентов
     */
    public static void delete(ContentForm... models)
    {
        for (ContentForm model : models)
        {
            String parentFqn = model.getParentFqn();
            //Прекращаем наследование, чтобы контент удалялся именно из типа, а не из класса
            if (parentFqn.contains("$"))
            {
                editSettings(parentFqn, model.getTab());
            }
            getContentModule().deleteContent(parentFqn, model.getCode(), model.getTab());
            model.setExists(false);
        }
    }

    /**
     * Удалить все виды в метаклассе
     * @param fqn идентификатор метакласса
     */
    public static void deleteAdvlistSettings(String fqn)
    {
        getContentModule().deleteAdvlistSettings(fqn);
    }

    /**
     * Удалить все контенты с определенной вкладки
     * @param model модель вкладки
     */
    public static List<String> deleteContentFromTab(ContentTab model)
    {
        List<String> dataForScript = new ArrayList<>();
        dataForScript.add(model.getParentFqn());
        dataForScript.add(model.getFormCode());
        dataForScript.add(model.getXpathId());
        return dataForScript;
    }

    /**
     * Удалить контенты определенного типа
     * @param parent модель метакласса, в котором находится контент
     * @param tab форма, на которой находится контент (добавления/редактирования/карточка)
     * @param type тип удаляемых контентов
     */
    public static void deleteContentsByType(MetaClass parent, MetaclassCardTab tab, ContentType type)
    {
        List<String> result = getContentModule().searchContent(parent.getFqn(), type.getType(), tab.get());

        ContentForm[] contents = new ContentForm[result.size()];
        for (int i = 0; i < result.size(); i++)
        {
            ContentForm content = DAOContentForm.createContent(parent.getFqn(), tab, type, PositionContent.FULL);
            content.setCode(result.get(i));
            contents[i] = content;
        }
        delete(contents);
    }

    /**
     * Удалить все контенты с определенных вкладок
     * @param models модели вкладок
     */
    public static void deleteContentsFromTab(ContentTab... models)
    {
        List<List<String>> elements = new ArrayList<>();
        for (ContentTab model : models)
        {
            elements.add(deleteContentFromTab(model));
        }
        getContentModule().deleteContentsFromTab(elements);
    }

    /**
     * Удалить все контенты с определенной вкладки {@link MetaclassCardTab} метакласса
     * @param metaClass модель метакласса
     * @param form {@link MetaclassCardTab}
     */
    public static void deleteContentsFromTab(MetaClass metaClass, MetaclassCardTab form)
    {
        ContentTab tab = new ContentTab();
        tab.setXpathId(null);
        tab.setParentFqn(metaClass.getFqn());
        tab.setFormCode(form.get());
        deleteContentsFromTab(tab);
    }

    /**
     * Удалить виджет "Выбор представления" из адвлиста контента "Список объектов
     * @param model модель контента
     */
    public static void deleteSelectToolsFromAdvlist(ContentForm model)
    {
        String parentFqn = model.getParentFqn();
        //Прекращаем наследование, чтобы контент удалялся именно из типа, а не из класса
        if (parentFqn.contains("$"))
        {
            editSettings(parentFqn, model.getTab());
        }
        List<String> element = deleteSelectToolsFromAdvlist(parentFqn, model.getXpathId());
        getContentModule().deleteSelectToolsFromAdvlist(element.get(0), element.get(1));
    }

    /**
     * Удалить виджет "Выбор представления" из адвлиста контента "Список объектов"
     * @param fqnCode код метакласса в котором редактируем контент.
     * @param code код контента
     * @return возвращает скриптовый элемент
     */
    public static List<String> deleteSelectToolsFromAdvlist(String fqnCode, String code)
    {
        //Добавляем параметры
        List<String> params = new ArrayList<>();
        params.add(fqnCode);
        if (code.contains(TAB_ID_PREFIX))
        {
            params.add(StringUtils.substringAfter(code, TAB_ID_PREFIX));
        }
        else
        {
            params.add(org.apache.commons.lang3.StringUtils.substringAfterLast(code, "."));
        }
        return params;
    }

    /**
     * Удаляем из системы вкладки, определённые их моделями
     * @param models - модели вкладок
     */
    public static void deleteTab(ContentTab... models)
    {
        for (ContentTab model : models)
        {
            if (model.isExists())
            {
                getContentModule().deleteContent(model.getParentFqn(), model.getCode(), model.getFormCode());
                model.setExists(false);
            }
        }
    }

    /**
     * Редактировать контенты
     * @param models набор редактируемых контентов
     */
    public static void edit(ContentForm... models)
    {
        edit(null, models);
    }

    /**
     * Редактировать контенты на определенной вкладке
     * @param tab вкладка, на которой редактируется контент
     * @param models набор редактируемых контентов
     */
    public static void edit(ContentTab tab, ContentForm... models)
    {
        for (ContentForm model : models)
        {
            getAddContentProperties(tab, model);
        }
    }

    /**
     * Переопределить параметры в настройках контента типа 'Отчет, печатная форма'.
     * Метод поддерживает следующие типы параметров:
     * Строка: любая строка.
     * Целое число: целое число в виде строки.
     * Дробное число: дробное число в виде строки.
     * Дата: дата в виде строки в формате dd.MM.yyyy
     * Дата-время: дата в виде строки в формате dd.MM.yyyy HH:mm
     * Логический: true/false в виде строки.
     * Справочник: UUID элемента.
     * Объект: UUID объекта.
     * Набор элементов справочников: список uuid-ов закодированных json в строку.
     * Набор объектов: список uuid-ов закодированных json в строку.
     * @param reportContent контент типа 'Отчет, печатная форма'.
     * @param params - реализует интерфейс Map, где в качестве ключа выступает код параметра, в качестве
     * значения - значение параметра. Если передан null или пустая мапа, параметры отчета будут сброшены на умолчания.
     * @param paramsVisible - возможность изменить значение параметров в интерфейсе оператора.
     */
    public static void editParametersReportContent(ContentForm reportContent, @Nullable Map<String, String> params,
            boolean paramsVisible)
    {
        Map<String, Object> dataForScript = new HashMap<>();
        dataForScript.put("code", reportContent.getCode());
        dataForScript.put("reportTemplateCode", reportContent.getTemplateCode());
        dataForScript.put("params", params);
        dataForScript.put("paramsVisible", paramsVisible);

        getContentModule().editParametersReportContent(dataForScript);
    }

    /**
     * Переопределить параметры в настройках контента типа 'Отчет, печатная форма'.
     * Метод поддерживает следующие типы параметров:
     * Строка: любая строка.
     * Целое число: целое число в виде строки.
     * Дробное число: дробное число в виде строки.
     * Дата: дата в виде строки в формате dd.MM.yyyy
     * Дата-время: дата в виде строки в формате dd.MM.yyyy HH:mm
     * Логический: true/false в виде строки.
     * Справочник: UUID элемента.
     * Объект: UUID объекта.
     * Набор элементов справочников: список uuid-ов закодированных json в строку.
     * Набор объектов: список uuid-ов закодированных json в строку.
     * @param reportContent контент типа 'Отчет, печатная форма'.
     * @param code код атрибута
     * @param value значение атрибута
     * @param visible возможность изменить значение параметров в интерфейсе оператора.
     */
    public static void editParametersReportContent(ContentForm reportContent, String code, String value,
            boolean visible)
    {
        Map<String, String> params = new HashMap<>();
        params.put(code, value);
        editParametersReportContent(reportContent, params, visible);
    }

    /**
     * Переопределить параметры в настройках контента типа 'Отчет, печатная форма'.
     * Метод поддерживает следующие типы параметров:
     * Строка: любая строка.
     * Целое число: целое число в виде строки.
     * Дробное число: дробное число в виде строки.
     * Дата: дата в виде строки в формате dd.MM.yyyy
     * Дата-время: дата в виде строки в формате dd.MM.yyyy HH:mm
     * Логический: true/false в виде строки.
     * Справочник: UUID элемента.
     * Объект: UUID объекта.
     * Набор элементов справочников: список uuid-ов закодированных json в строку.
     * Набор объектов: список uuid-ов закодированных json в строку.
     * @param reportContent контент типа 'Отчет, печатная форма'.
     * @param code код атрибута
     * @param value значение атрибута
     */
    public static void editParametersReportContent(ContentForm reportContent, String code, String value)
    {
        editParametersReportContent(reportContent, code, value, true);
    }

    /**
     * Прекратить наследование настроек контентов в метаклассе на одной из форм(добавления, редактирования, карточка).
     * Соответствует нажатию кнопки "Редактировать настройки" в интерфейсе
     * @param mc модель метакласса
     * @param tab форма
     */
    public static void editSettings(MetaClass mc, MetaclassCardTab tab)
    {
        editSettings(mc.getFqn(), tab.get());
    }

    /**
     * Прекратить наследование настроек контентов в метаклассе на одной из форм(добавления, редактирования, карточка).
     * Соответствует нажатию кнопки "Редактировать настройки" в интерфейсе
     * @param fqn идентификатор метакласса
     * @param tab код формы
     */
    public static void editSettings(String fqn, String tab)
    {
        getContentModule().resetNestingContentSettings(fqn, tab);
    }

    /**
     * Редактировать вкладки в контенте типа "Панель вкладок"
     * @param content модель контента
     * @param models набор редактируемых вкладок
     */
    public static void editTab(ContentForm content, ContentTab... models)
    {
        for (ContentTab model : models)
        {
            addTab(model, content);
        }
    }

    /**
     * Редактировать вкладки
     * @param tabs набор редактируемых вкладок
     */
    public static void editTab(ContentTab... tabs)
    {
        for (ContentTab model : tabs)
        {
            addTab(model, null);
        }
    }

    /**
     * Добавить/отредактировать контент на определенной вкладке
     * @param tab вкладка, на которой добавить/отредактировать контент
     * @param model модель контента
     */
    @SuppressFBWarnings("BC_UNCONFIRMED_CAST")
    public static void getAddContentProperties(ContentTab tab, ContentForm model) //NOSONAR много ифов
    {
        Map<String, Object> properties = new HashMap<>();
        properties.put("fqn", model.getParentFqn());
        properties.put(FORM_CODE, model.getTab());
        properties.put("code", model.getCode());
        properties.put(POSITION, model.getPosition());
        properties.put("tabId",
                tab == null ? null : StringUtils.substringAfter(tab.getXpathId(), TAB_ID_PREFIX));
        properties.put("profiles", Json.GSON.toJson(model.getProfileCodes()));
        properties.put("versProfiles", Json.GSON.toJson(model.getVersProfileCodes()));
        properties.put("tags", Json.GSON.toJson(model.getTagCodes()));
        properties.put("settingsSet", model.getSettingsSet());
        properties.put("toolPanel", model.getToolPanel());
        properties.put(ContentForm.VISIBILITY_CONDITION, model.getVisibilityCondition());

        if (model.getPresentation() != null && model.getPresentation().equals(PresentationContent.ADVLIST.get()))
        {
            properties.put("defaultSettings", model.getAdvlistDefaultSettings());
            if (model.getMassToolPanel() != null)
            {
                properties.put("massToolPanel", model.getMassToolPanel());
            }
            properties.put("objectFilter", model.getObjectListFilter());
        }

        String casesProperty = "cases";
        switch (ContentType.getContentType(model.getType()))
        {
            case CHILD_OBJECT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                properties.put(META_CLASS, model.getMetaclassCode());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(casesProperty, model.getCasesCode());
                properties.put("showNestedInNested", model.getShowNestedInNested());
                properties.put(CUSTOM_QUERIES_ENABLED, model.isCustomQueriesEnabled());
                properties.put(SQL_QUERY, model.getSqlQuery());
                getContentModule().addContent_ChildObjectList(properties);
                break;
            case OBJECT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                properties.put(META_CLASS, model.getMetaclassCode());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(casesProperty, model.getCasesCode());
                properties.put("refreshPresentationType", model.getAdvlistRefreshPresentationType());
                properties.put(CUSTOM_QUERIES_ENABLED, model.isCustomQueriesEnabled());
                properties.put(SQL_QUERY, model.getSqlQuery());
                getContentModule().addContent_ObjectList(properties);
                break;
            case RELATED_OBJECT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(ATTR_CHAIN, model.getRecepient());
                properties.put(casesProperty, model.getCasesCode());
                properties.put("formCaption", model.getTitleRelatedAddForm());
                properties.put("showLinkedObject", model.getShowLinked());
                properties.put("showRelatedWithNested", model.getShowRelatedWithNested());
                properties.put("beforeHierarchy", model.getBeforeHierarchyAttribute());
                properties.put("hierarchyAttribute", model.getHierarchyAttribute());
                properties.put(CUSTOM_QUERIES_ENABLED, model.isCustomQueriesEnabled());
                properties.put(SQL_QUERY, model.getSqlQuery());
                getContentModule().addContent_RelObjectList(properties);
                break;
            case EVENT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                getContentModule().addContent_EventList(properties);
                break;
            case EDITABLE_PROPERTY_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_EditablePropertyList(properties);
                break;
            case REL_OBJ_PROP_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put("attrCode", model.getRelAttribute());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                properties.put(ALLOW_EDIT, model.getAllowEdit());
                getContentModule().addContent_RelObjPropertyList(properties);
                break;
            case PROPERTY_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_PropertyList(properties);
                break;
            case TAB_BAR:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(ContentForm.HIDE_SINGLE_TAB, model.isHideSingleTab());
                getContentModule().addContent_TabBar(properties);
                break;
            case COMMENT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(SUBJECT_TYPE, model.getSubjectType());
                properties.put(RELATION_ATTR_CODE, model.getRelAttribute());
                properties.put(ATTR_CHAIN, model.getAttrChain());
                properties.put("detailedAttrGroup", model.getDetailedAttributeGroup());
                properties.put("addFormAttributeGroup", model.getAddFormAttributeGroup());
                properties.put("editFormAttributeGroup", model.getEditFormAttributeGroup());
                properties.put(SHOW_ATTR_DESCRIPTION_ON_ADD_FORM, model.isShowAttrDescriptionOnAddForm());
                properties.put(SHOW_ATTR_DESCRIPTION_ON_EDIT_FORM, model.isShowAttrDescriptionOnEditForm());
                properties.put(ContentForm.RESTRICT_CONTENT, model.getRestrictedContent());
                properties.put(SHOW_ATTRIBUTE_ATTACHED_FILES, model.getShowAttributeAttachedFiles());
                getContentModule().addContent_CommentList(properties);
                break;
            case FILE_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                properties.put(RELATION_TYPE, model.getFileRelationType());
                properties.put(ATTR_CHAIN, model.getAttrChain());
                properties.put("attributeCodes", model.getAttributeCodes());
                getContentModule().addContent_FileList(properties);
                break;
            case REPORT_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("templates", model.getTemplatesCode());
                getContentModule().addContent_ReportsList(properties);
                break;
            case REPORT:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("template", model.getTemplateCode());
                getContentModule().addContent_ReportContent(properties);
                break;
            case EMBEDDED_APPLICATION:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("application", model.getApplicationCode());
                getContentModule().addContent_EmbeddedApplicationContent(properties);
                break;
            case SELECT_CASE:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_SelectCase(properties);
                break;
            case SELECT_SC_CASE:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_SelectScCase(properties);
                break;
            case SELECT_PARENT:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_SelectParent(properties);
                break;
            case SELECT_CONTACTS:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("attributes", model.getAdditionalContacts());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_SelectContacts(properties);
                break;
            case CLIENT_INFO:
                properties.put(Model.TITLE, model.getTitle());
                properties.put("ouAttributeGroup", model.getOuAttrGroupCode());
                properties.put("emplAttributeGroup", model.getEmployeeAttrGroupCode());
                properties.put("teamAttributeGroup", model.getTeamAttrGroupCode());
                getContentModule().addContent_ClientInfo(properties);
                break;
            case HIERARCHY_GRID:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("structuredObjectsViewCode", model.getStructuredObjectsViewCode());
                properties.put("buildHierarchyFromCurrentObject", model.getBuildHierarchyFromCurrentObject());
                properties.put("focusOnCardObject", model.getFocusOnCardObject());
                properties.put("defaultSettings", ((HierarchyGridContent)model).getDefaultViewSettings());
                getContentModule().addContent_HierarchyGrid(properties);
                break;
            case MASS_PROBLEMS:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("presentationType", model.isSimpleForm() ? "simple" : "complex");
                getContentModule().addContent_MassProblems(properties);
                break;
            case SELECT_CLIENT:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                getContentModule().addContent_SelectClient(properties);
                break;
            case NETWORK_SCHEME:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(casesProperty, model.getCasesCode());
                getContentModule().addContent_NetworkScheme(properties);
                break;
            case RELATION_SCHEME:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put("hops", model.getHops());
                properties.put("displayCases", model.getDisplayCases());
                properties.put("relations", model.getAttributesCodes());
                getContentModule().addContent_RelationScheme(properties);
                break;
            case WF_DIAGRAM:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                getContentModule().addContent_WorkflowContent(properties);
                break;
            case USER_HISTORY_LIST:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                properties.put(PRESENTATION, model.getPresentation());
                properties.put(PAGING_POSITION, model.getPagingPosition());
                properties.put(META_CLASS, model.getMetaclassCode());
                properties.put(ATTR_GROUP, model.getAttributeGroupCode());
                getContentModule().addContent_UserHistoryList(properties);
                break;
            case GANTT:
                GanttContent gantt = (GanttContent)model;
                WorkParams work = gantt.getWorkParams();
                ResourceParams resource = gantt.getResourceParams();

                //@formatter:off
            properties.put(Model.TITLE, gantt.getTitle());
            properties.put(SHOW_TITLE, gantt.getShowTitle());
            properties.put("scale", gantt.getScale().name());
            properties.put("filterMode", gantt.getFilterMode().name());
            properties.put("serviceTime", gantt.getServiceTime() != null ? gantt.getServiceTime().getUuid() : null);
            properties.put("workLinkedWithCurrentObject", work.isLinkedWithCurrentObject());
            properties.put("workLinkedAttrChain", work.getLinkedAttrChain() != null
                    ? work.getLinkedAttrChain().stream().map(Attribute::getFqn).collect(Collectors.toList())
                    : null);
            properties.put("workAttributeGroup", work.getAttributeGroup() != null ? work.getAttributeGroup().getCode() : null);
            properties.put("workStateColorPrs", work.getStateColorPrs() != null ? work.getStateColorPrs().toString() : null);
            properties.put("workEndDate", work.getEndDate() != null ? work.getEndDate().getCode() : null);
            properties.put("workStartDate", work.getStartDate() != null ? work.getStartDate().getCode() : null);
            properties.put("workDeadline", work.getDeadline() != null ? work.getDeadline().getCode() : null);
            properties.put("workPreviousWork", work.getPreviousWork() != null ? work.getPreviousWork().getCode() : null);
            properties.put("workResource", work.getResource() != null ? work.getResource().getCode() : null);
            properties.put("workMetaClass", work.getWorkMetaClass() != null ? work.getWorkMetaClass().getCode() : null);
            properties.put("workMetaClasses", work.getWorkMetaClasses());
            properties.put("resourceIsCurrentObject", resource.isResourceIsCurrentObject());
            properties.put("resourceLinkedWithCurrentObject", resource.isLinkedWithCurrentObject());
            properties.put("resourceAttributeGroup", resource.getAttributeGroup() != null ? resource.getAttributeGroup().getCode() : null);
            properties.put("resourceLinkedAttrChain", resource.getLinkedAttrChain() != null ?
                    resource.getLinkedAttrChain().stream().map(Attribute::getFqn).collect(Collectors.toList()) : null);
            properties.put("resourceMetaClass", resource.getResourceMetaClass() != null ? resource.getResourceMetaClass().getCode() : null);
            properties.put("resourceMetaClasses", resource.getResourceMetaClassFqns());
            //@formatter:on
                getContentModule().addContent_GanttContent(properties);

                break;
            case WINDOW:
                String strCaption = model.getObjectCardCaptionString();
                String captionAttr = StringUtils.isEmpty(strCaption)
                        ? GUIBoCaption.TYPE_AND_TITLE_CODE
                        : GUIBoCaption.STRING_CAPTION_CODE;
                properties.put("objectCardCaptionAttributeCode", captionAttr);
                properties.put("objectCardCaptionString", strCaption);
                getContentModule().addContent_Window(properties);
                break;
            case NDAP_TRIGGER_HISTORY:
                properties.put(Model.TITLE, model.getTitle());
                properties.put(SHOW_TITLE, model.getShowTitle());
                getContentModule().addContent_NDAPTriggerHistory(properties);
                break;
            default:
                throw new RuntimeException("Unknown content: " + model.getType());
        }
    }

    /**
     * Получить модель контента по его типу
     * @param parent модель метакласса, в котором ищем контент
     * @param contentType тип контента
     * @param cardTab вкладка, на которой находится контент (форма добавления, редактирования, карточка)
     * @return модель контента
     */
    public static ContentForm getContent(MetaClass parent, ContentType contentType, MetaclassCardTab cardTab)
    {
        String title = GUIFillContent.getAutoName(contentType);
        return getContentByTitle(parent.getFqn(), title, cardTab.get());
    }

    /**
     * Получить модель контента, присутствующего на карточке объекта по умолчанию
     * @param metaClass метакласс, в котором ищем контент
     * @return модель контента
     */
    public static ContentForm getDefaultCardContent(MetaClass metaClass)
    {
        return getContentByTitle(metaClass.getFqn(), "Основные параметры", MetaclassCardTab.OBJECTCARD.get());
    }

    /**
     * Получить модель контента по его названию
     * @param metaClass метакласс, в котором ищем контент
     * @param title название контента
     * @param cardTab вкладка, на которой находится контент (форма добавления, редактирования, карточка)
     * @return модель контента
     */
    public static ContentForm getContentByTitle(MetaClass metaClass, String title, MetaclassCardTab cardTab)
    {
        return getContentByTitle(metaClass.getFqn(), title, cardTab.get());
    }

    /**
     * Получить модель контента по его названию
     * @param parentFqn идентификатор метакласса, в котором ищем контент
     * @param title название контента
     * @param tab вкладка, на которой находится контент (форма добавления, редактирования, карточка)
     * @return модель контента
     */
    public static ContentForm getContentByTitle(String parentFqn, String title, String tab)
    {
        ModelMap map = Json.stringToMapJson(getContentModule().getContentByTitle(parentFqn, title, tab));
        return getContentByMap(map, parentFqn, title, tab);
    }

    /**
     * Получить первый таб контента "Панель вкладок"
     * <b>ВНИМАНИЕ! этот метод не загружает модель с сервера</b>
     * @param tabBar модель контента "Панель вкладок"
     * @return модель вкладки
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static ContentTab getFirstTab(ContentForm tabBar)
    {
        ContentTab tab = new ContentTab();
        tab.setXpathId(getTabsFromTabBar(tabBar).get(0));
        tab.setCode(StringUtils.substringAfter(tab.getXpathId(), DAOContentTab.TAB_ID_PREFIX));
        tab.setTitle(tabBar.getTitle());
        tab.setParentFqn(tabBar.getParentFqn());
        tab.setParentTabs(tabBar.getParentTabs());
        tab.setTabBarId(tabBar.getXpathId());
        return tab;
    }

    /**
     * Получить таб контента "Панель вкладок"
     * @param tabBar модель контента "Панель вкладок"
     * @param index индекс в списке вкладок (начинается с нуля)
     * @return модель вкладки
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static ContentTab getTab(ContentForm tabBar, int index)
    {
        ContentTab tab = new ContentTab();
        tab.setXpathId(getTabsFromTabBar(tabBar).get(index));
        tab.setCode(StringUtils.substringAfter(tab.getXpathId(), DAOContentTab.TAB_ID_PREFIX));
        tab.setTitle(tabBar.getTitle());
        tab.setParentFqn(tabBar.getParentFqn());
        tab.setParentTabs(tabBar.getParentTabs());
        tab.setTabBarId(tabBar.getXpathId());
        return tab;
    }

    /**
     * Получить модель вкладки, на которой находится контент
     * <b>ВНИМАНИЕ! этот метод не загружает модель с сервера</b>
     * (у модели заполнены только поля: ID, UUID, PARENT_FQN)
     * @param model модель контента
     * @return вкладка
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static ContentTab getTabIdWithContent(ContentForm model)
    {
        ContentTab tab = new ContentTab();
        //Скрипт может вернуть null, если контент находится на вкладках "Форма добавления" и "Форма редактирования"
        String id = getContentModule().getTabIdWithContent(model.getParentFqn(), model.getXpathId(), model.getTab());
        tab.setXpathId(id);
        tab.setCode(id == null ? null : id.substring(TAB_ID_PREFIX.length(), id.length()));
        tab.setParentFqn(model.getParentFqn());
        return tab;
    }

    /**
     * Получить ID всех вкладок с указанной вкладки
     * @param tab модель вкладки
     * @return возвращает список id
     */
    public static List<String> getTabIds(ContentTab tab)
    {
        return Json.stringToList(getContentModule().getTabIds(tab.getParentFqn(), tab.getXpathId()));
    }

    /**
     * Получить ID всех вкладок с карточки объекта
     * @param fqnCode код метакласса
     * @return возвращает список ID
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static List<String> getTabIds(String fqnCode)
    {
        return Json.stringToList(getContentModule().getTabIds(fqnCode, "null"));
    }

    /**
     * Получить модель вкладки карточки метакласса
     * <b>ВНИМАНИЕ! этот метод не загружает модель с сервера</b>
     * @param fqn код метакласса
     * @param num номер вкладки (начинается с 0)
     * @return модель вкладки
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static ContentTab getTab(String fqn, int num)
    {
        List<String> tabs = getTabIds(fqn);
        return tabs == null || tabs.isEmpty() ? null : DAOContentTab.createTabById(fqn, tabs.get(num));
    }

    /**
     * Получить модель первой вкладки карточки метакласса
     * <b>ВНИМАНИЕ! этот метод не загружает модель с сервера</b>
     * @param fqn код метакласса
     * @return модель вкладки
     */
    //TODO: amerkulev какое-то старое наследие. Надо получить с сервера полноценную модель, а не конструировать
    // обрезок бесполезный. При случае переделать
    public static ContentTab getFirstTab(String fqn)
    {
        return getTab(fqn, 0);
    }

    /**
     * Получить ID всех вкладок с контента "Панель вкладок"
     * @param tabBar модель контента "Панель вкладок"
     * @return возвращает список id вкладок
     */
    public static List<String> getTabsFromTabBar(ContentForm tabBar)
    {
        return Json.stringToList(
                getContentModule().getTabsFromTabBar(tabBar.getParentFqn(), tabBar.getXpathId(), tabBar.getTab()));
    }

    /**
     * Получить ID панели инструментов для указанной вкладки
     * @param fqnCode код метакласса, в котором находится вкладка
     * @param tabId ID вкладки
     * @return ID панели инструментов
     */
    public static String getToolPanelIdByTabId(String fqnCode, String tabId)
    {
        return getContentModule().getToolPanelIdByTab(fqnCode, tabId, OBJECTCARD);
    }

    /**
     * Получить модель типа "Контент - Карточка объекта" для определенного метакласса
     * @param mc модель метакласса
     * @return модель контента
     */
    public static ContentForm getWindowContent(MetaClass mc)
    {
        ModelMap map = Json
                .stringToMapJson(getContentModule().getWindowContent(mc.getFqn(), MetaclassCardTab.OBJECTCARD.get()));

        ContentForm window = getContentByMap(map, mc.getFqn(), ContentType.WINDOW.getTypeTitle(),
                MetaclassCardTab.OBJECTCARD.get());

        if (Json.GSON.fromJson(window.getToolPanel(), ToolPanel.class).getToolBars().isEmpty())
        {
            DAOContentForm.setDefaultTools(window);
        }
        return window;
    }

    /**
     * Переместить контент на карточке на указанную позицию (номер по порядку от 1 до N, где N - количество
     * контентов на карточке; отсчет ведется сверху вниз)
     * @param model модель контента
     * @param position позиция, которую хочет занять контент
     */
    public static void move(ContentForm model, int position)
    {
        Map<String, Object> dataForScript = Maps.newHashMapWithExpectedSize(5);
        dataForScript.put("fqn", model.getParentFqn());
        dataForScript.put(FORM_CODE, model.getTab());
        dataForScript.put("code", model.getCode());
        dataForScript.put(POSITION, String.valueOf(position));
        String tabId = null;
        if (model.getParentTabs() != null)
        {
            List<String> tabs = Lists.newArrayList(model.getParentTabs().split(","));
            tabId = tabs.get(tabs.size() - 1);
        }
        dataForScript.put("tabId", StringUtils.substringAfter(tabId, TAB_ID_PREFIX));

        getContentModule().moveContent(dataForScript);
    }

    /**
     * Вернуть дефолтное значение для формирования заголовка на карточке объекта метакласса
     * @param mc модель метакласса
     */
    public static IRemoveOperation resetContentCaption(MetaClass mc)
    {
        return new RemoveOperation(SEContent.setContentCaption(mc.getFqn(), null));
    }

    /**
     * Сбросить настройки контентов в метаклассе на одной из форм(добавления, редактирования, карточка)
     * @param mc модель метакласса
     * @param tab форма
     */
    public static void resetContentSettings(MetaClass mc, MetaclassCardTab tab)
    {
        getContentModule().resetContentSettings(mc.getFqn(), tab.get());
    }

    /**
     * Сбросить параметры в настройках контента типа 'Отчет, печатная форма' до значения по умолчанию.
     * @param reportContent контент типа 'Отчет, печатная форма'.
     */
    public static void resetParametersReportContent(ContentForm reportContent)
    {
        editParametersReportContent(reportContent, null, true);
    }

    /**
     * Найти контент по его названию
     * @param content модель контента
     * @return код контента
     */
    public static String searchContentByTitle(ContentForm content)
    {
        String result = getContentModule().searchContentByTitle(content.getParentFqn(), content.getTitle(),
                content.getTab());
        return "null".equals(result) ? null : result;
    }

    /**
     * Найти контенты определенного типа на вкладке
     * @param content тип контента
     * @return ids - набор id контентов
     */
    public static Set<String> searchContentsByType(ContentForm content)
    {
        List<String> result = getContentModule().searchContent(content.getParentFqn(), content.getType(),
                content.getTab());

        Set<String> ids = new HashSet<>();
        for (String entry : result)
        {
            ids.add(String.format(CONTENT_PATTERN, content.getType(), entry));
        }
        return ids;
    }

    /**
     * Найти вкладку по ее названию
     * @param tab модель вкладки
     * @return code вкладки
     */
    public static String searchTabByTitle(ContentTab tab)
    {
        String result = getContentModule().searchContentByTitle(tab.getParentFqn(), tab.getTitle(),
                tab.getFormCode());
        return "null".equals(result) ? null : result;
    }

    /**
     * Установить значение атрибута для формирования заголовка объекта на карточке объекта метакласса
     * @param mc модель метакласса
     * @param attr модель атрибута для формирования заголовка может быть null, в таком случае установится
     * дефолтное значение "Тип + Наименование"
     */
    public static void setContentCaption(MetaClass mc, Attribute attr)
    {
        getContentModule();
        ScriptRunner script = new ScriptRunner(true,
                SEContent.setContentCaption(mc.getFqn(), attr == null ? null : attr.getCode()));
        script.runScript();
        Cleaner.push(resetContentCaption(mc));
    }

    /**
     * Установить контенту id родительской вкладки
     * @param model модель контента
     * @param parentTab - id вкладки
     */
    public static void setParent(ContentForm model, ContentTab parentTab)
    {
        if (parentTab != null)
        {
            if (parentTab.getParentTabs() != null)
            {
                List<String> parents = Lists.newArrayList(parentTab.getParentTabs().split(","));
                parents.add(parentTab.getXpathId());
                model.setParentTabs(StringUtils.join(parents, ","));
            }
            else
            {
                model.setParentTabs(parentTab.getXpathId());
            }
        }
        else
        {
            model.setParentTabs(getTabIdWithContent(model).getXpathId());
        }
    }

    /**
     * Получить id контента вкладки
     * @param model модель вкладки
     * @return возвращает id контента вкладки
     */
    static String getTabLayoutId(ContentTab model)
    {
        return getContentModule().getTabLayoutId(model.getParentFqn(), model.getXpathId());
    }

    private static ContentForm getContentByMap(ModelMap map, String parentFqn, String title, String tab)
    {
        if (!map.isEmpty())
        {
            ContentForm content = new ContentForm();
            String contentType = map.get("contentType");
            content.setCode(map.get("uuid"));
            content.setParentFqn(parentFqn);
            content.setType(contentType);
            content.setTypeTitle(ContentType.getContentType(map.get("contentType")).getTypeTitle());
            content.setTab(tab);
            content.setTitle(title);
            content.setPosition(map.get(POSITION));
            content.setRecepient(map.get("attrChain"));
            content.setPresentation(map.get(PRESENTATION));
            content.setAttributeGroupCode(map.get("attributeGroupCode"));
            content.setShowAttrDescriptionOnAddForm(Boolean.parseBoolean(map.get("showAttrDescriptionOnAddForm")));
            content.setShowAttrDescriptionOnEditForm(Boolean.parseBoolean(map.get("showAttrDescriptionOnEditForm")));
            content.setShowTitle(map.get("showCaption"));
            content.setShowRelatedWithNested(Boolean.valueOf(map.get("showRelatedWithNested")));
            content.setShowLinked(Boolean.valueOf(map.get("showLinkedObjects")));
            content.setPagingPosition(map.get("pagingSettings"));
            content.setTitleRelatedAddForm(map.get("titleRelatedAddForm"));
            content.setCasesCode(map.get("case"));
            content.setExists(true);
            content.setXpathId("gwt-debug-" + contentType + "." + content.getCode());
            if (map.get(TAB_BAR) != null)
            {
                content.setTabBar(new ContentTabBar(Json.GSON.fromJson(map.get(TAB_BAR), ModelMap.class)));
            }
            content.setToolPanel(Json.GSON.fromJson(map.get(TOOL_PANEL), ToolPanel.class));
            content.setMassToolPanel(Json.GSON.fromJson(map.get("massOperationsPanel"), ToolPanel.class));
            return content;
        }

        return null;
    }

    private DSLContent()
    {
    }
}
