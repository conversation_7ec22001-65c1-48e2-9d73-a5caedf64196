package ru.naumen.selenium.casesutil.model.adminprofiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * Матрица маркеров доступа в профиле администратора.
 *
 * <AUTHOR>
 * @since 29.11.2024
 */
public class AdminProfileAccessMarkerMatrix
{
    public static final Gson GSON = new GsonBuilder().serializeNulls().create();

    public enum AdminProfileAccessMarker
    {
        /**
         * Управление схемой базы данных
         */
        DATABASE_MANAGEMENT("databaseManagement"),

        /**
         * Пользовательский интерфейс
         */
        USER_INTERFACE("userInterface"),

        /**
         * Права доступа
         */
        ACCESS_RIGHTS("accessRights"),

        /**
         * Жизненный цикл
         */
        WORKFLOW("workflow"),

        /**
         * Скриптовые настройки
         */
        SCRIPTS("scripts"),

        /**
         * Настройки поиска
         */
        SEARCH_SETTINGS("searchSettings"),

        /**
         * Действия по событиям
         */
        EVENT_ACTIONS("eventActions"),

        /**
         * Системные справочники
         */
        SYSTEM_CATALOGS("systemCatalogs"),

        /**
         * Пользовательские справочники
         */
        USER_CATALOGS("userCatalogs"),

        /**
         * Группы пользователей
         */
        SECURITY_GROUP("securityGroup"),

        /**
         * Роли
         */
        SECURITY_ROLE("securityRole"),

        /**
         * Каталоги
         */
        CATALOGS("catalogs"),

        /**
         * Счётчики времени
         */
        TIMERS("timers"),

        /**
         * Метки
         */
        TAGS("tags"),

        /**
         * Параметры запросов
         */
        SC_PARAMETERS("scParameters"),

        /**
         * Эскалации
         */
        ESCALATIONS("escalations"),

        /**
         * Сложные списки
         */
        ADV_LISTS("advLists"),

        /**
         * Предварительный просмотр файлов
         */
        FILE_PREVIEW("filePreview"),

        /**
         * Политика безопасности
         */
        SECURITY_POLICY("securityPolicy"),

        /**
         * Прочие настройки
         */
        OTHER_SETTINGS("otherSettings"),

        /**
         * Список суперпользователей и технологов
         */
        SUPER_USERS("superUsers"),

        /**
         * Отслеживание изменений в режиме реального времени
         */
        CHANGE_TRACKING_SETTINGS("changeTrackingSettings"),

        /**
         * Облегченный интерфейс настройки
         */
        ADMIN_LITE_SETTINGS("adminLiteSettings"),

        /**
         * Интерфейс и навигация
         */
        INTERFACE_AND_NAVIGATION("interfaceAndNavigation"),

        /**
         * Почта
         */
        MAIL("mail"),

        /**
         * Очереди
         */
        QUEUES("queues"),

        /**
         * Подключения и конфигурации
         */
        CONNECTIONS_AND_CONFIGURATIONS("connectionsAndConfigurations"),

        /**
         * Файлы кастомизации
         */
        CUSTOMIZATION_FILES("customizationFiles"),

        /**
         * Работа со встроенными приложениями
         */
        EMBEDDED_APPLICATIONS("embeddedApplications"),

        /**
         * Управление шаблонами
         */
        TEMPLATES("templates"),

        /**
         * Профили администрирования
         */
        ADMINISTRATION_PROFILES("administrationProfiles"),

        /**
         * Планировщик
         */
        SCHEDULER("scheduler"),

        /**
         * Структуры
         */
        STRUCTURES("structures"),

        /**
         * CTI
         */
        CTI("cti"),

        /**
         * Управление привязкой профилей администрирования
         */
        ADMINISTRATION_PROFILES_MANAGEMENT("administrationProfilesManagement"),

        /**
         * Интерфейс администратора
         */
        ADMINISTRATOR_INTERFACE("administratorInterface"),

        /**
         * Интерфейс оператора
         */
        OPERATOR_INTERFACE("operatorInterface"),

        /**
         * Вход под сотрудником
         */
        LOGIN_AS_EMPLOYEE("loginAsEmployee"),

        /**
         * Управление профилями на формах
         */
        MANAGE_PROFILES_ON_FORMS("manageProfilesOnForms"),

        /**
         * Переиндексация
         */
        REINDEXING("reindexing"),

        /**
         * Архивирование метакласса
         */
        ARCHIVING_METACLASS("archivingMetaclass"),

        /**
         * Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм
         */
        IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES("importExportMetainfoAndReportTemplates"),

        /**
         * Загрузка-выгрузка встроенных приложений
         */
        IMPORT_EXPORT_EMBEDDED_APPLICATIONS("importExportEmbeddedApplications"),

        /**
         * Логи приложения
         */
        APPLICATION_LOGS("applicationLogs"),

        /**
         * Логи действий технолога
         */
        ADMIN_LOGS("adminLogs"),

        /**
         * Выгрузка информации о системе
         */
        EXPORT_SYSTEM_INFORMATION("exportSystemInformation"),

        /**
         * Выгрузка статистики
         */
        EXPORT_STATISTICS("exportStatistics"),

        /**
         * Загрузка-выгрузка лицензионных файлов
         */
        IMPORT_EXPORT_LICENSE_FILES("importExportLicenseFiles"),

        /**
         * Загрузка-выгрузка расширений настроек поля ввода с маской
         */
        IMPORT_EXPORT_EXTENSIONS_FOR_MASKED_SETTINGS("importExportExtensionsForMaskedSettings"),

        /**
         * Загрузка сертификатов
         */
        IMPORT_CERTIFICATES("importCertificates"),

        /**
         * Загрузка дополнительных библиотек
         */
        IMPORT_ADDITIONAL_LIBRARIES("importAdditionalLibraries"),

        /**
         * Список активных пользователей
         */
        ACTIVE_USERS("activeUsers"),

        /**
         * Блокировка входа на время технических работ
         */
        MAINTENANCE("maintenance"),

        /**
         * Выпадающие списки выбора
         */
        DROPDOWN_SELECTION_LISTS("dropdownSelectionLists"),

        /**
         * Локализация
         */
        LOCALIZATION("localization"),

        /**
         * Мобильные приложения
         */
        MOBILE_APPLICATIONS("mobileApplications"),

        /**
         * Консоль
         */
        CONSOLE("console"),

        /**
         * Выполнение скриптов из консоли
         */
        EXECUTE_CONSOLE_SCRIPTS("executeConsoleScripts"),

        /**
         * Комплекты метаинформации
         */
        SETTINGS_SETS("settingsSets"),

        /**
         * Выполнение задач планировщика
         */
        RUN_SCHEDULER_TASK("runSchedulerTask");

        private final String code;

        AdminProfileAccessMarker(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    /**
     * Типы действий над объектом метаинформации в интерфейсе администратора.
     * <AUTHOR>
     * @since 09.12.2024
     */
    public enum PermissionType
    {
        /**
         * Создание
         */
        CREATE("create"),

        /**
         * Редактирование
         */
        EDIT("edit"),

        /**
         * Удаление
         */
        DELETE("delete"),

        /**
         * Просмотр
         */
        VIEW("view"),

        /**
         * Полный доступ, разрешающий все действия. <br>
         * В матрице маркеров доступа называется "Доступ".
         */
        ALL("all");

        private final String code;

        PermissionType(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    private final Map<String, Set<String>> matrix = new HashMap<>();

    public Map<String, Set<String>> getMatrix()
    {
        return matrix;
    }

    /**
     * Добавить право на действие по маркеру доступа
     *
     * @param accessMarker маркер доступа
     * @param permissionType тип действия
     */
    public void addAccessMarkerPermission(AdminProfileAccessMarker accessMarker, PermissionType permissionType)
    {
        matrix.computeIfAbsent(accessMarker.getCode(), k -> new HashSet<>()).add(permissionType.getCode());
    }

    /**
     * Удалить право на действие по маркеру доступа
     *
     * @param accessMarker маркер доступа
     * @param permissionType тип действия
     */
    public void removeAccessMarkerPermission(AdminProfileAccessMarker accessMarker, PermissionType permissionType)
    {
        Set<String> enabledPermissionActions = matrix.get(accessMarker.getCode());
        enabledPermissionActions.remove(permissionType.getCode());
        matrix.put(accessMarker.getCode(), enabledPermissionActions);
    }

    /**
     * Добавить права на действия по маркеру доступа
     *
     * @param accessMarker маркер доступа
     * @param permissionTypes типы действий
     */
    public void addAccessMarkerPermissions(AdminProfileAccessMarker accessMarker, PermissionType... permissionTypes)
    {
        matrix.computeIfAbsent(accessMarker.getCode(), k -> new HashSet<>())
                .addAll(Arrays.stream(permissionTypes).map(PermissionType::getCode).collect(Collectors.toSet()));
    }

    /**
     * Удалить права на все действия по маркеру доступа
     *
     * @param accessMarker маркер доступа
     */
    public void removeAccessMarkerPermissions(AdminProfileAccessMarker accessMarker)
    {
        matrix.remove(accessMarker.getCode());
    }

    /**
     * Добавить права на действие по маркерам доступа
     *
     * @param permissionType тип действия
     * @param accessMarkers маркеры доступа
     */
    public void addAccessMarkersPermission(PermissionType permissionType, AdminProfileAccessMarker... accessMarkers)
    {
        Arrays.stream(accessMarkers).forEach(accessMarker -> matrix
                .computeIfAbsent(accessMarker.getCode(), k -> new HashSet<>())
                .add(permissionType.getCode()));
    }

    /**
     * Удалить права на действие по маркерам доступа
     *
     * @param permissionType тип действия
     * @param accessMarkers маркеры доступа
     */
    public void removeAccessMarkersPermission(PermissionType permissionType, AdminProfileAccessMarker... accessMarkers)
    {
        Arrays.stream(accessMarkers)
                .forEach(accessMarker -> removeAccessMarkerPermission(accessMarker, permissionType));
    }

    /**
     * Добавить право на доступ по маркеру доступа
     * {@link AdminProfileAccessMarker#ADMINISTRATOR_INTERFACE "Интерфейс администратора"}
     */
    public void addAdministrationInterfacePermission()
    {
        Set<String> enabledPermissionActions = new HashSet<>();
        enabledPermissionActions.add(PermissionType.ALL.getCode());

        matrix.put(AdminProfileAccessMarker.ADMINISTRATOR_INTERFACE.getCode(), enabledPermissionActions);
    }

    /**
     * Удалить все права из матрицы.
     */
    public void removeAllPermissions()
    {
        matrix.clear();
    }

    /**
     * @return строковое представление матрицы маркеров доступа в виде json
     */
    public String asJsonString()
    {
        return GSON.toJson(matrix);
    }
}