package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.IAttributeContext;
import ru.naumen.selenium.casesutil.role.ScClientRole;
import ru.naumen.selenium.casesutil.role.ScEmpOfClientOURole;
import ru.naumen.selenium.casesutil.role.ScLastRespEmplRole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamMemberRole;
import ru.naumen.selenium.casesutil.role.ScRespEmployeeRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamMemberRole;
import ru.naumen.selenium.casesutil.role.ScSolvEmployeeRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Необходимый контекст для тестирования прав, связанных с атрибутами, в классе Запрос
 * <AUTHOR>
 * @since 13.02.2013
 * <AUTHOR>
 */
public class ScAttributeContext extends AbstractRightContext implements IAttributeContext
{
    private MetaClass scCase;
    private Bo agreement;

    private CatalogItem timeZoneItem;
    private Bo client;
    private Bo responsibleEmpl;
    private Bo responsibleTeam;
    private Bo sc;

    private Attribute attribute;

    private ContentForm propertyList;

    public ScAttributeContext()
    {
        super();
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, scCase, roles);
        //Добавляем связь между ролями и контекстом прав
        Map<String, Object> params = new HashMap<>();
        params.put(ScClientRole.SC_MODEL, sc);
        params.put(ScEmpOfClientOURole.SC_MODEL, sc);
        params.put(ScRespEmpTeamLeaderRole.TEAM_MODEL, responsibleTeam);
        params.put(ScRespEmpTeamLeaderRole.EMPL_MODEL, responsibleEmpl);
        params.put(ScRespEmpTeamLeaderRole.OBJECT_MODEL, sc);
        params.put(ScRespTeamLeaderRole.TEAM_MODEL, responsibleTeam);
        params.put(ScRespTeamLeaderRole.OBJECT_MODEL, sc);
        params.put(ScRespTeamMemberRole.TEAM_MODEL, responsibleTeam);
        params.put(ScRespTeamMemberRole.OBJECT_MODEL, sc);
        params.put(ScRespEmpTeamMemberRole.TEAM_MODEL, responsibleTeam);
        params.put(ScRespEmpTeamMemberRole.EMPL_MODEL, responsibleEmpl);
        params.put(ScRespEmpTeamMemberRole.OBJECT_MODEL, sc);
        params.put(ScRespEmployeeRole.TEAM_MODEL, responsibleTeam);
        params.put(ScRespEmployeeRole.OBJECT_MODEL, sc);
        params.put(ScSolvEmployeeRole.SC_MODEL, sc);
        params.put(ScSolvEmployeeRole.TEAM_MODEL, responsibleTeam);
        params.put(ScSolvEmployeeRole.SC_CASE_MODEL, scCase);
        params.put(ScLastRespEmplRole.OBJECT_MODEL, sc);
        params.put(ScLastRespEmplRole.TEAM_MODEL, responsibleTeam);
        params.put(ScLastRespEmplRole.EMPL_MODEL, responsibleEmpl);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    public Attribute getAttribute()
    {
        return attribute;
    }

    @Override
    public Bo getObject()
    {
        return sc;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return scCase;
    }

    @Override
    public ContentForm getPropertyList()
    {
        return propertyList;
    }

    @Override
    protected void prepareImmutableData()
    {
        GUILogon.asSuper();
        //Создаем типы объектов
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        scCase = DAOScCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(ouCase, employeeCase, scCase, teamCase);
        //Создаем данные, необходимые для запроса
        timeZoneItem = DAOCatalogItem.createTimeZone();
        DSLCatalogItem.add(timeZoneItem);

        agreement = SharedFixture.agreement();
        client = DAOOu.create(ouCase);
        Bo responsibleOu = DAOOu.create(ouCase);
        responsibleTeam = DAOTeam.create(teamCase);
        DSLBo.add(client, responsibleOu, responsibleTeam);

        responsibleEmpl = DAOEmployee.create(employeeCase, responsibleOu, true);
        responsibleEmpl.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(responsibleEmpl);

        DSLTeam.addEmployees(responsibleTeam, responsibleEmpl);
        DSLAgreement.addToRecipients(SharedFixture.agreement(), client);

        //Создаем атрибут и контент с этим атрибутом
        attribute = DAOAttribute.createString(scCase.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attribute);

        propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);
    }

    @Override
    protected void prepareMutableData()
    {
        sc = DAOSc.create(scCase, client, agreement, timeZoneItem);
        DSLBo.add(sc);
    }
}
