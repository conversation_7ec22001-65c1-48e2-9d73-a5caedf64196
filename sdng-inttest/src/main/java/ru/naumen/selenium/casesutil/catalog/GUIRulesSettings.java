package ru.naumen.selenium.casesutil.catalog;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.ElementNotVisibleException;
import org.openqa.selenium.WebElement;

import com.google.common.base.Preconditions;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.util.StringUtils;

/**
 * Класс для работы с карточкой системного справочника "Таблицы соответствий"
 * <AUTHOR>
 * @since 06/08/2019
 */
public class GUIRulesSettings extends CoreTester
{
    public static final String SYNC_HISTORY = "syncHistory";
    public static final String BTN_ATTRS = "gwt-debug-attrs";

    /**
     * Поместить в архив элемент справочника "Таблицы соответствий"
     * Для работы метода необходимо находится на карточке таблицы соответствий
     */
    public static void archive()
    {
        if (!tester.waitAppear(WaitTool.WAIT_TIME_APPEAR, GUICatalogItem.ARCHIVE_CATALOG_ITEM_BUTTON))
        {
            tester.click(GUICatalog.X_BUTTON_EXPAND);
        }
        tester.click(GUICatalogItem.ARCHIVE_CATALOG_ITEM_BUTTON);
        GUIForm.confirmArchive();
    }

    /**
     * Проверить отсутствие Таблицы соответствия в списке таблиц соответствий\Элемента справочника в справочнике
     * Таблицы соответствий
     * @param code код Таблицы соответствия, отсутствие которой проверяется
     */
    public static void assertAbsenceTableRulesInList(String code)
    {
        GUITester.assertExists(String.format(GUIXpath.Any.ANY_RULES_SETTINGS_COLOR, code), false,
                "Указанная таблица соответствия с кодом " + code
                + " присутствует в списке схем, но ожидалось, что она будет отсутствовать");
    }

    /**
     * Проверяет количество элементов в списке истории синхронизаций
     * @param expectedCount ожидаемое количество элементов
     */
    public static void assertHistoryListCount(int expectedCount)
    {
        List<WebElement> elems = tester.findElements(GUICatalogItem.HISTORY_TAB_ADVLIST_ELEMS,
                GUIRulesSettings.SYNC_HISTORY);
        Assert.assertEquals("Количество элементов в списке истории синхронизаций не совпадает с ожидаемым",
                expectedCount, elems.size());
    }

    /**
     * Проверяет значение параметра "Объекты"
     * @param expected ожидаемое значение
     */
    public static void assertObjectsValue(String expected)
    {
        Assert.assertEquals("Полученное значение атрибута \"Объекты\" не совпало с ожидаемым.", expected,
                tester.getText(Any.CLASS_VALUE));
    }

    /**
     * Проверить строку в таблице соотвествий
     * @param rowId строки (может быть "")
     * @param values набор пар: код атрибута в строке - значение атрибута
     */
    public static void assertRowIdInRSItem(String rowId, String... values)
    {
        String xpath = GUIRulesSettings.getRSRowXpath(rowId, values);
        GUITester.assertPresent(xpath, "Отсутствует строка в таблице соотвествий, xpath: " + xpath);
    }

    /**
     * Проверить строку в таблице соотвествий
     * @param values набор пар: код атрибута в строке - значение атрибута
     */
    public static void assertRowInRSItem(String... values)
    {
        assertRowIdInRSItem("", values);
    }

    /**
     * Проверить строку в таблице соотвествий (для случая, когда в строке есть атрибут типа набор элементов справочника)
     * @param rowId идентификатор строки таблицы соответствий
     * @param attrCode код атрибута типа набор элементов справочника в строке
     * @param values значения атрибута типа набор элементов справочника
     */
    public static void assertRowInRSItem(String rowId, String attrCode, Set<String> values)
    {
        String xpath = GUIRulesSettings.getRsRowXpath(rowId, attrCode, values);
        GUITester.assertPresent(xpath, "Отсутствует строка в таблице соотвествий, xpath: " + xpath);
    }

    /**
     * Проверить отсутствие строки в таблице соответствий
     * @param codeAttr - код определяющего атрибута
     * @param value - значение определяющего атрибута
     */
    public static void assertRsRowAbsenceInList(String codeAttr, String value)
    {
        String msg = String.format("Найдена строка со значением %s с кодом %s", value, codeAttr);
        String xpath = String.format(GUIXpath.Div.ANY_AND_ANY_TEXT, codeAttr, value);
        GUITester.assertAbsent(xpath, msg);
    }

    /**
     * Проверить присутствие строки в таблице соответствий
     * @param codeAttr - код определяющего атрибута
     * @param value - значение определяющего атрибута
     */
    public static void assertRsRowPresenceInList(String codeAttr, String value)
    {
        String msg = String.format("Не найдена строка со значением %s с кодом %s", value, codeAttr);
        String xpath = String.format(GUIXpath.Div.ANY_AND_ANY_TEXT, codeAttr, value);
        GUITester.assertPresent(xpath, msg);
    }

    /**
     * Проверить наличие кнопок
     * @param buttonIds id кнопок
     * @param present должны ли присутствовать или отсутствовать (true - должен присутствовать, false - отсутствовать)
     */
    public static void assertButtonsPresent(boolean present, String... buttonIds)
    {
        for (String buttonId : buttonIds)
        {
            Assertions.assertTrue(present
                            ? tester.waitAppear(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN, BTN_ATTRS, buttonId)
                            : tester.waitDisappear(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN, BTN_ATTRS,
                                    buttonId),
                    present
                            ? String.format("Кнопка с кодом %s не отображается.", buttonId)
                            : String.format("Кнопка с кодом %s отображается.", buttonId));
        }
    }

    /**
     * Нажать добавить строку в таблице соответствий
     */
    public static void clickAddRSRow()
    {
        tester.click(GUIXpath.Div.ADD);
        GUIForm.assertDialogAppear(
                "Форма добавления строки элемента справочника \"Таблицы соответствий\" не появилась.");
    }

    /**
     * Нажать иконку удаления строки в справочнике таблицы соответствий
     * @param values набор пар: код атрибута в строке - значение атрибута(для определения строки), может отсутствовать,
     * тогда будет нажата первая попавшаяся иконка
     */
    public static void clickRSDeleteRow(String... values)
    {
        String editXpath = GUIRulesSettings.getRSRowXpath("", values) + GUIXpath.Span.DELETE_ICON;
        try
        {
            tester.click(editXpath, "");
        }
        catch (ElementNotVisibleException e)
        {
            tester.click(editXpath, "Hover");
        }
    }

    /**
     * Нажать иконку редактирования строки в справочнике таблицы соответствий
     * @param values набор пар: код атрибута в строке - значение атрибута(для определения строки), может отсутствовать,
     * тогда будет нажата первая попавшаяся иконка
     */
    public static void clickRSEditRow(String... values)
    {
        String editXpath = GUIRulesSettings.getRSRowXpath("", values) + GUIXpath.Span.EDIT_ICON;
        try
        {
            tester.click(editXpath, "");
        }
        catch (ElementNotVisibleException e)
        {
            tester.click(editXpath, "Hover");
        }
    }

    /**
     * Клик на элемент истории синхронизаций
     * @param elemPos позиция элемента с списке истории синхронизаций, начиная с 0
     */
    public static void clickSyncHistoryElem(int elemPos)
    {
        tester.findElements(GUICatalogItem.HISTORY_TAB_ADVLIST_ELEMS, GUIRulesSettings.SYNC_HISTORY).get(elemPos)
                .click();
    }

    /**
     * Выгружает таблицу соответствий на ее карточке
     * @param item модель элемента справочника "Таблицы соответствий"
     * @return выгруженный архив
     */
    public static File exportRS(CatalogItem item)
    {
        tester.click(GUICatalog.X_BUTTON_EXPORT);
        return GUIAdmin.waitExport(item.getCode(), "zip");
    }

    /**
     * Получить xpath до строки в таблице соотвествий, если в столбце элемент справочника(для случая, когда в строке
     * есть атрибут типа набор элементов справочника)
     * @param rowId идентификатор строки таблицы соответствий
     * @param attrCode код атрибута типа набор элементов справочника в строке
     * @param values значения атрибута типа набор элементов справочника
     * @return xpath до строки в таблице соотвествий
     */
    public static String getRsRowXpath(String rowId, String attrCode, Set<String> values)
    {
        String rowXpath = "";
        if (!values.isEmpty())
        {
            String x_row = "//tr[contains(@id, 'Row-%s') and %s ]";
            String catItems_td = ".//td[.//div[@id='%s']/../..//*[contains(text(),'%s')]]";
            Set<String> catItems_xpaths = new HashSet<>();
            for (String value : values)
            {
                catItems_xpaths.add(String.format(catItems_td, attrCode, value));
            }

            rowXpath = String.format(x_row, rowId, StringUtils.join(catItems_xpaths, " and "));
        }
        return rowXpath;
    }

    /**
     * Получить xpath до строки в таблице соотвествий
     * @param rowId идентификатор строки таблицы соответствий
     * @param values набор пар: код атрибута в строке - значение атрибута(для определения строки), может отсутствовать,
     * тогда будет возвращена пустая строка
     * @return xpath до строки в таблице соотвествий
     */
    public static String getRSRowXpath(String rowId, String... values)
    {
        Preconditions.checkArgument(values.length % 2 == 0);
        String rowXpath = "";
        if (values.length > 0)
        {
            String x_row = "//tr[contains(@id, 'Row-%s') and %s]";
            String x_td = ".//td[.//div[@id='%s']/../..//*[text()='%s']]";
            Set<String> td_xpaths = new HashSet<>();
            for (int i = 0; i < values.length; i += 2)
            {
                td_xpaths.add(String.format(x_td, values[i], values[i + 1]));
            }
            rowXpath = String.format(x_row, rowId, StringUtils.join(td_xpaths, " and "));
        }
        return rowXpath;
    }

    /**
     * Осуществляет переход на нужную вкладку в карточке таблицы соответствий
     * @param id идентификатор вкладки на карточке таблицы соответствий
     */
    public static void goToRulesSettingsCard(String id)
    {
        tester.click(GUIXpath.Any.ANY, id);
    }

    /**
     * Импортировать таблицу соответствий.
     * @param file файл для импорта
     * @param replace true, если необходимо установить флажок "Заменить настройки объекта"
     */
    public static void importRS(File file, boolean replace)
    {
        tester.click(GUICatalog.X_BUTTON_IMPORT);
        GUIFileAdmin.uploadFile(GUIXpath.Any.ANY_VALUE, file.getAbsolutePath(), "fileToImport");
        if (replace)
        {
            tester.setCheckbox(GUIXpath.InputComplex.ANY_VALUE, true, "rewriteMode");
        }
        GUIForm.applyModalForm();
    }

    /**
     * Импортировать таблицу соответствий с ожиданием ошибки.
     * @param file файл для импорта
     * @param replace true, если необходимо установить флажок "Заменить настройки объекта"
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param args объекты для подстановки в шаблон сообщения об ошибке
     */
    public static void importRSExpectError(File file, boolean replace, String expectedMessage, Object... args)
    {
        tester.click(GUICatalog.X_BUTTON_IMPORT);
        GUIFileAdmin.uploadFile(GUIXpath.Any.ANY_VALUE, file.getAbsolutePath(), "fileToImport");
        if (replace)
        {
            tester.setCheckbox(GUIXpath.InputComplex.ANY_VALUE, true, "rewriteMode");
        }
        GUIForm.applyFormAssertError(String.format(expectedMessage, args));
    }
}
