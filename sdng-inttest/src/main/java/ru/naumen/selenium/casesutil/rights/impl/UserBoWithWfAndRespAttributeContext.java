package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.IAttributeContext;
import ru.naumen.selenium.casesutil.role.UserWfAndRespEmployeeRole;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Необходимый контекст для тестирования прав из блока "Действия с атрибутами" для пользовательского класса с ЖЦ и
 * Ответственным
 * <AUTHOR>
 * @since 21.04.2016
 */
public class UserBoWithWfAndRespAttributeContext extends AbstractRightContext implements IAttributeContext
{
    private MetaClass userClass;
    private MetaClass userCase;
    private Bo user;
    private Bo respTeam;

    private Attribute attribute;

    private ContentForm propertyList;

    public UserBoWithWfAndRespAttributeContext()
    {
        super();
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, userClass, roles);
        //Добавляем связь между ролями и контекстом прав
        Map<String, Object> params = new HashMap<>();
        params.put(UserWfAndRespEmployeeRole.TEAM_MODEL, respTeam);
        params.put(UserWfAndRespEmployeeRole.USER_CASE, userCase);
        params.put(UserWfAndRespEmployeeRole.OBJECT_MODEL, user);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    public Attribute getAttribute()
    {
        return attribute;
    }

    @Override
    public Bo getObject()
    {
        return user;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return userCase;
    }

    @Override
    public ContentForm getPropertyList()
    {
        return propertyList;
    }

    @Override
    protected void prepareImmutableData()
    {
        userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(userClass, userCase, teamCase);

        respTeam = DAOTeam.create(teamCase);
        DSLBo.add(respTeam);

        //Создаем атрибут и контент с этим атрибутом
        attribute = DAOAttribute.createString(userCase.getFqn());
        DSLAttribute.add(attribute);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attribute);

        propertyList = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(propertyList);
    }

    @Override
    protected void prepareMutableData()
    {
        //Создаем объекты
        user = DAOUserBo.create(userCase);
        DSLBo.add(user);
    }
}