package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD
import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.junit.Assert;
import org.openqa.selenium.NoSuchElementException;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.content.advlist.AdvListEntry;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.CollectionUtils;

/**
 * Утилитарные методы для проверок массовых операций advList-а через интерфейс
 * <AUTHOR>
 * @since 17.04.2015
 */
public class AGUIAdvListMassOperation extends GUIAdvlist
{
    public AGUIAdvListMassOperation(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверить, что у advlist'а отсутствуют массовые операции
     */
    public void absenceAll()
    {
        GUITester.assertExists(getContentXpath() + MASS_LINKS, false, "В контенте присутствуют массовые операции.");
    }

    /**
     * Проверить отсутствие массовых операций в advlist'е (по коду операции)
     * @param operations набор проверяемых операций, {@link MassOperation}
     */
    public void absenceByCode(MassOperation... operations)
    {
        String msg = "Присутствует массовая операция: ";
        for (MassOperation operation : operations)
        {
            GUITester.assertExists(getContentXpath() + String.format(MASS_LINK, operation.getCode()), false, msg
                                                                                                             + operation.getTitle());
        }
    }

    /**
     * Проверить по названию отсутствие ссылки быстрой смены статуса
     * @param title название ссылки
     */
    public void fastChangeStateAbsence(String title)
    {
        String pathToLink = String.format(getContentXpath() + MASS_LINK_BY_TITLE, title);
        GUITester.assertExists(pathToLink, false, "Ссылка быстрой смены присутствует: " + title);
    }

    /**
     * Проверить присутствие ссылки быстрой смены статуса в указанный статус с указанным названием
     * @param status модель статуса
     * @param title название ссылки
     */
    public void fastChangeStatePresence(BoStatus status, String title)
    {
        String pathToLink = String.format(getContentXpath() + GUIAdvListXpath.MASS_FAST_CHANGE_STATE,
                MassOperation.TRANSACTION_CHANGE_STATE.getCode(), status.getCode());
        try
        {
            tester.findNumberDisplayedElements(pathToLink, 1);
        }
        catch (NoSuchElementException e)
        {
            Assert.fail("Присутствует не одна ссылка с кодом: " + status.getCode());
        }
        GUITester.assertTextPresentWithMsg(pathToLink, title, "Полученное название перехода не совпало с ожидаемым.");
    }

    /**
     * Проверить заголовок панели массовых операций
     * @param expected ожидаемый заголовок панели массовых операций
     */
    public void labelPanel(String expected)
    {
        GUITester.assertTextPresentWithMsg(getContentXpath() + MASS_LABEL_PANEL, expected,
                "Полученный заголовок панели массовых операций не совпал с ожидаемым.");
    }

    /**
     * Проверить наличие массовых операций в advlist'е (по коду операции)
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param operations набор проверяемых операций, {@link MassOperation}
     */
    @SuppressFBWarnings("DLS_DEAD_LOCAL_STORE")
    public void presenceByCode(boolean order, boolean match, MassOperation... operations)
    {
        List<String> expected = Lists.transform(Lists.newArrayList(operations), (MassOperation input) ->
        {
            return input == null ? null : input.getCode();
        });

        List<String> actual = new ArrayList<>();
        List<String> operationIds = new ArrayList<>();
        boolean isContinue = true;
        long currentTime = System.currentTimeMillis();
        while (isContinue && System.currentTimeMillis() - currentTime < WAIT_TIME * 1000)
        {
            operationIds = GUITester.getAttributePropertyElements(getContentXpath() + MASS_LINKS, "id");
            actual = Lists.transform(operationIds, (String input) ->
            {
                Matcher m = Pattern.compile("gwt-debug-(\\w*)\\..*").matcher(input);
                if (m.find())
                {
                    return m.group(1);
                }
                else
                {
                    throw new ErrorInCodeException("Из id не удалось получить код массовой операции.");
                }
            });

            if (match)
            {
                isContinue = !(order ? expected : Sets.newHashSet(expected)).equals(order ? actual
                        : Sets
                                .newHashSet(actual));
            }
            else
            {
                isContinue = !(order ? CollectionUtils.orderContains(expected, actual) : actual.containsAll(expected));
            }
        }

        String msg = "Полученный список массовых операций не совпал с ожидаемым. Ожидался: %s, получен %s";
        Assert.assertFalse(String.format(msg, expected, actual), isContinue);
    }

    /**
     * Проверить наличие массовых операций в advlist'е
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param titles заголовки проверяемых операций
     */
    public void presenceByTitle(boolean order, boolean match, String... titles)
    {
        GUITester.assertFindElements(getContentXpath() + MASS_LINKS, Lists.newArrayList(titles), order, match);
    }

    /**
     * Проверить отсутствие массовой операции
     * @param title наименование операции
     */
    public void absenceByTitle(String title)
    {
        String msg = "Присутствует массовая операция: ";
        GUITester.assertExists(getContentXpath() + String.format(MASS_LINK, title), false, msg
                                                                                           + title);
    }

    /**
     * Проверить наличие массовых операций в advlist'е (по названию операции на полное совпадение с учетом порядка)
     * @param operations набор проверяемых операций, {@link MassOperation}
     */
    public void presenceByTitle(MassOperation... operations)
    {
        GUITester.assertFindElements(getContentXpath() + MASS_LINKS,
                Lists.transform(Lists.newArrayList(operations), (MassOperation input) ->
                {
                    return input == null ? null : input.getTitle();
                }), true, true);
    }

    /**
     * Проверить, что элементы выбраны в списке
     * @param objects модели объектов - строки таблицы advlist'а
     */
    public void selected(AdvListEntry... objects)
    {
        for (AdvListEntry object : objects)
        {
            String message = String.format("Элемент %s не выбран в спике или не существует", object.entryId());
            String xpath = String.format(getContentXpath() + MASS_ROW_SELECTED_CHECKBOX, object.entryId());
            GUITester.assertExists(xpath, true, message);
        }
    }

    /**
     * Проверить, что элементы не выбраны в списке
     * @param objects модели объектов - строки таблицы advlist'а
     */
    public void unselected(AdvListEntry... objects)
    {
        for (AdvListEntry object : objects)
        {
            String message = String.format("Элемент %s выбран в списке или не существует", object.entryId());
            String xpath = String.format(getContentXpath() + MASS_ROW_CHECKBOX, object.entryId());
            GUITester.assertExists(xpath, true, message);
        }
    }
}
