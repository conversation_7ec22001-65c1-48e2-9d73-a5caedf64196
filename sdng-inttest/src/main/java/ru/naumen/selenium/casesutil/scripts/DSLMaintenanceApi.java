package ru.naumen.selenium.casesutil.scripts;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.attr.DateTimeInterval;
import ru.naumen.selenium.casesutil.attr.Interval;
import ru.naumen.selenium.casesutil.maintenance.MaintenanceMode;
import ru.naumen.selenium.casesutil.maintenance.MaintenanceState;

/**
 * Методы для тестирования api.maintenance
 *
 * <AUTHOR>
 * @since 20.11.2024
 */
public class DSLMaintenanceApi
{
    private final static String MODE = "mode";
    private final static String STATE = "state";
    private final static String NOTIFICATION_TEXT = "notificationText";
    private final static String START_DATE = "startDate";
    private final static String END_DATE = "endDate";
    private final static String NOTIFICATION_INTERVAL = "notificationInterval";
    private final static String LENGTH = "length";
    private final static String INTERVAL = "interval";

    /**
     * Проверяет, что параметры запланированного режима обслуживания соответствуют заданным
     * @param mode тип блокировки на время режима обслуживания
     * @param text текст информационного сообщения
     * @param start дата начала режима обслуживания
     * @param end дата окончания режима обслуживания
     * @param interval временной интервал, в который нужно показывать информационное сообщение
     */
    public static void assertScheduledMaintenance(MaintenanceMode mode, String text, LocalDateTime start,
            LocalDateTime end, DateTimeInterval interval)
    {
        String script = """
                import groovy.json.JsonOutput
                def result = api.maintenance.getMaintenanceInfo()
                return JsonOutput.toJson(result)""";

        Map<String, Object> result = DSLScriptApi.getScriptResultAsMap(script);

        Assert.assertEquals(result.get(MODE), mode.name());
        Assert.assertEquals(result.get(STATE), MaintenanceState.WAITING_FOR_START.name());
        Assert.assertEquals(result.get(NOTIFICATION_TEXT), text);
        compareDate((String)result.get(START_DATE), start);
        compareDate((String)result.get(END_DATE), end);
        compareInterval((Map<String, Object>)result.get(NOTIFICATION_INTERVAL), interval);
    }

    /**
     * Сравнивает дату, указанную в настройках режима обслуживания и полученную в результате вызова API
     * @param dateFromApi значение даты, полученное через API
     * @param localDateTimeFromSettings значение даты, указанное в настройках режима обслуживания
     */
    private static void compareDate(String dateFromApi, LocalDateTime localDateTimeFromSettings)
    {
        Date dateFromSettings = Date.from(localDateTimeFromSettings.atZone(ZoneId.systemDefault()).toInstant());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateFromApi, formatter);
        Date actualDate = Date.from(offsetDateTime.toInstant());

        Assert.assertEquals(dateFromSettings, actualDate);
    }

    /**
     * Сравнивает временной интервал, указанный в настройках режима обслуживания и полученный в результате вызова API
     * @param intervalFromApi значение временного интервала, полученного через API
     * @param intervalFromSettings значение временного интервала, указанного в настройках режима обслуживания
     */
    private static void compareInterval(Map<String, Object> intervalFromApi, DateTimeInterval intervalFromSettings)
    {
        DateTimeInterval actualInterval = DateTimeInterval.of(((Double)intervalFromApi.get(LENGTH)).longValue(),
                Interval.valueOf((String)intervalFromApi.get(INTERVAL)));

        Assert.assertEquals(intervalFromSettings.asString(), actualInterval.asString());
    }
}
