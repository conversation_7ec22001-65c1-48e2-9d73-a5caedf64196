package ru.naumen.selenium.casesutil.comment;

import static ru.naumen.selenium.casesutil.GUIXpath.Any.ANY;
import static ru.naumen.selenium.casesutil.GUIXpath.Any.ID_PATTERN;
import static ru.naumen.selenium.casesutil.GUIXpath.Any.IFRAME;
import static ru.naumen.selenium.casesutil.GUIXpath.Any.TEXT_PATTERN;
import static ru.naumen.selenium.casesutil.GUIXpath.Div.COMMENT_LIST;

import java.util.Map;
import java.util.Set;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.util.StringUtils;

/**
 *  Утилитарные методы для работы с комментариями через интерфейс
 * <AUTHOR>
 * @since 30.03.2013
 *
 */
public class GUIComment extends CoreTester
{
    public static final String ATTR_CAPTION = "//td[text()='%s:']";

    public static final String ATTR_VALUE = "//*[@id='gwt-debug-value']";

    public static final String COMMENT_BTM = GUIXpath.Div.ID_PATTERN + GUIXpath.Div.CONTAINS_ADD_COMMENT;
    /** Ссылка "Подробнее" у комментария*/
    public static final String COMMENT_PROPERTIES_PANEL = "//div[contains(@id, 'attrs-holder')]";

    public static final String CONTENT_BTN = GUIXpath.Div.ID_PATTERN + GUIXpath.Any.ADD_CONTAINS;

    /** Ссылка "Подробнее" у комментария*/
    public static final String SHOW_MORE_COMMENT_LINK = "//a[contains(@id,'show-more')]";
    public static final String SHOW_ALL_FILES_LINK = "//a[contains(@id,'all-files')]";
    public static final String X_COMMENT = "//*[@id='%s']//*[@id='%s']";

    public static final String X_COMMENTS = "//*[@id='%s']//div[contains(@id, 'comment$')]";

    /**Шаблон пути до всех комментариев. В качестве параметра передавать id контента комментарии объекта.*/
    public static final String X_COMMENTS_PTRN = "//*[@id='%s']//*[contains(@id,'comment$')]";

    public static final String X_COMMENTS_WITHSHOWMORE = "//*[@id='%s']//div[contains(@id,'comment$')]";

    public static final String X_COPY_LINK = X_COMMENT + "//*[@id='copied']";
    public static final String X_EDIT_COMMENT = X_COMMENT + GUIXpath.Span.EDIT_COMMENT_ICON;

    public static final String X_PRIVATE_BADGE = "//*[@id='%s']/div/div[1]/span[2]";

    /**Путь до поля ввода текста в формате RTF на форме добавления(редактирования) комментария*/
    public static final String X_IFRAME = GUIXpath.Div.FORM_CONTAINS + "//*[@id='gwt-debug-text-value']//iframe";

    public static final String COMMENT_PATTERN = "//div[@id='%s']";
    public static final String EDIT_COMMENT = COMMENT_PATTERN + GUIXpath.Span.EDIT_COMMENT_ICON;
    public static final String DELETE_COMMENT = COMMENT_PATTERN + GUIXpath.Span.DELETE_ICON;
    public static final String SEND_BUTTON = String.format(ANY, "send");
    public static final String TEXT_CAPTION = "gwt-debug-text-caption";

    /**
     * Добавить комментарий к объекту
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента "Комментарии к объекту"
     * @param messageForComment сообщение для комментария
     * @param privateComment нужно ли установить галку Приватный
     * @return возвращает uuid добавленного комментария
     */
    public static String add(ContentForm content, String messageForComment, boolean privateComment)
    {
        String uuid = StringUtils.substringPattern(GUITester.getCurrentUrl(), "(?<=uuid:).*\\$[0-9]+");
        Set<Map<String, Object>> oldUuids = DSLComment.getComments(uuid);
        GUICommentList.clickAddLink(content);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        fillCommentAddForm(messageForComment, privateComment);
        GUIForm.applyForm();
        Set<Map<String, Object>> newUuids = DSLComment.getComments(uuid);
        newUuids.removeAll(oldUuids);
        return (String)newUuids.iterator().next().get("UUID");
    }

    /**
     * Добавить комментарий к объекту
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента "Комментарии к объекту"
     * @param messageForComment сообщение для комментария
     * @param privateComment нужно ли установить галку Приватный
     * @param copyComment нужно ли копировать комментарий по связям массового запроса
     * @return возвращает uuid добавленного комментария
     */
    public static String add(ContentForm content, String messageForComment, boolean privateComment, boolean copyComment)
    {
        String uuid = StringUtils.substringPattern(GUITester.getCurrentUrl(), "(?<=uuid:).*\\$[0-9]+");
        Set<Map<String, Object>> oldUuids = DSLComment.getComments(uuid);
        GUICommentList.clickAddLink(content);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        fillCommentAddForm(messageForComment, privateComment, copyComment);
        GUIForm.applyForm();
        Set<Map<String, Object>> newUuids = DSLComment.getComments(uuid);
        newUuids.removeAll(oldUuids);
        return (String)newUuids.iterator().next().get("UUID");
    }

    /**
     * Проверить отсутствие комментариев в указанном контенте
     * @param content модель контента
     * @param uuids набор uuid-ов комментариев
     */
    public static void assertAbsenseByUuid(ContentForm content, String... uuids)
    {
        for (String commentUUID : uuids)
        {
            Assert.assertTrue("Комментарий присутствует в контенте, uuid: " + commentUUID,
                    tester.waitDisappear(X_COMMENT, content.getXpathId(), commentUUID));
        }
    }

    /**
     * Проверить значение атрибута комментария (после нажатия ссылки "Подробнее")
     * для атрибутов типа строка, текст, ссылка на БО
     * @param content модель контента
     * @param commentUuid комментарий
     * @param attrTitle yазвание атрибута, который проверяем
     * @param expectedValue ожидаемое значение атрибута
     */
    public static void assertAttributeValueInCommentPropertiesPanel(ContentForm content, String commentUuid,
            String attrTitle, String expectedValue)
    {
        assertAttributeValueInCommentPropertiesPanel(content, commentUuid, attrTitle, expectedValue, false);
    }

    /**
     * Проверить значение атрибута комментария (после нажатия ссылки "Подробнее")
     * для атрибутов типа строка, текст, ссылка на БО
     * @param content модель контента
     * @param commentUuid комментарий
     * @param attrTitle название атрибута, который проверяем
     * @param expectedValue ожидаемое значение атрибута
     * @param useNumeral использовать нумерацию строк
     */
    public static void assertAttributeValueInCommentPropertiesPanel(ContentForm content, String commentUuid,
            String attrTitle, String expectedValue, boolean useNumeral)
    {
        String xpath = String.format(
                X_COMMENT + COMMENT_PROPERTIES_PANEL + ATTR_CAPTION + "/following-sibling::*[1]" + ATTR_VALUE,
                content.getXpathId(), commentUuid, attrTitle);
        String actual = tester.getText(xpath);
        StringBuilder expectedText = processExpectedValue(expectedValue, useNumeral);
        Assert.assertEquals(expectedText.toString(), actual);
    }

    private static StringBuilder processExpectedValue(String expectedValue, boolean useNumeral)
    {
        if (!useNumeral)
        {
            return new StringBuilder(expectedValue);
        }
        StringBuilder expectedText = new StringBuilder();
        String[] splitExpectedValue = expectedValue.split("\n");
        int numeralLine = 1;
        for (String value : splitExpectedValue)
        {
            expectedText.append(numeralLine++).append('\n').append(value);
        }
        return expectedText;
    }

    /**
     * Проверить, отражается ли атрибут комментария после нажатия ссылки "Подробнее"
     * @param content модель контента
     * @param commentUuid комментарий
     * @param attr атрибут, который проверяем
     * @param visible виден ли атрибут
     */
    public static void assertAttributeVisibleInCommentPropertiesPanel(ContentForm content, String commentUuid,
            Attribute attr, boolean visible)
    {
        if (visible)
        {
            Assert.assertTrue("Атрибут отсутствует в контенте",
                    tester.waitAppear(X_COMMENT + COMMENT_PROPERTIES_PANEL + ATTR_CAPTION, content.getXpathId(),
                            commentUuid,
                            attr.getTitle()));
        }
        else
        {
            Assert.assertTrue("Атрибут отсутствует в контенте",
                    tester.waitDisappear(X_COMMENT + COMMENT_PROPERTIES_PANEL + ATTR_CAPTION, content.getXpathId(),
                            commentUuid, attr.getTitle()));
        }
    }

    /**
     * Проверить отсутствие комментария в указанном контенте с указанным текстом
     * @param content модель контента с комментариями
     * @param text текст проверяемого комментария
     */
    public static void assertCommentAbsence(ContentForm content, String text)
    {
        Assert.assertTrue("Комментарий присутствует, текст: " + text,
                tester.waitDisappear(COMMENT_LIST + TEXT_PATTERN, content.getCode(), text));
    }

    /**
     * Проверить отсутствие комментария в указанном контенте
     * @param content модель контента с комментариями
     * @param commentId id комментария
     */
    public static void assertCommentIdAbsence(ContentForm content, String commentId)
    {
        Assert.assertTrue("Комментарий присутствует: " + commentId,
                tester.waitDisappear(COMMENT_LIST + ID_PATTERN, content.getCode(), commentId));
    }

    /**
     * Проверить отсутствие поля ввода комментария (работает на на всех формах, кроме формы добавления комментария)
     */
    public static void assertCommentAbsenceOnForm()
    {
        Assert.assertTrue("На форме присутствует поле для ввода комментария.",
                tester.waitDisappear(GUIRichText.rtfIframeXpath(GUIRichText.TEXT)));
    }

    /**
     * Проверить присутствие комментария в указанном контенте с указанным текстом
     * @param content модель контента с комментариями
     * @param text текст проверяемого комментария
     */
    public static void assertCommentPresent(ContentForm content, String text)
    {
        GUIFrame.assertText(COMMENT_LIST + IFRAME, text,
                "Комментарий отсутствует, текст: " + text, content.getCode());
    }

    /**
     * Проверить присутствие комментария в указанном контенте с указанным текстом
     * @param content модель контента с комментариями
     * @param text текст проверяемого комментария
     * @param commentId id комментария
     */
    public static void assertCommentPresent(ContentForm content, String text, String commentId)
    {
        GUIFrame.assertText(COMMENT_LIST + ID_PATTERN + IFRAME, text,
                "Комментарий отсутствует, текст: " + text, content.getCode(), commentId);
    }

    /**
     * Проверить наличие поля ввода комментария (работает на на всех формах, кроме формы добавления комментария)
     */
    public static void assertCommentPresentOnForm()
    {
        Assert.assertTrue("На форме отсутствует поле для ввода комментария.",
                tester.waitAppear(GUIRichText.rtfIframeXpath(GUIRichText.TEXT)));
    }

    /**
     * Проверяет обязательность текста комментария на форме.
     * @param expected ожидаемое значение обязательности
     */
    public static void assertCommentTextRequired(boolean expected)
    {
        GUITester.assertRequiredProperty(TEXT_CAPTION, expected);
    }

    /**
     * Проверить количество комментариев в указнном контенте
     * @param content модель проверяемого контента
     * @param expected ожидаемое количество комментариев
     */
    public static void assertCommentsCount(ContentForm content, int expected)
    {
        if (expected == 0)
        {
            Assert.assertTrue("В контенте присутствуют комментарии.",
                    tester.waitDisappear(X_COMMENTS, content.getXpathId()));
        }
        else
        {
            int actual = getCommentUUIDs(content).size();
            String message = "Количество отображаемых комментариев не соответствует количеству ожидаемых";
            Assert.assertEquals(message, expected, actual);
        }
    }

    /**
     * Проверить отсутствие у комментария признака копирования "Скопирован из..."/"Скопирован в..."  в виде ссылки
     *
     * @param content контент типа "Комментарии к объекту"
     * @param commentUuid uuid комментария
     */
    public static void assertCopiedAbsents(ContentForm content, String commentUuid)
    {
        Assert.assertTrue("У комментария присутствует признак копирования",
                tester.waitDisappear(X_COPY_LINK, content.getXpathId(), commentUuid));
    }

    /**
     * Проверить наличие у комментария признака копирования "Скопирован из..."  в виде ссылки
     *
     * @param content контент типа "Комментарии к объекту"
     * @param commentUuid uuid комментария
     */
    public static void assertCopiedFromPresents(ContentForm content, String commentUuid)
    {
        Assert.assertEquals("Скопирован из...", tester.getText(X_COPY_LINK, content.getXpathId(), commentUuid));
    }

    /**
     * Проверить наличие у комментария признака копирования "Скопирован в..."  в виде ссылки
     *
     * @param content контент типа "Комментарии к объекту"
     * @param commentUuid uuid комментария
     */
    public static void assertCopiedToPresents(ContentForm content, String commentUuid)
    {
        Assert.assertEquals("Скопирован в...", tester.getText(X_COPY_LINK, content.getXpathId(), commentUuid));
    }

    /**
     * Проверить присутствие комментариев в указанном контенте
     * @param content модель контента
     * @param uuids набор uuid-ов комментариев
     */
    public static void assertPresenceByUuid(ContentForm content, String... uuids)
    {
        for (String commentUUID : uuids)
        {
            Assert.assertTrue("Комментарий отсутствует в контенте, uuid: " + commentUUID,
                    tester.waitAppear(X_COMMENT, content.getXpathId(), commentUUID));
        }
    }

    /**
     * Проверить значение свойства "Приватный" в указанном комментарии
     * @param commentUuid - идентификатор комментария
     * @param expected - ожидаемое текстовое значение свойства "Приватный"
     */
    public static void assertPrivatePresent(String commentUuid, String expected)
    {
        Assert.assertEquals(expected, tester.getText(X_PRIVATE_BADGE, commentUuid));
    }

    /**
     * Проверяет что ссылка "Показать все файлы/Свернуть файлы" отсутствует в контенте типа "Комментарии к объекту"
     * @param content контент тип "Комментарии к объекту"
     * @param uuid uuid комментария
     */
    public static void assertShowAllFilesLinkPresent(ContentForm content, String uuid)
    {
        Assert.assertTrue("Ссылка \"Показать все файлы/Свернуть файлы\" отсутствует в комментарии: " + uuid,
                tester.waitAppear(X_COMMENT + SHOW_ALL_FILES_LINK, content.getXpathId(), uuid));
    }

    /**
     * Проверяет что ссылка "Показать все файлы/Свернуть файлы" отсутствует в контенте типа "Комментарии к объекту"
     * @param content контент тип "Комментарии к объекту"
     * @param uuid uuid комментария
     */
    public static void assertShowAllFilesLinkAbsence(ContentForm content, String uuid)
    {
        Assert.assertTrue("Ссылка \"Показать все файлы/Свернуть файлы\" присутствует в комментарии: " + uuid,
                tester.waitDisappear(X_COMMENT + SHOW_ALL_FILES_LINK, content.getXpathId(), uuid));
    }

    /**
     * Кликнуть по признаку копирования "Скопирован из..."/"Скопирован в..."
     *
     * @param content контент типа "Комментарии к объекту"
     * @param commentUuid uuid комментария
     */
    public static void clickCopyLink(ContentForm content, String commentUuid)
    {
        tester.click(X_COPY_LINK, content.getXpathId(), commentUuid);
    }

    /**
     * Кликнуть по иконке редактирования комментария с указанным uuid
     *
     * @param content контент типа "Комментарии к объекту"
     * @param commentUuid uuid комментария
     */
    public static void clickEdit(ContentForm content, String commentUuid)
    {
        tester.click(String.format(X_EDIT_COMMENT, content.getXpathId(), commentUuid));
    }

    /**
     * Кликнуть по иконке редактирования комментария с указанным uuid
     *
     * @param commentUuid uuid комментария
     */
    public static void clickEdit(String commentUuid)
    {
        tester.click(String.format(EDIT_COMMENT, commentUuid));
    }

    /**
     * Нажать ссылку "Подробнее" на контенте типа "Комментарии к объекту"
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void clickShowMoreLink(ContentForm content, String commentUuid)
    {
        tester.click(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN + SHOW_MORE_COMMENT_LINK,
                content.getXpathId(), commentUuid);
    }

    /**
     * Нажать ссылку "Показать все файлы/Свернуть файлы" на контенте типа "Комментарии к объекту"
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Комментарии к объекту"
     * @param commentUuid идентификатор комментария к объекту
     */
    public static void clickShowAllFilesLink(ContentForm content, String commentUuid)
    {
        tester.click(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN + SHOW_ALL_FILES_LINK,
                content.getXpathId(), commentUuid);
    }

    /**
     * Заполнение комментария (работает не на всех формах, кроме формы добавления комментария)
     * @param commentText сообщение для комментария
     * @param privateComment нужно ли установить галочку Приватный
     */
    public static void fillCommentOnForm(String commentText, boolean privateComment)
    {
        GUIRichText.sendKeys(GUIRichText.TEXT, commentText);
        if (privateComment)
        {
            tester.setCheckbox(GUIXpath.InputComplex.PRIVATE_VALUE_CONTAINS, true);
        }
    }

    /**
     * Заполнение формы добавления комментария
     * @param messageForComment сообщение для комментария
     * @param privateComment нужно ли установить галку Приватный
     */
    public static void fillCommentAddForm(String messageForComment, boolean privateComment)
    {
        GUIRichText.sendKeys(GUIRichText.TEXT, messageForComment);
        if (privateComment)
        {
            tester.setCheckbox(GUIXpath.InputComplex.PRIVATE_VALUE_CONTAINS, true);
        }
    }

    /**
     * Заполнение формы добавления комментария
     * @param messageForComment сообщение для комментария
     * @param privateComment нужно ли установить галку Приватный
     * @param copyComment нужно ли установить галку Копировать
     */
    public static void fillCommentAddForm(String messageForComment, boolean privateComment, boolean copyComment)
    {
        GUIRichText.sendKeys(GUIRichText.TEXT, messageForComment);
        if (privateComment)
        {
            tester.setCheckbox(GUIXpath.InputComplex.PRIVATE_VALUE_CONTAINS, true);
        }
        if (copyComment)
        {
            tester.setCheckbox(GUICommentList.X_COPY_COMMENT_CHECKBOX_INPUT, true);
        }
    }

    /**
     * Получить uuid-ы всех отображаемых комментариев в контенте "Комментарии к объекту", на карточке объекта.
     * Вернется ошибка, если комментарии отсутствуют
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента "Комментарии к объекту"
     * @return возвращает набор uuid-ов всех отображаемых комментариев в контенте "Комментарии к объекту",
     * на карточке объекта
     */
    public static Set<String> getCommentUUIDs(ContentForm content)
    {
        return getComments(content, X_COMMENTS_PTRN);
    }

    /**
     * Получить uuid-ы всех отображаемых комментариев в контенте "Комментарии к объекту" с блоком "Подробнее", на
     * карточке объекта.
     * Вернется ошибка, если комментарии отсутствуют
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента "Комментарии к объекту"     
     * @return возвращает набор uuid-ов всех отображаемых комментариев в контенте "Комментарии к объекту" с блоком
     * "Подробнее",
     * на карточке объекта
     */
    public static Set<String> getCommentUUIDsWithShowMore(ContentForm content)
    {
        return getComments(content, X_COMMENTS_WITHSHOWMORE);
    }

    /**
     * Получить uuid-ы всех отображаемых комментариев в контенте "Комментарии к объекту" на карточке объекта.
     * Вернется ошибка, если комментарии отсутствуют
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента "Комментарии к объекту"
     * @param xpath путь до комментария
     * @return возвращает набор uuid-ов всех отображаемых комментариев в контенте "Комментарии к объекту",
     * на карточке объекта
     */
    private static Set<String> getComments(ContentForm content, String xpath)
    {
        Set<String> uuids = Sets.newTreeSet();
        GUIContent.assertPresent(content);
        for (WebElement comment : tester.findDisplayedElements(xpath, content.getXpathId()))
        {
            String uuid = comment.getAttribute("id");
            if (uuid.startsWith(SystemClass.COMMENT.getCode()))
            {
                uuids.add(uuid);
            }
        }
        return uuids;
    }

    /**
     * Проверить заголовок у ссылки "Подробнее" в контенте типа "Комментарии к объекту"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUuid идентификатор комментария
     * @param expected ожидаемый заголовок у ссылки "Подроднее"
     */
    public static void assertTitleShowMoreLink(ContentForm content, String commentUuid, String expected)
    {
        String actual = tester.getElement(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN + SHOW_MORE_COMMENT_LINK,
                content.getXpathId(), commentUuid).getAttribute("title");
        Assert.assertEquals("В блоке подробнее ожидался другой заголовок" + expected, expected, actual);
    }

    /**
     * Проверить заголовок у ссылки "Показать все файлы/Свернуть файлы" в контенте типа "Комментарии к объекту"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Комментарии к объекту"
     * @param commentUuid идентификатор комментария
     * @param expected ожидаемый заголовок у ссылки "Показать все файлы/Свернуть файлы"
     */
    public static void assertTitleShowAllFilesLink(ContentForm content, String commentUuid, String expected)
    {
        String actual = tester.getElement(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.ID_PATTERN + SHOW_ALL_FILES_LINK,
                content.getXpathId(), commentUuid).getAttribute("text");
        Assert.assertEquals("У ссылки подробнее ожидался другой заголовок: " + expected, expected, actual);
    }

    /**
     * Нажать кнопку "Отправить" на инлайн форме добавления комментария и проверить, что форма закрылась
     */
    public static void sendComment()
    {
        tester.click(SEND_BUTTON);
        GUIForm.assertFormDisappear(GUIXpath.Div.FORM_CONTAINS);
    }
}
