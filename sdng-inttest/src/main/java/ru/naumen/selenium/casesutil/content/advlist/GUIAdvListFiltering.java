package ru.naumen.selenium.casesutil.content.advlist;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListFiltering;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIDatePicker;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.SimpleTree;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с фильтрацией в advList-е
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListFiltering extends GUIAdvlist
{
    private static int WAIT_BUTTON_APPEAR_TIME = 2;

    private AGUIAdvListFiltering asserts;

    private GUIAdvListToolPanel toolPanel = new GUIAdvListToolPanel(getContentXpath());

    public GUIAdvListFiltering(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Добавить атрибут для фильтрации
     * (должна быть раскрыта панель настройки фильтров)
     * @param attr модель атрибута
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     */
    public void addAttr(Attribute attr, int andNumber, int orNumber)
    {
        String input = getContentXpath() + String.format(FILTER_ATTRS_INPUT, andNumber, orNumber);
        String value = String.format(GUIXpath.SpecificComplex.POPUP_SELECT_ENDS_WITH, "@" + attr.getCode());
        GUISelect.selectWithScroll(input, value);
    }

    /**
     * Добавить атрибут для фильтрации (с выбором условия фильтрации)
     * (должна быть раскрыта панель настройки фильтров)
     * @param attr модель атрибута
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param condition условие фильтрации {@link FilterCondition}
     */
    public void addAttr(Attribute attr, int andNumber, int orNumber, FilterCondition condition)
    {
        addAttr(attr, andNumber, orNumber);
        selectCondition(andNumber, orNumber, condition);
    }

    /**
     * Добавить атрибут для фильтрации
     * (должна быть раскрыта панель настройки фильтров)
     * @param attr модель атрибута
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param mc модель метакласса, в котором определен атрибут
     */
    public void addAttr(Attribute attr, int andNumber, int orNumber, MetaClass mc)
    {
        String input = getContentXpath() + String.format(FILTER_ATTRS_INPUT, andNumber, orNumber);
        String value = String.format(GUIXpath.SpecificComplex.POPUP_SELECT_CONTAINS,
                mc.getFqn() + "@" + attr.getCode());
        GUISelect.selectByXpath(input, value);
    }

    /**
     * Добавляет атрибут для фильтрации - выбор из дерева атрибутов
     * (должна быть раскрыта панель настройки фильтров).
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param attributePath путь к атрибуту в дереве
     */
    public void addAttrFromTree(int andNumber, int orNumber, Attribute... attributePath)
    {
        SimpleTree tree = new SimpleTree(FILTER_ATTRS, andNumber, orNumber);
        String[] nodes = new String[attributePath.length];
        for (int i = 0; i < attributePath.length; ++i)
        {
            String node = attributePath[i].getParentFqn() + '@' + attributePath[i].getCode();
            if (i > 0)
            {
                node = nodes[i - 1] + '.' + node;
            }
            nodes[i] = node;
        }
        tree.setElementInSelectTree(nodes);
    }

    /**
     * Добавляет атрибут для фильтрации - выбор из дерева атрибутов
     * (должна быть раскрыта панель настройки фильтров).
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param condition критерий фильтрации
     * @param attributePath путь к атрибуту в дереве
     */
    public void addAttrFromTree(int andNumber, int orNumber, FilterCondition condition, Attribute... attributePath)
    {
        addAttrFromTree(andNumber, orNumber, attributePath);
        selectCondition(andNumber, orNumber, condition);
    }

    /**
     * Получение утилитарных методы для проверок, связанных с настройкой фильтрации advList-а, через интерфейс
     * @return {@link AGUIAdvListFiltering}
     */
    public AGUIAdvListFiltering asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListFiltering(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Возвращает дерево выбора атрибута для элемента фильтрации.
     * @param andNumber номер элемента «И»
     * @param orNumber номер элемента «ИЛИ»
     * @return объект для работы с деревом выбора атрибута
     */
    public SimpleTree attrTree(int andNumber, int orNumber)
    {
        return new SimpleTree(FILTER_ATTRS, andNumber, orNumber);
    }

    /**
     * Кликнуть по кнопке "+ И"
     */
    public void clickAnd()
    {
        tester.click(getContentXpath() + FILTER_AND_BUTTON);
    }

    /**
     * Нажать кнопку Применить на панели настройки фильтров.
     */
    public void clickApply()
    {
        toolPanel.clickButton(BTN_APPLY_FILTER);
    }

    /**
     * Нажать кнопку Применить в диалоговом окне настройки фильтров (например, представлении по умолчанию адвлиста).
     */
    public void clickApplyInDialog()
    {
        tester.click(BTN_APPLY_FILTER_DIALOG);
    }

    /**
     * Нажать кнопку Отменить на панели настройки фильтров.
     */
    public void clickCancel()
    {
        toolPanel.clickButton(BTN_CANCEL_FILTER);
    }

    /**
     * Нажать по ссылке Изменить на панели настройки фильтров.
     */
    public void clickChange()
    {
        tester.click(getContentXpath() + FILTER_CHANGE_LINK);
    }

    /**
     * Кликнуть по кнопке "Скопировать настройки из из шаблона"
     */
    public void clickCopyFromTemplate()
    {
        tester.click(getContentXpath() + COPY_FROM_TEMPLATE_BUTTON);
    }

    /**
     * Кликнуть по иконке удаления настроенного фильтра
     * @param andNumber номер блока "И", в котором настроен фильтр
     */
    public void clickDelteAnd(int andNumber)
    {
        tester.click(getContentXpath() + FILTER_AND_DELETE, andNumber);
    }

    /**
     * Кликнуть по иконке удаления настроенного фильтра
     * @param andNumber номер блока "И", в котором настроен фильтр
     * @param orNumber номер блока "ИЛИ", в котором настроен фильтр
     */
    public void clickDelteOr(int andNumber, int orNumber)
    {
        tester.click(getContentXpath() + FILTER_OR_DELETE, andNumber, orNumber);
    }

    /**
     * Кликнуть на пиктограмму календаря, при выборе даты "с:" (начальная дата)
     */
    public void clickFromDatePicker()
    {
        tester.click(FILTER_WIDGET_DATE_FROM_IMG, getContentXpath(), 1, 1);
    }

    /**
     * Кликнуть по кнопке "+ ИЛИ" в указанном блоке "И"
     * @param andNumber номер блока "И"
     */
    public void clickOr(int andNumber)
    {
        tester.click(getContentXpath() + FILTER_OR_BUTTON, andNumber);
    }

    /**
     * Нажать кнопку Сбросить на панели настройки фильтров.
     */
    public void clickResetButton()
    {
        toolPanel.clickButton(BTN_RESET_FILTER);
    }

    /**
     * Нажать кнопку Сбросить на панели настройки фильтров в свернутом виде
     */
    public void clickResetButtonOnMinimizedList()
    {
        toolPanel.clickButton(BTN_RESET_FILTER_MINIMIZED);
    }

    /**
     * Нажать по ссылке Сбросить на панели настройки фильтров.
     */
    public void clickResetLink()
    {
        tester.click(getContentXpath() + FILTER_RESET_LINK);
    }

    /**
     * Нажать на чекбокс "Сбрасывать введенные значения при сохранении, если контент не отображается"
     */
    public void clickResetIfHidden()
    {
        tester.click(TOOL_BUTTON, "resetIfHidden-value");
    }

    /**
     * Нажать кнопку "Показать"
     */
    public void clickShow()
    {
        toolPanel.clickButton(BTN_SHOW_FILTER);
    }

    /**
     * Кликнуть на пиктограмму календаря, при выборе даты "по:" (конечная дата)
     */
    public void clickToDatePicker()
    {
        tester.click(FILTER_WIDGET_DATE_TO_IMG, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет наличие кнопки в интерфейсе
     * @param buttonXpath xPath кнопки
     */
    public boolean isButtonPresent(String buttonXpath)
    {
        return tester.waitAppear(WAIT_BUTTON_APPEAR_TIME, getContentXpath() + TOOL_BUTTON, buttonXpath);
    }

    /**
     * Выбрать условие фильтрации {@link FilterCondition}
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param condition условие фильтрации {@link FilterCondition}
     */
    public void selectCondition(int andNumber, int orNumber, FilterCondition condition)
    {
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_INPUT, condition.getCode(), andNumber, orNumber);
    }

    /**
     * Установить условия фильтрации по атрибуту дата "начиная с" или "заканчивая до"
     * @param attribute атрибут, по которому настриваем фильтрацию
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param isStartWith если true, то условие "начиная с", иначе "заканчивая до"
     * @param value значение временного интервала
     * @param isForwardDirection если true, то отступаем вперед, иначе назад
     */
    public void setDateWithDirectionCondition(Attribute attribute, int andNumber, int orNumber, boolean isStartWith,
            String value, boolean isForwardDirection)
    {
        addAttr(attribute, andNumber, orNumber,
                isStartWith ? FilterCondition.STARTING_FROM : FilterCondition.FINISHING_UP_TO);
        setString(andNumber, orNumber, value);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_DIRECTION_INPUT, isForwardDirection ? "inFuture" :
                "inPast", 1, 1);
        clickApply();
    }

    /**
     * Установить условия видимости контента по атрибуту дата "начиная с" или "заканчивая до"
     * @param attribute атрибут, по которому настриваем фильтрацию
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param isStartWith если true, то условие "начиная с", иначе "заканчивая до"
     * @param value значение временного интервала
     * @param isForwardDirection если true, то отступаем вперед, иначе назад
     */
    public void setDateWithDirectionInAdmin(Attribute attribute, int andNumber, int orNumber,
            boolean isStartWith, String value, boolean isForwardDirection)
    {
        addAttrFromTree(andNumber, orNumber,
                isStartWith ? FilterCondition.STARTING_FROM : FilterCondition.FINISHING_UP_TO,
                attribute);
        setString(andNumber, orNumber, value);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_DIRECTION_INPUT, isForwardDirection ? "inFuture" :
                "inPast", 1, 1);
        GUIForm.applyLastModalForm();
    }

    /**
     * Установить условия фильтрации по атрибуту дата/время "начиная с" или "заканчивая до"
     * @param attribute атрибут, по которому настриваем фильтрацию
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param isStartWith если true, то условие "начиная с", иначе "заканчивая до"
     * @param value значение временного интервала
     * @param timeUnit код временного интервала DAY, HOUR, MINUTE
     * @param isForwardDirection если true, то отступаем вперед, иначе назад
     */
    public void setDateTimeWithDirectionCondition(Attribute attribute, int andNumber, int orNumber, boolean isStartWith,
            String value, String timeUnit, boolean isForwardDirection)
    {
        addAttr(attribute, andNumber, orNumber,
                isStartWith ? FilterCondition.STARTING_FROM : FilterCondition.FINISHING_UP_TO);
        setString(andNumber, orNumber, value);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_TIMEUNIT_INPUT, timeUnit, 1, 1);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_DIRECTION_INPUT, isForwardDirection ? "inFuture" :
                "inPast", 1, 1);
        clickApply();
    }

    /**
     * Установить условие видимости контента по атрибуту дата/время "начиная с" или "заканчивая до"
     * @param attribute атрибут, по которому настриваем фильтрацию
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param isStartWith если true, то условие "начиная с", иначе "заканчивая до"
     * @param value значение временного интервала
     * @param timeUnit код временного интервала DAY, HOUR, MINUTE
     * @param isForwardDirection если true, то отступаем вперед, иначе назад
     */
    public void setDateTimeWithDirectionInAdmin(Attribute attribute, int andNumber, int orNumber,
            boolean isStartWith, String value, String timeUnit, boolean isForwardDirection)
    {
        addAttrFromTree(andNumber, orNumber, isStartWith ? FilterCondition.STARTING_FROM
                : FilterCondition.FINISHING_UP_TO, attribute);
        setString(andNumber, orNumber, value);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_TIMEUNIT_INPUT, timeUnit, 1, 1);
        GUISelect.select(getContentXpath() + FILTER_CONDITIONS_DIRECTION_INPUT, isForwardDirection ? "inFuture" :
                "inPast", 1, 1);
        GUIForm.applyLastModalForm();
    }

    /**
     * Выбирает атрибут в дереве при фильтрации по атрибутам объектов из контекста.
     * @param andNumber номер блока "И"
     * @param orNumber номер блока "ИЛИ"
     * @param attributes путь к выбираемому атрибуту
     */
    public void setAttributeTree(int andNumber, int orNumber, Attribute... attributes)
    {
        SimpleTree tree = new SimpleTree(FILTER_VALUE_WIDGET, andNumber, orNumber);
        String[] nodes = new String[attributes.length];
        for (int i = 0; i < attributes.length; ++i)
        {
            String node = attributes[i].getParentFqn() + '@' + attributes[i].getCode();
            if (i > 0)
            {
                node = nodes[i - 1] + '.' + node;
            }
            nodes[i] = node;
        }
        tree.setElementInSelectTree(nodes);
    }

    /**
     * Выбрать булево значение на переключателе (Radiobutton) при фильтрации
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param value устанавливаемое значение
     */
    public void setBool(int andNumber, int orNumber, boolean value)
    {
        String valueXpath = value ? FILTER_WIDGET_RADIOBUTTON_YES : FILTER_WIDGET_RADIOBUTTON_NO;
        tester.click(getContentXpath() + valueXpath, andNumber, orNumber);
    }

    /**
     * Установить значение при фильтрации из выпадающего списка типа Дерево БО
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param bos набор БО до устанавливаемого значения
     */
    public void setBoTree(int andNumber, int orNumber, Bo... bos)
    {
        BoTree tree = new BoTree(FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        tree.setElementInSelectTree(bos);
    }

    /**
     * Установить значение при фильтрации из выпадающего списка
     * (должна быть раскрыта панель настройки фильтров)
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param items набор идентификаторов до устанавливаемого значения
     */
    public void setTree(int andNumber, int orNumber, String... items)
    {
        SimpleTree tree = new SimpleTree(FILTER_VALUE_WIDGET, andNumber, orNumber);
        String[] nodes = new String[items.length];
        for (int i = 0; i < items.length; ++i)
        {
            String node = items[i];
            if (i > 0)
            {
                node = nodes[i - 1] + '.' + node;
            }
            nodes[i] = node;
        }
        tree.setElementInSelectTree(nodes);
    }

    /**
     * Установить значения "С" -"По" атрибута типа Дата(Дата/время) в advList-е при фильтрации. Значения могут быть null
     * @param dateFrom значения фильтра "С"
     * @param dateTo значения фильтра "По"
     */
    public void setDate(String dateFrom, String dateTo)
    {
        tester.sendKeys(FILTER_WIDGET_DATE_FROM_INPUT, dateFrom, getContentXpath(), 1, 1);
        tester.sendKeys(FILTER_WIDGET_DATE_TO_INPUT, dateTo, getContentXpath(), 1, 1);
    }

    /**
     * Установить значения "С" -"По" атрибута типа Время(Дата/время) в advList-е при фильтрации. Значения могут быть
     * null
     * @param timeFrom значения фильтра "С"
     * @param timeTo значения фильтра "По"
     */
    public void setTime(String timeFrom, String timeTo)
    {
        tester.sendKeys(FILTER_WIDGET_TIME_FROM_INPUT, timeFrom, getContentXpath(), 1, 1);
        tester.sendKeys(FILTER_WIDGET_TIME_TO_INPUT, timeTo, getContentXpath(), 1, 1);
    }

    /**
     * Установить дату на календаре при выборе даты "с:" (начальная дата)
     * @param date устанавливаемая дата в формате "dd.MM.yyyy"
     */
    public void setFromDatePicker(String date)
    {
        clickFromDatePicker();
        GUIDatePicker.setDate(date);
    }

    /**
     * Установить значения для атрибута типа Гиперссылка в advList-е при фильтрации
     * @param text значение текста ссылки
     * @param url значение адреса ссылки
     */
    public void setHyperLink(String text, String url)
    {
        tester.click(getContentXpath() + FILTER_WIDGET_HYPERLINK_TEXT, 1, 1);
        tester.sendKeys(getContentXpath() + FILTER_WIDGET_HYPERLINK_TEXT, text, 1, 1);
        tester.click(getContentXpath() + FILTER_WIDGET_HYPERLINK_URL, 1, 1);
        tester.sendKeys(getContentXpath() + FILTER_WIDGET_HYPERLINK_URL, url, 1, 1);
    }

    /**
     * Установить значение при фильтрации из выпадающего списка множественного выбора
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param values набор id устанавливаемых значений
     */
    public void setMultiSelect(int andNumber, int orNumber, String... values)
    {
        GUIMultiSelect.select(String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber),
                values);
    }

    /**
     * Установить значение при фильтрации из выпадающего списка
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param value id устанавливаемого значения
     */
    public void setSelect(int andNumber, int orNumber, String value)
    {
        String select = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber);
        String updatedValue = StringUtils.isEmpty(value) ? GUISelect.EMPTY_SELECTION_ITEM : value;
        GUISelect.selectWithScroll(select, GUISelect.getValueXpath(updatedValue));
        GUISelect.hideSelect(select);
    }

    /**
     * Установить строковое значение при фильтрации
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param value устанавливаемое значение
     */
    public void setString(int andNumber, int orNumber, String value)
    {
        tester.sendKeys(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, value, andNumber, orNumber);
    }

    /**
     * Установить временной интервал в поле фильтрации
     * @param value устанавливаемое значение
     * @param timeInterval единица измерения времени (SECOND, MINUTE, HOUR и т.д.)
     */
    public void setTimeInterval(String value, String timeInterval)
    {
        tester.sendKeys(FILTER_WIDGET_TIME_INERVAL_LENGTH, value, getContentXpath(), 1, 1);
        GUISelect.select(FILTER_WIDGET_TIME_INERVAL_INTERVAL, timeInterval, getContentXpath(), 1, 1);
    }

    /**
     * Установить дату на календаре при выборе даты "по:" (конечная дата)
     * @param date устанавливаемая дата в формате "dd.MM.yyyy"
     */
    public void setToDatePicker(String date)
    {
        clickToDatePicker();
        GUIDatePicker.setDate(date);
    }

    /**
     * Выставляет параметр "Игнорировать, если пусто" для выбранного атрибута.
     * @param andNumber номер блока "И"
     * @param orNumber номер блока "ИЛИ"
     * @param value значение параметра "Игнорировать, если пусто"
     */
    public void setIgnoreIfEmpty(int andNumber, int orNumber, boolean value)
    {
        String xpath = String.format(getContentXpath() + IGNORE_IF_EMPTY_IN_FILTER_CONDITIONS_INPUT, andNumber,
                orNumber);
        tester.setCheckbox(xpath, value);
    }
}