package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext;
import ru.naumen.selenium.casesutil.role.UserWfAndRespEmployeeRole;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Необходимый контекст для тестирования прав из блока "Действия с объектами"  для пользовательского класса с ЖЦ и
 * Ответственным
 * <AUTHOR>
 * @since 4.05.2016
 */
public class UserBoWithWfAndRespActionsContext extends AbstractRightContext implements IBoActionsContext
{
    private BoStatus status;
    private MetaClass userClass;
    private MetaClass userCase;
    private Bo user;
    private Bo respTeam;

    private ContentForm eventList;

    public UserBoWithWfAndRespActionsContext()
    {
        super();
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, userClass, roles);
        //Добавляем связь между ролями и контекстом прав
        Map<String, Object> params = new HashMap<>();
        params.put(UserWfAndRespEmployeeRole.TEAM_MODEL, respTeam);
        params.put(UserWfAndRespEmployeeRole.USER_CASE, userCase);
        params.put(UserWfAndRespEmployeeRole.OBJECT_MODEL, user);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    public ContentForm getEventList()
    {
        return eventList;
    }

    @Override
    public Bo getNewClient(NewClientType type)
    {
        throw new ErrorInCodeException("Нет основной привязки.");
    }

    @Override
    public Bo getObject()
    {
        return user;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return userCase;
    }

    @Override
    public MetaClass getParentCase()
    {
        return null;
    }

    @Override
    public Bo getRespEmpl()
    {
        return null;
    }

    @Override
    public Bo getRespTeam()
    {
        return respTeam;
    }

    @Override
    public BoStatus getStatus()
    {
        return status;

    }

    @Override
    protected void prepareImmutableData()
    {
        userClass = DAOUserClass.createWithWFAndResp();
        userClass.setParentRelFqn(userClass.getFqn());
        userCase = DAOUserCase.create(userClass);
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(userClass, userCase, teamCase);

        respTeam = DAOTeam.create(teamCase);
        DSLBo.add(respTeam);

        eventList = DAOContentCard.createEventList(userCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);
    }

    @Override
    protected void prepareMutableData()
    {
        //Создаем объекты
        if (userCase == null)
        {
            prepareImmutableData();
        }
        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        DSLBo.add(bo1, bo2);
        user = DAOUserBo.createWithParent(userCase, bo1);
        DSLBo.add(user);

        status = DAOBoStatus.createUserStatus(userCase.getFqn());
    }
}