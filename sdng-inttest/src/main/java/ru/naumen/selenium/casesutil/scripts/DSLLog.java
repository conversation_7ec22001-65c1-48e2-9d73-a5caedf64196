package ru.naumen.selenium.casesutil.scripts;

import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import ru.naumen.selenium.core.TSLogger;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Методы работы с логом приложения
 *
 * <AUTHOR>
 * @since 22.08.2024
 */
public class DSLLog
{
    private static final String IS_LOGGED_KEY = "isLogged";
    private static final String LOGGER_IS_OFF_MESSAGE = "Возможность получения логов с приложения выключена.";

    /**
     * Класс для проверок для лога
     * <p>Нужен чтобы не загружать лог с сервера по несколько раз,
     * когда надо сделать несколько проверок</p>
     */
    public static class LogAssert
    {
        public static final int MAX_LOG_LINES = 500;
        private final String log;

        public LogAssert()
        {
            log = getCurrentTestLog(MAX_LOG_LINES);
        }

        /**
         * Проверить, что сообщение отсутствует в логе
         */
        public LogAssert assertAbsence(String message)
        {
            Assert.assertFalse("В логе присутствует сообщение \"" + message + '"',
                    log.contains(message));
            return this;
        }

        /**
         * Проверить, что сообщение присутствует в логе
         */
        public LogAssert assertPresent(String message)
        {
            Assert.assertTrue("В логе отсутствует сообщение \"" + message + '"',
                    log.contains(message));
            return this;
        }

        /**
         * Проверить, что сообщение присутствует в логе определённое количество раз
         */
        public LogAssert assertCount(String message, int expectedCount)
        {
            Assert.assertEquals("Неожиданное количество совпадений выражения \"" + message + '"',
                    expectedCount, StringUtils.countMatches(log, message));
            return this;
        }
    }

    public static LogAssert assertLog()
    {
        return new LogAssert();
    }

    /**
     * Проверить наличие записи в логе.
     * Если метод вызывается из теста, то учитываются только записи касающиеся текущего теста.
     *
     * @param message ожидаемое сообщение
     */
    public static void assertPresentMessageInLog(String message)
    {
        Assert.assertTrue("Не логируется запись вида " + message,
                containedInCurrentTestLogAsString(message));
    }

    /**
     * Проверить наличие записи в файле лога. Если лог отключен, то ошибки не будет.
     *
     * @param message - ожидаемое сообщение
     */
    public static void assertPresentMessageInLogFile(String message)
    {
        Assert.assertTrue("Не логируется запись вида " + message,
                getCurrentTestLog().contains(message)
                || getCurrentTestLog().equals(LOGGER_IS_OFF_MESSAGE));
    }

    /**
     * Проверить что в логе содержится запись соответствующая паттерну
     * @param pattern паттерн поиска
     * @return true - есть в логе, false - отсутствует в логе
     */
    public static boolean containedInCurrentTestLogAsRegex(String pattern)
    {
        return countOccurrencesInCurrentTestLogAsRegex(pattern) != 0;
    }

    /**
     * Проверить что в логе содержится строка
     * @param substring строка для поиска в логе
     * @return true - есть в логе, false - отсутствует в логе
     */
    public static boolean containedInCurrentTestLogAsString(String substring)
    {
        return countOccurrencesInCurrentTestLogAsString(substring) != 0;
    }

    /**
     * Получить количество вхождений паттерна для лога
     * @param pattern паттерн поиска
     * @return количество вхождений
     */
    public static int countOccurrencesInCurrentTestLogAsRegex(String pattern)
    {
        String isLogged = System.getProperty(IS_LOGGED_KEY);
        if (isLogged == null || Boolean.parseBoolean(isLogged))
        {
            WaitTool.waitMills(700);
            return ScriptModules.getModuleApplication().countOccurrencesInCurrentTestLog(pattern);
        }
        return -1;
    }

    /**
     * Проверить количество вхождений паттерна в логе
     * @param errorMessage сообщение об ошибке
     * @param expectedCount ожидаемое количество вхождений
     * @param pattern паттерн поиска
     */
    public static void assertCountOccurrencesInCurrentTestLogAsRegex(String errorMessage, int expectedCount,
            String pattern)
    {
        int resultCount;
        long time = System.currentTimeMillis();
        do
        {
            resultCount = countOccurrencesInCurrentTestLogAsRegex(pattern);
        }
        while (resultCount < expectedCount && (System.currentTimeMillis() - time) / 1000 < WaitTool.WAIT_TIME);

        if (errorMessage != null)
        {
            Assert.assertEquals(errorMessage, expectedCount, resultCount);
        }
        else
        {
            Assert.assertEquals(expectedCount, resultCount);
        }
    }

    /**
     * Проверить количество вхождений паттерна в логе
     * @param expectedCount ожидаемое количество вхождений
     * @param pattern паттерн поиска
     */
    public static void assertCountOccurrencesInCurrentTestLogAsRegex(int expectedCount, String pattern)
    {
        assertCountOccurrencesInCurrentTestLogAsRegex(
                String.format("Количество вхождений паттерна '%s' в логе не совпало c ожидаемым", pattern),
                expectedCount, pattern);
    }

    /**
     * Получить количество вхождений подстроки в логе
     * @param substring подстрока
     * @return количество вхождений подстроки
     */
    public static int countOccurrencesInCurrentTestLogAsString(String substring)
    {
        return countOccurrencesInCurrentTestLogAsRegex(Pattern.quote(substring));
    }

    /**
     * Проверить количество вхождений подстроки в логе
     * @param errorMessage сообщение об ошибке
     * @param expectedCount ожидаемое количество вхождений
     * @param substring подстрока
     */
    public static void assertCountOccurrencesInCurrentTestLogAsString(String errorMessage, int expectedCount,
            String substring)
    {
        int resultCount;
        long time = System.currentTimeMillis();
        do
        {
            resultCount = countOccurrencesInCurrentTestLogAsString(substring);
        }
        while (resultCount < expectedCount && (System.currentTimeMillis() - time) / 1000 < WaitTool.WAIT_TIME);

        if (errorMessage != null)
        {
            Assert.assertEquals(errorMessage, expectedCount, resultCount);
        }
        else
        {
            Assert.assertEquals(expectedCount, resultCount);
        }
    }

    /**
     * Проверить количество вхождений подстроки в логе
     * @param expectedCount ожидаемое количество вхождений
     * @param substring подстрока
     */
    public static void assertCountOccurrencesInCurrentTestLogAsString(int expectedCount, String substring)
    {
        assertCountOccurrencesInCurrentTestLogAsString(
                String.format("Количество вхождений подстроки '%s' в логе не совпало c ожидаемым", substring),
                expectedCount, substring);
    }

    /**
     * Получить лог приложения (последние 500 (1000 для кластера) строк лога)
     * <p>При работе в кластере берется cluster.log, иначе - sdng.log</p>
     * @return лог приложения
     */
    public static String getCurrentTestLog()
    {
        return getCurrentTestLog(Config.get().getTestLogMaxSize());
    }

    /**
     * Получить лог приложения
     * <p>При работе в кластере берется cluster.log, иначе - sdng.log</p>
     * @param maxLogLines максимальное количество строк лога
     * @return лог приложения
     */
    public static String getCurrentTestLog(int maxLogLines)
    {
        String isLogged = System.getProperty(IS_LOGGED_KEY);
        if (isLogged == null || Boolean.parseBoolean(isLogged))
        {
            WaitTool.waitMills(1000);
            return ScriptModules.getModuleApplication().getCurrentTestLog(
                    computePresumableMemorySizeInBytes(), maxLogLines);
        }
        return LOGGER_IS_OFF_MESSAGE;
    }

    /**
     * Добавить сообщение в лог приложения
     * @param message сообщение
     */
    public static void logInfo(String message)
    {
        String isLogged = System.getProperty(IS_LOGGED_KEY);
        if ((isLogged == null || Boolean.parseBoolean(isLogged))
            && !Config.get().isReadOnly())
        {
            ScriptModules.getModuleApplication().logInfo(message);
        }
    }

    private static long computePresumableMemorySizeInBytes()
    {
        Runtime runtime = Runtime.getRuntime();
        long fiftyMegabytes = 50L * 1024L * 1024L; // Зарезервировал для нужд ТС
        long allocatedMemory = runtime.totalMemory() - runtime.freeMemory();
        final long presumableMemorySize = runtime.maxMemory() - allocatedMemory - fiftyMegabytes;
        TSLogger.logWebTester("Presumable memory size in bytes is {}.", presumableMemorySize);
        return presumableMemorySize;
    }
}
