package ru.naumen.selenium.casesutil.content.hierarchygrid;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import org.junit.Assert;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.A;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFiltering;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.hierarchygrid.HierarchyGridContent;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;

/**
 * Утилитарные методы для работы с контентом Иерархическая таблица
 * <AUTHOR>
 * @since 28.01.2020
 */
public class GUIHierarchyGrid extends CoreTester
{
    /**
     * Параметр "Фокусироваться на текущем объекте"
     */
    public enum CardObjectFocus
    {
        /**
         * Выключено (по умолчанию)
         */
        OFF("Выключено"),

        /**
         * Фокусировка со скрытием
         */
        FOCUS_WITH_HIDDEN("Фокусировка со скрытием"),

        /**
         * Фокусировка без скрытия
         */
        FOCUS_WITHOUT_HIDDEN("Фокусировка без скрытия");

        String title;

        CardObjectFocus(String title)
        {
            this.title = title;
        }

        public String getTitle()
        {
            return title;
        }
    }

    public static final String SHOW_NAME_NAME = "//b[@class='k-grid-expand-element-text']";
    public static final String FOCUS = "//span[contains(@class, 'toggle-focus')]";
    private static final String ATTRIBUTE_IN_TITLE = "//th[contains(@data-field, '%s')]";
    private static final String INDEX_OF_COLUMN = "//th[contains(@data-index, '%s')]";
    private static final String ATTRIBUTE_IN_TITLE_WITH_FQN = "//th[contains(@data-field, '%s') and contains"
                                                              + "(@data-field, '%s')]";
    public static final String TITLE = "//th[@data-title='%s']";
    private static final String SORTED_ATTRIBUTE_IN_TITLE = "//th[contains(@data-field, '%s') and contains"
                                                            + "(@aria-sort, '%s')]";
    private static final String ELEMENT = "//td[@role='gridcell']";
    private static final String CELL = ELEMENT + "[2]";
    private static final String BOX = ELEMENT + "[1]";
    private static final String DIV_GRID = "//div[@data-role='grid' and contains(@class, 'grid_%s')]";
    private static final String CHILD_GRID = "//div[@data-role='grid' and contains(@class, 'inner-grid') and " +
                                             "contains(@class, 'grid_%s')]";
    private static final String SELF_NESTED_GRID = "//div[@data-role='grid' and contains(@class, 'inner-grid') and " +
                                                   "contains(@class, 'selfnested_grid')]";
    public static final String COLLAPSE_ELEMENT = "//a[@class='k-icon k-i-collapse']";
    private static final String COLLAPSE_TABLE_ELEMENT = "//a[@class='k-icon k-i-collapse-table']";
    private static final String CHILD_COLLAPSE_ELEMENT = "//a[contains(@class, 'k-i-child-collapse')]";
    public static final String ELEMENT_IN_TABLE = "//tr[@debug-id='%s' and contains(@class, 'k-master-row')]";
    public static final String ATTRIBUTE_BADGE_IN_TABLE = ELEMENT_IN_TABLE +
                                                          "//td[@data-attr-fqn='%s']//a[@href='#uuid:%s']//span"
                                                          + "[contains(text(), '%s')]";
    private static final String GRID_IN_TABLE = "//tr[@debug-id='%s' and contains(@class, 'k-detail-row')]";
    private static final String FOCUSED_ELEMENT_IN_TABLE = "//table//tbody//tr[@debug-id='%s' and contains(@class, "
                                                           + "'focused-row')]";
    public static final String EXPAND_ELEMENT = "//a[@class='k-icon k-i-expand']";
    private static final String EXPAND_TABLE_ELEMENT = "//a[@class='k-icon k-i-expand-table']";
    public static final String CHILD_EXPAND_ELEMENT = "//a[contains(@class, 'k-i-child-expand')]";
    private static final String EXPAND_OR_COLLAPSE_ELEMENT = "//a[@class='k-icon k-i-expand' or @class='k-icon "
                                                             + "k-i-collapse']";
    private static final String EXPAND_OR_COLLAPSE_IN_SEARCH_ELEMENT = "//a[@class='k-icon k-i-child-expand' or "
                                                                       + "@class='k-icon k-i-child-collapse' or "
                                                                       + "@class='k-icon k-i-collapse' or "
                                                                       + "@class='k-icon k-i-expand']";
    private static final String SHOW_NAME_TILE = "//div[@class='k-grid-expand-table-element']";
    private static final String COLUMN_RESIZER = "//div[@class='k-resize-handle']";
    public static final String FILTER = "//a[contains(@class,'k-grid-filter')]";
    private static final String LAST_PAGE = "//span[contains(@class, 'k-i-arrow-end-right')]";
    private static final String MAIN_GRID = "//div[@data-role='grid' and not(contains(@class, 'inner-grid'))]";
    private static final String NEXT_PAGE = "//span[contains(@class, 'k-i-arrow-60-right')]";
    private static final String PAGER_INPUT = "//span[contains(@class, 'k-pager-input')"
                                              + "]//input[@class='k-input-inner']";
    private static final String PREVIOUS_PAGE = "//span[contains(@class, 'k-i-arrow-60-left')]";
    private static final String ROW_IN_TABLE = "//table//tbody//tr[@debug-id]";
    private static final String ACTIVE_FILTER = "//a[contains(@class, 'k-grid-filter') and contains(@class, "
                                                + "'k-state-active')]";
    private static final String ATTRIBUTE_VALUE_IN_ROW = "//td[@data-attr-fqn='%s']//*[text()='%s']";
    private static final String SELECTION_CELL = "//*[starts-with(@class, 'k-selection')]";
    private static final String TABLE_IN_GRID = "//table";
    public static final String KENDO_DEFAULT_WIDTH_CONTENT_DEBUG_ID = "gwt-debug-kendoGridWidthColumnsDefault";
    public static final String HIERARCHYGRID_EXPAND = "//div[@id='gwt-debug-HierarchyGrid"
                                                      + ".%s']//span[@title='Развернуть']";
    private static final String DIV_HIERARCHY_GRID = "//div[contains(@id, 'gwt-debug-HierarchyGrid')]";
    private static final String DIV_ROLE_GRID = "//div[@data-role = 'grid']";

    private static GUIAdvListContent filterSettingsTable;
    private static GUIAdvListFiltering filterSettingsForm;
    private static GUIAdvListFiltering objectFilterSettingsForm;

    /**
     * Проверить, что элемент нельзя выбрать (нет ни чекбоксе, ни переключателя)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertNoSelect(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + BOX, element.getUuid());
        GUITester.assertExists(xpath, true);
        GUITester.assertExists(xpath + "/input", false);
    }

    /**
     * Проверить, что элемент можно выбрать (установлена галочка в чекбоксе или в переключателе)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param enabled присутствует/отсутствует возможность выбрать элемент
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertSelectEnabled(ContentForm content, StructuredObjectsViewItem structuredItem,
            boolean enabled, Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + BOX, element.getUuid()) + "/input";
        WebElement item = tester.find(xpath);
        if (item.getAttribute("type").contentEquals("checkbox"))
        {
            GUITester.assertCheckboxAvailable(xpath, enabled);
        }
        else
        {
            GUITester.assertRadioButtonEnable(xpath, enabled);
        }
    }

    /**
     * Проверить, что в таблице отсутсвует столбец
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertAbsenceColumn(ContentForm content, StructuredObjectsViewItem structuredItem,
            String codeOfColumn, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(ATTRIBUTE_IN_TITLE,
                codeOfColumn));
        GUITester.assertAbsent(xpath, String.format("В списке есть колонка с кодом %s", codeOfColumn));
    }

    /**
     * Проверить, что элемент отстутствует в списке
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element проверяемый объект
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertAbsenceElement(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + String.format(ELEMENT_IN_TABLE,
                element.getUuid());
        GUITester.assertAbsent(xpath, "Во вложенном списке есть проверяемый элемент");
    }

    /**
     * Проверить, что элемент присутсвует в гриде и он не выделен серым
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertAbsenceFocusedElement(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + String.format(FOCUSED_ELEMENT_IN_TABLE,
                element.getUuid());
        GUITester.assertAbsent(xpath, "Во вложенном списке присутствует проверяемый элемент");
    }

    /**
     * Проверить отсутствие в списке панели массовых операций
     * @param content Контент типа Иерархическое дерево
     * @param element Проверяемый элемент
     */
    public static void assertAbsentMassOperationToolPanelForItem(ContentForm content, Bo element)
    {
        String xpath = String.format(GUIStructuredObjectsView.MASS_OPERATION_CHECKBOX, content.getXpathId(),
                element.getUuid()) + SELECTION_CELL;
        Assert.assertTrue("В списке присутствует панель массовых операций", tester.waitDisappear(xpath));
    }

    /**
     * Проверить, что у атрибута в шапке грида значок фильтрации является активным
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут, у которого должен быть значок
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertActiveFiltrationIcon(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos)
                       + String.format(ATTRIBUTE_IN_TITLE_WITH_FQN, attribute.getCode(), attribute.getParentFqn());
        try
        {
            tester.scrollIntoView(xpath);
        }
        finally
        {
            xpath += ACTIVE_FILTER;
            GUITester.assertPresent(xpath, "Значок быстрой фильтрации не активен");
        }
    }

    /**
     * Проверить значение атрибута у элемента в гриде (для атрибутов, у которых текст находится в подэлементе)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Проверяемый атрибут
     * @param value Значение атрибута
     * @param element Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertAttributeValue(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, String value, Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos);
        xpath += String.format(ELEMENT_IN_TABLE, element.getUuid());
        xpath += String.format(ATTRIBUTE_VALUE_IN_ROW, attribute.getFqn(), value);
        String message = String.format("В проверяемом списке нет элемента со значением %s атрибута с fqn %s", value,
                attribute);
        GUITester.assertPresent(xpath, message);
    }

    /**
     * Проверить количество объектов в списке в контенте
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param count Ожидаемое количество элементов
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertCountOfRows(ContentForm content, StructuredObjectsViewItem structuredItem, int count,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + ROW_IN_TABLE;
        GUITester.assertCountElements(xpath, count);
    }

    /**
     * Проверить что в контенте типа "Иерархическое дерево" отсутствует панель массовых операций.
     * @param content контент типа "Иерархическое дерево"
     */
    public static void assertDisableMassOperationToolPanel(ContentForm content)
    {
        String xpath = String.format(GUIXpath.Div.ID_PATTERN, content.getXpathId()) + SELECTION_CELL;
        Assert.assertTrue("В списке присутствует панель массовых операций", tester.waitDisappear(xpath));
    }

    /**
     * Проверить нахождение элементов в гриде и их порядок
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param expectedList Лист с ожидаемыми элементами
     * @param order Учитывать порядок или нет
     * @param match True - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertElementsInGrid(ContentForm content, StructuredObjectsViewItem structuredItem,
            List<Bo> expectedList, Boolean order, Boolean match, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos);
        List<String> expected = new ArrayList<>();
        for (Bo bo : expectedList)
        {
            expected.add(bo.getUuid());
        }
        GUITester.assertEqualListsByWebElementProperty(xpath + "//tr[@data-uid]", "debug-id", expected, order,
                match);
    }

    /**
     * Проверить, что у строки есть элемент для раскрытия вложенного списка.
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param element раскрываемый элемент
     * @param expected ожидаемое значение, true - раскрытие возможно, иначе false
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertExpandable(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            boolean expected, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + ELEMENT_IN_TABLE
                       + EXPAND_OR_COLLAPSE_ELEMENT;
        if (expected)
        {
            Assert.assertTrue("Раскрытие строки списка недоступно.", tester.waitAppear(xpath, element.getUuid()));
        }
        else
        {
            Assert.assertTrue("Раскрытие строки списка доступно", tester.waitDisappear(xpath, element.getUuid()));
        }
    }

    /**
     * Проверить видимость заголовка элемента структуры в иерархическом дереве<br>
     * Работает как в операторе, так и в админке!
     * @param hierarchyGrid контент иерархического дерева
     * @param item элемент структуры
     * @param gridLevel уровень в гриде, считается от нуля (именно в гриде, а не в структуре,
     *                  т.к. один и тот же элемент может занимать несколько уровней если
     *                  он отображает вложенные во вложенные)
     * @param visible должен ли быть виден
     */
    public static void assertItemHeaderVisible(
            HierarchyGridContent hierarchyGrid,
            StructuredObjectsViewItem item,
            int gridLevel, boolean visible)
    {
        String message = String.format("Для элемента '%s' (%s) на уровне %d заголовок должен быть %s",
                item.getTitle(), item.getCode(), gridLevel, (visible ? "видим" : "не видим"));
        String xpath = String.format(Div.ANY + DIV_GRID + "//table/thead",
                "HierarchyGrid." + hierarchyGrid.getCode(),
                item.getCode() + "_" + gridLevel);
        GUITester.assertExists(xpath, visible, message);
    }

    /**
     * Проверить, что у строки есть элемент для раскрытия вложенного списка в результатах поиска.
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param element раскрываемый элемент
     * @param expected ожидаемое значение, true - раскрытие возможно, иначе false
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertExpandableInSearch(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element,
            boolean expected, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + ELEMENT_IN_TABLE
                       + EXPAND_OR_COLLAPSE_IN_SEARCH_ELEMENT;
        if (expected)
        {
            Assert.assertTrue("Раскрытие строки списка в результатах поиска недоступно.", tester.waitAppear(xpath,
                    element.getUuid()));
        }
        else
        {
            Assert.assertTrue("Раскрытие строки списка в результатах поиска доступно", tester.waitDisappear(xpath,
                    element.getUuid()));
        }
    }

    /**
     * Проверить, что элемент присутсвует в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param elements Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPresenceElements(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo[] bos, Bo... elements)
    {
        for (Bo element : elements)
        {
            String xpath = getXpathToGridInContent(content, structuredItem, bos) + String.format(ELEMENT_IN_TABLE,
                    element.getUuid());
            GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
        }

    }

    /**
     * Проверить номер страницы в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param expected Ожидаемый номер страницы
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPage(ContentForm content, StructuredObjectsViewItem structuredItem, int expected,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + PAGER_INPUT;
        GUITester.assertValue(xpath, String.valueOf(expected));
    }

    /**
     * Проверить, что в таблице присутсвует столбец
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPresenceColumn(ContentForm content, StructuredObjectsViewItem structuredItem,
            String codeOfColumn, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(ATTRIBUTE_IN_TITLE,
                codeOfColumn));
        GUITester.assertPresent(xpath, String.format("В списке нет колонки с кодом %s", codeOfColumn));
    }

    /**
     * Проверить, что элемент присутсвует в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPresenceElement(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + String.format(ELEMENT_IN_TABLE,
                element.getUuid());
        GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
    }

    /**
     * Проверить, что элемент присутсвует в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param elements Проверяемые элементы
     * (работает лишь с первым уровнем)
     */
    public static void assertPresenceElements(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo... elements)
    {
        for (Bo element : elements)
        {
            String xpath = getXpathToGridInContent(content, structuredItem) + String.format(ELEMENT_IN_TABLE,
                    element.getUuid());
            GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
        }
    }

    /**
     * Проверить, что элемент структуры есть контенте "Иерархическое дерево"
     * @param contentWithGrid Контент типа "Иерархическое дерево"
     * @param viewItems список элементов структуры, начинается с корневого элемента (первый уровень), заканчивается тем
     * элементом, наличие которого нужно проверить. selfNested элементы учитываются.
     */
    public static void assertPresenceElementInAdmin(ContentForm contentWithGrid, StructuredObjectsViewItem... viewItems)
    {
        String xpath = getXpathOfGridInAdmin(contentWithGrid, viewItems);
        GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
    }

    /**
     * Проверить, что элемент присутсвует в гриде и он выделен серым
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPresenceFocusedElement(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + String.format(FOCUSED_ELEMENT_IN_TABLE,
                element.getUuid());
        GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
    }

    /**
     * Проверить присутствие в списке панели массовых операций
     * @param content Контент типа Иерархическое дерево
     * @param element Проверяемый элемент
     */
    public static void assertPresentMassOperationToolPanelForItem(ContentForm content, Bo element)
    {
        String xpath = String.format(GUIStructuredObjectsView.MASS_OPERATION_CHECKBOX, content.getXpathId(),
                element.getUuid()) + SELECTION_CELL;
        Assert.assertTrue("В списке отсутствует панель массовых операций", tester.waitAppear(xpath));
    }

    /**
     * Проверить список объектов в контенте
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param expectedList ожидаемый список объектов
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень) (Если на уровне
     * где проверяются объекты находятся несколько item, нужно указать в каком item проверяются элементы)
     */
    public static void assertRows(ContentForm content, StructuredObjectsViewItem structuredItem,
            List<Bo> expectedList, boolean order, boolean match, Bo... bos)
    {
        List<String> expected = expectedList.stream().map(entry -> entry.getUuid()).collect(Collectors.toList());
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + ROW_IN_TABLE;
        GUITester.assertEqualListsByWebElementProperty(xpath, "debug-id", expected, order, match);
    }

    /**
     * Проверить раскрыт ли элемент "Название элемента структуры".
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param expected ожидаемое значение, true - раскрыт, иначе false
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertShowNameElementExpand(ContentForm content, StructuredObjectsViewItem structuredItem,
            boolean expected, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + SHOW_NAME_TILE + (expected ?
                COLLAPSE_TABLE_ELEMENT : EXPAND_TABLE_ELEMENT);
        if (expected)
        {
            Assert.assertTrue("Список свернут", tester.waitAppear(xpath));
        }
        else
        {
            Assert.assertTrue("Список развернут", tester.waitAppear(xpath));
        }
    }

    /**
     * Проверить у списка наличие подписи для элемента структуры, если да, то совпадает ли название на подписи с
     * названием элемента структуры.
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param expected ожидаемое значение, true - присутствует, иначе false
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertShowNameElementPresent(ContentForm content, StructuredObjectsViewItem structuredItem,
            boolean expected, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + SHOW_NAME_TILE;
        if (expected)
        {
            Assert.assertTrue("Название элемента структуры не отображается", tester.waitAppear(xpath));
            String xpathToName = xpath + SHOW_NAME_NAME;
            WebElement element = tester.find(xpathToName);
            Assert.assertEquals("Отображаемое название не соответствует названию элемента структуры",
                    structuredItem.getTitle().toUpperCase(), element.getText());
        }
        else
        {
            Assert.assertTrue("Название элемента структуры отображается", tester.waitDisappear(xpath));
        }
    }

    /**
     * Проверить значение вещественного атрибута прижато к правому краю
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param attribute атрибут
     * @param bo элемент
     */
    public static void assertAttributeDoubleValuePosition(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, Bo bo)
    {
        String attributeXpath = getAttributeXpath(content, structuredItem, attribute, bo)
                                + String.format(Div.ANY_CLASS_CONTAINS, "doubleView") + Div.WITHOUT_CLASS;
        WebElement element = tester.findDisplayed(attributeXpath);
        Assert.assertTrue("Значение атрибута  не выравнено по правому краю",
                element != null && element.getCssValue("text-align").equals("right"));
    }

    /**
     * Навести указатель мыши на шапку столбца
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void hoverColumn(ContentForm content, StructuredObjectsViewItem structuredItem, String codeOfColumn,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(ATTRIBUTE_IN_TITLE,
                codeOfColumn));
        GUITester.assertPresent(xpath, "Колонка, на которую нужно навести курсор не отображается");
        tester.moveTo(xpath);
    }

    /**
     * Навести указатель мыши на шапку столбца
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param indexOfColumn Индекс столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void hoverColumn(ContentForm content, StructuredObjectsViewItem structuredItem, int indexOfColumn,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(INDEX_OF_COLUMN,
                indexOfColumn));
        tester.moveTo(xpath);
    }

    /**
     * Проверить, что в таблице присутсвует столбец
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Проверяемый атрибут
     * @param value Значение атрибута
     * @param element Проверяемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertSimpleAttributeValue(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, String value, Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos);
        xpath += String.format(ELEMENT_IN_TABLE, element.getUuid());
        xpath += String.format(ATTRIBUTE_VALUE_IN_ROW, attribute.getFqn(), value);
        String message = String.format("В проверяемом списке нет элемента со значением %s атрибута с fqn %s", value,
                attribute.getFqn());
        GUITester.assertPresent(xpath, message);
    }

    /**
     * Проверить, что значок сортировки активен около указанного атрибута
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код проверяемого столбца
     * @param order Порядок, если true - то прямой поряду, если false - обратный порядок
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertSorting(ContentForm content, StructuredObjectsViewItem structuredItem, String codeOfColumn,
            Boolean order, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(SORTED_ATTRIBUTE_IN_TITLE,
                codeOfColumn, order ? "ascending" : "descending"));
        GUITester.assertPresent(xpath, "Колонка не отсортированна или отсортирована в неправильном порядке");
    }

    /**
     * Кликнуть по значению атрибута у элемента в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickByAttribute(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, Bo element, Bo... bos)
    {
        String xpath = getAttributeXpath(content, structuredItem, attribute, element, bos) + A.A_PREFIX;
        tester.click(xpath);
    }

    /**
     * Кликнуть по шапке столбца
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickByColumn(ContentForm content, StructuredObjectsViewItem structuredItem, String codeOfColumn,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(ATTRIBUTE_IN_TITLE,
                codeOfColumn));
        tester.click(xpath);
    }

    /**
     * Кликниуть по элементу в столбце "Наименование"
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickByElement(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + CELL + "//a", element.getUuid());
        tester.click(xpath);
    }

    /**
     * Кликниуть на значек "Развернуть"
     * @param content Контент типа Иерархическое дерево
     */
    public static void clickExpand(HierarchyGridContent content)
    {
        tester.click(String.format(HIERARCHYGRID_EXPAND, content.getCode()));
    }

    /**
     * Сликнуть по значку быстрой фильтрации в шапке грида
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид(может быть null в случае самовложенного грида)
     * @param attribute Атрибут, которому соотсвует столбец
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickFilterAttribute(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, boolean active, Bo... bos)
    {
        String filter = active ? ACTIVE_FILTER : FILTER;
        String xpath = getXpathToGridInContent(content, structuredItem, bos)
                       + String.format(ATTRIBUTE_IN_TITLE_WITH_FQN, attribute.getCode(), attribute.getParentFqn())
                       + filter;
        try
        {
            tester.moveTo(xpath);
            tester.click(xpath);
        }
        catch (Exception e)
        {
            tester.clickOnInvisibleElement(xpath);
        }
    }

    /**
     * Кликнуть по значку быстрой фильтрации в шапке грида по названию атрибута
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид(может быть null в случае самовложенного грида)
     * @param title Название атрибута
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */

    public static void clickFilterAttributeByTitle(ContentForm content, StructuredObjectsViewItem structuredItem,
            String title, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(TITLE, title) + FILTER;
        try
        {
            tester.moveTo(xpath);
            tester.click(xpath);
        }
        catch (Exception e)
        {
            tester.clickOnInvisibleElement(xpath);
        }
    }

    /**
     * Кликнуть на значок мишени около контента
     * @param content Контент типа Иерархическое дерево
     */
    public static void clickFocus(ContentForm content)
    {
        String xpath = String.format(GUIXpath.Div.ID_PATTERN, content.getXpathId()) + FOCUS;
        tester.click(xpath);
    }

    /**
     * Кликнуть на значок мишени около контента
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickFocus(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + FOCUS;
        tester.click(xpath);
    }

    /**
     * Кликнуть по значению атрибута RTF у элемента в гриде (клик по xpath внутри iframe)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут
     * @param element Элемент
     * @param xpathInIframe xpath внутри iframe
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickInIframeByAttribute(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, String xpathInIframe, Bo element, Bo... bos)
    {
        String xpath = getAttributeXpath(content, structuredItem, attribute, element, bos) + Any.IFRAME;
        GUIRichText.doInIframe(xpath, () ->
        {
            tester.click(xpathInIframe);
            return null;
        });
    }

    /**
     * Перейти на последнюю страницу в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickLastPage(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + LAST_PAGE;
        tester.click(xpath);
    }

    /**
     * Перейти на следующую страницу в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickNextPage(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + NEXT_PAGE;
        tester.click(xpath);
    }

    /**
     * Перейти на предыдущую страницу в гриде
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void clickPreviousPage(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + PREVIOUS_PAGE;
        tester.click(xpath);
    }

    /**
     * Показать только найденные объекты
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрытый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void collapseBranch(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + CHILD_COLLAPSE_ELEMENT, element.getUuid());
        tester.click(xpath);
    }

    /**
     * Скрыть грид, раскрытый на элементе
     * @param content Контент типа Иерархическое дерево (либо null, если СФДС)
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрытый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void collapseElement(@Nullable ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element,
            Bo... bos)
    {
        String expandXpath = getXpathOfExpandButton(content, structuredItem, element, bos);
        String collapseXpath = getXpathOfCollapseButton(content, structuredItem, element, bos);
        Boolean actual = WaitTool.waitSomething(collapseXpath, input ->
        {
            tester.click(input);
            return tester.isPresence(expandXpath);
        });
        Assert.assertTrue("Не удалось скрыть грид", actual != null && actual.booleanValue());
    }

    /**
     * Свернуть элемент структуры по нажатию на Название элемента структуры.
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void collapseShowNameTile(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + SHOW_NAME_TILE + COLLAPSE_TABLE_ELEMENT;
        tester.click(xpath);
    }

    /**
     * Раскрыть грид у элемента и гриды у его предков
     * @param content Контент типа Иерархическое дерево
     * @param element Последний раскрываемый элемент
     * @param bos Список объектов до грида, которые нужно раскрыть(может быть пуст, тогда считается, что это первый
     *            уровень)
     */
    public static void expandAllElements(ContentForm content, Bo element, Bo... bos)
    {
        StringBuilder xpath = new StringBuilder(String.format(GUIXpath.Div.ID_PATTERN, content.getXpathId()));
        if (bos.length > 0)
        {
            for (int i = 0; i < bos.length; i++)
            {
                tester.click(xpath.toString() + String.format(ELEMENT_IN_TABLE + EXPAND_ELEMENT, bos[i].getUuid()));
                xpath.append(String.format(GRID_IN_TABLE, bos[i].getUuid()));
            }
        }
        else
        {
            xpath.append(MAIN_GRID);
        }
        xpath.append(String.format(ELEMENT_IN_TABLE + EXPAND_ELEMENT, element.getUuid()));
        tester.click(xpath.toString());
    }

    /**
     * Показать вложенные объекты у родителя при включенном поиске
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрываемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void expandBranch(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + CHILD_EXPAND_ELEMENT, element.getUuid());
        tester.click(xpath);
    }

    /**
     * Раскрыть грид у элемента
     * @param content Контент типа Иерархическое дерево (либо null, если СФДС)
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрываемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void expandElement(@Nullable ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element,
            Bo... bos)
    {
        String expandXpath = getXpathOfExpandButton(content, structuredItem, element, bos);
        String collapseXpath = getXpathOfCollapseButton(content, structuredItem, element, bos);
        Boolean actual = WaitTool.waitSomething(expandXpath, input ->
        {
            tester.click(input);
            return tester.isPresence(collapseXpath);
        });
        Assert.assertTrue("Не удалось раскрыть грид", actual != null && actual.booleanValue());
    }

    /**
     * Раскрыть элемент структуры по нажатию на Название элемента структуры.
     * @param content контент типа "Иерархическое дерево"
     * @param structuredItem элемент структуры, в которой находится грид
     * @param bos список объектов по пути до грида (может быть пуст, тогда считается, что это первый уровень)
     */
    public static void expandShowNameTile(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + SHOW_NAME_TILE + EXPAND_TABLE_ELEMENT;
        tester.click(xpath);
    }

    /**
     * Возвращает объект для работы с отдельной настройкой фильтрации иерархического списка.
     */
    public static GUIAdvListFiltering filterSettingsForm()
    {
        if (null == filterSettingsForm)
        {
            filterSettingsForm = new GUIAdvListFiltering("//div[@id='gwt-debug-hierarchyFilterFrom-value']");
        }
        return filterSettingsForm;
    }

    /**
     * Возвращает объект для работы с таблицей настроек фильтрации иерархического списка.
     */
    public static GUIAdvListContent filterSettingsTable()
    {
        if (null == filterSettingsTable)
        {
            filterSettingsTable = new GUIAdvListContent(Div.FORM);
        }
        return filterSettingsTable;
    }

    /**
     * Метод строит путь до грида
     * @param content Контент типа Иерархическое дерево (либо null, если СФДС)
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     * @return путь до грида
     */
    public static String getXpathToGridInContent(@Nullable ContentForm content,
            StructuredObjectsViewItem structuredItem,
            Bo... bos)
    {
        String contentId =
                content == null ? GUIComplexRelationForm.COMPLEX_RELATION_HIERARCHY_GRID : content.getXpathId();
        StringBuilder xpath = new StringBuilder(String.format(GUIXpath.Div.ID_PATTERN, contentId));
        if (bos.length > 0)
        {
            for (Bo bo : bos)
            {
                xpath.append(String.format(GRID_IN_TABLE, bo.getUuid()));
            }

            xpath.append(String.format(CHILD_GRID, structuredItem.getCode()));
        }
        else
        {
            xpath.append(MAIN_GRID);
        }
        return xpath.toString();
    }

    /**
     * Возвращает объект для работы с отдельной настройкой ограничения иерархического списка.
     */
    public static GUIAdvListFiltering objectFilterSettingsForm()
    {
        if (null == objectFilterSettingsForm)
        {
            objectFilterSettingsForm = new GUIAdvListFiltering(
                    "//div[@id='gwt-debug-hierarchyObjectFilterFrom-value']");
        }
        return objectFilterSettingsForm;
    }

    /**
     * Выполняет поиск в иерархическом дереве.
     * @param content контент типа «Иерархическое дерево»
     * @param query поисковый запрос
     */
    public static void search(ContentForm content, String query)
    {
        String contentXPath = String.format(Div.ID_PATTERN, content.getXpathId());
        tester.sendKeys(contentXPath + GUISearch.X_SIMPLE_SEARCH_INPUT, query);
        tester.click(contentXPath + GUISearch.X_SIMPLE_SEARCH_BUTTON);
    }

    /**
     * Выбрать элемент (поставить галочку в чекбоксе)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void selectElement(ContentForm content, StructuredObjectsViewItem structuredItem, Bo element,
            Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) +
                       String.format(ELEMENT_IN_TABLE + BOX, element.getUuid());
        tester.click(ScrollAlignment.START, ScrollAlignment.END, false, xpath);
    }

    /**
     * Выбрать элементы первого уровня(поставить галочку в чекбоксе)
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param elements Элементы
     */
    public static void selectElements(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo... elements)
    {
        for (Bo element : elements)
        {
            String xpath = getXpathToGridInContent(content, structuredItem) +
                           String.format(ELEMENT_IN_TABLE + BOX, element.getUuid());
            tester.click(xpath);
        }
    }

    /**
     * Установить странице в пейдженге
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param number Устанавливаемая страница
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void setPage(ContentForm content, StructuredObjectsViewItem structuredItem, int number, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos);
        xpath += PAGER_INPUT;
        tester.sendKeys(xpath, String.valueOf(number));
        tester.actives().sendKeys(Keys.ENTER);
    }

    /**
     * Перетащить границу колонки на заданное число пикселей
     * @param content
     * @param structuredItem
     * @param codeOfColumn
     * @param xOffset
     * @param bos
     */
    public static void dragAndDropColumnSplitter(ContentForm content, StructuredObjectsViewItem structuredItem,
            String codeOfColumn, int xOffset, Bo... bos)
    {
        GUIHierarchyGrid.hoverColumn(content, structuredItem, codeOfColumn, bos);

        String xpath = getXpathToGridInContent(content, structuredItem, bos) + COLUMN_RESIZER;
        tester.dragAndDropWithOffset(xpath, xOffset, 0);
    }

    /**
     * Получить ширину столбца иерархического списка
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param codeOfColumn Код столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static int getColumnWidth(ContentForm content, StructuredObjectsViewItem structuredItem,
            String codeOfColumn, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(ATTRIBUTE_IN_TITLE,
                codeOfColumn));
        GUITester.assertPresent(xpath, "Колонка не отображается");
        WebElement column = tester.getElement(xpath);
        return column.getSize().width;
    }

    /**
     * Получить ширину столбца иерархического списка
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param columnIndex индекс столбца
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static int getColumnWidth(ContentForm content, StructuredObjectsViewItem structuredItem,
            int columnIndex, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + (String.format(INDEX_OF_COLUMN,
                columnIndex));
        GUITester.assertPresent(xpath, "Колонка не отображается");
        WebElement column = tester.getElement(xpath);
        return column.getSize().width;
    }

    /**
     * Получить ширину таблицы иерархического списка
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static int getTableWidth(ContentForm content, StructuredObjectsViewItem structuredItem, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos) + TABLE_IN_GRID;
        WebElement table = tester.getElement(xpath);
        return table.getSize().width;
    }

    /**
     * Метод строит путь до грида
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут
     * @param element Элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    private static String getAttributeXpath(ContentForm content, StructuredObjectsViewItem structuredItem,
            Attribute attribute, Bo element, Bo... bos)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos);
        xpath += String.format(ELEMENT_IN_TABLE, element.getUuid());
        xpath += String.format("//td[@data-attr-fqn='%s']/*", attribute.getFqn());
        return xpath;
    }

    /**
     * Получить xpath кнопки скрытия грида
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрываемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     * @return xpath кнопки скрытия грида
     */
    private static String getXpathOfCollapseButton(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element, Bo... bos)
    {
        return getXpathToGridInContent(content, structuredItem, bos) +
               String.format(ELEMENT_IN_TABLE + COLLAPSE_ELEMENT, element.getUuid());
    }

    /**
     * Получить xpath кнопки раскрытия грида
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param element Раскрываемый элемент
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     * @return xpath кнопки раскрытия грида
     */
    private static String getXpathOfExpandButton(ContentForm content, StructuredObjectsViewItem structuredItem,
            Bo element, Bo... bos)
    {
        return getXpathToGridInContent(content, structuredItem, bos) +
               String.format(ELEMENT_IN_TABLE + EXPAND_ELEMENT, element.getUuid());
    }

    /**
     * Строит путь до последнего в списке элемента структуры
     * @param content Контент типа "Иерархическое дерево"
     * @param items список элементов структуры, начинается с корневого элемента (первый уровень), заканчивается тем
     * элементом, до которого строится путь. selfNested элементы учитываются.
     * @return путь до последнего в списке элемента структуры
     */
    private static String getXpathOfGridInAdmin(ContentForm content, StructuredObjectsViewItem... items)
    {
        StringBuilder xpath = new StringBuilder(String.format(GUIXpath.Div.ID_PATTERN, content.getXpathId()));
        int length = items.length;
        if (length > 0)
        {
            for (int i = 0; i < items.length; i++)
            {
                if (i == 0)
                {
                    xpath.append(MAIN_GRID);
                }
                else
                {
                    xpath.append(String.format(CHILD_GRID, items[i].getCode()));
                    if (i != items.length - 1 && !items[i].isShowNested())
                    {
                        xpath.append(String.format(GRID_IN_TABLE, items[i].getCode()));
                    }
                }
                if (items[i].isShowNested())
                {
                    xpath.append(SELF_NESTED_GRID);
                    if (i != items.length - 1)
                    {
                        xpath.append(String.format(GRID_IN_TABLE, items[i].getCode()));
                    }
                }
            }
            xpath.append(String.format(ELEMENT_IN_TABLE, items[items.length - 1].getCode()));
        }
        return xpath.toString();
    }

    /**
     * Метод формирует xpath ячейки элемента в списке //tr[@debug-id=uuid and contains(@class, 'k-master-row')]
     * @param uuid uuid элемента
     */
    public static String trDebugId(String uuid)
    {
        return String.format(ELEMENT_IN_TABLE, uuid);
    }

    /**
     * Проверить наличие бейджа '(окр.)' у объекта в иерархическом списке
     * @param content Контент типа "Иерархическое дерево"
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут, в котором должен быть бейдж '(окр.)'
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     * @param bo Объект значение которого отображается в строке иерархического списка
     * @param attrBo Объект, значение которого отображается в колонке иерархического списка
     */
    public static void assertAttrEnvironmentBadge(HierarchyGridContent content,
            StructuredObjectsViewItem structuredItem,
            Attribute attribute, List<Bo> bos, @Nullable ModelUuid bo, ModelUuid attrBo)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos.toArray(new Bo[bos.size()]))
                       + String.format(ATTRIBUTE_BADGE_IN_TABLE, bo.getUuid(), attribute.getFullFqn(), attrBo.getUuid(),
                GUIBo.ENVIRONMENT_INLINE_BADGE);
        GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
    }

    /**
     * Проверить наличие бейджа '(вер.)' у объекта в иерархическом списке
     * @param content Контент типа "Иерархическое дерево"
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attribute Атрибут, в котором должен быть бейдж '(окр.)'
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     * @param bo Объект, значение которого отображается в строке иерархического списка
     * @param attrBo Объект, значение которого отображается в колонке иерархического списка
     */
    public static void assertAttrVersionBadge(HierarchyGridContent content,
            StructuredObjectsViewItem structuredItem,
            Attribute attribute, List<Bo> bos, @Nullable ModelUuid bo, ModelUuid attrBo)
    {
        String xpath = getXpathToGridInContent(content, structuredItem, bos.toArray(new Bo[bos.size()]))
                       + String.format(ATTRIBUTE_BADGE_IN_TABLE, bo.getUuid(), attribute.getFullFqn(), attrBo.getUuid(),
                GUIBo.VERSION_INLINE_BADGE);
        GUITester.assertPresent(xpath, "Во вложенном списке нет проверяемого элемента");
    }

    /**
     * Кликнуть по значку свернуть/развернуть грид, работает только для первого уровня, если элемент был
     * развёрнут - сворачивается, если свёрнут - раскрывается
     * @param structuredItem Элемент структуры, в которой находится грид
     */
    public static void clickExpandItem(StructuredObjectsViewItem structuredItem)
    {
        tester.click(Div.ANY + EXPAND_TABLE_ELEMENT, structuredItem.getCode());
    }

    /**
     * Проверить, что в контенте присутсвуют столбцы
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attributes Список атрибутов которые должны отображаться
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertPresenceColumns(ContentForm content, StructuredObjectsViewItem structuredItem,
            List<Attribute> attributes, Bo... bos)
    {
        for (Attribute attribute : attributes)
        {
            GUIHierarchyGrid.assertPresenceColumn(content, structuredItem, attribute.getCode(), bos);
        }
    }

    /**
     * Проверить, что в таблице отсутсвуют столбцы
     * @param content Контент типа Иерархическое дерево
     * @param structuredItem Элемент структуры, в которой находится грид
     * @param attributes Список столбцов которые не должны отображаться
     * @param bos Список объектов до грида(может быть пуст, тогда считается, что это первый уровень)
     */
    public static void assertAbsenceColumns(ContentForm content, StructuredObjectsViewItem structuredItem,
            List<Attribute> attributes, Bo... bos)
    {
        for (Attribute attribute : attributes)
        {
            GUIHierarchyGrid.assertAbsenceColumn(content, structuredItem, attribute.getCode(), bos);
        }
    }

    /**
     * Проверить порядок item внутри контента
     * @param contentForm Контент типа Иерархическое дерево
     * @param expectedList Ожидаемый список item
     */
    public static void assertItemsOrder(ContentForm contentForm, List<StructuredObjectsViewItem> expectedList)
    {
        List<String> expected =
                expectedList.stream().map(entry -> "gwt-debug-" + entry.getCode()).collect(Collectors.toList());
        GUITester.assertEqualListsByWebElementProperty(String.format(Div.ID_PATTERN, contentForm.getXpathId()) +
                                                       DIV_ROLE_GRID, "id", expected, true, false);
    }

    /**
     * Проверить равенство ширины столбцов с учётом возможной разницы между ними. Разница возникает из-за того, что
     * иногда колонки отрисовываются с небольшим отличием.
     * @param width1 Ширина 1 столбца
     * @param width2 Ширина 2 столбца
     * @param dif Максимальная разница между столбцами
     */
    public static void assertColumnWidth(int width1, int width2, int dif)
    {
        Assert.assertTrue(String.format("Ширина столбцов разная: %s и %s, возможная разница - %s", width1, width2, dif),
                Math.abs(width1 - width2) <= dif);
    }

    /**
     * Получить модель контента иерархического списка на странице.
     * @return модель контента иерархического списка.
     */
    public static ContentForm getHierarchyContentForm()
    {
        String id = tester.find(DIV_HIERARCHY_GRID).getAttribute("id");
        ContentForm content = new ContentForm();
        content.setXpathId(id);
        return content;
    }
}