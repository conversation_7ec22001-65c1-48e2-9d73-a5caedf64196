package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.WebElement;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.GUIXpath.InputComplex;
import ru.naumen.selenium.casesutil.GUIXpath.Span;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel.AppliedTo;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel.ToolPresentation;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionsMenuPosition;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.Tool.PresentationType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для проверок, связанных с редактируемой панелью инструментов через интерфейс
 * <AUTHOR>
 * @since 29.06.2015
 */
public class AGUIAdvListEditableToolPanel extends GUIAdvlist
{

    /**
     *  Варианты расположения иконок указанные через радиокнопки
     */
    public enum RButton
    {
        /** Расположение иконки справа */
        RIGHT("Справа"),

        /** Расположение иконки слева */
        LEFT("Слева"),

        /** Расположение иконки с обеих сторон */
        BOTH("Слева и справа"),

        /** Иконка не отображается */
        NONE("Не отображается");

        private final String description;

        /**
         * Конструктор перечисления
         * @param description Описание вариантов расположения иконок
         */
        RButton(String description)
        {
            this.description = description;
        }

        /**
         * Возвращает описание вариантов расположения иконок
         * @return Текстовое описание варианта расположения
         */
        public String getDescription()
        {
            return description;
        }

        /**
         * Возвращает элемент расположения иконок по его описанию
         * Сравнение выполняется без учета регистра
         * @param description описание вариантов расположения иконки
         */
        public static RButton fromDescription(String description)
        {
            for (RButton button : RButton.values())
            {
                if (button.description.equalsIgnoreCase(description))
                {
                    return button;
                }
            }
            throw new IllegalArgumentException("Неизвестная кнопка: " + description);
        }
    }

    public AGUIAdvListEditableToolPanel(String contentXpath)
    {
        super(contentXpath);
    }

    public void absenceCopyFromTemplate()
    {
        presenceOrAbsenceCopyFromTemplate(false);
    }

    /**
     * Проверить отсутствие иконки "Скопировать настройку из родителя"
     */
    public void absenceCopyParent()
    {
        presenceOrAbsenceCopyParent(false);
    }

    /**
     * Проверить отсутствие кнопки "Сбросить до системной настройки"
     */
    public void absenceReset()
    {
        presenceOrAbsenceReset(false);
    }

    /**
     * Проверить установлен ли переключатель  расположения иконок на определенной позиции
     * @param position позиция
     * @param isChecked true - выставлен, false - иначе
     */
    public void actionMenuPosition(ActionsMenuPosition position, boolean isChecked)
    {
        GUITester.assertRadioButton(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_MENU_POSITION_INPUT, isChecked,
                position.toString());
    }

    /**
     * Проверить действие инструмента на форме редактирования
     * @param action название действия
     */
    public void actionOnForm(String action)
    {
        GUITester.assertTextPresentWithMsg(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_FIELD, action,
                "Неверное действие инструмента");
    }

    /**
     * Проверить инструменты в панели контента в указанном по счету тулбаре (на полное совпадение с учетом порядка)
     * в режиме редактирования
     * @param toolBarNumber номер тулбара (начиная с 1)
     * @param actions набор названий ожидаемых действий
     */
    public void actionsInEditableToolBar(int toolBarNumber, String... actions)
    {
        actionsInToolBar(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_CONTENT
                         + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS, toolBarNumber,
                actions);
    }

    /**
     * Проверить инструменты в панели контента в указанном по счету тулбаре (на полное совпадение с учетом порядка)
     * в режиме просмотра.
     * @param toolBarNumber номер тулбара (начиная с 1)
     * @param actions набор названий ожидаемых действий
     */
    public void actionsInToolBar(int toolBarNumber, String... actions)
    {
        actionsInToolBar(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS, toolBarNumber, actions);
    }

    /**
     * Проверяет значение поля "Применяется к" на форме редактирования.
     * @param appliedTo ожидаемое значение
     */
    public void appliedToOnForm(AppliedTo appliedTo)
    {
        appliedToOnForm(appliedTo, true);
    }

    /**
     * Проверяет значение поля "Применяется к" на форме редактирования.
     * @param appliedTo ожидаемое значение
     * @param editable true, если поле должно быть редактируемым, иначе false
     */
    public void appliedToOnForm(AppliedTo appliedTo, boolean editable)
    {
        if (editable)
        {
            GUISelect.assertSelectedElement(GUIAdvListXpath.EDITABLE_TOOL_FORM_APPLIED_TO_FIELD, appliedTo.getCode());
        }
        else
        {
            GUITester.assertTextPresent(GUIAdvListXpath.EDITABLE_TOOL_FORM_APPLIED_TO_NOT_EDITABLE, appliedTo
                    .getTitle());
        }
    }

    /**
     * Проверить растровую иконку инструмента
     * @param action код действия инструмента
     * @param expected иконка инструмента
     */
    public void buttonHasRasterIcon(String action, String expected)
    {
        final String rawUrl = tester.find(getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_BTN_IMAGE,
                action + '.')).getCssValue("background-image");
        final String url = rawUrl.replaceAll("\"", "");
        final String actual = StringUtils.substringBefore(StringUtils.substringAfter(url, "("), ")");
        Assert.assertEquals("На кнопке инструмента иконка содержит некорректное значение картинки", expected, actual);
    }

    /**
     * Проверить, что выбрана иконка с указанным названием
     * @param iconTitle название иконки
     */
    public static void buttonHasIconWithName(String iconTitle)
    {
        GUITester.assertValue(Div.ANY_VALUE + Input.INPUT_PREFIX, iconTitle, "icon");
    }

    /**
     * Проверить инструменты в панели доступных инструментов в указанном по счету тулбаре
     * (на полное совпадение с учетом порядка)
     * @param toolBarNumber номер тулбара (начиная с 1)
     * @param actions набор названий ожидаемых действий
     */
    public void availableActionsInToolBar(int toolBarNumber, String... actions)
    {
        actionsInToolBar(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_TOOLS
                         + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS, toolBarNumber,
                actions);
    }

    /**
     * Проверяет список доступных для выбора действий на форме редактирования.
     * @param mustExist true, если требуется проверить наличие, false - отсутствие
     * @param eventActions ожидаемый набор действий
     */
    public void availableActionsOnForm(boolean mustExist, EventAction... eventActions)
    {
        String[] codes = Arrays.stream(eventActions).map(ea -> ea.getUserEventUuid())
                .<String> toArray(n -> new String[n]);
        if (mustExist)
        {
            GUISelect.assertDisplayed(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_INPUT, codes);
        }
        else

        {
            GUISelect.assertNotDisplayed(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_INPUT, codes);
        }
    }

    /**
     * Проверяет список доступных для выбора элементов поля "Применяется к" на форме редактирования.
     * @param values значения, которые должны быть доступны для выбора
     */
    public void availableAppliedTo(AppliedTo... values)
    {
        GUISelect.assertDisplayed(GUIAdvListXpath.EDITABLE_TOOL_FORM_APPLIED_TO_FIELD,
                Arrays.stream(values).map(v -> v.getCode()).<String> toArray(n -> new String[n]));
        GUISelect.assertCountElements(GUIAdvListXpath.EDITABLE_TOOL_FORM_APPLIED_TO_FIELD, values.length);
    }

    /**
     * Проверяет список доступных для выбора действий на форме редактирования.
     * @param mustExist true, если требуется проверить наличие, false - отсутствие
     * @param actions ожидаемый набор системных действий
     */
    public void availableSystemActionsOnForm(boolean mustExist, String... actions)
    {
        if (mustExist)
        {
            GUISelect.assertDisplayed(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_INPUT, actions);
        }
        else
        {
            GUISelect.assertNotDisplayed(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_INPUT, actions);
        }
    }

    /**
     * Проверить кол-во тулбаров в панели доступных инструментов
     * @param expected ожидаемое кол-во
     */
    public void availableToolBarCount(int expected)
    {
        GUITester.assertCountElements(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_TOOLS
                                      + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS,
                expected, GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Проверить кол-во тулбаров в панели настроенных инструментов
     * @param expected ожидаемое кол-во
     */
    public void contentToolBarCount(int expected)
    {
        GUITester.assertCountElements(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_CONTENT
                                      + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS,
                expected);
    }

    /**
     * Проверить доступные опции контекстного меню (на полное совпадение с учетом порядка)
     * @param items названия ожидаемых опций контекстного меню
     */
    public void contextMenuItems(String... items)
    {
        GUITester.assertFindElements(GUIAdvListXpath.EDITABLE_TOOL_CONTEXT_MENU_ITEMS, Lists.newArrayList(items), true,
                true);
    }

    /**
     * Проверить, что опции контекстного меню отображаются
     * @param exist true - должен отображаться, false - не должен отображаться
     */
    public void absenceContextMenuOptions(boolean exist)
    {
        GUITester.assertExists(GUIXpath.Div.CONTAINS_FORM + GUIAdvListXpath.EDITABLE_TOOL_CONTEXT_MENU, exist);
    }

    /**
     * Проверить что инструмент не включен
     * @param toolTitle название контрола
     */
    public void disabledByTitle(String toolTitle)
    {
        String opacity = "0.5";
        String value = GUITester.waitCssValue(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_BTN_BY_TEXT, "opacity",
                opacity,
                toolTitle);
        Assert.assertTrue(String.format("Инструмент %s включен", toolTitle), opacity.equals(value));
    }

    /**
     * Проверить кол-во пустых тулбаров в панели настроенных инструментов
     * @param expected - ожидаемое количество пустых тулбаров
     */
    public void emptyToolBarCount(long expected)
    {
        List<WebElement> panelBars = tester.findElements(getContentXpath()
                                                         + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS);
        long actual = panelBars.stream().filter(toolBar -> !toolBar.isDisplayed()).count();
        Assert.assertEquals("Полученное кол-во элементов не совпало с ожидаемым.", expected, actual);
    }

    /**
     * Проверить что инструмент включен
     * @param toolTitle название контрола
     */
    public void enabledByTitle(String toolTitle)
    {
        String opacity = "1";
        String value = GUITester.waitCssValue(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_BTN_BY_TEXT, "opacity",
                opacity,
                toolTitle);
        Assert.assertTrue(String.format("Инструмент %s не включен", toolTitle), opacity.equals(value));
    }

    /**
     * Проверяет существование блока редактируемой панели на форме.
     * @param expected ожидаемое значение
     */
    public void exists(boolean expected)
    {
        GUITester.assertExists(getContentXpath(), expected,
                "Блок редактируемой панели" + (expected ? "отсутствует" : "присутствует"));
    }

    /**
     * Проверить иконку инструмента на форме редактирования
     * @param iconCode ожидаемый код иконки
     */
    public void iconOnForm(CatalogItem iconCode)
    {
        GUISelect.assertSelectedElement(GUIAdvListXpath.EDITABLE_TOOL_FORM_ICON_FIELD, iconCode.getCode());
    }

    /**
     * Проверить доступна или не доступна панель настройки для редактирования и полупрозрачна или не полупрозрачна
     * в зависимости от передаваемого параметра
     * @param disabled  true/false
     */
    public void notEditToolPanel(boolean disabled)
    {
        String opacity = "0.5";
        String value = GUITester.waitCssValue(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_TOOLS + "/parent::*",
                "opacity",
                opacity);
        Assert.assertEquals("Панель настройки " + (disabled ? "не" : "") + "полупрозрачна", disabled,
                opacity.equals(value));
    }

    /**
     * Проверяет выбор указанной формы быстрого добавления.
     * @param form модель формы или null, если форма не должна быть выбрана
     */
    public void quickAddFormSelected(@Nullable CustomForm form)
    {
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickAddForm"),
                null == form ? "[не указано]" : form.getTitle());
    }

    /**
     * Проверяет что список атрибутов, которые могут быть заполнены текущим объектом, не содержит ни одного атрибута.
     */
    public void attrsFilledByCurrentObjectIsEmpty()
    {
        String xpath = String.format(InputComplex.ANY_VALUE, "attributeFillByCurrentObject");
        GUISelect.assertCountElements(xpath, 1);
    }

    /**
     * Проверяет выбор указанного атрибута, который будет заполнен текущим объектом
     * @param attrTitle модель атрибута или null, если атрибут не был выбран
     */
    public void attrFilledByCurrentSelected(String attrTitle)
    {
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, "attributeFillByCurrentObject"),
                null == attrTitle ? "[не указано]" : attrTitle);
    }

    /**
     * Проверяет доступность указанной формы быстрого редактирования.
     * @param form модель формы
     * @param available true, если форма должна быть доступна для выбора, иначе false
     */
    public void quickEditFormAvailable(CustomForm form, boolean available)
    {
        if (available)
        {
            GUISelect.assertDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickEditForm"), form.getUuid());
        }
        else
        {
            GUISelect.assertNotDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickEditForm"), form
                    .getUuid());
        }
    }

    /**
     * Проверяет выбор указанной формы быстрого редактирования.
     * @param form модель формы или null, если форма не должна быть выбрана
     */
    public void quickEditFormSelected(@Nullable CustomForm form)
    {
        GUISelect.assertSelected(String.format(GUIXpath.InputComplex.ANY_VALUE, "quickEditForm"),
                null == form ? "[не указано]" : form.getTitle());
    }

    /**
     * Проверить наличие кнопки "Скопировать настройку из родителя"
     */
    public void presenceCopyParent()
    {
        presenceOrAbsenceCopyParent(true);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Скопировать настройку из родителя" для меню действий с объектом
     * @param exist - true должна присутствовать; false - должна отсутствовать
     */
    public void presenceOrAbsenceCopyObjectActionsFromParent(boolean exist)
    {
        String message = String.format("Кнопка \"Скопировать настройку из родителя\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(GUIAdvListXpath.EDITABLE_TOOL_COPY_ACTIONS_FROM_PARENT_BTN, exist, message);
    }

    /**
     * Проверить наличие кнопки "Сбросить до системной настройки"
     */
    public void presenceReset()
    {
        presenceOrAbsenceReset(true);
    }

    /**
     * Проверить представление инструмента на форме редактирования
     * @param presentation ожидаемый код представления
     */
    public void presentationOnForm(ToolPresentation presentation)
    {
        presentationOnForm(presentation, true);
    }

    /**
     * Проверить представление инструмента на форме редактирования
     * @param presentation ожидаемый код представления
     * @param editable true, если поле должно быть редактируемым, иначе false
     */
    public void presentationOnForm(ToolPresentation presentation, boolean editable)
    {
        if (editable)
        {
            GUISelect.assertSelectedElement(GUIAdvListXpath.EDITABLE_TOOL_FORM_PRESENTATION_FIELD, presentation
                    .getCode());
        }
        else
        {
            GUITester.assertTextPresent(GUIAdvListXpath.EDITABLE_TOOL_FORM_PRESENTATION_NOT_EDITABLE, presentation
                    .getTitle());
        }
    }

    /**
     * Проверить, что на форме редактирования нельзя выбрать "Действие"
     */
    public void selectActionAbsence()
    {
        GUITester.assertExists(GUIAdvListXpath.EDITABLE_TOOL_FORM_ACTION_INPUT, false,
                "Есть возможность выбрать Действие");
    }

    /**
     * Проверить наименование инструмента на форме редактирования
     * @param expected ожидаемое наименование
     */
    public void titleOnForm(String expected)
    {
        GUITester.assertValueWithMsg(GUIAdvListXpath.EDITABLE_TOOL_FORM_TITLE_FIELD, expected,
                "Неверное наименование инструмента");
    }

    /**
     * Проверить отсутствие инструмента по его названию
     * @param title
     */
    public void toolAbsenceByTitle(String title)
    {
        presenceOrAbsenceButtonByTitle(title, false);
    }

    /**
     * Проверить, что инструмент отсутствует в панели доступных действий
     * @param action код действия инструмента
     */
    public void toolAbsenceInAvailableTools(String action)
    {
        presenceOrAbsenceInAvailableTools(action, false);
    }

    /**
     * Проверить, что инструмент отсутствует в панели действий
     * @param action код действия инструмента
     */
    public void toolAbsenceInToolPanel(String action)
    {
        presenceOrAbsenceInToolPanel(action, false);
    }

    /**
     * Проверить отсутствие инструмента в ToolPanel по его названию
     * @param title
     */
    public void toolAbsenceInToolPanelByTitle(String title)
    {
        presenceOrAbsenceInToolPanelByTitle(title, false);
    }

    /**
     * Проверить, что инструмент ссылка отсутствует в панели действий
     * @param action код действия инструмента
     */
    public void toolAbsenceInToolPanelLink(String action)
    {
        String msg = "Представление инструмента '" + action + "' не является ссылкой";
        GUITester.assertExists(
                getContentXpath() + GUIAdvListXpath.EDIT_TOOL_PANEL_NOT_TOOLS + String.format(
                        GUIAdvListXpath.EDITABLE_TOOL_LINK, action), false, msg);
    }

    /**
     * Проверить, что инструмент ссылка отсутствует
     * @param action код действия инструмента
     */
    public void toolAbsenceLink(String action)
    {
        String msg = "Представление инструмента '" + action + "' не является ссылкой";
        GUITester.assertExists(getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_LINK, action), false,
                msg);
    }

    /**
     * Проверить отсутствие инструмента ссылки по его названию
     * @param title
     */
    public void toolAbsenceLinkByTitle(String title)
    {
        presenceOrAbsenceLinkByTitle(title, false);
    }

    /**
     * Проверить отсутствие инструмента ссылки в ToolPanel по его названию
     * @param title
     */
    public void toolAbsenceLinkInToolPanelByTitle(String title)
    {
        presenceOrAbsenceLinkInToolPanelByTitle(title, false);
    }

    /**
     * Проверить видимость иконки инструмента
     * @param action код действия инструмента
     * @param visible должен быть видимым или нет
     */
    public void toolImageVisible(String action, boolean visible)
    {
        String msg = "У инструмента '" + action + "' " + (visible ? "отсутствует" : "присутствует") + " иконка.";
        GUITester.assertExists(String.format(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_BTN_IMAGE, action),
                visible, msg);
    }

    /**
     * Проверить, что представлением инструмента в панели действий является ссылка
     * @param action код действия инструмента
     */
    public void toolInToolPanelLink(String action)
    {
        String msg = "Представление инструмента '" + action + "' не является ссылкой";
        GUITester.assertExists(
                getContentXpath() + GUIAdvListXpath.EDIT_TOOL_PANEL_NOT_TOOLS + String.format(
                        GUIAdvListXpath.EDITABLE_TOOL_LINK, action), true, msg);
    }

    /**
     * Проверить, что представлением инструмента является ссылка
     * @param action код действия инструмента
     */
    public void toolLink(String action)
    {
        String msg = "Представление инструмента '" + action + "' не является ссылкой";
        GUITester.assertExists(getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_LINK, action), true,
                msg);
    }

    /**
     * Проверить текст инструмента ссылки
     * @param action код действия инструмента
     * @param expected текст инструмента
     */
    public void toolLinkText(String action, String expected)
    {
        String msg = "Полученный текст инструмента не совпал с ожидаемым.";
        GUITester.assertTextPresentWithMsg(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_LINK, expected, msg,
                action);
    }

    /**
     * Проверить присутствие/отсутствие инструмента на панели неиспользуемых элементов
     * @param action инструмент
     * @param isPresent true - присутствует, false - иначе
     */
    public void toolOnDebugPanel(String action, boolean isPresent)
    {
        String toolXpath = getContentXpath() + GUIAdvListEditableToolPanel.DEBUG_PANEL_TOOL
                           + String.format(GUIAdvListXpath.TOOL_BUTTON, action);
        String message = String.format("%s инструмент %s в панели неиспользуемых элементов",
                isPresent ? "отсутствует" : "присутствует", action);
        GUITester.assertExists(toolXpath, isPresent, message);
    }

    /**
     * Проверить присутствие инструмента по его названию
     * @param title
     */
    public void toolPresenceByTitle(String title)
    {
        presenceOrAbsenceButtonByTitle(title, true);
    }

    /**
     * Проверить присутствие ссылки инструмента "Редактировать"
     */
    public void toolPresenceEditLinkByTitle()
    {
        presenceOrAbsenceEditLink(true);
    }

    /**
     * Проверить, что инструмент присутствует в панели действий
     * @param action код действия инструмента
     */
    public void toolPresenceInToolPanel(String action)
    {
        presenceOrAbsenceInToolPanel(action, true);
    }

    /**
     * Проверить присутствие инструмента в ToolPanel по его названию
     * @param title
     */
    public void toolPresenceInToolPanelByTitle(String title)
    {
        presenceOrAbsenceInToolPanelByTitle(title, true);
    }

    /**
     * Проверить присутствие инструмента ссылки по его названию
     * @param title
     */
    public void toolPresenceLinkByTitle(String title)
    {
        presenceOrAbsenceLinkByTitle(title, true);
    }

    /**
     * Проверить присутствие инструмента ссылки в ToolPanel по его названию
     * @param title
     */
    public void toolPresenceLinkInToolPanelByTitle(String title)
    {
        presenceOrAbsenceLinkInToolPanelByTitle(title, true);
    }

    /**
     * Проверить текст инструмента
     * @param action код действия инструмента
     * @param expected текст инструмента
     */
    public void toolText(String action, String expected)
    {
        String msg = "Полученный текст инструмента не совпал с ожидаемым.";
        GUITester.assertTextPresentWithMsg(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_BTN_TEXT, expected, msg,
                action);
    }

    /**
     * Проверить видимость текста инструмента
     * @param action код действия инструмента
     * @param visible должен быть видимым или нет
     */
    public void toolTextVisible(String action, boolean visible)
    {
        String msg = "У инструмента '" + action + "' " + (visible ? "отсутствует" : "присутствует") + " текст.";
        GUITester.assertExists(getContentXpath() + String.format(getContentXpath()
                                                                 + GUIAdvListXpath.EDITABLE_TOOL_BTN_TEXT, action),
                visible, msg);
    }

    /**
     * Проверить значение чекбокса "Использовать системные настройки" на форме редактирования тулпанели
     * @param value ожидаемое значение
     */
    public void useSystemSettingsValue(boolean value)
    {
        GUITester.assertCheckboxState(
                getContentXpath() + GUIAdvListEditableToolPanel.X_USE_SYSTEM_SETTINGS_CHECKBOX_INPUT,
                value);
    }

    /**
     * Проверить значение чекбокса "Использовать системные настройки" на форме редактирования тулпанели
     * @param value ожидаемое значение
     */
    public void useSystemSettingsValueWithoutToolPanel(boolean value)
    {
        GUITester.assertCheckboxState(GUIAdvListEditableToolPanel.X_USE_SYSTEM_SETTINGS_CHECKBOX_INPUT, value);
    }

    /**
     * Проверить "Внешний вид" инструмента на форме редактирования
     * @param expected ожидаемое представление
     */
    public void viewOnForm(PresentationType expected)
    {
        GUITester.assertValue(GUIAdvListXpath.EDITABLE_TOOL_FORM_PRESENTATION_FIELD, expected.getFormTitle());
    }

    /**
     * Проверить присутствие/отсутствие инструмента ввиде кнопки по названию
     * @param title название
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceButtonByTitle(String title, boolean isPresent)
    {
        String xpath = getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_BTN_BY_TEXT, title);
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", title);
        GUITester.assertExists(xpath, isPresent, message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента Редактировать в виде ссылки по названию
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceEditLink(boolean isPresent)
    {
        String message = String.format("%s инструмент Редактировать", isPresent ? "Отсутствует" : "Присутствует");
        GUITester.assertExists(getContentXpath() + GUIXpath.Div.CONTAINS_EDIT_PROPERTIES, isPresent, message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента в списке доступных по коду
     * @param action код действия инструмента
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceInAvailableTools(String action, boolean isPresent)
    {
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", action);
        GUITester.assertExists(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_TOOLS
                               + GUIAdvListXpath.EDITABLE_TOOL_CONTENT_TOOL_BARS
                               + String.format(GUIAdvListXpath.EDITABLE_TOOL_BTN, action), isPresent, message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента в ToolPanel по коду
     * @param action код действия инструмента
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceInToolPanel(String action, boolean isPresent)
    {
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", action);
        GUITester.assertExists(getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_BTN, action), isPresent,
                message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента в ToolPanel по названию
     * @param title название
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceInToolPanelByTitle(String title, boolean isPresent)
    {
        String xpath = getContentXpath() + GUIAdvListXpath.EDIT_TOOL_PANEL_NOT_TOOLS + String.format(
                GUIAdvListXpath.EDITABLE_TOOL_BTN_BY_TEXT, title);
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", title);
        GUITester.assertExists(xpath, isPresent, message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента ссылки по названию
     * @param title название
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceLinkByTitle(String title, boolean isPresent)
    {
        String xpath = getContentXpath() + String.format(GUIAdvListXpath.EDITABLE_TOOL_LINK_BY_TEXT, title);
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", title);
        GUITester.assertExists(xpath, isPresent, message);
    }

    /**
     * Проверить присутствие/отсутствие инструмента ссылки по названию
     * @param title название
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    private void presenceOrAbsenceLinkInToolPanelByTitle(String title, boolean isPresent)
    {
        String xpath = getContentXpath() + GUIAdvListXpath.EDIT_TOOL_PANEL_NOT_TOOLS + String.format(
                GUIAdvListXpath.EDITABLE_TOOL_LINK_BY_TEXT, title);
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", title);
        GUITester.assertExists(xpath, isPresent, message);

    }

    /**
     * Проверить инструменты в панели в указанном по счету тулбаре (на полное совпадение с учетом порядка)
     * @param xpath путь до тулбаров с инструментами
     * @param toolBarNumber номер тулбара (начиная с 1)
     * @param actions набор названий ожидаемых действий
     */
    private void actionsInToolBar(String xpath, int toolBarNumber, String... actions)
    {
        Preconditions.checkArgument(actions.length != 0);
        List<String> expected = Lists.newArrayList(actions);
        List<String> actual = new ArrayList<>();
        boolean isContinue = true;
        long currentTime = System.currentTimeMillis();
        while (isContinue && System.currentTimeMillis() - currentTime < WAIT_TIME * 1000)
        {
            try
            {
                List<WebElement> toolBars = tester.findDisplayedElements(xpath);
                if (toolBars.size() >= toolBarNumber)
                {
                    WebElement toolBar = toolBars.get(toolBarNumber - 1);
                    List<WebElement> tools = toolBar.findElements(By.xpath(
                            ".//*[contains(@id, 'gwt-debug-') and not(@id='gwt-debug-icon') and not"
                            + "(@id='gwt-debug-text')]"));
                    List<String> actionIds = Lists.transform(tools, (WebElement input) ->
                    {
                        if (input != null && input.isDisplayed())
                        {
                            String id = input.getAttribute("id");
                            String pattern = "gwt-debug-(\\w+)";
                            Pattern r = Pattern.compile(pattern);
                            Matcher m = r.matcher(id);
                            return m.find() ? m.group(1) : id;
                        }
                        return null;
                    });
                    actual.clear();
                    actual.addAll(actionIds.stream().filter(Objects::nonNull).collect(Collectors.toList()));
                    isContinue = !expected.equals(actual);
                }
            }
            catch (WebDriverException e)
            {
                continue;
            }
        }
        String msg = "Полученный список инструментов не совпал с ожидаемым.";
        Assert.assertEquals(msg, expected, actual);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Применить настройки из шаблона"/"Скопировать настройки из шаблона"
     * @param exist - true должна присутствовать; false - должна отсутствовать
     */
    public void presenceOrAbsenceCopyFromTemplate(boolean exist)
    {
        String message = String.format(
                "Кнопка \"Применить настройки из шаблона\"/\"Скопировать настройки из шаблона\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_COPY_FROM_TMPL_BTN, exist, message);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Скопировать настройку из родителя"
     * @param exist - true должна присутствовать; false - должна отсутствовать
     */
    private void presenceOrAbsenceCopyParent(boolean exist)
    {
        String message = String.format("Кнопка \"Скопировать настройку из родителя\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(GUIAdvListXpath.EDITABLE_TOOL_COPY_PARENT_BTN, exist, message);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Сбросить до системной настройки"
     * @param exist - true должна присутствовать; false - должна отсутствовать
     */
    public void presenceOrAbsenceReset(boolean exist)
    {
        String message = String.format("Кнопка \"Сбросить до системной настройки\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(getContentXpath() + GUIAdvListXpath.EDITABLE_TOOL_RESET_BTN, exist, message);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Редактировать"
     * @param lineNumber номер строки элемента меню
     * @param exist - true должна присутствовать; false - должна отсутствовать
     */
    public void presenceOrAbsenceEdit(int lineNumber, boolean exist)
    {
        String message = String.format("Кнопка \"Редактировать\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(getContentXpath() + GUIAdvListEditableToolPanel.X_ITEM_ROW + GUIXpath.Span.EDIT_ICON,
                exist, message, lineNumber);
    }

    /**
     * Проверить отсутствие или наличие кнопки "Удалить"
     * @param exist - true должна присутствовать; false - должна отсутствовать
     * @param lineNumber номер строки элемента меню
     */
    public void presenceOrAbsenceDelete(int lineNumber, boolean exist)
    {
        String message = String.format("Кнопка \"Удалить\" %s",
                exist ? "отсутствует" : "присутствует");
        GUITester.assertExists(getContentXpath() + GUIAdvListEditableToolPanel.X_ITEM_ROW + Span.DELETE_ICON, exist,
                message, lineNumber);
    }

    /**
     * Проверяет состояние радиокнопки (заблокирована/разблокирована)
     * @param unlocked ожидаемое состояние (true - разблокирована, false - заблокирована)
     * @param buttonTitle описание кнопки (например, "Справа", "Слева")
     */
    public void radioButtonIsEnabled(boolean unlocked, String buttonTitle)
    {
        RButton rButton = RButton.fromDescription(buttonTitle);
        String condition = unlocked ? "and not(@disabled)" : "and @disabled";
        String xpath = getContentXpath() + Any.DEBUG_VALUE + String.format(Span.ANY, rButton.name())
                       + Input.ANY_INPUT_WITH_CONDITION;
        boolean actualEnabledState = tester.isEnabled(xpath, rButton.name(), condition);
        Assert.assertEquals("Доступность переключателя не совпала с ожидаемой.", unlocked, actualEnabledState);
    }

    /**
     * Проверяет, выбрана ли радиокнопка
     * @param selected ожидаемое состояние (true - выбрана, false - не выбрана)
     * @param buttonTitle описание кнопки (например, "Справа", "Слева")
     */
    public void radioButtonIsSelected(boolean selected, String buttonTitle)
    {
        RButton rButton = RButton.fromDescription(buttonTitle);
        String condition = selected ? "and (@checked)" : "and not(@checked)";
        String xpath = getContentXpath() + Any.DEBUG_VALUE + String.format(Span.ANY, rButton.name())
                       + Input.ANY_INPUT_WITH_CONDITION;
        boolean actualSelectedState = tester.isSelected(xpath, rButton.name(), condition);
        Assert.assertEquals("Состояние выбора переключателя не совпало с ожидаемым.", selected, actualSelectedState);
    }

    /**
     * Проверить, что выпадающий список "Иконка вызова меню" доступен
     * @param isAvailable - true должен быть доступен; false - должен быть недоступен
     */
    public void menuCallIconDropdownIsAvailable(boolean isAvailable)
    {
        String xpath = isAvailable
                ? getContentXpath() + Any.ICON_VALUE
                : getContentXpath() + Any.ICON_VALUE + GUIXpath.Input.INPUT_DISABLED;
        boolean isEnabled = tester.isEnabled(xpath);
        String message = String.format("\"Иконка вызова меню\" %s", isAvailable ? "недоступна" : "доступна");
        Assert.assertEquals(message, isAvailable, isEnabled);
    }

}
