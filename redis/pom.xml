<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>ru.naumen</groupId>
		<artifactId>sd-parent</artifactId>
		<version>4.21.0-SNAPSHOT</version>
		<relativePath>../sdng-parent/pom.xml</relativePath>
	</parent>

	<artifactId>redis</artifactId>
	<description>Модуль для интеграции системы с Redis</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>sdng</artifactId>
		</dependency>
		<dependency>
			<groupId>io.lettuce</groupId>
			<artifactId>lettuce-core</artifactId>
		</dependency>
	</dependencies>

</project>