package ru.naumen.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.sec.server.encryption.EncryptedConfigValueProvider;
import ru.naumen.sec.server.encryption.EncryptionService;

/**
 * Провайдер свойства {@code ru.naumen.redis.password}.
 *
 * <AUTHOR>
 * @since 19.12.2024
 */
@Component(RedisPasswordProvider.NAME)
public class RedisPasswordProvider extends EncryptedConfigValueProvider
{
    public static final String NAME = "redisPasswordProvider";

    private final String redisPassword;
    private final String redisPasswordEnc;

    @Inject
    public RedisPasswordProvider(EncryptionService encryptionService,
            @Value("${ru.naumen.redis.password}") String redisPassword,
            @Value("${ru.naumen.redis.password.enc}") String redisPasswordEnc)
    {
        super(encryptionService);
        this.redisPassword = redisPassword;
        this.redisPasswordEnc = redisPasswordEnc;
    }

    @Override
    protected String getEncryptedValue()
    {
        return redisPasswordEnc;
    }

    @Override
    protected String getPropertyName()
    {
        return "ru.naumen.redis.password";
    }

    @Override
    protected String getValue()
    {
        return redisPassword;
    }
}