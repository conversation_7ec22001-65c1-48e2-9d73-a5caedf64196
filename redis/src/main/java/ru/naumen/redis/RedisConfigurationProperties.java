package ru.naumen.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.EncryptedValueProvider;

/**
 * Настройки для конфигурирования клиента Redis
 *
 * <AUTHOR>
 * @since 19.12.2024
 */
@Component
public class RedisConfigurationProperties
{
    private final String host;
    private final int port;
    private final int database;
    private final String userName;
    private final boolean sslEnabled;
    private final EncryptedValueProvider encryptedValueProvider;

    @Inject
    public RedisConfigurationProperties(
            @Value("${ru.naumen.redis.host}") String host,
            @Value("${ru.naumen.redis.port:6379}") int port,
            @Value("${ru.naumen.redis.database:0}") int database,
            @Value("${ru.naumen.redis.username}") String userName,
            @Value("${ru.naumen.redis.sslEnabled}") boolean sslEnabled,
            @Named(RedisPasswordProvider.NAME) EncryptedValueProvider encryptedValueProvider)
    {
        this.host = host;
        this.port = port;
        this.database = database;
        this.userName = userName;
        this.sslEnabled = sslEnabled;
        this.encryptedValueProvider = encryptedValueProvider;
    }

    public String getHost()
    {
        return host;
    }

    public int getPort()
    {
        return port;
    }

    public int getDatabase()
    {
        return database;
    }

    public char[] getPassword()
    {
        return encryptedValueProvider.get().toCharArray();
    }

    public String getUserName()
    {
        return userName;
    }

    public boolean isSslEnabled()
    {
        return sslEnabled;
    }
}