package ru.naumen.opensearch.fts.server.opensearch.queue;

import static ru.naumen.core.shared.Constants.IGNORE_CACHE;

import java.util.LinkedHashSet;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.transaction.Status;
import jakarta.transaction.TransactionManager;
import jakarta.validation.constraints.NotNull;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.fts.server.external.search.SearchConfiguration;
import ru.naumen.search.fts.server.IndexingRequiredChecker;
import ru.naumen.search.fts.server.lucene.SearchCommand;
import ru.naumen.search.fts.server.lucene.reindex.ReindexServiceUtil;
import ru.naumen.search.fts.server.queue.AbstractTxIndexingQueueImpl;

/**
 * Наследник {@link AbstractTxIndexingQueueImpl} для работы с OpenSearch.
 * Привязана к текущей транзакции, перед коммитом которой сообщения отправятся в очередь.
 *
 * <AUTHOR>
 * @since 13.12.24
 */
@Component
public class OpenSearchTxIndexingQueueImpl extends AbstractTxIndexingQueueImpl
{
    /**
     * Конкретная реализация AbstractSearchTxSynchronization для индексации.
     */
    private class SearchTxSynchronization extends AbstractSearchTxSynchronization
    {
        /**
         * Конструктор для создания объекта синхронизации.
         *
         * @param txUuid UUID текущей транзакции.
         * @param txMap  Карта для хранения синхронизаций транзакций.
         */
        public SearchTxSynchronization(String txUuid,
                ConcurrentMap<String, AbstractSearchTxSynchronization> txMap)
        {
            super(txUuid, txMap);
        }

        /**
         * Метод, вызываемый после завершения транзакции.
         * Выполняет обработку команд для индексации,
         * если транзакция была успешно зафиксирована (STATUS_COMMITTED).
         */
        @Override
        public void afterCompletion(int status)
        {
            try
            {
                if (status != Status.STATUS_COMMITTED)
                {
                    return;
                }
                LinkedHashSet<Pair<String, SearchCommand>> filteredCommands = new LinkedHashSet<>(filterCommands(
                        commands));
                LinkedHashSet<Pair<String, SearchCommand>> filteredLinkedObjectsCommands = new LinkedHashSet<>(
                        filterCommands(linkedObjectsCommands));
                // Обработка приоритета для создания новых индексов
                if (indexPreferNewObjects())
                {
                    prioritizeCreateCommands(filteredCommands);
                }
                // Определяем, игнорировать ли кэш
                boolean ignoreCache = false;
                if (context != null)
                {
                    ignoreCache = context.getProperty(IGNORE_CACHE, false);
                }
                processCommands(filteredCommands, ignoreCache);
                processLinkedObjects(filteredLinkedObjectsCommands, ignoreCache);

                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Отправка команд: {}", filteredCommands);
                }
            }
            finally
            {
                removeCurrentTx();
            }
        }

        /**
         * Приоритизирует команды CREATE, перемещая их в начало списка.
         *
         * @param commands Список команд для обработки.
         */
        private static void prioritizeCreateCommands(LinkedHashSet<Pair<String, SearchCommand>> commands)
        {
            commands.stream()
                    .filter(pair -> pair.getRight().equals(SearchCommand.CREATE))
                    .forEach(command ->
                    {
                        commands.remove(command);
                        commands.addFirst(command);
                    });
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(OpenSearchTxIndexingQueueImpl.class);

    private final SearchConfiguration searchConfiguration;
    private final QueueIndexProcessorService queueIndexProcessorService;

    /**
     * Конструктор для внедрения зависимостей.
     *
     * @param indexingRequiredChecker Проверяет, требуется ли индексация.
     * @param reindexServiceUtil Утилита для работы с переиндексацией.
     * @param txManager Менеджер транзакций.
     * @param searchConfiguration Сервис информации о Lucene.
     * @param queueIndexProcessorService Сервис для обработки очереди индексации.
     */
    @Inject
    public OpenSearchTxIndexingQueueImpl(IndexingRequiredChecker indexingRequiredChecker,
            ReindexServiceUtil reindexServiceUtil,
            TransactionManager txManager,
            SearchConfiguration searchConfiguration,
            QueueIndexProcessorService queueIndexProcessorService)
    {
        super(indexingRequiredChecker, reindexServiceUtil, txManager);
        this.searchConfiguration = searchConfiguration;
        this.queueIndexProcessorService = queueIndexProcessorService;
    }

    @NotNull
    @Override
    protected AbstractSearchTxSynchronization createTxSynchronization(String currentTxUuid)
    {
        return new SearchTxSynchronization(currentTxUuid, txMap);
    }

    //Игнорируется, чтобы избежать передачи queueIndexProcessorService в SearchTxSynchronization
    @SuppressWarnings("java:S3398")
    private boolean indexPreferNewObjects()
    {
        return Boolean.TRUE.equals(searchConfiguration.getIndexPreferNewObjects());
    }

    //Игнорируется, чтобы избежать передачи queueIndexProcessorService в SearchTxSynchronization
    @SuppressWarnings("java:S3398")
    private void processCommands(LinkedHashSet<Pair<String, SearchCommand>> filteredCommands,
            boolean ignoreCache)
    {
        queueIndexProcessorService.processCommands(filteredCommands, ignoreCache);
    }

    //Игнорируется, чтобы избежать передачи queueIndexProcessorService в SearchTxSynchronization
    @SuppressWarnings("java:S3398")
    private void processLinkedObjects(LinkedHashSet<Pair<String, SearchCommand>> filteredCommands,
            boolean ignoreCache)
    {
        queueIndexProcessorService.processLinkedObjects(filteredCommands, ignoreCache);
    }
}