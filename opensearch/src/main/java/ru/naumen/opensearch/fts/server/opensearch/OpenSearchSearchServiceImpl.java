package ru.naumen.opensearch.fts.server.opensearch;

import static java.lang.Integer.min;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.Query;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.query_dsl.QueryBuilders;
import org.opensearch.client.opensearch._types.query_dsl.QueryStringQuery;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchRequest.Builder;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.ingest.simulate.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.search.fts.server.lucene.AbstractSearchSpliterator;
import ru.naumen.search.fts.server.lucene.QueryProvider;
import ru.naumen.search.fts.server.lucene.SearchSystemService;

/**
 * Реализация {@link SearchSystemService} для работы с OpenSearch.
 *
 * <AUTHOR>
 * @since 15.12.2024
 */
@Component
public class OpenSearchSearchServiceImpl implements SearchSystemService
{
    private static final int DEFAULT_BATCH_SIZE = 200;
    private static final Logger LOG = LoggerFactory.getLogger(OpenSearchSearchServiceImpl.class);
    private final OpenSearchClient openSearchClient;
    // Максимальное количество документов, которое можно запросить в OpenSearch за один раз (по умолчанию 10 000).
    public static final int MAX_SIZE_RESULT = 10000;

    @Inject
    public OpenSearchSearchServiceImpl(OpenSearchClient openSearchClient)
    {
        this.openSearchClient = openSearchClient;
    }

    @Override
    public Object runQuery(String query, Analyzer analyzer, int maxResults)
    {
        try
        {
            // "title" Это имя поля, которое используется по умолчанию для поиска терминов в запросе.
            // Если в запросе не указано поле, будет использовать "title", как поле по умолчанию.
            Query q = new QueryParser("title", analyzer).parse(query);
            QueryStringQuery stringQuery = QueryBuilders.queryString()
                    .query(q.toString())
                    .build();

            SearchRequest searchRequest = new Builder()
                    .query(qu -> qu.queryString(stringQuery))
                    .source(builder -> builder.fetch(false))
                    .size(maxResults)
                    .build();
            SearchResponse<Document> searchResponse = openSearchClient.search(searchRequest, Document.class);
            // Извлечение результатов
            return new Pair<>(searchResponse, searchResponse.documents());
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> findUuidsByQuery(String query, Analyzer analyzer, int maxResults)
    {
        try
        {
            Query q = new QueryParser("title", analyzer).parse(query);
            QueryStringQuery stringQuery = QueryBuilders.queryString()
                    .query(q.toString())
                    .build();
            SearchRequest searchRequest = new Builder()
                    .query(qu -> qu.queryString(stringQuery))
                    .source(builder -> builder.fetch(false))
                    .size(maxResults)
                    .build();
            SearchResponse<Document> searchResponse = openSearchClient.search(searchRequest, Document.class);
            // Извлечение результатов
            return searchResponse.hits().hits().stream()
                    .map(Hit::id)
                    .toList();
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public Stream<Pair<AbstractSearchSpliterator, Integer>> getStreamSearchSpliterator(
            List<QueryProvider> queryProviders,
            int batchSize, boolean hasLimit)
    {
        // Размер пачки документов (batch size) для запроса
        final int adjustedBatchSize = hasLimit
                // Умножение на два - это просто эвристика из предположения, что уж в два раза больше
                // результатов хватит всем. Менять по необходимости.
                ? min(batchSize * 2, MAX_SIZE_RESULT)
                : DEFAULT_BATCH_SIZE;
        return queryProviders.stream()
                .map(cont ->
                {
                    final OpenSearchSearchSpliterator spliterator = new OpenSearchSearchSpliterator(
                            openSearchClient, cont.getQuery(), adjustedBatchSize, cont.getSort(), cont.getFqn());
                    return new Pair<>(spliterator, cont.getMaxResults());
                });
    }
}
