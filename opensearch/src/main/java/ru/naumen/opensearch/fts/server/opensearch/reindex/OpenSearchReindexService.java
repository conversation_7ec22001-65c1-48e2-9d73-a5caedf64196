package ru.naumen.opensearch.fts.server.opensearch.reindex;

import static ru.naumen.opensearch.fts.server.opensearch.OpenSearchIndexManager.getIndexName;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.OpenSearchException;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.ingest.simulate.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.TransactionHelper;
import ru.naumen.core.server.eventaction.jms.JmsMessageCustomizer.AdditionalBatchLoader;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.opensearch.fts.server.opensearch.OpenSearchIndexManager;
import ru.naumen.opensearch.fts.server.opensearch.queue.QueueIndexProcessorService;
import ru.naumen.search.fts.server.lucene.SearchCommand;
import ru.naumen.search.fts.server.lucene.reindex.ReindexBatchLoader;
import ru.naumen.search.fts.server.lucene.reindex.ReindexController.ReindexMode;
import ru.naumen.search.fts.server.lucene.reindex.ReindexServiceUtil;
import ru.naumen.search.fts.server.lucene.reindex.SystemReindexService;

/**
 * Реализация {@link SystemReindexService} для работы с OpenSearch.
 * Если используется {@link ru.naumen.search.fts.server.lucene.reindex.EmptyReindexControllerImpl},
 * то данный бин не будет создан {@link Lazy}
 *
 * <AUTHOR>
 * @since 09.12.24
 */
@Lazy
@Component
public class OpenSearchReindexService implements SystemReindexService
{
    private static final Logger LOG = LoggerFactory.getLogger(OpenSearchReindexService.class);

    private final OpenSearchClient openSearchClient;
    private final QueueIndexProcessorService queueIndexProcessorService;
    private final ReindexServiceUtil reindexServiceUtil;
    private final OpenSearchIndexManager openSearchIndexManager;

    @Inject
    public OpenSearchReindexService(
            OpenSearchClient openSearchClient,
            QueueIndexProcessorService queueIndexProcessorService,
            ReindexServiceUtil reindexServiceUtil,
            OpenSearchIndexManager openSearchIndexManager)
    {
        this.openSearchClient = openSearchClient;
        this.queueIndexProcessorService = queueIndexProcessorService;
        this.reindexServiceUtil = reindexServiceUtil;
        this.openSearchIndexManager = openSearchIndexManager;
    }

    @Override
    public void deleteAllIndexes(ReindexMode reindexMode)
    {
        openSearchIndexManager.deleteAllIndexes();
    }

    @Override
    public List<Map.Entry<String, Instant>> getLastIndexingDate()
    {
        return openSearchIndexManager.getLastIndexingDate();
    }

    @Override
    public void updateIndexByUUIDs(List<String> uuids)
    {
        queueIndexProcessorService.processCommands(uuids, SearchCommand.CREATE, false);
        queueIndexProcessorService.processCommands(uuids, SearchCommand.UPDATE_FILES, false);
    }

    @Override
    public void beforeReindex(ClassFqn fqn)
    {
        openSearchIndexManager.recreateIndexWithAnalyzer(fqn);
    }

    @Override
    public void processIndexing(ClassFqn fqn, ReindexBatchLoader reindexBatchLoader, //NOSONAR
            AdditionalBatchLoader additionalReindexBatchLoader, Set<String> indexedUuids, ReindexMode reindexMode,
            @Nullable String currentClusterNodeID)
    {
        long queueSize = 0L;
        int batch = 0;
        String operationUuid = reindexServiceUtil.getOperationUuid(fqn, reindexMode, currentClusterNodeID);
        String fileOperationUuid = reindexServiceUtil.getFileOperationUuid(fqn, reindexMode, currentClusterNodeID);
        try
        {
            if (reindexBatchLoader.peek())
            {
                while (reindexBatchLoader.next())
                {
                    batch++;
                    int lastBatchNum = reindexBatchLoader.isLast() && !additionalReindexBatchLoader.peek() ? batch : -1;
                    final List<String> uuids = reindexBatchLoader.current();
                    uuids.forEach(indexedUuids::remove);
                    processCommands(uuids, SearchCommand.CREATE, operationUuid, lastBatchNum);
                    if (fileOperationUuid != null)
                    {
                        processCommands(uuids, SearchCommand.UPDATE_FILES,
                                fileOperationUuid, lastBatchNum);
                    }
                    LOG.info("{} objects reindex.", uuids.size());
                    queueSize += uuids.size();
                }
            }
            //Обработка дополнительных объектов
            if (additionalReindexBatchLoader.peek())
            {
                while (additionalReindexBatchLoader.next())
                {
                    batch++;
                    int lastBatchNum = additionalReindexBatchLoader.isLast() ? batch : -1;
                    final List<String> uuids = additionalReindexBatchLoader.current();
                    uuids.forEach(indexedUuids::remove);
                    processCommands(uuids, SearchCommand.CREATE, operationUuid, lastBatchNum);
                    LOG.info("{} objects reindex.", uuids.size());
                    queueSize += uuids.size();
                }
            }
            LOG.info("Total of {} objects was queued for reindex", queueSize);
            long removeQueueSize = 0L;
            if (!indexedUuids.isEmpty())
            {
                for (List<String> partition : Lists.partition(new ArrayList<>(indexedUuids),
                        reindexBatchLoader.getBatchSize()))
                {
                    queueIndexProcessorService.processCommands(partition, SearchCommand.DELETE, false);
                    LOG.info("{} objects remove from index.", partition.size());
                    removeQueueSize += partition.size();
                }
                LOG.info("Total of {} objects was queued for removal from index", removeQueueSize);
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    private void processCommands(List<String> uuids, SearchCommand reindexCommand,
            String operationUuid, int lastBatchNum)
    {
        TransactionHelper.afterCommit(status ->
                queueIndexProcessorService.processCommands(uuids, reindexCommand, operationUuid, lastBatchNum, false));
    }

    /**
     * Возвращает список UUID проиндексированных объектов заданного класса/типа.
     * @param fqn FQN класса/типа, по которым производится поиск объектов
     * @return UUID индексированных объектов
     */
    @Override
    public Set<String> getIndexedUuids(ClassFqn fqn)
    {
        try
        {
            // Создание запроса поиска
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(getIndexName(fqn))
                    .source(builder -> builder.fetch(false)) // Исключает из ответа все поля
                    .build();
            // Выполнение поиска
            SearchResponse<Document> searchResponse = openSearchClient.search(searchRequest, Document.class);
            // Извлечение результатов
            return searchResponse.hits().hits().stream()
                    .map(Hit::id)
                    .collect(Collectors.toSet());
        }
        catch (OpenSearchException | IOException e)
        {
            throw new FxException(e);
        }
    }
}