package ru.naumen.opensearch.fts.server.opensearch;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.opensearch.client.opensearch._types.analysis.Analyzer;
import org.opensearch.client.opensearch._types.analysis.CustomAnalyzer;
import org.opensearch.client.opensearch._types.analysis.StemmerTokenFilter;
import org.opensearch.client.opensearch._types.analysis.StopTokenFilter;
import org.opensearch.client.opensearch._types.analysis.TokenFilter;

/**
 * Перечисление анализаторов OpenSearch с поддержкой различных языков.
 * Используется для настройки индексации и поиска.
 *
 * <AUTHOR>
 * @since 14.02.2025
 */
public enum OpenSearchAnalyzer
{
    /**
     * Анализатор для точного поиска (без токенизации, то есть токеном является всё значение поля.
     * Используется только для системных полей).
     */
    KEYWORD("", "keyword"),

    /** Анализатор для точного поиска (разделение на токены по пробелам) */
    STRICT("ftsNoAnalyzer", "whitespace"),

    /** Универсальный анализатор без морфологии и строгого соответствия */
    NO_MORPH_NO_STRICT("ftsNoMorphNoStrictAnalyzer", "standard"),

    /** Русский анализатор */
    RUSSIAN("ftsRussianAnalyzer", "russian"),

    /** Английский анализатор */
    ENGLISH("ftsEnglishAnalyzer", "english"),

    /** Чешский анализатор, - используется кастомный анализатор */
    CZECH("ftsChzechAnalyzer", "czech"),// NOSONAR String literals should not be duplicate

    /** Немецкий анализатор */
    GERMAN("ftsDeutschAnalyzer", "german"),

    /** Польский анализатор для работы необходимо установить плагин analysis-stempel */
    POLISH("ftsPolishAnalyzer", "polish"),

    /** Французский анализатор */
    FRENCH("ftsFrenchAnalyzer", "french"),

    /** Украинский анализатор не поддерживается, вместо него используется русский анализатор */
    UKRAINIAN("ftsUkrainianAnalyzer", "russian");

    private final String customAnalyzerName;
    private final String openSearchAnalyzerKind;

    OpenSearchAnalyzer(String customAnalyzerName, String openSearchAnalyzerKind)
    {
        this.customAnalyzerName = customAnalyzerName;
        this.openSearchAnalyzerKind = openSearchAnalyzerKind;
    }

    /**
     * Возвращает код анализатора OpenSearch.
     *
     * @return строка с кодом анализатора
     */
    public String getOpenSearchAnalyzerCode()
    {
        return openSearchAnalyzerKind;
    }

    /**
     * Получает анализатор по его кастомному имени.
     *
     * @param customName имя кастомного анализатора
     * @return соответствующий {@link OpenSearchAnalyzer}
     * @throws IllegalArgumentException если анализатор не найден
     */
    public static OpenSearchAnalyzer fromCustomName(String customName)
    {
        return Arrays.stream(values())
                .filter(analyzer -> analyzer.customAnalyzerName.equalsIgnoreCase(customName))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No analyzer found with custom name: " + customName));
    }

    /**
     * Возвращает карту кастомных анализаторов для чешского языка.
     */
    public static Map<String, Analyzer> getCustomAnalyzers()
    {
        return Map.of(
                "czech", new Analyzer(new CustomAnalyzer.Builder()
                        .tokenizer("standard")
                        .filter(List.of("lowercase", "czech_stop", "czech_stemmer"))
                        .build())
        );
    }

    /**
     * Возвращает карту кастомных фильтров для чешского языка.
     */
    public static Map<String, TokenFilter> getCustomFilters()
    {
        return Map.of(
                "czech_stop", new TokenFilter.Builder()
                        .definition(def -> def.stop(new StopTokenFilter.Builder()
                                .stopwords("_czech_")  // Используем встроенные стоп-слова Lucene
                                .build()))
                        .build(),

                "czech_stemmer", new TokenFilter.Builder()
                        .definition(def -> def.stemmer(new StemmerTokenFilter.Builder()
                                .language("czech")  // Используем Lucene Czech Stemmer
                                .build()))
                        .build()
        );
    }
}