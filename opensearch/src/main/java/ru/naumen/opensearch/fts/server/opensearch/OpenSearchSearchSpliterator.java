package ru.naumen.opensearch.fts.server.opensearch;

import static org.opensearch.client.opensearch._types.SortOrder.Asc;
import static org.opensearch.client.opensearch._types.SortOrder.Desc;
import static ru.naumen.core.shared.Constants.AbstractBO.UUID;
import static ru.naumen.metainfo.shared.Constants.CaseListAttributeType.METACLASS_ID;
import static ru.naumen.opensearch.fts.server.opensearch.OpenSearchIndexManager.getIndexName;
import static ru.naumen.opensearch.fts.server.opensearch.OpenSearchSearchServiceImpl.MAX_SIZE_RESULT;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.apache.lucene.index.Term;
import org.apache.lucene.search.BooleanClause;
import org.apache.lucene.search.BooleanQuery;
import org.apache.lucene.search.BoostQuery;
import org.apache.lucene.search.FuzzyQuery;
import org.apache.lucene.search.MatchAllDocsQuery;
import org.apache.lucene.search.PrefixQuery;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.RegexpQuery;
import org.apache.lucene.search.Sort;
import org.apache.lucene.search.SortField;
import org.apache.lucene.search.TermQuery;
import org.apache.lucene.search.WildcardQuery;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.OpenSearchException;
import org.opensearch.client.opensearch._types.SortOptions;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.mapping.FieldType;
import org.opensearch.client.opensearch._types.query_dsl.BoolQuery;
import org.opensearch.client.opensearch._types.query_dsl.QueryBuilders;
import org.opensearch.client.opensearch._types.query_dsl.QueryVariant;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchRequest.Builder;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.ingest.simulate.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.FxException;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.search.fts.server.lucene.AbstractSearchSpliterator;

/**
 * Реализация {@link AbstractSearchSpliterator} для работы с OpenSearch.
 *
 * <AUTHOR>
 * @since 13.12.2024
 */
public class OpenSearchSearchSpliterator extends AbstractSearchSpliterator
{
    private static final Logger LOG = LoggerFactory.getLogger(OpenSearchSearchSpliterator.class);

    private final OpenSearchClient indexSearcher;

    private org.opensearch.client.opensearch._types.query_dsl.Query query;
    private final Sort sort;
    private final int batchSize;
    private Iterator<Hit<Document>> currentIterator = null;
    private final ClassFqn fqn;

    private int batchNumber = 0;

    public OpenSearchSearchSpliterator(OpenSearchClient indexSearcher, Query query, int batchSize, Sort sort,
            @Nullable ClassFqn fqn)
    {
        this.indexSearcher = indexSearcher;
        this.sort = sort;
        try
        {
            this.query =
                    new org.opensearch.client.opensearch._types.query_dsl.Query(
                            convertLuceneQueryToOpenSearch(query));
        }
        catch (FxException e)
        {
            this.query = null;
        }
        this.batchSize = batchSize;
        this.fqn = fqn;
    }

    /**
     * Преобразует запрос Lucene в запрос OpenSearch.
     * Возвращает null в случае некорректного запроса.
     *
     * @param luceneQuery запрос Lucene, который необходимо преобразовать.
     * @return объект QueryBuilder OpenSearch, представляющий тот же запрос.
     */
    private static QueryVariant convertLuceneQueryToOpenSearch(Query luceneQuery)
    {
        switch (luceneQuery)
        {
            case TermQuery termQuery ->
            {
                // Поскольку TermQuery используется для точного соответствия,
                // а у нас не используются поля типа keyword,
                // преобразуем его в MatchQuery для работы с анализированными полями.
                Term term = termQuery.getTerm();
                return QueryBuilders.match().field(term.field()).query(FieldValue.of(term.text())).build();
            }
            case WildcardQuery wildcardQuery ->
            {
                Term term = wildcardQuery.getTerm();

                return QueryBuilders.wildcard().field(term.field()).value(term.text()).build();
            }
            case BoostQuery boostQuery ->
            {
                org.opensearch.client.opensearch._types.query_dsl.Query subQuery =
                        new org.opensearch.client.opensearch._types.query_dsl.Query(
                                convertLuceneQueryToOpenSearch(boostQuery.getQuery()));
                return QueryBuilders.boosting().positive(subQuery)
                        .boost(boostQuery.getBoost())
                        .negative(new org.opensearch.client.opensearch._types.query_dsl.Query(
                                QueryBuilders.matchNone().build()))
                        .negativeBoost(0).build();
            }
            case MatchAllDocsQuery ignored ->
            {
                return QueryBuilders.matchAll().build();
            }
            case PrefixQuery prefixQuery ->
            {
                Term term = prefixQuery.getPrefix();
                return QueryBuilders.prefix().field(term.field()).value(term.text()).build();
            }
            case FuzzyQuery fuzzyQuery ->
            {
                Term term = fuzzyQuery.getTerm();
                return QueryBuilders.fuzzy().field(term.field()).value(f -> f.stringValue(term.text()))
                        .fuzziness(String.valueOf(fuzzyQuery.getMaxEdits())).build();
            }
            case RegexpQuery regexpQuery ->
            {
                Term term = regexpQuery.getRegexp();
                return QueryBuilders.regexp().field(term.field()).value(term.text()).build();
            }
            case BooleanQuery booleanQuery ->
            {
                BoolQuery.Builder boolQueryBuilder = QueryBuilders.bool();
                for (BooleanClause clause : booleanQuery.clauses())
                {
                    // Если запрос пустой, это указывает на отсутствие фильтрации.
                    // В результате запрос работает как allMatch, то есть пользователь получит все объекты класса,
                    // хотя не должен получить ни одного.
                    if (clause.query().toString().isEmpty())
                    {
                        throw new FxException("Invalid query: " + clause.query());
                    }
                    org.opensearch.client.opensearch._types.query_dsl.Query subQuery =
                            new org.opensearch.client.opensearch._types.query_dsl.Query(
                                    convertLuceneQueryToOpenSearch(clause.query()));
                    switch (clause.occur())
                    {
                        case MUST:
                            boolQueryBuilder.must(subQuery);
                            break;
                        case SHOULD:
                            boolQueryBuilder.should(subQuery);
                            break;
                        case MUST_NOT:
                            boolQueryBuilder.mustNot(subQuery);
                            break;
                        default:
                            throw new UnsupportedOperationException(
                                    "Unsupported BooleanClause Occur: " + clause.occur());
                    }
                }
                return boolQueryBuilder.build();
            }
            default -> throw new UnsupportedOperationException(
                    "Unsupported query type: " + luceneQuery.getClass().getName());
        }
    }

    /**
     * Продвигает итератор к следующему элементу, если он доступен, и передаёт его в `Consumer`.
     * Если текущий результат поиска исчерпан, выполняется новый запрос для получения следующей порции данных.
     * Максимальное количество документов, которое можно запросить в OpenSearch за один раз (по умолчанию 10 000).
     *
     * @param action потребитель, который принимает следующий элемент в виде `Map<String, String>`.
     * @return `true`, если удалось продвинуться на следующий элемент, `false`, если данных больше нет.
     */
    @Override
    public boolean tryAdvance(Consumer<? super Map<String, String>> action)
    {
        LOG.debug("Trying to advance.");
        if (currentIterator == null || (!currentIterator.hasNext() && totalHits >= ((long)batchNumber * batchSize) + 1
                                        && MAX_SIZE_RESULT > ((long)batchNumber * batchSize)))
        {
            int remainingResults = MAX_SIZE_RESULT - (batchNumber * batchSize);
            int currentBatchSize = Math.min(batchSize, remainingResults);
            final SearchResponse<Document> searchResult = performQuery(currentBatchSize);
            if (searchResult != null)
            {
                totalHits = searchResult.hits().total().value();
                batchNumber++;
                currentIterator = searchResult.hits().hits().iterator();
            }
        }

        if (currentIterator != null && currentIterator.hasNext())
        {
            final Hit<Document> item = currentIterator.next();
            Map<String, String> fields = item.fields().entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            e -> e.getValue().to(ArrayList.class).getFirst().toString()));
            LOG.debug("Has item to advance.");
            action.accept(fields);
            LOG.debug("Item has been processed by downstream.");
            return true;
        }

        isFullyRead = totalHits < MAX_SIZE_RESULT;
        LOG.debug("Can't advance.");
        return false;
    }

    /**
     * Выполняет поиск в OpenSearch, запрашивая только поля UUID и metaClassId.
     * <p>
     * Метод формирует `SearchRequest`, в котором:
     * - Запрашиваются только поля `UUID` и `metaClassId`, остальные поля исключаются.
     * - Отключается загрузка исходного `_source` документа для экономии ресурсов.
     * - Применяется сортировка, если она задана.
     * - Используется постраничный поиск с параметрами `from` и `size`.
     * - Если указан `fqn`, выполняется поиск в соответствующем индексе.
     * - Если индекс не найден возвращает null
     *
     * @return SearchResponse с результатами поиска, содержащими объекты типа Document.
     * @throws FxException если произошла ошибка при выполнении запроса к OpenSearch.
     */
    @Nullable
    private SearchResponse<Document> performQuery(int currentBatchSize)
            throws FxException
    {
        try
        {
            if (query == null)
            {
                return null;
            }
            // запрашиваем только поля UUID и metaClassId остальные поля не нужны
            SearchRequest.Builder searchRequest = new Builder()
                    .query(query)
                    .fields(builder -> builder.field(UUID))
                    .fields(builder -> builder.field(METACLASS_ID))
                    .source(builder -> builder.fetch(false))
                    .sort(migrateSort(sort))
                    .from(batchNumber * batchSize)
                    .size(currentBatchSize);
            if (fqn != null)
            {
                searchRequest.index(getIndexName(fqn));
            }
            return indexSearcher.search(searchRequest.build(), Document.class);
        }
        catch (OpenSearchException e)
        {
            if (e.getMessage().contains("no such index"))
            {
                LOG.debug(e.getMessage());
                return null;
            }
            throw new FxException(e);
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Конвертирует Lucene Sort в список OpenSearch SortOptions.
     *
     * @param luceneSort объект Lucene Sort
     * @return список сортировок для OpenSearch
     */
    @Nullable
    public static List<SortOptions> migrateSort(@Nullable Sort luceneSort)
    {
        if (luceneSort == null || luceneSort.getSort() == null)
        {
            return null;
        }
        return Arrays.stream(luceneSort.getSort())
                .filter(Objects::nonNull)
                .map(OpenSearchSearchSpliterator::convertSortField)
                .toList();
    }

    /**
     * Конвертирует Lucene SortField в OpenSearch SortBuilder.
     *
     * @param sortField объект Lucene SortField
     * @return объект OpenSearch SortBuilder
     */
    private static SortOptions convertSortField(SortField sortField)
    {
        SortOrder sortOrderField = sortField.getReverse() ? Desc : Asc;
        // Сортировка по SCORE в OpenSearch имеет обратный порядок от Lucene
        SortOrder sortOrderScore = !sortField.getReverse() ? Desc : Asc;

        // Определяем unmappedType для сортировки, если поле отсутствует в mapping
        FieldType unmappedType = switch (sortField.getType())
        {
            case STRING, STRING_VAL -> FieldType.Keyword;
            case INT, LONG -> FieldType.Long;
            case FLOAT, DOUBLE -> FieldType.Double;
            default -> null;
        };

        return switch (sortField.getType())
        {
            case SCORE -> new SortOptions.Builder()
                    .score(builder -> builder.order(sortOrderScore))
                    .build();

            case DOC -> new SortOptions.Builder()
                    .doc(builder -> builder.order(sortOrderField))
                    .build();

            case STRING, STRING_VAL, INT, LONG, FLOAT, DOUBLE -> new SortOptions.Builder()
                    .field(builder -> builder
                            .field(sortField.getField())
                            .order(sortOrderField)
                            // Значение по умолчанию для сортировки, если поле отсутствует в документе.
                            // В данном случае документы без значения будут помещены в конец результатов.
                            .missing(m -> m.stringValue("_last"))
                            // Указывает тип поля, если оно отсутствует в mapping-е индекса.
                            // Это позволяет избежать ошибки "No mapping found for [field] in order to sort on"
                            // при сортировке по полю, которое может отсутствовать в некоторых индексах.
                            .unmappedType(unmappedType)
                    )
                    .build();
            default -> throw new UnsupportedOperationException("Unsupported SortField type: " + sortField.getType());
        };
    }
}