package ru.naumen.opensearch.fts.server.opensearch.reindex;

import static ru.naumen.search.fts.server.lucene.reindex.ReindexController.ReindexMode.UPDATE_SINGLE_NODE;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.cluster.external.lock.ClusterLockService;
import ru.naumen.core.server.cluster.external.lock.PersistentClusterLockType;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.opensearch.fts.server.opensearch.OpenSearchIndexManager;
import ru.naumen.search.fts.server.lucene.reindex.ReindexController;
import ru.naumen.search.fts.server.lucene.reindex.ReindexController.ReindexMode;

/**
 * Переиндексация всех объектов, требующих индексации на старте приложения.
 * Сделано через отдельный bean, чтобы выполнение действия проходило в конце запуска приложения.
 *
 * <AUTHOR>
 * @since 4.0.1.17
 */
@Component
public class ReindexChecker implements ApplicationListener<ContextRefreshedEvent>
{
    private static final Logger LOG = LoggerFactory.getLogger(ReindexChecker.class);

    private final ReindexController reindexController;
    private final OpenSearchIndexManager indexManager;
    private final ClusterLockService clusterLockService;
    private static final int WAIT_TIME = 1000;
    private static final String REINDEX_CHECKER = "REINDEX_CHECKER";

    @Inject
    public ReindexChecker(ReindexController reindexController, OpenSearchIndexManager indexManager,
            ClusterLockService clusterLockService)
    {
        this.reindexController = reindexController;
        this.indexManager = indexManager;
        this.clusterLockService = clusterLockService;
    }

    @Async
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
    {
        try
        {
            // Проверку индексов достаточно выполнить только на одной ноде,
            // поэтому используем механизм блокировки
            // чтобы избежать параллельного выполнения на нескольких нодах.
            if (System.getProperty("noReindexOnStart") == null && clusterLockService.lock(
                    PersistentClusterLockType.LOCK, REINDEX_CHECKER))
            {
                Set<String> allIndex = indexManager.getAllIndex();
                if (allIndex.isEmpty())
                {
                    reindexController.cancelReindexing();
                    reindexController.reindexAll(UPDATE_SINGLE_NODE);
                    return;
                }
                checkIndexAndReindexIfError();
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
        finally
        {
            clusterLockService.unlock(PersistentClusterLockType.LOCK, REINDEX_CHECKER);
        }
    }

    /**
     * Проверить не была ли прервана переиндексация, если прервана - то заново ставим те классы (в случае если есть и
     * класс и типы, то только типы), что не успели переиндексироваться, тк нужна гарантия, что ничего не утеряно во
     * время прерывания
     */
    private void checkIndexAndReindexIfError()
    {
        Collection<ClassFqn> reindexingFqns = reindexController.listReindexingFqns();
        if (reindexingFqns.isEmpty())
        {
            return;
        }

        LOG.info("Restarting the process of reindexing for these fqn: {}", reindexingFqns);

        Map<ClassFqn, Set<ClassFqn>> casesFqnByClass = new HashMap<>();
        for (ClassFqn fqn : reindexingFqns)
        {
            // очистим информацию о старой переиндексации, чтобы сообщения в очереди JMS не мешали новой
            reindexController.cancelReindexing(fqn);
            ClassFqn clazz = fqn.isClass() ? fqn : fqn.fqnOfClass();
            Set<ClassFqn> casesFqn = casesFqnByClass.computeIfAbsent(clazz, f -> new HashSet<>());
            if (fqn.isCase())
            {
                casesFqn.add(fqn);
            }
        }

        // восстановим переиндексацию
        // Filters.cases(classFqn.getCase()) - восстанавливаем именно те типы (или класс без типов),
        // что есть в таблице, без поиска в глубину, как в обычной переиндексации, чтобы не затаскивать в
        // переиндексацию вложенные типы
        casesFqnByClass.forEach((classFqn, casesFqn) ->
        {
            if (casesFqn.isEmpty())
            {
                reindexController.reindex(classFqn, Filters.cases(classFqn.getCase()), ReindexMode.UPDATE_SINGLE_NODE);
            }
            else
            {
                casesFqn.forEach(caseFqn ->
                        reindexController.reindex(caseFqn, Filters.cases(caseFqn.getCase()),
                                ReindexMode.RESUME_UPDATE));
            }
        });
    }
}
