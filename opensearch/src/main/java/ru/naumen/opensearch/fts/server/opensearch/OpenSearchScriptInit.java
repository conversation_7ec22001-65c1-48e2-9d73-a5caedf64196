package ru.naumen.opensearch.fts.server.opensearch;

import static ru.naumen.search.fts.server.lucene.SearchConstants.COMMENT_FIELD;
import static ru.naumen.search.fts.server.lucene.SearchConstants.FILE_FIELD;

import java.io.IOException;

import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.StoredScript;
import org.opensearch.client.opensearch.core.GetScriptRequest;
import org.opensearch.client.opensearch.core.GetScriptResponse;
import org.opensearch.client.opensearch.core.PutScriptRequest;
import org.opensearch.client.opensearch.core.PutScriptResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

/**
 * Класс для инициализации индексируемых скриптов в OpenSearch.
 * Этот класс проверяет наличие скриптов при запуске приложения и создает их, если они отсутствуют.
 * <p>
 * Скрипты в OpenSearch позволяют выполнять динамические операции над данными, которые сложно или невозможно реализовать
 * с помощью стандартных операторов. Они предоставляют гибкость для выполнения сложных вычислений,
 * условных логик и манипуляций с данными, которые выходят за рамки возможностей обычных запросов и операций.
 * <p>
 * Скрипты SCRIPT_UPDATE_ID, SCRIPT_UPDATE_FILE_ID, SCRIPT_UPDATE_COMMENT_ID
 * предназначены для частичного обновления документов. Они позволяют:
 * - Перезаписывать только определенные поля.
 * - Удалять ненужные поля.
 * - Сохранять остальные данные без изменений.
 *
 * <AUTHOR>
 * @since 24.12.2024
 */
@Component
public class OpenSearchScriptInit
{
    private static final Logger LOG = LoggerFactory.getLogger(OpenSearchScriptInit.class);

    private final OpenSearchClient openSearchClient;

    public static final String SCRIPT_UPDATE_ID = "update_non_file_fields";
    public static final String SCRIPT_UPDATE_FILE_ID = "update_file_fields";
    public static final String SCRIPT_UPDATE_COMMENT_ID = "update_comment_fields";

    private static final String SCRIPT_TEMPLATE = """
                def fields = new ArrayList();
                for (String key : ctx._source.keySet())
                {
                    if (%s)
                    {
                        fields.add(key);
                    }
                }
                for (String key : fields)
                {
                    ctx._source.remove(key);
                }
                for (entry in params.entries.entrySet())
                {
                    ctx._source.put(entry.getKey(), entry.getValue());
                }
                ctx._source['@timestamp'] = params.timestamp;
            """;

    /**
     * Конструктор для инициализации OpenSearchScriptInit с клиентом OpenSearch.
     *
     * @param openSearchClient клиент OpenSearch для выполнения операций.
     */
    @Inject
    public OpenSearchScriptInit(OpenSearchClient openSearchClient)
    {
        this.openSearchClient = openSearchClient;
    }

    /**
     * Метод инициализации, вызываемый после создания бина.
     * Проверяет и создает скрипты в OpenSearch, если они отсутствуют.
     */
    @PostConstruct
    public void init()
    {
        try
        {
            ensureScriptExists(SCRIPT_UPDATE_ID, String.format(SCRIPT_TEMPLATE, "!key.contains('" + FILE_FIELD + "')"));
            ensureScriptExists(SCRIPT_UPDATE_FILE_ID,
                    String.format(SCRIPT_TEMPLATE, "key.contains('" + FILE_FIELD + "')"));
            ensureScriptExists(SCRIPT_UPDATE_COMMENT_ID,
                    String.format(SCRIPT_TEMPLATE, "key.contains('" + COMMENT_FIELD + "')"));
        }
        catch (IOException e)
        {
            LOG.error("Failed to ensure script exists: {}", e.getMessage(), e);
        }
    }

    /**
     * Проверяет, существует ли скрипт с заданным идентификатором, и создает его, если он отсутствует.
     *
     * @param scriptId идентификатор скрипта.
     * @param scriptSource исходный код скрипта.
     * @throws IOException если возникает ошибка ввода-вывода при обращении к OpenSearch.
     */
    private void ensureScriptExists(String scriptId, String scriptSource) throws IOException
    {
        if (!isScriptExists(scriptId))
        {
            createScript(scriptId, scriptSource);
        }
    }

    /**
     * Проверяет, существует ли скрипт с заданным идентификатором.
     *
     * @param scriptId идентификатор скрипта.
     * @return true, если скрипт существует; false, если скрипт не найден.
     */
    private boolean isScriptExists(String scriptId)
    {
        try
        {
            GetScriptRequest getScriptRequest = new GetScriptRequest.Builder().id(scriptId).build();
            GetScriptResponse getScriptResponse = openSearchClient.getScript(getScriptRequest);
            return getScriptResponse.found();
        }
        catch (Exception e)
        {
            LOG.warn("Script {} not found: {}", scriptId, e.getMessage());
            return false;
        }
    }

    /**
     * Создает скрипт с заданным идентификатором и исходным кодом в OpenSearch.
     *
     * @param scriptId идентификатор скрипта.
     * @param scriptSource исходный код скрипта.
     * @throws IOException если возникает ошибка ввода-вывода при обращении к OpenSearch.
     */
    private void createScript(String scriptId, String scriptSource) throws IOException
    {
        StoredScript storedScript = new StoredScript.Builder()
                .lang(langBuilder -> langBuilder.custom("painless"))
                .source(scriptSource)
                .build();

        PutScriptRequest putScriptRequest = new PutScriptRequest.Builder()
                .id(scriptId)
                .script(storedScript)
                .build();

        PutScriptResponse putScriptResponse = openSearchClient.putScript(putScriptRequest);
        if (putScriptResponse.acknowledged())
        {
            LOG.info("Script created successfully: {}", scriptId);
        }
        else
        {
            LOG.error("Failed to create script: {}", scriptId);
        }
    }
}