package ru.naumen.opensearch.fts.server.opensearch;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.sec.server.encryption.EncryptedConfigValueProvider;
import ru.naumen.sec.server.encryption.EncryptionService;

/**
 * Провайдер свойства {@code ru.naumen.opensearch.client.password}.
 *
 * <AUTHOR>
 * @since 15.12.2024
 */
@Component(OpenSearchPasswordProvider.NAME)
public class OpenSearchPasswordProvider extends EncryptedConfigValueProvider
{
    public static final String NAME = "openSearchPasswordProvider";

    private final String openSearchPassword;
    private final String openSearchPasswordEnc;

    @Inject
    public OpenSearchPasswordProvider(EncryptionService encryptionService,
            @Value("${ru.naumen.opensearch.client.password}") String openSearchPassword,
            @Value("${ru.naumen.opensearch.client.password.enc}") String openSearchPasswordEnc)
    {
        super(encryptionService);
        this.openSearchPassword = openSearchPassword;
        this.openSearchPasswordEnc = openSearchPasswordEnc;
    }

    @Override
    protected String getEncryptedValue()
    {
        return openSearchPasswordEnc;
    }

    @Override
    protected String getPropertyName()
    {
        return "ru.naumen.opensearch.client.password";
    }

    @Override
    protected String getValue()
    {
        return openSearchPassword;
    }
}