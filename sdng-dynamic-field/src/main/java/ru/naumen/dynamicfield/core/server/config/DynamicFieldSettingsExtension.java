package ru.naumen.dynamicfield.core.server.config;

import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.settings.SharedSettingsExtension;
import ru.naumen.dynamicfield.core.server.license.DynamicFieldLicensingService;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.SharedSettings;
import ru.naumen.dynamicfield.core.shared.DynamicFieldMetainfoHelper;

/**
 * Расширение для общих настроек.
 * <AUTHOR>
 * @since Jul 31, 2023
 */
@Component
public class DynamicFieldSettingsExtension implements SharedSettingsExtension
{
    private final DynamicFieldMetainfoHelper metainfoHelper;
    private final DynamicFieldLicensingService licensingService;

    @Inject
    public DynamicFieldSettingsExtension(
            DynamicFieldMetainfoHelper metainfoHelper,
            DynamicFieldLicensingService licensingService)
    {
        this.metainfoHelper = metainfoHelper;
        this.licensingService = licensingService;
    }

    @Override
    public void addSettings(Map<String, Object> settings)
    {
        if (licensingService.isLicensed())
        {
            settings.put(SharedSettings.TEMPLATE_CLASS_ID, metainfoHelper.getTemplateClassId());
            settings.put(SharedSettings.GROUP_CLASS_ID, metainfoHelper.getGroupClassId());
        }
    }

    @Override
    public int countSettings()
    {
        return licensingService.isLicensed() ? 1 : 0;
    }
}
