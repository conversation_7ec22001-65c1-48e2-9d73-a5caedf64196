package ru.naumen.dynamicfield.core.server.config.xml;

import java.io.InputStream;

import org.springframework.stereotype.Component;
import org.xml.sax.SAXParseException;

import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.ExceptionsUtil;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.dynamicfield.core.server.config.DynamicFieldConfiguration;
import ru.naumen.dynamicfield.core.server.config.DynamicFieldConfigurationParser;

/**
 * Парсер конфигурации динамических полей в формате XML.
 * <AUTHOR>
 * @since Jan 17, 2024
 */
@Component
public class DynamicFieldConfigurationXmlParser implements DynamicFieldConfigurationParser
{
    private final XmlUtils xmlUtils;
    private final ConfigurationProperties configurationProperties;
    private final DynamicFieldConfigurationFactory configurationFactory;
    private final MessageFacade messages;

    @Inject
    public DynamicFieldConfigurationXmlParser(
            XmlUtils xmlUtils,
            ConfigurationProperties configurationProperties,
            DynamicFieldConfigurationFactory configurationFactory,
            MessageFacade messages)
    {
        this.xmlUtils = xmlUtils;
        this.configurationProperties = configurationProperties;
        this.configurationFactory = configurationFactory;
        this.messages = messages;
    }

    @Override
    public DynamicFieldConfiguration parse(InputStream stream)
    {
        try
        {
            Configuration xmlConfig = xmlUtils.parseXml(stream, Configuration.class,
                    configurationProperties.isProcessingExternalEntityInXML());
            return configurationFactory.create(xmlConfig);
        }
        catch (Exception ex)
        {
            SAXParseException sax = ExceptionsUtil.getException(ex, SAXParseException.class);
            if (null == sax)
            {
                throw new FxException(messages.getMessage("configNotValid", "Dynamic Fields", ex.getMessage()), ex);
            }
            throw new FxException(messages.getMessage("configNotValidXSD", "Dynamic Fields", sax.getLineNumber(),
                    sax.getColumnNumber()), sax);
        }
    }
}
