package ru.naumen.dynamicfield.core.server.config;

import ru.naumen.commons.shared.FxException;

/**
 * Исключение, сигнализирующие о критической ошибке проверки конфигурации динамических полей.
 * <AUTHOR>
 * @since Feb 20, 2024
 */
public class ConfigurationValidationException extends FxException
{
    public ConfigurationValidationException()
    {
    }

    public ConfigurationValidationException(String msg)
    {
        super(msg);
    }

    public ConfigurationValidationException(String msg, Throwable cause)
    {
        super(msg, cause);
    }

    public ConfigurationValidationException(Throwable cause)
    {
        super(cause);
    }
}
