package ru.naumen.dynamicfield.core.server.config;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.configuration.FxBootstrapProperties;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.modules.ModulesReloadEvent;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.dynamicfield.core.server.license.DynamicFieldLicensingService;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldPresentationCatalog;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTypeCatalog;
import ru.naumen.metainfo.server.BeforeAttributeDeleteEvent;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Компонент, загружающий конфигурацию модуля динамических полей.
 * <AUTHOR>
 * @since Jul 26, 2023
 */
@Component
public class DynamicFieldConfigurationLoader
{
    @FunctionalInterface
    private interface InputStreamProvider
    {
        InputStream getStream() throws IOException;
    }

    private static final String CONFIG_NAME = "dynamic-fields";
    private static final String CONFIG_EXT = ".xml";
    private static final String CONFIG_FILE = CONFIG_NAME + CONFIG_EXT;
    private static final Logger LOG = LoggerFactory.getLogger(DynamicFieldConfigurationLoader.class);

    private static final Set<ClassFqn> LICENSED_METACLASSES = Set.of(
            FieldTypeCatalog.ITEM_FQN,
            FieldPresentationCatalog.ITEM_FQN,
            FieldTemplate.FQN,
            FieldTemplate.CASE_FQN);

    private DynamicFieldConfiguration configuration = new DynamicFieldConfiguration();

    private final DynamicFieldLicensingService licensingService;
    private final DynamicFieldConfigurationParser configurationParser;
    private final ConfigurationProperties configurationProperties;
    private final DynamicFieldConfigurationValidator configurationValidator;
    private final IPrefixObjectLoaderService objectLoader;
    private final FileContentStorage fileContentStorage;
    private final MetainfoService metainfoService;
    private final MessageFacade messages;

    @Inject
    public DynamicFieldConfigurationLoader(
            DynamicFieldLicensingService licensingService,
            DynamicFieldConfigurationParser configurationParser,
            ConfigurationProperties configurationProperties,
            DynamicFieldConfigurationValidator configurationValidator,
            IPrefixObjectLoaderService objectLoader,
            FileContentStorage fileContentStorage,
            MetainfoService metainfoService,
            MessageFacade messages)
    {
        this.licensingService = licensingService;
        this.configurationParser = configurationParser;
        this.configurationProperties = configurationProperties;
        this.configurationValidator = configurationValidator;
        this.objectLoader = objectLoader;
        this.fileContentStorage = fileContentStorage;
        this.metainfoService = metainfoService;
        this.messages = messages;
    }

    @PostConstruct
    public void init()
    {
        this.configuration = new DynamicFieldConfiguration();
        reload();
    }

    public boolean reload()
    {
        Path dynFieldConfigPath = Path.of(FxBootstrapProperties.externalPropertiesDirectory(), CONFIG_FILE);
        if (!Files.exists(dynFieldConfigPath))
        {
            String productId = configurationProperties.getDbaccessDefaultSetup().toLowerCase().trim();
            if (!productId.isEmpty())
            {
                Resource resource = new ClassPathResource(CONFIG_NAME + '-' + productId + CONFIG_EXT);
                if (resource.exists())
                {
                    return reload(resource::getInputStream);
                }
            }

            return load(null);
        }
        return reload(() -> new FileInputStream(dynFieldConfigPath.toFile()));
    }

    public boolean reload(String configuration)
    {
        return reload(() -> new ByteArrayInputStream(configuration.getBytes(StandardCharsets.UTF_8)));
    }

    public boolean reloadFromFile(String fileUuid)
    {
        return reload(() -> fileContentStorage.getContent(objectLoader.get(fileUuid)));
    }

    private boolean reload(InputStreamProvider configurationSupplier)
    {
        try (var is = configurationSupplier.getStream())
        {
            return load(is);
        }
        catch (Exception e)
        {
            LOG.error("Unable to load dynamic field configuration. Cause: " + e.getMessage(), e);
            LOG.error("Continue serving previous configuration."); //NOPMD
            return false;
        }
    }

    private boolean load(@Nullable InputStream configurationSource)
    {
        boolean licensed = licensingService.isLicensed();
        if (configurationSource == null)
        {
            this.configuration = new DynamicFieldConfiguration();
            if (licensed)
            {
                LOG.info("No 'dynamic-fields.xml' configuration file found. "
                         + "Default dynamic field configuration will be loaded.");
            }
            updateMetaClassVisibility(licensed);
            return licensed;
        }

        if (!licensed)
        {
            this.configuration = new DynamicFieldConfiguration();
            LOG.error("No license for dynamic fields. Dynamic field configuration will be ignored.");
            updateMetaClassVisibility(false);
            return false;
        }

        try
        {
            DynamicFieldConfiguration loadedConfiguration = configurationParser.parse(configurationSource);
            configurationValidator.validate(loadedConfiguration);
            updateConfiguration(loadedConfiguration);
            this.configuration = loadedConfiguration;
            updateMetaClassVisibility(
                    licensingService.isLicensed() && FieldTemplate.FQN.equals(configuration.getTemplateClassFqn()));
            LOG.info("Configuration loaded successfully.");
            return true;
        }
        catch (ConfigurationValidationException e)
        {
            LOG.error("Configuration validation failed: {}", e.getMessage()); //NOPMD
            LOG.error("Continue serving previous configuration."); //NOPMD
            return false;
        }
    }

    private void updateConfiguration(DynamicFieldConfiguration configuration)
    {
        ClassFqn templateClassFqn = configuration.getTemplateClassFqn() == null ? FieldTemplate.FQN
                : configuration.getTemplateClassFqn();
        if (configuration.getPathToGroups() != null)
        {
            AttributeFqn groupAttributeFqn = AttributeFqn.create(templateClassFqn,
                    configuration.getPathToGroups().attributeCode());
            Attribute attribute = metainfoService.getAttribute(groupAttributeFqn);
            configuration.setGroupClassFqn(attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass());
        }
    }

    @EventListener(ModulesReloadEvent.class)
    protected void onModulesReloaded()
    {
        if (licensingService.isLicensed())
        {
            reload();
        }
        else
        {
            configuration = new DynamicFieldConfiguration();
            updateMetaClassVisibility(false);
        }
    }

    @EventListener(BeforeMetaClassDeleteEvent.class)
    protected void beforeMetaClassDelete(BeforeMetaClassDeleteEvent event)
    {
        if (event.getSource() instanceof MetaClass metaClass && isClassUsed(metaClass.getFqn()))
        {
            event.cancel();
            event.getMessages().add(messages.getMessage("BeforeMetaClassDeleteEvent.usedInDynamicFieldConfiguration"));
        }
    }

    @EventListener(BeforeAttributeDeleteEvent.class)
    protected void beforeAttributeDelete(BeforeAttributeDeleteEvent event)
    {
        if (event.getSource() instanceof Attribute attribute && isAttributeUsed(attribute.getFqn()))
        {
            event.cancel();
            event.getMessages().add(messages.getMessage("BeforeAttributeDeleteEvent.usedInDynamicFieldConfiguration"));
        }
    }

    private void updateMetaClassVisibility(boolean licensed)
    {
        LICENSED_METACLASSES.stream()
                .filter(metainfoService::isMetaclassExists)
                .map(metainfoService::getMetaClass)
                .map(MetaClassImpl.class::cast)
                .forEach(metaClass -> metaClass.setHidden(!licensed));
    }

    private boolean isClassUsed(ClassFqn classFqn) // NOPMD
    {
        return classFqn.equals(configuration.getTemplateClassFqn())
               || classFqn.equals(configuration.getGroupClassFqn());
    }

    private boolean isAttributeUsed(AttributeFqn attributeFqn)
    {
        if (configuration.getTemplateClassFqn() == null)
        {
            return false;
        }
        if (configuration.getAttributeMapping().values().stream()
                .map(code -> AttributeFqn.create(configuration.getTemplateClassFqn(), code))
                .anyMatch(attributeFqn::equals))
        {
            return true;
        }
        if (Stream.concat(Stream.of(configuration.getPathToGroups()),
                        configuration.getVisibleInListConditions().stream())
                .filter(Objects::nonNull)
                .map(this::pathToFqn)
                .filter(Objects::nonNull)
                .anyMatch(attributeFqn::equals))
        {
            return true;
        }
        return configuration.getFieldTypeMapping().values().stream()
                .flatMap(List::stream)
                .flatMap(type -> type.getConditions().keySet().stream())
                .map(attributeCode -> AttributeFqn.create(configuration.getTemplateClassFqn(), attributeCode))
                .anyMatch(attributeFqn::equals);
    }

    @Nullable
    private AttributeFqn pathToFqn(AttributePath path)
    {
        if (path.sourceType() == PathSourceType.TEMPLATE && configuration.getTemplateClassFqn() != null)
        {
            return AttributeFqn.create(configuration.getTemplateClassFqn(), path.attributeCode());
        }
        else if (path.sourceType() == PathSourceType.GROUP && configuration.getGroupClassFqn() != null)
        {
            return AttributeFqn.create(configuration.getGroupClassFqn(), path.attributeCode());
        }
        return null;
    }

    public DynamicFieldConfiguration getConfiguration()
    {
        return configuration;
    }
}
