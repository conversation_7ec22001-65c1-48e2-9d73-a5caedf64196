package ru.naumen.dynamicfield.core.server.filter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Конфигурация зависимостей для фабрик условий для динамических полей.
 * <AUTHOR>
 * @since Apr 04, 2024
 */
@Configuration
public class DynamicAttributeRestrictionFactoryConfiguration
{
    @Bean
    public DynamicAttributeRestrictionFactory getDynamicAttributeRestrictionFactory(DataBaseInfo dataBaseInfo)
    {
        if (dataBaseInfo.isPostgres())
        {
            return new DynamicAttributePostgresRestrictionFactory();
        }
        else if (dataBaseInfo.isMssql())
        {
            return new DynamicAttributeSqlServerRestrictionFactory();
        }
        return new DynamicAttributeEmptyRestrictionFactory();
    }
}
