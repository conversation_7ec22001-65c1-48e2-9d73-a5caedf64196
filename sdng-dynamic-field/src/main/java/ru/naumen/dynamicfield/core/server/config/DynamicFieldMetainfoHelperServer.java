package ru.naumen.dynamicfield.core.server.config;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;
import ru.naumen.dynamicfield.core.shared.DynamicFieldMetainfoHelper;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Серверная реализация компонента для получения метаинформации, связанной с шаблонами динамических полей.
 * <AUTHOR>
 * @since Jul 31, 2023
 */
@Component
public class DynamicFieldMetainfoHelperServer implements DynamicFieldMetainfoHelper
{
    private final DynamicFieldConfigurationLoader configurationLoader;

    @Inject
    public DynamicFieldMetainfoHelperServer(DynamicFieldConfigurationLoader configurationLoader)
    {
        this.configurationLoader = configurationLoader;
    }

    @Override
    public ClassFqn getTemplateClassFqn()
    {
        DynamicFieldConfiguration configuration = configurationLoader.getConfiguration();
        return configuration.getTemplateClassFqn() == null ? FieldTemplate.FQN : configuration.getTemplateClassFqn();
    }

    @Nullable
    @Override
    public ClassFqn getGroupClassFqn()
    {
        return configurationLoader.getConfiguration().getGroupClassFqn();
    }
}
