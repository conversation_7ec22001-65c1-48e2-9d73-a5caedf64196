package ru.naumen.metainfo.shared.permissioncheck;

import ru.naumen.common.shared.Snapshotable;

/**
 * Категории объектов для контроля прав суперпользователя
 *
 * <AUTHOR>
 * @since 09.07.2024
 */
public final class AdminPermissionCategories implements Snapshotable
{
    private AdminPermissionCategories()
    {
    }

    public static final String ATTRIBUTE = "attribute";
    public static final String ATTRIBUTE_GROUP = "attributeGroup";
    public static final String ACTION_CONDITION = "actionCondition";
    public static final String FORM_PARAMETR = "formParametr";
    public static final String CONTENT_TAB = "contentTab";
    public static final String CONTENT = "content";
    public static final String CRUMB = "crumb";
    public static final String EVENT_ACTION = "eventAction";
    public static final String ESCALATION_SCHEME = "escalationScheme";
    public static final String TAG = "tag";
    public static final String USER_CLASS = "userClass";
    public static final String MAIL_CONFIG = "mailConfig";
    public static final String CATALOG = "catalog";
    public static final String CATALOG_ITEM = "catalogItem";
    public static final String CONDITION = "condition";
    public static final String TRANSITION = "transition";
    public static final String STATE = "state";
    public static final String MAIL_PROCESSOR_RULE = "mailProcessorRule";
    public static final String CHANGE_STATE_ACTION = "changeStateAction";
    public static final String QUICK_ACCESS_TITLE = "QuickAccessTitle";
    public static final String HOME_PAGE = "homePage";
    public static final String QUEUE = "queue";
    public static final String EMBEDDED_APPLICATION = "embeddedApplication";
    public static final String METACLASS = "metaClass";
    public static final String GROUP = "group";
    public static final String ROLE = "role";
    public static final String STRUCTURE = "structure";
    public static final String SCRIPT = "script";
    public static final String RECEIVE_MAIL_TASK = "receiveMailTask";
    public static final String EXECUTE_SCRIPT_TASK = "executeScriptTask";
    public static final String SCHEDULER_TASK = "schedulerTask";
    public static final String WF_PROFILE = "wfProfile";
    public static final String VALUE_MAP_ROW = "valueMapRow";
    public static final String TOP_MENU = "topMenu";
    public static final String LEFT_MENU = "leftMenu";
    public static final String TRIGGER = "trigger";
    public static final String FAST_LINK_SETTINGS = "fastLinkSettings";
    public static final String MOBILE_MENU_ITEM = "mobileMenuItem";
    public static final String MOBILE_MENU_ITEM_VALUE = "mobileMenuItemValue";
}
