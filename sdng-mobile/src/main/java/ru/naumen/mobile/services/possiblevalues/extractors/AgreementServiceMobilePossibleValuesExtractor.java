package ru.naumen.mobile.services.possiblevalues.extractors;

import static ru.naumen.mobile.VersionConstants.Version.FROM_V15;
import static ru.naumen.mobile.services.possiblevalues.extractors.PossibleValuePresentationType.AGGREGATED_TREE;
import static ru.naumen.mobile.services.possiblevalues.extractors.PossibleValuePresentationType.TREE;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableMap;

import jakarta.inject.Inject;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeSearchResult;
import ru.naumen.core.server.form.possiblevalues.extractors.servicecall.agreementservice.valueextractors.AbstractAgreementServiceTreePossibleValuesExtractor;
import ru.naumen.core.server.form.possiblevalues.extractors.servicecall.agreementservice.valueextractors.AgreementServiceLegacyTreeMobilePossibleValuesExtractor;
import ru.naumen.core.server.form.possiblevalues.extractors.servicecall.agreementservice.valueextractors.AgreementServiceListMobilePossibleValuesExtractor;
import ru.naumen.core.server.form.possiblevalues.values.AggregatedPossibleValue;
import ru.naumen.core.server.possiblevalues.api.PossibleValuesExtractorContext;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.settings.AgreementServiceEditPrs;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.mobile.context.MobileVersionContextService;

/**
 * Возвращает возможные значения для псевдоатрибута "Соглашение/Услуга"
 *
 * <AUTHOR>
 * @since 19.12.2017
 */
@Component
public class AgreementServiceMobilePossibleValuesExtractor
        implements MobilePossibleValuesExtractor<Object, PossibleValuesExtractorContext>
{
    private static final List<String> ATTR_TYPES = List.of(Association.AGREEMENT_SERVICE);

    private final SettingsStorage settingsStorage;
    private final Map<AgreementServiceEditPrs, AbstractAgreementServiceTreePossibleValuesExtractor> extractors;
    private final AgreementServiceListMobilePossibleValuesExtractor listExtractor;
    private final AgreementServiceLegacyTreeMobilePossibleValuesExtractor legacyTreeExtractor;

    @Inject
    public AgreementServiceMobilePossibleValuesExtractor(
            final SettingsStorage settingsStorage,
            final List<AbstractAgreementServiceTreePossibleValuesExtractor> extractors,
            final AgreementServiceListMobilePossibleValuesExtractor listExtractor,
            final AgreementServiceLegacyTreeMobilePossibleValuesExtractor legacyTreeExtractor)
    {
        final ImmutableMap.Builder<AgreementServiceEditPrs, AbstractAgreementServiceTreePossibleValuesExtractor> builder =
                ImmutableMap.builder();
        for (AbstractAgreementServiceTreePossibleValuesExtractor extractor : extractors)
        {
            builder.put(extractor.getEditPresentation(), extractor);
        }
        this.settingsStorage = settingsStorage;
        this.extractors = builder.build();
        this.listExtractor = listExtractor;
        this.legacyTreeExtractor = legacyTreeExtractor;
    }

    @Override
    public List<String> getSupportTypes()
    {
        return ATTR_TYPES;
    }

    @Override
    public Object extract(final PossibleValuesExtractorContext context)
    {
        if (FROM_V15.contains(MobileVersionContextService.getInvokedVersion()))
        {
            SCParameters scParameters = settingsStorage.getSettings().getScParameters();
            if (AgreementServiceEditPrs.List != scParameters.getAgreementServiceEditPrs()
                || AgreementServiceSetting.Both == scParameters.getAgreementServiceSetting())
            {
                return getExtractor().extractTree(context);
            }
            // чтобы не возвращать единственную родительскую папку, требующую отдельного действия для её разворачивания
            return listExtractor.extractTree(context);
        }
        return legacyTreeExtractor.extractTree(context);
    }

    @Override
    public PossibleValuesTreeSearchResult<AggregatedPossibleValue> search(final String searchString,
            final PossibleValuesExtractorContext context)
    {
        if (FROM_V15.contains(MobileVersionContextService.getInvokedVersion()))
        {
            SCParameters scParameters = settingsStorage.getSettings().getScParameters();
            if (AgreementServiceEditPrs.List != scParameters.getAgreementServiceEditPrs()
                    || AgreementServiceSetting.Both == scParameters.getAgreementServiceSetting())
            {
                return getExtractor().searchTree(searchString, context);
            }
            // чтобы не возвращать единственную родительскую папку, по аналогии с обычным получением
            return listExtractor.searchTree(searchString, context);
        }
        return legacyTreeExtractor.searchTree(searchString, context);
    }

    private AbstractAgreementServiceTreePossibleValuesExtractor getExtractor()
    {
        return extractors.get(settingsStorage.getSettings().getScParameters().getAgreementServiceEditPrs());
    }

    @Override
    public PossibleValuePresentationType getPresentation(Attribute attribute)
    {
        return FROM_V15.contains(MobileVersionContextService.getInvokedVersion())
                ? AGGREGATED_TREE
                : TREE;
    }
}
