package ru.naumen.mobile.metainfoadmin.server.cards;

import static ru.naumen.core.shared.permission.PermissionType.DELETE;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.tags.usage.listeners.mobile.contents.events.BeforeDeleteMobileContentEvent;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.DeleteMobileContentResponse;
import ru.naumen.mobile.metainfoadmin.shared.DeleteObjectCardMobileContentAction;

/**
 * Реализация действия удаления контента в карточке МК
 *
 * <AUTHOR>
 * @since 12 мар. 2019 г.
 */
@Component
public class DeleteObjectCardMobileContentActionHandler extends
        TransactionalActionHandler<DeleteObjectCardMobileContentAction, DeleteMobileContentResponse>
{

    private final MobileSettingsService mobileSettingsService;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public DeleteObjectCardMobileContentActionHandler(
            final MobileSettingsService mobileSettingsService,
            final ApplicationEventPublisher eventPublisher,
            final AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public DeleteMobileContentResponse executeInTransaction(final DeleteObjectCardMobileContentAction action,
            final ExecutionContext context) throws DispatchException
    {
        final AbstractMobileContent mobileContent = action.getMobileContent();
        adminPermissionCheckService.checkPermission(mobileContent, DELETE);
        final ObjectCard objectCard = mobileSettingsService.getObjectCard(action.getViewUuid());

        eventPublisher.publishEvent(new BeforeDeleteMobileContentEvent(mobileContent, objectCard));

        objectCard.removeChild(mobileContent);
        if (mobileContent instanceof MobileListsGroupContent mobileListsGroupContent)
        {
            mobileListsGroupContent.getListContents().forEach(objectCard::removeChild);
        }
        mobileSettingsService.saveContent(objectCard);
        return new DeleteMobileContentResponse(mobileContent);
    }
}
