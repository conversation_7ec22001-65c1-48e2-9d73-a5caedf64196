package ru.naumen.mobile.metainfoadmin.server.editforms;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.tags.usage.listeners.mobile.view.events.AfterAddMobileViewEvent;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentsValidator;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.ContentUuidResponse;
import ru.naumen.mobile.metainfoadmin.shared.editforms.AddMobileEditFormAction;

/**
 *
 * <AUTHOR>
 * @since 10.11.2016
 */
@Component
public class AddMobileEditFormActionHandler extends
        TransactionalActionHandler<AddMobileEditFormAction, ContentUuidResponse>
{
    private final MobileSettingsService mobileSettingsService;
    private final MobileContentsValidator mobileContentsValidator;
    private final MobileSettingsLogService mobileSettingsLogService;
    private final ApplicationEventPublisher eventPublisher;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public AddMobileEditFormActionHandler(MobileSettingsService mobileSettingsService,
            MobileContentsValidator mobileContentsValidator,
            MobileSettingsLogService mobileSettingsLogService,
            ApplicationEventPublisher eventPublisher,
            MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobileContentsValidator = mobileContentsValidator;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.eventPublisher = eventPublisher;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public ContentUuidResponse executeInTransaction(AddMobileEditFormAction action, ExecutionContext context)
            throws DispatchException
    {
        EditForm form = action.getEditForm();
        adminPermissionCheckService.checkPermission(form, CREATE);

        mobileContentsValidator.validateEditForm(form);

        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        mobileSettings.getEditForms().add(form);
        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.importSettings(mobileSettings))
        {
            if (action.getCodeOfOriginalForm() != null)
            {
                mobileSettingsLogService.logCopyView(form, action.getCodeOfOriginalForm());
            }
            else
            {
                mobileSettingsLogService.logAddView(form, "");
            }
        }

        eventPublisher.publishEvent(new AfterAddMobileViewEvent(form));

        return new ContentUuidResponse(action.getEditForm().getUuid());
    }
}