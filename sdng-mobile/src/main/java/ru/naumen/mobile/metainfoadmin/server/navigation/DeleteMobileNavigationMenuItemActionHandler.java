package ru.naumen.mobile.metainfoadmin.server.navigation;

import static ru.naumen.core.shared.permission.PermissionType.DELETE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.tags.usage.listeners.mobile.navigation.events.BeforeDeleteMobileNavigationMenuItemEvent;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.mobile.metainfoadmin.shared.navigation.DeleteMobileNavigationMenuItemAction;

/**
 * Обработчик действия удаления элемента навигационного меню.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class DeleteMobileNavigationMenuItemActionHandler extends
        MobileNavigationMenuItemActionHandler<DeleteMobileNavigationMenuItemAction>
{
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public DeleteMobileNavigationMenuItemActionHandler(ApplicationEventPublisher eventPublisher)
    {
        this.eventPublisher = eventPublisher;
    }

    @Override
    protected void addAdminLogRecord(DeleteMobileNavigationMenuItemAction action, MapProperties oldProperties,
            MobileSettings settings)
    {
        mobileSettingsLogService.logDeleteNavigationMenuItem(action.getItemTitle(), "");
    }

    @Override
    protected MapProperties getOldProperties(DeleteMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        return new MapProperties();
    }

    @Override
    protected void processAction(MobileSettings settings, DeleteMobileNavigationMenuItemAction action)
    {
        checkPermission(EDIT);

        final MobileMenuItemValue parent = findMenuItem(action.getPathToMenuItem(), settings);
        final MobileMenuItemValue removeMenuItem = findChild(parent, action.getMenuItemCode(), settings);
        checkPermission(removeMenuItem, DELETE);

        final List<MobileMenuItemValue> removeElements = new ArrayList<>();
        fillRemoveElements(removeElements, removeMenuItem);

        eventPublisher.publishEvent(new BeforeDeleteMobileNavigationMenuItemEvent(removeElements));
        getChildren(parent, settings).remove(removeMenuItem);
    }

    /**
     * Наполняет список удоляемых элементов навигационного меню для дальнейших проверок по использованию связных
     * сущностей
     * @param removeElements список с удаляемыми элементами
     * @param removeMenuItem родительский удаляемый элемент
     */
    private static void fillRemoveElements(List<MobileMenuItemValue> removeElements, MobileMenuItemValue removeMenuItem)
    {
        removeElements.add(removeMenuItem);
        final List<MobileMenuItemValue> removeChildren = removeMenuItem.getChildren();
        if (removeChildren.isEmpty())
        {
            return;
        }
        for (MobileMenuItemValue child : removeChildren)
        {
            fillRemoveElements(removeElements, child);
        }
    }

    @Override
    protected void validateSettings(DeleteMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        MobileMenuItemValue parent = validateParent(action.getPathToMenuItem(), settings);
        validateItem(parent, action.getMenuItemCode(), settings);
    }
}
