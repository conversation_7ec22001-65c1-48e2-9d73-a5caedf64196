package ru.naumen.mobile.metainfoadmin.server.attributes;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentsValidator;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.server.mappers.MobileSettingsDtoMapper;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentType;
import ru.naumen.mobile.metainfoadmin.shared.attributes.AbstractMobileAttributeAction;

/**
 *
 * <AUTHOR>
 * @since 22 мая 2015 г.
 */
public abstract class AbstractEditMobileAttrbuteActionHandler<T extends AbstractMobileAttributeAction<R>,
        R extends Result>
        extends TransactionalActionHandler<T, R>
{
    @Inject
    protected MobileSettingsService mobileSettingsService;
    @Inject
    protected MobileSettingsDtoMapper mapper;
    @Inject
    private MobileContentsValidator mobileContentsValidator;
    @Inject
    protected MessageFacade messages;
    @Inject
    protected MobileSettingsLogService mobileSettingsLogService;
    @Inject
    private AdminPermissionCheckService adminPermissionCheckService;

    @Override
    public R executeInTransaction(T action, ExecutionContext context) throws DispatchException
    {
        AbstractMobileView list = getContent(action);
        adminPermissionCheckService.checkPermission(list, EDIT);

        MapProperties oldProperties = getOldProperties(list);
        mobileContentsValidator.validateAttribute(list, action.getMobileAttribute());
        CommonMobileView commonList = (CommonMobileView)list;
        int index = commonList.getAttributes().indexOf(action.getMobileAttribute());
        if (index < 0)
        {
            throw new ClassMetainfoServiceException(getNotFoundMessage(action.getContentType()));
        }

        commonList.getAttributes().set(index, action.getMobileAttribute());

        saveContent(commonList, oldProperties);

        return transform(commonList);
    }

    protected abstract AbstractMobileView getContent(T action);

    protected abstract String getNotFoundMessage(MobileContentType contentType);

    protected abstract MapProperties getOldProperties(AbstractMobileView content);

    protected abstract void saveContent(AbstractMobileView content, MapProperties oldProperties);

    protected abstract R transform(AbstractMobileView content);
}
