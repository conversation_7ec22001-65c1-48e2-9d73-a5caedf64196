package ru.naumen.mobile.metainfoadmin.client.cards.contents.lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.CommonUtils.TitleExtractor;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.ContentType;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileRelObjectsListContent;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListContentDisplayMessages;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentPresenter;
import ru.naumen.mobile.metainfoadmin.client.MobileGinModule.RestMobileVersions;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;
import ru.naumen.mobile.metainfoadmin.client.cards.contents.listgroup.EditGroupContentWithListContentHelper;
import ru.naumen.mobile.metainfoadmin.client.cards.events.DeleteMobileContentEvent;
import ru.naumen.mobile.metainfoadmin.client.cards.events.EditMobileViewEvent;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileSettingsCommandCode;
import ru.naumen.mobile.metainfoadmin.client.events.RefreshCardContentsEvent;
import ru.naumen.mobile.metainfoadmin.client.helper.MobileContentCardsValidator;
import ru.naumen.mobile.metainfoadmin.shared.MobileSettingsDto;

/**
 * Презентер списочных контентов на карточке мобильного приложения
 * <AUTHOR>
 * @since 19.06.19
 *
 */
public class MobileListContentPresenter extends
        AbstractFlowContentPresenter<MobileListContentBase, MobileObjectCardContext, MobileListContentDisplayImpl>
{
    private static final String DEBUG_ID_PREFIX = "MobileListContentBase";
    private final MetainfoUtils metainfoUtils;
    private final List<PresenterRegistration> registerChildPresenters = new ArrayList<>();
    private final CommandFactory commandFactory; //NOSONAR
    private AsyncCallback<MobileListContentBase> refreshCallback; //NOSONAR
    private AsyncCallback<MobileListContentBase> deleteContentCallback;
    private AsyncCallback<MobileViewBase> editViewCallback; //NOSONAR
    private final MetainfoServiceAsync metainfoService;
    private final ObjectListContentDisplayMessages listMessages;
    private final MobileContentCardsValidator contentCardsValidator;
    private final EditGroupContentWithListContentHelper groupHelper;
    private final I18nUtil i18nUtil;

    @Inject
    @SuppressWarnings("java:S107")
    protected MobileListContentPresenter(
            MobileListContentDisplayImpl display, EventBus eventBus,
            MetainfoUtils metainfoUtils, CommandFactory commandFactory, MobileSettingsService mobileSettingsService,
            MetainfoServiceAsync metainfoService, ObjectListContentDisplayMessages listMessages,
            AdminWidgetResources widgetResources, MobileContentCardsValidator contentCardsValidator,
            EditGroupContentWithListContentHelper groupHelper, I18nUtil i18nUtil)
    {
        super(display, eventBus, DEBUG_ID_PREFIX, widgetResources);
        this.metainfoUtils = metainfoUtils;
        this.commandFactory = commandFactory;
        this.i18nUtil = i18nUtil;
        this.mobileSettingsService = mobileSettingsService;
        this.metainfoService = metainfoService;
        this.listMessages = listMessages;
        this.contentCardsValidator = contentCardsValidator;
        this.groupHelper = groupHelper;
    }

    @Override
    public void init(MobileListContentBase content, MobileObjectCardContext context)
    {
        super.init(content, context);
        DebugIdBuilder.ensureDebugId((Display)getDisplay(), DEBUG_ID_PREFIX, content.getCode());
        refreshCallback = new BasicCallback<MobileListContentBase>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileListContentBase content)
            {
                MobileListContentPresenter.this.content = content;
                context.setContentIntoGroup(content);
                groupHelper.editGroupContent(context, new BasicCallback<Void>()
                {
                    @Override
                    protected void handleSuccess(Void result)
                    {
                        context.getEventBus().fireEvent(new RefreshCardContentsEvent());
                        refreshInterfaceInfo();
                    }
                });
            }
        };
        deleteContentCallback = new BasicCallback<MobileListContentBase>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileListContentBase listContent)
            {
                context.getEventBus().fireEvent(new DeleteMobileContentEvent(listContent));
                refreshInterfaceInfo();
            }
        };
        editViewCallback = new BasicCallback<MobileViewBase>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileViewBase view)
            {
                context.getEventBus().fireEvent(new EditMobileViewEvent(view));
                refreshInterfaceInfo();
            }
        };
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        unbindContent();
        bindContent();
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void addCommonCommand(List<BaseCommand<MobileListContentBase, ?>> commands)
    {
        MobileCommandParam<MobileObjectCardContext, MobileListContentBase, MobileListContentBase> param =
                new MobileCommandParam<>(context, content, refreshCallback);
        MobileCommandParam<MobileObjectCardContext, MobileListContentBase, MobileListContentBase> deleteParam =
                new MobileCommandParam<>(context, content, deleteContentCallback);
        if (context.getObjectPermissions().contains(PermissionType.EDIT))
        {
            commands.add((BaseCommand<MobileListContentBase, MobileListContentBase>)commandFactory
                    .create(MobileSettingsCommandCode.MOBILE_CONTENT_EDIT, param));
        }

        if (context.getInterfaceInfo().isMobileVersionSupported(RestMobileVersions.REST_V15))
        {
            commands.add((BaseCommand<MobileListContentBase, MobileListContentBase>)commandFactory
                    .create(MobileSettingsCommandCode.MOBILE_CONTENT_LIST_ACTIONS_COMMAND, param));
        }

        if (context.getObjectPermissions().contains(PermissionType.DELETE))
        {
            commands.add((BaseCommand<MobileListContentBase, MobileListContentBase>)commandFactory
                    .create(MobileSettingsCommandCode.MOBILE_CONTENT_DELETE, deleteParam));
        }
    }

    @Override
    protected CommandParam<MobileListContentBase, MobileViewBase> getEditViewParam()
    {
        return new MobileCommandParam<>(context, content, editViewCallback);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        bindContent();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        unbindContent();
    }

    @SuppressWarnings("java:S3776")
    private void bindContent()
    {
        display.setCollapsible(false);
        display.setLabel(metainfoUtils.getLocalizedValue(content.getCaption()));

        ReadyState rs = new ReadyState(this);
        Map<String, String> infoMap = new HashMap<>();

        // Части информационного сообщения о контенте, получаемые асинхронно
        final String linkAttributePart = "linkAttribute";
        final String classesAndTypesPart = "classesAndTypes";
        final String listTitlePart = "listTitle";

        final ClassFqn fqnOfClass = content.getFqnOfClass();
        List<ClassFqn> fqns = Lists.newArrayList(content.getCases());
        fqns.add(fqnOfClass);
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>(rs)
        {
            @Override
            @SuppressWarnings("java:S3655")
            protected void handleSuccess(List<MetaClassLite> classes)
            {
                final MetaClassLite classMetaClass = classes.stream()
                        .filter(mc -> mc.getFqn().equals(fqnOfClass))
                        .findFirst()
                        .get();
                classes.remove(classMetaClass);
                infoMap.put(classesAndTypesPart,
                        messages.classAndTypes(classMetaClass.getTitle(), content.getClazz() == null
                                ? StringUtilities.join(classes, TitleExtractor.INSTANCE) : listMessages.anyType()));
            }
        });

        if (content.getContentType() == ContentType.REL_OBJECT_LIST)
        {
            Set<ClassFqn> requiredClasses = ((MobileRelObjectsListContent)content).getAttrsChain().stream()
                    .map(AttrReference::getClassFqn).collect(Collectors.toSet());
            metainfoService.getFullMetaInfo(requiredClasses, new BasicCallback<List<MetaClass>>(rs)
            {
                @Override
                protected void handleSuccess(List<MetaClass> requiredMetaClasses)
                {
                    String chainAsString = ((MobileRelObjectsListContent)content).getAttrsChain().stream()
                            .map(ar -> requiredMetaClasses.stream().filter(mc -> mc.getFqn().equals(ar.getClassFqn()))
                                    .findFirst().get().getAttribute(ar.getAttrCode()).getTitle())
                            .collect(Collectors.joining("\\"));
                    infoMap.put(linkAttributePart, listMessages.linkAttribute(chainAsString) + ' ');
                }
            });
        }

        if (content.getLinkToMobileList() != null)
        {
            mobileSettingsService.getMobileSettings(new BasicCallback<MobileSettingsDto>(rs)
            {
                @Override
                protected void handleSuccess(MobileSettingsDto settings)
                {
                    settings.getMobileSettings().getLists().stream()
                            .filter(l -> l.getUuid().equals(content.getLinkToMobileList())).findFirst()
                            .ifPresent(list -> infoMap.put(listTitlePart,
                                    ' ' + messages.listForDisplay(metainfoUtils.getLocalizedValue(list.getTitle()))));
                }
            });
        }

        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                StringBuilder infoText = new StringBuilder();
                if (content.getContentType() == ContentType.REL_OBJECT_LIST)
                {
                    infoText.append(messages.mobileContentTypeRelObjectsList());
                }
                else if (content.getContentType() == ContentType.CHILD_OBJECT_LIST)
                {
                    infoText.append(messages.mobileContentTypeChildObjectsList());
                }
                infoText.append(' ');
                if (infoMap.containsKey(linkAttributePart))
                {
                    infoText.append(infoMap.get(linkAttributePart));
                }
                if (infoMap.containsKey(classesAndTypesPart))
                {
                    infoText.append(infoMap.get(classesAndTypesPart));
                }
                if (infoMap.containsKey(listTitlePart))
                {
                    infoText.append(infoMap.get(listTitlePart));
                }
                if (!content.getVisibilityCondition().getElements().isEmpty())
                {
                    infoText.append(' ').append(messages.displayConditionsSet());
                }
                if (context.getInterfaceInfo().isMobileVersionSupported(RestMobileVersions.REST_V15)
                    && !content.getContentListActions().getActions().isEmpty())
                {
                    infoText.append(" | ")
                            .append(messages.contentListsActions())
                            .append(": ")
                            .append(content.getContentListActions().getActions().stream()
                                    .map(i18nUtil::getLocalizedTitle)
                                    .collect(Collectors.joining(", ")));
                }

                display.setInfoFromSafeConstant(infoText.toString());
            }
        });

        if (!contentCardsValidator.validate(content, context))
        {
            display.addStyleName(widgetResources.mobileContent().hasError());
        }
        else
        {
            display.removeStyleName(widgetResources.mobileContent().hasError());
        }

        validateContent();
    }

    private void unbindContent()
    {
        registerChildPresenters.forEach(pr -> pr.getPresenter().getDisplay().destroy());
        registerChildPresenters.forEach(PresenterRegistration::unregister);
        registerChildPresenters.clear();
        getDisplay().getPopup().clear();
    }
}
