package ru.naumen.mobile.metainfoadmin.server.navigation;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.mobile.navigationmenu.MobileNavigationSettings;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.navigation.GetMobileNavigationSettingsAction;

/**
 * Обработчик действия (редактирование/удаление/перемещение) выполняемого для элемента навигационного меню.
 * <AUTHOR>
 * @since 28.03.2018
 */
public abstract class MobileNavigationMenuItemActionHandler<T extends Action<SimpleResult<MobileNavigationSettings>>>
        extends TransactionalActionHandler<T, SimpleResult<MobileNavigationSettings>>
{
    @Inject
    protected MobileSettingsService mobileSettingsService;
    @Inject
    protected Dispatch dispatch;
    @Inject
    protected MessageFacade messages;
    @Inject
    protected MobileSettingsLogService mobileSettingsLogService;
    @Inject
    protected MetainfoModification metainfoModification;
    @Inject
    private AdminPermissionCheckService adminPermissionCheckService;

    @Override
    public SimpleResult<MobileNavigationSettings> executeInTransaction(T action, ExecutionContext context)
            throws DispatchException
    {
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        validateSettings(action, mobileSettings);
        MapProperties oldProperties = getOldProperties(action, mobileSettings);
        processAction(mobileSettings, action);

        metainfoModification.modify(MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);

        if (mobileSettingsService.importSettings(mobileSettings))
        {
            addAdminLogRecord(action, oldProperties, mobileSettings);
        }
        return dispatch.execute(new GetMobileNavigationSettingsAction());
    }

    protected abstract void addAdminLogRecord(T action, MapProperties oldProperties, MobileSettings settings);

    protected MobileMenuItemValue findChild(MobileMenuItemValue parent, String code, MobileSettings settings)
    {
        return getChildren(parent, settings).stream()
                .filter(new HasCode.HasCodeFilter<>(code))
                .findFirst()
                .orElse(null);
    }

    protected MobileMenuItemValue findMenuItem(LinkedList<String> path, MobileSettings settings)
    {
        MobileMenuItemValue result = null;
        for (String code : path)
        {
            result = findChild(result, code, settings);
        }
        return result;
    }

    protected List<MobileMenuItemValue> getChildren(@Nullable MobileMenuItemValue parent, MobileSettings settings)
    {
        if (parent == null)
        {
            return settings.getNavigationSettings();
        }
        return parent.getChildren() != null ? parent.getChildren() : new ArrayList<>();
    }

    protected abstract MapProperties getOldProperties(T action, MobileSettings settings);

    protected abstract void processAction(MobileSettings settings, T action);

    protected void validateItem(MobileMenuItemValue parent, String code, MobileSettings settings)
    {
        MobileMenuItemValue item = findChild(parent, code, settings);
        if (item == null)
        {
            throw new FxException(messages.getMessage("DeleteNavigationMenuItem.itemIsAbsent", code));
        }
    }

    protected MobileMenuItemValue validateParent(List<String> path, MobileSettings settings)
    {
        MobileMenuItemValue item = null;
        for (String code : path)
        {
            item = findChild(item, code, settings);
            if (item == null)
            {
                throw new FxException(messages.getMessage("DeleteNavigationMenuItem.parentItemIsAbsent", code));
            }
        }
        return item;
    }

    protected abstract void validateSettings(T action, MobileSettings settings);

    protected void checkPermission(PermissionType permissionType)
    {
        adminPermissionCheckService.checkPermission(INTERFACE_AND_NAVIGATION, permissionType);
    }

    protected void checkPermission(MobileMenuItemValue itemValue, PermissionType permissionType)
    {
        adminPermissionCheckService.checkPermission(itemValue, permissionType);
    }
}
