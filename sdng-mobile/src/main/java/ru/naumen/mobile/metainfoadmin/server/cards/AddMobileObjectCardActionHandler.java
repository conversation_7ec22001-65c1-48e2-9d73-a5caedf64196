package ru.naumen.mobile.metainfoadmin.server.cards;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.tags.usage.listeners.mobile.view.events.AfterAddMobileViewEvent;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentsValidator;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.ContentUuidResponse;
import ru.naumen.mobile.metainfoadmin.shared.cards.AddMobileObjectCardAction;

/**
 *
 * <AUTHOR>
 * @since 20 мая 2015 г.
 */
@Component
public class AddMobileObjectCardActionHandler
        extends TransactionalActionHandler<AddMobileObjectCardAction, ContentUuidResponse>
{
    private final MobileSettingsService mobileSettingsService;
    private final MobileContentsValidator mobileContentsValidator;
    private final MobileSettingsLogService logService;
    private final ApplicationEventPublisher eventPublisher;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public AddMobileObjectCardActionHandler(MobileSettingsService mobileSettingsService,
            MobileContentsValidator mobileContentsValidator,
            MobileSettingsLogService logService,
            ApplicationEventPublisher eventPublisher,
            MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobileContentsValidator = mobileContentsValidator;
        this.logService = logService;
        this.eventPublisher = eventPublisher;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public ContentUuidResponse executeInTransaction(AddMobileObjectCardAction action, ExecutionContext context)
            throws DispatchException
    {
        ObjectCard card = action.getObjectCard();
        adminPermissionCheckService.checkPermission(card, CREATE);

        mobileContentsValidator.validateCard(card);

        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        mobileSettings.getObjectCards().add(card);

        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.importSettings(mobileSettings))
        {
            if (action.getCodeOfOriginalCard() != null)
            {
                logService.logCopyView(card, action.getCodeOfOriginalCard());
            }
            else
            {
                logService.logAddView(card, "");
            }
        }

        eventPublisher.publishEvent(new AfterAddMobileViewEvent(card));

        return new ContentUuidResponse(action.getObjectCard().getUuid());
    }
}
