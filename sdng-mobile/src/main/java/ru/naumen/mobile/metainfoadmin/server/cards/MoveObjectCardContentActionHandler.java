package ru.naumen.mobile.metainfoadmin.server.cards;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfo.MobileVisibilityConditionService;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.EditMobileViewResponse;
import ru.naumen.mobile.metainfoadmin.shared.MoveObjectCardContentAction;

/**
 * Реализация действия перемещения контента в карточке МК
 * <AUTHOR>
 * @since 14 мар. 2019 г.
 */
@Component
public class MoveObjectCardContentActionHandler extends
        TransactionalActionHandler<MoveObjectCardContentAction, EditMobileViewResponse>
{

    private final MobileSettingsService mobileSettingsService;
    private final MobileSettingsLogService mobileSettingsLogService;
    private final MobileVisibilityConditionService visibilityConditionService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public MoveObjectCardContentActionHandler(MobileSettingsService mobileSettingsService,
            MobileSettingsLogService mobileSettingsLogService,
            MobileVisibilityConditionService visibilityConditionService,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.visibilityConditionService = visibilityConditionService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public EditMobileViewResponse executeInTransaction(MoveObjectCardContentAction action, ExecutionContext context)
            throws DispatchException
    {
        ObjectCard objectCard = mobileSettingsService.getObjectCard(action.getUuid());
        adminPermissionCheckService.checkPermission(objectCard, EDIT);

        MapProperties oldProperties = mobileSettingsLogService.getMobileContentLogInfo(objectCard);
        List<AbstractMobileContent> contents = objectCard.getContents();
        int index = contents.indexOf(action.getMobileContentContent());
        int newIndex = index + action.getOffset();
        if (newIndex >= 0 && newIndex < contents.size())
        {
            Collections.swap(contents, index, newIndex);
            objectCard.setContents(contents);
            if (mobileSettingsService.saveContent(objectCard))
            {
                mobileSettingsLogService.logEditView(objectCard, oldProperties, "");
            }
        }
        objectCard.getContents().forEach(content ->
        {
            visibilityConditionService.setValueVisibilityCondition(content.getVisibilityCondition());
            visibilityConditionService.prepareConditionForClient(content.getVisibilityCondition());
        });
        return new EditMobileViewResponse(objectCard);
    }

}
