package ru.naumen.mobile.metainfoadmin.client.cards.contents.embeddedapp;

import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.ThemeService;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent.Presentation;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentPresenter;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;
import ru.naumen.mobile.metainfoadmin.client.cards.events.DeleteMobileContentEvent;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileSettingsCommandCode;
import ru.naumen.mobile.metainfoadmin.client.helper.MobileContentCardsValidator;

/**
 * Презентер контента "Встроенное приложение" на карточке карточки мобильного приложения
 * <AUTHOR>
 * @since 02.10.2019
 */
public class MobileEmbeddedAppContentPresenter extends
        AbstractFlowContentPresenter<MobileEmbeddedApplicationContent, MobileObjectCardContext,
                MobileEmbeddedAppContentDisplayImpl>
{

    private static final long MOBILE_HEIGHT_DEFAULT_VALUE = 300;
    private static final long MAX_MOBILE_HEIGHT_DEFAULT_VALUE = 800;
    private static final String DEBUG_ID_PREFIX = "mobileEmbeddedAppContent";
    protected final MetainfoUtils metainfoUtils;
    private EmbeddedApplication embeddedApplication;
    private HandlerRegistration titleClickHandler;
    private AsyncCallback<MobileEmbeddedApplicationContent> refreshCallback; //NOSONAR
    private AsyncCallback<MobileEmbeddedApplicationContent> deleteContentCallback;
    private final MobileSettingsMessages mobileSettingsMessages;
    private String applicationStatus = StringUtilities.EMPTY;
    private final EmbeddedApplicationMessages appMessages;
    private final ThemeService themeService;
    private final MobileContentCardsValidator contentCardsValidator;
    private final CommonMessages commonMessages;

    @Inject
    @SuppressWarnings("java:S107")
    public MobileEmbeddedAppContentPresenter(MobileEmbeddedAppContentDisplayImpl display, EventBus eventBus,
            MetainfoUtils metainfoUtils, MobileSettingsService mobileSettingsService,
            MobileSettingsMessages mobileSettingsMessages, AdminWidgetResources widgetResources,
            EmbeddedApplicationMessages appMessages, ThemeService themeService,
            MobileContentCardsValidator contentCardsValidator, CommonMessages commonMessages)
    {
        super(display, eventBus, DEBUG_ID_PREFIX, widgetResources);
        this.metainfoUtils = metainfoUtils;
        this.mobileSettingsService = mobileSettingsService;
        this.mobileSettingsMessages = mobileSettingsMessages;
        this.appMessages = appMessages;
        this.themeService = themeService;
        this.contentCardsValidator = contentCardsValidator;
        this.commonMessages = commonMessages;
    }

    @Override
    public void init(MobileEmbeddedApplicationContent content, MobileObjectCardContext context)
    {
        super.init(content, context);
        refreshCallback = new BasicCallback<MobileEmbeddedApplicationContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileEmbeddedApplicationContent content)
            {
                MobileEmbeddedAppContentPresenter.this.content = content;
                opened = !content.isCollapsed();
                refreshInterfaceInfo();
            }
        };
        deleteContentCallback = new BasicCallback<MobileEmbeddedApplicationContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileEmbeddedApplicationContent content)
            {
                context.getEventBus().fireEvent(new DeleteMobileContentEvent(content));
                refreshInterfaceInfo();
            }
        };
        embeddedApplication = context.getInterfaceInfo().getEmbeddedApplications().get(content
                .getEmbeddedApplicationCode());
    }

    @Override
    public void refreshDisplay()
    {
        embeddedApplication = context.getInterfaceInfo().getEmbeddedApplications().get(content
                .getEmbeddedApplicationCode());
        super.refreshDisplay();
        unbindContent();
        bindContent();
        validateContent();
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void addCommonCommand(List<BaseCommand<MobileEmbeddedApplicationContent, ?>> commands)
    {
        MobileCommandParam<MobileObjectCardContext, MobileEmbeddedApplicationContent,
                MobileEmbeddedApplicationContent> param = new MobileCommandParam<>(
                context, content, refreshCallback);

        MobileCommandParam<MobileObjectCardContext, MobileEmbeddedApplicationContent,
                MobileEmbeddedApplicationContent> deleteParam = new MobileCommandParam<>(
                context, content, deleteContentCallback);

        commands.add((BaseCommand<MobileEmbeddedApplicationContent, MobileEmbeddedApplicationContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_EDIT, param));

        commands.add((BaseCommand<MobileEmbeddedApplicationContent, MobileEmbeddedApplicationContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_DELETE, deleteParam));
    }

    @Override
    protected CommandParam<MobileEmbeddedApplicationContent, MobileViewBase> getEditViewParam()
    {
        return new MobileCommandParam<>(context, content, editViewCallback);
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        bindContent();
        validateContent();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        unbindContent();
    }

    private void bindContent()
    {
        initApplicationStatus();
        bindTitle();
        bindEmbeddedApplication();

        ReadyState rs = new ReadyState(this);
        rs.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                StringBuilder infoText = new StringBuilder();
                infoText.append(mobileSettingsMessages.embeddedApplicationInfo(
                        embeddedApplication == null ? "[" + commonMessages.empty() + "]"
                                : metainfoUtils.getLocalizedValue(embeddedApplication.getTitle())));
                if (!content.getVisibilityCondition().getElements().isEmpty())
                {
                    infoText.append(' ').append(messages.displayConditionsSet());
                }
                display.setInfo(infoText.toString());
            }
        });
    }

    private void bindEmbeddedApplication()
    {
        if (embeddedApplication == null || isApplicationIsOn() && content.getPresentation() == Presentation.WINDOW)
        {
            return;
        }
        getDisplay().getGrid().resizeRows(1);
        if (getDisplay().getGrid().getCellCount(0) > 1)
        {
            getDisplay().getGrid().removeCell(0, 1);
        }
        getDisplay().getGrid().setHTML(0, 0, applicationStatus);
        if (isApplicationIsOn() && content.getPresentation() == Presentation.WIDGET)
        {
            getDisplay().getGrid().removeStyleName(widgetResources.mobileContent().embeddedApplicationDisabled());
            getDisplay().getGrid().addStyleName(widgetResources.mobileContent().embeddedApplication());
            getDisplay().getGrid().getNauCellFormatter().getElement(0, 0).getStyle()
                    .setColor(themeService.getValue().secondaryTextColor());
            getDisplay().getGrid().setHeight(getMobileApplicationHeight());
        }
        else if (!isApplicationIsOn())
        {
            getDisplay().getGrid().removeStyleName(widgetResources.mobileContent().embeddedApplication());
            getDisplay().getGrid().addStyleName(widgetResources.mobileContent().embeddedApplicationDisabled());
            getDisplay().getGrid().getNauCellFormatter().getElement(0, 0).getStyle()
                    .setColor(themeService.getValue().secondaryTextColor());
        }
        else
        {
            getDisplay().getGrid().removeStyleName(widgetResources.mobileContent().embeddedApplicationDisabled());
        }
    }

    private void bindTitle()
    {
        display.setCaption(metainfoUtils.getLocalizedValue(content.getCaption()));
        display.getWindowLabel().setText(metainfoUtils.getLocalizedValue(content.getCaption()));
        if (content.getPresentation() == Presentation.WINDOW)
        {
            display.superSetCaptionVisible(false);
            display.getWindowApplicationTitle().setVisible(true);
            display.setOpened(!isApplicationIsOn());
            removeClickHandler();
        }
        else
        {
            display.getWindowApplicationTitle().setVisible(false);
            display.superSetCaptionVisible(true);
            display.setCollapsible(content.isAllowCollapse());
            titleClickHandler = display.getCaptionWidget()
                    .addDomHandler((event) -> opened = !opened, ClickEvent.getType());
            display.setOpened(getOpenedDefaultValue());
            display.getCaptionPanel().addStyleName(widgetResources.mobileContent().contentTitle());
        }
    }

    private String getMobileApplicationHeight()
    {
        long mobileHeight = embeddedApplication == null || embeddedApplication.getMobileHeight() == null
                ? MOBILE_HEIGHT_DEFAULT_VALUE
                : embeddedApplication.getMobileHeight();
        return Math.min(MAX_MOBILE_HEIGHT_DEFAULT_VALUE, mobileHeight) + "pt";
    }

    private void initApplicationStatus()
    {
        if (embeddedApplication != null)
        {
            applicationStatus = embeddedApplication.isOn() ? StringUtilities.EMPTY
                    : mobileSettingsMessages.applicationIsOff();
            if (embeddedApplication.isOn() && context.getInterfaceInfo().isSilentMode() &&
                embeddedApplication.getApplicationType() != EmbeddedApplicationType.ClientSideApplication)
            {
                applicationStatus = appMessages.silentModeIsOn();
            }
            else if (StringUtilities.isEmpty(applicationStatus))
            {
                applicationStatus = "&nbsp;";
            }
        }
    }

    private boolean isApplicationIsOn()
    {
        return embeddedApplication != null && embeddedApplication.isOn() &&
               (!context.getInterfaceInfo().isSilentMode() ||
                embeddedApplication.getApplicationType() == EmbeddedApplicationType.ClientSideApplication);
    }

    private void removeClickHandler()
    {
        if (titleClickHandler != null)
        {
            titleClickHandler.removeHandler();
            titleClickHandler = null;
        }
    }

    private void unbindContent()
    {
        getDisplay().getPopup().clear();
        removeClickHandler();
        unbindEmbeddedApplication();
    }

    private void unbindEmbeddedApplication()
    {
        applicationStatus = StringUtilities.EMPTY;
        if (getDisplay().getGrid().getRowCount() > 0)
        {
            getDisplay().getGrid().removeRow(0);
        }
        getDisplay().getGrid().removeStyleName(widgetResources.mobileContent().embeddedApplication());
    }

    @Override
    protected void validateContent()
    {
        super.validateContent();
        if (contentCardsValidator.validate(content, context))
        {
            display.removeStyleName(widgetResources.mobileContent().hasError());
        }
        else
        {
            display.addStyleName(widgetResources.mobileContent().hasError());
        }
    }

}
