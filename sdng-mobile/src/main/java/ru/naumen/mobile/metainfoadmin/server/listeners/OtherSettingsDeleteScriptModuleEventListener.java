package ru.naumen.mobile.metainfoadmin.server.listeners;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.metainfo.server.BeforeDeleteScriptModuleEvent;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.sec.server.jwt.mobile.JwtMobileConfigurationProperties;

/**
 * Проверяет использование модуля в прочих настройках мобильного приложения.
 * Если модуль используется - процесс удаления останавливается
 *
 * <AUTHOR>
 * @since 24.09.2021
 */
@Component
public class OtherSettingsDeleteScriptModuleEventListener implements ApplicationListener<BeforeDeleteScriptModuleEvent>
{
    private final ModulesService modulesService;
    private final JwtMobileConfigurationProperties jwtProperties;
    private final MobileSettingsService settingsService;
    private final MessageFacade messages;

    @Inject
    public OtherSettingsDeleteScriptModuleEventListener(
            final ModulesService modulesService,
            final JwtMobileConfigurationProperties jwtProperties,
            final MobileSettingsService settingsService,
            final MessageFacade messages)
    {
        this.modulesService = modulesService;
        this.jwtProperties = jwtProperties;
        this.settingsService = settingsService;
        this.messages = messages;
    }

    /**
     * Проверяет использование скриптового модуля в прочих настройках МК
     *
     * @param event событие удаления скриптового модуля
     */
    @Override
    public void onApplicationEvent(BeforeDeleteScriptModuleEvent event)
    {
        if (jwtProperties.isEnabled() && modulesService.isInstalled(Modules.MOBILE_API) &&
            event.getScriptModuleCodes().contains(settingsService.getOtherSettings().getCustomLoginModuleCode()))
        {
            event.cancel();
            event.addMessage(messages.getMessage("BeforeDeleteScriptModuleEvent.errorUsedInMobileLoginSettings"));
        }
    }
}
