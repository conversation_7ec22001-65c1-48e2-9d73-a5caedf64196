package ru.naumen.mobile.metainfoadmin.client.cards.contents.listgroup;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.VerticalPanel;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentPresenter;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;
import ru.naumen.mobile.metainfoadmin.client.cards.events.DeleteMobileContentEvent;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileSettingsCommandCode;
import ru.naumen.mobile.metainfoadmin.client.events.RefreshCardContentsEvent;

/**
 * Презентер отображения контента "Агрегатор списков" на карточке настройки мобильного приложения
 *
 * <AUTHOR>
 * @since 21.06.2021
 */
@SuppressWarnings("java:S125")
public class MobileListsGroupContentPresenter extends
        AbstractFlowContentPresenter<MobileListsGroupContent, MobileObjectCardContext,
                MobileListsGroupContentDisplayImpl>
{
    private static final String OBJECTS_COUNT_IN_LIST_PREVIEW = "3";

    private static final String DEBUG_ID_PREFIX = "mobileListsGroup";
    private AsyncCallback<MobileListsGroupContent> refreshCallback; //NOSONAR
    private AsyncCallback<MobileListsGroupContent> deleteContentCallback;
    private final List<PresenterRegistration> registerChildPresenters = new ArrayList<>();
    private final MetainfoUtils metainfoUtils;
    private final MobileSettingsMessages mobileSettingsMessages;

    @Inject
    protected MobileListsGroupContentPresenter(MobileListsGroupContentDisplayImpl display, EventBus eventBus,
            MetainfoUtils metainfoUtils, MobileSettingsService mobileSettingsService,
            MobileSettingsMessages mobileSettingsMessages, AdminWidgetResources widgetResources)
    {
        super(display, eventBus, DEBUG_ID_PREFIX, widgetResources);
        this.metainfoUtils = metainfoUtils;
        this.mobileSettingsService = mobileSettingsService;
        this.mobileSettingsMessages = mobileSettingsMessages;
    }

    @Override
    public void init(MobileListsGroupContent content, MobileObjectCardContext context)
    {
        super.init(content, context);
        DebugIdBuilder.ensureDebugId((Display)getDisplay(), DEBUG_ID_PREFIX, content.getCode());
        refreshCallback = new BasicCallback<MobileListsGroupContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileListsGroupContent content)
            {
                MobileListsGroupContentPresenter.this.content = content;
                refreshInterfaceInfo();
                context.getEventBus().fireEvent(new RefreshCardContentsEvent());
            }
        };
        deleteContentCallback = new BasicCallback<MobileListsGroupContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobileListsGroupContent listsGroup)
            {
                context.getEventBus().fireEvent(new DeleteMobileContentEvent(listsGroup));
                refreshInterfaceInfo();
            }
        };
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void addCommonCommand(List<BaseCommand<MobileListsGroupContent, ?>> commands)
    {
        MobileCommandParam<MobileObjectCardContext, MobileListsGroupContent, MobileListsGroupContent> param =
                new MobileCommandParam<MobileObjectCardContext, MobileListsGroupContent, MobileListsGroupContent>(
                        context, content, refreshCallback);
        MobileCommandParam<MobileObjectCardContext, MobileListsGroupContent, MobileListsGroupContent> deleteParam =
                new MobileCommandParam<MobileObjectCardContext, MobileListsGroupContent, MobileListsGroupContent>(
                        context, content, deleteContentCallback);

        commands.add((BaseCommand<MobileListsGroupContent, MobileListsGroupContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_EDIT, param));

        commands.add((BaseCommand<MobileListsGroupContent, MobileListsGroupContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_DELETE, deleteParam));
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        unbindContent();
        bindContent();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        bindContent();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        unbindContent();
    }

    @SuppressWarnings("java:S125")
    private void bindContent()
    {
        display.setCollapsible(false);
        display.setLabel(metainfoUtils.getLocalizedValue(content.getCaption()));
        display.setOpened(true);
        List<String> contentsTitles = new ArrayList<>();
        if (content.getListContents().isEmpty())
        {
            getDisplay().getGrid().resize(0, 0);
        }
        else
        {
            getDisplay().getGrid().resize(1, Math.min(content.getListContents().size(), 2));
            content.getListContents().forEach(cnt ->
            {
                int contentIndex = content.getListContents().indexOf(cnt);
                String contentTitle = metainfoUtils.getLocalizedValue(cnt.getCaption());
                contentsTitles.add(contentTitle);
                if (contentIndex < 2)
                {
                    bindChildList(contentTitle, contentIndex);
                }
            /* TODO Раскомментировать позднее, когда будет решено, как быть с отображением дочерних контентов
            В рамках задачи NSDPRD-14671 так и не смогли решить, как должен выглядеть контент "Группа списков", если
            вложенных списков больше 2. Закомментированный кусок отвечает как раз за один из вариантов отображения.
            else
            {
                bindMoreContentsPanel(content.getListContents().size() - 2);
            }*/
            });
        }

        StringBuilder infoText = new StringBuilder();

        String listsTitle = contentsTitles.isEmpty() ?
                mobileSettingsMessages.noListContents() :
                StringUtilities.join(contentsTitles);
        infoText.append(mobileSettingsMessages.mobileListsGroupInfo(listsTitle));
        if (!content.getVisibilityCondition().getElements().isEmpty())
        {
            infoText.append(' ').append(messages.displayConditionsSet());
        }
        display.setInfo(infoText.toString());
        validateContent();
    }

    /* TODO Раскомментировать позднее, когда будет решено, как быть с отображением дочерних контентов
    В рамках задачи NSDPRD-14671 так и не смогли решить, как должен выглядеть контент "Группа списков", если
    вложенных списков больше 2. Закомментированный кусок отвечает как раз за один из вариантов отображения.
    private void bindMoreContentsPanel(int restContentsListSize)
    {
        NauGrid grid = getDisplay().getGrid();
        grid.resize(1, 3);
        SimplePanel childRestPanel = new SimplePanel();
        HTML childRestLabel = new HTML();
        childRestLabel.setHTML("...+" + restContentsListSize);
        childRestPanel.add(childRestLabel);
        childRestPanel.setStyleName(widgetResources.mobileContent().groupChildRest());
        grid.setWidget(0, 2, childRestPanel);
    }*/

    private void bindChildList(String contentTitle, int contentIndex)
    {
        NauGrid grid = getDisplay().getGrid();
        VerticalPanel childListPanel = new VerticalPanel();
        Label countOfObjectsLabel = new Label();
        countOfObjectsLabel.addStyleName(widgetResources.mobileContent().groupChildCount());
        countOfObjectsLabel.getElement().getStyle().setMarginRight(11, Unit.PX);
        countOfObjectsLabel.setText(OBJECTS_COUNT_IN_LIST_PREVIEW);
        childListPanel.add(countOfObjectsLabel);
        HTML childListLabel = new HTML(); // NOPMD NSDPRD-28509 unsafe html
        childListLabel.setHTML(contentTitle); // NOPMD NSDPRD-28509 unsafe html
        childListLabel.setStyleName(widgetResources.mobileContent().groupChildTitle());
        childListPanel.add(childListLabel);
        childListPanel.setTitle(contentTitle);
        childListPanel.setStyleName(widgetResources.mobileContent().groupChildList());
        grid.setWidget(0, contentIndex, childListPanel);
    }

    private void unbindContent()
    {
        registerChildPresenters.forEach(pr -> pr.getPresenter().getDisplay().destroy());
        registerChildPresenters.forEach(PresenterRegistration::unregister);
        registerChildPresenters.clear();
    }

    @Override
    protected CommandParam<MobileListsGroupContent, MobileViewBase> getEditViewParam()
    {
        return new MobileCommandParam<>(context, content, editViewCallback);
    }
}