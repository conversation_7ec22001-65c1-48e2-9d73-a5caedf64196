package ru.naumen.mobile.metainfoadmin.shared;

import net.customware.gwt.dispatch.shared.Action;

import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;

/**
 * Абстрактный класс действия над мобильным контентом
 * <AUTHOR>
 * @since 24 февр. 2019 г.
 */
public abstract class AbstractMobileContentAction<T extends AbstractMobileContent,
        R extends AbstractMobileContentResponse<T>>
        implements Action<R>
{

    private String viewUuid;
    private T mobileContent;

    public AbstractMobileContentAction()
    {

    }

    public AbstractMobileContentAction(String viewUuid, T mobileContent)
    {
        super();
        this.viewUuid = viewUuid;
        this.mobileContent = mobileContent;
    }

    public T getMobileContent()
    {
        return mobileContent;
    }

    public String getViewUuid()
    {
        return viewUuid;
    }

}
