package ru.naumen.mobile.metainfoadmin.server.othersettings;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.mobile.other.OtherSettings;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.SaveSecuritySettingsAction;

/**
 * Обработчик события сохранения настроек безопасности мобильного приложения
 *
 * <AUTHOR>
 * @since 13.03.2019
 */
@Component
public class SaveSecuritySettingsActionHandler
        extends OtherSettingsActionHandlerBase<SaveSecuritySettingsAction>
{
    @Inject
    private MobileSettingsLogService logService;

    @Override
    protected void logSettings(OtherSettings newSettings, OtherSettings oldSettings)
    {
        logService.logEditSecuritySettings(newSettings, oldSettings);
    }
}