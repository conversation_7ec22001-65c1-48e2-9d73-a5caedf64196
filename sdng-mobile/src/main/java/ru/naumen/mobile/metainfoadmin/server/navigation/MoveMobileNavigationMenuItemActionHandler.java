package ru.naumen.mobile.metainfoadmin.server.navigation;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.mobile.metainfoadmin.shared.navigation.MoveMobileNavigationMenuItemAction;

/**
 * Обработчик действия перемещения для элемента навигационного меню.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class MoveMobileNavigationMenuItemActionHandler extends
        MobileNavigationMenuItemActionHandler<MoveMobileNavigationMenuItemAction>
{
    @Override
    protected void addAdminLogRecord(MoveMobileNavigationMenuItemAction action, MapProperties oldProperties,
            MobileSettings settings)
    {
    }

    @Override
    protected MapProperties getOldProperties(MoveMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        return new MapProperties();
    }

    @Override
    protected void processAction(MobileSettings settings, MoveMobileNavigationMenuItemAction action)
    {
        checkPermission(EDIT);

        MobileMenuItemValue parent = findMenuItem(action.getPathToMenuItem(), settings);
        checkPermission(parent, EDIT);
        MobileMenuItemValue targetMenuItem = findChild(parent, action.getMenuItemCode(), settings);
        int oldIndex = getChildren(parent, settings).indexOf(targetMenuItem);
        int newIndex = oldIndex + action.getDirection();
        List<MobileMenuItemValue> children = getChildren(parent, settings);
        children.add(newIndex, children.remove(oldIndex));
    }

    @Override
    protected void validateSettings(MoveMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        MobileMenuItemValue parent = validateParent(action.getPathToMenuItem(), settings);
        validateItem(parent, action.getMenuItemCode(), settings);
    }

}
