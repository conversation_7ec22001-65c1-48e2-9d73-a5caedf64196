package ru.naumen.mobile.metainfoadmin.server.othersettings;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.mobile.other.OtherSettings;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.SaveOtherSettingsAction;

/**
 * Обработчик события сохранения прочих настроек мобильного приложения
 *
 * <AUTHOR>
 * @since 30.07.2018
 */
@Component
public class SaveOtherSettingsActionHandler
        extends OtherSettingsActionHandlerBase<SaveOtherSettingsAction>
{
    @Inject
    private MobileSettingsLogService logService;

    @Override
    protected void logSettings(OtherSettings newSettings, OtherSettings oldSettings)
    {
        logService.logEditOtherSettings(newSettings, oldSettings);
    }
}