package ru.naumen.mobile.metainfoadmin.server.mappers;

import static ru.naumen.core.shared.SecConstants.AbstractBO.VIEW_OBJECT_CARD;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.COMPANY;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.OBJECT_LINKED_TO_CURRENT_USER;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.METACLASS_FQN;
import static ru.naumen.mobile.VersionConstants.Version.FROM_V11_2;
import static ru.naumen.mobile.services.forms.MobileAddFormsSettingsService.getVoiceCreationSettingsSource;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.bo.ISimpleBO;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationServiceBean;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileAddFormLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileEmbeddedApplicationMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileListLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileReference;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ui.impl.EmbeddedApplicationHelper;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.mobile.context.MobileAuthenticationTypeContextService;
import ru.naumen.mobile.context.MobileVersionContextService;
import ru.naumen.mobile.mapping.dto.navigationmenu.*;
import ru.naumen.mobile.mapping.mapper.contents.EmbeddedApplicationLinksGenerator;
import ru.naumen.mobile.metainfo.MobileListsSettingsService;
import ru.naumen.mobile.services.auth.AuthConstants.AuthenticationType;
import ru.naumen.mobile.services.forms.MobileAddFormsSettingsService;
import ru.naumen.mobile.services.forms.MobileFormsCommonSettingsService;
import ru.naumen.mobile.services.objects.MobileCardsSettingsService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Класс для преобразования элементов мобильного навигационного меню в класс, который будет передан на мобильный клиент
 * <AUTHOR>
 * @since 10.05.2018
 */
@Component
public class MobileMenuItemToClientMapper extends AbstractMapper<MobileMenuItem, NavigationMenuItem>
{
    private static final Logger LOG = LoggerFactory.getLogger(MobileMenuItemToClientMapper.class);

    public static final MappingService.ObjectCreator<MobileMenuItem, NavigationMenuItem>
            NAVIGATION_MENU_ITEM_FROM_MOBILE_MENU_ITEM_CREATOR = from -> new NavigationMenuItem();

    public static Collection<NavigationMenuItem> filterOutEmptyValueMenuItems(Collection<NavigationMenuItem> menuItems)
    {
        return menuItems.stream()
                .filter(menuItem -> menuItem.getValue() != null)
                .filter(menuItem -> !(menuItem.getValue() instanceof Collection<?>)
                                    || !CollectionUtils.isEmpty((Collection<?>)menuItem.getValue()))
                .collect(Collectors.toList());
    }

    private final CurrentEmployeeContext currentEmployeeContext;
    private final MappingService mappingService;
    private final MobileListsSettingsService listsHelper;
    private final MobileFormsCommonSettingsService commonSettingsService;
    private final MobileCardsSettingsService cardService;
    private final MobileAddFormsSettingsService addFormService;
    private final MetainfoUtils metainfoUtils;
    private final RootDao rootDao;
    private final I18nUtil i18nUtil;
    private final EmbeddedApplicationServiceBean embeddedApplicationService;
    private final EmbeddedApplicationLinksGenerator applicationLinksGenerator;
    private final EmbeddedApplicationHelper embeddedApplicationHelper;
    private final AuthorizationService authorizationService;
    private final TagService tagService;
    private final MessageFacade messages;
    private final DaoFactory daoFactory;
    private final MetainfoService metainfoService;

    @Inject
    public MobileMenuItemToClientMapper(
            final @Lazy CurrentEmployeeContext currentEmployeeContext,
            final MappingService mappingService,
            final MobileListsSettingsService listsHelper,
            final MobileFormsCommonSettingsService commonSettingsService,
            final MobileCardsSettingsService cardService,
            final MobileAddFormsSettingsService addFormService,
            final @Lazy MetainfoUtils metainfoUtils,
            final @Lazy RootDao rootDao,
            final I18nUtil i18nUtil,
            final EmbeddedApplicationServiceBean embeddedApplicationService,
            final @Lazy EmbeddedApplicationLinksGenerator applicationLinksGenerator,
            final EmbeddedApplicationHelper embeddedApplicationHelper,
            final AuthorizationService authorizationService,
            final TagService tagService,
            final MessageFacade messages,
            final DaoFactory daoFactory,
            final MetainfoService metainfoService)
    {
        super(MobileMenuItem.class, NavigationMenuItem.class);
        this.currentEmployeeContext = currentEmployeeContext;
        this.mappingService = mappingService;
        this.metainfoUtils = metainfoUtils;
        this.listsHelper = listsHelper;
        this.commonSettingsService = commonSettingsService;
        this.cardService = cardService;
        this.addFormService = addFormService;
        this.rootDao = rootDao;
        this.i18nUtil = i18nUtil;
        this.embeddedApplicationService = embeddedApplicationService;
        this.applicationLinksGenerator = applicationLinksGenerator;
        this.embeddedApplicationHelper = embeddedApplicationHelper;
        this.authorizationService = authorizationService;
        this.tagService = tagService;
        this.messages = messages;
        this.daoFactory = daoFactory;
        this.metainfoService = metainfoService;
    }

    @Override
    public void transform(MobileMenuItem from, NavigationMenuItem to, @Nullable DtoProperties properties)
    {
        to.setTitle(i18nUtil.getLocalizedTitle(from));
        to.setMenuItemUuid(from.getUUID());
        to.setTypeCode(from.getType());
        to.setValue(extractValueFromMobileMenuItem(from));
    }

    /**
     * Извлекает значение элемента мобильного навигационного меню
     * @param menuItem элемент мобильного навигационного меню, значение которого будет извлекаться
     * @return преобразованное значение
     */
    private Object extractValueFromMobileMenuItem(final MobileMenuItem menuItem)
    {
        return switch (menuItem.getType())
        {
            case addFormLink -> extractAddFormLinkValue(menuItem);
            case listLink -> extractListLinkValue(menuItem);
            case reference -> extractReferenceValue(menuItem);
            case chapter -> extractChapterValue(menuItem);
            case barcodeScanButton -> getBarcodeScannerButton();
            case embeddedApplication -> extractEmbeddedApplicationValue(menuItem);
        };
    }

    private Collection<AddFormLink> extractAddFormLinkValue(final MobileMenuItem menuItem)
    {
        MobileAddFormLink value = (MobileAddFormLink)menuItem.getValue();
        boolean isVoiceServicesAvailable = commonSettingsService.isVoiceServicesAvailable();

        return addFormService.findAddFormsForUser(currentEmployeeContext.getCurrentEmployee()).stream()
                .filter(addForm -> addForm.getCode().equals(value.getCode()))
                .findFirst()
                // Кнопка добавления на данный момент имеет только одно значение,
                // но в будущем будет иметь множество. Поэтому сразу в апи отправляем список
                .map(addForm ->
                {
                    String title = metainfoUtils.getLocalizedValue(addForm.getCaption());
                    boolean isAllowVoiceCreation = isVoiceServicesAvailable &&
                                                   getVoiceCreationSettingsSource(addForm).isAllowVoiceCreation();

                    return Collections.singletonList(new AddFormLink(title, addForm.getCode(), isAllowVoiceCreation));
                })
                .orElse(null);
    }

    private Collection<NavigationMenuItem> extractChapterValue(final MobileMenuItem menuItem)
    {
        return filterOutEmptyValueMenuItems(mappingService.transform(getAvailableMenuItems(menuItem.getChildren()),
                NAVIGATION_MENU_ITEM_FROM_MOBILE_MENU_ITEM_CREATOR));
    }

    /**
     * Возвращает доступные элементы навигационного меню.
     * @param menuItems список элементов для проверки
     * @return активные элементы навигационного меню
     */
    public List<MobileMenuItem> getAvailableMenuItems(final List<MobileMenuItem> menuItems)
    {
        return menuItems.stream()
                .filter(this::isAvailableMenuItem)
                .collect(Collectors.toList());
    }

    @Nullable
    private ListLink extractListLinkValue(final MobileMenuItem menuItem)
    {
        final MobileListLink value = (MobileListLink)menuItem.getValue();
        final Employee employee = currentEmployeeContext.getCurrentEmployee();
        final MobileList list = listsHelper.safeFindListForUser(value.getCode(), employee, true);

        return (list != null) ? new ListLink(list.getCode(), list.getFqnOfClass().toString()) : null;
    }

    @Nullable
    private Reference extractReferenceValue(final MobileMenuItem menuItem)
    {
        final MobileReference reference = (MobileReference)menuItem.getValue();
        // получаем карточку без проверок прав доступа, проверить не можем - нет объекта
        final ObjectCard referenceCard = cardService.findCard(reference.getCode());
        if (referenceCard == null)
        {
            return null;
        }

        // получаем объект, относительно которого делаем проверку прав далее
        final AbstractBO object = fetchObjectForReferenceValue(referenceCard.getFqnOfClass());
        if (object == null)
        {
            return null;
        }

        // получаем карточку с проверкой прав
        final ObjectCard objectCard = cardService.findCardForProfileSafe(object, reference.getCode());
        if (objectCard == null)
        {
            return null;
        }
        return new Reference(objectCard.getUuid(), object.getUUID());
    }

    @Nullable
    private AbstractBO fetchObjectForReferenceValue(final ClassFqn fqnOfCardObjects)
    {
        if (Constants.Root.FQN.isSameClass(fqnOfCardObjects))
        {
            return rootDao.getCoreRoot();
        }

        if (CurrentEmployeeContext.isSuperUser())
        {
            return null;
        }

        if (Constants.Employee.FQN.isSameClass(fqnOfCardObjects))
        {
            return currentEmployeeContext.getCurrentEmployee();
        }
        else if (!Constants.OU.FQN.isSameClass(fqnOfCardObjects))
        {
            // остальные типы объектов не поддерживаются в навигационном меню
            return null;
        }

        final Employee employee = currentEmployeeContext.getCurrentEmployee();
        return (employee != null) ? employee.getParent() : null;
    }

    @Nullable
    private NavigationEmbeddedApplicationMenuItem extractEmbeddedApplicationValue(final MobileMenuItem menuItem)
    {
        if (!AuthenticationType.JWT.equals(MobileAuthenticationTypeContextService.getAuthenticationType())
            || !FROM_V11_2.contains(MobileVersionContextService.getInvokedVersion())
            || !(menuItem instanceof MobileEmbeddedApplicationMenuItem embeddedApplicationMenuItem))
        {
            return null;
        }

        final EmbeddedApplication application =
                embeddedApplicationService.getApplication(embeddedApplicationMenuItem.getEmbeddedApplicationCode());
        final EmbeddedApplicationType applicationType = application.getApplicationType();
        final String link = getEmbeddedApplicationLink(embeddedApplicationMenuItem, application);

        return link != null
                ? new NavigationEmbeddedApplicationMenuItem(link, applicationType)
                : null;
    }

    @Nullable
    private BarcodeScannerButton getBarcodeScannerButton()
    {
        if (commonSettingsService.getBarcodeScannerSettings(currentEmployeeContext.getCurrentEmployee()) != null)
        {
            return new BarcodeScannerButton();
        }
        return null;
    }

    /**
     * Получает из элемента навигационного меню типа "Встроенное приложение" - ссылку на данное встроенное приложение
     * @param menuItem элемент навигационного меню МК типа "Встроенное приложение"
     * @param application вcтроенное приложение
     * @return ссылка на встроенное приложение
     */
    @Nullable
    private String getEmbeddedApplicationLink(final MobileEmbeddedApplicationMenuItem menuItem,
            final EmbeddedApplication application)
    {
        final ISimpleBO linkObject =
                getApplicationLinkObject(currentEmployeeContext.getCurrentEmployee(), menuItem);

        return (linkObject != null && authorizationService.hasPermission(linkObject, VIEW_OBJECT_CARD))
                ? applicationLinksGenerator.createApplicationLink(menuItem, application, linkObject, null)
                : null;
    }

    private boolean isAvailableMenuItem(final MobileMenuItem menuItem)
    {
        final boolean isEnabledByTag = tagService.isElementEnabled(menuItem);
        if (!MobileMenuItemType.embeddedApplication.equals(menuItem.getType()))
        {
            return isEnabledByTag;
        }

        final Employee currentEmployee = currentEmployeeContext.getCurrentEmployee();
        final MobileEmbeddedApplicationMenuItem applicationMenuItem = (MobileEmbeddedApplicationMenuItem)menuItem;
        final EmbeddedApplication embeddedApplication =
                embeddedApplicationService.getApplication(applicationMenuItem.getEmbeddedApplicationCode());
        return isEnabledByTag && embeddedApplicationHelper.isEnabled(embeddedApplication) &&
               (currentEmployee == null || checkProfiles(currentEmployee, applicationMenuItem.getProfiles()));
    }

    /**
     * Проверяет наличие хотя бы одного из указанных профилей у пользователя
     * @param employee пользователь
     * @param profiles проверяемые профили
     * @return true, если профиль объекта настроен в элементе меню
     */
    private boolean checkProfiles(final Employee employee, final @Nullable List<String> profiles)
    {
        if (profiles == null || profiles.isEmpty())
        {
            return true;
        }

        for (String profileCode : profiles)
        {
            if (!authorizationService.hasProfile(employee, profileCode))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * Получаем объект, с которым работает приложение с учетом настроек элемента навигационного меню МК
     * типа "Встроенное приложение"
     *
     * @param currentEmployee текущий пользователь. Может быть null, если это суперпользователь
     * @param menuItem элемент навигационного меню МК типа "Встроенное приложение"
     * @return объект, с которым работает приложение, либо null, если такого объекта не существует
     */
    @Nullable
    private ISimpleBO getApplicationLinkObject(@Nullable final Employee currentEmployee,
            final MobileEmbeddedApplicationMenuItem menuItem)
    {
        try
        {
            return switch (menuItem.getLinkObject())
            {
                //Если currentEmployee == null, значит мы работаем под суперпользователем, а для него не определён
                // текущий пользователь и, соответственно, объект связанный с текущим пользователем
                case CURRENT_USER -> currentEmployee;
                case COMPANY -> rootDao.getCoreRoot();
                case OBJECT_LINKED_TO_CURRENT_USER -> currentEmployee != null
                        ? getApplicationLinkObjectByAttrsChain(
                        currentEmployee,
                        Objects.requireNonNull(menuItem.getLinkObjectAttrs()))
                        : null;
                default -> throw new FxException(
                        "Menu item has illegal link object - %s".formatted(menuItem.getLinkObject()));
            };
        }
        catch (RuntimeException exception)
        {
            LOG.warn(messages.getMessage("mobile.navigationMenu.error.couldNotGetLinkedObject", menuItem.getTitle(),
                    menuItem.getCode()), exception);
            return null;
        }
    }

    /**
     * Пытаемся получить объект работы приложения по цепочке ссылочных атрибутов
     * @param currentEmployee текущий пользователь
     * @param attrReferences цепочка ссылочных атрибутов элемента навигационного меню типа "Встроенное приложение"
     * @return объект работы приложения, когда существует объект связанный с текущим пользователем
     */
    private ISimpleBO getApplicationLinkObjectByAttrsChain(final Employee currentEmployee,
            final List<AttrReference> attrReferences)
    {
        //определяем тип поднимаемого объекта
        final AttrReference attrReference = attrReferences.get(attrReferences.size() - 1);
        final MetaClass metaClass = metainfoService.getMetaClass(attrReference.getClassFqn());
        final Attribute attribute = Objects.requireNonNull(metaClass.getAttribute(attrReference.getAttrCode()));
        final String targetClassFqn = Objects.requireNonNull(attribute.getType().getProperty(METACLASS_FQN));

        //создаем фильтр с указанием цепочки атрибутов и объекта, с которого начинаем раскручивать цепочку
        final InAttributesChainFilter chainFilter =
                new InAttributesChainFilter(attrReferences, false).setEqUuid(currentEmployee.getUUID());

        final IDao<IUUIDIdentifiable> dao = daoFactory.get(ClassFqn.parse(targetClassFqn));
        return (ISimpleBO)dao.list(List.of(chainFilter), 0, 1).get(0);
    }
}
