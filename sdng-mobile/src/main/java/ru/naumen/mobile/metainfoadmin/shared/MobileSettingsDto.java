package ru.naumen.mobile.metainfoadmin.shared;

import java.io.Serializable;

import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.mobile.MobileSettings;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Объект переноса данных для всех настроек мобильного приложения.
 * Помимо самих настроек содержит объект с информацией для отображения
 * их в интерфейсе технолога.
 *
 * <AUTHOR>
 * @since 20 апр. 2015 г.
 */
public class MobileSettingsDto implements IsSerializable, Serializable
{
    private static final long serialVersionUID = -7302800219253152435L;

    private MobileSettings mobileSettings;
    private InterfaceInfo interfaceInfo;
    private PermissionHolder permissionHolder;

    public MobileSettingsDto()
    {
    }

    public MobileSettingsDto(MobileSettings mobileSettings, InterfaceInfo interfaceInfo,
            PermissionHolder permissionHolder)
    {
        this.mobileSettings = mobileSettings;
        this.interfaceInfo = interfaceInfo;
        this.permissionHolder = permissionHolder;
    }

    public InterfaceInfo getInterfaceInfo()
    {
        return interfaceInfo;
    }

    public MobileSettings getMobileSettings()
    {
        return mobileSettings;
    }

    public PermissionHolder getPermissionHolder()
    {
        return permissionHolder;
    }
}
