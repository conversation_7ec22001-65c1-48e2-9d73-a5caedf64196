package ru.naumen.mobile.metainfoadmin.server.mappers;

import static ru.naumen.mobile.Constants.AdminPermissionsCheck.ADD_FORM_PREFIX;
import static ru.naumen.mobile.Constants.AdminPermissionsCheck.EDIT_FORM_PREFIX;
import static ru.naumen.mobile.Constants.AdminPermissionsCheck.OBJECT_CARD_PREFIX;
import static ru.naumen.mobile.Constants.AdminPermissionsCheck.OBJECT_LIST_PREFIX;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.server.config.SilentModeSettingsProvider;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.bo.association.AgreementServiceAttributeProvider;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.AbstractUserEntity;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Workflow;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.mobile.other.MobileLoginType;
import ru.naumen.metainfo.shared.spi.store.Transition;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode;
import ru.naumen.mobile.controllers.common.MobileVersionService;
import ru.naumen.mobile.metainfo.MobileVisibilityConditionService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentTitleExtractor;
import ru.naumen.mobile.metainfoadmin.server.MobileTagHelper;
import ru.naumen.mobile.metainfoadmin.server.MobileTagHelper.ListSettings;
import ru.naumen.mobile.metainfoadmin.server.lists.MobileListsUsagePlaceCache;
import ru.naumen.mobile.metainfoadmin.shared.InterfaceInfo;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentDto;
import ru.naumen.mobile.metainfoadmin.shared.MobileSettingsDto;
import ru.naumen.mobile.metainfoadmin.shared.addforms.MobileAddFormDto;
import ru.naumen.mobile.metainfoadmin.shared.cards.MobileObjectCardDto;
import ru.naumen.mobile.metainfoadmin.shared.editforms.MobileEditFormDto;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListDto;
import ru.naumen.objectlist.server.advlist.AdvlistUtils;

/**
 * Отвечает за преобразование настроек мобильного приложения в объекты пригодные
 * для отображения в интерфейсе администратора.
 *
 * <AUTHOR>
 * @since 24 апр. 2015 г.
 */
@Component
public class MobileSettingsDtoMapper implements MobileSettingsFiltersMapper
{
    /**
     * Контекст, содержащий данные которые могут понадобиться при трансформации
     * а также объект хранения информации необходимой для отображения настроек в
     * интерфейсе технолога.
     *
     * <AUTHOR>
     */
    public final class TransformationContext
    {
        private final ClassFqn clazz;
        private final InterfaceInfo interfaceInfo;
        private Map<AttributeFqn, Attribute> attributesMap;

        private TransformationContext(ClassFqn clazz, InterfaceInfo interfaceInfo)
        {
            this.clazz = clazz;
            this.interfaceInfo = interfaceInfo;
        }

        public @Nullable Attribute getAttribute(final String attributeFqnString)
        {
            if (!AttributeFqn.isAttributeFqn(attributeFqnString))
            {
                return null;
            }
            AttributeFqn attributeFqn = AttributeFqn.parse(attributeFqnString);
            if (attributesMap == null)
            {
                initAttributes();
            }
            Attribute attribute = attributesMap.get(attributeFqn);
            if (attribute == null)
            {
                return null;
            }
            if (!interfaceInfo.hasAttribute(attribute))
            {
                interfaceInfo.addAttribute(snapshotService.prepare(attribute));
            }

            return attribute;
        }

        public ClassFqn getClazz()
        {
            return clazz;
        }

        public InterfaceInfo getInterfaceInfo()
        {
            return interfaceInfo;
        }

        private void initAttributes()
        {
            attributesMap = new HashMap<>();
            if (!metainfoService.isMetaclassExists(clazz))
            {
                return;
            }
            Collection<ClassFqn> metaClasses = metainfoService.getMetaClassDescendants(clazz, true);
            for (ClassFqn fqn : metaClasses)
            {
                MetaClass metaClass = metainfoService.getMetaClass(fqn);
                for (Attribute attribute : metaClass.getAttributes())
                {
                    AttributeFqn attributeFqn = fqn.isClass() ? attribute.getFqn() : attribute.getHierarchicalFqn();
                    if (!attributesMap.containsKey(attributeFqn))
                    {
                        attributesMap.put(attributeFqn, attribute);
                    }
                }
            }
        }
    }

    protected final MetainfoUtils metainfoUtils;
    private final MetainfoServiceBean metainfoService;
    private final SecurityService securityService;
    private final TagService tagService;
    private final SnapshotService snapshotService;
    private final ListFilterMapper filterMapper;
    private final MobileListSortMapper sortMapper;
    private final AgreementServiceAttributeProvider agreementServiceProvider;
    private final MobileTagHelper tagHelper;
    private final EmbeddedApplicationService applicationService;
    private final SilentModeSettingsProvider silentMode;
    private final AdvlistUtils advlistUtils;
    private final MobileListsUsagePlaceCache mobileListsUsagePlaceCache;
    private final MobileVisibilityConditionService visibilityConditionService;
    private final MobileContentTitleExtractor titleExtractor;
    private final MobileVersionService mobileVersionService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public MobileSettingsDtoMapper(
            final MetainfoUtils metainfoUtils,
            final MetainfoServiceBean metainfoService,
            final SecurityService securityService,
            final TagService tagService,
            final SnapshotService snapshotService,
            final ListFilterMapper filterMapper,
            final MobileListSortMapper sortMapper,
            final AgreementServiceAttributeProvider agreementServiceProvider,
            final MobileTagHelper tagHelper,
            final EmbeddedApplicationService applicationService,
            final SilentModeSettingsProvider silentMode,
            final AdvlistUtils advlistUtils,
            final MobileListsUsagePlaceCache mobileListsUsagePlaceCache,
            final MobileVisibilityConditionService visibilityConditionService,
            final MobileContentTitleExtractor titleExtractor,
            final MobileVersionService mobileVersionService, AdminPermissionCheckService adminPermissionCheckService)
    {
        this.metainfoUtils = metainfoUtils;
        this.metainfoService = metainfoService;
        this.securityService = securityService;
        this.tagService = tagService;
        this.snapshotService = snapshotService;
        this.filterMapper = filterMapper;
        this.sortMapper = sortMapper;
        this.agreementServiceProvider = agreementServiceProvider;
        this.tagHelper = tagHelper;
        this.applicationService = applicationService;
        this.silentMode = silentMode;
        this.advlistUtils = advlistUtils;
        this.mobileListsUsagePlaceCache = mobileListsUsagePlaceCache;
        this.visibilityConditionService = visibilityConditionService;
        this.titleExtractor = titleExtractor;
        this.mobileVersionService = mobileVersionService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    /**
     * Преобразовать контент объекта в соответствующий ей объект переноса данных.
     *
     */
    public MobileContentDto transform(AbstractMobileView content)
    {
        return switch (content)
        {
            case MobileList mobileList -> transform(mobileList);
            case ObjectCard objectCard -> transform(objectCard);
            case AddForm addForm -> transform(addForm);
            case EditForm editForm -> transform(editForm);
            default -> null;
        };
    }

    /**
     * Преобразовать форму добавления объекта в соответствующий ей объект переноса данных.
     *
     */
    public MobileAddFormDto transform(AddForm addForm)
    {
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        getFromAddOrEditForm(addForm, new TransformationContext(addForm.getFqnOfClass(), interfaceInfo));
        MobileAddFormDto result = new MobileAddFormDto(addForm, interfaceInfo);
        result.setPermissions(adminPermissionCheckService.getPermissions(addForm));
        return result;
    }

    /**
     * Преобразовать форму редактирования объекта в соответствующий ей объект переноса данных.
     *
     */
    public MobileEditFormDto transform(EditForm editForm)
    {
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        getFromAddOrEditForm(editForm, new TransformationContext(editForm.getFqnOfClass(), interfaceInfo));
        MobileEditFormDto result = new MobileEditFormDto(editForm, interfaceInfo);
        result.setPermissions(adminPermissionCheckService.getPermissions(editForm));
        return result;
    }

    /**
     * Преобразовать список объектов в соответствующий ему объект переноса данных.
     *
     */
    public MobileListDto transform(MobileList list)
    {
        MobileList preparedList = tagHelper.prepareMobileList(list,
                Sets.newHashSet(ListSettings.Filters, ListSettings.Orders));
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        TransformationContext context = new TransformationContext(preparedList.getFqnOfClass(), interfaceInfo);
        preparedList.setListFilter(prepareFilter(preparedList.getListFilter(), context));
        preparedList.setSortForClient(sortMapper.transform(preparedList.getOrders(), context));
        getFromObjectList(preparedList, context);
        MobileListDto mobileListDto = new MobileListDto(preparedList, interfaceInfo);
        mobileListDto.setPermissions(adminPermissionCheckService.getPermissions(list));
        mobileListDto.setUsagePlaces(mobileListsUsagePlaceCache.getAllUsagePlaces(list.getUuid()));
        return mobileListDto;
    }

    /**
     * Преобразовать настройки мобильного приложения в соответствующий им
     * объект переноса данных.
     *
     */
    public MobileSettingsDto transform(MobileSettings mobileSettings)
    {
        return new MobileSettingsDto(mobileSettings, getInterfaceInfo(mobileSettings),
                getAdminPermissions(mobileSettings));
    }

    /**
     * Преобразовать карточку объекта в соответствующий ей объект переноса данных.
     *
     */
    public MobileObjectCardDto transform(ObjectCard card)
    {
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        getFromObjectCard(card, new TransformationContext(card.getFqnOfClass(), interfaceInfo));
        MobileObjectCardDto result = new MobileObjectCardDto(card, interfaceInfo);
        result.setPermissions(adminPermissionCheckService.getPermissions(card));
        return result;
    }

    /**
     * Формирует строковый fqn атрибута находящегося в классе или первом типе контента в котором он редактируемый
     * @param content контент мобильного приложения
     * @param attr мобильный атрибут
     */
    String prepareAttrFqn(AbstractMobileView content, MobileAttribute attr)
    {
        // Если контент настроен на класс, то проверки не нужны
        if (null != content.getClazz())
        {
            return prepareForClass(content.getClazz(), attr);
        }

        // Если используется только 1 тип, то проверку делать не нужно
        if (content.getCases().size() == 1)
        {
            return AttributeFqn.toString(content.getCases().getFirst(), attr.getCode());
        }

        return prepareForMoreThenOneCases(content.getCases(), attr);
    }

    private String prepareForClass(ClassFqn classFqn, MobileAttribute attr)
    {
        String attrFqn = attr.getAttributeFqnString();
        Collection<MetaClass> childMetaClasses = metainfoService.getClassTypes(classFqn);
        for (MetaClass childMetaClass : childMetaClasses)
        {
            if (!childMetaClass.hasAttribute(attr.getCode()))
            {
                continue;
            }

            Attribute attribute = childMetaClass.getAttribute(attr.getCode());
            if (attribute != null && attribute.isEditable())
            {
                attrFqn = AttributeFqn.toString(childMetaClass.getFqn(), attribute.getCode());
                break;
            }
        }
        return attrFqn;
    }

    private String prepareForMoreThenOneCases(List<ClassFqn> cases, MobileAttribute attr)
    {
        String attrFqn = attr.getAttributeFqnString();
        for (ClassFqn objCase : cases)
        {
            if (!metainfoService.isMetaclassExists(objCase))
            {
                continue;
            }

            final MetaClassImpl metaClass = metainfoService.getMetaClass(objCase);
            if (!metaClass.hasAttribute(attr.getCode()))
            {
                continue;
            }

            Attribute attribute = metaClass.getAttribute(attr.getCode());
            if (attribute != null && attribute.isEditable())
            {
                attrFqn = AttributeFqn.toString(objCase, attr.getCode());
                break;
            }

            //Если атрибут не редактируем, то возвращаем последний не редатируемый fqn атрибута
            attrFqn = AttributeFqn.toString(objCase, attr.getCode());
        }

        return attrFqn;
    }

    private void extractTransitions(ObjectCard card, InterfaceInfo interfaceInfo)
    {
        Collection<ClassFqn> fqns = CollectionUtils.isEmpty(card.getCases()) ?
                Lists.newArrayList(metainfoService.getMetaClassDescendants(card.getFqnOfClass(), true)) :
                card.getCases();

        for (ClassFqn fqn : fqns)
        {
            if (!metainfoService.isMetaclassExists(fqn))
            {
                continue;
            }

            Workflow workflow = metainfoService.getMetaClass(fqn).getWorkflow();
            if (workflow == null)
            {
                return;
            }
            for (ru.naumen.metainfo.shared.elements.wf.Transition transition : workflow.getTransitions())
            {
                String transitionCode = transition.getBeginState() + "-" + transition.getEndState();
                List<String> transitionsForCard = interfaceInfo.getTransitionCodes().get(card.getUuid());
                if (transitionsForCard == null || (!transitionsForCard.contains(transitionCode)
                                                   && transition.isEnabled()))
                {
                    interfaceInfo.addTransitionCodes(card.getUuid(), transition.getBeginState(),
                            transition.getEndState());
                }
            }
        }
    }

    private void extractStateTitle(ObjectCard card, String stateCode, InterfaceInfo interfaceInfo)
    {
        if (interfaceInfo.getStateTitles().containsKey(stateCode))
        {
            return;
        }

        Collection<ClassFqn> fqns;
        //Если типы не определены ищем статус только в классе,
        //в противном случае ищем статус в определенных типах
        if (CollectionUtils.isEmpty(card.getCases()))
        {
            fqns = Lists.newArrayList(card.getFqnOfClass());
        }
        else
        {
            fqns = card.getCases();
        }

        for (ClassFqn fqn : fqns)
        {
            if (!metainfoService.isMetaclassExists(fqn))
            {
                continue;
            }

            Workflow workflow = metainfoService.getMetaClass(fqn).getWorkflow();
            if (workflow == null)
            {
                return;
            }

            putStateIfPresenceInMetaClass(stateCode, interfaceInfo, workflow);

            if (!interfaceInfo.getStateTitles().containsKey(stateCode) && fqn.isClass())
            {
                for (MetaClass mcCase : metainfoService.getClassTypes(fqn))
                {
                    putStateIfPresenceInMetaClass(stateCode, interfaceInfo, mcCase.getWorkflow());

                    if (interfaceInfo.getStateTitles().containsKey(stateCode))
                    {
                        return;
                    }
                }
            }
        }
    }

    private Attribute getAttribute(AbstractMobileView content, String attributeFqn, TransformationContext context)
    {
        AttributeFqn fqn = AttributeFqn.parse(attributeFqn);
        if (content instanceof AddForm && LinkAttributeUtils.isAgreementService(fqn))
        {
            return agreementServiceProvider.get(metainfoService.getMetaClass(fqn.getClassFqn()));
        }
        return context.getAttribute(attributeFqn);
    }

    private void getFromAddForms(MobileSettings mobileSettings, InterfaceInfo interfaceInfo)
    {
        if (CollectionUtils.isEmpty(mobileSettings.getAddForms()))
        {
            return;
        }
        mobileSettings.getAllAddFormsStream().forEach(addForm ->
                getFromAddOrEditForm(addForm, new TransformationContext(addForm.getFqnOfClass(), interfaceInfo)));
    }

    private void getFromAddOrEditForm(CommonMobileView content, TransformationContext context)
    {
        getFromView(content, context);
    }

    private void getFromView(AbstractMobileView view, TransformationContext context)
    {
        ClassFqn fqn = view.getFqnOfClass();
        InterfaceInfo interfaceInfo = context.getInterfaceInfo();
        HashMap<ClassFqn, List<String>> attributeGroups = interfaceInfo.getAttributeGroups();
        HashMap<ClassFqn, List<String>> attributeCodes = interfaceInfo.getAttributeCodes();
        if (!metainfoService.isMetaclassExists(fqn))
        {
            return;
        }
        MetaClass mc = metainfoService.getMetaClass(fqn);
        interfaceInfo.setMetaClassTitle(fqn, mc.getTitle());
        interfaceInfo.setIsHasMetaClassWorkflow(fqn, mc.isHasWorkflow());
        attributeGroups.putIfAbsent(fqn, Lists.newArrayList(mc.getAttributeGroupCodes()));
        attributeCodes.putIfAbsent(fqn, Lists.newArrayList(mc.getAttributeCodes()));

        List<ClassFqn> cases = view.getCases().stream()
                .filter(metainfoService::isMetaclassExists).toList();
        cases.forEach(caze -> interfaceInfo.setMetaClassTitle(caze, metainfoService.getMetaClass(caze).getTitle()));

        // зависящие от типа объекта МК (по коду и типу объекта МК)
        Set<ClassFqn> mobileContentCasesWithParents;
        Pair<String, String> key = new Pair<>(view.getClass().getSimpleName(), view.getUuid());
        mobileContentCasesWithParents = new HashSet<>();
        // Проходим по каждому типу и формируем список из него и его подтипов
        cases.forEach(caseFqn ->
        {
            MetaClass metaClass = metainfoService.getMetaClass(caseFqn);
            attributeGroups.putIfAbsent(caseFqn, Lists.newArrayList(metaClass.getAttributeGroupCodes()));
            attributeCodes.putIfAbsent(caseFqn, Lists.newArrayList(metaClass.getAttributeCodes()));
            while (!mobileContentCasesWithParents.contains(caseFqn))
            {
                mobileContentCasesWithParents.add(caseFqn);
                caseFqn = metainfoService.getParentClassFqn(caseFqn);
            }
        });

        if (mobileContentCasesWithParents.contains(AbstractUserEntity.FQN))
        {
            mobileContentCasesWithParents.add(AbstractBO.FQN);
        }
        interfaceInfo.setMobileContentMetaClases(key, mobileContentCasesWithParents);

        view.getProfiles().stream().map(securityService::getProfile).filter(Objects::nonNull)
                .forEach(profile -> interfaceInfo.setProfileTitle(profile.getCode(), profile.getTitle()));

        setEnabledTagsToInterfaceInfo(view.getTags(), interfaceInfo);

        if (view instanceof CommonMobileView)
        {
            ((CommonMobileView)view).getAttributes().stream()
                    .map(attr -> prepareAttrFqn(view, attr))
                    .map(attrFqn -> getAttribute(view, attrFqn, context)).filter(
                            Objects::nonNull)
                    .map(snapshotService::prepare)
                    .forEach(interfaceInfo::addAttribute);
        }
        else if (view instanceof MobileViewBase)
        {
            ((MobileViewBase)view).getContents().stream()
                    .filter(MobilePropertiesListContent.class::isInstance)
                    .map(MobilePropertiesListContent.class::cast)
                    .map(MobilePropertiesListContent::getAttributes)
                    .flatMap(Collection::stream)
                    .map(attr -> prepareAttrFqn(view, attr))
                    .map(attrFqn -> getAttribute(view, attrFqn, context)).filter(
                            Objects::nonNull)
                    .map(snapshotService::prepare)
                    .forEach(interfaceInfo::addAttribute);
        }
        if (view instanceof ObjectCard)
        {
            interfaceInfo.getEmbeddedApplications().putAll(applicationService.getApplications().stream()
                    .collect(Collectors.toMap(EmbeddedApplication::getCode, Function.identity())));
        }
        interfaceInfo.setSilentMode(silentMode.isSilent());

        tagHelper.getDisabledAttributes(view).forEach(
                attrFqn -> context.getInterfaceInfo().setAttributeEnabled(attrFqn, false));
    }

    private void getFromEditForms(MobileSettings mobileSettings, InterfaceInfo interfaceInfo)
    {
        if (CollectionUtils.isEmpty(mobileSettings.getEditForms()))
        {
            return;
        }
        for (EditForm editForm : mobileSettings.getEditForms())
        {
            getFromAddOrEditForm(editForm, new TransformationContext(editForm.getFqnOfClass(), interfaceInfo));
        }
    }

    private void getFromObjectCard(final ObjectCard card, final TransformationContext context)
    {
        InterfaceInfo interfaceInfo = context.getInterfaceInfo();
        getFromView(card, context);

        // добавить в interfaceInfo метки с контентов
        card.getContents().forEach(content -> setEnabledTagsToInterfaceInfo(content.getTags(), interfaceInfo));
        card.getContents().stream()
                .filter(content -> content instanceof MobileListsGroupContent)
                .flatMap(content -> ((MobileListsGroupContent)content).getListContents().stream())
                .forEach(content -> setEnabledTagsToInterfaceInfo(content.getTags(), interfaceInfo));

        visibilityConditionService.setValueVisibilityCondition(card);
        visibilityConditionService.prepareConditionForClient(card);
        extractTransitions(card, interfaceInfo);

        for (final Transition transition : card.getTransitions())
        {
            extractStateTitle(card, transition.getFrom(), interfaceInfo);
            extractStateTitle(card, transition.getTo(), interfaceInfo);
        }
    }

    private void setEnabledTagsToInterfaceInfo(List<String> tags, InterfaceInfo interfaceInfo)
    {
        tags.stream().map(tagService::getTag).filter(Objects::nonNull)
                .forEach(tag ->
                {
                    interfaceInfo.setTagTitle(tag.getCode(), metainfoUtils.getLocalizedValue(tag.getTitle()));
                    interfaceInfo.setTagEnabled(tag.getCode(), tag.isEnabled());
                });
    }

    private void getFromObjectCards(MobileSettings mobileSettings, InterfaceInfo interfaceInfo)
    {
        if (CollectionUtils.isEmpty(mobileSettings.getObjectCards()))
        {
            return;
        }
        for (ObjectCard card : mobileSettings.getObjectCards())
        {
            getFromObjectCard(card, new TransformationContext(card.getFqnOfClass(), interfaceInfo));
        }
    }

    private void getFromObjectList(MobileList list, TransformationContext context)
    {
        InterfaceInfo interfaceInfo = context.getInterfaceInfo();
        getFromView(list, context);

        if (list instanceof MobileRelObjectsList)
        {
            for (AttrReference attrRef : ((MobileRelObjectsList)list).getAttrsChain())
            {
                final ClassFqn classFqn = attrRef.getClassFqn();
                if (metainfoService.isMetaclassExists(classFqn))
                {
                    final MetaClassImpl metaClass = metainfoService.getMetaClass(classFqn);
                    if (metaClass.hasAttribute(attrRef.getAttrCode()))
                    {
                        Attribute attribute = metaClass.getAttribute(attrRef.getAttrCode());
                        interfaceInfo.addAttribute(snapshotService.prepare(attribute));
                    }
                }
            }
        }
    }

    private void getFromObjectLists(MobileSettings mobileSettings, InterfaceInfo interfaceInfo)
    {
        if (CollectionUtils.isEmpty(mobileSettings.getLists()))
        {
            return;
        }
        for (MobileList list : mobileSettings.getLists())
        {
            TransformationContext context = new TransformationContext(list.getFqnOfClass(), interfaceInfo);
            list.setListFilter(prepareFilter(list.getListFilter(), context));
            list.setSortForClient(sortMapper.transform(list.getOrders(), context));
            getFromObjectList(list, new TransformationContext(list.getFqnOfClass(), interfaceInfo));
        }
    }

    /**
     * Подгатавливает фильтр для отображения в интерфейсе
     */
    private ListFilter prepareFilter(ListFilter filter, TransformationContext context)
    {
        ListFilter filterCopy = (ListFilter)filter.clone();
        advlistUtils.swapUuidToDtObject(filterCopy, false);
        filterCopy.getElements().forEach(andFilter -> andFilter.getElements().forEach(orFilter ->
        {
            if (context.getAttribute(orFilter.getAttributeFqn()) == null)
            {
                orFilter.setProperty(PropertyCode.CONDITION_CODE, ConditionCode.INCORRECT);
            }
        }));
        return filterCopy;
    }

    private InterfaceInfo getInterfaceInfo(MobileSettings mobileSettings)
    {
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        getFromObjectLists(mobileSettings, interfaceInfo);
        getFromObjectCards(mobileSettings, interfaceInfo);
        getFromAddForms(mobileSettings, interfaceInfo);
        getFromEditForms(mobileSettings, interfaceInfo);
        getFromOtherSettings(interfaceInfo);
        return interfaceInfo;
    }

    private void getFromOtherSettings(InterfaceInfo interfaceInfo)
    {
        for (MobileLoginType type : MobileLoginType.values())
        {
            interfaceInfo.setLoginTypeTitle(type, titleExtractor.getLoginTypeTitle(type));
        }
    }

    private void putStateIfPresenceInMetaClass(String stateCode, InterfaceInfo interfaceInfo, Workflow workflow)
    {
        for (State state : workflow.getStates())
        {
            if (ObjectUtils.equals(state.getCode(), stateCode))
            {
                if (ObjectUtils.equals(state.getDeclaredMetaClass(), state.getMetaClass()))
                {
                    interfaceInfo.getStateTitles().put(stateCode, state.getTitle());
                }
                else
                {
                    State declaredState = metainfoService.getMetaClass(state.getDeclaredMetaClass()).getWorkflow()
                            .getState(stateCode);
                    interfaceInfo.getStateTitles().put(stateCode, declaredState.getTitle());
                }
                return;
            }
        }
    }

    @Override
    public ListFilter transformToListFilter(MobileList list, List<IObjectFilter> filters)
    {
        InterfaceInfo interfaceInfo = createNewInterfaceInfo();
        TransformationContext context = new TransformationContext(list.getFqnOfClass(), interfaceInfo);
        return filterMapper.transformToListFilter(context, filters);
    }

    private InterfaceInfo createNewInterfaceInfo()
    {
        InterfaceInfo interfaceInfo = new InterfaceInfo();
        interfaceInfo.setMobileVersions(mobileVersionService.getSupportedVersions());
        return interfaceInfo;
    }

    private PermissionHolder getAdminPermissions(MobileSettings mobileSettings)
    {
        PermissionHolder permissionHolder = new PermissionHolder();

        mobileSettings.getLists().forEach(list ->
                Arrays.stream(PermissionType.values())
                        .forEach(permissionType ->
                                permissionHolder.setPermission(OBJECT_LIST_PREFIX + list.getUuid(),
                                        permissionType.name(), adminPermissionCheckService
                                                .hasPermission(list.getSettingsSet(), permissionType))));

        mobileSettings.getObjectCards().forEach(objectCard ->
                Arrays.stream(PermissionType.values())
                        .forEach(permissionType ->
                                permissionHolder.setPermission(OBJECT_CARD_PREFIX + objectCard.getUuid(),
                                        permissionType.name(), adminPermissionCheckService
                                                .hasPermission(objectCard.getSettingsSet(), permissionType))));

        mobileSettings.getAddForms().forEach(addForm ->
                Arrays.stream(PermissionType.values()).forEach(permissionType ->
                {
                    permissionHolder.setPermission(ADD_FORM_PREFIX + addForm.getUuid(),
                            permissionType.name(),
                            adminPermissionCheckService.hasPermission(addForm.getSettingsSet(),
                                    permissionType));

                    addForm.getChildren().forEach(form -> permissionHolder
                            .setPermission(ADD_FORM_PREFIX + form.getUuid(), permissionType.name(),
                                    adminPermissionCheckService.hasPermission(form.getSettingsSet(),
                                            permissionType)));

                }));

        mobileSettings.getEditForms().forEach(editForm ->
                Arrays.stream(PermissionType.values()).forEach(permissionType ->
                        permissionHolder.setPermission(EDIT_FORM_PREFIX + editForm.getUuid(),
                                permissionType.name(),
                                adminPermissionCheckService
                                        .hasPermission(editForm.getSettingsSet(), permissionType))));

        getAllMenuItemsChilds(mobileSettings.getNavigationSettings()).forEach(menuItem ->
                Arrays.stream(PermissionType.values())
                        .forEach(permissionType -> permissionHolder.setPermission(menuItem.getUUID(),
                                permissionType.name(),
                                adminPermissionCheckService.hasPermission(menuItem.getSettingsSet(), permissionType))));

        return permissionHolder;
    }

    private List<MobileMenuItemValue> getAllMenuItemsChilds(List<MobileMenuItemValue> list)
    {
        return list.stream().flatMap(this::getAllMenuItemsChildsStream).toList();
    }

    private Stream<MobileMenuItemValue> getAllMenuItemsChildsStream(MobileMenuItemValue item)
    {
        return Stream.concat(Stream.of(item), item.getChildren().stream().flatMap(this::getAllMenuItemsChildsStream));
    }
}
