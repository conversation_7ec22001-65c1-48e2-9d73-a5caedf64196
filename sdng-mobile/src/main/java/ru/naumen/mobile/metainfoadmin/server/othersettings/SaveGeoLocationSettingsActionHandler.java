package ru.naumen.mobile.metainfoadmin.server.othersettings;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.other.GeoHistorySettings;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.geo.history.GeoHistoryDao;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.SaveGeoLocationSettingsAction;

@Component
public class SaveGeoLocationSettingsActionHandler extends
        TransactionalActionHandler<SaveGeoLocationSettingsAction, EmptyResult>
{
    private final MobileSettingsLogService logService;
    private final GeoHistoryDao geoHistoryDao;
    private final MobileSettingsService mobileSettingsService;
    private final MetainfoModification metainfoModification;

    @Inject
    public SaveGeoLocationSettingsActionHandler(MobileSettingsLogService logService, GeoHistoryDao geoHistoryDao,
            MobileSettingsService mobileSettingsService, MetainfoModification metainfoModification)
    {
        this.logService = logService;
        this.geoHistoryDao = geoHistoryDao;
        this.mobileSettingsService = mobileSettingsService;
        this.metainfoModification = metainfoModification;
    }

    @Override
    public EmptyResult executeInTransaction(SaveGeoLocationSettingsAction action, ExecutionContext context)
            throws DispatchException
    {
        GeoHistorySettings newSettings = action.getGeoHistorySettings() == null ? new GeoHistorySettings()
                : action.getGeoHistorySettings();
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        GeoHistorySettings oldSettings = mobileSettings.getGeoHistorySettings();
        mobileSettings.setGeoHistorySettings(newSettings);
        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.importSettings(mobileSettings))
        {
            logService.logEditGeoLocationSettings(newSettings, oldSettings);
        }
        if (newSettings.getMaxVolume() < oldSettings.getMaxVolume()
            || newSettings.getMaxVolume() < geoHistoryDao.countAll())
        {
            geoHistoryDao.clearOldValuesByLimit();
        }
        return new EmptyResult();
    }
}
