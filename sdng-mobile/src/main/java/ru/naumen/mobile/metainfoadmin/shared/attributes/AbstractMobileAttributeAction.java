package ru.naumen.mobile.metainfoadmin.shared.attributes;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentType;

/**
 * Абстрактный action редактирования/добавления мобильного атрибута
 *
 * <AUTHOR>
 * @since 12 мая 2015 г.
 */
public abstract class AbstractMobileAttributeAction<T extends Result> implements Action<T>
{
    private MobileAttribute mobileAttribute;
    private MobileContentType contentType;
    private String contentUuid;

    public AbstractMobileAttributeAction()
    {
    }

    public AbstractMobileAttributeAction(MobileContentType contentType, String contentUuid,
            MobileAttribute mobileAttribute)
    {
        this.mobileAttribute = mobileAttribute;
        this.contentType = contentType;
        this.contentUuid = contentUuid;
    }

    public MobileContentType getContentType()
    {
        return contentType;
    }

    public String getContentUuid()
    {
        return contentUuid;
    }

    public MobileAttribute getMobileAttribute()
    {
        return mobileAttribute;
    }
}
