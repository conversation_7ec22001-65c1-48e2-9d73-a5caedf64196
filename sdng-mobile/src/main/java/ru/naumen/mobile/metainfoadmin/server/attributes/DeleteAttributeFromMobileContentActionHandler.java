package ru.naumen.mobile.metainfoadmin.server.attributes;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.Iterator;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentHelper;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.attributes.DeleteAttributeFromMobileContentAction;

/**
 * Удаление мобильного атрибута из карточки, списка либо формы редактирования объекта
 *
 * <AUTHOR>
 * @since 25 мая 2015 г.
 */
@Component
public class DeleteAttributeFromMobileContentActionHandler extends
        TransactionalActionHandler<DeleteAttributeFromMobileContentAction, EmptyResult>
{
    protected final MobileSettingsService mobileSettingsService;
    protected final MobileSettingsLogService mobileSettingsLogService;
    private final MobileContentHelper mobileContentHelper;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public DeleteAttributeFromMobileContentActionHandler(MobileSettingsService mobileSettingsService,
            MobileSettingsLogService mobileSettingsLogService,
            MobileContentHelper mobileContentHelper,
            MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.mobileContentHelper = mobileContentHelper;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public EmptyResult executeInTransaction(DeleteAttributeFromMobileContentAction action, ExecutionContext context)
            throws DispatchException
    {
        AbstractMobileView content = mobileContentHelper.getContent(action.getContentType(), action.getContentUuid());
        adminPermissionCheckService.checkPermission(content, EDIT);

        MapProperties oldProperties = mobileSettingsLogService.getMobileContentLogInfo(content);
        List<MobileAttribute> attributes = ((CommonMobileView)content).getAttributes();
        for (Iterator<MobileAttribute> iterator = attributes.iterator(); iterator.hasNext(); )
        {
            MobileAttribute attr = iterator.next();
            if (ObjectUtils.equals(attr.getUuid(), action.getAttrUuid()))
            {
                iterator.remove();
                saveContent((CommonMobileView)content, oldProperties);
                return new EmptyResult();
            }
        }
        throw new IllegalStateException("No attribute found");
    }

    protected void saveContent(CommonMobileView content, MapProperties oldProperties)
    {
        metainfoModification.modify(MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.saveContent(content))
        {
            mobileSettingsLogService.logEditView(content, oldProperties, "");
        }
    }
}
