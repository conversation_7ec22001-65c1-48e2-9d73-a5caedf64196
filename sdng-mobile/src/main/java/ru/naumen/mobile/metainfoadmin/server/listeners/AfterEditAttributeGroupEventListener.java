package ru.naumen.mobile.metainfoadmin.server.listeners;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MobilePropertyListUsageAttrGroup;
import ru.naumen.core.server.common.attribute.group.AttributeGroupService;
import ru.naumen.metainfo.server.AfterEditAttributeGroupEvent;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.metainfoadmin.server.cards.EditMobilePropertiesListHelper;

/**
 * Обработчик события {@link AfterEditAttributeGroupEvent}, изменяющий состав контентов типа "Параметры объекта" на
 * карточках мобильного приложения
 * <AUTHOR>
 * @since 18 мар. 2019 г.
 */
@Component
public class AfterEditAttributeGroupEventListener implements ApplicationListener<AfterEditAttributeGroupEvent>
{

    private final AttributeGroupService attributeGroupService;
    private final MetainfoService metainfoService;
    private final EditMobilePropertiesListHelper editMobilePropertiesListHelper;

    @Inject
    public AfterEditAttributeGroupEventListener(
            AttributeGroupService attributeGroupService,
            MetainfoService metainfoService,
            EditMobilePropertiesListHelper editMobilePropertiesListHelper)
    {
        this.attributeGroupService = attributeGroupService;
        this.metainfoService = metainfoService;
        this.editMobilePropertiesListHelper = editMobilePropertiesListHelper;
    }

    @Override
    public void onApplicationEvent(AfterEditAttributeGroupEvent event)
    {
        ClassFqn classFqn = event.getFqn();
        MetaClass metaClass = metainfoService.getMetaClass(classFqn);
        for (String group : event.getAttributeGroupCodes())
        {
            attributeGroupService.getAttrGroupUsages(metaClass.getAttributeGroup(group)).stream()
                    .filter(MobilePropertyListUsageAttrGroup.class::isInstance)
                    .map(MobilePropertyListUsageAttrGroup.class::cast)
                    .filter(getFilterUsages(classFqn, group))
                    .forEach(this::processUsage);
        }
    }

    private Predicate<? super MobilePropertyListUsageAttrGroup> getFilterUsages(ClassFqn classFqn, String attrGroupCode)
    {
        return usage ->
        {
            boolean hasGroup = getGeneralMetaClass(usage.getMobileView()).getAttributeGroupCodes().contains(
                    attrGroupCode);
            return hasGroup && (usage.getClazz() != null && classFqn.isSameClass(usage.getClazz().getFqn()) ||
                                usage.getClassFqns().stream().anyMatch(classFqn::isSameClass));
        };
    }

    private MetaClass getGeneralMetaClass(MobileViewBase mobileView)
    {
        List<MetaClass> metaclasses = editMobilePropertiesListHelper.getCardClasses(mobileView);
        ClassFqn generalParent = MetainfoUtilities.getGeneralParent(metaclasses);
        return metainfoService.getMetaClass(generalParent);
    }

    private void processUsage(MobilePropertyListUsageAttrGroup usage)
    {
        MobileViewBase mobileView = usage.getMobileView();
        MobilePropertiesListContent mobilePropertiesListContent = usage.getMobilePropertiesListContent();
        List<Attribute> commonAttributes = editMobilePropertiesListHelper.getAttributes(mobileView,
                mobilePropertiesListContent);
        Map<AttributeFqn, MobileAttribute> mobileAttributesByFqn = EditMobilePropertiesListHelper
                .getMapMobileAttributesByFqn(mobilePropertiesListContent);
        List<MobileAttribute> mobileAttributesToSet = EditMobilePropertiesListHelper.getMobileAttributes(
                commonAttributes, mobileAttributesByFqn);
        mobilePropertiesListContent.setAttributes(mobileAttributesToSet);
        editMobilePropertiesListHelper.saveMobileView(mobileView, mobilePropertiesListContent);
    }
}
