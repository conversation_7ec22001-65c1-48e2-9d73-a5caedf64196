package ru.naumen.mobile.controllers.push;

import static ru.naumen.mobile.controllers.push.contexts.NotificationTransport.*;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.core.server.mobile.MobileUserAgentService;
import ru.naumen.core.server.rest.logging.MobileRestLogStatistics;
import ru.naumen.metainfo.shared.mobile.other.MobileOperationSystem;
import ru.naumen.mobile.VersionConstants.Path;
import ru.naumen.mobile.aspects.MobileRestAuthAspect.AuthAndTransaction;
import ru.naumen.mobile.aspects.MobileRestCommonAspect;
import ru.naumen.mobile.controllers.MobileRestRequestMapping;
import ru.naumen.mobile.controllers.MobileRestServiceController;
import ru.naumen.mobile.controllers.push.contexts.MobileRegisterPushContext;
import ru.naumen.mobile.controllers.push.contexts.MobileRegistrationContext;
import ru.naumen.mobile.controllers.push.contexts.NotificationTransport;
import ru.naumen.mobile.metainfoadmin.server.push.tokens.PushProvider;
import ru.naumen.mobile.metainfoadmin.server.push.tokens.PushToken;
import ru.naumen.mobile.services.auth.MobilePushService;
import ru.naumen.sec.server.jwt.mobile.utils.JwtTokenExtractor;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.employee.EmployeeUser;

/**
 * API мобильного клиента для работы с входом/выходом из системы
 * <p>
 * Ошибки для передачи на сторону МК обрабатываются через {@link MobileRestCommonAspect}
 * Все вызовы REST логируются при помощи аннотации {@link MobileRestLogStatistics}
 *
 * <AUTHOR>
 * @since 05.02.2025
 */
@Lazy
@MobileRestRequestMapping
@MobileRestServiceController
public class MobileRestPushController
{
    private static final String TOKEN_FIELD = "token";

    private final MobilePushService pushService;
    private final MobileUserAgentService userAgentService;
    private final JwtTokenExtractor jwtTokenExtractor;

    @Inject
    public MobileRestPushController(MobilePushService pushService, MobileUserAgentService userAgentService,
            JwtTokenExtractor jwtTokenExtractor)
    {
        this.pushService = pushService;
        this.userAgentService = userAgentService;
        this.jwtTokenExtractor = jwtTokenExtractor;
    }

    /**
     * Связывает FCM-токен устройства с текущим пользователем.
     * Необходимо для доставки push-уведомлений на устройство. Не сохраняет связь push-токена и JWT access-токена.
     *
     * @param context тело запроса с FCM-токеном устройства
     */
    @AuthAndTransaction
    @MobileRestLogStatistics
    @PutMapping(path = Path.BEFORE_V16 + "/authentication/push", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> registerPush(final @RequestBody MobileRegistrationContext context,
            final HttpServletRequest request)
    {
        final String registrationId = context.getRegistrationId();
        if (StringUtils.isNotEmpty(registrationId))
        {
            final PushToken pushToken = new PushToken(context.getRegistrationId(), PushProvider.FCM);
            final EmployeeUser employeeUser = CurrentEmployeeContext.getEmployeeUserPrincipal();
            pushService.savePushTokens(List.of(pushToken), jwtTokenExtractor.extractToken(request), employeeUser,
                    context.getNotificationTransport());
        }

        return ResponseEntity.noContent().build();
    }

    /**
     * Связывает push-токены устройства с текущим пользователем.
     * Необходимо для доставки push-уведомлений на устройство.
     *
     * @param context тело запроса с push-токенами устройства
     * @param request объект запроса
     */
    @AuthAndTransaction
    @MobileRestLogStatistics
    @PutMapping(path = Path.FROM_V16 + "/push/register", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> registerPushes(final @RequestBody MobileRegisterPushContext context,
            final HttpServletRequest request)
    {
        final Map<String, Map<String, String>> providers = context.getProviders();
        if (providers.isEmpty())
        {
            return ResponseEntity.noContent().build();
        }
        final List<PushToken> pushTokens = providers.entrySet().stream()
                .map(entry -> new PushToken(entry.getValue().get(TOKEN_FIELD), PushProvider.of(entry.getKey())))
                .toList();
        if (!pushTokens.isEmpty())
        {
            final EmployeeUser employeeUser = CurrentEmployeeContext.getEmployeeUserPrincipal();
            pushService.savePushTokens(pushTokens, jwtTokenExtractor.extractToken(request), employeeUser,
                    determineNotificationTransport(request));
        }

        return ResponseEntity.noContent().build();
    }

    /**
     * Определить, каким образом push-уведомления будут доставляться на устройство.
     * Для Android устройств всегда используется data push.
     * На iOS используется notification push.
     *
     * @param request объект запроса
     * @return data push для Android устройств, notification push для всех остальных
     */
    private NotificationTransport determineNotificationTransport(HttpServletRequest request)
    {
        String userAgent = request.getHeader(HttpHeaders.USER_AGENT);
        MobileOperationSystem operationSystem = userAgentService.getMobileOperationSystem(userAgent);

        return MobileOperationSystem.ANDROID.equals(operationSystem) ? DATA_PUSH : NOTIFICATION_PUSH;
    }
}
