package ru.naumen.mobile.mapping.dto.form.dtos;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.services.configuration.MobileAvailableFeaturesService;

/**
 * Фабрика для создания DTO для передачи атрибута на форме на сторону мобильного клиента.
 *
 * <AUTHOR>
 * @since 18.05.2019
 */
@Component
public class FormAttributeDtObjectFactory
{
    private final MobileAvailableFeaturesService availableFeaturesService;

    @Inject
    public FormAttributeDtObjectFactory(final MobileAvailableFeaturesService availableFeaturesService)
    {
        this.availableFeaturesService = availableFeaturesService;
    }

    /**
     * Создать DTO для передачи атрибута на форме на сторону мобильного клиента.
     *
     * @param attribute атрибут
     * @param attrValue значение атрибута
     * @param isRequired обязательность атрибута
     * @param onlyVisible необходимо ли сделать атрибут не редактируемым на форме?
     * @param isHidden необходимо ли скрыть атрибут на форме?
     */
    public FormAttributeDtObject getAttributeDtObject(final Attribute attribute,
            final @Nullable AttrValueDtObject attrValue, final boolean isRequired, final boolean onlyVisible,
            final boolean isHidden)
    {
        final String title = processTitle(attribute.getTitle());
        return new FormAttributeDtObject(attribute, attribute.getCode(), title,
                attribute.getType().getCode(), attrValue, isRequired, onlyVisible, isHidden);
    }

    /**
     * Создать DTO для передачи атрибута на форме на сторону мобильного клиента.
     *
     * @param attribute атрибут
     * @param isRequired обязательность атрибута
     * @param onlyVisible необходимо ли сделать атрибут не редактируемым на форме?
     * @param isHidden необходимо ли скрыть атрибут на форме?
     */
    public FormAttributeDtObject getAttributeDtObject(final Attribute attribute, final boolean isRequired,
            final boolean onlyVisible, final boolean isHidden)
    {
        return getAttributeDtObject(attribute, null, isRequired, onlyVisible, isHidden);
    }

    /**
     * Обрезать название атрибута, если стоит соответствующая настройка
     */
    private String processTitle(final String title)
    {
        return availableFeaturesService.isTrimAttributeTitle() ? title.trim() : title;
    }
}
