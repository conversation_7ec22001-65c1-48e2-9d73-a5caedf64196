<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>ru.naumen</groupId>
        <artifactId>sd-parent</artifactId>
        <version>4.21.0-SNAPSHOT</version>
        <relativePath>../sdng-parent/pom.xml</relativePath>
    </parent>

    <artifactId>pentaho</artifactId>
    <name>pentaho</name>
    <description>Pentaho Report Module</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Версии библиотек для отчетов -->
        <pentaho-reporting-engine.version>3.9.1-GA</pentaho-reporting-engine.version>
        <pentaho-libraries.version>1.2.8</pentaho-libraries.version>
        <!-- Префикс уставлен спереди, потому что нужно избавиться от ложного срабатывания cve -->
        <jfreechart.version>naumen-1.0.19</jfreechart.version>
        <bsf.version>2.4.0</bsf.version>
        <bsh.version>2.0b6</bsh.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>ru.naumen</groupId>
            <artifactId>report-spec</artifactId>
        </dependency>
        <dependency>
            <groupId>ru.naumen</groupId>
            <artifactId>sdng</artifactId>
        </dependency>

        <!-- Библиотеки для отчетов -->
        <dependency>
            <groupId>pentaho-reporting-engine</groupId>
            <artifactId>pentaho-reporting-engine-classic-core</artifactId>
            <version>${pentaho-reporting-engine.version}-naumen</version>
        </dependency>
        <dependency>
            <groupId>pentaho-reporting-engine</groupId>
            <artifactId>pentaho-metadata</artifactId>
            <version>${pentaho-reporting-engine.version}-naumen</version>
        </dependency>
        <dependency>
            <groupId>pentaho-reporting-engine</groupId>
            <artifactId>pentaho-reporting-engine-classic-extensions</artifactId>
            <version>${pentaho-reporting-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-reporting-engine</groupId>
            <artifactId>pentaho-reporting-engine-wizard-core</artifactId>
            <version>${pentaho-reporting-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-reporting-engine</groupId>
            <artifactId>pentaho-reporting-engine-legacy-charts</artifactId>
            <version>${pentaho-reporting-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libbase</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libfonts</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libloader</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libxml</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>librepository</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libformat</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libformula</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libserializer</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>pentaho-library</groupId>
            <artifactId>libdocbundle</artifactId>
            <version>${pentaho-libraries.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext-rtf</artifactId>
            <version>${itext.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcmail-jdk14</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk14</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bctsp-jdk14</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>bsf</groupId>
            <artifactId>bsf</artifactId>
            <version>${bsf.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--библиотека используется в отчетах с диаграммой, где язык скрипта = beanshell-->
        <dependency>
            <groupId>org.apache-extras.beanshell</groupId>
            <artifactId>bsh</artifactId>
            <version>${bsh.version}</version>
        </dependency>
        <!--Зависимость, которая используется в шаблонах отчетов, для расширения функциональности Pentaho-->
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>${jfreechart.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>xml-apis</groupId>
                    <artifactId>xml-apis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>ru.naumen</groupId>
            <artifactId>commons-util-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ru.naumen</groupId>
            <artifactId>sdng</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.websocket</groupId>
            <artifactId>jakarta.websocket-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.websocket</groupId>
            <artifactId>jakarta.websocket-client-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
