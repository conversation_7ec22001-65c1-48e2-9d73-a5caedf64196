package org.codehaus.groovy.bsf;

import java.util.HashMap;
import java.util.Map;
import java.util.Vector;
import java.util.logging.Logger;

import org.apache.bsf.BSFDeclaredBean;
import org.apache.bsf.BSFException;
import org.apache.bsf.BSFManager;
import org.apache.bsf.util.BSFFunctions;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.codehaus.groovy.runtime.InvokerHelper;

import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyShell;
import groovy.lang.Script;

/**
 * A Caching implementation of the GroovyEngine
 */
public class CachingGroovyEngine extends GroovyEngine
{
    private static final Logger LOG = Logger.getLogger(CachingGroovyEngine.class.getName());
    private static final Object[] EMPTY_ARGS = new Object[] { new String[] {} };

    private Map<Object, Class<?>> evalScripts;
    private Map<Object, Class<?>> execScripts;
    private Binding context;
    private GroovyClassLoader loader;

    /**
     * Evaluate an expression.
     */
    @Override
    public Object eval(String source, int lineNo, int columnNo, Object script) throws BSFException
    {
        try
        {
            Class<?> scriptClass = evalScripts.get(script);
            if (scriptClass == null)
            {
                scriptClass = loader.parseClass(script.toString(), source);
                evalScripts.put(script, scriptClass);
            }
            else
            {
                LOG.fine(() -> "eval() - Using cached script for source: " + source);
            }
            //can't cache the script because the context may be different.
            //but don't bother loading parsing the class again
            Script s = InvokerHelper.createScript(scriptClass, context);
            return s.run();
        }
        catch (Exception e)
        {
            throw new BSFException(BSFException.REASON_EXECUTION_ERROR, "exception from Groovy: " + e, e);
        }
    }

    /**
     * Execute a script.
     */
    @Override
    public void exec(String source, int lineNo, int columnNo, Object script) throws BSFException
    {
        try
        {
            Class<?> scriptClass = execScripts.get(script);
            if (scriptClass == null)
            {
                scriptClass = loader.parseClass(script.toString(), source);
                execScripts.put(script, scriptClass);
            }
            else
            {
                Class<?> finalScriptClass = scriptClass;
                LOG.fine(() -> "exec() - Using cached version of class: " + finalScriptClass);
            }
            InvokerHelper.invokeMethod(scriptClass, "main", EMPTY_ARGS);
        }
        catch (Exception e)
        {
            throw new BSFException(BSFException.REASON_EXECUTION_ERROR, "exception from Groovy: " + e.getMessage(), e);
        }
    }

    /**
     * Initialize the engine.
     */
    @Override
    public void initialize(final BSFManager mgr, String lang, Vector declaredBeans) throws BSFException //NOPMD
    {
        super.initialize(mgr, lang, declaredBeans);
        ClassLoader parent = mgr.getClassLoader();
        if (parent == null)
        {
            parent = GroovyShell.class.getClassLoader();
        }
        setLoader(mgr, parent);
        execScripts = new HashMap<>();
        evalScripts = new HashMap<>();
        context = shell.getContext();
        // create a shell
        // register the mgr with object name "bsf"
        context.setVariable("bsf", new BSFFunctions(mgr, this));
        for (int i = 0; i < declaredBeans.size(); i++)
        {
            declareBean((BSFDeclaredBean)declaredBeans.elementAt(i));
        }
    }

    @SuppressWarnings("java:S5738")
    private void setLoader(final BSFManager mgr, final ClassLoader finalParent)
    {
        CompilerConfiguration configuration = new CompilerConfiguration();
        configuration.setClasspath(mgr.getClassPath());
        this.loader = new GroovyClassLoader(finalParent, configuration);
    }
}
