package ru.naumen.reports.engine.classic.core.modules.output.pageable.base;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.pentaho.reporting.engine.classic.core.ReportDefinition;
import org.pentaho.reporting.engine.classic.core.layout.model.LogicalPageBox;
import org.pentaho.reporting.engine.classic.core.layout.model.PageBreakPositionList;
import org.pentaho.reporting.engine.classic.core.layout.output.ContentProcessingException;
import org.pentaho.reporting.engine.classic.core.layout.output.LayoutPagebreakHandler;
import org.pentaho.reporting.engine.classic.core.layout.output.OutputProcessor;
import org.pentaho.reporting.engine.classic.core.layout.process.ApplyPageShiftValuesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.CleanPaginatedBoxesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.FillPhysicalPagesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.PaginationResult;
import org.pentaho.reporting.engine.classic.core.layout.process.PaginationStep;
import org.pentaho.reporting.engine.classic.core.util.InstanceID;

import ru.naumen.reports.engine.classic.core.layout.AbstractRenderer;

/**
 * Creation-Date: 08.04.2007, 15:08:48
 *
 * <AUTHOR> Morgner
 */
public class PageableRenderer extends AbstractRenderer
{
    private static final Log logger = LogFactory.getLog(PageableRenderer.class);

    private PaginationStep paginationStep;
    private FillPhysicalPagesStep fillPhysicalPagesStep;
    private CleanPaginatedBoxesStep cleanPaginatedBoxesStep;
    private ApplyPageShiftValuesStep applyPageShiftValuesStep;
    private int pageCount;
    private long lastPageAge;
    private boolean pageStartPending;

    public PageableRenderer(final OutputProcessor outputProcessor)
    {
        super(outputProcessor);
        this.paginationStep = new PaginationStep();
        this.fillPhysicalPagesStep = new FillPhysicalPagesStep();
        this.cleanPaginatedBoxesStep = new CleanPaginatedBoxesStep();
        this.applyPageShiftValuesStep = new ApplyPageShiftValuesStep();
        this.lastPageAge = System.currentTimeMillis();
    }

    @Override
    public boolean clearPendingPageStart(final LayoutPagebreakHandler layoutPagebreakHandler)
    {
        if (!pageStartPending)
        {
            return false;
        }

        if (layoutPagebreakHandler != null)
        {
            layoutPagebreakHandler.pageStarted();
        }
        pageStartPending = false;
        return true;
    }

    @Override
    public int getPageCount()
    {
        return pageCount;
    }

    @Override
    public boolean isCurrentPageEmpty()
    {
        // todo: Invent a test that checks whether the page is currently empty.
        final LogicalPageBox logicalPageBox = getPageBox();
        if (logicalPageBox == null)
        {
            throw new IllegalStateException("LogicalPageBox being null? You messed it up again!");
        }

        final PageBreakPositionList breakPositionList = logicalPageBox.getAllVerticalBreaks();
        final long masterBreak = breakPositionList.getLastMasterBreak();
        final boolean nextPageContainsContent = (logicalPageBox.getHeight() > masterBreak);
        return !nextPageContainsContent;
    }

    @Override
    public boolean isPageStartPending()
    {
        return pageStartPending;
    }

    @Override
    public boolean isPendingPageHack()
    {
        return true;
    }

    @Override
    public void startReport(final ReportDefinition report)
    {
        final long prePageAge = System.currentTimeMillis();
        PageableRenderer.logger.debug("Time to pagination " + (prePageAge - lastPageAge));
        this.lastPageAge = prePageAge;

        super.startReport(report);
        pageCount = 0;
    }

    @Override
    protected void debugPrint(final LogicalPageBox pageBox)
    {
    }

    @Override
    protected boolean isPageFinished()
    {
        final LogicalPageBox pageBox = getPageBox();
        //    final long sizeBeforePagination = pageBox.getHeight();
        //    final LogicalPageBox clone = (LogicalPageBox) pageBox.derive(true);
        final PaginationResult pageBreak = paginationStep.performPagebreak(pageBox);
        if (pageBreak.isOverflow() || !pageBox.isOpen())
        {
            setLastStateKey(pageBreak.getLastVisibleState());
            return true;
        }
        return false;
    }

    @Override
    protected boolean performPagination(final LayoutPagebreakHandler layoutPagebreakHandler,
            final boolean performOutput) throws ContentProcessingException
    {
        // next: perform pagination.
        final LogicalPageBox pageBox = getPageBox();

        //    final long sizeBeforePagination = pageBox.getHeight();
        //    final LogicalPageBox clone = (LogicalPageBox) pageBox.derive(true);
        final PaginationResult pageBreak = paginationStep.performPagebreak(pageBox);
        if (pageBreak.isOverflow() || !pageBox.isOpen())
        {
            //      final long sizeAfterPagination = pageBox.getHeight();
            setLastStateKey(pageBreak.getLastVisibleState());
            setPagebreaks(getPagebreaks() + 1);
            pageBox.setAllVerticalBreaks(pageBreak.getAllBreaks());

            pageCount += 1;
            //      DebugLog.log("1: **** Start Printing Page: " + pageCount);
            debugPrint(pageBox);

            // A new page has been started. Recover the page-grid, then restart
            // everything from scratch. (We have to recompute, as the pages may
            // be different now, due to changed margins or page definitions)
            final OutputProcessor outputProcessor = getOutputProcessor();
            final long nextOffset = pageBreak.getLastPosition();
            final long pageOffset = pageBox.getPageOffset();

            if (performOutput)
            {
                if (outputProcessor.isNeedAlignedPage())
                {
                    final LogicalPageBox box = fillPhysicalPagesStep.compute(pageBox, pageOffset, nextOffset);
                    outputProcessor.processContent(box);
                    // DebugLog.log("Processing contents for Page " + pageCount + " Page-Offset: " + pageOffset + "
                    // -> " + nextOffset);
                }
                else
                {
                    // DebugLog.log("Processing fast contents for Page " + pageCount + " Page-Offset: " + pageOffset
                    // + " -> " + nextOffset);
                    outputProcessor.processContent(pageBox);
                }
            }
            else
            {
                // todo: When recomputing the contents, we have to update the page cursor or the whole exercise is
                //  next to useless ..
                // DebugLog.log("Recomputing contents for Page " + pageCount + " Page-Offset: " + pageOffset + " -> "
                // + nextOffset);
                outputProcessor.processRecomputedContent(pageBox);
            }

            // Now fire the pagebreak. This goes through all layers and informs all
            // components, that a pagebreak has been encountered and possibly a
            // new page has been set. It does not save the state or perform other
            // expensive operations. However, it updates the 'isPagebreakEncountered'
            // flag, which will be active until the input-feed received a new event.
            //      Log.debug ("PageTime " + (currentPageAge - lastPageAge));
            lastPageAge = System.currentTimeMillis();

            final boolean repeat = pageBox.isOpen() || (pageBox.getHeight() > nextOffset);
            if (repeat)
            {
                pageBox.setPageOffset(nextOffset);

                final long shift = cleanPaginatedBoxesStep.compute(pageBox);
                if (shift > 0)
                {
                    final InstanceID shiftNode = cleanPaginatedBoxesStep.getShiftNode();
                    applyPageShiftValuesStep.compute(pageBox, shift, shiftNode);
                }

                if (pageBreak.isNextPageContainsContent())
                {
                    if (layoutPagebreakHandler != null)
                    {
                        layoutPagebreakHandler.pageStarted();
                    }
                    return true;
                }
                // No need to try again, we know that the result will not change, as the next page is
                // empty. (We already tested it.)
                pageStartPending = true;
                return false;
            }
            else
            {
                pageBox.setPageOffset(nextOffset);
                outputProcessor.processingFinished();
                return false;
            }
        }
        return false;
    }
}
