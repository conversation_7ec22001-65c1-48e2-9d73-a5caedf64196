package ru.naumen.reports.engine.classic.core.modules.misc.datafactory.sql;

import java.util.Map;

import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableModel;

import org.pentaho.reporting.engine.classic.core.DataRow;
import org.pentaho.reporting.engine.classic.core.ReportDataFactoryException;
import org.pentaho.reporting.engine.classic.core.modules.misc.datafactory.sql.ConnectionProvider;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.reports.server.ReportsConfiguration;
import ru.naumen.reports.server.objects.ReportTableModel;
import ru.naumen.reports.server.objects.ReportTableModelAdapter;
import ru.naumen.reports.server.objects.StreamProcessor;
import ru.naumen.reports.server.objects.TableGenerationMode;
import ru.naumen.reports.server.script.AbstractTableModelWrapper;
import ru.naumen.reports.server.script.DefaultTableModelWrapper;
import ru.naumen.reports.server.script.ReportScriptUtils;
import ru.naumen.reports.shared.Constants;

/**
 * <AUTHOR>
 * @since 01.10.2012
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SQLReportDataFactoryImpl extends SQLReportDataFactory
{
    private static final String INIT_QUERY = "initQuery";

    //Экспериментально проверенная константа, ограничивающая потребление памяти запросом БД при построении отчета
    private static final int FETCH_SIZE = 1000;

    @Inject
    private ScriptService scriptService;
    @Inject
    @Named(ReportsConfiguration.CONNECTION_PROVIDER)
    private ConnectionProvider connectionProvider;
    @Inject
    private StreamProcessor streamProcessor;

    @Nullable
    private Script script;

    @Nullable
    private CoreEmployee currentUser;
    private ReportTableModel tableModel;

    @Inject
    public SQLReportDataFactoryImpl(
            @Named(ReportsConfiguration.CONNECTION_PROVIDER) ConnectionProvider connectionProvider)
    {
        super(connectionProvider);
    }

    @Override
    public Object clone() // NOSONAR переопределенный библиотечный класс
    {
        final SQLReportDataFactoryImpl dataFactory = (SQLReportDataFactoryImpl)super.clone();
        dataFactory.setConnectionProvider(connectionProvider);
        dataFactory.tableModel = tableModel;
        dataFactory.script = script;
        dataFactory.setTableGenerationMode(getTableGenerationMode());
        return dataFactory;
    }

    @Override
    public int getQueryFetchSize()
    {
        return FETCH_SIZE;
    }

    public void initQuery()
    {
        if (script != null && scriptService.isScriptHasMethod(script, INIT_QUERY))
        {
            scriptService.executeFunction(script, INIT_QUERY);
        }
    }

    @Override
    public ReportTableModel queryData(String query, DataRow parameters) throws ReportDataFactoryException
    {
        if (tableModel != null)
        {
            return tableModel;
        }

        initQuery();
        String realQuery = getQuery(query);
        if (null != parameters.get(Constants.ReportParameters.SUBJECT_TABLE_PARAM))
        {
            realQuery = realQuery.replace(
                    "${" + Constants.ReportParameters.SUBJECT_TABLE_PARAM + "}",
                    (String)parameters.get(Constants.ReportParameters.SUBJECT_TABLE_PARAM));
        }
        setQuery(query, realQuery, null, null);

        //@formatter:off
        TableModel tableModel = super.queryData(query, parameters);
        if (getTableGenerationMode() != TableGenerationMode.IN_MEMORY)
        {
            //Потоковые реализации уже возвращают ReportTableModel
            return (ReportTableModel)tableModel;
        }
        //Не потоковая реализация возвращает CloseableDefaultTableModel
        //Можно просто оборачивать в DefaultTableWrapper
        if (!(tableModel instanceof DefaultTableModel))
        {
            throw new IllegalStateException("Expected to get DefaultTableModel at this point");
        }

        AbstractTableModelWrapper table = new DefaultTableModelWrapper((DefaultTableModel)tableModel);
        if (script != null && !StringUtilities.isEmptyTrim(script.getBody()))
        {
            Map<String, Object> bindings = ReportScriptUtils.prepareBindings(parameters);
            bindings.put(Constants.SQLReportDataFactoryImpl.TABLE_PARAM, table);
            bindings.put(ScriptService.Constants.ACTION_USER, currentUser);

            logger.debug("Start scripting report...");
            Object scriptTable = scriptService.execute(script, bindings);
            table = scriptTable instanceof AbstractTableModelWrapper ? (AbstractTableModelWrapper)scriptTable : table;
        }
        //@formatter:on

        return ReportTableModelAdapter.adapt(table);
    }

    public SQLReportDataFactoryImpl setScript(@Nullable Script script)
    {
        this.script = script;
        return this;
    }

    public SQLReportDataFactoryImpl setTableModel(ReportTableModel tableModel)
    {
        this.tableModel = tableModel;
        return this;
    }

    public SQLReportDataFactoryImpl setUserUUID(@Nullable CoreEmployee currentUser)
    {
        this.currentUser = currentUser;
        return this;
    }

    /**
     * Чтобы работал fetch-size в postgresql нужно false
     */
    @Override
    protected boolean getAutoCommit()
    {
        return false;
    }

    @Override
    protected StreamProcessor getStreamProcessor(DataRow parameters)
    {
        return streamProcessor.configure(currentUser, script, isLabelMapping(), parameters, getTableGenerationMode());
    }

    /**
     * Чтобы работал fetch-size в postgresql нужно true
     */
    @Override
    protected boolean isResultSetForwardOnly()
    {
        return true;
    }

    @Override
    protected boolean useDefaultTableModel()
    {
        return getTableGenerationMode() == TableGenerationMode.IN_MEMORY;
    }
}
