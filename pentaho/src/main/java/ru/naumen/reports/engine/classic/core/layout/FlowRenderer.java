package ru.naumen.reports.engine.classic.core.layout;

import org.pentaho.reporting.engine.classic.core.ReportDefinition;
import org.pentaho.reporting.engine.classic.core.layout.model.LogicalPageBox;
import org.pentaho.reporting.engine.classic.core.layout.model.PageBreakPositionList;
import org.pentaho.reporting.engine.classic.core.layout.output.ContentProcessingException;
import org.pentaho.reporting.engine.classic.core.layout.output.IterativeOutputProcessor;
import org.pentaho.reporting.engine.classic.core.layout.output.LayoutPagebreakHandler;
import org.pentaho.reporting.engine.classic.core.layout.output.OutputProcessor;
import org.pentaho.reporting.engine.classic.core.layout.output.OutputProcessorFeature;
import org.pentaho.reporting.engine.classic.core.layout.process.ApplyAutoCommitPageHeaderStep;
import org.pentaho.reporting.engine.classic.core.layout.process.ApplyPageShiftValuesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.CleanFlowBoxesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.CleanPaginatedBoxesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.FillFlowPagesStep;
import org.pentaho.reporting.engine.classic.core.layout.process.FlowPaginationStep;
import org.pentaho.reporting.engine.classic.core.layout.process.PaginationResult;
import org.pentaho.reporting.engine.classic.core.util.InstanceID;

/**
 *
 * Копипаст. 
 * Для возможности перенести AbstractRenderer
 *
 * A flow renderer is a light-weight paginating renderer. It does not care about the page-size but searches for manual
 * breaks. Once a manual break is encountered, the flow shifts and creates a page-event. (This is the behavior of the
 * old table-exporters.)
 * <p/>
 * This implementation is a mix of a paginated and streaming renderer.
 *
 * <AUTHOR> Morgner
 */
public class FlowRenderer extends AbstractRenderer
{
    private FlowPaginationStep paginationStep;
    private FillFlowPagesStep fillPhysicalPagesStep;
    private CleanPaginatedBoxesStep cleanPaginatedBoxesStep;
    private CleanFlowBoxesStep cleanFlowBoxesStep;
    private ApplyPageShiftValuesStep applyPageShiftValuesStep;
    private ApplyAutoCommitPageHeaderStep applyAutoCommitPageHeaderStep;
    private int flowCount;

    private boolean pageStartPending;
    private int floodPrevention;

    public FlowRenderer(final OutputProcessor outputProcessor)
    {
        super(outputProcessor);
        this.paginationStep = new FlowPaginationStep();
        this.fillPhysicalPagesStep = new FillFlowPagesStep();
        this.cleanPaginatedBoxesStep = new CleanPaginatedBoxesStep();
        this.cleanFlowBoxesStep = new CleanFlowBoxesStep();
        this.applyPageShiftValuesStep = new ApplyPageShiftValuesStep();
        this.applyAutoCommitPageHeaderStep = new ApplyAutoCommitPageHeaderStep();
    }

    @Override
    public boolean clearPendingPageStart(final LayoutPagebreakHandler layoutPagebreakHandler)
    {
        if (!pageStartPending)
        {
            return false;
        }

        if (layoutPagebreakHandler != null)
        {
            layoutPagebreakHandler.pageStarted();
        }
        pageStartPending = false;
        return true;
    }

    @Override
    public int getPageCount()
    {
        return flowCount;
    }

    @Override
    public boolean isCurrentPageEmpty()
    {
        // todo: Invent a test that checks whether the page is currently empty.
        final LogicalPageBox logicalPageBox = getPageBox();
        final PageBreakPositionList breakPositionList = logicalPageBox.getAllVerticalBreaks();
        final long masterBreak = breakPositionList.getLastMasterBreak();
        final boolean nextPageContainsContent = (logicalPageBox.getHeight() > masterBreak);
        return !nextPageContainsContent;
    }

    @Override
    public boolean isPageStartPending()
    {
        return pageStartPending;
    }

    @Override
    public void processIncrementalUpdate(final boolean performOutput) throws ContentProcessingException
    {
        if (!isDirty())
        {
            //      Log.debug ("Not dirty, no update needed.");
            return;
        }
        clearDirty();

        floodPrevention += 1;
        if (floodPrevention < 50) // this is a magic number ..
        {
            return;
        }
        floodPrevention = 0;

        final OutputProcessor outputProcessor = getOutputProcessor();
        if (!(outputProcessor instanceof IterativeOutputProcessor) || !outputProcessor.getMetaData()
                .isFeatureSupported(OutputProcessorFeature.ITERATIVE_RENDERING))
        {
            //      Log.debug ("No incremental system.");
            return;
        }

        final LogicalPageBox pageBox = getPageBox();
        pageBox.setPageEnd(pageBox.getHeight());
        //    Log.debug ("Computing Incremental update: " + pageBox.getPageOffset() + " " + pageBox.getPageEnd());
        // shiftBox(pageBox, true);

        if (pageBox.isOpen())
        {
            final IterativeOutputProcessor io = (IterativeOutputProcessor)outputProcessor;
            if (applyAutoCommitPageHeaderStep.compute(pageBox))
            {
                io.processIterativeContent(pageBox, performOutput);
                cleanFlowBoxesStep.compute(pageBox);
            }
        }
    }

    @Override
    public void startReport(final ReportDefinition report)
    {
        flowCount = 0;
        super.startReport(report);
    }

    @Override
    protected void debugPrint(final LogicalPageBox pageBox)
    {
        //    ModelPrinter.print(pageBox);
    }

    @Override
    protected boolean isPageFinished()
    {
        final LogicalPageBox pageBox = getPageBox();
        //    final long sizeBeforePagination = pageBox.getHeight();
        //final LogicalPageBox clone = (LogicalPageBox) pageBox.deriveForAdvance(true);
        final PaginationResult pageBreak = paginationStep.performPagebreak(pageBox);
        if (pageBreak.isOverflow() || !pageBox.isOpen())
        {
            setLastStateKey(pageBreak.getLastVisibleState());
            return true;
        }
        return false;
    }

    @Override
    protected boolean performPagination(final LayoutPagebreakHandler layoutPagebreakHandler,
            final boolean performOutput) throws ContentProcessingException
    {
        final OutputProcessor outputProcessor = getOutputProcessor();
        // next: perform pagination.
        final LogicalPageBox pageBox = getPageBox();
        //    final long sizeBeforePagination = pageBox.getHeight();
        //final LogicalPageBox clone = (LogicalPageBox) pageBox.deriveForAdvance(true);
        final PaginationResult pageBreak = paginationStep.performPagebreak(pageBox);
        if (pageBreak.isOverflow() || !pageBox.isOpen())
        {
            setLastStateKey(pageBreak.getLastVisibleState());
            //      final long sizeAfterPagination = pageBox.getHeight();
            setPagebreaks(getPagebreaks() + 1);
            pageBox.setAllVerticalBreaks(pageBreak.getAllBreaks());

            flowCount += 1;
            debugPrint(pageBox);

            // A new page has been started. Recover the page-grid, then restart
            // everything from scratch. (We have to recompute, as the pages may
            // be different now, due to changed margins or page definitions)
            final long nextOffset = pageBox.computePageEnd();
            pageBox.setPageEnd(nextOffset);
            final long pageOffset = pageBox.getPageOffset();

            if (performOutput)
            {
                if (outputProcessor.isNeedAlignedPage())
                {
                    final LogicalPageBox box = fillPhysicalPagesStep.compute(pageBox, pageOffset, nextOffset);
                    // DebugLog.log("Processing contents for Page " + flowCount + " Page-Offset: " + pageOffset + "
                    // -> " + nextOffset);

                    outputProcessor.processContent(box);
                }
                else
                {
                    //          DebugLog.log("Processing fast contents for Page " + flowCount + " Page-Offset: " +
                    //          pageOffset + " -> " + nextOffset);
                    outputProcessor.processContent(pageBox);
                }
            }
            else
            {
                //        DebugLog.log("Recomputing contents for Page " + flowCount + " Page-Offset: " + pageOffset +
                //        " -> " + nextOffset);
                outputProcessor.processRecomputedContent(pageBox);
            }

            // Now fire the pagebreak. This goes through all layers and informs all
            // components, that a pagebreak has been encountered and possibly a
            // new page has been set. It does not save the state or perform other
            // expensive operations. However, it updates the 'isPagebreakEncountered'
            // flag, which will be active until the input-feed received a new event.
            //      Log.debug("PageTime " + (currentPageAge - lastPageAge));

            final boolean repeat = pageBox.isOpen() || pageBreak.isOverflow();
            if (repeat)
            {
                // pageBox.setAllVerticalBreaks(pageBreak.getAllBreaks());
                // First clean all boxes that have been marked as finished. This reduces the overall complexity of the
                // pagebox and improves performance on huge reports.
                cleanFlowBoxesStep.compute(pageBox);

                final long l = cleanPaginatedBoxesStep.compute(pageBox);
                if (l > 0)
                {
                    //          Log.debug ("Apply shift afterwards " + l);
                    final InstanceID shiftNode = cleanPaginatedBoxesStep.getShiftNode();
                    applyPageShiftValuesStep.compute(pageBox, l, shiftNode);
                    debugPrint(pageBox);
                }

                pageBox.setPageOffset(nextOffset);
                if (pageBreak.isNextPageContainsContent())
                {
                    if (layoutPagebreakHandler != null)
                    {
                        layoutPagebreakHandler.pageStarted();
                    }
                    return true;
                }
                // No need to try again, we know that the result will not change, as the next page is
                // empty. (We already tested it.)
                pageStartPending = true;
                return false;
            }
            else
            {
                outputProcessor.processingFinished();
                pageBox.setPageOffset(nextOffset);
                return false;
            }
        }
        else if (outputProcessor instanceof IterativeOutputProcessor
                 && outputProcessor.getMetaData().isFeatureSupported(OutputProcessorFeature.ITERATIVE_RENDERING))
        {
            processIncrementalUpdate(performOutput);
            //      final IterativeOutputProcessor io = (IterativeOutputProcessor) outputProcessor;
            //      io.processIterativeContent(pageBox, performOutput);
            //      cleanFlowBoxesStep.compute(pageBox);
        }
        return false;
    }
}

