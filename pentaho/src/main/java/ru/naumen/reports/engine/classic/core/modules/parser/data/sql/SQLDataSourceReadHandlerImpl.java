package ru.naumen.reports.engine.classic.core.modules.parser.data.sql;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.SpringContext;
import ru.naumen.reports.engine.classic.core.modules.misc.datafactory.sql.SQLReportDataFactory;
import ru.naumen.reports.engine.classic.core.modules.misc.datafactory.sql.SQLReportDataFactoryImpl;

/**
 * <AUTHOR>
 * @since 01.10.2012
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SQLDataSourceReadHandlerImpl extends SQLDataSourceReadHandler
{
    @Inject
    SpringContext context;

    @Override
    protected SQLReportDataFactory createDataFactory()
    {
        return context.getBean(SQLReportDataFactoryImpl.class);
    }

}
