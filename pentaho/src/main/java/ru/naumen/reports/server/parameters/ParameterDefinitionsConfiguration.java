package ru.naumen.reports.server.parameters;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.inject.Named;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.reports.server.parameters.providers.ParameterDefinitionsProvider;

/**
 * Конфигурация провайдеров по типу параметров отчётов
 *
 * <AUTHOR> mnovozhilov
 * @since 06.11.2012
 */
@Configuration
public class ParameterDefinitionsConfiguration
{
    @Bean(name = "parameterDefinitionsProviders")
    public Map<String, ParameterDefinitionsProvider> getParameterDefinitionsProviders(
            @Named("caseListParameterDefinitionProvider")
            ParameterDefinitionsProvider caseListParameterDefinitionProvider,
            @Named("objectParameterDefinitionProvider")
            ParameterDefinitionsProvider objectParameterDefinitionProvider,
            @Named("objectsParameterDefinitionProvider")
            ParameterDefinitionsProvider objectsParameterDefinitionProvider,
            @Named("booleanParameterDefinitionProvider")
            ParameterDefinitionsProvider booleanParameterDefinitionProvider,
            @Named("dateParameterDefinitionProvider")
            ParameterDefinitionsProvider dateParameterDefinitionProvider,
            @Named("doubleParameterDefinitionProvider")
            ParameterDefinitionsProvider doubleParameterDefinitionProvider,
            @Named("longParameterDefinitionProvider")
            ParameterDefinitionsProvider longParameterDefinitionProvider,
            @Named("stringParameterDefinitionProvider")
            ParameterDefinitionsProvider stringParameterDefinitionProvider,
            @Named("stateParameterDefinitionProvider")
            ParameterDefinitionsProvider stateParameterDefinitionProvider,
            @Named("statesParameterDefinitionProvider")
            ParameterDefinitionsProvider statesParameterDefinitionProvider,
            @Named("subjectParameterDefinitionProvider")
            ParameterDefinitionsProvider subjectParameterDefinitionProvider
    )
    {
        Map<String, ParameterDefinitionsProvider> providers = new HashMap<>();
        //@formatter:off
        providers.put(Constants.BooleanAttributeType.CODE,          booleanParameterDefinitionProvider);
        providers.put(Constants.DateAttributeType.CODE,             dateParameterDefinitionProvider);
        providers.put(Constants.DateTimeAttributeType.CODE,         dateParameterDefinitionProvider);
        providers.put(Constants.DoubleAttributeType.CODE,           doubleParameterDefinitionProvider);
        providers.put(Constants.IntegerAttributeType.CODE,          longParameterDefinitionProvider);
        providers.put(Constants.StringAttributeType.CODE,           stringParameterDefinitionProvider);

        providers.put(Constants.MetricSeverityType.CODE,            objectParameterDefinitionProvider);
        providers.put(Constants.NDAPComputableCatalogItemType.CODE, objectParameterDefinitionProvider);
        providers.put(Constants.ObjectAttributeType.CODE,           objectParameterDefinitionProvider);
        providers.put(Constants.BOLinksAttributeType.CODE,          objectsParameterDefinitionProvider);
        providers.put(Constants.CatalogItemAttributeType.CODE,      objectParameterDefinitionProvider);
        providers.put(Constants.CatalogItemsAttributeType.CODE,     objectsParameterDefinitionProvider);
        providers.put(Constants.CaseListAttributeType.CODE,         caseListParameterDefinitionProvider);
        providers.put(Constants.StateAttributeType.CODE,            stateParameterDefinitionProvider);
        providers.put(Constants.StatesAttributeType.CODE,           statesParameterDefinitionProvider);

        providers.put(ScriptService.Constants.SUBJECT,              subjectParameterDefinitionProvider);
        providers.put(ScriptService.Constants.ACTION_USER,          objectParameterDefinitionProvider);
        //@formatter:on
        return providers;
    }
}
