package ru.naumen.reports.server.format;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.TimeZone;

import org.pentaho.reporting.engine.classic.core.layout.model.LogicalPageBox;
import org.pentaho.reporting.engine.classic.core.layout.output.ContentProcessingException;
import org.pentaho.reporting.engine.classic.core.layout.output.LogicalPageKey;
import org.pentaho.reporting.engine.classic.core.modules.output.table.base.TableContentProducer;
import org.pentaho.reporting.engine.classic.core.modules.output.table.xls.FlowExcelOutputProcessor;
import org.pentaho.reporting.libraries.base.config.Configuration;
import org.pentaho.reporting.libraries.resourceloader.ResourceManager;

/**
 * Расширения класса org.pentaho.reporting.engine.classic.core.modules.output.table.xls.FlowExcelOutputProcessor 
 * с частичным копипастом для того, чтобы значение private-поля printer.
 * См. {@link ExcelPrinterNau}
 * <AUTHOR>
 * @since 10.02.2014
 */
public class FlowExcelOutputProcessorNau extends FlowExcelOutputProcessor
{
    private final ExcelPrinterNau printer;

    public FlowExcelOutputProcessorNau(final Configuration config, final OutputStream outputStream,
            final ResourceManager resourceManager, TimeZone currentTimeZone, boolean useSXSSFWorkbook)
    {
        super(config, outputStream, resourceManager);

        this.printer = new ExcelPrinterNau(currentTimeZone, useSXSSFWorkbook);
        this.printer.init(config, getMetaData(), outputStream, resourceManager);
    }

    @Override
    public InputStream getTemplateInputStream()
    {
        return printer.getTemplateInputStream();
    }

    @Override
    public boolean isUseXlsxFormat()
    {
        return printer.isUseXlsxFormat();
    }

    @Override
    public void setTemplateInputStream(final InputStream templateInputStream)
    {
        printer.setTemplateInputStream(templateInputStream);
    }

    @Override
    public void setUseXlsxFormat(final boolean useXlsxFormat)
    {
        printer.setUseXlsxFormat(useXlsxFormat);
    }

    @Override
    protected void processingContentFinished()
    {
        if (!isContentGeneratable())
        {
            return;
        }

        this.getMetaData().commit();
        this.printer.close();
    }

    @Override
    protected void processTableContent(final LogicalPageKey logicalPageKey, final LogicalPageBox logicalPage,
            final TableContentProducer contentProducer) throws ContentProcessingException
    {
        printer.print(logicalPage, contentProducer, false);
    }

    @Override
    protected void updateTableContent(final LogicalPageKey logicalPageKey, final LogicalPageBox logicalPageBox,
            final TableContentProducer tableContentProducer, final boolean performOutput)
            throws ContentProcessingException
    {
        printer.print(logicalPageBox, tableContentProducer, true);
    }

}
