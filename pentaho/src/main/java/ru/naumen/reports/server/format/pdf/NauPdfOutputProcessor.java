package ru.naumen.reports.server.format.pdf;

import java.io.OutputStream;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.pentaho.reporting.engine.classic.core.layout.model.LogicalPageBox;
import org.pentaho.reporting.engine.classic.core.layout.model.PageGrid;
import org.pentaho.reporting.engine.classic.core.layout.output.ContentProcessingException;
import org.pentaho.reporting.engine.classic.core.layout.output.LogicalPageKey;
import org.pentaho.reporting.engine.classic.core.layout.output.OutputProcessorMetaData;
import org.pentaho.reporting.engine.classic.core.layout.output.PhysicalPageKey;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.base.AbstractPageableOutputProcessor;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.base.AllPageFlowSelector;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.base.PageFlowSelector;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.pdf.PdfOutputProcessor;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.pdf.internal.PdfDocumentWriter;
import org.pentaho.reporting.engine.classic.core.modules.output.pageable.pdf.internal.PdfOutputProcessorMetaData;
import org.pentaho.reporting.libraries.base.config.Configuration;
import org.pentaho.reporting.libraries.fonts.encoding.EncodingRegistry;
import org.pentaho.reporting.libraries.fonts.itext.ITextFontRegistry;
import org.pentaho.reporting.libraries.fonts.itext.ITextFontStorage;
import org.pentaho.reporting.libraries.resourceloader.ResourceManager;

/**
 * Копия оригинального {@link PdfOutputProcessor} за исключением некоторых строчек,
 * позволяющих использовать наши кастомизированные классы.
 * Используется {@link NauPdfOutputProcessorMetaData}, который задает шрифт, используемый по-умолчанию в отчетах. 
 * Ранее это был Helvetica, сейчас Open Sans.
 * Кроме того, используется {@link NauTextFontRegistry} вместо {@link ITextFontRegistry}, 
 * который не содержит BuildIn шрифтов с отсутствующей кириллицей (Helvetica, Times, SansSerif)
 *
 * <AUTHOR>
 */
@SuppressWarnings({ "java:S2583", "java:S2589" })
public class NauPdfOutputProcessor extends AbstractPageableOutputProcessor
{
    private static class NauPdfOutputProcessorMetaData extends PdfOutputProcessorMetaData
    {
        public NauPdfOutputProcessorMetaData(final Configuration configuration, final ITextFontStorage fontStorage)
        {
            super(configuration, fontStorage);
            setFamilyMapping(null, "Open Sans");
        }
    }

    private static ITextFontRegistry fontRegistry = null;
    private static final Lock lock = new ReentrantLock();

    @SuppressWarnings("PMD.NonThreadSafeSingleton")
    public static ITextFontRegistry getFontRegistry()
    {
        if (null == fontRegistry)
        {
            lock.lock();
            try
            {
                if (null == fontRegistry)
                {
                    fontRegistry = new NauTextFontRegistry();
                    fontRegistry.initialize();
                }
            }
            finally
            {
                lock.unlock();
            }

        }
        return fontRegistry;
    }

    private static ResourceManager createResourceManager()
    {
        final ResourceManager resourceManager = new ResourceManager();
        resourceManager.registerDefaults();
        return resourceManager;

    }

    private NauPdfOutputProcessorMetaData metaData;
    private PageFlowSelector flowSelector;
    private OutputStream outputStream;

    private PdfDocumentWriter writer;

    private ResourceManager resourceManager;

    public NauPdfOutputProcessor(final Configuration configuration, final OutputStream outputStream)
    {
        this(configuration, outputStream, NauPdfOutputProcessor.createResourceManager());
    }

    public NauPdfOutputProcessor(final Configuration configuration, final OutputStream outputStream,
            final ResourceManager resourceManager)
    {
        if (configuration == null)
        {
            throw new NullPointerException("Configuration must not be null");
        }
        if (outputStream == null)
        {
            throw new NullPointerException("OutputStream must not be null");
        }
        if (resourceManager == null)
        {
            throw new NullPointerException();
        }
        this.resourceManager = resourceManager;
        this.outputStream = outputStream;
        this.flowSelector = new AllPageFlowSelector();

        // for the sake of simplicity, we use the AWT font registry for now.
        // This is less accurate than using the iText fonts, but completing
        // the TrueType registry or implementing an iText registry is too expensive
        // for now.
        final String encoding = configuration.getConfigProperty(
                "org.pentaho.reporting.engine.classic.core.modules.output.pageable.pdf.Encoding",
                EncodingRegistry.getPlatformDefaultEncoding());
        final ITextFontStorage fontStorage = new ITextFontStorage(getFontRegistry(), encoding);

        metaData = new NauPdfOutputProcessorMetaData(configuration, fontStorage);

    }

    @Override
    public PageFlowSelector getFlowSelector()
    {
        return flowSelector;
    }

    @Override
    public OutputProcessorMetaData getMetaData()
    {
        return metaData;
    }

    public void setFlowSelector(final PageFlowSelector flowSelector)
    {
        if (flowSelector == null)
        {
            throw new NullPointerException();
        }

        this.flowSelector = flowSelector;
    }

    protected PdfDocumentWriter createPdfDocumentWriter()
    {
        return new PdfDocumentWriter((PdfOutputProcessorMetaData)getMetaData(), getOutputStream(),
                getResourceManager());
    }

    protected OutputStream getOutputStream()
    {
        return outputStream;
    }

    protected ResourceManager getResourceManager()
    {
        return resourceManager;
    }

    protected PdfDocumentWriter getWriter()
    {
        return writer;
    }

    @Override
    protected void processingContentFinished()
    {
        if (!isContentGeneratable())
        {
            return;
        }

        if (writer != null)
        {
            this.writer.close();
            this.metaData.commit();
        }
    }

    @Override
    protected void processLogicalPage(final LogicalPageKey key, final LogicalPageBox logicalPage)
            throws ContentProcessingException
    {
        try
        {
            if (writer == null)
            {
                writer = new PdfDocumentWriter(metaData, outputStream, resourceManager);
                writer.open();
            }
            writer.processLogicalPage(key, logicalPage);
        }
        catch (Exception e)
        {
            throw new ContentProcessingException("Failed to generate PDF document", e);
        }
    }

    @Override
    protected void processPhysicalPage(final PageGrid pageGrid, final LogicalPageBox logicalPage, final int row,
            final int col, final PhysicalPageKey pageKey) throws ContentProcessingException
    {
        try
        {
            if (writer == null)
            {
                writer = new PdfDocumentWriter(metaData, outputStream, resourceManager);
                writer.open();
            }
            writer.processPhysicalPage(pageGrid, logicalPage, row, col, pageKey);
        }
        catch (Exception e)
        {
            throw new ContentProcessingException("Failed to generate PDF document", e);
        }
    }
}