package ru.naumen.reports.server.format;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;

import org.apache.commons.io.FilenameUtils;
import org.pentaho.reporting.engine.classic.core.MasterReport;
import org.pentaho.reporting.engine.classic.core.ReportProcessingException;
import org.pentaho.reporting.engine.classic.core.modules.output.table.html.FileSystemURLRewriter;
import org.pentaho.reporting.engine.classic.core.modules.output.table.html.HtmlOutputProcessor;
import org.pentaho.reporting.engine.classic.core.modules.output.table.html.HtmlPrinter;
import org.pentaho.reporting.engine.classic.core.modules.output.table.html.StreamHtmlOutputProcessor;
import org.pentaho.reporting.libraries.repository.ContentLocation;
import org.pentaho.reporting.libraries.repository.DefaultNameGenerator;
import org.pentaho.reporting.libraries.repository.file.FileRepository;
import org.pentaho.reporting.libraries.repository.stream.StreamRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.reports.engine.classic.core.modules.output.table.base.StreamReportProcessor;
import ru.naumen.reports.engine.classic.core.modules.output.table.html.HtmlPrinterImpl;
import ru.naumen.reports.server.ReportsConfiguration;
import ru.naumen.reports.server.format.resources.ReportResourcesService;
import ru.naumen.reports.shared.Constants;

/**
 * <AUTHOR>
 * @since 10.10.2012
 */
@Component
public class HtmlReportFormat extends ReportFormatBase
{
    private static final Logger LOG = LoggerFactory.getLogger(HtmlReportFormat.class);

    private final ReportsConfiguration configuration;
    private final FileRepository repository;
    private final ContentLocation location;
    private final ReportResourcesService resourcesService;

    @Inject
    protected HtmlReportFormat(ReportsConfiguration configuration, FileRepository repository, ContentLocation location,
            ReportResourcesService resourcesService)
    {
        super(Constants.ReportFormat.HTML, "html");
        this.configuration = configuration;
        this.repository = repository;
        this.location = location;
        this.resourcesService = resourcesService;
    }

    @Override
    public void processReport(String reportUUID, MasterReport report, OutputStream outputStream,
            @Nullable Employee currentUser) throws ReportProcessingException
    {
        try
        {
            final String result = processReport(report, reportUUID);
            new PrintStream(outputStream, false, StandardCharsets.UTF_8.displayName()).print(result);
        }
        catch (final UnsupportedEncodingException e)
        {
            throw new FxException(e);
        }
    }

    private String processReport(MasterReport report, String reportUUID) throws ReportProcessingException
    {
        final StopWatch sw = StopWatchFactory.create("processReport", LOG.isDebugEnabled());

        final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        final StreamRepository targetRepository = new StreamRepository(outputStream);
        final ContentLocation targetRoot = targetRepository.getRoot();
        final HtmlOutputProcessor outputProcessor = new StreamHtmlOutputProcessor(report.getConfiguration());
        final HtmlPrinter printer = new HtmlPrinterImpl(report.getResourceManager(), resourcesService);
        printer.setContentWriter(targetRoot, new DefaultNameGenerator(targetRoot, "index", "html"));
        final ContentLocation dataRoot = location;
        printer.setDataWriter(dataRoot, new DefaultNameGenerator(dataRoot, "content"));
        printer.setUrlRewriter(new FileSystemURLRewriter());
        outputProcessor.setPrinter(printer);

        sw.start("process");
        final StreamReportProcessor reportProcessor = new StreamReportProcessor(report, outputProcessor, reportUUID);
        try
        {
            reportProcessor.processReport();
        }
        finally
        {
            reportProcessor.close();
        }
        sw.stop();

        sw.start("resources");
        String result = new String(outputStream.toByteArray(), StandardCharsets.UTF_8);
        result = processResources(result);
        sw.stop();

        if (LOG.isDebugEnabled())
        {
            LOG.debug("\n{}", sw.prettyPrint());
        }

        return result;
    }

    private String processResources(String source)
    {
        try
        {
            final String repositoryPath = FilenameUtils.normalize(repository.getURL().toString(), true);
            return source.replace(repositoryPath, configuration.getResourcesWebUrl());
        }
        catch (final MalformedURLException e)
        {
            throw new FxException(e);
        }
    }
}