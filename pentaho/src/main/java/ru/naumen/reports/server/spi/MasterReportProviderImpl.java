package ru.naumen.reports.server.spi;

import static org.pentaho.reporting.engine.classic.core.ClassicEngineCoreModule.NO_PRINTER_AVAILABLE_KEY;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.pentaho.reporting.engine.classic.core.AttributeNames;
import org.pentaho.reporting.engine.classic.core.ClassicEngineBoot;
import org.pentaho.reporting.engine.classic.core.MasterReport;
import org.pentaho.reporting.libraries.resourceloader.ResourceException;
import org.pentaho.reporting.libraries.resourceloader.ResourceManager;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.reports.engine.classic.core.modules.misc.datafactory.sql.SQLReportDataFactoryImpl;
import ru.naumen.reports.server.parameters.ParameterDefinitionsUtils;
import ru.naumen.reports.server.script.ClosureFunction;
import ru.naumen.reports.shared.Constants;
import ru.naumen.smp.report.specification.model.HasReportValues;
import ru.naumen.smp.report.specification.model.ReportBuildContext;
import ru.naumen.smp.report.specification.model.Template;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Реализация провайдера мастер-отчета — шаблона отчета в терминах Pentaho.
 * <AUTHOR>
 * @since 17.04.2025
 */
@Component
public class MasterReportProviderImpl implements MasterReportProvider
{
    private final ScriptService scriptService;
    private final ParameterDefinitionsUtils parameterDefinitionsUtils;
    private final ReportDataFactoryProvider reportDataFactoryProvider;
    private final ReportsDtObjectUtils dtObjectUtils;

    @Inject
    public MasterReportProviderImpl(
            ScriptService scriptService,
            ParameterDefinitionsUtils parameterDefinitionsUtils,
            ReportDataFactoryProvider reportDataFactoryProvider,
            ReportsDtObjectUtils dtObjectUtils)
    {
        this.scriptService = scriptService;
        this.parameterDefinitionsUtils = parameterDefinitionsUtils;
        this.reportDataFactoryProvider = reportDataFactoryProvider;
        this.dtObjectUtils = dtObjectUtils;
    }

    @Override
    public MasterReport getMasterReport(ReportBuildContext buildContext)
    {
        try
        {
            Template template = buildContext.getReportTemplate();
            byte[] rawTemplate = template.getFile();
            MasterReport masterReport = getMasterReport(rawTemplate);
            return configureMasterReport(template.getCode(), template.getScript(), masterReport, buildContext);
        }
        catch (ResourceException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public MasterReport getMasterReport(ReportBuildContext buildContext, HasReportValues valuesContainer)
    {
        return TransactionRunner.call(() ->
        {
            MasterReport masterReport = getMasterReport(buildContext);
            parameterDefinitionsUtils.addReportValues(masterReport, valuesContainer.getReportValues());
            return masterReport;
        });
    }

    @Override
    public MasterReport getMasterReport(byte[] rawTemplate) throws ResourceException
    {
        ensureReportEngineStarted();
        ResourceManager resourceManager = new ResourceManager();
        resourceManager.registerDefaults();
        MasterReport masterReport =
                (MasterReport)resourceManager.createDirectly(rawTemplate, MasterReport.class).getResource();

        //отключаем кэширование данных внутри pentaho, так как данные кэшируются в экземпляре отчета
        masterReport.setAttribute(AttributeNames.Core.NAMESPACE, AttributeNames.Core.DATA_CACHE, false);
        return masterReport;
    }

    private MasterReport configureMasterReport(@Nullable String templateCode, @Nullable Script script,
            MasterReport masterReport, ReportBuildContext buildContext)
    {
        SQLReportDataFactoryImpl sqlFactory = reportDataFactoryProvider.getSQLDataFactory(masterReport);
        if (null == sqlFactory)
        {
            throw new FxException("No SQL query in report template: " + templateCode);
        }

        List<Parameter> newParameters = prepareReportParameters(buildContext.getParameters(), script, buildContext);

        sqlFactory.setScript(script);
        sqlFactory.setUserUUID(buildContext.getCurrentUser());
        parameterDefinitionsUtils.configureParameters(masterReport,
                dtObjectUtils.copyAndSwapUuidToDtObject(newParameters),
                buildContext);

        if (null != script && scriptService.isScriptHasMethod(script, Constants.MasterReport.FUNCTIONS_METHOD))
        {
            Collection<ClosureFunction> functions = scriptService.executeFunction(script,
                    Constants.MasterReport.FUNCTIONS_METHOD, buildContext.getScriptContext());
            masterReport.getExpressions().addAll(functions);
        }

        // Для сохранения обратной совместимости новый FUNCTIONS_METHOD с параметрами отчета
        // реализован через отдельный вызов
        Map<String, Object> propertiesForFunctions = parametersToMap(buildContext.getParameters());

        if (null != script && scriptService.isScriptHasMethod(script, Constants.MasterReport.FUNCTIONS_METHOD,
                propertiesForFunctions))
        {
            Collection<ClosureFunction> functions = scriptService.executeFunction(script,
                    Constants.MasterReport.FUNCTIONS_METHOD, buildContext.getScriptContext(), propertiesForFunctions);
            masterReport.getExpressions().addAll(functions);
        }

        return masterReport;
    }

    /**
     * Подготовка значений параметров отчета
     *
     * @param interfaceParameters исходные параметры заполненные в интерфейсе
     * @param script скрипт шаблона отчета
     * @return список параметров
     */
    private List<Parameter> prepareReportParameters(List<Parameter> interfaceParameters, @Nullable Script script,
            ReportBuildContext buildContext)
    {
        if (script == null || script.getBody() == null)
        {
            return new ArrayList<>(interfaceParameters);
        }

        ArrayList<Parameter> parameters = new ArrayList<>(interfaceParameters.size());
        Map<String, Object> updatedParameters =
                customizeFilledReportParameters(script.getBody(), interfaceParameters, buildContext);
        for (Parameter parameter : interfaceParameters)
        {
            Parameter newParameter = ObjectUtils.clone(parameter);
            if (updatedParameters.containsKey(parameter.getCode()))
            {
                prepareAndSetNewParameterValue(updatedParameters.get(parameter.getCode()), newParameter);
            }
            parameters.add(newParameter);
        }
        return parameters;
    }

    /**
     * Выполняет преобразование нового значения атрибута к подходящему типу данных с последующим применением
     *
     * @param value новое значение параметра
     * @param newParameter для установки нового значения
     */
    private static void prepareAndSetNewParameterValue(@Nullable Object value, Parameter newParameter)
    {
        if (value == null)
        {
            return;
        }

        Object newValue = switch (newParameter.getType())
        {
            case IntegerAttributeType.CODE -> Long.parseLong(value.toString());
            case DoubleAttributeType.CODE -> Double.valueOf(value.toString());
            case CaseListAttributeType.CODE -> value instanceof List<?> listValue && !listValue.isEmpty()
                    ? listValue.stream()
                    .filter(Objects::nonNull)
                    .map(MasterReportProviderImpl::extractClassFqn)
                    .toList()
                    : value;
            default -> value;
        };
        newParameter.setValue(newValue);
    }

    private static Object extractClassFqn(Object value)
    {
        return value instanceof String fqnString ? ClassFqn.parse(fqnString) : value;
    }

    /**
     * Выполняет постобработку параметров отчета для возможности переопределения выбранных значений.
     *
     * @param script скрипт шаблона отчета
     * @param parameters параметры шаблона отчета
     * @return мапа из кодов атрибутов и их значений
     */
    private Map<String, Object> customizeFilledReportParameters(@Nullable String script, List<Parameter> parameters,
            ReportBuildContext context)
    {
        Map<String, Object> properties = parametersToMap(parameters);

        Script scriptObj = new Script(script);
        if (null != script && scriptService.isScriptHasMethod(scriptObj,
                Constants.ReportTemplate.GET_ACTUAL_PARAMETERS_METHOD,
                properties))
        {
            Map<String, Object> bindings = context.getScriptContext();
            return scriptService.executeFunction(scriptObj, Constants.ReportTemplate.GET_ACTUAL_PARAMETERS_METHOD,
                    bindings, properties);
        }
        return properties;
    }

    private static void ensureReportEngineStarted()
    {
        ClassicEngineBoot.getInstance().getEditableConfig().setConfigProperty(
                NO_PRINTER_AVAILABLE_KEY, Boolean.TRUE.toString());
        ClassicEngineBoot.getInstance().start();
    }

    private static Map<String, Object> parametersToMap(Collection<Parameter> parameters)
    {
        Map<String, Object> result = new HashMap<>();
        for (Parameter parameter : parameters)
        {
            result.put(parameter.getCode(), parameter.getValue());
        }
        return result;
    }
}
