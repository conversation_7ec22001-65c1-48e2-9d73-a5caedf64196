package ru.naumen.reports.server.spi;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;

import javax.imageio.ImageIO;

import org.pentaho.reporting.engine.classic.core.MasterReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.Constants.ImageFileExtension;
import ru.naumen.reports.server.ReportsConfiguration;
import ru.naumen.reports.server.format.ReportFormatsRegistry;
import ru.naumen.reports.server.objects.ReportImageValue;
import ru.naumen.reports.server.objects.ReportInstance;
import ru.naumen.reports.server.objects.ReportTableModelAdapter;
import ru.naumen.reports.server.objects.ReportValue;
import ru.naumen.reports.server.script.AbstractTableModelWrapper;
import ru.naumen.reports.server.script.DefaultTableModelWrapper;
import ru.naumen.reports.server.script.StreamingTableModelWrapper;
import ru.naumen.smp.report.specification.model.Report;
import ru.naumen.smp.report.specification.model.ReportBuildContext;
import ru.naumen.smp.report.specification.model.ReportVariable;
import ru.naumen.smp.report.specification.services.ReportExportController;

/**
 * Реализация компонента, отвечающего за выгрузку отчета в определенном формате.
 * <AUTHOR>
 * @since Mar 11, 2025
 */
@Component
public class ReportExportControllerImpl implements ReportExportController
{
    private static final Logger LOG = LoggerFactory.getLogger(ReportExportControllerImpl.class);

    private final ReportsConfiguration configuration;
    private final MappingService mappingService;
    private final ReportFormatsRegistry formatsRegistry;
    private final ReportDateUtils reportDateUtils;
    private final MasterReportProvider masterReportProvider;
    private final ReportDataFactoryProvider dataFactorySource;

    @Inject
    public ReportExportControllerImpl(
            ReportsConfiguration configuration,
            MappingService mappingService,
            ReportFormatsRegistry formatsRegistry,
            ReportDateUtils reportDateUtils,
            MasterReportProvider masterReportProvider,
            ReportDataFactoryProvider dataFactorySource)
    {
        this.configuration = configuration;
        this.mappingService = mappingService;
        this.formatsRegistry = formatsRegistry;
        this.reportDateUtils = reportDateUtils;
        this.masterReportProvider = masterReportProvider;
        this.dataFactorySource = dataFactorySource;
    }

    @Override
    public boolean canBeStreamed(Report report)
    {
        return report.canBeStreamed() && report.getTableSize() > configuration.getMaxDataSize();
    }

    @Override
    public void exportReport(ReportBuildContext buildContext, Report report, String format,
            @Nullable String clientTimeZone, boolean streamMode, OutputStream outputStream)
    {
        StopWatch sw = StopWatchFactory.create("exportReport", LOG.isDebugEnabled());

        sw.start("transform table");
        AbstractTableModelWrapper wrapper = streamMode
                ? new StreamingTableModelWrapper()
                : new DefaultTableModelWrapper();
        AbstractTableModelWrapper transformedWrapper = mappingService.transform(report, wrapper);
        sw.stop();

        sw.start("prepare parameters");
        Date creationDate = report instanceof ReportInstance instance ? instance.getCreationDate() : new Date();
        //параметр с именем reportCreationDate должен иметь типа Date и будет содержать дату создания отчёта
        transformedWrapper.getReportValues().add(new ReportValue("reportCreationDate", creationDate));

        reportDateUtils.convertDateParametersToTimeZone(report.getParameters());
        sw.stop();

        sw.start("prepare report");
        ReportExportControllerImpl.convertReportImageValues(transformedWrapper, format);

        MasterReport reportDefinition = masterReportProvider.getMasterReport(buildContext, transformedWrapper);
        dataFactorySource.getSQLDataFactory(reportDefinition).setTableModel(ReportTableModelAdapter.adapt(wrapper));
        sw.stop();

        sw.start("process");
        CoreEmployee user = buildContext.getCurrentUser();
        String userUuid = user == null ? null : user.getUUID();
        formatsRegistry.processReport(report.getUUID(), format, reportDefinition, outputStream, userUuid,
                clientTimeZone);
        sw.stop();

        if (LOG.isDebugEnabled())
        {
            LOG.debug("\n{}", sw.prettyPrint());
        }
    }

    @Override
    public String getFormatFileExtension(String format)
    {
        return formatsRegistry.getFileNameExtension(format);
    }

    /**
     * Метод для преобразования значений ReportImageValue таблицы отчета в ReportValue
     * с сохранением цветового формата изображения. Необходим для корректного экспорта
     * изображений в отчетах в pdf, до тех пор, пока не будет разрешен дефект:
     * http://jira.pentaho.com/browse/PRD-4720
     *
     * @param table обертка таблицы отчета для работы с ней из groovy-скрипта
     * @param format формат документа, в который выполняется экспорт
     */
    private static void convertReportImageValues(@Nullable AbstractTableModelWrapper table, String format)
    {
        if (!ru.naumen.reports.shared.Constants.ReportFormat.PDF.equalsIgnoreCase(format) || table == null)
        {
            return;
        }
        Map<String, Object> changedReportValues = new HashMap<>();
        List<ReportVariable> newReportValues = table.getReportValues();
        ListIterator<ReportVariable> iter = newReportValues.listIterator();
        while (iter.hasNext())
        {
            Object value = iter.next();
            if (value instanceof ReportImageValue reportImageValue)
            {
                byte[] bytes = ReportExportControllerImpl.getBufferedImageBytes(reportImageValue.getImage());
                final String code = reportImageValue.getCode();
                changedReportValues.put(code, bytes);
                iter.remove();
            }
        }
        for (Entry<String, Object> entry : changedReportValues.entrySet())
        {
            final String code = entry.getKey();
            final Object value = entry.getValue();
            if (!StringUtilities.isEmptyTrim(code) && value != null)
            {
                table.addValue(code, value);
            }
        }
    }

    /**
     * Метод для получения массива байт BufferedImage.
     *
     * @param image исходный объект изображения BufferedImage.
     */
    @Nullable
    private static byte[] getBufferedImageBytes(BufferedImage image)
    {
        try (final ByteArrayOutputStream baos = new ByteArrayOutputStream())
        {
            final String formatName = ReportExportControllerImpl.getAppropriateImageFormatName(image.getType());
            ImageIO.write(image, formatName, baos);
            baos.flush();
            return baos.toByteArray();
        }
        catch (IOException e)
        {
            LOG.debug("Cannot get buffered image bytes.", e);
        }
        return null;
    }

    /**
     * Метод для получения корректного файлового формата изображения,
     * соответствующего его типу.
     *
     * @param imageType тип изображения
     *
     * @return строковое представление файлового формата изображения.
     */
    private static String getAppropriateImageFormatName(int imageType)
    {
        String formatName = ImageFileExtension.JPG;
        if (imageType == BufferedImage.TYPE_INT_ARGB || imageType == BufferedImage.TYPE_INT_ARGB_PRE)
        {
            formatName = ImageFileExtension.PNG;
        }
        return formatName;
    }
}
