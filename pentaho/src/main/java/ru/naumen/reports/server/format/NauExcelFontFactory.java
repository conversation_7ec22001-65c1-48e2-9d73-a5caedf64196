package ru.naumen.reports.server.format;

import java.awt.*;
import java.util.HashMap;

import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;
import org.pentaho.reporting.engine.classic.core.modules.output.table.xls.helper.ExcelColorProducer;
import org.pentaho.reporting.engine.classic.core.modules.output.table.xls.helper.ExcelFontFactory;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

/**
 * Перенесён из {@link ExcelFontFactory ExcelFontFactory} для внесения изменений для совместимости c обновлённой версией
 * poi ради Java 11.
 * <AUTHOR>
 * @since 27.01.2020
 */
public class NauExcelFontFactory
{
    /**
     * The list of fonts that we have used so far.
     */
    private final HashMap<NauHSSFFontWrapper, Font> fonts;

    /**
     * The workbook that is used to create the font.
     */
    private final Workbook workbook;

    /**
     * Constructor for ExcelFontFactory.
     *
     * @param workbook the workbook.
     */
    public NauExcelFontFactory(@Nullable final Workbook workbook,
            @Nullable final ExcelColorProducer colorProducer)
    {
        if (workbook == null)
        {
            throw new NullPointerException();
        }
        if (colorProducer == null)
        {
            throw new NullPointerException();
        }

        this.fonts = new HashMap<>();
        this.workbook = workbook;

        // read the fonts from the workbook ...
        // Funny one: Please note that the layout will be broken if the first
        // font is not 'Arial 10'.
        final int numberOfFonts = this.workbook.getNumberOfFonts();
        for (int i = 0; i < numberOfFonts; i++)
        {
            final Font font = workbook.getFontAt(i);
            this.fonts.put(new NauHSSFFontWrapper(font), font);
        }

        // add the default font
        // this MUST be the first one, that is created.
        // oh, I hate Excel ...
        final NauHSSFFontWrapper wrapper = new NauHSSFFontWrapper("Arial", (short)10, false, false, false, false,
                colorProducer.getNearestColor(Color.black));
        getExcelFont(wrapper);
    }

    /**
     * Creates a HSSFFont. The created font is cached and reused later, if a similar font is requested.
     *
     * @param wrapper the font information that should be used to produce the excel font
     * @return the created or a cached HSSFFont instance
     */
    public Font getExcelFont(@CheckForNull final NauHSSFFontWrapper wrapper)
    {
        if (wrapper == null)
        {
            throw new NullPointerException();
        }

        if (fonts.containsKey(wrapper))
        {
            return fonts.get(wrapper);
        }

        // ok, we need a new one ...
        final Font excelFont = createFont(wrapper);
        fonts.put(wrapper, excelFont);
        return excelFont;
    }

    /**
     * Returns the excel font stored in this wrapper.
     *
     * @param wrapper the font wrapper that holds all font information from the repagination.
     * @return the created font.
     */
    private Font createFont(final NauHSSFFontWrapper wrapper)
    {
        final Font font = workbook.createFont();
        font.setBold(wrapper.isBold());
        font.setColor(wrapper.getColorIndex());
        font.setFontName(wrapper.getFontName());
        font.setFontHeightInPoints((short)wrapper.getFontHeight());
        font.setItalic(wrapper.isItalic());
        font.setStrikeout(wrapper.isStrikethrough());
        if (wrapper.isUnderline())
        {
            font.setUnderline(Font.U_SINGLE);
        }
        else
        {
            font.setUnderline(Font.U_NONE);
        }
        return font;
    }
}
