package ru.naumen.fts.server.search;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;

import jakarta.inject.Inject;

import org.apache.lucene.store.Directory;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.test.util.ReflectionTestUtils;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.search.fts.server.lucene.SearchCommand;
import ru.naumen.lucene.fts.server.lucene.LuceneDirectoryManager;
import ru.naumen.lucene.fts.server.lucene.LuceneIndexUpdater;
import ru.naumen.search.fts.server.queue.TxIndexingQueue;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jms.JMSManager;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.search.fts.server.ObjectModificationEventListener;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.DocumentBuilderService;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.DocumentBuilderService.DocumentBuildResult;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.UpdateDocumentEntity;
import ru.naumen.search.fts.server.lucene.filters.EqSearchFilterHandler;
import ru.naumen.lucene.fts.server.lucene.mutator.IndexMutator;
import ru.naumen.lucene.fts.server.lucene.mutator.RollbackIndexPolicy;
import ru.naumen.lucene.fts.server.lucene.mutator.actions.DeleteIndexAction;
import ru.naumen.lucene.fts.server.lucene.mutator.actions.StoreDocumentsAction;
import ru.naumen.fts.shared.FtsConstants;
import ru.naumen.fts.shared.SimpleSearchAction;
import ru.naumen.metainfo.server.spi.elements.SearchSettingImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.server.search.SearchSettingsService;

/**
 * Утилитарный класс для тестов на Lucene
 * @see FileSystemLuceneOperationDbTest
 */
@Component
public class LuceneTestUtils
{
    private static final Logger LOG = LoggerFactory.getLogger(LuceneTestUtils.class);
    private final ObjectTestUtils objectTestUtils;
    private final LuceneDirectoryManager luceneDirectoryManager;
    private final TxIndexingQueue txIndexingQueue;
    private final LuceneIndexUpdater updater;
    private final SecurityTestUtils security;
    private final CurrentEmployeeContext currentEmployeeContext;
    private final CreatedListener createdListener;
    private final ObjectModificationEventListener modifyListener;
    private final Dispatch dispatch;
    private final JMSManager jmsManager;
    private final IndexMutator indexMutator;
    private final DocumentBuilderService documentBuilderService;
    private final SearchSettingsService searchSettingsService;
    private final EqSearchFilterHandler eqSearchFilterHandler;

    @Inject
    public LuceneTestUtils(ObjectTestUtils objectTestUtils,
            LuceneDirectoryManager luceneDirectoryManager,
            TxIndexingQueue txIndexingQueue,
            LuceneIndexUpdater updater,
            SecurityTestUtils security,
            CurrentEmployeeContext currentEmployeeContext,
            CreatedListener createdListener,
            ObjectModificationEventListener modifyListener,
            Dispatch dispatch,
            JMSManager jmsManager,
            IndexMutator indexMutator,
            DocumentBuilderService documentBuilderService,
            SearchSettingsService searchSettingsService,
            EqSearchFilterHandler eqSearchFilterHandler)
    {
        this.objectTestUtils = objectTestUtils;
        this.luceneDirectoryManager = luceneDirectoryManager;
        this.txIndexingQueue = txIndexingQueue;
        this.updater = updater;
        this.security = security;
        this.currentEmployeeContext = currentEmployeeContext;
        this.createdListener = createdListener;
        this.modifyListener = modifyListener;
        this.dispatch = dispatch;
        this.jmsManager = jmsManager;
        this.indexMutator = indexMutator;
        this.documentBuilderService = documentBuilderService;
        this.searchSettingsService = searchSettingsService;
        this.eqSearchFilterHandler = eqSearchFilterHandler;
    }

    /**
     * Добавить объект в индекс
     * @param createdObject добавляемы объект
     * @throws IOException
     */
    public void addToIndex(IUUIDIdentifiable createdObject) throws IOException
    {
        DocumentBuildResult buildResult = documentBuilderService.buildDocument(
                createdObject.getUUID(), SearchCommand.UPDATE, false);
        buildResult.getErrorMessage().ifPresent(errorMsg ->
        {
            throw new FxException("Error building document for " + createdObject.getUUID() + " Error " + errorMsg);
        });
        UpdateDocumentEntity entity = buildResult.getDocEntity().orElseThrow(
                () -> new FxException("document was not build for " + createdObject.getUUID()));
        indexMutator.mutate(new StoreDocumentsAction(updater, Collections.singletonList(entity)));
    }

    public void clearIndex() throws Exception
    {
        LOG.info("Clearing index");
        indexMutator.mutate(DeleteIndexAction.INSTANCE);
        LOG.info("Index cleared");
    }

    /**
     * Создать объект и добавить его в индекс.
     * Будут создаваться отделы
     * @param title название создаваемого объекта
     * @return созданный объект
     * @throws Exception
     */
    public IUUIDIdentifiable createAndAddToIndex(String title) throws Exception
    {
        IUUIDIdentifiable createdObject = createObject(title);
        addToIndex(createdObject);
        return createdObject;
    }

    /**
     * Создать объект
     * Будут создаваться отделы
     * @param objectTitle название создавамого объекта
     * @return созданный объект
     * @throws Exception
     */
    public IUUIDIdentifiable createObject(String objectTitle) throws Exception
    {
        return objectTestUtils.create(objectTestUtils.getDefaultOUCase(), getProps(objectTitle));
    }

    /**
     * Получить {@link Directory}
     * @return
     */
    public Directory getDirectory()
    {
        return luceneDirectoryManager.getCurrentDirectory();
    }

    public LuceneDirectoryManager getLuceneDirectoryManager()
    {
        return luceneDirectoryManager;
    }

    /**
     * Искать объекты(отделы) по названию
     * @param searchStr поисковая строка (название)
     * @return список найденных UUID или null, если не найдены объекты данного типа fqn
     * @throws DispatchException
     */
    public ArrayList<String> searchObjects(String searchStr) throws DispatchException
    {
        return dispatch.execute(new SimpleSearchAction(searchStr, false)).getSimpleSearchResults().getObjects().get(
                OU.FQN);
    }

    /**
     * Подготовка теста перед стартом.
     * Авторизует как суперпользователя, и убирает проверку прав в {@link CommonUtils}
     * Отключает обработку новых объектов через JMS(сообщений о необходимости индексации не будет)
     * @throws Exception
     */
    public void setup() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        objectTestUtils.getDefaultOUCase();
        jmsManager.disconnectFileDocumentIndexer();
        jmsManager.disconnectDocumentIndexer();
        clearIndex();
        resetPolicy();
        createdListener.setUp();
        security.autenticateAsSuperUser();

        CurrentEmployeeContext spy = Mockito.spy(currentEmployeeContext);
        Mockito.doReturn(true).when(spy).isCurrentUserLicensedOrSuperUser(Mockito.any());
        TxIndexingQueue queueMock = Mockito.mock(TxIndexingQueue.class);

        ReflectionTestUtils.setField(eqSearchFilterHandler, "currentEmployeeContext", spy);
        ReflectionTestUtils.setField(modifyListener, "txIndexingQueue", queueMock);
    }

    public Directory swapDirectory(Directory newDirectory)
    {
        final Directory prevDirectory = getDirectory();
        luceneDirectoryManager.overrideDirectory(newDirectory);
        return prevDirectory;
    }

    /**
     * Очистка после теста.
     * Удаляет созданные в тестах объекты из индекса и удаляет их из системы.
     * Возвращает обработку через JMS
     * @throws Throwable
     */
    public void teardown() throws Throwable
    {
        clearIndex();
        resetPolicy();
        createdListener.tearDown();
        ReflectionTestUtils.setField(eqSearchFilterHandler, "currentEmployeeContext", currentEmployeeContext);
        ReflectionTestUtils.setField(modifyListener, "txIndexingQueue", txIndexingQueue);
        jmsManager.connectDocumentFileIndexer();
        jmsManager.connectDocumentIndexer();
    }

    /**
     * Получение {@link IProperties} создаваемого объекта
     * @param objectTitle название объекта
     * @return свойства для объекта
     */
    private IProperties getProps(String objectTitle)
    {
        MapProperties mapProperties = new MapProperties();
        mapProperties.setProperty(Constants.AbstractBO.TITLE, objectTitle);
        return mapProperties;
    }

    private void resetPolicy()
    {
        RollbackIndexPolicy policy = (RollbackIndexPolicy)SpringContext.getInstance().getBean("rollbackIndexPolicy");
        ReflectionTestUtils.setField(policy, "successfullCommit", null);
        ReflectionTestUtils.setField(policy, "previousSuccessfullCommit", null);
    }

    /**
     * Включаем поиск по атрибуту codeAttr для объектов типа fqn
     * @param fqn fqn для которого добавляем поисковой атрибут
     * @param codeAttr код атрибута
     */
    public void changeSearchable(ClassFqn fqn, String codeAttr)
    {
        SearchSettingImpl ss = new SearchSettingImpl();
        ss.setSimpleSearchableForLicensed(true);
        ss.setSimpleSearchableForNotLicensed(true);
        ss.setExtendedSearchableForLicensed(true);
        ss.setExtendedSearchableForNotLicensed(true);
        ss.addSearchAlias("ru", "");
        ss.setSearchBoost(1.0f);
        ss.setSearchAnalyzers(CollectionUtils.map("base", FtsConstants.ANALYZER_NO_MORPH_NO_STRICT));
        TransactionRunner.call(() -> searchSettingsService.saveSearchSettings(ss, codeAttr, fqn));
    }

    /**
     * Выключаем поиск по атрибуту codeAttr для объектов типа fqn
     */
    public void disableSearchable(ClassFqn fqn, String codeAttr)
    {
        SearchSettingImpl ss = new SearchSettingImpl();
        ss.setSimpleSearchableForLicensed(false);
        ss.setSimpleSearchableForNotLicensed(false);
        ss.setExtendedSearchableForLicensed(false);
        ss.setExtendedSearchableForNotLicensed(false);
        ss.addSearchAlias("ru", "");
        ss.setSearchBoost(1.0f);
        ss.setSearchAnalyzers(CollectionUtils.map("base", FtsConstants.ANALYZER_NO_MORPH_NO_STRICT));
        TransactionRunner.call(() -> searchSettingsService.saveSearchSettings(ss, codeAttr, fqn));
    }
}
