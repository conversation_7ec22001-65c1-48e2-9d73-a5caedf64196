package ru.naumen.lucene.fts.server.lucene.mutator.actions;

import java.io.IOException;

import edu.umd.cs.findbugs.annotations.CheckForNull;

import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.IndexWriter;

/**
 * Действие мутации индекса
 * <AUTHOR>
 *
 */
public interface MutateAction
{
    /**
     * @return идентификатор действия
     */
    default String getActionID()
    {
        return "unknown mutation action";
    }

    /**
     * Выполнение действия над индексом
     * @param writer {@link IndexWriter} для модификаций
     * @param reader на основе <code>writer</code> для использования внутри операции, например для поиска схожих
     *               документов в индексе.Перед использованием проверить на <code>null</code> ибо если
     *               <code>shouldPerformWithReader == false</code>, здесь будет <code>null</code>
     * @throws IOException при низкоуровневых ошибках
     */
    void perform(IndexWriter writer, @CheckForNull IndexReader reader) throws IOException;

    /**
     * @return должно ли данное действие выполняться с {@link IndexReader}
     */
    default boolean shouldPerformWithReader()
    {
        return false;
    }
}