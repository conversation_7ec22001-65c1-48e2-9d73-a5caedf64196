package ru.naumen.lucene.fts.server.lucene.mutator.actions;

import java.io.IOException;

import edu.umd.cs.findbugs.annotations.CheckForNull;

import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.IndexWriter;

/**
 * Действие удаления индекса
 * <AUTHOR>
 * @since 27.11.2019
 */
public final class DeleteIndexAction implements MutateAction
{
    public static final DeleteIndexAction INSTANCE = new DeleteIndexAction();

    private DeleteIndexAction()
    {

    }

    @Override
    public String getActionID()
    {
        return "Delete index";
    }

    @Override
    public void perform(IndexWriter writer, @CheckForNull IndexReader reader) throws IOException
    {
        writer.deleteAll();
    }
}