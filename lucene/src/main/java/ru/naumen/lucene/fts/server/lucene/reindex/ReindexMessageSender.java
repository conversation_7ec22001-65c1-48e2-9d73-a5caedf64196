package ru.naumen.lucene.fts.server.lucene.reindex;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.jms.MapMessage;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.eventaction.jms.JmsMessageCustomizer;
import ru.naumen.core.server.eventaction.jms.JmsMessageCustomizer.AdditionalBatchLoader;
import ru.naumen.core.server.jms.Constants.Queues;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.monitoring.JMSStatistics;
import ru.naumen.search.fts.server.lucene.SearchCommand;
import ru.naumen.search.fts.server.lucene.SearchConstants;
import ru.naumen.search.fts.server.lucene.reindex.ReindexBatchLoader;
import ru.naumen.search.fts.server.lucene.reindex.ReindexController.ReindexMode;

/**
 * Непосредственный отправить сообщений о переиндексации
 * <AUTHOR>
 * @since 01.10.18
 */
@Component
public class ReindexMessageSender
{
    /**
     * Дескриптор пачки переиндексации
     * Содержит:
     * <ol>
     *     <li>Список UUID`в объектов</li>
     *     <li>Комманду для обработки индексации</li>
     *     <li>Идентификатор операции переиндексации</li>
     *     <li>Идентификатор узла кластера, что должен обработать сообщения</li>
     *     <li>Номер последней пачки объектов для переиндексации</li>
     *     <li>Очередь, куда отправить сообщения</li>
     * </ol>
     */
    private static final class ReindexBatchDescriptor
    {
        private final List<String> uuids;
        private final SearchCommand searchCommand;
        private final String operationUUID;
        private final String clusterNode;
        private final int lastBatchNum;
        private final String targetQueue;

        private ReindexBatchDescriptor(List<String> uuids, SearchCommand searchCommand, @Nullable String clusterNode)
        {
            this(uuids, searchCommand, null, clusterNode, -1);
        }

        private ReindexBatchDescriptor(List<String> uuids, SearchCommand searchCommand, @Nullable String operationUUID,
                @Nullable String clusterNode, int lastBatchNum)
        {
            this.uuids = uuids;
            this.searchCommand = searchCommand;
            this.operationUUID = operationUUID;
            this.clusterNode = clusterNode;
            this.lastBatchNum = lastBatchNum;
            this.targetQueue = this.searchCommand == SearchCommand.UPDATE_FILES
                    ? Queues.QUEUE_DOCUMENT_FILE_INDEXER
                    : Queues.QUEUE_DOCUMENT_INDEXER;
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(ReindexMessageSender.class);
    private final Provider<JmsTemplate> jmsTemplateProvider;
    private final JMSStatistics jmsStat;
    private final JmsMessageCustomizer jmsMessageCustomizer;

    @Inject
    public ReindexMessageSender(
            final Provider<JmsTemplate> jmsTemplateProvider,
            final JmsMessageCustomizer jmsMessageCustomizer,
            final JMSStatistics jmsStat)
    {
        this.jmsTemplateProvider = jmsTemplateProvider;
        this.jmsStat = jmsStat;
        this.jmsMessageCustomizer = jmsMessageCustomizer;
    }

    /**
     * Отправить сообщение о том, что нужно удалить весь индекс
     */
    void sendDropIndex(ReindexMode reindexMode)
    {
        TransactionRunner.run(TransactionType.NEW, () ->
                jmsTemplateProvider.get().send(Queues.QUEUE_DOCUMENT_INDEXER, session ->
                {
                    final MapMessage message = session.createMapMessage();
                    message.setBooleanProperty(SearchConstants.REINDEX_ALL, true);
                    if (reindexMode == ReindexMode.UPDATE_SINGLE_NODE || reindexMode == ReindexMode.RESUME_UPDATE)
                    {
                        message.setStringProperty(SearchConstants.INDEXING_NODE, ClusterInfoService.getClusterNodeID());
                    }
                    message.setJMSPriority(9);
                    return message;
                }));
    }

    /**
     * Отправить объекты на переиндексацию
     * @param batchLoader попачечный загручзик UUID-ов для переиндексации
     * @param additionalBatchLoader Дополнительный попачечный загручзик UUID-ов для переиндексации (У нас могут быть 
     *                              объекты того же fqn из другого мира (например, версии объектов))
     * @param indexedObjects UUID`ы проиндексировнных объектов, если не пусто, то для объектов, которые уже удалены
     *                       будут отправлены сообщения о удалении их документов из индекса
     * @param operationUuid UUID операции переиндексации
     * @param fileOperationUuid UUID операции переиндексации файлов
     * @param reindexClusterNode Идентификатор ноды, которая должна будет обработать сообщения
     */
     void sendMessages(ReindexBatchLoader batchLoader,
            AdditionalBatchLoader additionalBatchLoader,
            Set<String> indexedObjects,
            String operationUuid,
            @Nullable String fileOperationUuid,
            @Nullable String reindexClusterNode)
    {
        long queueSize = 0L;
        long removeQueueSize = 0L;
        final JmsTemplate jmsTemplate = this.jmsTemplateProvider.get();
        jmsTemplate.setSessionTransacted(false);
        final boolean hasAdditionalBatch = additionalBatchLoader.peek();
        int batch = 0;

        if (batchLoader.peek())
        {
            while (batchLoader.next())
            {
                ++batch;
                final List<String> uuids = batchLoader.current();
                uuids.forEach(indexedObjects::remove);
                queueSize += uuids.size();

                final int lastBatchNum = batchLoader.isLast() && !hasAdditionalBatch ? batch : -1;
                final ReindexBatchDescriptor reindexBatchDescriptor = new ReindexBatchDescriptor(uuids,
                        SearchCommand.UPDATE,
                        operationUuid, reindexClusterNode, lastBatchNum);
                doSend(reindexBatchDescriptor, jmsTemplate);
                if (fileOperationUuid != null)
                {
                    ReindexBatchDescriptor fileReindexBatchDescriptor = new ReindexBatchDescriptor(uuids,
                            SearchCommand.UPDATE_FILES, fileOperationUuid, reindexClusterNode, lastBatchNum);
                    doSend(fileReindexBatchDescriptor, jmsTemplate);
                    LOG.info("Queued {} objects for file reindexing. Operation id = {}", uuids.size(),
                            fileOperationUuid);
                }
                LOG.info("Queued {} objects for reindexing. Operation id = {}", uuids.size(), operationUuid);
            }
        }

        sendAdditionalBatches(additionalBatchLoader, indexedObjects, operationUuid,
                reindexClusterNode, queueSize, jmsTemplate, batch);
        sendDeleteDocuments(batchLoader, indexedObjects, operationUuid, reindexClusterNode, removeQueueSize,
                jmsTemplate);
    }

    private void sendAdditionalBatches(AdditionalBatchLoader additionalBatchLoader, Set<String> indexedObjects,
            String operationUuid, @Nullable String reindexClusterNode, long queueSize, JmsTemplate jmsTemplate,
            int batch)
    {
        //Обработка дополнительных объектов
        if (additionalBatchLoader.peek())
        {
            while (additionalBatchLoader.next())
            {
                ++batch;
                final List<String> uuids = additionalBatchLoader.current();
                uuids.forEach(indexedObjects::remove);
                queueSize += uuids.size();

                int lastBatchNum = additionalBatchLoader.isLast() ? batch : -1;
                final ReindexBatchDescriptor reindexBatchDescriptor = new ReindexBatchDescriptor(uuids,
                        SearchCommand.UPDATE,
                        operationUuid, reindexClusterNode, lastBatchNum);
                doSend(reindexBatchDescriptor, jmsTemplate);
                LOG.info("Queued {} objects for reindexing. Operation id = {}", uuids.size(), operationUuid);
            }
        }
        LOG.info("Total of {} objects was queued for reindex", queueSize);
    }

    private void sendDeleteDocuments(ReindexBatchLoader batchLoader, Set<String> indexedObjects, String operationUuid,
            @Nullable String reindexClusterNode, long removeQueueSize, JmsTemplate jmsTemplate)
    {
        if (!indexedObjects.isEmpty())
        {
            for (List<String> partition : Lists.partition(Lists.newArrayList(indexedObjects),
                    batchLoader.getBatchSize()))
            {
                removeQueueSize += partition.size();
                //бесполезные документы удаляются вне общей операции переиндексации
                final ReindexBatchDescriptor deleteDocuments = new ReindexBatchDescriptor(partition,
                        SearchCommand.DELETE, reindexClusterNode);
                doSend(deleteDocuments, jmsTemplate);
                LOG.info("Queued {} for removal from index. Operation id = {}", partition.size(), operationUuid);
            }
            LOG.info("Total of {} objects was queued for removal from index", removeQueueSize);
        }
    }

    private void doSend(ReindexBatchDescriptor reindexBatchDescriptor, JmsTemplate jmsTemplate)
    {
        final Map<String, String> toSend = new HashMap<>();
        for (String s : reindexBatchDescriptor.uuids)
        {
            toSend.put(s, reindexBatchDescriptor.searchCommand.name());
        }
        jmsTemplate.convertAndSend(reindexBatchDescriptor.targetQueue, toSend, msg ->
        {
            if (reindexBatchDescriptor.operationUUID != null)
            {
                msg.setStringProperty(SearchConstants.OPERATION_UUID, reindexBatchDescriptor.operationUUID);
            }

            if (reindexBatchDescriptor.clusterNode != null)
            {
                msg.setStringProperty(SearchConstants.INDEXING_NODE, reindexBatchDescriptor.clusterNode);
            }
            msg.setIntProperty(SearchConstants.LAST_BATCH_NUM, reindexBatchDescriptor.lastBatchNum);
            jmsMessageCustomizer.customizeMessage(msg, null);
            return msg;
        });
        if (Queues.QUEUE_DOCUMENT_INDEXER.equals(reindexBatchDescriptor.targetQueue))
        {
            jmsStat.addDataObjectIndexerInbound();
        }
        else
        {
            jmsStat.addDataFileIndexerInbound();
        }
    }
}