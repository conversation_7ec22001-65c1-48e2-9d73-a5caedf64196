package ru.naumen.lucene.fts.server.lucene.jms;

import static ru.naumen.core.shared.Constants.IGNORE_CACHE;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

import jakarta.jms.JMSException;
import jakarta.jms.MapMessage;
import jakarta.jms.Message;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.eventaction.jms.JmsMessageAdditionalProcessor;
import ru.naumen.core.server.jms.listeners.IdentifiableMessageListener;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.fts.server.external.search.SearchConfiguration;
import ru.naumen.lucene.fts.server.lucene.LuceneConfiguration;
import ru.naumen.search.fts.server.lucene.SearchCommand;
import ru.naumen.search.fts.server.lucene.SearchConstants;
import ru.naumen.lucene.fts.server.lucene.ReprocessMessagesSender;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.DocumentBuilderService;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.DocumentBuilderService.DocumentBuildResult;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.UpdateDocumentEntity;
import ru.naumen.lucene.fts.server.lucene.mutator.MutationService;
import ru.naumen.search.fts.server.lucene.reindex.ReindexController;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 * Производит индексацию объектов, помещенных в очередь на индексацию, в несколько потоков
 * Количество потоков задается {JMSListenersConfiguration#INDEXING_THREAD_NUMBER}
 *
 * <AUTHOR>
 *
 */
public class LuceneIndexUpdaterMessageListener implements IdentifiableMessageListener
{
    private static void authAsIndexer()
    {
        final SecurityContext securityContext = SecurityContextHolder.getContext();
        securityContext.setAuthentication(INDEXER_AUTHENTICATION_TOKEN);
        SecurityContextHolder.setContext(securityContext);
    }
    private static final Logger LOG = LoggerFactory.getLogger(LuceneIndexUpdaterMessageListener.class);
    private static final String INDEXER = "indexer";
    private static final UsernamePasswordAuthenticationToken INDEXER_AUTHENTICATION_TOKEN =
            new UsernamePasswordAuthenticationToken(
                    new SuperUser(INDEXER, StringUtilities.EMPTY), INDEXER, SuperUser.ROLES);

    private final ReindexController reindexController;
    private final MutationService mutationService;
    private final DocumentBuilderService documentBuilderService;
    private final SearchConfiguration searchConfiguration;
    private final LuceneConfiguration luceneConfiguration;
    private final ReprocessMessagesSender reprocessMessagesSender;
    private JmsMessageAdditionalProcessor jmsMessageAdditionalProcessor;

    private final String listenerID;
    private final String clusterNodeID;

    //unbound queue
    private final LinkedBlockingQueue<UpdateDocumentEntity> entityQueue = new LinkedBlockingQueue<>();

    public LuceneIndexUpdaterMessageListener( //NOSONAR
            ReindexController reindexController,
            MutationService mutationService,
            DocumentBuilderService documentBuilderService,
            LuceneConfiguration luceneConfiguration,
            SearchConfiguration searchConfiguration,
            ReprocessMessagesSender reprocessMessagesSender,
            String listenerID,
            String clusterNodeID)
    {
        this.reprocessMessagesSender = reprocessMessagesSender;
        this.reindexController = reindexController;
        this.mutationService = mutationService;
        this.documentBuilderService = documentBuilderService;
        this.luceneConfiguration = luceneConfiguration;
        this.searchConfiguration = searchConfiguration;
        this.listenerID = listenerID;
        this.clusterNodeID = clusterNodeID;
    }

    @Override
    public String getID()
    {
        return listenerID;
    }

    @Override
    public void onMessage(Message message)
    {
        authAsIndexer();
        MapMessage mapMessage = (MapMessage)message;
        try
        {
            String indexingNode = mapMessage.getStringProperty(SearchConstants.INDEXING_NODE);
            if (indexingNode != null && !clusterNodeID.equals(indexingNode))
            {
                LOG.debug("Target node is not current one. This NodeID [{}], Target NodeID [{}]", clusterNodeID,
                        indexingNode);
                return;
            }
            if (message.getBooleanProperty(SearchConstants.REINDEX_ALL))
            {
                mutationService.dropAll();
                LOG.info("All objects has been deleted from index");
                return;
            }

            // uuid операции индексирования по нажатию кнопки или вызову reindexAll,
            // состоит из fqn индексируемого метакласса + id
            String operationUuid = mapMessage.getStringProperty(SearchConstants.OPERATION_UUID);
            // эта проверка нужна, чтобы обработать прерывание переиндексации через api.search.cancelReindexing()
            if (operationUuid != null && !reindexController.isReindexing(operationUuid))
            {
                LOG.info("Enqueued reindexing operation (ID {}) was cancelled", operationUuid);
                return;
            }

            doProcessMessage(mapMessage);
            if (operationUuid != null)
            {
                /*
                Нельзя просто взять числовую проперти если её нет в сообщении, упадет NumberFormatException
                LAST_BATCH_NUM приходит только если идет переиндексация и задан operationUuid
                 */
                final int lastBatchNum = mapMessage.getIntProperty(SearchConstants.LAST_BATCH_NUM);
                reindexController.batchProcessed(operationUuid, lastBatchNum);
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Сохранить очередь документов в индекс
     */
    void flushPending()
    {
        List<UpdateDocumentEntity> queuedElements = new ArrayList<>();
        entityQueue.drainTo(queuedElements, luceneConfiguration.getProcessingBatchSize());
        if (queuedElements.isEmpty())
        {
            return;
        }
        mutationService.storeDocuments(queuedElements, failedDocuments ->
        {
            Map<String, Object> forResend = new HashMap<>();
            for (UpdateDocumentEntity entity : failedDocuments)
            {
                forResend.put(entity.getUuid(), entity.getCommand().name());
            }
            reprocessMessagesSender.send(clusterNodeID, forResend, false);
        });
    }

    /**
     * Необходимо ли сохранить документы в индекс
     */
    boolean needFlush()
    {
        return entityQueue.size() > luceneConfiguration.getProcessingBatchSize();
    }

    /**
     * Метод выполняет формирование объекта Document и сохранение его во внутреннюю очередь
     * для последующей обработки = сохранению в индекс, что происходит в методе flushPending()
     */
    @SuppressWarnings("unchecked")
    private void doProcessMessage(MapMessage mapMessage) throws JMSException
    {
        final Map<String, Object> failedUuidsWithCmd = new HashMap<>();
        boolean isReprocess = mapMessage.getBooleanProperty(SearchConstants.REPROCESS_FLAG);
        boolean ignoreCache = mapMessage.getBooleanProperty(IGNORE_CACHE);
        int updateIndexTimeout = searchConfiguration.getIndexingPerObjectTimeout();
        List<String> uuids = Collections.list(mapMessage.getMapNames());
        try
        {
            for (String uuid : uuids)
            {
                jmsMessageAdditionalProcessor.beforeOnMessage(uuid, mapMessage);
                SearchCommand cmd = SearchCommand.valueOf(mapMessage.getString(uuid));
                TransactionRunner.run(TransactionType.NEW_READ_ONLY, updateIndexTimeout, () ->
                {
                    DocumentBuildResult documentBuildResult = documentBuilderService.buildDocument(uuid, cmd,
                            ignoreCache);
                    documentBuildResult.getErrorMessage().ifPresent(error ->
                    {
                        if (isReprocess)
                        {
                            LOG.warn(String.format(
                                    "Reprocess failed for %s. This object will not be indexed until next modification",
                                    uuid));
                        }
                        else
                        {
                            failedUuidsWithCmd.put(uuid, cmd.name());
                        }

                    });
                    documentBuildResult.getDocEntity().ifPresent(entityQueue::offer);
                });
            }
        }
        finally
        {
            if (!failedUuidsWithCmd.isEmpty())
            {
                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Sending for reprocessing. Objects {}", failedUuidsWithCmd.keySet());
                }
                reprocessMessagesSender.send(clusterNodeID, failedUuidsWithCmd, true);
            }
        }
    }

    public void setJmsMessageAdditionalProcessor(
            JmsMessageAdditionalProcessor jmsMessageAdditionalProcessor)
    {
        this.jmsMessageAdditionalProcessor = jmsMessageAdditionalProcessor;
    }
}
