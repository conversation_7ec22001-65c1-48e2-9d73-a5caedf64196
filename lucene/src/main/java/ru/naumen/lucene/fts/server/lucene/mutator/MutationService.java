package ru.naumen.lucene.fts.server.lucene.mutator;

import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.function.Consumer;

import jakarta.inject.Inject;

import org.apache.lucene.index.CorruptIndexException;
import org.apache.lucene.store.AlreadyClosedException;
import org.apache.lucene.store.LockObtainFailedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.lucene.fts.server.lucene.LuceneIndexUpdater;
import ru.naumen.lucene.fts.server.lucene.ReprocessMessagesSender;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.UpdateDocumentEntity;
import ru.naumen.lucene.fts.server.lucene.mutator.actions.DeleteIndexAction;
import ru.naumen.lucene.fts.server.lucene.mutator.actions.StoreDocumentsAction;
import ru.naumen.search.fts.server.lucene.pressure.IndexingBackPressureController;

/**
 * Сервис мутации индекса
 * <AUTHOR>
 * @since 01.10.18
 */
@Component
public class MutationService
{

    private static final Logger LOG = LoggerFactory.getLogger(MutationService.class);
    private final LuceneIndexUpdater luceneIndexUpdater;
    private final IndexMutator indexMutator;
    private final IndexingBackPressureController backPressureController;

    @Inject
    public MutationService(LuceneIndexUpdater luceneIndexUpdater,
            IndexMutator indexMutator,
            IndexingBackPressureController backPressureController)
    {
        this.luceneIndexUpdater = luceneIndexUpdater;
        this.indexMutator = indexMutator;
        this.backPressureController = backPressureController;
    }

    /**
     * Удалить индекс
     * @throws IOException
     */
    public void dropAll() throws IOException
    {
        try
        {
            indexMutator.mutate(DeleteIndexAction.INSTANCE);
        }
        catch (CorruptIndexException | EOFException | IllegalArgumentException | FileNotFoundException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Сохранить пачку документов в индекс
     * @param batch пачка
     * @param beforeErrorThrow сallback, выполняемый перед выбросом ошибки
     * @see ReprocessMessagesSender
     */
    public void storeDocuments(List<UpdateDocumentEntity> batch,
            Consumer<List<UpdateDocumentEntity>> beforeErrorThrow)
    {
        LOG.info("Saving {} objects to index", batch.size());
        try
        {
            long startTransaction = System.currentTimeMillis();
            indexMutator.mutate(new StoreDocumentsAction(luceneIndexUpdater, batch));
            long endTransaction = System.currentTimeMillis();
            backPressureController.storeIndexingTime(endTransaction - startTransaction);
        }
        catch (LockObtainFailedException e)
        {
            LOG.debug(e.toString(), e);
        }
        //Известные мне типы ошибок когда с индексом работать невозможно
        catch (CorruptIndexException | EOFException | IllegalArgumentException | FileNotFoundException e)
        {
            throw new FxException(e);
        }
        //При отправке сообщений из-за IO и ООМ не помечаются как переобработку
        //т.к. это довольно исключительные ситуации, IO, например, возникает при
        //проблемах коммита в индекс и в таком случае лучше переобработать без
        //вероятности попадания объектов в DeadLetterQueue
        catch (IOException | AlreadyClosedException io)
        {
            beforeErrorThrow.accept(batch);
            throw new FxException(io);
        }
        catch (OutOfMemoryError oom)
        {
            beforeErrorThrow.accept(batch);
            throw oom;
        }
    }

}
