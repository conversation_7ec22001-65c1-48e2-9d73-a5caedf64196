package ru.naumen.lucene.fts.server.lucene.mutator.actions;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import edu.umd.cs.findbugs.annotations.CheckForNull;

import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.IndexWriter;

import ru.naumen.lucene.fts.server.lucene.LuceneIndexUpdater;
import ru.naumen.search.fts.server.lucene.docbuilders.spi.UpdateDocumentEntity;
import ru.naumen.lucene.fts.server.lucene.mutator.MutationService;

/**
 * Действие сохранения сформированных документов в индекс
 * <AUTHOR>
 * @since 27.11.2019
 * @see MutationService#storeDocuments
 */
public final class StoreDocumentsAction implements MutateAction
{
    private final LuceneIndexUpdater luceneIndexUpdater;
    private final List<UpdateDocumentEntity> batchToStore;

    public StoreDocumentsAction(LuceneIndexUpdater luceneIndexUpdater,
            List<UpdateDocumentEntity> batchToStore)
    {
        this.luceneIndexUpdater = luceneIndexUpdater;
        this.batchToStore = batchToStore;
    }

    @Override
    public String getActionID()
    {
        return "Store documents in index";
    }

    @Override
    public void perform(IndexWriter writer, @CheckForNull IndexReader reader) throws IOException
    {
        Objects.requireNonNull(reader, "Reader should never be null here");
        luceneIndexUpdater.updateDocuments(writer, reader, batchToStore);
    }

    @Override
    public boolean shouldPerformWithReader()
    {
        return true;
    }
}