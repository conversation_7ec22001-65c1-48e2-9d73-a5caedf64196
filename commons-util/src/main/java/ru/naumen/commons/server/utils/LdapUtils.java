package ru.naumen.commons.server.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.annotation.Nullable;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Вспомогательные методы для работы с LDAP
 *
 * <AUTHOR>
 *
 */
public class LdapUtils
{
    private static final Logger LOG = LoggerFactory.getLogger(LdapUtils.class);

    //Разделитель для списка групп пользователя. используется точка с запятой, так как в написании группы
    // присутствует запятая.
    private static final String USER_GROUPS_DELIMETER = ";";
    private static final Pattern FULL_DOMAIN_PATTERN = Pattern.compile("DC=([^,]*)");

    private static final Pattern PARENT_DOMAIN_PATTERN = Pattern.compile("(\\\\*),");

    @Nullable
    public static String determineElementSearchFilter(String dn, Attributes attributes, String idAttributeName)
    {
        try
        {
            String objectGUID = getValue(attributes.get(idAttributeName));
            if (!StringUtilities.isEmpty(objectGUID))
            {
                return objectGUID;
            }

            String dn_lower = dn.toLowerCase();
            if (dn_lower.startsWith("ou"))
            {
                return escapeSpecialCharacters(getValue(attributes.get("ou"), ""));
            }
            else if (dn_lower.startsWith("uid"))
            {
                return getValue(attributes.get("uid"), "");
            }
            return null;
        }
        catch (NamingException e)
        {
            LOG.warn("Wrong attribute name", e);
            return null;
        }
    }

    public static String escapeSpecialCharacters(String dn)
    {
        // @formatter:off
        //Запятая и равно должны оставаться в виде \\, и \\= 
        dn = dn.replaceAll("\\\\\\\\", "\\\\5C");
 
        //Если Вы добавляете символ в эти replace-ы, вероятно, Вам нужно добавить его в LdapUtils.prepareDNStringFromConfig(String)
        //Независимо от того: просто + или \\+ , его нужно заменить на \\2B, кроме /
        dn = dn.replaceAll("\\\\(?=[\"#+<>;]|$)", "");        
        dn = dn.replace("\"", "\\22")
               .replace("#",  "\\23")
               .replace("+",  "\\2B")
               .replace("/",  "\\2F")
               .replace(";",  "\\3B")
               .replace("<",  "\\3C")
               .replace(">",  "\\3E");        
        return dn;         
        // @formatter:on
    }

    public static String extractFullDomainName(String domainName)
    {
        String domainNameUpper = domainName.toUpperCase();

        Matcher matcher = FULL_DOMAIN_PATTERN.matcher(domainNameUpper);
        List<String> domainNameParts = new ArrayList<>();
        while (matcher.find())
        {
            domainNameParts.add(matcher.group(1));
        }

        return StringUtilities.join(domainNameParts, ".");
    }

    public static String extractLogin(String dn, Attributes attributes, boolean fullDomain) throws NamingException
    {
        String res = getValue(attributes.get("sAMAccountName"), null);
        String domain = fullDomain ? extractFullDomainName(dn) : extractShortDomainName(dn);
        if (!StringUtilities.isEmpty(domain))
        {
            res += "@" + domain;
        }
        return res;
    }

    public static String extractShortDomainName(String dn)
    {
        String dn_upper = dn.toUpperCase();
        int begin = dn_upper.indexOf("DC=");
        if (begin == -1)
        {
            return "";
        }
        int end = dn_upper.indexOf(',', begin);
        if (end == -1)
        {
            end = dn_upper.length();
        }
        return dn_upper.substring(begin + 3, end);
    }

    /**
     * Заменить все двойные обратные слэши на одинарные
     *
     * @param dn - distinguished name
     * @return - исправленный distinguished name
     */
    public static String fixSlashes(String dn)
    {
        return dn.replaceAll("\\\\{2}", Matcher.quoteReplacement("\\"));
    }

    /**
     * Получение домена из имени пользователя
     */
    public static String getDomain(String username)
    {
        String[] parts = getSplittedUserName(username);
        if (null == parts)
        {
            return null;
        }
        if (parts.length > 1)
        {
            return parts[1];
        }
        return "";
    }

    /**
     * Получение логина из имени пользователя
     */
    public static String getLogin(String username)
    {
        String[] parts = getSplittedUserName(username);
        if (null == parts)
        {
            return null;
        }
        return parts[0];
    }

    /**
     *
     * @param domainName
     * @return - родительский узел
     */
    public static String getParentDN(String domainName)
    {
        //Запятая является разделительной, если перед ней нет слешей, либо количество слешей четно
        Matcher matcher = PARENT_DOMAIN_PATTERN.matcher(domainName);
        while (matcher.find())
        {
            if (matcher.group(1).length() % 2 == 0)
            {
                return domainName.substring(matcher.end(1) + 1);
            }
        }
        return null;
    }

    /**
     * @param attr
     * @return значение атрибута
     * @throws NamingException
     */
    @Nullable
    public static String getValue(@Nullable Attribute attr) throws NamingException
    {
        if (attr == null)
        {
            return null;
        }
        Object valueObj = 1 < attr.size() ? attr.getAll() : attr.get();
        if (valueObj == null)
        {
            return null;
        }
        if (valueObj instanceof byte[])
        {
            byte[] bytes = (byte[])valueObj;

            StringBuilder res = new StringBuilder();
            for (byte aByte : bytes)
            {
                int bb = aByte & 0xFF;
                res.append('\\');
                if (bb <= 0x0F)
                {
                    res.append('0');
                }
                res.append(Integer.toHexString(bb));
            }
            return res.toString();
        }
        else if (valueObj instanceof Enumeration<?>)
        {
            Enumeration<?> valueEnum = (Enumeration<?>)valueObj;
            Collection<String> values = new HashSet<>();
            while (valueEnum.hasMoreElements())
            {
                Object v = valueEnum.nextElement();
                values.add(String.valueOf(v).trim());
            }
            return StringUtilities.join(values, USER_GROUPS_DELIMETER);
        }
        return String.valueOf(valueObj).trim();
    }

    /**
     * Возвращает значение атрибута, а если атрибут не задан или имеет пустое значение, то возвращает значение
     * по умолчанию. 
     *
     * @param attr
     * @param defaultValue
     * @return значение атрибута или defaultValue
     * @throws NamingException
     */
    public static String getValue(Attribute attr, String defaultValue) throws NamingException
    {
        String value = getValue(attr);
        if (StringUtilities.isEmpty(value))
        {
            return defaultValue;
        }
        return value;
    }

    /**
     * Проверяет сотояние пользователя: являетсяли пользователь заблокированным
     *
     * @param attributes
     * @return true - пользователь заблокирован, false - иначе
     * @throws NamingException
     */
    public static boolean isUserDisabled(Attributes attributes) throws NamingException
    {
        String codeStr = getValue(attributes.get("userAccountControl"), "");
        if (StringUtilities.isEmpty(codeStr))
        {
            return false;
        }

        int code = Integer.parseInt(codeStr);
        // check second bit (1 - user disabled, 0 - user enabled)
        // see http://support.microsoft.com/kb/305144
        return (code & 0x2) == 0x2;
    }

    public static Collection<String> prepareCollection(Collection<String> elements)
    {
        Collection<String> escapeElements = new HashSet<String>();
        for (String item : elements)
        {
            escapeElements.add(LdapUtils.prepareDNStringFromConfig(item));
        }
        return escapeElements;
    }

    /**
     * Метод выполняет преобразование строки из конфигурации. В частности: экранирование спец. символов: /"#+<>;
     * Символы ,=\ должны быть экранированы заранее в самой конфигурации
     */
    public static String prepareDNStringFromConfig(String rootDN)
    {
        String dn = rootDN;
        if (!StringUtilities.isEmptyTrim(dn))
        {
            dn = dn.replaceAll("([\"#+<>;])", "\\\\$1");
        }
        return dn;
    }

    /**
     * Метод выполняет преобразование строки фильтра. В частности: экранирование спец. символов: /"#+<>;
     * и удвоение слешей
     */
    public static String prepareFilterStringFromConfig(String filterString)
    {
        String dn = filterString;
        if (!StringUtilities.isEmptyTrim(dn))
        {
            dn = dn.replaceAll("([\"#+<>;])", "\\\\$1");
            dn = dn.replaceAll("\\\\", "\\\\\\\\");
        }
        return dn;
    }

    /**
     * Получение разделенных логина и домена из имени пользователя (логин, домен)
     */
    private static String[] getSplittedUserName(String username)
    {
        if (null == username)
        {
            return null;
        }
        String[] parts = username.split("@");
        if (parts.length > 1)
        {
            return parts;
        }
        parts = username.split("\\\\");
        if (parts.length > 1)
        {
            return new String[] { parts[1], parts[0] };
        }
        return new String[] { username, "" };
    }
}
