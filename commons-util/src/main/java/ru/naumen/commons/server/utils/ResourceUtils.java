package ru.naumen.commons.server.utils;

import java.lang.annotation.Annotation;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import org.reflections.vfs.Vfs;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

/**
 *  Вспомогательные методы для автообнаружения ресурсов приложения
 *  По-умолчанию поиск выполняется в пакете ru.naumen
 *
 *  <AUTHOR>
 *
 */
@Component
public class ResourceUtils
{
    /**
     * Фильтр ресурсов
     *
     */
    public interface Filter
    {
        boolean apply(Class<? extends Annotation> resource);
    }

    public interface Function<T>
    {
        /**
         * Вызывается для выполнения процедуры над объектом
         * @param object значение параметра процедуры
         */
        void execute(T object);
    }

    private static final String NAUMEN_PKG = "ru.naumen";

    private static final Reflections resourceReflections = new Reflections(new ConfigurationBuilder()
            .setUrls(ClasspathHelper.forPackage(NAUMEN_PKG))
            .setScanners(Scanners.Resources));
    private final Reflections annotationReflections;

    public ResourceUtils()
    {
        annotationReflections = hasReflectionsFiles() ? Reflections.collect() : createReflection();
    }

    private static boolean hasReflectionsFiles()
    {
        String loc = "META-INF/reflections";
        Collection<URL> urls = ClasspathHelper.forPackage(loc);
        FilterBuilder filterBuilder = new FilterBuilder().includePattern(".*-reflections.xml");
        return Vfs.findFiles(urls, loc, filterBuilder).iterator().hasNext();
    }

    /**
     * Поиск классов по аннотациям
     * @apiNote Порядок аннотаций в варарге важен - классы ищутся последовательно
     * i.e [MetaClass,Catalog] сначала будут найденны метаклассы, а уже в них каталоги. итд
     * @param classes - Классы аннотаций по которым производится поиск
     * @return классы, на которых присутствуют <strong>все</strong> аннотации
     */
    @SafeVarargs
    public final List<Class<?>> findClassesByAnnotation(Class<? extends Annotation>... classes)
    {
        return findClasses(classes);
    }

    /**
     * Производит поиск ресурсов с расширением extension
     *
     * @param extension
     *            расширение файлов удовлетворяющих фильтру
     * @return список ресурсов с данным расширением
     */
    public List<Resource> findFilesWithExtension(String extension)
    {
        return resourceReflections.getResources(Pattern.compile(".*\\." + extension))
                .stream()
                .map(ClassPathResource::new)
                .collect(Collectors.toList());
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public <A extends Annotation, O> void handleClassesWithAnnotation(Class<A> annotationClass,
            Function<Class<O>> function)
    {
        List<Class<?>> result = findClassesByAnnotation(annotationClass);
        for (Class clazz : result)
        {
            function.execute(clazz);
        }
    }

    private Reflections createReflection()
    {
        return new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(NAUMEN_PKG))
                .setScanners(Scanners.TypesAnnotated));
    }

    @SuppressWarnings("unchecked")
    private List<Class<?>> findClasses(Class<? extends Annotation>... annotations)
    {
        return Stream.of(annotations)
                .map(annotationReflections::getTypesAnnotatedWith)
                .reduce((sinkClasses, classes) ->
                {
                    sinkClasses.retainAll(classes);
                    return sinkClasses;
                }).map(Lists::newArrayList).orElseGet(ArrayList::new);
    }

}
