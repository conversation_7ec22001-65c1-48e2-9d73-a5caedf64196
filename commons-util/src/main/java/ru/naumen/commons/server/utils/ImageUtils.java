package ru.naumen.commons.server.utils;

import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.color.ICC_ColorSpace;
import java.awt.color.ICC_Profile;
import java.awt.image.BufferedImage;
import java.awt.image.ColorConvertOp;
import java.awt.image.Raster;
import java.awt.image.WritableRaster;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.Iterator;
import java.util.Set;

import jakarta.annotation.Nullable;

import javax.imageio.IIOException;
import javax.imageio.ImageIO;
import javax.imageio.ImageReadParam;
import javax.imageio.ImageReader;
import javax.imageio.ImageTypeSpecifier;
import javax.imageio.stream.ImageInputStream;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Sets;

/**
 * Утилитарный класс для работы с картинками на сервере
 * <AUTHOR>
 * @since 10.04.2014
 */
public class ImageUtils
{
    public static class ImageFromADToResizeParams
    {
        private int height;
        private int width;
        private byte[] imageBytes;
        private String contentType;
        private Integer imageType;
        private boolean resizeOnlyBig;

        public ImageFromADToResizeParams(int height, int width, byte[] imageBytes, String contentType,
                Integer imageType, boolean resizeOnlyBig)
        {
            this.height = height;
            this.width = width;
            this.imageBytes = imageBytes;
            this.contentType = contentType;
            this.imageType = imageType;
            this.resizeOnlyBig = resizeOnlyBig;
        }

        public String getContentType()
        {
            return contentType;
        }

        public int getHeight()
        {
            return height;
        }

        public byte[] getImageBytes()
        {
            return imageBytes;
        }

        public Integer getImageType()
        {
            return imageType;
        }

        public int getWidth()
        {
            return width;
        }

        public boolean isResizeOnlyBig()
        {
            return resizeOnlyBig;
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(ImageUtils.class);

    public static final int COLOR_TYPE_RGB = 1;
    public static final int COLOR_TYPE_CMYK = 2;
    public static final int COLOR_TYPE_YCCK = 3;

    private static int colorType = COLOR_TYPE_RGB; // NOSONAR NOPMD
    private static boolean hasAdobeMarker = false;

    /**
     * Проверить, что файл содержит изображение
     * ВНИМАНИЕ! После выполнения метода переданный в него поток для дальнейшего чтения не пригоден
     *
     * @param is - поток содержимого проверяемого файла
     * @return true в случае если файл содержит изображение
     */
    public static boolean checkImageFormat(InputStream is) throws IOException
    {
        return null != getFormat(is);
    }

    /**
     * Если поток содержит изображение, то вернуть формат этого изображения, иначе null
     */
    @Nullable
    private static String getFormat(final InputStream input) throws IOException
    {
        try (final ImageInputStream stream = ImageIO.createImageInputStream(input))
        {
            final Iterator<ImageReader> imageReaders = ImageIO.getImageReaders(stream);

            if (!imageReaders.hasNext())
            {
                return null;
            }

            final ImageReader imageReader = imageReaders.next();
            try
            {
                imageReader.setInput(stream, true, true);
                return imageReader.getFormatName();
            }
            finally
            {
                imageReader.dispose();
            }
        }
    }

    /**
     * Метод увеличивает изображение, заполняя свободное место прозрачными пикселями и устанавливая 
     * по центру исходное изображение.
     *
     * @param image исходное изображение 
     * @param newWidth новое значение ширины изображения
     * @param newHeight новое значение высоты изображения
     *
     * @return увеличенное изображение
     */
    public static BufferedImage expandImageBoundaries(BufferedImage image, int newWidth, int newHeight)
    {
        if (image == null || image.getWidth() > newWidth || image.getHeight() > newHeight)
        {
            return image;
        }
        BufferedImage result = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = result.createGraphics();
        graphics.drawImage(image, (newWidth - image.getWidth()) / 2, (newHeight - image.getHeight()) / 2,
                image.getWidth(), image.getHeight(), null);
        graphics.dispose();
        return result;
    }

    /**
     * Получить список поддерживаемых форматов для изображений
     */
    public static Set<String> getSupportedImageFormats()
    {
        Set<String> formats = getSupportedRasterImageFormats();

        if (!formats.isEmpty())
        {
            formats.add("svg+xml");
            formats.add("svg+xml-compressed");
            formats.add("svg");
            formats.add("svgz");
            formats.add("x-ms-bmp");
        }

        return formats;
    }

    /**
     * Получить список поддерживаемых форматов для растровых изображений
     */
    public static Set<String> getSupportedRasterImageFormats()
    {
        String[] formats = ImageIO.getWriterFormatNames();
        return formats == null ? Collections.emptySet() : Sets.newHashSet(formats);
    }

    /**
     * Метод выполняет увеличение размеров изображения формата gif, если он не удовлетворяет 
     * минимально допустимым значениям.
     *
     * @param image исходное изображение
     * @param minSize минимальный размер иконок формата gif (width * height)
     * @param maxHeight максимально допустимая высота для иконок
     *
     * @return увеличенное изображение, если размеры иконки не соответствуют минимально допустимым
     */
    public static BufferedImage normalizeGifSize(BufferedImage image, int minSize, int maxHeight)
    {
        if (image == null)
        {
            return image;
        }
        if (image.getHeight() * image.getWidth() < minSize)
        {
            int newWidth = minSize / maxHeight + ((minSize % maxHeight == 0) ? 0 : 1);
            return expandImageBoundaries(image, newWidth, maxHeight);
        }
        return image;
    }

    /**
     * Считывает изображение в память {@link BufferedImage}
     * ВНИМАНИЕ! После выполнения метода переданный в него поток для дальнейшего чтения не пригоден
     * @param is файловый поток содержащий изображение
     * @return изображение загруженная в память
     * @throws IOException
     */
    public static BufferedImage readImage(InputStream is) throws IOException
    {
        try
        {
            is.mark(Integer.MAX_VALUE);
            return ImageIO.read(is);
        }
        catch (IllegalArgumentException | IIOException e)
        {
            LOG.debug("There is some trouble reading image from input stream.");
            is.reset();
            return readImageWithTrouble(is);
        }
    }

    public static byte[] resizeImageFromAD(ImageFromADToResizeParams imageToResizeParams) throws IOException
    {
        final byte[] imageBytes = imageToResizeParams.getImageBytes();
        return resizeImage(imageToResizeParams.getHeight(), imageToResizeParams.getWidth(),
                new ByteArrayInputStream(imageBytes), imageBytes, imageToResizeParams.contentType,
                imageToResizeParams.imageType, imageToResizeParams.isResizeOnlyBig());
    }

    public static byte[] resizeImageWithRatio(FileItem fileLogo, int height, int width)
    {
        try
        {
            InputStream inputStream = fileLogo.getInputStream();
            byte[] imageBytes = fileLogo.get();
            String contentType = fileLogo.getContentType();
            return resizeImage(height, width, inputStream, imageBytes, contentType, null, true);
        }
        catch (IOException e)
        {
            LOG.error("Error converting image ", e);
            return ArrayUtils.EMPTY_BYTE_ARRAY;
        }
    }

    private static String getExtensionFromContentType(String fileContentType)
    {
        return fileContentType.substring(fileContentType.indexOf('/') + 1);
    }

    /**
     * Считывает изображение в память {@link BufferedImage}
     * Используется для изображений с которыми {@link ImageIO#read(InputStream)} не справляется
     * @param is файловый поток содержащий изображение
     * @return изображение загруженная в память
     * @throws IOException
     */
    private static BufferedImage readImageWithTrouble(InputStream is) throws IOException
    {
        BufferedImage bufferedImage = null;
        try (ImageInputStream iis = ImageIO.createImageInputStream(is))
        {
            Iterator<ImageReader> iter = ImageIO.getImageReaders(iis);
            while (iter.hasNext() && bufferedImage == null)
            {
                ImageReader reader = iter.next();
                ImageReadParam param = reader.getDefaultReadParam();
                reader.setInput(iis, true, true);
                Iterator<ImageTypeSpecifier> imageTypes = null;
                try
                {
                    imageTypes = reader.getImageTypes(0);
                }
                catch (IOException e)
                {
                    return bufferedImage;
                }

                while (imageTypes.hasNext())
                {
                    ImageTypeSpecifier imageTypeSpecifier = imageTypes.next();
                    int bufferedImageType = imageTypeSpecifier.getBufferedImageType();
                    if (bufferedImageType == BufferedImage.TYPE_BYTE_GRAY)
                    {
                        param.setDestinationType(imageTypeSpecifier);
                        break;
                    }
                }
                ICC_Profile profile = null;
                try
                {
                    bufferedImage = reader.read(0);
                }
                catch (IIOException e)
                {
                    if (reader.canReadRaster())
                    {
                        colorType = COLOR_TYPE_CMYK;
                        WritableRaster raster = (WritableRaster)reader.readRaster(0, null);
                        if (colorType == COLOR_TYPE_YCCK)
                        {
                            convertYcckToCmyk(raster);
                        }
                        if (hasAdobeMarker)
                        {
                            convertInvertedColors(raster);
                        }
                        bufferedImage = convertCmykToRgb(raster, profile);
                    }
                }

                if (null != reader)
                {
                    reader.dispose();
                }
            }
        }
        return bufferedImage;
    }

    /**
     * Преобразует цветовую схему УCCK в CMYK
     * @param raster растр с данными о пикселях изображения
     */
    private static void convertYcckToCmyk(WritableRaster raster)
    {
        int height = raster.getHeight();
        int width = raster.getWidth();
        int stride = width * 4;
        int[] pixelRow = new int[stride];
        for (int h = 0; h < height; h++)
        {
            raster.getPixels(0, h, width, 1, pixelRow);

            for (int x = 0; x < stride; x += 4)
            {
                int y = pixelRow[x];
                int cb = pixelRow[x + 1];
                int cr = pixelRow[x + 2];

                int c = (int)(y + 1.402 * cr - 178.956);
                int m = (int)(y - 0.34414 * cb - 0.71414 * cr + 135.95984);
                y = (int)(y + 1.772 * cb - 226.316);

                if (c < 0)
                {
                    c = 0;
                }
                else if (c > 255)
                {
                    c = 255;
                }
                if (m < 0)
                {
                    m = 0;
                }
                else if (m > 255)
                {
                    m = 255;
                }
                if (y < 0)
                {
                    y = 0;
                }
                else if (y > 255)
                {
                    y = 255;
                }

                pixelRow[x] = 255 - c;
                pixelRow[x + 1] = 255 - m;
                pixelRow[x + 2] = 255 - y;
            }

            raster.setPixels(0, h, width, 1, pixelRow);
        }
    }

    /**
     * Преобразует инвертированные значения цветов для adobe изображений
     * @param raster растр с данными о пикселях изображения
     */
    private static void convertInvertedColors(WritableRaster raster)
    {
        int height = raster.getHeight();
        int width = raster.getWidth();
        int stride = width * 4;
        int[] pixelRow = new int[stride];
        for (int h = 0; h < height; h++)
        {
            raster.getPixels(0, h, width, 1, pixelRow);
            for (int x = 0; x < stride; x++)
            {
                pixelRow[x] = 255 - pixelRow[x];
            }
            raster.setPixels(0, h, width, 1, pixelRow);
        }
    }

    /**
     * Преобразует цветовую схему CMYK в RGB
     * @param cmykRaster растр
     * @param cmykProfile профиль изображения
     * @return преобразованное изображение
     * @throws IOException
     */
    private static BufferedImage convertCmykToRgb(Raster cmykRaster, ICC_Profile cmykProfile) throws IOException
    {
        if (cmykProfile == null)
        {
            cmykProfile = ICC_Profile.getInstance(ImageUtils.class.getResourceAsStream("Generic CMYK Profile.icc"));
        }

        if (cmykProfile.getProfileClass() != ICC_Profile.CLASS_DISPLAY)
        {
            byte[] profileData = cmykProfile.getData();

            if (profileData[ICC_Profile.icHdrRenderingIntent] == ICC_Profile.icPerceptual)
            {
                intToBigEndian(ICC_Profile.icSigDisplayClass, profileData,
                        ICC_Profile.icHdrDeviceClass); // Header is first

                cmykProfile = ICC_Profile.getInstance(profileData);
            }
        }

        ICC_ColorSpace cmykCS = new ICC_ColorSpace(cmykProfile);
        BufferedImage rgbImage = new BufferedImage(cmykRaster.getWidth(), cmykRaster.getHeight(),
                BufferedImage.TYPE_INT_RGB);
        WritableRaster rgbRaster = rgbImage.getRaster();
        ColorSpace rgbCS = rgbImage.getColorModel().getColorSpace();
        ColorConvertOp cmykToRgb = new ColorConvertOp(cmykCS, rgbCS, null);
        cmykToRgb.filter(cmykRaster, rgbRaster);
        return rgbImage;
    }

    private static void intToBigEndian(int value, byte[] array, int index)
    {
        array[index] = (byte)(value >> 24);
        array[index + 1] = (byte)(value >> 16);
        array[index + 2] = (byte)(value >> 8);
        array[index + 3] = (byte)(value);
    }

    /**
     * Масштабировать картинку
     * @param input исходная картинка
     * @param width нужная ширина
     * @param height нужная высота
     * @return картинка с заданными width и height
     */
    public static BufferedImage resizeImage(BufferedImage input, int width, int height)
    {
        int outputType = input.getType();
        return resizeImage(input, width, height, outputType);
    }

    /**
     * Масштабировать картинку
     * @param input исходная картинка
     * @param width нужная ширина
     * @param height нужная высота
     * @param outputType тип результирующей картинки
     * @return картинка с заданными width и height
     */
    public static BufferedImage resizeImage(BufferedImage input, int width, int height, int outputType)
    {
        BufferedImage outputImage = new BufferedImage(width, height, outputType);
        Graphics2D g2d = outputImage.createGraphics();
        g2d.drawImage(input, 0, 0, width, height, null);
        g2d.dispose();
        return outputImage;
    }

    /**
     * Записать картинку в файл
     * @param inputImage входное изображение
     * @param outputType выходной формат файла
     * @param outputPath полный путь до выходного файла
     * @throws IOException
     */
    public static void writeImage(BufferedImage inputImage, String outputType, String outputPath)
            throws IOException
    {
        ImageIO.write(inputImage, outputType, new java.io.File(outputPath));
    }

    /**
     * Записать картинку в файл
     * @param inputImage входное PNG изображение
     * @param outputPath полный путь до выходного файла
     * @throws IOException
     */
    public static void writePNGImage(BufferedImage inputImage, String outputPath)
            throws IOException
    {
        ImageIO.write(inputImage, "png", new java.io.File(outputPath));
    }

    /**
     * Изменение размера изображения
     * @param height требуемая высота изображения
     * @param width требуемая широта изображения
     * @param inputStream inputStream исходного изображения
     * @param imageBytes массив байт исходного изображения
     * @param contentType тип изображения
     * @param imageType тип изображения для перевода в BufferedImage
     * @param resizeOnlyBig true, если нужно только уменьшать изображение, превышающее указанные размеры,
     *  false - если нужно приводить все изображения к одному размеру
     * @return
     * @throws IOException
     */
    private static byte[] resizeImage(int height, int width, InputStream inputStream, byte[] imageBytes,
            String contentType, Integer imageType, boolean resizeOnlyBig) throws IOException
    {
        ByteArrayOutputStream baos;
        BufferedImage image = readImage(inputStream);
        if (null == image)
        {
            return ArrayUtils.EMPTY_BYTE_ARRAY;
        }

        if (resizeOnlyBig && image.getHeight() <= height && image.getWidth() <= width)
        {
            return imageBytes;
        }
        if (!resizeOnlyBig && (image.getHeight() == height && image.getWidth() == width))
        {
            return imageBytes;
        }
        double ratio = Math.min(height / (double)image.getHeight(), width / (double)image.getWidth());
        int scaledWidth = (int)(image.getWidth() * ratio);
        int scaledHeight = (int)(image.getHeight() * ratio);
        int buffImgType = imageType == null ? image.getType() : imageType;

        BufferedImage outputImage = resizeImage(image, scaledWidth, scaledHeight, buffImgType);

        baos = new ByteArrayOutputStream();
        ImageIO.write(outputImage, getExtensionFromContentType(contentType), baos);
        return baos.toByteArray();
    }
}
