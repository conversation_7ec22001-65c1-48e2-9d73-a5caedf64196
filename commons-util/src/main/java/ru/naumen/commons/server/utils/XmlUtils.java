package ru.naumen.commons.server.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.PublicKey;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.XMLConstants;
import javax.xml.crypto.AlgorithmMethod;
import javax.xml.crypto.KeySelector;
import javax.xml.crypto.KeySelectorResult;
import javax.xml.crypto.XMLCryptoContext;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.DigestMethod;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignatureMethod;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.Transform;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.dom.DOMValidateContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.KeyValue;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.w3c.dom.Attr;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.ls.DOMImplementationLS;
import org.w3c.dom.ls.LSOutput;
import org.w3c.dom.ls.LSSerializer;
import org.xml.sax.EntityResolver;
import org.xml.sax.ErrorHandler;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Marshaller.Listener;
import jakarta.xml.bind.Unmarshaller;
import jakarta.xml.bind.ValidationEventHandler;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.InvalidXmlException;
import ru.naumen.commons.shared.utils.XMLChar;

/**
 * Вспомогательные методы для работы с XML
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("restriction")
@Component
public class XmlUtils
{
    private static final class CustomErrorHandler implements ErrorHandler
    {
        @Override
        public void error(SAXParseException exception)
        {
            logger.debug(exception.toString(), exception);
        }

        @Override
        public void fatalError(SAXParseException exception)
        {
            logger.atDebug().log(exception.toString(), exception);
        }

        @Override
        public void warning(SAXParseException exception)
        {
            logger.atDebug().log(exception.toString(), exception);
        }
    }

    public static final String USER_DIR_PARAM = "user.dir";
    public static final String EXT_DIR_PARAM = "ext.prop.dir";
    public static final String DTD_EXT = ".dtd";
    public static final String FILE_SCHEME = "file";
    public static final String SIGNATURE_TAG_NAME = "Signature";
    private static final Pattern TYPE_PATTERN = Pattern.compile("(xsi:type=\")(\\w+:)");
    private static final Pattern CLOSE_PREFIX_PATTERN = Pattern.compile("(</)(\\w+:)(.*?>)");
    private static final Pattern OPEN_PREFIX_PATTERN = Pattern.compile("(<)(\\w+:)(.*?>)");
    private static final Pattern NAU_XLMNS_PATTERN = Pattern.compile("xmlns([^\"])*?(\"http://www.naumen.ru/sd40\")");
    private static final String EMPTY_XMLNS = "xmlns=\"\"";

    private static final String LOAD_EXTERNAL_DTD = "http://apache.org/xml/features/nonvalidating/load-external-dtd";

    private static final Logger logger = LoggerFactory.getLogger(XmlUtils.class);

    /**
     * Резолвер для DTD файлов
     * see NSDPRD-4583
     */
    private static final EntityResolver DTD_FILE_RESOLVER = new EntityResolver()
    {
        @Override
        public InputSource resolveEntity(String publicId, String systemId) throws IOException
        {
            if (logger.isDebugEnabled())
            {
                logger.debug("Resolving entity for publicId = " + publicId + " and systemId = " + systemId);
            }
            //проверяем, что ищем файл в файловой системе и что у него расширение dtd
            final URI fileUri = URI.create(systemId);
            if (systemId.endsWith(DTD_EXT) && FILE_SCHEME.equals(fileUri.getScheme()))
            {
                final Path filePath = resolveDtdFilePath(fileUri);
                if (filePath != null)
                {
                    final File file = filePath.toFile();
                    if (file.exists())
                    {
                        return new InputSource(new FileReader(file));
                    }
                }
                logger.warn("Cant resolve filePath for dtd file " + systemId);
            }
            return null;
        }

        /**
         * Исправляет путь до DTD файла таким образом, чтобы путь ссылался на директорию {ext.prop.dir}
         * @param fileUri - оригинальный путь
         * @return корректный путь до файла
         */
        private static Path resolveDtdFilePath(URI fileUri)
        {
            final Path fileAbsolutePath = Paths.get(fileUri);
            final Path userDirPath = Paths.get(System.getProperty(USER_DIR_PARAM));
            if (!fileAbsolutePath.startsWith(userDirPath))
            {
                return null;
            }
            final Path path = userDirPath.relativize(fileAbsolutePath);
            if (path.isAbsolute())
            {
                return path;
            }
            final Path extDirPath = Paths.get(System.getProperty(EXT_DIR_PARAM));
            return extDirPath.resolve(path);
        }
    };

    /**
     * Закрывающий тег по имени
     */
    public static String closeTag(String tagName)
    {
        return "</" + tagName + ">";
    }

    /**
     * Удаляет namespace http://www.naumen.ru/sd40 и префиксы.
     * @param stringValue
     * @return
     */
    public static String deleteNamespace(String stringValue)
    {
        String result = stringValue;
        if (stringValue.contains(EMPTY_XMLNS))
        {
            result = result.replace(EMPTY_XMLNS, "");
        }
        Matcher matcher = NAU_XLMNS_PATTERN.matcher(result);
        if (matcher.find())
        {
            result = matcher.replaceAll("");
        }
        matcher = OPEN_PREFIX_PATTERN.matcher(result);
        if (matcher.find())
        {
            result = matcher.replaceAll("$1$3");
        }
        matcher = CLOSE_PREFIX_PATTERN.matcher(result);
        if (matcher.find())
        {
            result = matcher.replaceAll("$1$3");
        }
        matcher = TYPE_PATTERN.matcher(result);
        if (matcher.find())
        {
            result = matcher.replaceAll("xsi:type=\"");
        }

        return result;
    }

    /**
     * Преобразует документ в строку
     * @param document исходный документ
     * @return
     */
    public static String docToString(Document document)
    {
        final DOMImplementationLS domImplLS = (DOMImplementationLS)document.getImplementation();
        final LSOutput lsOutput = domImplLS.createLSOutput();
        lsOutput.setEncoding(StandardCharsets.UTF_8.displayName());
        final Writer stringWriter = new StringWriter();
        lsOutput.setCharacterStream(stringWriter);
        final LSSerializer serializer = domImplLS.createLSSerializer();
        serializer.write(document, lsOutput);
        return stringWriter.toString();
    }

    /**
     * Преобразует DOM-элемент в строку
     * @param element исходный элемент
     * @return
     */
    public static String elementToString(Element element)
    {
        final DOMImplementationLS domImplLS = (DOMImplementationLS)element.getOwnerDocument().getImplementation();
        final LSOutput lsOutput = domImplLS.createLSOutput();
        lsOutput.setEncoding(StandardCharsets.UTF_8.displayName());
        final Writer stringWriter = new StringWriter();
        lsOutput.setCharacterStream(stringWriter);
        final LSSerializer serializer = domImplLS.createLSSerializer();
        serializer.write(element, lsOutput);
        return stringWriter.toString();
    }

    public static Document getDocument(byte[] xml, boolean isProcessingExternalEntityInXML)
            throws SAXException, IOException, ParserConfigurationException
    {
        return getDocument(new ByteArrayInputStream(xml), isProcessingExternalEntityInXML);
    }

    /**
     * Получить документ с специфическими для нашего приложения опциями парсера
     *
     * @param in
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     * @throws SAXException
     * @throws IOException
     */
    public static Document getDocument(InputStream in, boolean isProcessingExternalEntityInXML)
            throws SAXException, IOException, ParserConfigurationException
    {
        return getDocument(in, true, isProcessingExternalEntityInXML);
    }

    /**
     * Получает Document из потока
     *
     * @param in
     * @param specificParser использовать специфичный для нашего приложения парсер (true) или использовать настройки
     *                       по умолчанию (false)
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     * @throws SAXException
     * @throws IOException
     * @throws ParserConfigurationException
     */
    public static Document getDocument(InputStream in, boolean specificParser, boolean isProcessingExternalEntityInXML)
            throws SAXException, IOException, ParserConfigurationException
    {
        final DocumentBuilderFactory dbf = specificParser ? getSpecificDocumentBuilderFactory()
                : DocumentBuilderFactory.newDefaultInstance();

        //https://owasp.org/www-project-cheat-sheets/cheatsheets/XML_External_Entity_Prevention_Cheat_Sheet.html
        isProcessingExternalEntityInXML(dbf, isProcessingExternalEntityInXML);
        dbf.setFeature(LOAD_EXTERNAL_DTD, false);
        try
        {
            DocumentBuilder db = dbf.newDocumentBuilder();
            db.setErrorHandler(new CustomErrorHandler());
            InputSource is = new InputSource(in);
            //При использование InputStream корректно обрабатывается BOM в начале UTF-8 файлов.
            //Если использовать Reader для парсинга файлов с BOM в начале, то будет ошибка
            db.setEntityResolver(DTD_FILE_RESOLVER);

            return db.parse(is);
        }
        catch (ParserConfigurationException x)
        {
            throw new Error(x);
        }
    }

    /**
     * Получить документ из переданного {@link InputStream}
     *
     * @param in       поток данных
     * @param systemID идентификатор источника данных, чаще всего URI относительно которого будут резолвится элементы
     *                 в XML если таковые есть, например XInclude
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return документ
     */
    public static Document getDocumentWithoutValidating(InputStream in, @Nullable String systemID,
            boolean isProcessingExternalEntityInXML)
            throws IOException, SAXException, ParserConfigurationException
    {
        final DocumentBuilderFactory dbf = getSpecificDocumentBuilderFactory();
        isProcessingExternalEntityInXML(dbf, isProcessingExternalEntityInXML);
        dbf.setValidating(false);
        dbf.setXIncludeAware(true);
        try
        {
            DocumentBuilder db = dbf.newDocumentBuilder();
            db.setErrorHandler(new CustomErrorHandler());
            InputSource is = new InputSource(in);
            if (systemID != null)
            {
                is.setSystemId(systemID);
            }
            //При использование InputStream корректно обрабатывается BOM в начале UTF-8 файлов.
            //Если использовать Reader для парсинга файлов с BOM в начале, то будет ошибка
            return db.parse(is);
        }
        catch (ParserConfigurationException x)
        {
            throw new Error(x);
        }
    }

    public static Document getDocumentWithoutValidating(InputStream in, boolean isProcessingExternalEntityInXML)
            throws SAXException, IOException, ParserConfigurationException
    {
        return getDocumentWithoutValidating(in, null, isProcessingExternalEntityInXML);
    }

    public static void addNamespace(Node node, String nameSpacePrefix, String uri)
    {
        Attr attribute = node.getOwnerDocument().createAttribute("xmlns:" + nameSpacePrefix);
        attribute.setValue(uri);
        node.getAttributes().setNamedItem(attribute);
    }

    /**
     * Открывающий тег по имени
     */
    public static String openTag(String tagName)
    {
        return "<" + tagName + ">";
    }

    public static ByteArrayInputStream toInputStream(String xml)
    {
        return new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8));
    }

    private static DocumentBuilderFactory getSpecificDocumentBuilderFactory()
    {
        final DocumentBuilderFactory dbf = DocumentBuilderFactory.newDefaultInstance();
        dbf.setNamespaceAware(true);
        dbf.setIgnoringElementContentWhitespace(true);
        dbf.setCoalescing(true);
        dbf.setIgnoringComments(true);
        return dbf;
    }

    /**
     * Проверить XML на корректность
     * <ul>
     *   <li>Проверяет отсутсвие невалидных символов</li>
     *   <li>Парсит xml с целью проверки его корректности</li>
     * </ul>
     * NOTE: Так уж сложилось, что JAXB при маршалинге не делает валидацию по умолчанию.
     * Можно было бы ничего это не делать, а валидировать xml во время маршалинга
     * из Java объектов, но для этого необходима xsd схема для каждого объекта.
     * <pre>
     *  SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
     *  Schema schema = schemaFactory.newSchema(xsdSchemaFile);
     *  Marshaller m = jc.createMarshaller();
     *  m.setSchema(schema);
     * </pre>
     * @param xml xml в виде строки
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @throws InvalidXmlException исключение при ошибке валидации
     */
    private static void validateXml(String xml, boolean isProcessingExternalEntityInXML)
    {
        validateXmlCharacters(xml);
        try
        {
            getDocument(xml.getBytes(StandardCharsets.UTF_8), isProcessingExternalEntityInXML);
        }
        catch (SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }

    /**
     * Проверить все ли символы допустимы для сохранения в XML
     * @param xml xml в виде строки
     * @throws InvalidXmlException исключение при ошибке валидации
     * @see <a href="https://www.w3.org/TR/xml/#charsets">Стандарт XML</a>
     */
    private static void validateXmlCharacters(String xml)
    {
        int codePoint = XMLChar.validateXmlChars(xml);
        if (codePoint != XMLChar.VALID_TEXT)
        {
            String tag = findTag(xml, codePoint);
            throw new InvalidXmlException(tag, codePoint);
        }
    }

    /**
     * Найти тег, в котором встречается указанный символ
     * @param xml текст для поиска тега
     * @param codePoint код символа
     * @return имя тега, или "" если тег не найден
     */
    private static String findTag(String xml, int codePoint)
    {
        int index = xml.indexOf(codePoint);
        if (index < 0)
        {
            return "";
        }
        String substr = xml.substring(0, index);
        int endTagBegin = substr.lastIndexOf('<');
        int endTagEnd = substr.lastIndexOf('>');
        if (endTagBegin < 0 || endTagEnd < 0)
        {
            return "";
        }
        String tag = substr.substring(endTagBegin + 1, endTagEnd);
        return tag.split(" ", 2)[0];
    }

    private final Map<String, JAXBContext> contextCache = new ConcurrentHashMap<>();

    public JAXBContext createJAXBContext(String pkg) throws JAXBException
    {
        JAXBContext context = contextCache.get(pkg);
        if (null == context)
        {
            context = JAXBContext.newInstance(pkg);
            contextCache.put(pkg, context);
        }
        return context;
    }

    public <T> T parseXml(InputStream xml, Class<?> clazz, Source[] sources, boolean isProcessingExternalEntityInXML)
    {
        return parseXml(xml, clazz.getPackage().getName(), sources, isProcessingExternalEntityInXML);
    }

    /**
     * преобразовывает XML в соответствующие объекты
     *
     * @see <a href=https://jaxb.dev.java.net/tutorial/>https://jaxb.dev.java.net/tutorial/</a>
     *
     * @param <T>
     * @param xml
     *            input stream содержащая xml документ
     * @param cls
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T parseXml(InputStream xml, Class<T> cls, boolean isProcessingExternalEntityInXML)
    {
        return (T)parseXml(xml, cls.getPackage().getName(), isProcessingExternalEntityInXML);
    }

    /**
     * преобразовывает XML в соответствующие объекты
     *
     * @see <a href=https://jaxb.dev.java.net/tutorial/>https://jaxb.dev.java.net/tutorial/</a>
     *
     * @param <T>
     * @param xml
     *            input stream содержащая xml документ
     * @param pkg
     *            имя пакета в котором лежат JAXB классы
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T parseXml(InputStream xml, String pkg, boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final Unmarshaller u = jc.createUnmarshaller();
            //необходимо десериализовывать Document, чтобы сообщения об ошибках
            //содержали не строку и столбец ошибки, а тег
            return (T)u.unmarshal(getDocumentWithoutValidating(xml, isProcessingExternalEntityInXML));
        }
        catch (JAXBException | SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Распарсить XML в объект заданного класса
     *
     * @param is                    поток с XML
     * @param targetClass           класс объекта
     * @param basePathForInclusions путь относительно которого будут выполнятся include, если они есть в XML
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return объект из XML
     */
    @SuppressWarnings("unchecked")
    public <T> T parseXml(InputStream is, Class<T> targetClass, String basePathForInclusions,
            boolean isProcessingExternalEntityInXML)
    {
        try
        {
            JAXBContext jc = createJAXBContext(targetClass.getPackage().getName());
            Unmarshaller u = jc.createUnmarshaller();
            //необходимо десериализовывать Document, чтобы сообщения об ошибках
            //содержали не строку и столбец ошибки, а тег
            return (T)u.unmarshal(
                    getDocumentWithoutValidating(is, basePathForInclusions, isProcessingExternalEntityInXML));
        }
        catch (JAXBException | SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    @SuppressWarnings("unchecked")
    public <T> T parseXml(InputStream xml, String pkg, Source[] sources, boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final Unmarshaller u = jc.createUnmarshaller();
            u.setSchema(createSchema(sources));
            //необходимо десериализовывать Document, чтобы сообщения об ошибках
            //содержали не строку и столбец ошибки, а тег
            return (T)u.unmarshal(getDocumentWithoutValidating(xml, isProcessingExternalEntityInXML));
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Преобразовывает XML в соответствующие объекты
     *
     * @see <a href="https://jaxb.dev.java.net/tutorial/">https://jaxb.dev.java.net/tutorial/</a>
     *
     * @param <T>
     * @param xml
     *            input stream содержащая xml документ
     * @param pkg
     *            имя пакета в котором лежат JAXB классы
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T parseXml(InputStream xml, String pkg, ValidationEventHandler handler,
            boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final Unmarshaller u = jc.createUnmarshaller();
            u.setEventHandler(handler);
            //необходимо десериализовывать Document, чтобы сообщения об ошибках
            //содержали не строку и столбец ошибки, а тег
            return (T)u.unmarshal(getDocumentWithoutValidating(xml, isProcessingExternalEntityInXML));
        }
        catch (JAXBException | SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    public <T> T parseXml(String xml, Class<?> clazz, Source[] sources, boolean isProcessingExternalEntityInXML)
    {
        final InputStream is = toInputStream(xml);
        return parseXml(is, clazz, sources, isProcessingExternalEntityInXML);
    }

    public <T> T parseXml(String xml, Class<?> clazz, boolean isProcessingExternalEntityInXML, String... xsds)
    {
        return parseXml(xml, clazz, getResources(xsds), isProcessingExternalEntityInXML);
    }

    public Source[] getResources(String... xsds)
    {
        final Source[] resources = new Source[xsds.length];
        for (int i = 0; i < xsds.length; ++i)
        {
            final ClassPathResource r = new ClassPathResource(xsds[i], getClass().getClassLoader());
            try
            {
                resources[i] = new StreamSource(r.getInputStream());
            }
            catch (IOException e)
            {
                throw new FxException(e);
            }
        }
        return resources;
    }

    /**
     * преобразовывает XML в соответствующие объекты
     *
     * @param <T>
     * @param xml строка содержащая xml документ
     * @param cls
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     * @see <a href="https://jaxb.dev.java.net/tutorial/">https://jaxb.dev.java.net/tutorial/</a>
     */
    @SuppressWarnings("unchecked")
    public <T> T parseXml(String xml, Class<T> cls, boolean isProcessingExternalEntityInXML)
    {
        final InputStream is = toInputStream(xml);
        return (T)parseXml(is, cls.getPackage().getName(), isProcessingExternalEntityInXML);
    }

    /**
     * Производит подписывание xml указанным ключем
     *
     * @param xml
     * @param kp
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return
     */
    public byte[] sign(byte[] xml, KeyPair kp, boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final Document document = getDocument(new ByteArrayInputStream(xml), isProcessingExternalEntityInXML);
            sign(document, kp);
            return toByteArray(document);
        }
        catch (FxException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Подписывает документ указанным ключем
     *
     * @see #validate(Document, KeySelector)
     *
     * @param document
     * @param kp
     */
    public void sign(Document document, KeyPair kp)
    {
        try
        {
            final XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");
            final Transform transform = fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec)null);
            final DigestMethod digestMethod = fac.newDigestMethod(DigestMethod.SHA512, null);
            final Reference ref = fac.newReference("", digestMethod, Collections.singletonList(transform), null, null);

            final CanonicalizationMethod method = fac.newCanonicalizationMethod(
                    CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS, (C14NMethodParameterSpec)null);
            final SignatureMethod signatureMethod = fac.newSignatureMethod(SignatureMethod.RSA_SHA1, null);
            final SignedInfo si = fac.newSignedInfo(method, signatureMethod, Collections.singletonList(ref));

            final KeyInfoFactory kif = fac.getKeyInfoFactory();
            final KeyValue kv = kif.newKeyValue(kp.getPublic());
            final KeyInfo ki = kif.newKeyInfo(Collections.singletonList(kv));

            final XMLSignature signature = fac.newXMLSignature(si, ki);

            final NodeList nodes = document.getChildNodes();
            for (int i = 0; i < nodes.getLength(); ++i)
            {
                signature.sign(new DOMSignContext(kp.getPrivate(), nodes.item(i)));
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Преобразует документ в массив байт
     *
     * @param document
     * @return
     * @throws TransformerException
     */
    public static byte[] toByteArray(Document document) throws TransformerException
    {
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        final Result result = new StreamResult(out);

        final Source source = new DOMSource(document);

        final TransformerFactory factory = TransformerFactory.newInstance();
        final Transformer transformer = factory.newTransformer();
        transformer.transform(source, result);

        return out.toByteArray();
    }

    /**
     * Преобразование объекта в DOM документ
     *
     * @param obj объект
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return DOM документ
     */
    public Document toDocument(Object obj, boolean isProcessingExternalEntityInXML)
    {
        return toDocument(obj, obj.getClass().getPackage().getName(), isProcessingExternalEntityInXML);
    }

    /**
     * Преобразование заданного объекта в DOM документ с
     *
     * @param obj      преобразуемый объект
     * @param listener слушатель событий маршаллинга
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return DOM документ
     */
    public Document toDocument(Object obj, Listener listener, boolean isProcessingExternalEntityInXML)
    {
        return toDocument(obj, obj.getClass().getPackage().getName(), listener, isProcessingExternalEntityInXML);
    }

    /**
     * Преобразование объекта в DOM документ
     *
     * @param obj объект
     * @param pkg пакет
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return DOM документ
     */
    public Document toDocument(Object obj, String pkg, boolean isProcessingExternalEntityInXML)
    {
        return toDocument(obj, pkg, null, isProcessingExternalEntityInXML);
    }

    /**
     * Преобразование объекта в DOM документ
     *
     * @param obj                 объект
     * @param pkg                 пакет
     * @param marshallingListener слушатель событий маршаллинга
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @return DOM документ
     */
    private Document toDocument(Object obj, String pkg, @Nullable Listener marshallingListener,
            boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final Marshaller m = jc.createMarshaller();
            if (marshallingListener != null)
            {
                m.setListener(marshallingListener);
            }
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newDefaultInstance();
            isProcessingExternalEntityInXML(dbf, isProcessingExternalEntityInXML);
            final Document doc = dbf.newDocumentBuilder().newDocument();
            m.marshal(obj, doc);
            return doc;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public static Transformer createTransformer(Class<?> clz)
    {
        try
        {
            final Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty(OutputKeys.STANDALONE, "yes");
            transformer.setOutputProperty(OutputKeys.ENCODING, StandardCharsets.UTF_8.name());
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
            final String cdata = String.join(" ", CDataTagsRegistry.getCDataElements(clz));
            transformer.setOutputProperty(OutputKeys.CDATA_SECTION_ELEMENTS, cdata);
            transformer.setOutputProperty(OutputKeys.METHOD, "xml");
            return transformer;
        }
        catch (TransformerConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Записать отформатированный XML в {@link OutputStream}
     * @param transformer трансформер с заданными настройками форматирования
     * @param element записываемый XML элемент
     * @param outputStream куда писать
     */
    public static void formatXmlToOutputStream(Transformer transformer, Node element, OutputStream outputStream)
    {
        final StreamResult streamResult = new StreamResult(outputStream);
        final DOMSource domSource = new DOMSource(element);
        try
        {
            transformer.transform(domSource, streamResult);
        }
        catch (TransformerException e)
        {
            throw new FxException(e);
        }
    }

    public void toOutputStream(OutputStream stream, Object obj, String pkg, boolean isProcessingExternalEntityInXML)
    {
        final Document document = toDocument(obj, pkg, isProcessingExternalEntityInXML);
        Transformer transformer = createTransformer(obj.getClass());
        formatXmlToOutputStream(transformer, document.getDocumentElement(), stream);
    }

    /**
     * Преобразует JAXB элемент в xml
     *
     * @param obj преобразуемый объект
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @throws InvalidXmlException исключение при ошибке валидации
     * @see <a href="https://jaxb.dev.java.net/tutorial/">https://jaxb.dev.java.net/tutorial/</a>
     */
    public String toXml(Object obj, boolean isProcessingExternalEntityInXML)
    {
        return toXml(obj, obj.getClass().getPackage().getName(), isProcessingExternalEntityInXML);
    }

    /**
     * Преобразует JAXB элемент в xml
     *
     * @param obj преобразуемый объект
     * @param pkg имя пакета в котором лежат JAXB классы
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @throws InvalidXmlException исключение при ошибке валидации
     * @see <a href="https://jaxb.dev.java.net/tutorial/">https://jaxb.dev.java.net/tutorial/</a>
     */
    public String toXml(Object obj, String pkg, boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final ByteArrayOutputStream os = new ByteArrayOutputStream();
            final Marshaller m = jc.createMarshaller();
            m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            m.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
            m.marshal(obj, os);
            String xml = os.toString(StandardCharsets.UTF_8);
            validateXml(xml, isProcessingExternalEntityInXML);
            return xml;
        }
        catch (JAXBException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Преобразует JAXB элемент в xml, проверяя его на соответсвтвие схеме.
     * <p>
     * Если есть несоответсвие выбрасывается исключение, например, такое
     * "Value 'b24c9042' with length = '8' is not facet-valid with respect to maxLength '6' for type
     * '#AnonType_attr1TestObject'"
     *
     * @param obj
     *            преобразуемый объект
     * @param pkg
     *            имя пакета в котором лежат JAXB классы
     * @param sources
     *            массив потоков с файлами .xsd
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @throws InvalidXmlException исключение при ошибке валидации
     */
    public String toXml(Object obj, String pkg, Source[] sources, boolean isProcessingExternalEntityInXML)
    {
        try
        {
            final JAXBContext jc = createJAXBContext(pkg);
            final ByteArrayOutputStream os = new ByteArrayOutputStream();
            final Marshaller m = jc.createMarshaller();
            m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            m.setSchema(createSchema(sources));
            m.marshal(obj, os);
            String xml = os.toString(StandardCharsets.UTF_8);
            validateXml(xml, isProcessingExternalEntityInXML);
            return xml;
        }
        catch (InvalidXmlException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Производит проверку xml подписи документа
     *
     * @see #sign(Document, KeyPair)
     *
     * @param document
     * @param ks
     * @return
     */
    public boolean validate(Document document, KeySelector ks)
    {
        try
        {
            final NodeList nl = document.getElementsByTagNameNS(XMLSignature.XMLNS, SIGNATURE_TAG_NAME);
            if (nl.getLength() == 0)
            {
                throw new FxException("Cannot find Signature element");
            }

            for (int i = 0; i < nl.getLength(); ++i)
            {
                final DOMValidateContext valContext = new DOMValidateContext(ks, nl.item(i));

                final XMLSignatureFactory factory = XMLSignatureFactory.getInstance("DOM");
                final XMLSignature signature = factory.unmarshalXMLSignature(valContext);

                if (!signature.validate(valContext))
                {
                    return false;
                }
            }
            return true;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public boolean validate(Document document, final PublicKey key)
    {
        final KeySelectorResult keySelectorResult = () -> key;
        return validate(document, new KeySelector()
        {
            @Override
            public KeySelectorResult select(KeyInfo keyInfo, Purpose purpose, AlgorithmMethod method,
                    XMLCryptoContext context)
            {
                return keySelectorResult;
            }
        });
    }

    /**
     * Позволяет включить/выключить внешние сущности в XML
     *
     * @see <a href="https://capec.mitre.org/data/definitions/201.html">CAPEC-201: Serialized Data External Linking</a>
     * @see
     * <a href="https://cwe.mitre.org/data/definitions/611.html">CWE-611: Improper Restriction of XML External Entity Reference</a>
     *
     * @param dbf фабрика
     * @param isProcessingExternalEntityInXML параметр для включения/выключения обработки внешних сущностей в XML
     * @throws ParserConfigurationException
     */
    public static void isProcessingExternalEntityInXML(DocumentBuilderFactory dbf,
            boolean isProcessingExternalEntityInXML) throws ParserConfigurationException
    {
        if (!isProcessingExternalEntityInXML)
        {
            dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
        }
    }

    private static Schema createSchema(Source[] sources) throws SAXException
    {
        final SchemaFactory sf = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
        return sf.newSchema(sources);
    }
}
