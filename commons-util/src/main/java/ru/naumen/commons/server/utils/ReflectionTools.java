package ru.naumen.commons.server.utils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.reflect.MethodUtils;

import ru.naumen.commons.shared.FxException;

import java.util.HashMap;

/**
 * Утилитарные методы для reflection
 *
 * <AUTHOR>
 *
 */
public final class ReflectionTools
{
    private ReflectionTools()
    {
    }

    private static final String CANT_SET_PROPERTY_VALUE = "Can't set '%s' property value : %s%s";

    /**
     * Пустой список классов для нахождения методов без параметров
     */
    public static final Class<?>[] CLASSES = new Class[0];

    /**
     * Пустой массив объектов для вызова методов без параметров
     */
    public static final Object[] OBJECTS = new Object[0];

    /**
     * Значение ссылки на сам объект при доступе к его свойствам
     */
    public static final String THIS = "this";

    private static final Map<Class<?>, Class<?>> TYPE_TO_WRAPPER_CLASS;

    static
    {
        Map<Class<?>, Class<?>> map = new HashMap<>();
        TYPE_TO_WRAPPER_CLASS = Collections.unmodifiableMap(map);

        map.put(Boolean.TYPE, Boolean.class);
        map.put(Integer.TYPE, Integer.class);
        map.put(Long.TYPE, Long.class);
        map.put(Short.TYPE, Short.class);
        map.put(Byte.TYPE, Byte.class);
        map.put(Character.TYPE, Character.class);
        map.put(Float.TYPE, Float.class);
        map.put(Double.TYPE, Double.class);
        map.put(Void.TYPE, Void.class);
    }

    /**
     * Возвращает метод класса с одним из имен указанных в names с заданными параметрами. Если у класса есть несколько
     * методов с указанными именами, то возвращает метод с именем первым из списка.
     *
     * @param clazz
     *            класс у которого требуется получить метод
     * @param names
     *            имена методов
     * @param args
     *            классы аргументов искомого метода
     * @return метод класса
     */
    public static Method findMethod(Class<?> clazz, String[] names, Class<?>[] args)
    {
        for (String name : names)
        {
            try
            {
                return clazz.getMethod(name, args);
            }
            catch (NoSuchMethodException e) //NOPMD
            {
                // игнорируем.
                // пытаемся получить метод для следующего имени
                continue; //NOSONAR Пустой catch блок ломает тест BasePackageCheckJdkTest.findEmptyCatchBlockTest
            }
        }
        return null;
    }

    /**
     * Возвращает геттер для поля класса
     *
     * @param clazz
     *            класс у которого требуется получить геттер
     * @param property
     *            имя поля
     * @return метод
     */
    public static Method getGetter(Class<?> clazz, String property)
    {
        String[] names = getterName(property);
        Method result = findMethod(clazz, names, CLASSES);
        if (result == null)
        {
            throw new FxException("Getter for '%s' for class '%s' not found".formatted(property,
                    clazz)); //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
        }
        return result;
    }

    /**
     * Метод предназначен для получения значения свойства указанного объекта.
     * Для этого используется вызов геттера с помощью Reflection.
     *
     * @param target целевой объект
     * @param property имя свойства
     * @return значение свойства
     * @throws FxException при невозможности получить значение указанного свойства объекта
     */
    public static Object getProperty(Object target, String property) throws FxException
    {
        if (THIS.equals(property))
        {
            return target;
        }
        Method readMethod = getGetter(target.getClass(), property);
        try
        {
            return readMethod.invoke(target, OBJECTS);
        }
        catch (Exception e)
        {
            throw new FxException("Can't get '" + property + "' property value", e);
        }
    }

    /**
     * Возвращает сеттер для поля класса.
     *
     * @param clazz
     *            класса
     * @param property
     *            имя поля
     * @return метод
     */
    public static Method getSetter(Class<?> clazz, String property)
    {
        return getSetter(clazz, property, getGetter(clazz, property).getReturnType());
    }

    /**
     * Возвращает сеттер для поля класса.
     *
     * @param clazz
     *            класса
     * @param property
     *            имя поля
     * @param propertyClass
     *            тип поля
     * @return метод
     */
    public static Method getSetter(Class<?> clazz, String property, Class<?> propertyClass)
    {
        String[] names = new String[] { setterName(property) };
        Method result = findMethod(clazz, names, new Class[] { propertyClass });
        if (result == null)
        {
            throw new FxException("Setter for '" + property + "' type of '" + propertyClass //$NON-NLS-1$ //$NON-NLS-2$ 
                                  + "' for class '" + clazz + "' not found"); //$NON-NLS-1$ //$NON-NLS-2$
        }
        return result;
    }

    /**
     * Возвращает сеттер для поля класса.
     *
     * @param clazz
     *            класса
     * @param property
     *            имя поля
     * @param propertyClass
     *            тип поля по геттеру
     * @param valueClass
     *            тип поля по значению
     * @return метод
     */
    public static Method getSetter(Class<?> clazz, String property, Class<?> propertyClass, Class<?> valueClass)
    {
        String[] names = new String[] { setterName(property) };
        Method result = findMethod(clazz, names, new Class[] { valueClass });
        if (result == null)
        {
            result = findMethod(clazz, names, new Class[] { propertyClass });
            if (result == null)
            {
                throw new FxException(
                        "Setter for '" + property + "' type of '" + propertyClass //$NON-NLS-1$ //$NON-NLS-2$
                        + "' or '" + valueClass + "' for class '" + clazz
                        + "' not found"); //$NON-NLS-1$ //$NON-NLS-2$
            }
        }
        return result;
    }

    /**
     * Возвращает все возможные имена геттеров для поля класса
     *
     * @param property
     *            имя поля класса
     * @return возможные имена геттеров
     */
    public static String[] getterName(String property)
    {
        String capitalized = StringUtilities.capitalize(property);
        return new String[] { "get" + capitalized, "is" + capitalized,
                "list" + capitalized }; //$NON-NLS-1$ //$NON-NLS-2$ //$NON-NLS-3$
    }

    /**
     * Возвращает класс объекта соотвтствующего примитивному типу. Например для <code>int</code> вернет
     * <code>Integer</code>
     *
     * @param clazz
     *            класс для которого надо получит класс обертку
     * @return класс обертки или null если clazz не примитив
     */
    public static Class<?> getWrapperClass(Class<?> clazz)
    {
        return TYPE_TO_WRAPPER_CLASS.get(clazz);
    }

    /**
     * @return true, если у target есть свойство propertry
     */
    public static boolean hasProperty(Object target, String property)
    {
        String[] names = getterName(property);
        return findMethod(target.getClass(), names, CLASSES) != null;
    }

    public static void setStaticValue(Class<?> clazz, String property, @Nullable Object value)
    {
        setValue(clazz, null, property, value);
    }

    /**
     * Устанавливает значение для поля объекта
     * Поле может быть как public так и private
     * @param clazz - класс в котором нужно установить поле
     * @param property - название поле
     * @param value - устанавливаемое значение
     */
    public static void setValue(Class<?> clazz, @Nullable Object object, String property, @Nullable Object value)
    {
        try
        {
            final Field field = clazz.getDeclaredField(property);
            field.setAccessible(true); //NOSONAR Ругается на reflection
            field.set(object, value); //NOSONAR Ругается на reflection
        }
        catch (Exception e)
        {
            throw new FxException(CANT_SET_PROPERTY_VALUE.formatted(property, value,
                    null == value ? "" : " (" + value.getClass() + ")"), e);
        }
    }

    /**
     * Устанавливает значение для поля объекта
     * Поле может быть как public так и private
     * @param field - поле
     * @param object - объект
     * @param value - устанавливаемое значение
     */
    public static void setValue(Field field, @Nullable Object object, @Nullable Object value)
    {
        try
        {
            field.setAccessible(true); //NOSONAR Ругается на reflection
            field.set(object, value); //NOSONAR Ругается на reflection
        }
        catch (Exception e)
        {
            throw new FxException(CANT_SET_PROPERTY_VALUE.formatted(field.getName(), value,
                    null == value ? "" : " (" + value.getClass() + ")"), e);
        }
    }

    /**
     *
     * @param target
     * @param property
     * @param value
     */
    public static void setProperty(Object target, String property, @Nullable Object value)
    {
        try
        {
            Method setter;
            if (value != null)
            {
                setter = getSetter(target.getClass(), property, getGetter(target.getClass(), property).getReturnType(),
                        value.getClass());
            }
            else
            {
                setter = getSetter(target.getClass(), property, getGetter(target.getClass(), property).getReturnType());
            }
            setter.invoke(target, value);
        }
        catch (Exception e)
        {
            throw new FxException(CANT_SET_PROPERTY_VALUE.formatted(property, value,
                    null == value ? "" : " (" + value.getClass() + ")"), e);
        }
    }

    /**
     * Возвращает имя сеттера для поля класса
     *
     * @param property
     *            имя поля класса
     * @return имя сеттера
     */
    public static String setterName(String property)
    {
        return "set" + StringUtilities.capitalize(property); //$NON-NLS-1$
    }

    /**
     * Устанавливает значение заданного поля объекта.
     * <p>
     * Должен сущетвовать сеттер для заданного поля
     *
     * @param obj
     *            объект
     * @param property
     *            название поля
     * @param value
     *            устанавливаемое значение
     */
    public static void setValue(Object obj, String property, Object value)
    {
        try
        {
            MethodUtils.invokeMethod(obj, setterName(property), value);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Устанавливает значение заданного поля объекта.
     * <p>
     * Должен сущетвовать сеттер для заданного поля
     *
     * @param obj
     *            объект
     * @param property
     *            название поля
     * @param value
     *            устанавливаемое значение
     * @param clazz
     *            тип устанавливаемого значения
     */
    public static void setValue(Object obj, String property, Object value, Class<?> clazz)
    {
        try
        {
            getSetter(obj.getClass(), property, clazz).invoke(obj, value);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Получить поле с учетом особенностей java 12+
     */
    public static Field getDeclaredField(Class<?> clazz, String name) throws NoSuchFieldException
    {
        try
        {
            return clazz.getDeclaredField(name);
        }
        catch (NoSuchFieldException e1)
        {
            try
            {
                Method getDeclaredFields0 =
                        Class.class.getDeclaredMethod("getDeclaredFields0", boolean.class);
                getDeclaredFields0.setAccessible(true); //NOSONAR Ругается на reflection
                Field[] fields = (Field[])getDeclaredFields0.invoke(clazz, false);
                for (Field f : fields)
                {
                    if (f.getName().equals(name))
                    {
                        return f;
                    }
                }
                throw new NoSuchFieldException(name);
            }
            catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e2)
            {
                throw new NoSuchFieldException(name);
            }
        }
    }

    /**
     * Получить значение переданного поля у переданного объекта
     */
    public static Object getDeclaredFieldValue(Object object, String name)
    {
        return getDeclaredFieldValue(object.getClass(), object, name);
    }

    /**
     * Получить значение переданного поля у переданного объекта
     */
    public static Object getDeclaredFieldValue(Class<?> clazz, Object object, String name)
    {
        try
        {
            Field field = getDeclaredField(clazz, name);
            field.setAccessible(true); //NOSONAR Ругается на reflection
            return field.get(object);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }
}
