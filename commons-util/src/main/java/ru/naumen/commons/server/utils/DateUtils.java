package ru.naumen.commons.server.utils;

import java.security.InvalidParameterException;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import jakarta.annotation.Nullable;

import com.google.common.base.Preconditions;

import ru.naumen.commons.shared.utils.Pair;

/**
 * Утилитарные методы для работы с датами.
 *
 * <AUTHOR>
 * @since 24.12.2010
 *
 */
public class DateUtils
{
    public static final TimeZone GMT_TZ = TimeZone.getTimeZone("GMT");
    public static final String INVALID_CALENDAR_FIELD = "Invalid calendar field";

    public static Date addDays(Date date, int d)
    {
        Calendar calendar = createCalendar(date);
        calendar.add(Calendar.DATE, d);
        return calendar.getTime();
    }

    public static Date addHours(Date date, int h)
    {
        Calendar calendar = createCalendar(date);
        calendar.add(Calendar.HOUR, h);
        return calendar.getTime();
    }

    public static Date addMinutes(Date date, int m)
    {
        Calendar calendar = createCalendar(date);
        calendar.add(Calendar.MINUTE, m);
        return calendar.getTime();
    }

    public static Date addSeconds(Date date, int s)
    {
        Calendar calendar = createCalendar(date);
        calendar.add(Calendar.SECOND, s);
        return calendar.getTime();
    }

    public static Calendar createCalendar(Date date)
    {
        return createCalendar(date, null);
    }

    public static Calendar createCalendar(Date date, @Nullable TimeZone timeZone)
    {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        setTimeZoneIfNotNull(calendar, timeZone);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        return calendar;
    }

    /**
     * Возвращает день месяца в часовом поясе сервера
     * @see Calendar#DAY_OF_MONTH
     */
    public static int getDay(Date date)
    {
        return createCalendar(date).get(Calendar.DAY_OF_MONTH);
    }

    /**
     * Возвращает день недели в часовом поясе сервера
     * @see Calendar#DAY_OF_WEEK
     */
    public static int getDayOfWeek(Date date)
    {
        return createCalendar(date).get(Calendar.DAY_OF_WEEK);
    }

    /**
     * Возвращает начало дня, месяца или года в часовом поясе сервера
     */
    public static Date getFirstSecondOfCalendarField(Date date, int field, TimeZone timeZone)
    {
        return switch (field)
        {
            case Calendar.DAY_OF_MONTH,
                 Calendar.DAY_OF_WEEK,
                 Calendar.DAY_OF_WEEK_IN_MONTH,
                 Calendar.DAY_OF_YEAR -> getFirstSecondOfDay(date, timeZone);
            case Calendar.MONTH -> getFirstSecondOfMonth(date, timeZone);
            case Calendar.YEAR -> getFirstSecondOfYear(date, timeZone);
            default -> throw new InvalidParameterException(INVALID_CALENDAR_FIELD);
        };
    }

    /**
     * Возвращает час из указанной даты в часовом поясе сервера
     * @see Calendar#HOUR_OF_DAY
     */
    public static int getHourOfDay(Date date)
    {
        return createCalendar(date).get(Calendar.HOUR_OF_DAY);
    }

    /**
     * Возвращает конец дня, месяца или года
     *
     * @param date дата, от которой нужно посчитать начало
     * @param field номер {@link Calendar календарного поля}
     * @param timeZone часовой пояс, в котором передаётся дата
     * @return дата, которая является концом дня, месяца или года
     */
    public static Date getLastSecondOfCalendarField(Date date, int field, TimeZone timeZone)
    {
        return switch (field)
        {
            case Calendar.DAY_OF_MONTH, Calendar.DAY_OF_WEEK, Calendar.DAY_OF_WEEK_IN_MONTH, Calendar.DAY_OF_YEAR ->
                    getLastSecondOfDay(date, timeZone);
            case Calendar.MONTH -> getLastSecondOfMonth(date, timeZone);
            case Calendar.YEAR -> getLastSecondOfYear(date, timeZone);
            default -> throw new InvalidParameterException(INVALID_CALENDAR_FIELD);
        };
    }

    /**
     * Возвращает минуту дня в часовом поясе сервера
     */
    public static int getMinute(Date date)
    {
        return createCalendar(date).get(Calendar.MINUTE);
    }

    /**
     * Возвращает секунду дня в часовом поясе сервера
     */
    public static int getSeconds(Date date)
    {
        return createCalendar(date).get(Calendar.SECOND);
    }

    /**
     * Возвращает месяц даты в часовом поясе сервера
     */
    public static int getMonth(Date date)
    {
        return createCalendar(date).get(Calendar.MONTH);
    }

    public static TimeZone getTimeZoneById(String timeZoneId)
    {
        if (ru.naumen.commons.shared.utils.StringUtilities.isEmpty(timeZoneId))
        {
            return TimeZone.getDefault();
        }
        return TimeZone.getTimeZone(timeZoneId);
    }

    /**
     * Возвращает год даты в часовом поясе сервера
     */
    public static int getYear(Date date)
    {
        return createCalendar(date).get(Calendar.YEAR);
    }

    /**
     * Возвращает текущий год в часовом поясе сервера
     */
    public static String getCurrentYear()
    {
        Date date = new Date();
        return String.valueOf(createCalendar(date).get(Calendar.YEAR));
    }

    /**
     * Возвращает результат проверки двух дат на предмет эквивалентности их календарных полей (день, месяц, год)
     *
     * @param date1 первая дата для сравнения
     * @param date2 вторая дата для сравнения
     * @param field номер {@link Calendar календарного поля}
     * @param timeZone часовой пояс в котором передаются даты
     * @return true - если календарные поля эквивалентны, false - если поля различаются
     * @see Calendar#DAY_OF_MONTH
     * @see Calendar#DAY_OF_WEEK
     * @see Calendar#DAY_OF_WEEK_IN_MONTH
     * @see Calendar#DAY_OF_YEAR
     * @see Calendar#MONTH
     * @see Calendar#YEAR
     */
    public static boolean isCalendarFieldEquals(Date date1, Date date2, int field, TimeZone timeZone)
    {
        return switch (field)
        {
            case Calendar.DAY_OF_MONTH, Calendar.DAY_OF_WEEK, Calendar.DAY_OF_WEEK_IN_MONTH, Calendar.DAY_OF_YEAR ->
                    isDaysEquals(date1, date2, timeZone);
            case Calendar.MONTH -> isMonthsEquals(date1, date2, timeZone);
            case Calendar.YEAR -> isYearsEquals(date1, date2, timeZone);
            default -> throw new InvalidParameterException(INVALID_CALENDAR_FIELD);
        };
    }

    public static String time2Str(Calendar c)
    {
        return StringUtilities.appendToLen(c.get(Calendar.HOUR_OF_DAY), 2) + ":" //$NON-NLS-1$
               + StringUtilities.appendToLen(c.get(Calendar.MINUTE), 2);
    }

    public static String time2Str(Date dt)
    {
        Preconditions.checkNotNull(dt, "Dt cannot be null");

        GregorianCalendar c = new GregorianCalendar();
        c.setTime(dt);
        return time2Str(c);
    }

    /**
     * Форматирование времени в строку вида "HHH:mm", где HHH - кол-во часов (может быть более 24),
     * mm - кол-во минут (00-59). Милисекунды отбрасываются.
     *
     * @param ms значение длительности временного отрезка в милисекундах
     * @return строка значения продолжительности временного отрезка
     * @throws IllegalArgumentException если значение аргумента < 0
     */
    public static String time2Str(long ms) throws IllegalArgumentException
    {
        if (ms < 0L)
        {
            throw new IllegalArgumentException("ms value must not be negative");
        }
        long min = ms / 60000L;
        return String.format("%02d:%02d", min / 60, min % 60);
    }

    /**
     * Метод предназначен для обнуления младших значащих значений даты от
     * указанного календарного поля.
     * @param calendar календарь
     * @param roundField старшее значащее поле календаря, до которого поисходит обнуление даты
     */
    public static void trunc(Calendar calendar, int roundField)
    {
        switch (roundField) // NOPMD
        {
            case Calendar.YEAR:
                calendar.set(Calendar.MONTH, Calendar.JANUARY);
            case Calendar.MONTH:
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                break;
            case Calendar.WEEK_OF_YEAR:
                calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
                break;
            default:
                break;
            //ничего не делаем
        }

        switch (roundField) // NOPMD
        {
            case Calendar.YEAR:
            case Calendar.MONTH:
            case Calendar.WEEK_OF_YEAR:
            case Calendar.DAY_OF_YEAR:
            case Calendar.DAY_OF_MONTH:
            case Calendar.DAY_OF_WEEK:
            case Calendar.DAY_OF_WEEK_IN_MONTH:
                calendar.set(Calendar.HOUR_OF_DAY, 0);
            case Calendar.HOUR_OF_DAY:
            case Calendar.HOUR:
                calendar.set(Calendar.MINUTE, 0);
            case Calendar.MINUTE:
                calendar.set(Calendar.SECOND, 0);
            case Calendar.SECOND:
                calendar.set(Calendar.MILLISECOND, 0);
                break;
            default:
                break;
            //ничего не делаем
        }
    }

    /**
     * Метод предназначен для обнуления младших значащих значений даты от
     * указанного календарного поля.
     * @param dt дата
     * @param roundField старшее значащее поле календаря, до которого поисходит обнуление даты
     */
    public static Date trunc(Date dt, int roundField)
    {
        Calendar calendar = createCalendar(dt);
        trunc(calendar, roundField);
        return calendar.getTime();
    }

    public static Calendar truncateDate(Calendar calendar)
    {
        trunc(calendar, Calendar.DAY_OF_MONTH);
        return calendar;
    }

    public static Date truncateDate(Date date)
    {
        return truncateDate(createCalendar(date)).getTime();
    }

    /**
     * Возвращает дату обнуленную в младших значащих значений даты от дня месяца или null, если переданная дата null
     * @see Calendar#DAY_OF_MONTH
     */
    public static Date truncateDateSafe(@Nullable Date date)
    {
        return (null != date) ? truncateDate(date) : null;
    }

    /**
     * Возвращает начало дня с учётом часового пояса
     *
     * @param date дата, у которой нужно обнулить время
     * @param timeZone часовой пояс
     * @return дата, у которой время 0:00:00
     */
    private static Date getFirstSecondOfDay(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        return Date.from(localDate.atStartOfDay(timeZone.toZoneId()).toInstant());
    }

    /**
     * Возвращает первую секунду месяца 
     */
    private static Date getFirstSecondOfMonth(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        LocalDate firstDayOfMonth = localDate.withDayOfMonth(1);
        return Date.from(firstDayOfMonth.atStartOfDay(timeZone.toZoneId()).toInstant());
    }

    /**
     * Возвращает первую секунду года
     */
    private static Date getFirstSecondOfYear(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        LocalDate firstDayOfYear = localDate.withDayOfYear(1);
        return Date.from(firstDayOfYear.atStartOfDay(timeZone.toZoneId()).toInstant());
    }

    /**
     * Возвращает последнюю секунду дня с учётом часового пояса
     *
     * @param date дата, для которой нужно вернуть последнюю секунду дня
     * @param timeZone часовой пояс
     * @return дата, у которой время 23:59:59
     */
    private static Date getLastSecondOfDay(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        return Date.from(localDate.atTime(23, 59, 59).atZone(timeZone.toZoneId()).toInstant());
    }

    /**
     * Возвращает последнюю секунду месяца
     */
    private static Date getLastSecondOfMonth(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        LocalDate lastDayOfMonth = localDate.withDayOfMonth(localDate.lengthOfMonth());
        return getLastSecondOfDay(Date.from(lastDayOfMonth.atStartOfDay(timeZone.toZoneId()).toInstant()), timeZone);
    }

    /**
     * Возвращает последнюю секунду года
     */
    private static Date getLastSecondOfYear(Date date, TimeZone timeZone)
    {
        LocalDate localDate = LocalDate.ofInstant(date.toInstant(), timeZone.toZoneId());
        LocalDate lastDayOfYear = localDate.withDayOfYear(localDate.lengthOfYear());
        return getLastSecondOfDay(Date.from(lastDayOfYear.atStartOfDay(timeZone.toZoneId()).toInstant()), timeZone);
    }

    /**
     * Возвращает true если оба параметра содержат один и тот же день
     */
    private static boolean isDaysEquals(Date date1, Date date2, TimeZone timeZone)
    {
        LocalDate localDate1 = LocalDate.ofInstant(date1.toInstant(), timeZone.toZoneId());
        LocalDate localDate2 = LocalDate.ofInstant(date2.toInstant(), timeZone.toZoneId());
        return localDate1.getDayOfMonth() == localDate2.getDayOfMonth();
    }

    /**
     * Возвращает true если оба параметра содержат один и тот же месяц
     */
    private static boolean isMonthsEquals(Date date1, Date date2, TimeZone timeZone)
    {
        LocalDate localDate1 = LocalDate.ofInstant(date1.toInstant(), timeZone.toZoneId());
        LocalDate localDate2 = LocalDate.ofInstant(date2.toInstant(), timeZone.toZoneId());
        return localDate1.getMonthValue() == localDate2.getMonthValue();
    }

    /**
     * Возвращает true если оба параметра содержат один и тот же год
     */
    private static boolean isYearsEquals(Date date1, Date date2, TimeZone timeZone)
    {
        LocalDate localDate1 = LocalDate.ofInstant(date1.toInstant(), timeZone.toZoneId());
        LocalDate localDate2 = LocalDate.ofInstant(date2.toInstant(), timeZone.toZoneId());
        return localDate1.getYear() == localDate2.getYear();
    }

    private static void setTimeZoneIfNotNull(Calendar calendar, @Nullable TimeZone timeZone)
    {
        if (timeZone != null)
        {
            calendar.setTimeZone(timeZone);
        }
    }

    /**
     * Получить отрезок времени (дата без времени )по которому будет организован фильтр. Вынесли логику в метод, для
     * использования в другом месте.
     */
    public static Pair<Date, Date> getDateRange(long days, boolean afterToday)
    {
        Calendar cal = Calendar.getInstance();
        truncateDate(cal);
        return getRange(cal, days, afterToday);
    }

    /**
     * Получить отрезок времени (дата со временем) по которому будет организован фильтр. Вынесли логику в метод, для
     * использования в другом месте.
     */
    public static Pair<Date, Date> getDateTimeRange(long days, boolean afterToday)
    {
        Calendar cal = Calendar.getInstance();
        return getRange(cal, days, afterToday);
    }

    private static Pair<Date, Date> getRange(Calendar cal, long days, boolean afterToday)
    {
        Date current = cal.getTime();
        cal.add(Calendar.DATE, afterToday ? (int)days : -(int)days);
        Date date = cal.getTime();
        return afterToday ? Pair.create(current, date) : Pair.create(date, current);
    }
}
