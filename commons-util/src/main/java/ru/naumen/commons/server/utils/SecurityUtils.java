package ru.naumen.commons.server.utils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;

/**
 * Вспомогательные методы для работы с XmlSecurity
 *
 * @see http://docs.oracle.com/javase/6/docs/technotes/guides/security/xmldsig/XMLDigitalSignature.html
 *
 * <AUTHOR>
 *
 */
@Component
public class SecurityUtils
{
    public SecurityUtils()
    {
        removeSha1AlgsFromDSigPolicy();
    }

    /**
     * Removes the SHA-1 algorithms from the
     * jdk.xml.dsig.secureValidationPolicy security property.
     * Because https://bugs.openjdk.java.net/browse/JDK-8259709
     */
    public static void removeSha1AlgsFromDSigPolicy()
    {
        String value = Security.getProperty("jdk.xml.dsig.secureValidationPolicy");
        value = Arrays.stream(value.split(","))
                .filter(v -> !v.contains("disallowAlg") || !v.contains("sha1"))
                .collect(Collectors.joining(","));
        Security.setProperty("jdk.xml.dsig.secureValidationPolicy", value);
    }

    private static final Logger LOG = LoggerFactory.getLogger(SecurityUtils.class);

    /**
     * Производит генерацию {@link KeyPair} по алгоритму DSA длинной 2048
     *
     * @return сгенерированную {@link KeyPair}
     */
    public KeyPair generateKeyPair()
    {
        return generateKeyPair("RSA", 4096);
    }

    /**
     * Производит генерацию ключа по указанному алгоритму с заданной длинной
     *
     * @param type RSA or DSA
     */
    public KeyPair generateKeyPair(String type, int length)
    {
        try
        {
            KeyPairGenerator kpg = KeyPairGenerator.getInstance(type);
            kpg.initialize(length);
            return kpg.generateKeyPair();
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public KeyStore getKeyStore(InputStream is, String passwd)
    {
        try
        {
            KeyStore keystore = KeyStore.getInstance(KeyStore.getDefaultType());
            keystore.load(is, passwd.toCharArray());
            return keystore;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Читает закрытый ключ из потока
     */
    public PrivateKey getPrivateKey(InputStream is)
    {
        try
        {
            byte[] bytes = IOUtils.toByteArray(is);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            KeySpec ks = new PKCS8EncodedKeySpec(bytes);
            return keyFactory.generatePrivate(ks);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Читает открытый ключ из потока
     */
    public PublicKey getPublicKey(InputStream is)
    {
        try
        {
            byte[] bytes = IOUtils.toByteArray(is);
            LOG.atDebug().log(new String(bytes, StandardCharsets.UTF_8));
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(bytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA", "SUN");
            return keyFactory.generatePublic(publicKeySpec);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public Certificate getCertificate(InputStream is)
    {
        try
        {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            return cf.generateCertificate(is);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    public KeyPair readKey(InputStream is, String passwd, String alias)
    {
        try
        {
            KeyStore keyStore = getKeyStore(is, passwd);

            Certificate certificate = keyStore.getCertificate(alias);
            PublicKey publicKey = certificate.getPublicKey();

            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            Key pkey = keyStore.getKey(alias, passwd.toCharArray());
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkey.getEncoded());
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            return new KeyPair(publicKey, privateKey);
        }
        catch (Exception e)
        {
            throw new Error(e);
        }
    }

    public void saveKeyPair(String path, KeyPair keyPair)
    {
        try
        {
            PrivateKey privateKey = keyPair.getPrivate();
            PublicKey publicKey = keyPair.getPublic();

            // Store Public Key.
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(publicKey.getEncoded());
            FileOutputStream fos = new FileOutputStream(path + "/public.key");
            fos.write(x509EncodedKeySpec.getEncoded());
            fos.close();

            // Store Private Key.
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(privateKey.getEncoded());
            fos = new FileOutputStream(path + "/private.key");
            fos.write(pkcs8EncodedKeySpec.getEncoded());
            fos.close();
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }
}
