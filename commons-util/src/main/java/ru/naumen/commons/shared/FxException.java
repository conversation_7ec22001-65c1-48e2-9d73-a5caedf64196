/*
 * NAUMEN FX (c) NAUMEN 2005
 */
package ru.naumen.commons.shared;

import java.util.Map;

import com.google.common.collect.Maps;
import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.annotation.Nullable;

/**
 * Base class for all the FX errors
 *
 * <AUTHOR>
 */
public class FxException extends RuntimeException implements IReadableException, IsSerializable, ILocalizedException
{
    private static final long serialVersionUID = 1L;

    private boolean readable = false;
    private String details;
    private String uiMessage;
    private boolean alreadyLogged;
    private Map<String, String> localizedMessages;

    public FxException()
    {
        super();
    }

    public FxException(String msg)
    {
        super(msg);
    }

    public FxException(String msg, boolean readable)
    {
        super(msg);
        this.readable = readable;
    }

    public FxException(String msg, boolean readable, String uiMessage)
    {
        this(msg, readable);
        this.uiMessage = uiMessage;
    }

    public FxException(String msg, boolean readable, String uiMessage, Throwable cause)
    {
        super(msg, cause);
        this.readable = readable;
        this.uiMessage = uiMessage;
        initDetails(cause);
    }

    public FxException(String msg, boolean readable, Throwable cause)
    {
        super(msg, cause);
        this.readable = readable;
        initDetails(cause);
        initUiMessage(cause);
        initLocalizedMessages(cause);
    }

    public FxException(String msg, String details)
    {
        this(msg, true);
        this.details = details;
    }

    public FxException(String msg, String details, String uiMessage)
    {
        this(msg, details);
        this.uiMessage = uiMessage;
    }

    public FxException(String msg, Throwable cause)
    {
        super(msg, cause);
        initReadable(cause);
        initDetails(cause);
        initUiMessage(cause);
        initLocalizedMessages(cause);
    }

    public FxException(Throwable cause)
    {
        super(cause);
        initReadable(cause);
        initDetails(cause);
        initUiMessage(cause);
        initLocalizedMessages(cause);
    }

    /**
     * Конструктор, позволяющий не выводить полный стектрейс исключения без необходимости
     * @param message - текст сообщения об ошибке
     * @param cause - исключение-причина. Может быть null
     * @param enableSuppression - включить подавление исключения
     * @param writableStackTrace - выводить или нет стектрейс
     * @param readable - читаемое сообщение об ошибке
     */
    public FxException(String message, @Nullable Throwable cause,
            boolean enableSuppression,
            boolean writableStackTrace,
            boolean readable)
    {
        this(message, cause, enableSuppression, writableStackTrace);
        this.readable = readable;
    }

    public FxException(String message, @Nullable Throwable cause,
            boolean enableSuppression,
            boolean writableStackTrace)
    {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public FxException(Map<String, String> localizedMessages)
    {
        super(localizedMessages.values().iterator().next());
        this.localizedMessages = localizedMessages;
    }

    public FxException(Map<String, String> localizedMessages, boolean readable)
    {
        super(localizedMessages.values().iterator().next());
        this.localizedMessages = localizedMessages;
        this.readable = readable;
    }

    public FxException(Map<String, String> localizedMessages, Throwable cause)
    {
        super(localizedMessages.values().iterator().next(), cause);
        initReadable(cause);
        initDetails(cause);
        initUiMessage(cause);
        initLocalizedMessages(cause);
    }

    public FxException(String msg,
            boolean readable,
            boolean writableStackTrace)
    {
        super(msg, null, true, writableStackTrace);
        this.readable = readable;
    }

    /**
     * @return технические детали сообщения
     */
    public String getDetails()
    {
        return details;
    }

    /* (non-Javadoc)
     * @see ru.naumen.commons.shared.HasUiMessageException#getUiMessage()
     */
    @Override
    public String getUiMessage()
    {
        return uiMessage;
    }

    public boolean isAlreadyLogged()
    {
        return alreadyLogged;
    }

    @Override
    public boolean isReadable()
    {
        return readable;
    }

    public void setAlreadyLogged()
    {
        alreadyLogged = true;
    }

    protected void initReadable(Throwable cause)
    {
        this.readable = cause instanceof IReadableException && ((IReadableException)cause).isReadable();
    }

    private void initDetails(Throwable e)
    {
        if (e instanceof FxException)
        {
            details = ((FxException)e).getDetails();
        }
    }

    private void initUiMessage(Throwable cause)
    {
        if (cause instanceof IReadableException)
        {
            this.uiMessage = ((IReadableException)cause).getUiMessage();
        }
    }

    private void initLocalizedMessages(Throwable cause)
    {
        if (cause instanceof ILocalizedException)
        {
            this.localizedMessages = ((ILocalizedException)cause).getLocalizedMessages();
        }
    }

    @Override
    public Map<String, String> getLocalizedMessages()
    {
        return localizedMessages != null ? localizedMessages : Maps.newLinkedHashMap();
    }

    @Override
    public String getLocalizedMessage(String locale)
    {
        if (isLocalized() && getLocalizedMessages().containsKey(locale))
        {
            return getLocalizedMessages().get(locale);
        }
        return getMessage();
    }

    @Override
    public void setLocalizedMessage(String locale, String message)
    {
        if (localizedMessages == null)
        {
            localizedMessages = Maps.newLinkedHashMap();
        }
        if (!localizedMessages.containsKey(locale))
        {
            localizedMessages.put(locale, message);
        }
    }

    @Override
    public void setLocalizedMessages(Map<String, String> localizedMessages)
    {
        if (this.localizedMessages == null)
        {
            this.localizedMessages = localizedMessages;
        }
        else
        {
            this.localizedMessages.putAll(localizedMessages);
        }
    }

    @Override
    public boolean isLocalized()
    {
        return localizedMessages != null;
    }
}
