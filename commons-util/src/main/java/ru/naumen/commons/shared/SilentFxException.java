package ru.naumen.commons.shared;

/**
 * Расширение для {@link FxException}, чтобы во время catch можно было решить логировать данную ошибку или нет
 *
 * <AUTHOR>
 * @since January 29, 2019
 */
public class SilentFxException extends FxException implements ISilentException
{
    private final boolean needLog;

    public SilentFxException(final String msg, final boolean readable, final boolean needLog,
            final boolean writableStackTrace)
    {
        super(msg, readable, writableStackTrace);
        this.needLog = needLog;
    }

    @Override
    public boolean isNeedLog()
    {
        return needLog;
    }
}
