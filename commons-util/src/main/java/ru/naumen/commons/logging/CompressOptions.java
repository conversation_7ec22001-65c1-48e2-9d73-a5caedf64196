package ru.naumen.commons.logging;

/**
 * Опции сжатия логов
 *
 * <AUTHOR>
 * @since 08.12.2016
 */
public final class CompressOptions
{
    private final boolean compression;
    private final int uncompressedDays;
    private final CompressType compressType;
    private final int compressionLevel;

    public CompressOptions(boolean compression, int uncompressedDays, CompressType compressType, int compressionLevel)
    {
        this.compression = compression;
        this.uncompressedDays = uncompressedDays;
        this.compressType = compressType;
        this.compressionLevel = compressionLevel;
    }

    public boolean isCompression()
    {
        return compression;
    }

    public int getUncompressedDays()
    {
        return uncompressedDays;
    }

    public CompressType getCompressType()
    {
        return compressType;
    }

    public int getCompressionLevel()
    {
        return compressionLevel;
    }
}
