package ru.naumen.commons.server.utils;

import java.text.DecimalFormatSymbols;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.FxException;

/**
 * Тесты на методы работы со строками
 * <AUTHOR>
 * @since 30.07.2010
 */
public class StringUtilitiesJdkTest
{
    @Test
    public void appendToLen()
    {
        Assert.assertEquals("00042", StringUtilities.appendToLen(42, 5));
        Assert.assertEquals("123", StringUtilities.appendToLen(123, -4));
    }

    @Test
    public void breakLongWords()
    {
        final String param = "qqqqqqqqqqwwwwwwwwwweeeeeeeeeerrrrrrrrrrttttttttttyyyyyyyyyyuuuuuuuuuuiiiiiiiiii";
        final String expected = "qqqqqqqqqqwwwwwwwwwweeeeeee eeerrrrrrrrrrttttttttttyyyy yyyyyyuuuuuuuuuuiiiiiiiiii";
        final String actual = StringUtilities.breakLongWords(param);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void breakSingleLongWord()
    {
        String[] params = { "qqqqqqqqqqwwwwwwwwwwttttttttttrrrrrrrrrrgggggggggg", "123456789123456789",
                "1234567891234567891" };
        String[] actual = { "qqqqqqqqqq wwwwwwwwww tttttttttt rrrrrrrrrr gggggggggg", "123456789 123456789",
                "1234567891 234567891" };
        int[] mwl = { 10, 10, 10 };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.breakSingleLongWord for arg[" + i + "]:" + params[i], actual[i],
                    StringUtilities.breakSingleLongWord(params[i], mwl[i]));
        }
    }

    @Test
    public void breakString()
    {
        final String[] params = { "aassdd", "a-aaaaaa-ss-d" };
        final String[][] expected = { { "aa", "ss", "dd" }, { "a-", "aaaa", "aa-", "ss-d" } };
        final int[] len = { 2, 4 };
        final String[] delimiters = { "", "-" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.breakString for arg [" + i + "]:" + params[i],
                    Arrays.asList(expected[i]), StringUtilities.breakString(params[i], len[i], delimiters[i]));
        }
    }

    @Test
    public void byteFromBase32Char()
    {
        final char ch = 'a';
        byte expected = StringUtilities.byteFromBase32Char(ch);
        Assert.assertEquals(10, expected);
    }

    @Test
    public void carryOver()
    {
        final String sample = "12345";
        String parts = StringUtilities.carryOver(sample, 2);
        Assert.assertEquals("12345", parts);
    }

    @Test
    public void changeRegister()
    {
        final String[] params = { null, "ender", "wiggin" };
        final String[] expected = { "", "ender", "wiGgin" };
        final int[] positions = { 0, -1, 2 };
        final boolean[] upperFlags = { true, true, true };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.changeRegister for arg [" + i + "]:" + params[i], expected[i],
                    StringUtilities.changeRegister(params[i], positions[i], upperFlags[i]));
        }
    }

    @Test
    public void compareTo()
    {
        String[] str1 = { "abcd", "1234", "12345" };
        String[] str2 = { "abc", "123456789", "12345" };
        int[] actual = { 1, -5, 0 };
        for (int i = 0; i < str1.length; i++)
        {
            Assert.assertEquals("StringUtilities.compareTo for arg[" + i + "]:" + str1[i] + str2[i], actual[i],
                    ru.naumen.commons.shared.utils.StringUtilities.compareTo(str1[i], str2[i]));
        }
    }

    @Test
    public void compareToIgnoreCase()
    {
        final String first = "abcd";
        final String second = "abc";
        int parts = ru.naumen.commons.shared.utils.StringUtilities.compareTo(first, second);
        Assert.assertEquals(1, parts);
    }

    @Test
    public void concatAll()
    {
        final String str[] = { "d", "ftyuh", "gg", " ", "S", "!", "r" };
        String expected = StringUtilities.concatAll(str);
        Assert.assertEquals("dftyuhgg S!r", expected);
    }

    @Test
    public void concatenateString()
    {
        final String[] params1[] = { { "aa", "bb", "cc" } };
        final String[] params2 = { "dd" };
        final String[] expected[] = { { "aa", "bb", "cc", "dd" } };
        for (int i = 0; i < params1.length; i++)
        {
            Assert.assertArrayEquals("StringUtilities.concatenateString #" + i, expected[i],
                    StringUtilities.concatenate(params1[i], params2[i]));
        }
    }

    @Test
    public void concatenateStringArray()
    {
        final String[] params1[] = { { "a", "b", "c" } };
        final String[] params2[] = { { "d", "e" } };
        final String[] expected[] = { { "a", "b", "c", "d", "e" } };
        for (int i = 0; i < params1.length; i++)
        {
            Assert.assertArrayEquals("StringUtilities.concatenateStringArray #" + i, expected[i],
                    StringUtilities.concatenate(params1[i], params2[i]));
        }
    }

    @Test
    public void concatStrings()
    {
        final String str[] = { "dgjtjhj", "f", "g", " ", "Shju", "!", "rkjk" };
        String expected = StringUtilities.concatAll(str);
        Assert.assertEquals("dgjtjhjfg Shju!rkjk", expected);
    }

    @Test
    public void containsRussianLetter()
    {
        final String[] params = { "asdf", "asвf", null };
        final boolean[] expected = { false, true, false };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertTrue("StringUtilities.containsRussianLetter #" + i,
                    expected[i] == StringUtilities.containsRussianLetter(params[i]));
        }
    }

    @Test
    public void containStr1InStr2()
    {
        Object[][] paramsForTests = { { "abcd", "dffabcd", Boolean.TRUE }, { null, null, Boolean.FALSE },
                { null, "anything", Boolean.FALSE }, { "anything", null, Boolean.FALSE },
                { " ", "ab cc", Boolean.TRUE } };
        for (Object[] params : paramsForTests)
        {

            String str1 = (String)params[0];
            String str2 = (String)params[1];
            Boolean expected = (Boolean)params[2];

            Assert.assertEquals("str1:" + str1 + ",str2:" + str2, StringUtilities.containsSafe(str1, str2), expected);
        }
    }

    @Test
    public void createNotInClause()
    {
        final String[] params[] = { { "a", "b", "c" } };
        final String[] expected = { "NOT IN ( 'a', 'b', 'c' ) " };
        Assert.assertEquals("StringUtilities.createNotInClause # null", "", StringUtilities.createNotInClause(null));
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.createNotInClause #" + i, expected[i],
                    StringUtilities.createNotInClause(Arrays.asList(params[i])));
        }
    }

    @Test
    public void displayFloat()
    {
        {
            int plc = 5;
            float f = 5678.437976896457856f;
            String str = "NaNsymbol";
            String actual = StringUtilities.displayFloat(f, plc, str);
            Assert.assertEquals("5678.43776", actual);
        }
        {
            int plc = 5;
            float f = Float.NaN;
            String str = "NaNsymbol";
            String actual = StringUtilities.displayFloat(f, plc, str);
            Assert.assertEquals(str, actual);

        }
    }

    @Test
    public void displayFloatAsMoney()
    {
        {
            float f = 75978786797897896.4379457856f;
            String str = "NaNsymbol";
            String expected = StringUtilities.displayFloatAsMoney(f, str);
            Assert.assertEquals("75,978,787,510,026,240", expected);
        }
        {
            float f = 76.4379457856f;
            String str = "NaNsymbol";
            String expected = StringUtilities.displayFloatAsMoney(f, str);
            Assert.assertEquals("76.44", expected);
        }
    }

    @Test
    public void duplicateChars()
    {
        final char[] params = { 'a' };
        final int[] counts = { 4 };
        final String[] expected = { "aaaa" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.duplicateChars #" + i, expected[i],
                    StringUtilities.duplicate(params[i], counts[i]));

        }
    }

    @Test
    public void duplicateStrings()
    {
        final String[] params = { null, "asdf", "qw" };
        final int[] counts = { 2, 0, 4 };
        final String[] expected = { null, "", "qwqwqwqw" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.duplicateString #" + i, expected[i],
                    StringUtilities.duplicate(params[i], counts[i]));
        }
    }

    @Test
    public void escapeLikeString()
    {
        String[] params = { null, "fgnfyhjfyrt545hj675", "vufrhtuioh%bijrti'''hiogj_gjhojf" };
        String[] stresc = { "123", "123", "123" };
        boolean[] qt = { false, true, true };
        String[] actual = { null, "'fgnfyhjfyrt545hj675'", "'vufrhtuioh123%bijrti''''''hiogj123_gjhojf'" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.escapeLikeString for arg[" + i + "]:" + params[i], actual[i],
                    StringUtilities.escapeLikeString(params[i], stresc[i], qt[i]));
        }
    }

    @Test
    public void escapeString()
    {
        {
            final String str = null;
            String parts = StringUtilities.escapeString(str, false);
            Assert.assertEquals(null, parts);
        }
        {
            final String str = "dfrgf\'dhgyg";
            String parts = StringUtilities.escapeString(str, false);
            Assert.assertEquals("dfrgf''dhgyg", parts);
        }
        {
            final String str = "dfrgf\'dhgyg";
            String parts = StringUtilities.escapeString(str, true);
            Assert.assertEquals("'dfrgf''dhgyg'", parts);
        }
    }

    @Test
    public void extractBounded()
    {
        {
            final String vl = "6 666 123 666 12 666";
            String strbeg = "123";
            String strend = "12";
            String expected = StringUtilities.extractBounded(vl, strbeg, strend);
            Assert.assertEquals(" 666 ", expected);
        }
        {
            final String vl = "555555";
            String strbeg = "";
            String strend = "12";
            String expected = StringUtilities.extractBounded(vl, strbeg, strend);
            Assert.assertEquals(null, expected);
        }
        {
            final String vl = "6 666 123 666 12 666";
            String strbeg = "567";
            String strend = "6578";
            String expected = StringUtilities.extractBounded(vl, strbeg, strend);
            Assert.assertEquals(null, expected);
        }
    }

    @Test
    public void formatNumber()
    {
        // final String str = "dfrgf\'dhgyg";
        // String parts = StringUtilities.formatNumber();
        // Assert.assertEquals("'dfrgf''dhgyg'", parts);
    }

    @Test
    public void fromHexString()
    {
        {
            final String str = "";
            String expected = StringUtilities.fromHexString(str);
            Assert.assertEquals("", expected);
        }
        {
            final String str = "vftgb45hdvfg";
            String expected = StringUtilities.fromHexString(str);
            Assert.assertEquals("", expected);
        }

    }

    @Test
    public void fromTextToHtml()
    {
        final String[] params = { "checkout my site\n at http://mysite.com someveryveryveryveryveryverylongword" };
        final boolean[] DBFormatFlags = { true };
        final String[] frameTargets = { "target" };
        final String[] newLineRpls = { "NEWLINE" };
        final int[] maxWordLens = { 15 };
        final String[] expected = {
                "'checkout my site\nNEWLINE at <A HREF=\"http://mysite.com\" TARGET=\"target\">http://mysit...</A> "
                + "someveryvery veryveryvery verylongword'" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.fromTextToHtml #" + i, expected[i], StringUtilities
                    .fromTextToHTML(params[i], DBFormatFlags[i], frameTargets[i], newLineRpls[i], maxWordLens[i]));
        }
    }

    @Test
    public void getPhoneNumberByString()
    {
        final String str = "+7909-023-86-89+";
        String actual = StringUtilities.getPhoneNumberByString(str);
        Assert.assertEquals("+79090238689", actual);
    }

    @Test(expected = FxException.class)
    public void getPhoneNumberByStringWithChars()
    {
        String str = "+7909-023-86-89abcd";
        StringUtilities.getPhoneNumberByString(str);
    }

    @Test
    public void getPhoneNumberList()
    {
        String[] num = { "+7909-023-86-89", "3456957698", "73456756", "+7 - (908) - 123-45-67" };
        String actual = StringUtilities.getPhoneNumberList(num);
        Assert.assertEquals("+79090238689,3456957698,73456756,+79081234567", actual);
    }

    @Test
    public void handleNull()
    {
        {
            String str = null;
            String expected = StringUtilities.handleNull(str);
            Assert.assertEquals("", expected);
        }
        {
            String str = "gh934h8hgh";
            String expected = StringUtilities.handleNull(str);
            Assert.assertEquals("gh934h8hgh", expected);
        }
    }

    @Test
    public void hexToBase64()
    {
        String[] params = { null, "" };
        String[] actual = { null, "" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.hexToBase64 for arg[" + i + "]:" + params[i], actual[i],
                    StringUtilities.hexToBase64(params[i]));
        }
    }

    @Test
    public void insertWordBrakes()
    {
        String str = "test.string_is.here_";
        String expected = "test.<wbr>string_<wbr>is.<wbr>here_<wbr>";
        String actual = StringUtilities.insertWordBrakes(str, ".", "_");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void isEmpty()
    {
        String[] params = { null, "" };
        boolean[] expected = { true, true };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.isEmpty for arg[" + i + "]:" + params[i], expected[i],
                    ru.naumen.commons.shared.utils.StringUtilities.isEmpty(params[i]));
        }
    }

    @Test
    public void isEmptyTrim()
    {
        String[] str = { null, "bn65bhy 6g" };
        boolean[] expected = { true, false };
        for (int i = 0; i < str.length; i++)
        {
            Assert.assertEquals("StringUtilities.isEmptyTrim for arg[" + i + "]:" + str[i], expected[i],
                    ru.naumen.commons.shared.utils.StringUtilities.isEmptyTrim(str[i]));
        }
    }

    @Test
    public void isLatinLetter()
    {
        {
            final char sample = 'R';
            boolean letter = ru.naumen.commons.shared.utils.StringUtilities.isLatinLetter(sample);
            Assert.assertTrue(sample + " is Latin letter", letter);
        }

        {
            final char sample = 'ы';
            boolean letter = ru.naumen.commons.shared.utils.StringUtilities.isLatinLetter(sample);
            Assert.assertFalse(sample + " is not Latin letter", letter);
        }
    }

    @Test
    public void isNaturalNumber()
    {
        final String[] params = { "55", "55", "", "f" };
        final int[] lens = { 1, 2, 2, 2 };
        final boolean[] expected = { false, true, false, false };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertTrue("StringUtilities.isNaturalNumber #" + i,
                    expected[i] == StringUtilities.isNaturalNumber(params[i], lens[i]));
        }
    }

    @Test
    public void isRussianLetter()
    {
        {
            final char sample = 'л';
            boolean letter = StringUtilities.isRussianLetter(sample);
            Assert.assertTrue(sample + " is Russian letter", letter);
        }
        {
            final char sample = 'h';
            boolean letter = StringUtilities.isRussianLetter(sample);
            Assert.assertFalse(sample + " is not Russian letter", letter);
        }
    }

    @Test
    public void isValidClassName()
    {
        String[] params = { "NJHcouihuohg", "опрващрир" };
        boolean[] expected = { true, false };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.isValidClassName for arg[" + i + "]:" + params[i], expected[i],
                    StringUtilities.isValidClassName(params[i]));
        }
    }

    @Test
    public void isValidEmailAdditionalCheck()
    {
        {
            final String str = null;
            boolean expected = StringUtilities.isValidEmailAdditionalCheck(str);
            Assert.assertFalse("null is not valid class name", expected);
        }
        {
            final String str = "опрващрир";
            boolean expected = StringUtilities.isValidClassName(str);
            Assert.assertFalse(str + " is not valid class name", expected);
        }
    }

    @Test
    public void joinInOrderWithPrefixSuffix()
    {
        List<String> o = Lists.newArrayList("1", "2");
        String sep = ",";
        String p = "[";
        String s = "]";
        Assert.assertEquals("forward", "[1],[2]", StringUtilities.joinInOrderWithPrefixSuffix(o, sep, p, s));
    }

    @Test
    public void makeIDNCompatibleEmptyEmail()
    {
        String expected = "";
        String actual = StringUtilities.makeEmailIDNCompatible("");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void makeIDNCompatibleEnEmail()
    {
        String expected = "<EMAIL>";
        String actual = StringUtilities.makeEmailIDNCompatible("<EMAIL>");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void makeIDNCompatibleIncorrectEmail()
    {
        String expected = "test.java";
        String actual = StringUtilities.makeEmailIDNCompatible("test.java");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void makeIDNCompatibleIncorrectEmail2()
    {
        String expected = "test.java@";
        String actual = StringUtilities.makeEmailIDNCompatible("test.java@");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void makeIDNCompatibleNullEmail()
    {
        String expected = null;
        String actual = StringUtilities.makeEmailIDNCompatible(null);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void makeIDNCompatibleRuEmail()
    {
        String expected = "<EMAIL>--p1ai";
        String actual = StringUtilities.makeEmailIDNCompatible("test.java@яндекс.рф");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_badURL()
    {
        String expected = "не правильный URL http:// и т.д.";
        String actual = StringUtilities.normalizeLinks("не правильный URL http:// и т.д.");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_complexURL()
    {
        String expected = "<a href=\"http://www.naumen.ru:80/go/products/nausd?a=b&c=d\" target=\"_top\">http://www"
                          + ".naumen.ru:80/go/products/nausd?a=b&c=d</a>";
        String actual = StringUtilities.normalizeLinks("http://www.naumen.ru:80/go/products/nausd?a=b&c=d");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_longLink()
    {
        String expected = "<a href=\"http://very-very-very-very-very-long.ru\" target=\"_top\">http://...ry-long"
                          + ".ru</a>";
        String actual = StringUtilities.normalizeLinks("http://very-very-very-very-very-long.ru", 10, "_top");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_manyLinksWithOtherProto()
    {
        String expected = "ftp archive of <a href=\"http://debian.org\" target=\"_top\">debian.org</a> is <a "
                          + "href=\"ftp://debian.org\" target=\"_top\">ftp://debian.org</a>";
        String actual = StringUtilities.normalizeLinks("ftp archive of debian.org is ftp://debian.org");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simple()
    {
        String expected = "<a href=\"http://www.naumen.ru\" target=\"_top\">www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simpleWithFile()
    {
        String expected = "<a href=\"file://www.naumen.ru\" target=\"_top\">file://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("file://www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simpleWithFTP()
    {
        String expected = "<a href=\"ftp://www.naumen.ru\" target=\"_top\">ftp://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("ftp://www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simpleWithHTTP()
    {
        String expected = "<a href=\"http://www.naumen.ru\" target=\"_top\">http://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("http://www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simpleWithHTTPS()
    {
        String expected = "<a href=\"https://www.naumen.ru\" target=\"_top\">https://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("https://www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_simpleWithTelnet()
    {
        String expected = "<a href=\"telnet://www.naumen.ru\" target=\"_top\">telnet://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("telnet://www.naumen.ru");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_target()
    {
        String expected = "<a href=\"http://www.naumen.ru\" target=\"TARGET\">http://www.naumen.ru</a>";
        String actual = StringUtilities.normalizeLinks("http://www.naumen.ru", 255, "TARGET");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_textWithHTTP()
    {
        String expected = "site <a href=\"http://www.naumen.ru\" target=\"_top\">http://www.naumen.ru</a> is good";
        String actual = StringUtilities.normalizeLinks("site http://www.naumen.ru is good");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_textWithRussianLetters()
    {
        String expected = "Сайт компании <a href=\"http://naumen.ru\" target=\"_top\">naumen.ru</a>. Не забудь "
                          + "посетить!";
        String actual = StringUtilities.normalizeLinks("Сайт компании naumen.ru. Не забудь посетить!");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void normalizeLinks_withProto()
    {
        String expected = "<a href=\"http://www.naumen.ru:80\" target=\"_top\">www.naumen.ru:80</a>";
        String actual = StringUtilities.normalizeLinks("www.naumen.ru:80");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void phraseList()
    {
        final String[] params = { "h sfo4w5 yu04($%&(*75 5986 gk" };
        final String[][] expected = { { "h", "sfo4w5", "yu04", "%", "*75", "5986", "gk" } };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.phraseList #" + i, Arrays.asList(expected[i]),
                    StringUtilities.phraseList(params[i]));
        }
    }

    @Test
    public void removeBlanksBetweenWords()
    {
        final String[] params = { "asd     dfg  hg " };
        final String[] expected = { "asd dfg hg " };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.removeBlanksBetweenWords #" + i, expected[i],
                    StringUtilities.removeBlanksBetweenWords(params[i]));
        }
    }

    @Test
    public void removeEmptyStrings()
    {
        String[] strs = { "", "test1", "", "test2", "test3" };
        String[] expected = StringUtilities.removeEmptyStrings(strs);
        String[] strs2 = { "test1", "test2", "test3" };
        Assert.assertArrayEquals(strs2, expected);
    }

    @Test
    public void removeSubstring()
    {

        {
            final String orgn = null;
            String[] sbstr = { "fgh5656g" };
            String expected = StringUtilities.removeSubstring(orgn, sbstr);
            Assert.assertEquals("", expected);
        }
        {
            final String orgn = "h65u67jf";
            String[] sbstr = null;
            String expected = StringUtilities.removeSubstring(orgn, sbstr);
            Assert.assertEquals("", expected);
        }
        {
            final String orgn = "444 888 666 999 69 1313 5555";
            String[] sbstr = { "44", "69", "13", "666" };
            String expected = StringUtilities.removeSubstring(orgn, sbstr);
            Assert.assertEquals("4 888  999   5555", expected);
        }
        {
            String str = "4448886669996969 13135555";
            String sbstr = "666";
            String expected = StringUtilities.removeSubstring(str, sbstr);
            Assert.assertEquals("4448889996969 13135555", expected);
        }

    }

    @Test
    public void removeSubstringArray()
    {
        final String[] originals = { "444 888 666 999 69 1313 5555" };
        final String[] substrs[] = { { "44", "69", "13", "666" } };
        final String[] expected = { "4 888  999   5555" };
        for (int i = 0; i < originals.length; i++)
        {
            Assert.assertEquals("removeSubstringArray #" + i, expected[i],
                    StringUtilities.removeSubstring(originals[i], substrs[i]));
        }
    }

    @Test
    public void removeSubstringCaseInsensitive()
    {
        String[] orgn = { null, "h65u67jf", "abcd efghi jklmn" };
        String[] sbstr = { "fgh5656g", null, "ghi" };
        String[] expected = { "", "", "abcd ef jklmn" };

        for (int i = 0; i < orgn.length & i < sbstr.length; i++)
        {
            Assert.assertEquals(
                    "StringUtilities.removeSubstringCaseInsensitive for arg[" + i + "]:" + orgn[i] + '\t' + sbstr[i],
                    expected[i], StringUtilities.removeSubstringCaseInsensitive(orgn[i], sbstr[i]));
        }

    }

    @Test
    public void replaceSubstring()
    {
        String[] orgn = { null, "h65u67jf", "abcd efghi jklmn" };
        String[] sbstrfrom = { "fgh5656g", null, "e" };
        String[] sbstrto = { "fgh5656g", null, "ghi" };
        String[] expected = { "", "", "abcd ghifghi jklmn" };

        for (int i = 0; i < orgn.length & i < sbstrfrom.length & i < sbstrto.length; i++)
        {
            Assert.assertEquals(
                    "StringUtilities.replaceSubstring for arg[" + i + "]:" + orgn[i] + sbstrfrom[i] + sbstrto[i],
                    expected[i], StringUtilities.replaceSubstring(orgn[i], sbstrfrom[i], sbstrto[i]));
        }

    }

    @Test
    public void reverse()
    {
        final String sample3 = "12345";
        String parts3 = StringUtilities.reverse(sample3);
        Assert.assertEquals("54321", parts3);
    }

    @Test
    public void safeStringFormatWithOneStringParamAndTwoInTemplate()
    {
        final String format = "qq %s ee %s";
        final String expected = "Wrong argument count. Formatting string 'qq %s ee %s', arguments 'ww' (size: 1)";

        String actual;
        try
        {
            actual = StringUtilities.stringFormatWitchCheck(format, "ww");
        }
        catch (IllegalArgumentException e)
        {
            actual = e.getMessage();
        }
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithoutParamsAndParamInTemplate()
    {
        final String format = "qq ww ee";
        final String expected = "qq ww ee";
        String actual = StringUtilities.stringFormatWitchCheck(format);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithStringParam()
    {
        final String format = "qq %s ee";
        final String expected = "qq ww ee";
        String actual = StringUtilities.stringFormatWitchCheck(format, "ww");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithStringParamAndEscapedPercent()
    {
        final String format = "qq %s %% ee";
        final String expected = "qq ww % ee";
        String actual = StringUtilities.stringFormatWitchCheck(format, "ww");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithStringParamAndNotEscapedPercent()
    {
        final String format = "qq %s % ee";
        final String expected = "Wrong argument count. Formatting string 'qq %s % ee', arguments 'ww' (size: 1)";

        String actual;
        try
        {
            actual = StringUtilities.stringFormatWitchCheck(format, "ww");
        }
        catch (IllegalArgumentException e)
        {
            actual = e.getMessage();
        }
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithTwoStringParam()
    {
        final String format = "qq %s ee %s";
        final String expected = "qq ww ee rr";
        String actual = StringUtilities.stringFormatWitchCheck(format, "ww", "rr");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeStringFormatWithTwoStringParamAndOneInTemplate()
    {
        final String format = "qq %s ee";
        final String expected = "Wrong argument count. Formatting string 'qq %s ee', arguments 'ww, rr' (size: 2)";

        String actual;
        try
        {
            actual = StringUtilities.stringFormatWitchCheck(format, "ww", "rr");
        }
        catch (IllegalArgumentException e)
        {
            actual = e.getMessage();
        }
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void safeTrim()
    {
        {
            final String str = null;
            String parts = StringUtilities.safeTrim(str);
            Assert.assertNull("safeTrim(null) = null", parts);
        }
        {
            final String str = " fg dfg     ";
            String parts = StringUtilities.safeTrim(str);
            Assert.assertEquals("fg dfg", parts);
        }
    }

    @Test
    public void splitForJSNotyfy()
    {
        final String[] values = { "asdf", "jg oert < sdfasdf sl froi sdf >" };
        final Integer[] lens = { null, 5 };
        final String[] expected[] = { { "asdf", "" },
                { "jg oe...< sdfasdf sl froi sdf >", "jg oert < sdfasdf sl froi sdf >" } };
        for (int i = 0; i < values.length; i++)
        {
            Assert.assertArrayEquals("splitForJSNotyfy #" + i, expected[i],
                    StringUtilities.splitForJSNotyfy(values[i], lens[i]));
        }
    }

    @Test
    public void testbreakLongWords() throws Exception
    {
        final String sample = "wwwwwwwwwwhhhhhhhhhhggggggggggssssssssss rrrrrrrrrrnnnnnnnnnn";
        String parts = StringUtilities.breakLongWords(sample, 5);
        Assert.assertEquals("wwwww wwwww hhhhh hhhhh ggggg ggggg sssss sssss rrrrr rrrrr nnnnn nnnnn", parts);
    }

    @Test
    public void testConstants() throws Exception
    {
        Assert.assertTrue("" == "");
        Assert.assertTrue("a" == "a");
        String aa = "a";
        aa += "a";
        Assert.assertEquals(aa, "aa");
        Assert.assertFalse(aa == "aa");
        Assert.assertTrue(aa.intern() == "aa".intern());
    }

    @Test
    public void testSplitByDelimitorWithSeparator() throws Exception
    {
        final String sample = "/a/b[@c='d']/e";
        String[] parts = StringUtilities.splitByDelimitorWithSeparator(sample, '/', '\'');
        Assert.assertArrayEquals(new String[] { "a", "b[@c='d']", "e" }, parts);
    }

    @Test
    public void textToHTML_simple()
    {
        String expected = "checkout my site at <a href=\"http://mysite.com\" target=\"_top\">http://mysite.com</a>";
        String actual = StringUtilities.textToHTML("checkout my site at http://mysite.com");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void textToHTML_text()
    {
        String expected = "Naumen Service Desk.\n<P> not-url <a href=\"http://may.by.url\" target=\"_top\">may.by"
                          + ".url</a> &#060;<a href=\"http://may.by\" target=\"_top\">may.by</a>&#062;";
        String actual = StringUtilities.textToHTML("Naumen Service Desk.\n not-url may.by.url <may.by>");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void textToHTML_text1()
    {
        String expected = "Naumen Service Desk.\n<P> not-url <a href=\"http://may.by.url\" target=\"_top\">may.by"
                          + ".url</a> &#060;<a href=\"http://may.by\" target=\"_top\">may.by</a>&#062;";
        String actual = StringUtilities.textToHTML("Naumen Service Desk.\n not-url may.by.url <may.by>");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void textToHTML_withNewLine()
    {
        String expected = "line 1\n<P>line 2";
        String actual = StringUtilities.textToHTML("line 1\nline 2");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toDBStringNoQuotes()
    {
        String str = "jfdjug'dghg'gf";
        String actual = StringUtilities.toDBStringNoQuotes(str);
        Assert.assertEquals("jfdjug''dghg''gf", actual);
    }

    @Test
    public void toFileName()
    {
        final String[] params = { "fdg / \\ : | \" * ? > < , sdf" };
        final String[] expected = { "fdg - - - - ' _ _ _ _ _ sdf" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("toFileName #" + i, expected[i], StringUtilities.toFileName(params[i]));
        }
    }

    @Test
    public void toFileSize()
    {
        final char UNBREAKABLE_SPACE = '\u00a0';
        int[] sz = { 1024, 547697897, 2147483647 };
        char decimalSeparator = DecimalFormatSymbols.getInstance().getDecimalSeparator();
        String[] expectedArray = { "1 KB", "522" + decimalSeparator + "3 MB", "2" + UNBREAKABLE_SPACE + "048 MB" };
        for (int i = 0; i < sz.length; i++)
        {
            String expected = expectedArray[i];
            String actual = StringUtilities.toFileSize(sz[i]);
            Assert.assertEquals("StringUtilities.toFileSize for arg[" + i + "]:" + sz[i], expected, actual);
        }
    }

    @Test
    public void toHexString()
    {
        String[] params = { "", "hgnm565hj" };
        String[] actual = { "", "00680067006e006d0035003600350068006a" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("StringUtilities.toHexString for arg[" + i + "]:" + params[i], actual[i],
                    StringUtilities.toHexString(params[i]));
        }
    }

    @Test(expected = NullPointerException.class)
    public void toHexString_null()
    {
        StringUtilities.toHexString((String)null);
    }

    @Test
    public void toHexStringByte()
    {
        final byte[] params[] = { null, {}, { 4, 3, 9, 23, 45, 67, 127, 54, -23 } };
        final String[] expected = { null, "", "040309172D437F36E9" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("toHexString(byte[]) #" + i, expected[i], StringUtilities.toHexString(params[i]));
        }
    }

    @Test
    public void toHTMLString_additionalTable()
    {
        String expected = "\n<P>";
        HashMap<Character, char[]> additionalTable = new HashMap<>();
        additionalTable.put('\n', new char[] { '\n', '<', 'P', '>' });
        String actual = StringUtilities.toHTMLString("\n", false, additionalTable);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toHTMLString_complexString()
    {
        String expected = "НаУМен &#062; NamuMen &amp;&#062;&#060; Test .<BR> &#009; http://bla-bla/?";
        String actual = StringUtilities.toHTMLString("НаУМен > NamuMen &>< Test .\n \t http://bla-bla/?");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toHTMLString_emptyAdditionalTable()
    {
        String expected = "<BR>";
        String actual = StringUtilities.toHTMLString("\n", false, new HashMap<Character, char[]>());
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toHTMLString_EndLine()
    {
        String expected = "<BR>";
        String actual = StringUtilities.toHTMLString("\n");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toHTMLString_R()
    {
        String expected = "";
        String actual = StringUtilities.toHTMLString("\r");
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void toJavaScriptString()
    {
        final String[] params = { "lf dfgsdfg \"' \\ dfg \\\\" };
        final String[] expected = { "lf dfgsdfg \\\"\\' \\\\ dfg \\\\\\\\" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("toJavaScriptString #" + i, expected[i], StringUtilities.toJavaScriptString(params[i]));
        }
    }

    @Test
    public void toLines()
    {
        final String[] originals = { null, "original", "asdf\naaa\n", "\n\n\n\n\n\n\n" };
        final int[] counts = { 4, -5, 10, 5 };
        final String[] defs = { "default", "default", "default", "default" };
        final String[] expected = { "default", "original", "asdf\naaa\n", "\n\n\n\n..." };
        for (int i = 0; i < originals.length; i++)
        {
            Assert.assertEquals("toLines #" + i, expected[i],
                    StringUtilities.toLines(originals[i], counts[i], defs[i]));
        }
    }

    @Test
    public void toLowerCase()
    {
        {
            final String str = null;
            String expected = StringUtilities.toLowerCase(str);
            Assert.assertEquals(null, expected);
        }
        {
            final String str = "abcd ABCD 12345";
            String expected = StringUtilities.toLowerCase(str);
            Assert.assertEquals("abcd abcd 12345", expected);
        }

    }

    @Test
    public void toMarkupString()
    {
        final String[] ins = { null, "", "asdf &nbsp; &lt; &quot; &amp; &gt;" };
        final boolean[] escapeSpaces = { false, false, true };
        final String[] expected = { "", "", "asdf   < \" & >" };
        for (int i = 0; i < ins.length; i++)
        {
            Assert.assertEquals("toMarkupString #" + i, expected[i],
                    StringUtilities.toMarkupString(ins[i], escapeSpaces[i]));
        }
    }

    @Test
    public void toNonMarkupString()
    {
        final String[] ins = { null, "", "asdf <\"&>" };
        final boolean[] escapeStrings = { false, false, true };
        final String[] expected = { "", "", "asdf&nbsp;&lt;&quot;&amp;&gt;" };
        for (int i = 0; i < ins.length; i++)
        {
            Assert.assertEquals("toNonMarkupString #" + i, expected[i],
                    ru.naumen.commons.shared.utils.StringUtilities.toNonMarkupString(ins[i], escapeStrings[i]));
        }
    }

    @Test
    public void toStringArray()
    {
        {
            Object obj = null;
            String str[] = new String[0];
            String expected[] = StringUtilities.toStringArray(obj);
            Assert.assertArrayEquals(str, expected);
        }

        {
            String arr[] = { "fkls;", "jfksl" };
            String expected[] = StringUtilities.toStringArray(arr);
            String arr2[] = { "fkls;", "jfksl" };
            Assert.assertArrayEquals(arr2, expected);
        }

        {
            Integer i = Integer.valueOf(55);
            String expected[] = StringUtilities.toStringArray(i);
            String str[] = { "55" };
            Assert.assertArrayEquals(str, expected);
        }
    }

    @Test
    public void toTitleCase()
    {
        final String[] params = { "hgrTGFk," };
        final String[] expected = { "Hgrtgfk," };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("toTitleCase #" + i, expected[i], StringUtilities.toTitleCase(params[i]));
        }
    }

    @Test
    public void toUpperCase()
    {
        {
            final String str = null;
            String expected = StringUtilities.toUpperCase(str);
            Assert.assertEquals(null, expected);
        }
        {
            final String str = "abcd ABCD 12345";
            String expected = StringUtilities.toUpperCase(str);
            Assert.assertEquals("ABCD ABCD 12345", expected);
        }
    }

    @Test
    public void toXmlChar()
    {
        final int[] chars = { 0x9, '"', '\'', '&', '<', '>', 0x10000 };
        final String[] expected = { "&#9;", "&quot;", "&apos;", "&amp;", "&lt;", "&gt;", "&#65536;" };
        for (int i = 0; i < chars.length; i++)
        {
            Assert.assertEquals("toXmlChar #" + i, expected[i], StringUtilities.toXmlChar(chars[i]));
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void toXmlChar_WithException()
    {
        StringUtilities.toXmlChar(-5);
    }

    @Test(expected = IllegalArgumentException.class)
    public void toXmlChar_WithException2()
    {
        StringUtilities.toXmlChar(0xB);
    }

    @Test(expected = IllegalArgumentException.class)
    public void toXmlChar_WithException3()
    {
        StringUtilities.toXmlChar(0xFFFF);
    }

    @Test
    public void toXmlChars()
    {
        final String[] params = { null, "", "asdf" + (char)0xB + (char)(-5) + "asdf" };
        final boolean[] skipNonChars = { false, false, true };
        final String[] expected = { null, "", "asdf&#65531;asdf" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("toXmlChars #" + i, expected[i],
                    StringUtilities.toXmlChars(params[i], skipNonChars[i]));
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void toXmlChars_Exception()
    {
        StringUtilities.toXmlChars("asdf" + (char)0xB, false);
    }

    @Test
    public void trimWhitespaces()
    {
        final String str = "     hyhtytju   jhy    ";
        String expected = StringUtilities.trimWhitespaces(str);
        Assert.assertEquals("hyhtytju   jhy", expected);
    }

    @Test
    public void urlEncode()
    {
        final String[] params = { null, "", "sdf \" sdf < > sdf" };
        final String[] expected = { null, "", "sdf+%22+sdf+%3C+%3E+sdf" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("urlEncode #" + i, expected[i], StringUtilities.urlEncode(params[i]));
        }
    }

    @Test
    public void urlEncodeUTF8()
    {
        final String[] params = { "sdf \" sdf < > sdf" };
        final String[] expected = { "sdf+%22+sdf+%3C+%3E+sdf" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("urlEncode #" + i, expected[i], StringUtilities.urlEncodeUTF8(params[i]));
        }
    }

    @Test
    public void xorString()
    {
        final String[] params = { "asdfghjk" };
        final char[] keys[] = { { 0xFF, 0xCC, 0xFFFF } };
        final String[] expected = { "¿ﾛ«ﾗ§" };
        for (int i = 0; i < params.length; i++)
        {
            Assert.assertEquals("urlEncode #" + i, expected[i], StringUtilities.xorString(params[i], keys[i]));
        }
    }

}
